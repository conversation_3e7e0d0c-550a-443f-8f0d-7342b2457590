apply plugin: 'com.android.dynamic-feature'
apply plugin: 'com.xmly.dynamic-feature'

xmBundle {
    bundleName = "devtools"
    packageId = "0x99"
    packageName = "com.ximalaya.ting.android.devtools.application"
    isEncrypt = "true"
    isBuildIn = "true"
    encryptStr = "135.0"
}


ext {
    extChannels = "ceshi"// 不能命名test/debug等字段开头，开发时渠道改为 ceshi
    //pChannels shell 输入参数
    shallReadChannelfromFile = "false"
    versionCode = "134"
    versionName = "2.2.9.0"
    minSdkVersion = rootProject.ext.minSdkVersion
    targetSdkVersion = "23"
    isProguard = "false"
    isReleaseDebug = "true"
}

def readChannel() {
    if (shallReadChannelfromFile.toBoolean()) {
        println "------------------read Channels from file start-------------------------"
        // 渠道号配置文件路径
        def path = "build-files/channels.txt"
        file(path).eachLine { channel ->
            if (!channel.startsWith("//")) { //剔除注释行
                setManifestValue(channel)
            }
        }
        println "------------------read Channels from file end-------------------------"
    } else if (project.hasProperty('pChannels')) {
        println "------------------read Channels from property start---------------"
        project.properties['pChannels'].split(',').each { channel ->
            setManifestValue(channel)
        }
        println "------------------read Channels from property end---------------"
    } else if (project.hasProperty('extChannels')) {
        println "------------------read extChannels from ext property start---------------"
        project.properties['extChannels'].split(',').each { channel ->
            setManifestValue(channel)
        }
        println "------------------read extChannels from ext property end---------------"
    } else {
        println "------------------No Channels-------------------------"
    }
}

// 设置Manifest里面需要替换的字段
private void setManifestValue(channel) {
    println channel
    android.productFlavors.create(channel, {
        if (isProguard.toBoolean() && !(isReleaseDebug.toBoolean())) {
            // 正式包
            manifestPlaceholders = [CHANNEL_VALUE: name, UMENG_APP_KEY: "5d022275570df31369000ccf"]
        } else {
            manifestPlaceholders = [CHANNEL_VALUE: name, UMENG_APP_KEY: "5d022275570df31369000ccf"]
        }
    })
}

def buildTime = new Date().format("yyMMdd")

dependencies {

    api fileTree(include: '*.jar', dir: 'libs')
    api project(':DevToolsBundle:DevToolsModule')
    providedhost project(path: ':TingMainHost:TingMainApp', configuration: rootProject.configurationType)

}

android {

    compileSdkVersion rootProject.ext.compileSdkVersion
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            assets.srcDirs = ['assets']
            jniLibs.srcDirs = ['libs']
        }
        androidTest.setRoot('tests')
        debug.setRoot('build-types/debug')
        release.setRoot('build-types/release')
    }

    defaultConfig {
        applicationId "com.ximalaya.ting.android.devtools.application"
        minSdkVersion Integer.parseInt(project.property('minSdkVersion'))
        targetSdkVersion Integer.parseInt(project.property('targetSdkVersion'))
        if (project.hasProperty('pVersionCode')) {
            versionCode Integer.parseInt(project.property('pVersionCode'))
        } else {
            versionCode Integer.parseInt(project.property('versionCode'))
        }
        if (project.hasProperty('pVersionName')) {
            versionName project.property('pVersionName')
        } else {
            versionName project.property('versionName')
        }
        if (project.hasProperty('pIsProguard')) {
            isProguard = project.property('pIsProguard')
        } else {
            isProguard = project.property('isProguard')
        }
        if (project.hasProperty('pIsReleaseDebug')) {
            isReleaseDebug = project.property('pIsReleaseDebug')
        } else {
            isReleaseDebug = project.property('isReleaseDebug')
        }
        //支持multidex 解决65536爆包问题
        multiDexEnabled false

        ndk {
            // 设置支持的SO库架构
            abiFilters 'armeabi'/*, 'x86'*///, 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }
    }

    flavorDimensions "default"

    productFlavors {
        readChannel()
    }

    // 签名
    signingConfigs {
        myConfig {
            storeFile file("build-files/ximalaya.keystore")
            storePassword "111111"
            keyAlias "ximalaya"
            keyPassword "111111"
            v2SigningEnabled false
        }
    }


    //自动移除不用资源
    buildTypes {

        release {
            debuggable project.property('isReleaseDebug').toBoolean()
            signingConfig signingConfigs.myConfig
            //是否混淆
            minifyEnabled project.property('isProguard').toBoolean()
            zipAlignEnabled project.property('isProguard').toBoolean()
            // 移除无用的resource文件
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
            applicationVariants.all { variant ->
                variant.outputs.all {
                    if (minifyEnabled.toBoolean()) {
                        println "------------------apk output proguard file-------------------------"
                        def fileName = "MainApp_v${defaultConfig.versionName}_c${defaultConfig.versionCode}_release_proguard_${buildTime}"
                        if (needChannel.toBoolean()) {
                            fileName += "_${variant.productFlavors[0].name}"
                        }
                        fileName += ".apk"
                        outputFileName = fileName
                    } else {
                        println "------------------apk output normal file-------------------------"
                        def fileName = "MainApp_v${defaultConfig.versionName}_c${defaultConfig.versionCode}_release_${buildTime}"
                        if (needChannel.toBoolean()) {
                            fileName += "_${variant.productFlavors[0].name}"
                        }
                        fileName += ".apk"
                        outputFileName = fileName
                    }
                }
            }
        }

    }

    lintOptions {
        abortOnError false
        ignoreWarnings false
        checkAllWarnings true
        lintConfig file("lint.xml")
    }

    compileOptions {
        sourceCompatibility rootProject.javaCompileVersion
        targetCompatibility rootProject.javaCompileVersion
    }

    //为所有的子项目设置一些通用配置
    subprojects {
        afterEvaluate {
            if (getPlugins().hasPlugin('android') || getPlugins().hasPlugin('android-library')) {
                android {
                    lintOptions {
                        abortOnError false
                        ignoreWarnings true
                        checkAllWarnings false
                        checkReleaseBuilds false
                    }
                }
            }
        }
    }


    aaptOptions.cruncherEnabled = false
    aaptOptions.useNewCruncher = false

    dexOptions {
        maxProcessCount 4
        javaMaxHeapSize "2048M"
    }

}

android.variantFilter { variant ->
    if (variant.buildType.name.endsWith('debug')) {
        variant.setIgnore(true)
    }
}
