apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply from: project.rootDir.absolutePath + '/util.gradle'
android {
    aaptOptions.cruncherEnabled = false
    aaptOptions.useNewCruncher = false

    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    
    lintOptions {
        abortOnError false
        ignoreWarnings false
        checkAllWarnings true
        lintConfig file("lint.xml")
    }

    defaultConfig {
        minSdkVersion Integer.parseInt(rootProject.ext.minSdkVersion)
        targetSdkVersion Integer.parseInt(rootProject.ext.targetSdkVersion)
        versionCode 1
        versionName "1.0"
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [ moduleName : project.getName() , packageName : "com.ximalaya.ting.android.devtools"]
            }
        }
        consumerProguardFile 'proguard-rules.pro'
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
            res.srcDirs = ['src/main/res', 'src/main/res_lite']
        }
    }
    buildTypes {
        release {
            //混淆
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    resourcePrefix "devtools_"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    api fileTree(include: ['*.jar'], dir: 'libs')
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'
    testImplementation 'junit:junit:4.12'
    api project(':TingMainHost:TingMainApp')

    //接入zxing，扫码使用
    api("com.google.zxing:core:3.3.3")

    //内存泄漏检测
    //api 'com.squareup.leakcanary:leakcanary-android:2.8.1'
}