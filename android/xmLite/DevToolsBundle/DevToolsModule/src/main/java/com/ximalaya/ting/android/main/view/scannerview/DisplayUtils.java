package com.ximalaya.ting.android.main.view.scannerview;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Point;
import android.view.Display;
import android.view.WindowManager;

import com.ximalaya.ting.android.xmutil.SystemServiceManager;

public class DisplayUtils {
    public static Point getScreenResolution(Context context) {
        Point screenResolution = new Point();
        WindowManager wm = SystemServiceManager.getWindowManager(context);
        if(wm != null) {
            Display display = wm.getDefaultDisplay();
            display.getSize(screenResolution);
        }
        return screenResolution;
    }

    public static int getScreenOrientation(Context context)
    {
        int orientation = Configuration.ORIENTATION_UNDEFINED;
        WindowManager wm = SystemServiceManager.getWindowManager(context);
        if(wm != null) {
            Display display = wm.getDefaultDisplay();
            if (display.getWidth() == display.getHeight()) {
                orientation = Configuration.ORIENTATION_SQUARE;
            } else {
                if (display.getWidth() < display.getHeight()) {
                    orientation = Configuration.ORIENTATION_PORTRAIT;
                } else {
                    orientation = Configuration.ORIENTATION_LANDSCAPE;
                }
            }
        }
        return orientation;
    }

}
