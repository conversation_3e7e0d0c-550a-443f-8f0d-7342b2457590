package com.ximalaya.ting.android.main.view.scannerview;

import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.NotFoundException;
import com.google.zxing.Reader;
import com.google.zxing.ReaderException;
import com.google.zxing.Result;
import com.google.zxing.qrcode.QRCodeReader;

import java.util.Map;

/**
 * Created by roc on 2018/12/20.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15618953973
 */
public class MyMultiFormatReader implements Reader {

//  private static final Reader[] EMPTY_READER_ARRAY = new Reader[0];

  private Map<DecodeHintType,?> hints;
  private Reader[] readers = {new QRCodeReader(),new WaveCodeReader()};

  /**
   * This version of decode honors the intent of Reader.decode(BinaryBitmap) in that it
   * passes null as a hint to the decoders. However, that makes it inefficient to call repeatedly.
   * Use setHints() followed by decodeWithState() for continuous scan applications.
   *
   * @param image The pixel data to decode
   * @return The contents of the image
   * @throws NotFoundException Any errors which occurred
   */
  @Override
  public Result decode(BinaryBitmap image) throws NotFoundException {
    setHints(null);
    return decodeInternal(image);
  }

  /**
   * Decode an image using the hints provided. Does not honor existing state.
   *
   * @param image The pixel data to decode
   * @param hints The hints to use, clearing the previous state.
   * @return The contents of the image
   * @throws NotFoundException Any errors which occurred
   */
  @Override
  public Result decode(BinaryBitmap image, Map<DecodeHintType,?> hints) throws NotFoundException {
    setHints(hints);
    return decodeInternal(image);
  }
//
//  public Result decodeWithState(BinaryBitmap image) throws NotFoundException {
//    // Make sure to set up the default state so we don't crash
//    if (readers == null) {
//      setHints(null);
//    }
//    return decodeInternal(image);
//  }

  /**
   * This method adds state to the MultiFormatReader. By setting the hints once, subsequent calls
   * to decodeWithState(image) can reuse the same set of readers without reallocating memory. This
   * is important for performance in continuous scan clients.
   *
   * @param hints The set of hints to use for subsequent calls to decode(image)
   */
  public void setHints(Map<DecodeHintType,?> hints) {
    this.hints = hints;
  }

  @Override
  public void reset() {
    if (readers != null) {
      for (Reader reader : readers) {
        reader.reset();
      }
    }
  }

  private Result decodeInternal(BinaryBitmap image) throws NotFoundException {
    if (readers != null) {
      for (Reader reader : readers) {
        try {
          return reader.decode(image, hints);
        } catch (ReaderException re) {
            re.printStackTrace();
        }
      }
    }
    throw NotFoundException.getNotFoundInstance();
  }

}
