package com.ximalaya.ting.lite.devtools;

import android.content.Context;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication;
import com.ximalaya.ting.android.host.manager.bundleframework.route.RouterConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.DevToolsActionRouter;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * Created by huifeng.qin on 2021/5/24.
 */
public class DevToolsApplication implements IApplication<DevToolsActionRouter> {
    private static final String TAG = DevToolsApplication.class.getSimpleName();
    Context mAppContext;

    @Override
    public void attachBaseContext(Context context) {
        this.mAppContext = context;
        Logger.i(TAG, "DevToolsApplication attachBaseContext");
    }

    @NonNull
    @Override
    public Class onCreateAction() {
        return DevToolsActionRouter.class;
    }

    @Override
    public void onCreate(DevToolsActionRouter actionRouter) {
        try {
            actionRouter.addAction(RouterConstant.ACTIVITY_ACTION, new DevToolsActivityActionImpl());
            actionRouter.addAction(RouterConstant.FRAGMENT_ACTION, new DevToolsFragmentActionImpl());
            actionRouter.addAction(RouterConstant.FUNCTION_ACTION, new DevToolsFunctionActionImpl());
        } catch (Exception e) {
            e.printStackTrace();
        }
        Logger.i(TAG, "DevToolsApplication onCreate");
    }

    @Override
    public void initApp() {
        Logger.i(TAG, "DevToolsApplication initApp");
    }

    @Override
    public void exitApp() {
        Logger.i(TAG, "DevToolsApplication exitApp");
    }
}
