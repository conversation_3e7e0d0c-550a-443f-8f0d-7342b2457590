package com.ximalaya.ting.lite.devtools.activity;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.RadioGroup;
import android.widget.TextView;


import com.sina.util.dnscache.DNSCache;
import com.sina.util.dnscache.constants.PreferenceConstantsInDNSCache;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.lite.devtools.R;

import java.util.UUID;


/**
 * Created by nali on 2018/5/3.
 *
 * <AUTHOR>
 */
public class DevToolsActivity extends BaseFragmentActivity2 {

    private TextView mBtnDevToolsPageClose;
    private RadioGroup mRgChooseEnv;

    private Button mBtnRecreateDevicesId;

    @Override
    protected void onCreate(Bundle savedState) {
        super.onCreate(savedState);

        setContentView(R.layout.main_act_dev_tools_page);

        initUi();

        initListener();

        initDevSwitch();
    }

    /**
     * 环境切换
     */
    private void initDevSwitch() {
        int requestEnvironment = SharedPreferencesUtil.getInstance(this).getInt(PreferenceConstantsInHost.TINGMAIN_KEY_REQUEST_ENVIRONMENT, !ConstantsOpenSdk.isDebug || !AppConstants.isDebugSvrTest ? AppConstants.ENVIRONMENT_ON_LINE : AppConstants.ENVIRONMENT_TEST);
        if (requestEnvironment == AppConstants.ENVIRONMENT_ON_LINE) {
            mRgChooseEnv.check(R.id.devtools_release);
        } else if (requestEnvironment == AppConstants.ENVIRONMENT_TEST) {
            mRgChooseEnv.check(R.id.devtools_debug);
        } else if (requestEnvironment == AppConstants.ENVIRONMENT_UAT) {
            mRgChooseEnv.check(R.id.devtools_uat);
        }
        UrlConstants.getInstanse().switchOnline(requestEnvironment);
    }

    private void initUi() {
        mBtnDevToolsPageClose = findViewById(R.id.devtools_btn_dev_tools_page_close);
        mRgChooseEnv = findViewById(R.id.devtools_choose_environment);
        mBtnRecreateDevicesId = findViewById(R.id.devtools_btn_create_uuid);
    }

    private void initListener() {
        //关闭页面点击
        mBtnDevToolsPageClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        //环境切换
        mRgChooseEnv.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {

                int environment = AppConstants.ENVIRONMENT_ON_LINE;
                if (checkedId == R.id.devtools_release) {
                    environment = AppConstants.ENVIRONMENT_ON_LINE;
                } else if (checkedId == R.id.devtools_debug) {
                    environment = AppConstants.ENVIRONMENT_TEST;
                } else if (checkedId == R.id.devtools_uat) {
                    environment = AppConstants.ENVIRONMENT_UAT;
                }
                //app环境发生变化，重置播放器内容
                XmPlayerManager instance = XmPlayerManager.getInstance(DevToolsActivity.this);
                if (instance.isPlaying()) {
                    instance.pause();
                }
                instance.clearCurTrackCache();
                instance.clearPlayList();
                instance.clearPlayCache();
                instance.clearAllLocalHistory();
                //切换环境
                UrlConstants.getInstanse().switchOnline(environment);
                // DNS 去掉之前的缓存
                SharedPreferences sharedPreferences = MainApplication.getMyApplicationContext().getSharedPreferences(DNSCache.isMainProcess ? PreferenceConstantsInDNSCache.DNSCache_FILENAME_INIT_CONFIG_SP : PreferenceConstantsInDNSCache.DNSCache_FILENAME_INIT_CONFIG_SP_2, Context.MODE_PRIVATE);
                sharedPreferences.edit().remove(PreferenceConstantsInDNSCache.DNSCache_KEY_INIT_CONFIG_SP_KEY).apply();
                SharedPreferencesUtil.getInstance(DevToolsActivity.this).saveInt(PreferenceConstantsInHost.TINGMAIN_KEY_REQUEST_ENVIRONMENT, environment);
            }
        });
        //重新生成uuid
        mBtnRecreateDevicesId.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String uuid = UUID.randomUUID().toString();
                SharedPreferencesUtil.getInstance(DevToolsActivity.this).saveString(PreferenceConstantsInHost.KEY_UUID_FOR_TEST, uuid);
                CustomToast.showSuccessToast("修改成功");
            }
        });
    }
}
