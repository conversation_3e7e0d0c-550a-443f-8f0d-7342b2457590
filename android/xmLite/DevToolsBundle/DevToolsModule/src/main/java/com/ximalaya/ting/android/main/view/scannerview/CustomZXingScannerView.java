package com.ximalaya.ting.android.main.view.scannerview;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.TypedValue;

import com.ximalaya.ting.android.framework.util.BaseUtil;


/**
 * <AUTHOR> on 17/3/27.
 */

public class CustomZXingScannerView extends ZXingScannerView {
    public CustomZXingScannerView(Context context) {
        super(context);

    }

    public CustomZXingScannerView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);

    }

    @Override
    protected IViewFinder createViewFinderView(Context context) {
        return new CustomViewFinderView(context);
    }

//    @Override
//    protected View getHintView() {
//        return LayoutInflater.from(getContext()).inflate(R.layout.main_view_qrscan_hint, null, false);
//    }

    private static class CustomViewFinderView extends ViewFinderView {
        public static final String TRADE_MARK_TEXT = "将二维码放入框内，自动扫描";
        public static final int TRADE_MARK_TEXT_SIZE_SP = 16;
        public final Paint PAINT = new Paint();

        public CustomViewFinderView(Context context) {
            super(context);
            init();
        }

        public CustomViewFinderView(Context context, AttributeSet attrs) {
            super(context, attrs);
            init();
        }

        private void init() {
            PAINT.setColor(Color.WHITE);
            PAINT.setAntiAlias(true);
            float textPixelSize = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP,
                    TRADE_MARK_TEXT_SIZE_SP, getResources().getDisplayMetrics());
            PAINT.setTextSize(textPixelSize);
            PAINT.setColor(0xFFCCCCCC);
            setSquareViewFinder(true);
        }

        @Override
        public void onDraw(Canvas canvas) {
            super.onDraw(canvas);
            drawTradeMark(canvas);
        }

        private void drawTradeMark(Canvas canvas) {
            Rect framingRect = getFramingRect();
            float tradeMarkTop;
            float tradeMarkLeft;

            int gap;
            if (getContext() == null) {
                gap = 40;
            } else {
                gap = BaseUtil.dp2px(getContext(), 20);
            }

            if (framingRect != null) {
                //tradeMarkTop = framingRect.top - PAINT.getTextSize() - 10;
                tradeMarkTop = framingRect.bottom + PAINT.getTextSize() + gap;
                int textWidth = (int) PAINT.measureText(TRADE_MARK_TEXT);// 得到总体长度
                tradeMarkLeft = framingRect.left + ((framingRect.width() - textWidth) / 2);
                canvas.drawText(TRADE_MARK_TEXT, tradeMarkLeft, tradeMarkTop, PAINT);
            } else {
                tradeMarkTop = canvas.getHeight() - gap;
                int textWidth = (int) PAINT.measureText(TRADE_MARK_TEXT);// 得到总体长度
                tradeMarkLeft = (canvas.getWidth() - textWidth) / 2;
                canvas.drawText(TRADE_MARK_TEXT, tradeMarkLeft, tradeMarkTop, PAINT);
            }

        }
    }
}
