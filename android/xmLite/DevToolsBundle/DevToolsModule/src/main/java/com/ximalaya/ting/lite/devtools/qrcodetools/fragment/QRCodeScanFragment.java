package com.ximalaya.ting.lite.devtools.qrcodetools.fragment;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.google.zxing.BinaryBitmap;
import com.google.zxing.RGBLuminanceSource;
import com.google.zxing.Result;
import com.google.zxing.common.GlobalHistogramBinarizer;
import com.google.zxing.common.HybridBinarizer;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FileProviderUtil;
import com.ximalaya.ting.android.framework.util.fixtoast.ToastCompat;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IPhotoAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.view.scannerview.MyMultiFormatReader;
import com.ximalaya.ting.android.main.view.scannerview.ZXingScannerView;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;

import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;
import com.ximalaya.ting.lite.devtools.R;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR> on 17/3/23.
 */

public class QRCodeScanFragment extends BaseFragment2 implements ZXingScannerView.ResultHandler, IPhotoAction, View.OnClickListener {

    private static final String TAG = "QRCodeScanFragment";
    private static final String QR_CROP_IMAGE = "qr_crop.jpg";
    private static final int REQUEST_CODE_ASK_CAMERA_PERMISSION = 1;
    private ZXingScannerView mQRCodeView;
    private boolean mIsScanInterrupted = false;
    private String result;

    public QRCodeScanFragment() {
        super(AppConstants.isPageCanSlide, null);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getActivity() != null && getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).addPhotoActionListener(this);
        }
        new UserTracking().setItem("扫码页").statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_VIEW_ITEM);
    }

    @Override
    public void onStart() {
        super.onStart();
        Logger.d("feiwen", "onStart**********");
        //如果项目需要在页面可见时进行权限申请，请放在onStart()方法中，不要写在onResume()中。
        // 可以想象一下，如果写在onResume()中，当用户同意了权限，则无碍，若是点击拒绝，则会回调权限拒绝的方法，
        // 这时系统申请权限的对话框消失不见，会再次调用onResume()请求权限显示对话框，若是还拒绝，
        // 则会再次调用onResume()方法，一直处于死循环。因为系统请求权限的对话框其实一个开启了一个Activity。
        if (getActivity() != null && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            requestPermission();
        }
    }

    @Override
    protected String getPageLogicName() {
        return "扫描二维码";
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 47061;
        super.onMyResume();
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                resumeCamera();
            }
        });
    }

    private void resumeCamera() {
        if (!canUpdateUi()) {
            return;
        }
        if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            startScan();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).removePhotoActionListener(this);
        }
        mIsScanInterrupted = false;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        setTitle("扫一扫");
        mQRCodeView = findViewById(R.id.main_zxing_scanner_view);
        findViewById(R.id.main_qrscan_album_image).setOnClickListener(this);
        findViewById(R.id.main_qrscan_album_text).setOnClickListener(this);
    }

    private void startOpenGalleryProcess() {
        checkPermission(new HashMap<String, Integer>() {
            {
                put(Manifest.permission.READ_EXTERNAL_STORAGE, R.string.host_deny_perm_read_sdcard);
            }
        }, new IMainFunctionAction.IPermissionListener() {
            @Override
            public void havedPermissionOrUseAgree() {
                if (canUpdateUi()) {
                    // 从相册获取
                    getFromPhotos();
                }
            }

            @Override
            public void userReject(Map<String, Integer> noRejectPermiss) {
                CustomToast.showFailToast(R.string.host_deny_perm_read_sdcard);
                if (mQRCodeView != null) {
                    mQRCodeView.startCamera();
                    mIsScanInterrupted = false;
                }
            }
        });
    }

    @Override
    protected void loadData() {
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (!canUpdateUi()) {
                    return;
                }

                if (mQRCodeView != null) {
                    mQRCodeView.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            onPageLoadingCompleted(LoadCompleteType.OK);
                        }
                    }, 100);

                }
            }
        });

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_qrcode_scan;
    }

    private void requestPermission() {
        if (!canUpdateUi()) {
            return;
        }

        //在Fragment中直接用 requestPermissions()请求权限，不要在前面加上ActivityCompat，否则会回调Fragment所在Activity的回调方法；
        requestPermissions(new String[]{Manifest.permission.CAMERA},
                REQUEST_CODE_ASK_CAMERA_PERMISSION);
    }

    private void startScan() {
        if (mQRCodeView != null) {
//            mQRCodeView.setFormats(mFormats);
            mQRCodeView.setResultHandler(QRCodeScanFragment.this);
            mQRCodeView.startCamera();
            mQRCodeView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    getSlideView().getContentView().setBackgroundColor(0x00000000);
                }
            }, 500);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == REQUEST_CODE_ASK_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                doAfterAnimation(this::startScan);
            } else {
                if (shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)) {
                    new DialogBuilder(getActivity()).setTitle(R.string.main_no_camera_permission)
                            .setMessage(R.string.main_allow_carema_permission)
                            .setOkBtn(this::requestPermission)
                            .setCancelBtn(QRCodeScanFragment.this::finish).showConfirm();
                } else {
                    new DialogBuilder(getActivity()).setTitle(R.string.main_no_camera_permission)
                            .setMessage("摄像头权限已被禁用，请前往设置中心开启")
                            .setOkBtn(() -> {
                                try {
                                    DeviceUtil.showInstalledAppDetails(getActivity());
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            })
                            .setCancelBtn(QRCodeScanFragment.this::finish).showConfirm();
                }
            }
        }
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mQRCodeView != null) {
            mQRCodeView.stopCamera();
        }
    }

    private void getFromPhotos() {
        checkPermission(new HashMap<String, Integer>() {{
            put(Manifest.permission.READ_EXTERNAL_STORAGE, R.string.host_deny_perm_read_sdcard);
        }}, new IMainFunctionAction.IPermissionListener() {
            @Override
            public void havedPermissionOrUseAgree() {
                Intent intent = new Intent();
                intent.setAction(Intent.ACTION_PICK);
                //从所有图片中进行选择
                intent.setType("image/*");
                try {
                    if (mActivity != null) {
                        mActivity.startActivityForResult(intent, IPhotoAction.IMAGE_FROM_PHOTOS);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void userReject(Map<String, Integer> noRejectPermiss) {
                CustomToast.showFailToast(R.string.host_deny_perm_read_sdcard);
            }
        });
    }

    @SuppressLint("MissingPermission")
    private void vibrate() {
        if (getActivity() != null) {
            SystemServiceManager.setVibrator(getActivity(), 200);
        }
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public void catchPhoto(int requestCode, Intent data) {
        CustomToast.showToast(R.string.main_scanning_qr_code);
        if (requestCode == IPhotoAction.IMAGE_FROM_PHOTOS) {
            Cursor cursor = null;
            String picturePath;
            try {
                Uri selectedImage = data.getData(); //获取系统返回的照片的Uri
                String[] filePathColumn = {MediaStore.Images.Media.DATA};
                if (getActivity() != null && getActivity().getContentResolver() != null && selectedImage != null) {
                    cursor = getActivity().getContentResolver().query(selectedImage,
                            filePathColumn, null, null, null);//从系统表中查询指定Uri对应的照片
                }
                if (cursor == null) {
                    // 5.0以下手机用这种方式获取路径(如小米4.4)
                    picturePath = selectedImage.getPath();
                } else {
                    cursor.moveToFirst();
                    int columnIndex = cursor.getColumnIndex(filePathColumn[0]);
                    picturePath = cursor.getString(columnIndex);  //获取照片路径
                    cursor.close();
                }
                if (!TextUtils.isEmpty(picturePath)) {
                    new ImageParserTask(this, selectedImage).myexec(picturePath);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
        }
    }

    @Override
    public void cropPhoto() {
        File image = ToolUtil.getTempImageFile(QR_CROP_IMAGE);
        if (image != null && image.exists()) {
            new ImageParserTask(this, null).myexec(image.getAbsolutePath());
        } else {
            mIsScanInterrupted = false;
            CustomToast.showFailToast(R.string.main_cannot_recognize_qr_code);
        }
    }

    @Override
    public void canceled() {

    }

    private void startPhotoZoom(Uri uri) {
        if (getActivity() == null) {
            return;
        }
        try {
            uri = FileProviderUtil.replaceUriIfNeed(uri);
            Intent intent = new Intent("com.android.camera.action.CROP");
            intent.setDataAndType(uri, "image/*");
            intent.putExtra("crop", "true");
            intent.putExtra("aspectX", 1);
            intent.putExtra("aspectY", 1);
            intent.putExtra("outputX", 640);
            intent.putExtra("outputY", 640);
            intent.putExtra("scale", true);
            intent.putExtra("scaleUpIfNeeded", true);
            intent.putExtra(MediaStore.EXTRA_OUTPUT, FileProviderUtil.replaceUriIfNeed(ToolUtil.getImageUri(QR_CROP_IMAGE)));
            intent.putExtra("return-data", false);
            intent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());
            intent.putExtra("noFaceDetection", true);
            FileProviderUtil.setIntentForCameraCrop(intent);
            getActivity().startActivityForResult(intent, IPhotoAction.IMAGE_CROP);
            if (mContext != null) {
                ToastCompat toast = ToastCompat.makeText(mContext, mContext.getText(R.string.main_drag_clipping_area), Toast.LENGTH_LONG);
                toast.setGravity(Gravity.CENTER, 0, 0);
                toast.show();
            }
        } catch (Exception e) {
            e.printStackTrace();
            mIsScanInterrupted = false;
        }
    }


    @Override
    public void handleResult(Result barcodeResult) {
        if (!canUpdateUi()) {
            return;
        }
        // 如果是解析相册时就不处理result
        if (mIsScanInterrupted) {
            return;
        }
        if (barcodeResult != null) {
            result = barcodeResult.getText();
        } else {
            result = "";
        }
        Logger.i(TAG, "解析结果 result: " + result);
        if (!TextUtils.isEmpty(result) && result.startsWith("\uFEFF")) {
            //URL中带中文时，解析出的字符串会自带BOM头(efbbbf)导致iting解析失败，在这里去除即可
            result = result.substring(1);
        }
        handleResult();
    }

    private void handleResult() {
        if (!canUpdateUi()) {
            return;
        }
        //震动
        vibrate();
        if (result == null) {
            result = "";
        }
        if (TextUtils.isEmpty(result)) {
            showScanErrorDialog("不支持该二维码类型");
        } else if (result.startsWith("http") || result.startsWith("iting") || result.startsWith("uting")) {
            showOpenUrlDialog(result);
        } else {
            showCopyTextDialog(result);
        }
    }

    private void showScanErrorDialog(String errorMsg) {
        new DialogBuilder(getActivity())
                .setMessage(errorMsg)
                .setOkBtn("重新扫描", new DialogBuilder.DialogCallback() {

                    @Override
                    public void onExecute() {
                        startScan();
                    }
                }).setCancelBtn("关闭", new DialogBuilder.DialogCallback() {

            @Override
            public void onExecute() {
                finish();
            }
        }).setOutsideTouchExecCallback(false).setOutSideCancelListener(new DialogBuilder.DialogCallback() {
            @Override
            public void onExecute() {
                startScan();
            }
        }).showConfirm();
    }

    private boolean showOpenUrlDialog(final String url) {
        if (mActivity == null) {
            return false;
        }
        if (!result.startsWith("http") && !result.startsWith("iting") && !result.startsWith("uting")) {
            return false;
        }
        new DialogBuilder(mActivity)
                .setTitle("是否打开以下链接?")
                .setMessage(url)
                .setOkBtn("确定", new DialogBuilder.DialogCallback() {
                    @Override
                    public void onExecute() {
                        finishFragment();
                        ToolUtil.clickUrlAction(QRCodeScanFragment.this, url, null);
                    }
                })
                .setCancelBtn("取消", new DialogBuilder.DialogCallback() {
                    @Override
                    public void onExecute() {
                        if (mQRCodeView != null) {
                            mQRCodeView.setResultHandler(QRCodeScanFragment.this);
                            mQRCodeView.startCamera();
                        }
                    }
                })
                .showConfirm();
        return true;
    }


    private boolean showCopyTextDialog(String contest) {
        if (mActivity == null) {
            return false;
        }
        new DialogBuilder(mActivity)
                .setTitle("扫描结果")
                .setMessage(contest)
                .setOkBtn("复制", new DialogBuilder.DialogCallback() {
                    @Override
                    public void onExecute() {
                        ClipboardManager clipboardManager = (ClipboardManager) mActivity.getSystemService(Context.CLIPBOARD_SERVICE);
                        if (clipboardManager != null) {
                            clipboardManager.setPrimaryClip(ClipData.newPlainText(null, contest));
                        }
                        if (mQRCodeView != null) {
                            mQRCodeView.setResultHandler(QRCodeScanFragment.this);
                            mQRCodeView.startCamera();
                        }
                    }
                })
                .setCancelBtn("取消", new DialogBuilder.DialogCallback() {
                    @Override
                    public void onExecute() {
                        if (mQRCodeView != null) {
                            mQRCodeView.setResultHandler(QRCodeScanFragment.this);
                            mQRCodeView.startCamera();
                        }
                    }
                })
                .showConfirm();
        return true;
    }


    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_qrscan_album_image || id == R.id.main_qrscan_album_text) {
            mIsScanInterrupted = true;
            new UserTracking()
                    .setSrcPage("扫码页")
                    .setSrcModule("roofTool")
                    .setItem("button")
                    .setItemId("相册")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            startOpenGalleryProcess();
        }
    }

    private static class ImageParserTask extends MyAsyncTask<String, Void, Result> {

        private WeakReference<QRCodeScanFragment> mRef;
        /**
         * 原始图片路径(未剪切过的图片)
         */
        private Uri mOriginImageUri;

        public ImageParserTask(QRCodeScanFragment fragment, Uri uri) {
            mRef = new WeakReference<>(fragment);
            mOriginImageUri = uri;
        }

        @Override
        protected Result doInBackground(String... paths) {
            String path = paths[0];
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            options.inSampleSize = 1;
            BitmapFactory.decodeFile(path, options);
            int bitmapSize = options.outWidth * options.outHeight;
            if (bitmapSize > 600 * 600) {
                options.inSampleSize = (int) Math.sqrt(bitmapSize / 600d / 600d);
            }
            options.inJustDecodeBounds = false;
            Bitmap scanBitmap = BitmapFactory.decodeFile(path, options);
            if (scanBitmap == null) {
                return null;
            }
            // 获取bitmap的宽高，像素矩阵
            int width = scanBitmap.getWidth();
            int height = scanBitmap.getHeight();
            int[] pixels = new int[width * height];
            scanBitmap.getPixels(pixels, 0, width, 0, 0, width, height);
            RGBLuminanceSource source = new RGBLuminanceSource(width, height, pixels);
            BinaryBitmap binaryBitmap = new BinaryBitmap(new HybridBinarizer(source));
            MyMultiFormatReader mMultiFormatReader = new MyMultiFormatReader();
            try {
                return mMultiFormatReader.decode(binaryBitmap);
            } catch (Exception e) {
                try {
                    // lower cpu phone
                    return mMultiFormatReader.decode(new BinaryBitmap(new GlobalHistogramBinarizer(source)));
                } catch (Throwable e2) {
                    e2.printStackTrace();
                }
            }
            return null;
        }

        @Override
        protected void onPostExecute(Result result) {
            super.onPostExecute(result);
            QRCodeScanFragment fragment = mRef.get();
            if (fragment == null) {
                return;
            }
            if (result == null || TextUtils.isEmpty(result.getText())) {
                // 不为空说明是原始图片，可进行裁剪
                if (mOriginImageUri != null) {
                    fragment.startPhotoZoom(mOriginImageUri);
                } else {
                    CustomToast.showFailToast(R.string.main_cannot_recognize_qr_code);
                }
            } else {
                fragment.mIsScanInterrupted = false;
                fragment.handleResult(result);
            }
        }
    }

    @Override
    protected boolean isShowCoinGuide() {
        return false;
    }

    @Override
    protected boolean isShowSevenDay() {
        return false;
    }
}
