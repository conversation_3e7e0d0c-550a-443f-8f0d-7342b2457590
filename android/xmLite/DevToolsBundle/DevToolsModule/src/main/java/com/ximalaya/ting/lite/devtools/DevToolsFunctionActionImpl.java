package com.ximalaya.ting.lite.devtools;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.devtools.IDevToolsFunctionActionRouter;
import com.ximalaya.ting.lite.devtools.activity.DevToolsActivity;
import com.ximalaya.ting.lite.devtools.qrcodetools.fragment.QRCodeScanFragment;


/**
 * Created by huifeng.qin on 2021/5/24.
 */
public class DevToolsFunctionActionImpl implements IDevToolsFunctionActionRouter {

    @Override
    public void testDevToolsBundleSuccess(String log) {
        Log.e("DevToolsFunctionAction", "DevToolsBundle加载成功=" + log);
    }

    @Override
    public void openScanDevTools() {
        Activity topActivity = BaseApplication.getTopActivity();
        if (!(topActivity instanceof MainActivity)) {
            return;
        }
        MainActivity mainActivity = (MainActivity) topActivity;
        mainActivity.startFragment(new QRCodeScanFragment());
    }

    @Override
    public void openDevToolsPageActivity() {
        Context context = BaseApplication.getTopActivity();
        if (context == null) {
            context = BaseApplication.getMainActivity();
        }
        if (context == null) {
            context = BaseApplication.getMyApplicationContext();
        }
        Intent intent = new Intent(context, DevToolsActivity.class);
        context.startActivity(intent);
    }
}
