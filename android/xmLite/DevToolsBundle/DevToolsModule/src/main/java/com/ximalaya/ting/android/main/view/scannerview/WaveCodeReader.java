package com.ximalaya.ting.android.main.view.scannerview;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.ChecksumException;
import com.google.zxing.DecodeHintType;
import com.google.zxing.FormatException;
import com.google.zxing.NotFoundException;
import com.google.zxing.RGBLuminanceSource;
import com.google.zxing.Reader;
import com.google.zxing.Result;
import com.google.zxing.common.BitArray;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.common.HybridBinarizer;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by roc on 2018/12/20.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15618953973
 */
public class WaveCodeReader implements Reader {

  public static String TAG = "WaveDeocode";
  private CRC8 mCrc8 = new CRC8();
  private int SPECIAL_CODE_LEN = 2;
  private int TYPE_LEN = 1;
  private int REAL_LEN = 2;
  private int MASK_LEN = 1;
  private int CRC_NUM_LEN = 3;
  private int LAST_SPECIAL_CODE_LEN = 1;

  public static int URL_TYPE = 0;
  public static int ALBUM_TYPE = 1;
  public static int ANCHOR_TYPE = 2;
  public static int SOUND_TYPE = 3;
  public static int UID_TYPE = 4;

  public boolean isDebug = true;

  private String[] waveMaskArr = {
    "48630364202100",
    "01496310135310",
    "04987223454210",
    "01496310024310",
    "24865300000248",
    "58643000463013",
    "00024875420347",
    "02530004878675",
    "36786865300023",
    "00365868563200"
  };

  private int count = 0;
  private int mType = 0;

  int[] FIRST_START_END_PATTERN = {1, 1, 1};//第一次直接匹配2条黑线
  int[] START_END_PATTERN = {1, 1, 1, 1, 1};//一次性匹配3条黑线，失败则快速报错

  @Override
  public Result decode(BinaryBitmap image) throws NotFoundException, ChecksumException, FormatException {
    return decode(image, null);
  }

  @Override
  public Result decode(BinaryBitmap image, Map<DecodeHintType, ?> hints) throws NotFoundException, ChecksumException, FormatException {
    Result result = null;
    result = doDecode(image, hints);
    return result;
  }

  @Override
  public void reset() {

  }

  public MyLine getLine(int[] centerPoint, BitMatrix bitMatrix, int maxDeltaX,boolean isReverse) {

    int[] highestPoint = Arrays.copyOf(centerPoint, 2);
    int[] lowestPoint = Arrays.copyOf(centerPoint, 2);
    Logger.i(TAG, "centerPoint[0]:" + centerPoint[0] + "centerPoint[1]" + centerPoint[1]);
//    int[] nextValidPoint = centerPoint;
    int noFoundYBlackPoint = 0;//为了应对中间有间隔线条的波形码
    for (int y = centerPoint[1]; y < bitMatrix.getHeight(); y++) {
      int delta = 0;
      boolean hasFoundBlackPoint = false;
      while (delta < maxDeltaX) {
        if (bitMatrix.get(highestPoint[0] + delta, y)==!isReverse) {
          highestPoint[0] = highestPoint[0] + delta;
          highestPoint[1] = y;
          hasFoundBlackPoint = true;
          break;
        } else if (delta != 0 && bitMatrix.get(highestPoint[0] - delta, y)==!isReverse) {
          highestPoint[0] = highestPoint[0] - delta;
          highestPoint[1] = y;
          hasFoundBlackPoint = true;
          break;
        }
        delta++;
      }
      if (!hasFoundBlackPoint) {
        noFoundYBlackPoint++;
      }
      if (noFoundYBlackPoint > 3) {
        break;
      }
    }
    noFoundYBlackPoint = 0;
    for (int y = centerPoint[1]; y > 0; y--) {
      int delta = 0;
      boolean hasFoundBlackPoint = false;
      while (delta < maxDeltaX) {
        if (bitMatrix.get(lowestPoint[0] + delta, y)==!isReverse) {
          lowestPoint[0] = lowestPoint[0] + delta;
          lowestPoint[1] = y;
          hasFoundBlackPoint = true;
          break;
        }
        if (delta != 0 && bitMatrix.get(lowestPoint[0] - delta, y)==!isReverse) {
          lowestPoint[0] = lowestPoint[0] - delta;
          lowestPoint[1] = y;
          hasFoundBlackPoint = true;
          break;
        }
        delta++;
      }
      if (!hasFoundBlackPoint) {
        noFoundYBlackPoint++;
      }
      if (noFoundYBlackPoint > 3) {
        break;
      }
    }
    MyLine line = new MyLine(lowestPoint, highestPoint);
    line.centerPoint = centerPoint;
    return line;
  }

  private String getRightPossibleNum(List<MyLine> lines, int maxLen, int verifyNum) {

    if(maxLen > lines.size()){
      Logger.i(TAG, "getRightPossibleNum maxLen too long:"+maxLen+" lines.size():"+lines.size());
      return null;
    }

    StringBuilder numStr = new StringBuilder();
    for (int i = 0; i < maxLen; i++) {
      MyLine line = lines.get(i);
      //跳过验证码
      if (i != 6 && i != 7 && i != 8) {
        numStr.append(line.realNum);
      }
    }

    mCrc8.reset();
    mCrc8.update(numStr.toString().getBytes());
    int totalCrcNum = (int) (mCrc8.getValue() % 1000);
    Logger.i(TAG, "totalCrcNum:" + totalCrcNum + " numStr:" + numStr + " verifyNum:" + verifyNum);
    if (totalCrcNum == verifyNum) {
      return numStr.toString();
    }

    return null;
  }

  public float getAngle(int[] pointX, int[] pointY) {
    return (float) Math.toDegrees(Math.atan2(pointY[0] - pointX[0], pointY[1] - pointX[1]));
  }

  private Bitmap rotateBitmap(BinaryBitmap binaryBitmap, float[] translate, float degrees, float[] anchorPoint) throws NotFoundException {

    int width = binaryBitmap.getWidth();
    int height = binaryBitmap.getHeight();

    int[] pixels = new int[width * height];

    for (int y1 = 0; y1 < height; y1++) {
      int offset = y1 * width;
      for (int x1 = 0; x1 < width; x1++) {
        pixels[offset + x1] = binaryBitmap.getBlackMatrix().get(x1, y1) ? 0xFF000000 : 0xFFFFFFFF;
      }
    }

    Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
    bitmap.setPixels(pixels, 0, width, 0, 0, width, height);

    Bitmap bitmap2 = Bitmap.createBitmap(bitmap.getWidth(), bitmap.getHeight(), bitmap.getConfig());
    Matrix mtx = new Matrix();
    mtx.setTranslate(translate[0], translate[1]);
    mtx.postRotate(degrees, anchorPoint[0], anchorPoint[1]);
    Canvas canvas = new Canvas(bitmap2);
    canvas.drawBitmap(bitmap, mtx, null);

    return bitmap2;
  }

  private BitMatrix mInverseBitMatrix;
  private BitMatrix mFlipBitMatrix;

  /**
   * We're going to examine rows from the middle outward, searching alternately above and below the
   * middle, and farther out each time. rowStep is the number of rows between each successive
   * attempt above and below the middle. So we'd scan row middle, then middle - rowStep, then
   * middle + rowStep, then middle - (2 * rowStep), etc.
   * rowStep is bigger as the image is taller, but is always at least 1. We've somewhat arbitrarily
   * decided that moving up and down by about 1/16 of the image is pretty good; we try more of the
   * image if "trying harder".
   *
   * @param image The image to decode
   * @param hints Any hints that were requested
   * @return The contents of the decoded barcode
   * @throws NotFoundException Any spontaneous errors which occur
   */
  private Result doDecode(BinaryBitmap image,
                          Map<DecodeHintType, ?> hints) throws NotFoundException {

    Logger.i(TAG,"doDecode new image--------------------->");
    mInverseBitMatrix = null;
    mFlipBitMatrix = null;
    int width = image.getWidth();
    int height = image.getHeight();
    BitArray row = new BitArray(width);
    boolean tryHarder = hints != null && hints.containsKey(DecodeHintType.TRY_HARDER);
    int rowStep = Math.max(1, height >> (tryHarder ? 8 : 5));
    int maxLines;
    if (tryHarder) {
      maxLines = height; // Look at the whole image, not just the center
    } else {
      maxLines = 25; // 15 rows spaced 1/32 apart is roughly the middle half of the image
    }

//    boolean shouldRotateRetry = false;

    int middle = height / 2;
    for (int x = 0; x < maxLines; x++) {
      // Scanning from the middle out. Determine which row we're looking at next:
      int rowStepsAboveOrBelow = (x + 1) / 2;
      boolean isAbove = (x & 0x01) == 0; // i.e. is x even?
      int rowNumber = middle + rowStep * (isAbove ? rowStepsAboveOrBelow : -rowStepsAboveOrBelow);
      if (rowNumber < 0 || rowNumber >= height) {
        // Oops, if we run off the top or bottom, stop
        break;
      }
      try {
        row = image.getBlackRow(rowNumber, row);
      } catch (NotFoundException ignored) {
        continue;
      }

//      int[] bArr = new int[row.getSize()];
//      for(int k=0;k<bArr.length;k++){
//        bArr[k] = row.get(k)?1:0;
//      }
//      Logger.i(WaveCodeReader.TAG, "bArr:" + Arrays.toString(bArr));

//      if(isDebug) {
//        int[] testPixels = new int[width * height];
//
//        for (int y1 = 0; y1 < height; y1++) {
//          int offset = y1 * width;
//          for (int x1 = 0; x1 < width; x1++) {
//            testPixels[offset + x1] = image.getBlackMatrix().get(x1, rowNumber) ? 0xFF000000 : 0xFFFFFFFF;
//          }
//        }
//        Bitmap testBitmap = Bitmap.createBitmap(width, height,
//          Bitmap.Config.ARGB_8888);
//        testBitmap.setPixels(testPixels, 0, width, 0, 0, width, height);
//        MainActivity.setImageBitmap2(testBitmap);
//      }
//
      //图片是否进行了反色
      boolean isReverse = false;
      int[] startGuardPattern = null;
      try {
        startGuardPattern = PatternMatchUtil.findGuardPatternNew(row, 0, FIRST_START_END_PATTERN,width/(21*2));
        isReverse = startGuardPattern[2] == 1;
        Logger.i(WaveCodeReader.TAG, "isReverse:"+isReverse+" startGuardPattern:"+Arrays.toString(startGuardPattern));
      } catch (NotFoundException e) {
        e.printStackTrace();
        Logger.i(WaveCodeReader.TAG, "startGuardPattern Test find Image null");
        continue;
      }

      BitMatrix bitMatrix = image.getBlackMatrix();

      float halfWidth = (startGuardPattern[1] - startGuardPattern[0]) / 6.0f;
      int firstPoint[] = {0, 0};
      firstPoint[0] = (int) (startGuardPattern[0] + halfWidth);
      firstPoint[1] = rowNumber;
      MyLine line0 = getLine(firstPoint, bitMatrix, (int) halfWidth,isReverse);
      line0.recalculateCenterPoint();
      line0.width = (int) (halfWidth * 2);
      Logger.i(TAG, "LineHeight0:" + line0.getLineHeight()+" line0.width:"+line0.width+" halfWidth:"+halfWidth);

      if (line0.getLineHeight() < 10) {
        throw NotFoundException.getNotFoundInstance();
      }

      int secondPoint[] = {0, 0};
      secondPoint[0] = (int) (startGuardPattern[1] - halfWidth);
      secondPoint[1] = rowNumber;
      MyLine line1 = getLine(secondPoint, bitMatrix, (int) halfWidth,isReverse);
      line1.recalculateCenterPoint();
      line1.width = (int) (halfWidth * 2);
      Logger.i(TAG, "LineHeight1:" + line1.getLineHeight());
      if (line1.getLineHeight() < 10) {
        throw NotFoundException.getNotFoundInstance();
      }

      int baseRectNum = 3;//生成波形码的时候定义的
//      double oneRect = line0.getLineHeight() - line1.getLineHeight();

      double oneRect = (line0.getLineHeight() + line1.getLineHeight()) / (8d+7d);

      int firstGuardNum = MyLine.getRealNum(line0, oneRect, baseRectNum);

      Logger.i(WaveCodeReader.TAG, "firstGuardNum:"+firstGuardNum);

      //图片是否旋转了180度
      boolean isFlip = false;

      if (firstGuardNum != 5) {
        //目前设计的声纹码一份的大小OneRect其实是和线条的宽度*2是一致的
        int lastLineNum = MyLine.getRealNum(line0, halfWidth * 2 * 2, baseRectNum);
        Logger.i(WaveCodeReader.TAG, "lastLineNum:" + lastLineNum + " halfWidth*2*2:" + halfWidth * 2 * 2);
        //目前设计的声纹码最后一位数字是5
        if (lastLineNum == 5) {
          Logger.i(WaveCodeReader.TAG, "findFirstLine start flip img");
          //说明图片有可能被旋转了180度
          isFlip = true;
          oneRect = halfWidth * 2 * 2;
          firstGuardNum = 5;
        }
      }

      if (firstGuardNum != 5) {
        Logger.i(TAG, "firstGuardNum != 5:" + firstGuardNum);
        continue;
      }

      int secondGuardNum = MyLine.getRealNum(line1, oneRect, baseRectNum);

      if (!isFlip && secondGuardNum != 4) {
        Logger.i(TAG, "secondGuardNum:" + secondGuardNum);
        continue;
      }

      Logger.i(TAG, "firstGuardNum:" + firstGuardNum + " secondGuardNum:" + secondGuardNum +" oneRect:"+oneRect);

//      if(isDebug) {
//        int[] testPixels = new int[width * height];
//
//        for (int y1 = 0; y1 < height; y1++) {
//          int offset = y1 * width;
//          for (int x1 = 0; x1 < width; x1++) {
//            testPixels[offset + x1] = bitMatrix.get(x1, y1) ? 0xFF000000 : 0xFFFFFFFF;
//          }
//        }
//        Bitmap testBitmap = Bitmap.createBitmap(width, height,
//          Bitmap.Config.ARGB_8888);
//        testBitmap.setPixels(testPixels, 0, width, 0, 0, width, height);
//        MainActivity.setImageBitmap(testBitmap);
//      }

      //比较两根线的斜率，看看两根线是否平行，并且根据斜率旋转整张图片，再进行扫描
      float degree0 = getAngle(line0.lowestPoint, line0.highestPoint);
      float degree1 = getAngle(line1.lowestPoint, line1.highestPoint);
      Logger.i(TAG, "degree0:" + degree0 + " degree1:" + degree1);

      if (Math.abs(degree0 - degree1) > 1.5) {
        Logger.i(TAG, "degree0 degree1 差距太多，说明两条线不平行");
        throw NotFoundException.getNotFoundInstance();
      }

      float averageDegree = (degree0 + degree1) / 2;
      int[] line0CenterPoint = line0.getCenterPoint();

      Logger.i(TAG, "line0CenterPoint:" + Arrays.toString(line0CenterPoint)+" width:"+bitMatrix.getWidth()+" height:"+bitMatrix.getHeight());

      int rowOffset = 0;
      int rowNum = line0CenterPoint[1];
      //进行旋转
      if (Math.abs(averageDegree) > 7 ) {

        Logger.i(TAG, "进行了旋转averageDegree0:" + averageDegree);
        float[] translateTargetPoint = {50, image.getHeight() / 2};
        float[] anchorPoint = new float[]{line0CenterPoint[0], line0CenterPoint[1]};
        float[] translate = new float[]{translateTargetPoint[0] - anchorPoint[0], translateTargetPoint[1] - anchorPoint[1]};
        Logger.i(TAG, "translateTargetPoint:"+Arrays.toString(translateTargetPoint)
          +" anchorPoint:"+Arrays.toString(anchorPoint)
          +" translate:" + Arrays.toString(translate));
        Bitmap bitmap = rotateBitmap(image, translate, averageDegree, translateTargetPoint);
        Logger.i(TAG, "旋转成功");
//        MainActivity.setImageBitmap2(bitmap);

        int bitmapWidth = bitmap.getWidth();
        int bitmapHeight = bitmap.getHeight();

        int[] pixels = new int[bitmapWidth * bitmapHeight];
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height);
        RGBLuminanceSource source = new RGBLuminanceSource(bitmapWidth, bitmapHeight, pixels);
        image = new BinaryBitmap(new HybridBinarizer(source));

        bitMatrix = image.getBlackMatrix();
        rowNum = image.getHeight() / 2;
        //获取中心线
        try {
          row = image.getBlackRow(rowNum, row);
        } catch (NotFoundException ignored) {
          break;
        }

        rowOffset = (int) (translateTargetPoint[0] - line0.width);

        Logger.i(TAG, "获取中心线成功 rotate rowOffset:" + rowOffset);
      } else {
        rowNum = line0CenterPoint[1];
        //获取中心线
//        try {
//          row = image.getBlackRow(rowNum, row);
//        } catch (NotFoundException ignored) {
//          break;
//        }
        row = bitMatrix.getRow(rowNum,row);
        rowOffset = line0.getCenterPoint()[0] - line0.width;
        Logger.i(TAG, "获取中心线成功 rowOffset:" + rowOffset+" rowNum:"+rowNum);
      }

      List<MyLine> myLines = new ArrayList<>();

      int end = row.getSize();
      int[] allPattern = {0, 0};
      int patternNum = 7;//一共21条黑线，一次匹配3条，需要匹配7次
      boolean hasFound = false;

      Logger.i(TAG, "开始匹配 isReverse:" + isReverse + " isFlip:" + isFlip);

      for (int i = 0; i < patternNum && rowOffset < end && !hasFound; i++) {
        int[] guardPattern = PatternMatchUtil.findGuardPattern(row, rowOffset, isReverse, START_END_PATTERN,line0.width*2);
        float tHalfWidth = (guardPattern[1] - guardPattern[0]) / 10;

        Logger.i(TAG, "i:"+i+" guardPattern:"+Arrays.toString(guardPattern)+"tHalfWidth:" + tHalfWidth);

        for (int j = 0; j < 3; j++) {
          Logger.i(TAG, "匹配次数i:"+i+" j:" +j);

          int[] centerPoint = null;
          if (j == 0) {
            centerPoint = new int[]{guardPattern[0] + (int) Math.ceil(tHalfWidth / 2), rowNum};
          } else if (j == 1) {
            centerPoint = new int[]{(guardPattern[1] + guardPattern[0]) / 2, rowNum};
          } else {
            centerPoint = new int[]{guardPattern[1] - (int) Math.ceil(tHalfWidth / 2), rowNum};
          }

          MyLine line = getLine(centerPoint, bitMatrix, (int) tHalfWidth,isReverse);
          myLines.add(line);
          line.setRealNum(MyLine.getRealNum(line, oneRect, baseRectNum));

          if (i == 0) {

            if(j==0){
              if(isFlip){
                oneRect = myLines.get(0).getLineHeight() / 8d;
                Logger.i(TAG, "flip new oneRect:" + oneRect);
                line.setRealNum(MyLine.getRealNum(line, oneRect, baseRectNum));
              }
            }

            if (j == 1) {
              if(!isFlip){
                Logger.i(TAG, "重新计算OneRect");

                double firstLineOneRect = myLines.get(0).getLineHeight() / 8d;
                double secondLineOneRect = myLines.get(1).getLineHeight() / 7d;
                oneRect = (firstLineOneRect + secondLineOneRect) / 2;
                Logger.i(TAG, "firstLineOneRect:" + firstLineOneRect + " secondLineOneRect:" + secondLineOneRect + " newOneRect" + oneRect);

                int firstNum = MyLine.getRealNum(myLines.get(0), oneRect, baseRectNum);
                myLines.get(0).realNum = firstNum;
                Logger.i(TAG, "line1 realNum:" + firstNum + " oneRect:" + oneRect);
                if (firstNum != 5) {
                  Logger.i(TAG, "firstNum:" + firstNum);
                  throw NotFoundException.getNotFoundInstance();
                }

                int secondNum = MyLine.getRealNum(myLines.get(1), oneRect, baseRectNum);
                myLines.get(1).realNum = secondNum;
                Logger.i(TAG, "line2 realNum:" + secondNum);
                if (secondNum != 4) {
                  Logger.i(TAG, "secondNum:" + secondNum);
                  throw NotFoundException.getNotFoundInstance();
                }
              }
            }
          }

          //第一个有效数不可能为0
          if (myLines.size() == 10 && myLines.get(9).realNum == 0) {
            line.realNum = 1;
          }

          Logger.i(TAG, "------------>line" + myLines.size() + " height:" + line.getLineHeight() + " line.realNum:" + line.realNum);

          if (line.realNum < 0) {
            Logger.i(TAG, "line.realNum<0 exception:" + line.realNum);
            throw NotFoundException.getNotFoundInstance();
          }
        }

        if (i == 0) {
          allPattern[0] = guardPattern[0];
          allPattern[1] = guardPattern[1];
        } else {
          if (allPattern[1] > guardPattern[0]) {
            throw NotFoundException.getNotFoundInstance();
          }
          allPattern[1] = guardPattern[1];
        }

        Logger.i(TAG, "i:" + i + " allPattern[0]" + allPattern[0] + " allPattern[1]" + allPattern[1]);

        rowOffset = guardPattern[1] + 1;
      }

      if (myLines.size() != 21) {
        throw NotFoundException.getNotFoundInstance();
      }

      if(isFlip){
        int[] arr = new int[myLines.size()];
        List<MyLine> list = new ArrayList<>(myLines.size());
        for(int i = 0;i<myLines.size();i++){
          MyLine line = myLines.get(myLines.size()-i-1);
          list.add(line);
          arr[i]= line.realNum;
        }
        Logger.i(TAG, "all lines num:" + Arrays.toString(arr));
        myLines = list;
      }else{
        int[] arr = new int[myLines.size()];
        for(int i = 0;i<myLines.size();i++){
          arr[i]= myLines.get(i).realNum;
        }
        Logger.i(TAG, "all lines num2:" + Arrays.toString(arr));
      }

      int realNumLength = myLines.get(3).realNum * 10 + myLines.get(4).realNum;
      Logger.i(TAG, "realNumLength1:" + realNumLength);
      if (realNumLength <= 0) {
        Logger.i(TAG, "有效数字长度识别有误<=0 realNumLength1:" + realNumLength);
        throw NotFoundException.getNotFoundInstance();
      }

      MyLine maskLine = myLines.get(5);
      //容错处理
      if (maskLine.realNum == -1) {
        maskLine.realNum = 0;
      } else if (maskLine.realNum == waveMaskArr.length) {
        maskLine.realNum = waveMaskArr.length - 1;
      }

      if (maskLine.realNum < 0 || maskLine.realNum >= waveMaskArr.length) {
        Logger.i(TAG, "exception maskLine" + myLines.size() + " height:" + maskLine.getLineHeight() + " line.realNum:" + maskLine.realNum);
        throw NotFoundException.getNotFoundInstance();
      }
      String maskArrStr = waveMaskArr[maskLine.realNum];
      int[] maskArr = new int[maskArrStr.length()];
      for (int k = 0; k < maskArr.length; k++) {
        maskArr[k] = Character.getNumericValue(maskArrStr.charAt(k));
      }
      //Logger.i(TAG, "line maskArr:" + Arrays.toString(maskArr));

      for(int i=6;i<myLines.size();i++){
        int maskPosition = i-6;
        if (maskPosition < maskArr.length) {
          int maskNum = maskArr[maskPosition];
          myLines.get(i).taskOffMaskValueFromRealNumAndPossibleNum(maskNum);
        }
      }

      int verifyNum = 0;

      verifyNum += myLines.get(6).realNum * 100;
      verifyNum += myLines.get(7).realNum * 10;
      verifyNum += (myLines.get(8).realNum);

      count = 0;
      int maxLen = realNumLength + SPECIAL_CODE_LEN + TYPE_LEN + REAL_LEN + MASK_LEN + CRC_NUM_LEN;
      mType = myLines.get(2).realNum;

      String totalNumStr = getRightPossibleNum(myLines, maxLen, verifyNum);
      Logger.i(TAG, "totalNumStr0:" + totalNumStr);

      if (totalNumStr == null) {
        //透视校验位已经识别准确或者识别误差太大，则放弃透视变换
        MyLine lastLine = myLines.get(myLines.size() - 1);
        if (lastLine.realNum == 5
          || lastLine.realNum > 7
          || lastLine.realNum < 3) {
          throw NotFoundException.getNotFoundInstance();
        } else {
          Logger.i(TAG, "尝试通过透视变换挽救一下");
          //尝试通过透视变换挽救一下
          MyLine firstLine = myLines.get(0);
          double newOneRect = 0;
          for (int i = 1; i < myLines.size() - 1; i++) {
            MyLine line = myLines.get(i);
            double[] intersectionPoint0 = MyLine.twoLineIntersection(
              MyLine.intArrToDouble(firstLine.highestPoint),
              MyLine.intArrToDouble(lastLine.highestPoint),
              MyLine.intArrToDouble(line.highestPoint),
              MyLine.intArrToDouble(line.lowestPoint));

            double[] intersectionPoint1 = MyLine.twoLineIntersection(
              MyLine.intArrToDouble(firstLine.lowestPoint),
              MyLine.intArrToDouble(lastLine.lowestPoint),
              MyLine.intArrToDouble(line.highestPoint),
              MyLine.intArrToDouble(line.lowestPoint));
            double h = intersectionPoint0[1] - intersectionPoint1[1];
            double realHeight = line.getLineHeight() * (firstLine.getLineHeight() / h);
            if (i == 1) {
              newOneRect = firstLine.getLineHeight() - realHeight;
            }

            int newNum = (int) Math.round(realHeight / newOneRect - baseRectNum);

            if (newNum < 0) {
              newNum = 0;
            } else if (newNum > 9) {
              newNum = 9;
            }

            if (i == 5) {
              if (newNum >= waveMaskArr.length) {
                throw NotFoundException.getNotFoundInstance();
              }
              maskArrStr = waveMaskArr[newNum];
              maskArr = new int[maskArrStr.length()];
              for (int k = 0; k < maskArr.length; k++) {
                maskArr[k] = Character.getNumericValue(maskArrStr.charAt(k));
              }
            } else if (i > 5) {
              int maskNum = maskArr[i - 6];
              if (newNum < maskNum) {
                newNum += 10;
              }
              newNum -= maskNum;
            }

            line.realNum = newNum;
            Logger.i(TAG, "i:" + i + " newNum:" + newNum + " newHeight:" + realHeight + " oldNum:" + line.realNum + " oldHeight:" + line.getLineHeight());
          }

          realNumLength = myLines.get(3).realNum * 10 + myLines.get(4).realNum;
          Logger.i(TAG, "realNumLength2:" + realNumLength);
          if (realNumLength <= 0) {
            Logger.i(TAG, "有效数字长度识别有误<=0 realNumLength2:" + realNumLength);
            throw NotFoundException.getNotFoundInstance();
          }

          verifyNum = 0;

          verifyNum += myLines.get(6).realNum * 100;
          verifyNum += myLines.get(7).realNum * 10;
          verifyNum += (myLines.get(8).realNum);

          maxLen = realNumLength + SPECIAL_CODE_LEN + TYPE_LEN + REAL_LEN + MASK_LEN + CRC_NUM_LEN;

          count = 0;
          mType = myLines.get(2).realNum;
          totalNumStr = getRightPossibleNum(myLines, maxLen, verifyNum);
          Logger.i("intersection", "totalNumStr：" + totalNumStr);

          if (totalNumStr == null) {
            throw NotFoundException.getNotFoundInstance();
          }

        }
      }

      String lastRealNum = totalNumStr.substring(6, totalNumStr.length());

      Logger.i(TAG, "totalNumStr1:" + totalNumStr + " verifyNum:" + verifyNum + " lastRealNum:" + lastRealNum);

      Result decodeResult = new Result(mType + "," + lastRealNum, null, null, BarcodeFormat.UPC_EAN_EXTENSION);

      Logger.i(TAG, "扫描声纹码成功 totalNumStr2:" + totalNumStr + " verifyNum:" + verifyNum + " lastRealNum:" + lastRealNum);

//      if(isDebug) {
//        int[] testPixels2 = new int[width * height];
//
//        for (int y1 = 0; y1 < height; y1++) {
//          int offset = y1 * width;
//          for (int x1 = 0; x1 < width; x1++) {
//            testPixels2[offset + x1] = bitMatrix.get(x1, y1) ? 0xFF000000 : 0xFFFFFFFF;
//          }
//        }
//        Bitmap testBitmap2 = Bitmap.createBitmap(width, height,
//          Bitmap.Config.ARGB_8888);
//        testBitmap2.setPixels(testPixels2, 0, width, 0, 0, width, height);
//        MainActivity.setImageBitmap2(testBitmap2);
//      }

      return decodeResult;

    }

    throw NotFoundException.getNotFoundInstance();
  }

//  private double[] findFirstLine(BitArray row,BitMatrix bitMatrix,int rowNumber,MyLine[] lines) throws NotFoundException{
//
////    int[] bArr = new int[row.getSize()];
////    for(int k=0;k<bArr.length;k++){
////      bArr[k] = row.get(k)?1:0;
////    }
////    Logger.i(WaveCodeReader.TAG, "findFirstLine bArr:" + Arrays.toString(bArr));
//
//    int[] startGuardPattern;
//    try {
//      startGuardPattern = PatternMatchUtil.findGuardPattern(row, 0, false, FIRST_START_END_PATTERN,bitMatrix.getWidth()/(21*2));
//    } catch (NotFoundException e) {
//      e.printStackTrace();
//      Logger.i(WaveCodeReader.TAG, "startGuardPattern find Image null");
//      throw NotFoundException.getNotFoundInstance();
//    }
//
//    float halfWidth = (startGuardPattern[1] - startGuardPattern[0]) / 6.0f;
//    int firstPoint[] = {0, 0};
//    firstPoint[0] = (int) (startGuardPattern[0] + halfWidth);
//    firstPoint[1] = rowNumber;
//    MyLine line0 = getLine(firstPoint, bitMatrix, (int) halfWidth);
//    line0.recalculateCenterPoint();
//    line0.width = (int) (halfWidth * 2);
//    Logger.i(TAG, "LineHeight0:" + line0.getLineHeight()+" line0.width:"+line0.width);
//
//    if (line0.getLineHeight() < 10) {
//      throw NotFoundException.getNotFoundInstance();
//    }
//
//    int secondPoint[] = {0, 0};
//    secondPoint[0] = (int) (startGuardPattern[1] - halfWidth);
//    secondPoint[1] = rowNumber;
//    MyLine line1 = getLine(secondPoint, bitMatrix, (int) halfWidth);
//    line1.recalculateCenterPoint();
//    line1.width = (int) (halfWidth * 2);
//    Logger.i(TAG, "LineHeight1:" + line1.getLineHeight());
//    if (line1.getLineHeight() < 10) {
//      throw NotFoundException.getNotFoundInstance();
//    }
//
//    int baseRectNum = 3;//生成波形码的时候定义的
//    double oneRect = line0.getLineHeight() - line1.getLineHeight();
//    int firstGuardNum = MyLine.getRealNum(line0, oneRect, baseRectNum);
//
//    Logger.i(TAG, "findFirstLine firstGuardNum:" + firstGuardNum);
//    lines[0] = line0;
//    lines[1] = line1;
//    double[] result = {firstGuardNum,oneRect,baseRectNum,halfWidth};
//    return result;
//  }

}
