package com.ximalaya.ting.android.main.view.scannerview;

import com.google.zxing.NotFoundException;
import com.google.zxing.common.BitArray;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.Arrays;

/**
 * Created by roc on 2018/12/20.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15618953973
 */
public class PatternMatchUtil {

    private static final float MAX_AVG_VARIANCE = 0.48f;
    private static final float MAX_INDIVIDUAL_VARIANCE = 0.7f;

    public static int[] findGuardPattern(BitArray row,
                                         int rowOffset,
                                         boolean whiteFirst,
                                         int[] pattern, int maxWidth) throws NotFoundException {
        return findGuardPattern(row, rowOffset, whiteFirst, pattern, new int[pattern.length], maxWidth);
    }

    /**
     * @param row        row of black/white values to search
     * @param rowOffset  position to start search
     * @param whiteFirst if true, indicates that the pattern specifies white/black/white/...
     *                   pixel counts, otherwise, it is interpreted as black/white/black/...
     * @param pattern    pattern of counts of number of black and white pixels that are being
     *                   searched for as a pattern
     * @param counters   array of counters, as long as pattern, to re-use
     * @return start/end horizontal offset of guard pattern, as an array of two ints
     * @throws NotFoundException if pattern is not found
     */
    public static int[] findGuardPattern(BitArray row,
                                         int rowOffset,
                                         boolean whiteFirst,
                                         int[] pattern,
                                         int[] counters, int maxWidth) throws NotFoundException {
        int width = row.getSize();
        rowOffset = whiteFirst ? row.getNextUnset(rowOffset) : row.getNextSet(rowOffset);
        int counterPosition = 0;
        int patternStart = rowOffset;
        int patternLength = pattern.length;
        boolean isWhite = whiteFirst;
        int mergeCount = 0;
        for (int x = rowOffset; x < width; x++) {
            if (row.get(x) != isWhite) {
                counters[counterPosition]++;
            } else {

                //声临其境奖杯，激光打印导致条纹中有很多白色间隙，此处做容错处理（活动已经结束）
                //此处容错处理容易导致宽度为6个像素以下的声纹码识别失败，所有去掉
//        if(counters[counterPosition]<6 && counterPosition>0){
////          Logger.i(WaveCodeReader.TAG,"mergeCount:"+mergeCount+" counters[]0:"+ Arrays.toString(counters));
//          counters[counterPosition-1] += counters[counterPosition];
//          counters[counterPosition] = 0;
//          counterPosition--;
//          isWhite = !isWhite;
//          mergeCount++;
////          Logger.i(WaveCodeReader.TAG,"mergeCount:"+mergeCount+" counters[]1:"+ Arrays.toString(counters));
//          if(counters[counterPosition]>maxWidth){
//            throw NotFoundException.getNotFoundInstance();
//          }
//          continue;
//        }

                mergeCount = 0;
                if (counterPosition == patternLength - 1) {
                    if (patternMatchVariance(counters, pattern, MAX_INDIVIDUAL_VARIANCE) < MAX_AVG_VARIANCE) {
                        return new int[]{patternStart, x};
                    }
                    patternStart += counters[0] + counters[1];
                    System.arraycopy(counters, 2, counters, 0, counterPosition - 1);
                    counters[counterPosition - 1] = 0;
                    counters[counterPosition] = 0;
                    counterPosition--;
                } else {
                    counterPosition++;
                }
                counters[counterPosition] = 1;
                isWhite = !isWhite;
            }
        }
        throw NotFoundException.getNotFoundInstance();
    }


    public static int[] findGuardPatternNew(BitArray row,
                                            int rowOffset,
                                            int[] pattern,
                                            int maxWidth) throws NotFoundException {
        int[] counters = new int[pattern.length];
        int width = row.getSize();
        int counterPosition = 0;
        int patternStart = rowOffset;
        int patternLength = pattern.length;
        boolean isWhite = row.get(rowOffset);
        for (int x = rowOffset; x < width; x++) {
            if (row.get(x) == isWhite) {
                counters[counterPosition]++;
            } else {
                if (counterPosition == patternLength - 1) {
                    if (patternMatchVariance(counters, pattern, MAX_INDIVIDUAL_VARIANCE) < MAX_AVG_VARIANCE) {
                        Logger.i(WaveCodeReader.TAG, "shallReverseImage counters:" + Arrays.toString(counters) + " isWhite:" + isWhite);
                        //匹配到的最后一条为白色，说明需要进行反色
                        //            return isWhite;
                        return new int[]{patternStart, x, isWhite ? 0 : 1};
                    }
                    patternStart += counters[0];
                    System.arraycopy(counters, 1, counters, 0, counters.length - 1);
                    counters[counters.length - 1] = 0;
                    //          counterPosition--;
                } else {
                    counterPosition++;
                }
                counters[counterPosition] = 1;
                isWhite = !isWhite;
                Logger.i(WaveCodeReader.TAG, "shallReverseImage isWhite:" + isWhite);
            }
        }
        throw NotFoundException.getNotFoundInstance();
    }

    protected static float patternMatchVariance(int[] counters,
                                                int[] pattern,
                                                float maxIndividualVariance) {
        int numCounters = counters.length;
        int total = 0;
        int patternLength = 0;
        for (int i = 0; i < numCounters; i++) {
            total += counters[i];
            patternLength += pattern[i];
        }
        if (total < patternLength) {
            // If we don't even have one pixel per unit of bar width, assume this is too small
            // to reliably match, so fail:
            return Float.POSITIVE_INFINITY;
        }

        float unitBarWidth = (float) total / patternLength;
        maxIndividualVariance *= unitBarWidth;

        float totalVariance = 0.0f;
        for (int x = 0; x < numCounters; x++) {
            int counter = counters[x];
            float scaledPattern = pattern[x] * unitBarWidth;
            float variance = counter > scaledPattern ? counter - scaledPattern : scaledPattern - counter;
            if (variance > maxIndividualVariance) {
                return Float.POSITIVE_INFINITY;
            }
            totalVariance += variance;
        }
        return totalVariance / total;
    }

}
