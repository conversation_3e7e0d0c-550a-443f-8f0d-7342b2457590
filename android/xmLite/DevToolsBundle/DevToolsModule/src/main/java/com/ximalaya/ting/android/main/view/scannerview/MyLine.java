package com.ximalaya.ting.android.main.view.scannerview;

/**
 * Created by roc on 2018/12/12.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15618953973
 */
public class MyLine {

    public int[] highestPoint;
    public int[] lowestPoint;
    public int[] centerPoint;
    public int width;
    public int realNum=-1;
//    public int possibleNum=-1;

    public MyLine(int[] lowestPoint, int[] highestPoint, int[] centerPoint){
        this.highestPoint = highestPoint;
        this.lowestPoint = lowestPoint;
        this.centerPoint = centerPoint;
    }

    public MyLine(int[] lowestPoint, int[] highestPoint){
        this.highestPoint = highestPoint;
        this.lowestPoint = lowestPoint;
    }

    public int getLineHeight(){
        return highestPoint[1]-lowestPoint[1];
    }

    public void setRealNum(int num){
        realNum = num;
    }

    public void taskOffMaskValueFromRealNumAndPossibleNum(int maskNum){
        if(realNum < maskNum){
            realNum+=10;
        }
        realNum -= maskNum;
//        if(possibleNum < maskNum){
//            possibleNum+=10;
//        }
//        possibleNum -= maskNum;
        //偏差太大，则不进行纠正
        if(realNum==-1){
            realNum = 0;
        }
        //相同的数字，节省后面的遍历次数
//        if(realNum==possibleNum || Math.abs(realNum-possibleNum)>1){
//            possibleNum = -1;
//        }
    }

    public static int getRealNum(MyLine line, double oneRect, int baseRectNum){
        return (int) Math.round(line.getLineHeight() * 1.0 / oneRect - baseRectNum);
    }

    public float getGradient(){
        float deltaX = (float)(highestPoint[0]-lowestPoint[0]);
        if(deltaX<2){
            return 0;
        }
        float deltaY = (float) (highestPoint[1]-lowestPoint[1]);
        return deltaY/deltaX;
    }

    public int[] getCenterPoint(){
        if(centerPoint==null){
            centerPoint = recalculateCenterPoint();
        }
        return centerPoint;
    }

    public int[] recalculateCenterPoint(){
        centerPoint = new int[]{(highestPoint[0]+lowestPoint[0])/2,(highestPoint[1]+lowestPoint[1])/2};
        return centerPoint;
    }

    public static double[] twoLineIntersection(double[] A, double[] B, double[] C, double[] D){
      // Line AB represented as a1x + b1y = c1
      double a1 = B[1] - A[1];
      double b1 = A[0] - B[0];
      double c1 = a1*(A[0]) + b1*(A[1]);

      // Line CD represented as a2x + b2y = c2
      double a2 = D[1] - C[1];
      double b2 = C[0] - D[0];
      double c2 = a2*(C[0])+ b2*(C[1]);

      double determinant = a1*b2 - a2*b1;

      if (determinant == 0){
        // The lines are parallel. This is simplified
        // by returning a pair of FLT_MAX
        return null;
      }
      else{
        double x = (b2*c1 - b1*c2)/determinant;
        double y = (a1*c2 - a2*c1)/determinant;
        return new double[]{x, y};
      }
    }

    public static double[] intArrToDouble(int[] integerArr){
      double[] doubleArr = new double[integerArr.length];
      for(int i=0; i<integerArr.length; i++) {
        doubleArr[i] = integerArr[i];
      }
      return doubleArr;
    }

}
