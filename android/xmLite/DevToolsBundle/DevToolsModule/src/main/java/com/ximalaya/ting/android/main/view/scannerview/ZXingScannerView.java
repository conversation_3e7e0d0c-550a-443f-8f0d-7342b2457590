package com.ximalaya.ting.android.main.view.scannerview;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Rect;
import android.hardware.Camera;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;
import android.util.AttributeSet;
import android.util.Log;

import com.google.zxing.BinaryBitmap;
import com.google.zxing.LuminanceSource;
import com.google.zxing.Reader;
import com.google.zxing.Result;
import com.google.zxing.common.GlobalHistogramBinarizer;
import com.google.zxing.common.HybridBinarizer;


public class ZXingScannerView extends BarcodeScannerView {
    private static final String TAG = "ZXingScannerView";

    public interface ResultHandler {
        public void handleResult(Result rawResult);
    }

    private Reader mMultiFormatReader;
    //    public static final List<BarcodeFormat> ALL_FORMATS = new ArrayList<BarcodeFormat>();
//    private List<BarcodeFormat> mFormats;
    private ResultHandler mResultHandler;

//    static {
//        ALL_FORMATS.add(BarcodeFormat.UPC_A);
//        ALL_FORMATS.add(BarcodeFormat.UPC_E);
//        ALL_FORMATS.add(BarcodeFormat.EAN_13);
//        ALL_FORMATS.add(BarcodeFormat.EAN_8);
//        ALL_FORMATS.add(BarcodeFormat.RSS_14);
//        ALL_FORMATS.add(BarcodeFormat.CODE_39);
//        ALL_FORMATS.add(BarcodeFormat.CODE_93);
//        ALL_FORMATS.add(BarcodeFormat.CODE_128);
//        ALL_FORMATS.add(BarcodeFormat.ITF);
//        ALL_FORMATS.add(BarcodeFormat.CODABAR);
//        ALL_FORMATS.add(BarcodeFormat.QR_CODE);
//        ALL_FORMATS.add(BarcodeFormat.DATA_MATRIX);
//        ALL_FORMATS.add(BarcodeFormat.PDF_417);
//    }

    public ZXingScannerView(Context context) {
        super(context);
        initMultiFormatReader();
    }

    public ZXingScannerView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        initMultiFormatReader();
    }

//    public void setFormats(List<BarcodeFormat> formats) {
//        mFormats = formats;
//        initMultiFormatReader();
//    }

    public void setResultHandler(ResultHandler resultHandler) {
        mResultHandler = resultHandler;
    }

//    public Collection<BarcodeFormat> getFormats() {
//        if(mFormats == null) {
//            return ALL_FORMATS;
//        }
//        return mFormats;
//    }

    private void initMultiFormatReader() {
//        Map<DecodeHintType,Object> hints = new EnumMap<DecodeHintType,Object>(DecodeHintType.class);
//        hints.put(DecodeHintType.POSSIBLE_FORMATS, getFormats());
        mMultiFormatReader = new MyMultiFormatReader();
//        mMultiFormatReader.setHints(hints);
    }

    @Override
    public void onPreviewFrame(byte[] data, Camera camera) {
        if(mResultHandler == null) {
            return;
        }

        try {
            Camera.Parameters parameters = camera.getParameters();
            Camera.Size size = parameters.getPreviewSize();
            int width = size.width;
            int height = size.height;


            if (DisplayUtils.getScreenOrientation(getContext()) == Configuration.ORIENTATION_PORTRAIT) {
                long pre = System.currentTimeMillis();
                long start = Process.getElapsedCpuTime();

                byte[] rotatedData = new byte[data.length];
                for (int y = 0; y < height; y++) {
                    for (int x = 0; x < width; x++)
                        rotatedData[(((x * height) + height) - y) - 1] = data[(y * width) + x];
                }

                int tmp = width;
                width = height;
                height = tmp;
                data = rotatedData;
            }

            Result rawResult = null;
            LuminanceSource source = buildLuminanceSource(data, width, height);

            if (source != null) {
                BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
                try {
                    rawResult = mMultiFormatReader.decode(bitmap);
                    Log.i("Result","Result:"+rawResult);
                } catch (Exception e1) {
                    e1.printStackTrace();
                    bitmap = new BinaryBitmap(new GlobalHistogramBinarizer(source));
                    try {
                        rawResult = mMultiFormatReader.decode(bitmap);
                    } catch (Exception e2) {
                        e2.printStackTrace();
                    }
                }finally {
                    mMultiFormatReader.reset();
                }
            }

            final Result finalRawResult = rawResult;

            if (finalRawResult != null) {
                Handler handler = new Handler(Looper.getMainLooper());
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        // Stopping the preview can take a little long.
                        // So we want to set result handler to null to discard subsequent calls to
                        // onPreviewFrame.
                        ResultHandler tmpResultHandler = mResultHandler;
                        mResultHandler = null;

                        stopCameraPreview();
                        if (tmpResultHandler != null) {
                            tmpResultHandler.handleResult(finalRawResult);
                        }
                    }
                });
            } else {
                camera.setOneShotPreviewCallback(this);
            }
        } catch(RuntimeException e) {
            e.printStackTrace();
        }
    }

    public void resumeCameraPreview(ResultHandler resultHandler) {
        mResultHandler = resultHandler;
        super.resumeCameraPreview();
    }

    public LuminanceSource buildLuminanceSource(byte[] data, int width, int height) {
        Rect rect = getFramingRectInPreview(width, height);
        if (rect == null) {
            return null;
        }
        // Go ahead and assume it's YUV rather than die.
        MyPlanarYUVLuminanceSource source = null;

        try {
            source = new MyPlanarYUVLuminanceSource(data, width, height, rect.left, rect.top,
                    rect.width(), rect.height(), false);
        } catch(Exception e) {
        }

        return source;
    }
}
