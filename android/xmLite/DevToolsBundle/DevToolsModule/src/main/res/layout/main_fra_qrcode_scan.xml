<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#111111"
    android:orientation="vertical">

    <com.ximalaya.ting.android.main.view.scannerview.CustomZXingScannerView
        android:id="@+id/main_zxing_scanner_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <RelativeLayout
        android:id="@+id/main_title_bar"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/devtools_gray_underline_white_bg" />

    <LinearLayout
        android:id="@+id/main_qrscan_bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/main_qrscan_album_image"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="12dp"
            android:background="@drawable/devtools_icon_qrscan_photo_album" />

        <TextView
            android:id="@+id/main_qrscan_album_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="5dp"
            android:text="相册"
            android:textColor="#ffffff"
            android:textSize="12sp" />
    </LinearLayout>

</RelativeLayout>
