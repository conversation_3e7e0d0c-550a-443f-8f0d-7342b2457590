<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/devtools_btn_dev_tools_page_close">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingTop="50dp">

            <RadioGroup
                android:id="@+id/devtools_choose_environment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="visible"
                tools:visibility="visible">

                <TextView
                    style="@style/devtools_setting_textview_bg_white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableRight="@null"
                    android:text="环境:" />

                <RadioButton
                    android:id="@+id/devtools_release"
                    style="@style/devtools_setting_textview_bg_white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableRight="@null"
                    android:text="正式" />

                <RadioButton
                    android:id="@+id/devtools_debug"
                    style="@style/devtools_setting_textview_bg_white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableRight="@null"
                    android:text="测试" />

                <RadioButton
                    android:id="@+id/devtools_uat"
                    style="@style/devtools_setting_textview_bg_white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableRight="@null"
                    android:text="uat" />

            </RadioGroup>

            <Button
                android:id="@+id/devtools_btn_create_uuid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="15dp"
                android:text="重新生成DevicesId"
                android:textAllCaps="false"
                android:textSize="15sp" />

        </LinearLayout>

    </ScrollView>

    <TextView
        android:id="@+id/devtools_btn_dev_tools_page_close"
        android:layout_width="250dp"
        android:layout_height="43dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/host_bg_rect_stroke_f86442_radius_24"
        android:gravity="center"
        android:text="关闭当前页面"
        android:textColor="@color/host_white"
        android:textSize="15sp" />

</RelativeLayout>
