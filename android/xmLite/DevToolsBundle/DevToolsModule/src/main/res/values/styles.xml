<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="devtools_setting_textview_bg_white" parent="@style/devtools_setting_text_title">
        <item name="android:background">@drawable/host_bg_list_selector_white</item>
    </style>

    <style name="devtools_setting_text_title">
        <item name="android:textColor">@color/host_black_030303</item>
        <item name="android:textSize">15sp</item>
        <item name="android:drawablePadding">10dp</item>
        <item name="android:paddingLeft">15dp</item>
        <item name="android:paddingRight">15dp</item>
        <item name="android:clickable">true</item>
        <item name="android:minHeight">50dp</item>
        <item name="android:ellipsize">middle</item>
        <item name="android:gravity">center_vertical|left</item>
        <item name="android:background">@drawable/host_bg_list_selector</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:drawableRight">@drawable/host_ic_more</item>
        <item name="android:singleLine">true</item>
        <item name="android:maxEms">8</item>
    </style>

</resources>