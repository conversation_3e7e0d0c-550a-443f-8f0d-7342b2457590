# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2560m

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

#\uFFFD\uFFFD\uFFFD\uFFFD\u03E2
GROUP_ID = com.ximalaya.ting.android
# Licence\uFFFD\uFFFD\u03E2
PROJ_LICENCE_NAME=The Apache Software License, Version 2.0
PROJ_LICENCE_URL=http://www.apache.org/licenses/LICENSE-2.0.txt
PROJ_LICENCE_DEST=repo

LOCAL_REPO_URL=http://************:8082/artifactory/host/
REPO_VERSION=1.1.10
REPO_BRANCH=canary

ALL_COMPILE_SRC=false
BUILD_PLUGIN_APK=false

ANDROID_TOOLS_BUILD_GRADLE=3.4.2
android.useAndroidX=true
android.enableJetifier=true 
