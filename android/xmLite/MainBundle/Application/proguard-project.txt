# To enable ProGuard in your project, edit project.properties
# to define the proguard.config property as described in that file.
#
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in ${sdk.dir}/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the ProGuard
# include property in project.properties.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-ignorewarnings
-optimizationpasses 5
-dontusemixedcaseclassnames #【混淆时不会产生形形色色的类名 】
-dontskipnonpubliclibraryclasses #【指定不去忽略非公共的库类。 】
-dontpreverify #【不预校验】
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/* #【优化】
-dontshrink
# ProGuard configurations for Bugtags
  -keepattributes LineNumberTable,SourceFile

  -keep class com.bugtags.library.** {*;}
  -dontwarn com.bugtags.library.**
  -keep class io.bugtags.** {*;}
  -dontwarn io.bugtags.**
  -dontwarn org.apache.http.**
  -dontwarn android.net.http.AndroidHttpClient
  -dontwarn com.bugtags.ec.lib.**
  -keep class com.bugtags.ec.lib.** {*;}

  # End Bugtags
#-----opensdk start----------
#-dontwarn okio.**
#-keep class okio.** { *;}
#
#-dontwarn okhttp3.**
#-keep class okhttp3.** { *;}

-keep public class com.rtmpclient.JNIDataModel{ *;}
-keep public class com.rtmpclient.publisher.LivePublisherJNI{ *;}
-keep public class com.rtmpclient.player.LivePlayerJNI{ *;}

-dontwarn com.ximalaya.ting.android.player.**
-keep class com.ximalaya.ting.android.player.** { *;}

-dontwarn com.google.gson.**
-keep class com.google.gson.** { *;}

-dontwarn com.google.android.gms.**
-keep class com.google.android.gms.** { *; }

-dontwarn org.litepal.**
-keep class org.litepal.** { *;}

-dontwarn android.support.**
-keep class android.support.** { *;}

-keep interface com.ximalaya.ting.android.opensdk.** {*;}
-keep class com.ximalaya.ting.android.opensdk.** { *; }
#-----opensdk end----------

#-----framework start----------
-dontwarn com.umeng.**
-keep class com.umeng.** {*;}
-dontwarn u.aly.**
-keep class u.aly.** { *;}

-keep class com.ximalaya.ting.android.framework.** { *; }
#-----framework end----------

#-----plugin start----------
-keep interface com.morgoo.** {*;}
-dontwarn com.morgoo.**
-keep class com.morgoo.** { *;}
-dontwarn android.util.**
-keep class android.util.** { *;}
-dontwarn android.app.**
-keep class android.app.** { *;}
#-----plugin end----------

-dontwarn com.alipay.**
-keep class com.alipay.** { *;}
-dontwarn com.ta.utdid2.**
-keep class com.ta.utdid2.** { *;}
-dontwarn com.ut.device.**
-keep class com.ut.device.** { *;}
-dontwarn org.json.alipay.**
-keep class org.json.alipay.** { *;}
-keep class com.ximalaya.ting.android.main.payModule.XMWXPayEntryActivity$*{ *; }
-keep class com.ximalaya.ting.android.main.payModule.XMWXPayEntryActivity{ *; }
-keep class com.ximalaya.ting.android.manager.pay.PayActionHelper*{ *; }
-keep class com.ximalaya.ting.android.manager.pay.PayActionHelper{ *; }

-dontwarn com.ximalaya.ting.android.lib.mixer.**
-keep class com.ximalaya.ting.android.lib.mixer.** {*;}

-dontwarn com.rt.**
-keep class com.rt.** {*;}
-dontwarn sun.misc.**
-keep class sun.misc.** {*;}

#-dontwarn com.baidu.carlife.platform.**
#-keep class com.baidu.carlife.platform.** {*;}
#-keep interface com.baidu.carlife.platform.** {*;}

-dontwarn com.umeng.**
-keep class com.umeng.** { *;}

#-dontwarn master.flame.danmaku.**
#-keep class master.flame.danmaku.** { *;}
-dontwarn tv.cjump.jni.**
-keep class tv.cjump.jni.** { *;}

-dontwarn com.eguan.drivermonitor.**
-keep class com.eguan.drivermonitor.** {*;}

-dontwarn com.igexin.**
-keep class com.igexin.** { *; }

-dontwarn com.tencent.**
-keep class com.tencent.** {*;}

-dontwarn cn.com.iresearch.mapptracker.**
-keep class cn.com.iresearch.mapptracker.** {*;}

-dontwarn com.ximalaya.ting.android.lib.mpg123.**
-keep class com.ximalaya.ting.android.lib.mpg123.** {*;}

-dontwarn com.ximalaya.ting.android.lib.lame.**
-keep class com.ximalaya.ting.android.lib.lame.** {*;}

-dontwarn com.bosch.myspin.serversdk.**
-keep class com.bosch.myspin.serversdk.** {*;}

-dontwarn com.nineoldandroids.**
-keep class com.nineoldandroids.** {*;}

-dontwarn com.ximalaya.ting.android.lib.ns.**
-keep class com.ximalaya.ting.android.lib.ns.** { *;}

-dontwarn com.baidu.**
-keep class com.baidu.**{ *;}

-keep class com.qq.e.** {
    public protected *;
}

-keep class android.support.v4.app.NotificationCompat**{
    public *;
}

-dontwarn com.xiaomi.**
-keep class com.xiaomi.** { *;}
-dontwarn miui.net.**
-keep class miui.net.** { *;}

-dontwarn uk.co.senab.photoview.**
-keep class uk.co.senab.photoview.** {*;}

-dontwarn com.tendcloud.**
-keep class com.tendcloud.** {*;}

-dontwarn com.sina.**
-keep class com.sina.** {*;}
-keep interface com.sina.** {*;}

-dontwarn cn.wemart.sdk.**
-keep class cn.wemart.sdk.** {*;}

# Youzan SDK
-dontwarn com.youzan.sdk.***
-keep class com.youzan.sdk.**{*;}
# OkHttp
-dontwarn com.squareup.okhttp.**
-keep class com.squareup.okhttp.** { *; }
-keep interface com.squareup.okhttp.** { *; }
-dontwarn java.nio.file.*
-dontwarn javax.annotation.**
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# Image Loader
-keep class com.squareup.picasso.Picasso
-keep class com.android.volley.toolbox.Volley
-keep class com.bumptech.glide.Glide
-keep class com.nostra13.universalimageloader.core.ImageLoader
-keep class com.facebook.drawee.backends.pipeline.Fresco

-keep class com.ximalaya.ting.android.main.util.ui.RingtoneUtil$* { *; }

-keep interface com.ximalaya.ting.android.** {*;}
-keep class com.ximalaya.ting.android.main.fragment.find.other.recommend.ReportFragment$* { *; }
-keep class com.ximalaya.ting.android.main.playModule.fragment.PlayingSoundDetailView$* { *; }
-keep class com.ximalaya.ting.android.host.util.common.EncryptUtil { *; }


#-keep class com.ximalaya.ting.android.fragment.BaseListHaveRefreshFragment { *; }
#-keep class com.ximalaya.ting.android.fragment.BaseListHaveRefreshFragment$* { *; }
#-keep class com.ximalaya.ting.android.main.fragment.find.child.AnchorFragment { *; }
#-keep class com.ximalaya.ting.android.main.fragment.find.child.AnchorFragment$* { *; }
#-keep class com.ximalaya.ting.android.main.adapter.find.other.AnchorAdapter { *; }
#-keep class com.ximalaya.ting.android.main.adapter.find.other.AnchorAdapter$* { *; }
#-keep class com.ximalaya.ting.android.adapter.find.other.AnchorListAdapter { *; }
#-keep class com.ximalaya.ting.android.adapter.find.other.AnchorListAdapter$* { *; }
#-keep class com.ximalaya.ting.android.activity.** { *; }
-keep class com.ximalaya.ting.android.adapter.** { *; }
-keep class com.ximalaya.ting.android.chat.adapter.** { *; }
-keep class com.ximalaya.ting.android.host.adapter.** { *; }
-keep class com.ximalaya.ting.android.live.adapter.** { *; }
-keep class com.ximalaya.ting.android.main.adapter.** { *; }
-keep class com.ximalaya.ting.android.record.adapter.** { *; }
-keep class com.ximalaya.ting.android.main.adModule.manager.** {*; }

#-keep class com.ximalaya.ting.android.broadcast.** { *; }
#-keep class com.ximalaya.ting.android.carlink.** { *; }
-keep class com.ximalaya.ting.android.data.** { *; }
-keep class com.ximalaya.ting.android.host.model.** { *; }
-keep class com.ximalaya.ting.android.main.model.** { *; }
-keep class com.ximalaya.ting.android.live.data.model.** { *; }
-keep class com.ximalaya.ting.android.chat.data.model.** { *; }
-keep class com.ximalaya.ting.android.record.data.model.** { *; }
-keep class com.ximalaya.ting.android.host.xdcs.** { *; }
-keep class com.ximalaya.ting.android.host.manager.request.CommonRequestM { *; }
-keep class com.ximalaya.ting.android.main.request.MainCommonRequest { *; }
-keep class com.ximalaya.ting.android.host.xdcs.** { *; }
#-keep class com.ximalaya.ting.android.fragment.** { *; }
#-keep class com.ximalaya.ting.android.receiver.** { *; }
#-keep class com.ximalaya.ting.android.service.** { *; }
#-keep class com.ximalaya.ting.android.view.** { *; }

-keep class com.ximalaya.ting.android.manager.device.** { *; }
-keep class com.ximalaya.ting.android.main.payModule.WebWemartActivityNew$* { *; }
-keep class com.ximalaya.ting.android.fragment.other.web.** { *; }

-keep class com.ximalaya.ting.android.host.fragment.web.WebFragment { *; }
-keep class com.ximalaya.ting.android.host.fragment.web.WebFragment$* { *; }
-keep class com.ximalaya.ting.android.framework.adapter.HolderAdapter{ *; }
-keep class com.ximalaya.ting.android.host.util.constant.AppConstants { *; }

#-keep class com.ximalaya.ting.android.live.LiveActivityGuessYouLikeAlbumInfo { *; }
#-keep class com.ximalaya.ting.android.live.LiveActivityGuessYouLikeAlbumInfo$* { *; }
#-keep class com.ximalaya.ting.android.live.PlayingLiveSoundInfo { *; }
#-keep class com.ximalaya.ting.android.live.PlayingLiveSoundInfo$* { *; }

-keep public class com.ximalaya.ting.android.R$* { *; }

#-keep public class com.ximalaya.ting.android.library.** { *; }
#-dontwarn com.loopj.android.http.**
#-keep class com.loopj.android.http.** {*;}

-keepattributes Signature,Exceptions,InnerClasses,SourceFile,LineNumberTable

-keepattributes *Annotation*

#android默认项
-keep public class * extends android.widget.BaseAdapter
-keep public class * extends android.support.v4.app.Fragment
-keep public class * extends android.support.v4.app.FragmentActivity
-keep public class * extends android.app.Fragment
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class com.android.vending.licensing.ILicensingService

-keepclasseswithmembernames class * {
    native <methods>;
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

-keepclassmembers class * extends com.ximalaya.ting.android.framework.adapter.HolderAdapter {
   public <init> (android.content.Context ,java.util.List);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

-keep public class com.ximalaya.ting.android.R$*{
public static final int *; }

-keep public class com.ximalaya.ting.android.main.adModule.manager.AdsDataHandler { *; }
-keep public class com.ximalaya.ting.android.manager.statistic.PlayStatisticUpload { *; }
-keep public class com.ximalaya.ting.android.main.accountModule.bind.other.BindPhoneManager$* { *; }

-keep class com.squareup.wire.** { *; }
-keep class IM.** { *; }
-keep class RM.** { *; }
-keepclassmembers class com.ximalaya.android.xchat.EncodedMessageInfo {
	public <fields>;
}
-keepclassmembers class com.ximalaya.android.xchat.CalChecksumInfo {
	public <fields>;
}
-keep class com.ximalaya.rtmpclient.jni.**{ *;}
-keep class com.ximalaya.ting.android.live.newxchat.mic.model.**{ *;}
-keep class com.ximalaya.android.xchat.chatroom.model.**{ *;}
-keep class MIC.** { *; }
-keep class com.zego.ve.** { *; }
-keep class com.zego.zegoliveroom.** { *; }
-keep class com.zego.zegoliveroom.videocapture.** { *; }
-keep class com.zego.zegoliveroom.videofilter.** { *; }

-keepclassmembers class * {
        void onEvent*(**);
}

#逸创用户反馈SDK
-keep class com.kf5.sdk.im.entity.**{*;}
-keep class com.kf5.sdk.helpcenter.entity.**{*;}
-keep class com.kf5.sdk.system.entity.**{*;}
-keep class com.kf5.sdk.ticket.entity.**{*;}
-keep class com.kf5.sdk.im.expression.**{*;}
#glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
 **[] $VALUES;
 public *;
}
#gson
-keep class com.google.**{*;}
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.stream.** { *; }
-keep class com.google.gson.examples.android.model.** { *; }

-keepclassmembers class * {void onEvent*(**);}
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);}
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();}
-keep class **.R$* {*;}

#腾讯bugly
-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}

#百度sdk begin
-keep class com.baidu.bottom.** { *; }
-keep class com.baidu.kirin.** { *; }
-keep class com.baidu.mobstat.** { *; }
#百度sdk end

#科大讯飞 语音搜索 begin
-keep class com.iflytek.**{*;}
-keepattributes Signature
#科大讯飞 语音搜索 end

#子项目化各模块入口类
-keep class * implements com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication {*;}
-keep public class com.ximalaya.ting.android.host.HostApplication {*;}
-keep public class com.ximalaya.ting.android.chat.ChatApplication {*;}
-keep public class com.ximalaya.ting.android.car.CarApplication{*;}
-keep public class com.ximalaya.ting.android.smartdevice.SmartDeviceApplication {*;}
-keep public class com.ximalaya.ting.android.live.LiveApplication {*;}
-keep public class com.ximalaya.ting.android.record.RecordApplication {*;}
-keep public class com.ximalaya.ting.android.watch.WatchApplication {*;}
-keep public class com.ximalaya.ting.lite.main.MainApplication {*;}
-keep public class com.ximalaya.ting.android.hybridviewmodule.HybridViewApplication{*;}

-keep public class com.ximalaya.ting.android.smartdevice.activity.SmartDeviceMainActivity { *; }
-keep public class com.ximalaya.ting.android.watch.activity.WatchMainActivity { *; }
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.LiveRoomStreamPlayIntent { *; }
#子项目化各模块入口类

# 统计混淆规则
-keep class com.hmt.analytics.** { *; }

#-----car start----------
-dontwarn com.bosch.myspin.serversdk.**
-keep class com.bosch.myspin.serversdk.** {*;}
-keep class com.android.inputmethod.pinyin.**{*;}
-keep class com.android.inputmethod.pinyin.PinyinDecoderService{*;}
-keep class com.bosch.myspin.chinese.**{*;}

-dontwarn master.flame.danmaku.**
-keep class master.flame.danmaku.** { *;}

-dontwarn tv.cjump.jni.**
-keep class tv.cjump.jni.** { *;}

-dontwarn com.mxnavi.sdl.**
-keep class com.mxnavi.sdl.** { *;}

-dontwarn com.havalsdl.**
-keep class com.havalsdl.** { *;}

-dontwarn com.smartdevicelink.**
-keep class com.smartdevicelink.** { *;}

-dontwarn com.baidu.carlife.platform.**
-keep class com.baidu.carlife.platform.** {*;}
-keep interface com.baidu.carlife.platform.** {*;}

-dontwarn com.airbiquity.hap.**
-keep class com.airbiquity.hap.** { *;}

-dontwarn com.ximalaya.ting.android.car.abq.model.**
-keep class com.ximalaya.ting.android.car.abq.model.** { *;}
-keep class com.ximalaya.ting.android.car.abq.XmCommandResponder$AbqNotification{*;}

#-----car end----------


#-------------------录音模块使用的类 start-----------------------
-keep public class com.ximalaya.ting.android.framework.adapter.HolderAdapter {*;}
-keep public class com.ximalaya.ting.android.host.util.view.LocalImageUtil {*;}
-keep public class com.ximalaya.ting.android.framework.adapter.HolderAdapter {*;}
-keep public class com.ximalaya.ting.android.framework.adapter.HolderAdapter {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.framework.download.Downloader {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder {*;}
-keep public class com.ximalaya.ting.android.framework.view.image.RoundImageView {*;}
-keep public class com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.manager.track.AlbumEventManage {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.PlayableModel {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class com.ximalaya.ting.android.framework.util.MyConcurrentHashMap {*;}
-keep public class com.ximalaya.ting.android.framework.util.CustomToast {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordFunctionAction {*;}
-keep public class com.ximalaya.ting.android.host.listener.IAlbumCallBack {*;}
-keep public class com.ximalaya.ting.android.host.listener.ITrackCallBack {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.track.TrackM {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.server.DownloadTools {*;}
-keep public class com.ximalaya.ting.android.host.util.server.NetworkUtils {*;}
-keep public class com.ximalaya.ting.android.host.model.album.AlbumM {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.SlideView.IOnFinishListener {*;}
-keep public class com.ximalaya.ting.android.host.util.view.SoftInputUtil {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class com.ximalaya.ting.android.host.util.common.TimeHelper {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer.PlayerStatusListener {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.live.util.LiveUtil {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MenuDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.host.listener.IPhotoAction {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ResultWrapper {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.UploadType {*;}
-keep public class com.ximalaya.ting.android.host.util.common.DeviceUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.host.model.album.AlbumM {*;}
-keep public class com.ximalaya.ting.android.main.model.category.CategoryM {*;}
-keep public class com.ximalaya.ting.android.main.model.category.CategoryMList {*;}
-keep public class com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.download.Downloader {*;}
-keep public class com.ximalaya.ting.android.framework.fragment.BaseFragment {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MenuDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView {*;}
-keep public class com.ximalaya.ting.android.host.adapter.track.TrackAdapterCreator {*;}
-keep public class com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.ITrackCallBack {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.model.base.ListModeBase {*;}
-keep public class com.ximalaya.ting.android.host.util.common.TimeHelper {*;}
-keep public class com.ximalaya.ting.android.host.util.common.ToolUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.view.TitleBar {*;}
-keep public class com.ximalaya.ting.android.main.dialog.ShareDialog {*;}
-keep public class com.ximalaya.ting.android.manager.track.AlbumEventManage {*;}
-keep public class com.ximalaya.ting.android.opensdk.constants.DTransferConstants {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.XmPlayerManager {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.IDbDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.OneClickHelper {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MenuDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.image.RoundImageView {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.** {*;}
-keep public class com.ximalaya.ting.android.host.adapter.album.AlbumAdapter {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.IFragmentFinish {*;}
-keep public class com.ximalaya.ting.android.host.listener.IPhotoAction {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager {*;}
-keep public class com.ximalaya.ting.android.host.model.account.LoginInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.model.account.UserInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.model.base.ListModeBase {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ResultWrapper {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.UploadType {*;}
-keep public class com.ximalaya.ting.android.host.util.common.DeviceUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.view.TitleBar {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.main.listener.IBindAction {*;}
-keep public class com.ximalaya.ting.android.host.model.album.AlbumM {*;}
-keep public class com.ximalaya.ting.android.host.model.user.ThirdPartyUserInfo {*;}
-keep public class com.ximalaya.ting.android.opensdk.constants.DTransferConstants {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Album {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType.NetWorkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.manager.StatusBarManager {*;}
-keep public class com.ximalaya.ting.android.framework.manager.XDCSCollectUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.IFragmentFinish {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager {*;}
-keep public class com.ximalaya.ting.android.host.model.account.LoginInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.util.common.TimeHelper {*;}
-keep public class com.ximalaya.ting.android.host.util.common.ToolUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.util.view.TitleBar {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.data.model.dialog.BaseDialogModel {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.manager.StatusBarManager {*;}
-keep public class com.ximalaya.ting.android.framework.manager.XDCSCollectUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder.DialogCallback {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.IFragmentFinish {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager {*;}
-keep public class com.ximalaya.ting.android.host.model.account.LoginInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.util.common.TimeHelper {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.XmPlayerManager {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.host.view.BaseBottomDialog {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.fragment.myspace.other.record.CreateAlbumFragment {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class com.ximalaya.ting.android.framework.util.Blur {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder.DialogCallback {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MenuDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView {*;}
-keep public class com.ximalaya.ting.android.host.adapter.album.AlbumAdapter {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.IFragmentFinish {*;}
-keep public class com.ximalaya.ting.android.host.listener.IPhotoAction {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager {*;}
-keep public class com.ximalaya.ting.android.host.model.account.LoginInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.model.account.UserInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.model.base.ListModeBase {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ResultWrapper {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.UploadType {*;}
-keep public class com.ximalaya.ting.android.host.util.common.DeviceUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.view.TitleBar {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.main.accountModule.login.GetAndVerifySmsCodeFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.IBindAction {*;}
-keep public class com.ximalaya.ting.android.host.model.album.AlbumM {*;}
-keep public class com.ximalaya.ting.android.host.model.user.ThirdPartyUserInfo {*;}
-keep public class com.ximalaya.ting.android.opensdk.constants.DTransferConstants {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Album {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType.NetWorkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.ModelUtil {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.image.RoundImageView {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.track.TrackM {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.main.view.other.ShareDialog {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.fragment.other.BaseDialogFragment {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.opensdk.httputil.XimalayaException {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class com.ximalaya.ting.android.MainApplication {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.framework.BaseApplication {*;}
-keep public class com.ximalaya.ting.android.framework.constants.PreferenceConstantsLib {*;}
-keep public class com.ximalaya.ting.android.framework.database.DBConnector {*;}
-keep public class com.ximalaya.ting.android.framework.database.DBUtil {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.IDbDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer.PlayerStatusListener {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.host.adapter.track.TrackAdapterCreator {*;}
-keep public class com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordFunctionAction {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ResultWrapper {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.UploadType {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.IDbDataCallBack {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordActivityAction {*;}
-keep public class com.ximalaya.ting.android.framework.fragment.BaseFragment {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.model.BundleException {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordFragmentAction {*;}
-keep public class com.ximalaya.ting.android.host.model.album.AlbumM {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class com.ximalaya.ting.android.framework.util.CustomToast {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.base.ListModeBase {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.main.util.http.TokenIntercepterUrlList {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.manager.record.BaseDownloadTask {*;}
-keep public class com.ximalaya.ting.android.manager.record.DownloadManager {*;}
-keep public class com.ximalaya.ting.android.MainApplication {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.BaseErrEvent {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ErrEventsModel {*;}
-keep public class com.ximalaya.ting.android.framework.manager.XDCSCollectUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ResultWrapper {*;}
-keep public class com.ximalaya.ting.android.record.data.model.UploadErrorModel {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.UploadType {*;}
-keep public class com.ximalaya.ting.android.host.util.common.DeviceUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.common.EncryptUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.server.NetworkUtils {*;}
-keep public class com.ximalaya.ting.android.host.util.server.PlayTools {*;}
-keep public class com.ximalaya.ting.android.manager.activity.ActivityManager {*;}
-keep public class com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IUploadCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.httputil.BaseResponse {*;}
-keep public class com.ximalaya.ting.android.opensdk.httputil.XimalayaException {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.PlayableModel {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.XmPlayerManager {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.IDbDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.RouterConstant {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.RecordActionRouter{*;}
-keep public class com.ximalaya.ting.android.opensdk.model.PlayableModel {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordFunctionAction {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.manager.ad.AdManager {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.advertis.Advertis {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class com.ximalaya.ting.android.host.util.common.TimeHelper {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.** {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure$* { *; }
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.** {*;}
-keep public class com.ximalaya.mediaprocessor.** {*;}


-keep public class android.content.Context {*;}
-keep public class android.graphics.drawable.Drawable {*;}
-keep public class android.view.View {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.ximalaya.ting.android.framework.adapter.HolderAdapter {*;}
-keep public class com.ximalaya.ting.android.host.util.view.LocalImageUtil {*;}
-keep public class java.util.List {*;}
-keep public class android.content.Context {*;}
-keep public class android.view.View {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.ximalaya.ting.android.framework.adapter.HolderAdapter {*;}
-keep public class java.util.List {*;}
-keep public class android.content.Context {*;}
-keep public class android.view.LayoutInflater {*;}
-keep public class android.view.View {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.ximalaya.ting.android.framework.adapter.HolderAdapter {*;}
-keep public class java.util.List {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.support.v4.app.FragmentActivity {*;}
-keep public class android.util.TypedValue {*;}
-keep public class android.view.Gravity {*;}
-keep public class android.view.View {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.widget.BaseAdapter {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class java.util.List {*;}
-keep public class android.content.Context {*;}
-keep public class android.graphics.Bitmap {*;}
-keep public class android.graphics.BitmapFactory {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.media.MediaPlayer {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.os.Handler {*;}
-keep public class android.os.Message {*;}
-keep public class android.text.Html {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.view.View {*;}
-keep public class android.widget.FrameLayout {*;}
-keep public class android.widget.ImageButton {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.LinearLayout {*;}
-keep public class android.widget.ProgressBar {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.framework.download.Downloader {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder {*;}
-keep public class com.ximalaya.ting.android.framework.view.image.RoundImageView {*;}
-keep public class com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.manager.track.AlbumEventManage {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class java.io.File {*;}
-keep public class java.util.List {*;}
-keep public class java.util.LinkedList {*;}
-keep public class java.util.List {*;}
-keep public class org.json.JSONException {*;}
-keep public class org.json.JSONObject {*;}
-keep public class org.json.JSONException {*;}
-keep public class org.json.JSONObject {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class java.io.DataInputStream {*;}
-keep public class java.io.DataOutputStream {*;}
-keep public class java.io.File {*;}
-keep public class java.io.FileInputStream {*;}
-keep public class java.io.IOException {*;}
-keep public class java.io.InputStream {*;}
-keep public class java.security.MessageDigest {*;}
-keep public class java.security.NoSuchAlgorithmException {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.Map {*;}
-keep public class java.util.Set {*;}
-keep public class android.text.TextUtils {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.PlayableModel {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class org.json.JSONObject {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.List {*;}
-keep public class com.ximalaya.ting.android.framework.util.MyConcurrentHashMap {*;}
-keep public class java.util.Map {*;}
-keep public class android.app.Dialog {*;}
-keep public class android.content.Context {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.view.Gravity {*;}
-keep public class android.view.View {*;}
-keep public class android.view.Window {*;}
-keep public class android.view.WindowManager {*;}
-keep public class android.widget.LinearLayout {*;}
-keep public class com.ximalaya.ting.android.framework.util.CustomToast {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordFunctionAction {*;}
-keep public class com.ximalaya.ting.android.host.listener.IAlbumCallBack {*;}
-keep public class com.ximalaya.ting.android.host.listener.ITrackCallBack {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.track.TrackM {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.server.DownloadTools {*;}
-keep public class com.ximalaya.ting.android.host.util.server.NetworkUtils {*;}
-keep public class com.ximalaya.ting.android.host.model.album.AlbumM {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.httputil.Config {*;}
-keep public class com.ximalaya.ting.android.player.IGetHttpUrlConnectByUrl {*;}
-keep public class com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowReadSPContentProvider {*;}
-keep public class org.json.JSONArray {*;}
-keep public class org.json.JSONException {*;}
-keep public class org.json.JSONObject {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.List {*;}
-keep public class android.content.ContentResolver {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.Intent {*;}
-keep public class android.content.res.AssetManager {*;}
-keep public class android.database.Cursor {*;}
-keep public class android.media.MediaScannerConnection {*;}
-keep public class android.net.Uri {*;}
-keep public class android.os.AsyncTask {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.provider.MediaStore {*;}
-keep public class android.text.Editable {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.text.TextWatcher {*;}
-keep public class android.util.Log {*;}
-keep public class android.view.View {*;}
-keep public class android.view.View.OnClickListener {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.widget.BaseAdapter {*;}
-keep public class android.widget.EditText {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.LinearLayout {*;}
-keep public class android.widget.ListView {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.SlideView.IOnFinishListener {*;}
-keep public class com.ximalaya.ting.android.host.util.view.SoftInputUtil {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class com.ximalaya.ting.android.host.util.common.TimeHelper {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer.PlayerStatusListener {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.live.util.LiveUtil {*;}
-keep public class java.io.File {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.LinkedList {*;}
-keep public class java.util.List {*;}
-keep public class android.app.ProgressDialog {*;}
-keep public class android.content.Intent {*;}
-keep public class android.graphics.Bitmap {*;}
-keep public class android.graphics.BitmapFactory {*;}
-keep public class android.net.Uri {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.provider.MediaStore {*;}
-keep public class androidx.core.content.ContextCompat {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.view.View {*;}
-keep public class android.view.View.OnClickListener {*;}
-keep public class android.widget.AdapterView {*;}
-keep public class android.widget.AdapterView.OnItemClickListener {*;}
-keep public class android.widget.AdapterView.OnItemSelectedListener {*;}
-keep public class android.widget.ArrayAdapter {*;}
-keep public class android.widget.Button {*;}
-keep public class android.widget.EditText {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.RadioButton {*;}
-keep public class android.widget.RadioGroup {*;}
-keep public class android.widget.RadioGroup.OnCheckedChangeListener {*;}
-keep public class android.widget.Spinner {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.google.gson.Gson {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MenuDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.host.listener.IPhotoAction {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ResultWrapper {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.UploadType {*;}
-keep public class com.ximalaya.ting.android.host.util.common.DeviceUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.host.model.album.AlbumM {*;}
-keep public class com.ximalaya.ting.android.main.model.category.CategoryM {*;}
-keep public class com.ximalaya.ting.android.main.model.category.CategoryMList {*;}
-keep public class com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class org.json.JSONException {*;}
-keep public class org.json.JSONObject {*;}
-keep public class java.io.File {*;}
-keep public class java.io.FileNotFoundException {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.List {*;}
-keep public class java.util.Map {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.view.View {*;}
-keep public class android.widget.EditText {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class android.app.ProgressDialog {*;}
-keep public class android.database.DataSetObserver {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.os.Handler {*;}
-keep public class android.os.Message {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.view.Gravity {*;}
-keep public class android.view.View {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.widget.AdapterView {*;}
-keep public class android.widget.AdapterView.OnItemClickListener {*;}
-keep public class android.widget.ImageButton {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.PopupWindow {*;}
-keep public class android.widget.RadioGroup {*;}
-keep public class android.widget.RadioGroup.OnCheckedChangeListener {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.handmark.pulltorefresh.library.PullToRefreshBase.Mode {*;}
-keep public class com.handmark.pulltorefresh.library.** {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.download.Downloader {*;}
-keep public class com.ximalaya.ting.android.framework.fragment.BaseFragment {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MenuDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView {*;}
-keep public class com.ximalaya.ting.android.host.adapter.track.TrackAdapterCreator {*;}
-keep public class com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.ITrackCallBack {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.model.base.ListModeBase {*;}
-keep public class com.ximalaya.ting.android.host.util.common.TimeHelper {*;}
-keep public class com.ximalaya.ting.android.host.util.common.ToolUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.view.TitleBar {*;}
-keep public class com.ximalaya.ting.android.host.util.view.TitleBar$* {*;}
-keep public class com.ximalaya.ting.android.main.dialog.ShareDialog {*;}
-keep public class com.ximalaya.ting.android.manager.track.AlbumEventManage {*;}
-keep public class com.ximalaya.ting.android.opensdk.constants.DTransferConstants {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.XmPlayerManager {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.IDbDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.Iterator {*;}
-keep public class java.util.List {*;}
-keep public class java.util.Map {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.view.LayoutInflater {*;}
-keep public class android.view.View {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.widget.RelativeLayout {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.handmark.pulltorefresh.library.PullToRefreshListView {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.List {*;}
-keep public class android.app.ProgressDialog {*;}
-keep public class android.content.ActivityNotFoundException {*;}
-keep public class android.content.Intent {*;}
-keep public class android.database.Cursor {*;}
-keep public class android.graphics.Bitmap {*;}
-keep public class android.graphics.BitmapFactory {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.graphics.drawable.ColorDrawable {*;}
-keep public class android.net.Uri {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.provider.MediaStore {*;}
-keep public class android.text.Editable {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.text.TextWatcher {*;}
-keep public class android.view.Gravity {*;}
-keep public class android.view.LayoutInflater {*;}
-keep public class android.view.View {*;}
-keep public class android.view.View.OnClickListener {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.view.WindowManager {*;}
-keep public class android.view.animation.Animation {*;}
-keep public class android.view.animation.AnimationUtils {*;}
-keep public class android.widget.AdapterView {*;}
-keep public class android.widget.AdapterView.OnItemClickListener {*;}
-keep public class android.widget.EditText {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.PopupWindow {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.google.gson.Gson {*;}
-keep public class com.google.gson.reflect.TypeToken {*;}
-keep public class com.handmark.pulltorefresh.library.PullToRefreshBase.Mode {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.OneClickHelper {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MenuDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.image.RoundImageView {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView {*;}
-keep public class com.ximalaya.ting.android.host.adapter.album.AlbumAdapter {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.IFragmentFinish {*;}
-keep public class com.ximalaya.ting.android.host.listener.IPhotoAction {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager {*;}
-keep public class com.ximalaya.ting.android.host.model.account.LoginInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.model.account.UserInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.model.base.ListModeBase {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ResultWrapper {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.UploadType {*;}
-keep public class com.ximalaya.ting.android.host.util.common.DeviceUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.view.TitleBar {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.main.listener.IBindAction {*;}
-keep public class com.ximalaya.ting.android.host.model.album.AlbumM {*;}
-keep public class com.ximalaya.ting.android.host.model.user.ThirdPartyUserInfo {*;}
-keep public class com.ximalaya.ting.android.opensdk.constants.DTransferConstants {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Album {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType.NetWorkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class org.json.JSONArray {*;}
-keep public class org.json.JSONException {*;}
-keep public class org.json.JSONObject {*;}
-keep public class java.io.File {*;}
-keep public class java.io.FileNotFoundException {*;}
-keep public class java.io.IOException {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.Collections {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.List {*;}
-keep public class java.util.Map {*;}
-keep public class android.app.AlertDialog {*;}
-keep public class android.app.ProgressDialog {*;}
-keep public class android.content.Context {*;}
-keep public class android.graphics.drawable.Drawable {*;}
-keep public class android.os.AsyncTask {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.os.Handler {*;}
-keep public class android.os.Message {*;}
-keep public class android.util.TypedValue {*;}
-keep public class android.view.View {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.view.ViewTreeObserver {*;}
-keep public class android.view.inputmethod.InputMethodManager {*;}
-keep public class android.widget.EditText {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.LinearLayout {*;}
-keep public class android.widget.ListView {*;}
-keep public class android.widget.RelativeLayout {*;}
-keep public class android.widget.SeekBar {*;}
-keep public class android.widget.TextView {*;}
-keep public class android.widget.Toast {*;}
-keep public class com.ximalaya.mediaprocessor.MediaMixer {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.manager.StatusBarManager {*;}
-keep public class com.ximalaya.ting.android.framework.manager.XDCSCollectUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.IFragmentFinish {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager {*;}
-keep public class com.ximalaya.ting.android.host.model.account.LoginInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.util.common.TimeHelper {*;}
-keep public class com.ximalaya.ting.android.host.util.common.ToolUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.Date {*;}
-keep public class java.util.List {*;}
-keep public class java.util.Timer {*;}
-keep public class java.util.TimerTask {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.view.View {*;}
-keep public class android.widget.GridView {*;}
-keep public class android.widget.ImageView {*;}
-keep public class com.handmark.pulltorefresh.library.PullToRefreshListView {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.util.view.TitleBar {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class org.json.JSONArray {*;}
-keep public class org.json.JSONException {*;}
-keep public class org.json.JSONObject {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.List {*;}
-keep public class java.util.Map {*;}
-keep public class android.Manifest {*;}
-keep public class android.annotation.SuppressLint {*;}
-keep public class android.app.Activity {*;}
-keep public class android.app.AlertDialog {*;}
-keep public class android.app.ProgressDialog {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.Intent {*;}
-keep public class android.os.Build {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.os.Handler {*;}
-keep public class android.os.Message {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.util.TypedValue {*;}
-keep public class android.view.Gravity {*;}
-keep public class android.view.View {*;}
-keep public class android.view.View.OnClickListener {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.view.ViewGroup.LayoutParams {*;}
-keep public class android.view.inputmethod.InputMethodManager {*;}
-keep public class android.widget.AdapterView {*;}
-keep public class android.widget.EditText {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.ImageView.ScaleType {*;}
-keep public class android.widget.LinearLayout {*;}
-keep public class android.widget.RelativeLayout {*;}
-keep public class android.widget.SeekBar {*;}
-keep public class android.widget.SeekBar.OnSeekBarChangeListener {*;}
-keep public class android.widget.TextView {*;}
-keep public class android.widget.Toast {*;}
-keep public class com.nineoldandroids.animation.Animator {*;}
-keep public class com.nineoldandroids.animation.AnimatorSet {*;}
-keep public class com.nineoldandroids.animation.ObjectAnimator {*;}
-keep public class com.nineoldandroids.animation.ValueAnimator {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.data.model.dialog.BaseDialogModel {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.manager.StatusBarManager {*;}
-keep public class com.ximalaya.ting.android.framework.manager.XDCSCollectUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder.DialogCallback {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.IFragmentFinish {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager {*;}
-keep public class com.ximalaya.ting.android.host.model.account.LoginInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.util.common.TimeHelper {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.XmPlayerManager {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class com.ximalaya.ting.android.view.BaseBottomDialog {*;}
-keep public class java.io.File {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.Date {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.List {*;}
-keep public class java.util.Timer {*;}
-keep public class java.util.TimerTask {*;}
-keep public class android.app.ProgressDialog {*;}
-keep public class android.content.ActivityNotFoundException {*;}
-keep public class android.content.Intent {*;}
-keep public class android.database.Cursor {*;}
-keep public class android.graphics.Bitmap {*;}
-keep public class android.graphics.BitmapFactory {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.graphics.drawable.ColorDrawable {*;}
-keep public class android.net.Uri {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.provider.MediaStore {*;}
-keep public class android.text.Editable {*;}
-keep public class android.text.SpannableString {*;}
-keep public class android.text.Spanned {*;}
-keep public class android.text.TextPaint {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.text.TextWatcher {*;}
-keep public class android.text.method.LinkMovementMethod {*;}
-keep public class android.text.style.ClickableSpan {*;}
-keep public class android.view.Gravity {*;}
-keep public class android.view.LayoutInflater {*;}
-keep public class android.view.View {*;}
-keep public class android.view.View.OnClickListener {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.view.WindowManager {*;}
-keep public class android.view.animation.Animation {*;}
-keep public class android.view.animation.AnimationUtils {*;}
-keep public class android.widget.AdapterView {*;}
-keep public class android.widget.AdapterView.OnItemClickListener {*;}
-keep public class android.widget.Button {*;}
-keep public class android.widget.EditText {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.LinearLayout {*;}
-keep public class android.widget.PopupWindow {*;}
-keep public class android.widget.RelativeLayout {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.google.gson.Gson {*;}
-keep public class com.google.gson.reflect.TypeToken {*;}
-keep public class com.handmark.pulltorefresh.library.PullToRefreshBase.Mode {*;}
-keep public class com.ximalaya.ting.android.activity.MainActivity {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.fragment.myspace.other.record.CreateAlbumFragment {*;}
-keep public class com.ximalaya.ting.android.framework.commoninterface.IHandleOk {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.manager.StatusBarManager {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class com.ximalaya.ting.android.framework.util.Blur {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.DialogBuilder.DialogCallback {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MenuDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener {*;}
-keep public class com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView {*;}
-keep public class com.ximalaya.ting.android.host.adapter.album.AlbumAdapter {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.listener.IFragmentFinish {*;}
-keep public class com.ximalaya.ting.android.host.listener.IPhotoAction {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager {*;}
-keep public class com.ximalaya.ting.android.host.model.account.LoginInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.model.account.UserInfoModel {*;}
-keep public class com.ximalaya.ting.android.host.model.base.ListModeBase {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ResultWrapper {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.UploadType {*;}
-keep public class com.ximalaya.ting.android.host.util.common.DeviceUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.view.TitleBar {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment {*;}
-keep public class com.ximalaya.ting.android.main.accountModule.login.GetAndVerifySmsCodeFragment {*;}
-keep public class com.ximalaya.ting.android.main.listener.IBindAction {*;}
-keep public class com.ximalaya.ting.android.host.model.album.AlbumM {*;}
-keep public class com.ximalaya.ting.android.host.model.user.ThirdPartyUserInfo {*;}
-keep public class com.ximalaya.ting.android.opensdk.constants.DTransferConstants {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Album {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType.NetWorkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class org.json.JSONArray {*;}
-keep public class org.json.JSONException {*;}
-keep public class org.json.JSONObject {*;}
-keep public class java.io.File {*;}
-keep public class java.io.FileNotFoundException {*;}
-keep public class java.io.IOException {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.Collections {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.List {*;}
-keep public class java.util.Map {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.support.annotation.Nullable {*;}
-keep public class android.support.v4.app.DialogFragment {*;}
-keep public class android.text.Html {*;}
-keep public class android.view.LayoutInflater {*;}
-keep public class android.view.View {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.view.Window {*;}
-keep public class android.widget.Button {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.framework.view.image.RoundImageView {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.track.TrackM {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.BundleKeyConstants {*;}
-keep public class com.ximalaya.ting.android.main.view.other.ShareDialog {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.Map {*;}
-keep public class android.content.Context {*;}
-keep public class android.support.v4.app.FragmentActivity {*;}
-keep public class android.view.View {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.LinearLayout {*;}
-keep public class java.util.List {*;}
-keep public class android.graphics.Paint {*;}
-keep public class android.support.v4.app.FragmentActivity {*;}
-keep public class android.view.MotionEvent {*;}
-keep public class android.view.View {*;}
-keep public class android.view.ViewConfiguration {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.widget.AbsListView {*;}
-keep public class android.widget.BaseAdapter {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.ListView {*;}
-keep public class android.widget.RelativeLayout {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.nineoldandroids.animation.Animator {*;}
-keep public class com.nineoldandroids.animation.AnimatorSet {*;}
-keep public class com.nineoldandroids.animation.ObjectAnimator {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class android.content.Context {*;}
-keep public class android.support.v4.app.FragmentActivity {*;}
-keep public class java.util.List {*;}
-keep public class android.app.Service {*;}
-keep public class android.content.BroadcastReceiver {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.Intent {*;}
-keep public class android.content.IntentFilter {*;}
-keep public class android.media.AudioFormat {*;}
-keep public class android.media.AudioRecord {*;}
-keep public class android.media.MediaRecorder {*;}
-keep public class android.support.v4.app.FragmentActivity {*;}
-keep public class android.telephony.PhoneStateListener {*;}
-keep public class android.telephony.TelephonyManager {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.widget.Toast {*;}
-keep public class com.ximalaya.mediaprocessor.IMediaErrorListener {*;}
-keep public class com.ximalaya.mediaprocessor.MediaAEC {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class org.json.JSONException {*;}
-keep public class org.json.JSONObject {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.Iterator {*;}
-keep public class java.util.LinkedList {*;}
-keep public class java.util.List {*;}
-keep public class android.os.Bundle {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class android.app.Dialog {*;}
-keep public class android.app.ProgressDialog {*;}
-keep public class android.graphics.Bitmap {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.support.v4.app.FragmentManager {*;}
-keep public class android.view.LayoutInflater {*;}
-keep public class android.view.View {*;}
-keep public class android.view.View.OnClickListener {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.view.Window {*;}
-keep public class android.widget.EditText {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.TextView {*;}
-keep public class android.widget.Toast {*;}
-keep public class com.ximalaya.ting.android.fragment.other.BaseDialogFragment {*;}
-keep public class com.ximalaya.ting.android.framework.manager.ImageManager {*;}
-keep public class com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog {*;}
-keep public class com.ximalaya.ting.android.opensdk.httputil.XimalayaException {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class java.io.IOException {*;}
-keep public class com.ximalaya.mediaprocessor.AudioReverb {*;}
-keep public class android.util.Log {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class java.util.LinkedList {*;}
-keep public class java.util.List {*;}
-keep public class java.util.concurrent.LinkedBlockingQueue {*;}
-keep public class com.ximalaya.mediaprocessor.IMediaErrorListener {*;}
-keep public class com.ximalaya.mediaprocessor.MediaDecoder {*;}
-keep public class com.ximalaya.mediaprocessor.MediaMixer {*;}
-keep public class android.media.AudioFormat {*;}
-keep public class com.ximalaya.mediaprocessor.IMediaErrorListener {*;}
-keep public class com.ximalaya.mediaprocessor.MediaEncoder {*;}
-keep public class android.os.Environment {*;}
-keep public class com.ximalaya.mediaprocessor.IMediaErrorListener {*;}
-keep public class com.ximalaya.mediaprocessor.MediaMixer {*;}
-keep public class java.io.File {*;}
-keep public class java.io.FileOutputStream {*;}
-keep public class java.io.IOException {*;}
-keep public class java.util.List {*;}
-keep public class android.media.AudioFormat {*;}
-keep public class android.media.AudioManager {*;}
-keep public class android.media.AudioTrack {*;}
-keep public class com.ximalaya.mediaprocessor.MediaAEC {*;}
-keep public class android.media.AudioFormat {*;}
-keep public class android.media.MediaRecorder.AudioSource {*;}
-keep public class android.os.Environment {*;}
-keep public class com.ximalaya.mediaprocessor.MediaAEC {*;}
-keep public class com.ximalaya.mediaprocessor.MediaNS {*;}
-keep public class java.io.File {*;}
-keep public class java.io.FileOutputStream {*;}
-keep public class android.media.AudioRecord {*;}
-keep public class android.content.Context {*;}
-keep public class android.os.Build {*;}
-keep public class android.os.Environment {*;}
-keep public class android.text.TextUtils {*;}
-keep public class com.ximalaya.ting.android.MainApplication {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class java.io.File {*;}
-keep public class java.math.BigDecimal {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.SharedPreferences {*;}
-keep public class android.database.Cursor {*;}
-keep public class android.text.TextUtils {*;}
-keep public class com.google.gson.Gson {*;}
-keep public class com.google.gson.reflect.TypeToken {*;}
-keep public class com.ximalaya.ting.android.framework.BaseApplication {*;}
-keep public class com.ximalaya.ting.android.framework.constants.PreferenceConstantsLib {*;}
-keep public class com.ximalaya.ting.android.framework.database.DBConnector {*;}
-keep public class com.ximalaya.ting.android.framework.database.DBUtil {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.IDbDataCallBack {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.List {*;}
-keep public class java.util.Vector {*;}
-keep public class android.content.Context {*;}
-keep public class android.media.MediaPlayer {*;}
-keep public class android.os.Handler {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.res.AssetFileDescriptor {*;}
-keep public class android.content.res.AssetManager {*;}
-keep public class android.media.MediaPlayer.OnCompletionListener {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer.PlayerStatusListener {*;}
-keep public class java.io.File {*;}
-keep public class java.io.FileDescriptor {*;}
-keep public class java.io.FileInputStream {*;}
-keep public class java.io.IOException {*;}
-keep public class android.app.Activity {*;}
-keep public class android.content.Context {*;}
-keep public class android.database.sqlite.SQLiteDatabase {*;}
-keep public class android.support.v4.app.FragmentActivity {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.host.adapter.track.TrackAdapterCreator {*;}
-keep public class com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordFunctionAction {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ResultWrapper {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.UploadType {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.IDbDataCallBack {*;}
-keep public class org.json.JSONObject {*;}
-keep public class java.io.FileNotFoundException {*;}
-keep public class java.util.List {*;}
-keep public class java.util.Map {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordActivityAction {*;}
-keep public class android.os.Bundle {*;}
-keep public class android.support.v4.app.DialogFragment {*;}
-keep public class com.ximalaya.ting.android.framework.fragment.BaseFragment {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.model.BundleException {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordFragmentAction {*;}
-keep public class com.ximalaya.ting.android.host.model.album.AlbumM {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.Map {*;}
-keep public class android.support.v4.app.FragmentActivity {*;}
-keep public class android.text.TextUtils {*;}
-keep public class com.ximalaya.ting.android.framework.util.CustomToast {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.base.ListModeBase {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.main.util.http.TokenIntercepterUrlList {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class org.json.JSONObject {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.Map {*;}
-keep public class android.text.TextUtils {*;}
-keep public class com.ximalaya.ting.android.manager.record.BaseDownloadTask {*;}
-keep public class com.ximalaya.ting.android.manager.record.DownloadManager {*;}
-keep public class java.io.File {*;}
-keep public class java.io.IOException {*;}
-keep public class android.content.Context {*;}
-keep public class android.os.Handler {*;}
-keep public class android.os.HandlerThread {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.widget.Toast {*;}
-keep public class com.google.gson.Gson {*;}
-keep public class com.tencent.bugly.crashreport.CrashReport {*;}
-keep public class com.ximalaya.ting.android.MainApplication {*;}
-keep public class com.ximalaya.ting.android.framework.manager.XDCSCollectUtil {*;}
-keep public class com.ximalaya.ting.android.framework.util.FileUtil {*;}
-keep public class com.ximalaya.ting.android.host.manager.account.UserInfoMannage {*;}
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.ResultWrapper {*;}
-keep public class com.ximalaya.ting.android.host.model.upload.UploadType {*;}
-keep public class com.ximalaya.ting.android.host.util.common.DeviceUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.common.EncryptUtil {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.HttpParamsConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.server.NetworkUtils {*;}
-keep public class com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking {*;}
-keep public class com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.datatrasfer.IUploadCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.httputil.BaseResponse {*;}
-keep public class com.ximalaya.ting.android.opensdk.httputil.XimalayaException {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.PlayableModel {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.XmPlayerManager {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.IDbDataCallBack {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask$* {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.NetworkType {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil {*;}
-keep public class org.json.JSONArray {*;}
-keep public class org.json.JSONException {*;}
-keep public class org.json.JSONObject {*;}
-keep public class java.io.DataInputStream {*;}
-keep public class java.io.DataOutputStream {*;}
-keep public class java.io.File {*;}
-keep public class java.io.FileNotFoundException {*;}
-keep public class java.io.IOException {*;}
-keep public class java.net.InetSocketAddress {*;}
-keep public class java.net.Socket {*;}
-keep public class java.net.SocketTimeoutException {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.Collections {*;}
-keep public class java.util.Comparator {*;}
-keep public class java.util.HashMap {*;}
-keep public class java.util.HashSet {*;}
-keep public class java.util.Iterator {*;}
-keep public class java.util.LinkedList {*;}
-keep public class java.util.List {*;}
-keep public class java.util.Map {*;}
-keep public class java.util.Set {*;}
-keep public class okhttp3.Response {*;}
-keep public class android.content.Context {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.RouterConstant {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router {*;}
-keep public class android.content.Context {*;}
-keep public class android.database.Cursor {*;}
-keep public class android.database.sqlite.SQLiteDatabase {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.PlayableModel {*;}
-keep public class com.ximalaya.ting.android.opensdk.model.album.Announcer {*;}
-keep public class com.ximalaya.ting.android.opensdk.util.Logger {*;}
-keep public class java.util.LinkedList {*;}
-keep public class java.util.List {*;}
-keep public class java.nio.ByteOrder {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class android.graphics.Bitmap {*;}
-keep public class android.support.annotation.Nullable {*;}
-keep public class android.support.v4.app.FragmentActivity {*;}
-keep public class android.view.MotionEvent {*;}
-keep public class android.view.View {*;}
-keep public class android.view.ViewConfiguration {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.RelativeLayout {*;}
-keep public class com.nineoldandroids.animation.Animator {*;}
-keep public class com.nineoldandroids.animation.AnimatorSet {*;}
-keep public class com.nineoldandroids.animation.ObjectAnimator {*;}
-keep public class com.nineoldandroids.animation.ValueAnimator {*;}
-keep public class com.ximalaya.ting.android.fragment.BaseFragment2 {*;}
-keep public class com.ximalaya.ting.android.host.fragment.web.WebFragment {*;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordFunctionAction {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.AppConstants {*;}
-keep public class com.ximalaya.ting.android.host.util.constant.UrlConstants {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.res.TypedArray {*;}
-keep public class android.graphics.Canvas {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.graphics.Paint {*;}
-keep public class android.util.AttributeSet {*;}
-keep public class android.view.View {*;}
-keep public class android.content.Context {*;}
-keep public class android.graphics.Canvas {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.graphics.Paint {*;}
-keep public class android.graphics.Paint.Style {*;}
-keep public class android.graphics.Path {*;}
-keep public class android.util.AttributeSet {*;}
-keep public class android.view.View {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.res.TypedArray {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.graphics.Typeface {*;}
-keep public class android.util.AttributeSet {*;}
-keep public class android.view.LayoutInflater {*;}
-keep public class android.view.View {*;}
-keep public class android.widget.LinearLayout {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.ximalaya.ting.android.framework.util.BaseUtil {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.res.TypedArray {*;}
-keep public class android.graphics.Canvas {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.graphics.Paint {*;}
-keep public class android.graphics.Paint.Style {*;}
-keep public class android.util.AttributeSet {*;}
-keep public class android.view.View {*;}
-keep public class android.content.Context {*;}
-keep public class android.util.AttributeSet {*;}
-keep public class android.view.MotionEvent {*;}
-keep public class android.widget.ListView {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.res.TypedArray {*;}
-keep public class android.graphics.Canvas {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.graphics.Paint {*;}
-keep public class android.support.annotation.Nullable {*;}
-keep public class android.util.AttributeSet {*;}
-keep public class android.view.View {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.res.TypedArray {*;}
-keep public class android.graphics.Bitmap {*;}
-keep public class android.graphics.Canvas {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.graphics.Paint {*;}
-keep public class android.graphics.drawable.Drawable {*;}
-keep public class android.util.AttributeSet {*;}
-keep public class android.view.MotionEvent {*;}
-keep public class android.view.View {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.List {*;}
-keep public class android.graphics.Paint {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.res.TypedArray {*;}
-keep public class android.graphics.Bitmap {*;}
-keep public class android.graphics.Canvas {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.graphics.Paint {*;}
-keep public class android.graphics.Point {*;}
-keep public class android.graphics.drawable.Drawable {*;}
-keep public class android.util.AttributeSet {*;}
-keep public class android.view.MotionEvent {*;}
-keep public class android.view.View {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class android.graphics.Canvas {*;}
-keep public class android.graphics.Paint {*;}
-keep public class android.graphics.PorterDuff.Mode {*;}
-keep public class android.graphics.PorterDuffXfermode {*;}
-keep public class android.view.SurfaceHolder {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.List {*;}
-keep public class android.app.Dialog {*;}
-keep public class android.content.Context {*;}
-keep public class android.support.annotation.NonNull {*;}
-keep public class android.view.Gravity {*;}
-keep public class android.view.View {*;}
-keep public class android.view.Window {*;}
-keep public class android.content.Context {*;}
-keep public class android.media.MediaPlayer {*;}
-keep public class android.os.Handler {*;}
-keep public class android.os.Message {*;}
-keep public class android.text.TextUtils {*;}
-keep public class android.view.LayoutInflater {*;}
-keep public class android.view.View {*;}
-keep public class android.view.ViewGroup {*;}
-keep public class android.widget.ImageView {*;}
-keep public class android.widget.PopupWindow {*;}
-keep public class android.widget.TextView {*;}
-keep public class com.ximalaya.ting.android.host.util.common.TimeHelper {*;}
-keep public class com.ximalaya.ting.android.opensdk.player.advertis.MiniPlayer {*;}
-keep public class java.io.File {*;}
-keep public class android.content.res.Resources {*;}
-keep public class android.graphics.Bitmap {*;}
-keep public class android.view.MotionEvent {*;}
-keep public class android.view.SurfaceHolder {*;}
-keep public class android.view.View {*;}
-keep public class android.view.View.OnTouchListener {*;}
-keep public class android.view.ViewConfiguration {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class java.math.BigDecimal {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.List {*;}
-keep public class android.content.Context {*;}
-keep public class android.content.res.TypedArray {*;}
-keep public class android.graphics.Bitmap {*;}
-keep public class android.graphics.Color {*;}
-keep public class android.graphics.drawable.Drawable {*;}
-keep public class android.util.AttributeSet {*;}
-keep public class android.view.SurfaceView {*;}
-keep public class com.ximalaya.ting.android.framework.util.BitmapUtils {*;}
-keep public class java.util.ArrayList {*;}
-keep public class java.util.List {*;}
#-------------------录音模块使用的类 end-----------------------

#-------------------智能硬件模块混淆规则 start--------------——--
-dontwarn com.umeng.**
-keep class com.umeng.** { *;}
-dontwarn u.aly.**
-keep class u.aly.** { *;}

-dontwarn com.squareup.**
-keep class com.squareup.** { *;}

-dontwarn com.handmark.pulltorefresh.**
-keep class com.handmark.pulltorefresh.** { *;}

-dontwarn com.androidwiimusdk.**
-keep class com.androidwiimusdk.** { *;}

-dontwarn com.ximalaya.ting.android.player.**
-keep class com.ximalaya.ting.android.player.** { *;}

-dontwarn com.google.**
-keep class com.google.** { *;}

-dontwarn okio.**
-keep class okio.** { *;}

-dontwarn com.sun.**
-keep class com.sun.** { *;}

-dontwarn sun.net.**
-keep class sun.net.** { *;}

-dontwarn com.ifeegoo.lib.**
-keep class com.ifeegoo.lib.** {*;}

-dontwarn com.chipsguide.lib.**
-keep class com.chipsguide.lib.** {*;}

-dontwarn com.actions.ibluz.**
-keep class com.actions.ibluz.** {*;}

-dontwarn com.iflytek.**
-keep class com.iflytek.** {*;}

-dontwarn com.androidwiimusdk.**
-keep class com.androidwiimusdk.** {*;}

-dontwarn org.teleal.**
-keep class org.teleal.** {*;}

-dontwarn com.ximalaya.action.**
-keep class com.ximalaya.action.** {*;}

-dontwarn com.ximalaya.activity.**
-keep class com.ximalaya.activity.** {*;}

-dontwarn com.ximalaya.service.**
-keep class com.ximalaya.service.** {*;}

-dontwarn com.ximalaya.audioserver.**
-keep class com.ximalaya.audioserver.** {*;}

-dontwarn com.ximalaya.model.**
-keep class com.ximalaya.model.** {*;}

-dontwarn com.ximalaya.util.**
-keep class com.ximalaya.util.** {*;}

-dontwarn com.eshare.**
-keep class com.eshare.** {*;}

-dontwarn com.ximalaya.maindatasupport.**
-keep class com.ximalaya.maindatasupport.** {*;}

-dontwarn com.ximalaya.ting.android.data.**
-keep class com.ximalaya.ting.android.data.** {*;}

-dontwarn com.ximalaya.speechcontrol.**
-keep class com.ximalaya.speechcontrol.** {*;}

-dontwarn com.astuetz.**
-keep class com.astuetz.** {*;}

-dontwarn com.ximalaya.ting.android.opensdk.**
-keep class com.ximalaya.ting.android.opensdk.** {*;}

-dontwarn cn.coolhear.**
-keep class cn.coolhear.** {*;}

-dontwarn com.coolhear.**
-keep class com.coolhear.** {*;}

-keep class com.ximalaya.device.smartdevice.activity.** { *; }
-keep class com.ximalaya.device.smartdevice.adapter.** { *; }
-keep class com.ximalaya.device.smartdevice.broadcast.** { *; }
-keep class com.ximalaya.device.smartdevice.model.** { *; }
-keep class com.ximalaya.device.smartdevice.view.** { *; }
-keep class com.ximalaya.device.smartdevice.manager.** { *; }
-keep class com.ximalaya.device.smartdevice.transaction.** { *; }
-keep class com.ximalaya.device.smartdevice.fragment.** { *; }
-keep class com.ximalaya.device.smartdevice.util.** { *; }
-keep class com.ximalaya.device.smartdevice.control.** { *; }

-keep class com.ximalaya.ting.device.commonuilibrary.activity.** { *; }
-keep class com.ximalaya.ting.device.commonuilibrary.adapter.** { *; }
-keep class com.ximalaya.ting.device.commonuilibrary.fragment.** { *; }
-keep class com.ximalaya.ting.device.commonuilibrary.model.** { *; }
-keep class com.ximalaya.ting.device.commonuilibrary.manager.** { *; }
-keep class com.ximalaya.ting.device.commonuilibrary.util.** { *; }

-keep class com.ximalaya.ting.android.plugin.framework.activity.** { *; }
-keep class com.ximalaya.ting.android.plugin.framework.adapter.** { *; }
-keep class com.ximalaya.ting.android.plugin.framework.fragment.** { *; }
-keep class com.ximalaya.ting.android.plugin.framework.data.** { *; }
-keep class com.ximalaya.ting.android.plugin.framework.manager.** { *; }
-keep class com.ximalaya.ting.android.plugin.framework.util.** { *; }
-keep class com.ximalaya.ting.lite.main.playlet.model.** { *; }

-keep class com.ximalaya.device.wificontrollibrary.controller.** { *; }
-keep class com.ximalaya.device.wificontrollibrary.manager.** { *; }
-keep class com.ximalaya.device.wificontrollibrary.module.** { *; }
-keep class com.ximalaya.device.wificontrollibrary.model.** { *; }

-keep public class com.ximalaya.device.smartdevice.R$* { *; }

-keepattributes Signature,Exceptions,InnerClasses,SourceFile,LineNumberTable

-keepattributes *Annotation*

#android默认项
-keep public class * extends android.app.Fragment
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class com.android.vending.licensing.ILicensingService

-keepclasseswithmembernames class * {
    native <methods>;
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

-keep public class com.ximalaya.device.smartdevice.R$*{
public static final int *; }

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
#-------------------智能硬件模块混淆规则 end----------------——--
#----组件框架混淆规则 begin -------
-keep class * implements com.ximalaya.ting.android.hybridview.NoProguard{
    protected <methods>;
    public <methods>;
    public <fields>;
}
#----组件框架混淆规则 end ------
#----组件框架统计模块混淆规则 begin -------
-keep class * implements com.ximalya.ting.android.statisticsservice.NoProguard{
    protected <methods>;
    public <methods>;
    public <fields>;
}

-keep class com.ximalya.ting.android.statisticsservice.bean.**{
 *;
}


#----组件框架统计模块混淆规则 end ------

#----智能硬件依赖Host混淆规则 ----
-keep public class com.ximalaya.ting.android.host.util.common.JsonUtil { *; }
-keep public class com.ximalaya.ting.android.opensdk.util.Logger { *; }
-keep public class com.ximalaya.ting.android.opensdk.player.XmPlayerManager { *;}
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.Configure { *; }
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.model.BundleException { *; }
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.watch.** { *; }
-keep public class com.ximalaya.ting.android.opensdk.model.track.Track { *; }
-keep public class com.ximalaya.ting.android.opensdk.util.MyAsyncTask { *; }
-keep public class com.ximalaya.ting.android.host.model.search.SuggestWordsM { *; }
-keep public class com.ximalaya.ting.android.host.model.device.ProductModel { *; }
-keep public class com.ximalaya.ting.android.host.util.common.StringUtil { *; }
-keep public class com.ximalaya.ting.android.host.model.anchor.Anchor { *; }
-keep public class com.ximalaya.ting.android.host.model.search.AlbumResultM { *; }
-keep public class com.ximalaya.ting.android.host.model.search.QueryResultM { *; }
-keep public class com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener { *; }
-keep public class com.ximalaya.ting.android.host.model.category.CategoryRecommendMList { *; }
-keep public class com.ximalaya.ting.android.host.model.search.SearchHotWord { *; }
-keep public class com.ximalaya.ting.android.host.manager.bundleframework.route.action.smartdevice.** { *; }
-keep public class com.ximalaya.ting.android.host.model.base.BaseModel { *; }
-keep public class com.ximalaya.ting.android.host.manager.request.CommonRequestM.IRequestCallBack { *; }

-keep public class com.ximalaya.ting.android.host.manager.account.XmLocationManager { *; }
-keep public class com.ximalaya.ting.android.opensdk.player.service.XmPlayerException { *; }
