project.afterEvaluate {
    def android = project.extensions.android
    android.applicationVariants.all { variant ->
        def mergeTaskName = "merge" + toUpperCaseFirstOne(channel) + "ReleaseResources"
        def mergeTask = project.tasks.getByName(mergeTaskName)
        if (mergeTask) {
            mergeTask.outputs.upToDateWhen { false }
            mergeTask.doLast {
                println "====================merged resources start============================"
                mergeTask.outputs.each {output->
                    output.getFiles().each {file->
                        println file
                    }
                }
                println "====================merged resources end============================"
            }
        }

    }
}

static String toUpperCaseFirstOne(String s) {
    if (Character.isUpperCase(s.charAt(0)))
        return s;
    else
        return (new StringBuilder()).append(Character.toUpperCase(s.charAt(0))).append(s.substring(1)).toString();
}