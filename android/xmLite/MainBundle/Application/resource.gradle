buildscript {
    repositories {
        maven { url "http://************:8082/artifactory/ximalaya-android-maven-repo/"}
        maven { url "http://************:8082/artifactory/host/"}
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/jcenter" }
        maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:2.3.3'
    }
}
project.afterEvaluate {
    def android = project.extensions.android
    android.applicationVariants.all { variant ->
        def variantOutput = variant.outputs.first()
        def variantName = variant.name.capitalize()
        if (!variantName.equals("Debug")) {
            def mergeReleaseResources = variant.mergeResources
            if (mergeReleaseResources) {
                mergeReleaseResources.doLast{
                    File srcPublicFile = new File(project.rootDir.absolutePath + "/TingMainHost/TingMainApp/constant-resource/public.xml")
                    File srcIdxFile = new File(project.rootDir.absolutePath + "/TingMainHost/TingMainApp/constant-resource/ids.xml")
                    if(srcIdxFile.exists() && srcPublicFile.exists()) {
                        String idsXml = variantOutput.processResources.resDir.absolutePath + "/values/ids.xml";
                        String publicXml = variantOutput.processResources.resDir.absolutePath + "/values/public.xml";
                        File publicFile = new File(publicXml)
                        if(!publicFile.exists()) {
                            publicFile.createNewFile()
                        }
                        copyFile(srcPublicFile, publicFile)

                        File idxFile = new File(idsXml)
                        if(!idxFile.exists()) {
                            idxFile.createNewFile();
                        }
                        copyFile(srcIdxFile, idxFile)
                    }else {
                        println("applicaton resource.gradle file not exist" + srcPublicFile.exists() + srcIdxFile.exists());
                        println(" file path: "+srcPublicFile +" " +publicXml)
                    }
                }
            }
        }

    }
}

static void copyFile(File srcFile, File destFile) {
    destFile.getParentFile().mkdirs()
    def inputStream = srcFile.newInputStream()
    def outputStream = destFile.newOutputStream()
    outputStream << inputStream
    inputStream.close()
}