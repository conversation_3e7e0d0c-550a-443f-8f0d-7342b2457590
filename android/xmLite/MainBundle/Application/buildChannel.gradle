import java.util.zip.ZipEntry
import java.util.zip.ZipFile
import java.util.zip.ZipOutputStream

buildscript {
    repositories {
        maven { url "http://************:8082/artifactory/ximalaya-android-maven-repo/"}
        maven { url "http://************:8082/artifactory/host/"}
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/jcenter" }
        maven { url "https://maven.aliyun.com/repository/gradle-plugin"
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:2.3.3'
    }
}
project.afterEvaluate {
    def android = project.extensions.android
    android.applicationVariants.all { variant ->
        def kNoCompressExt = [
                ".jpg", ".jpeg", ".png", ".gif",
                ".wav", ".mp2", ".mp3", ".ogg", ".aac",
                ".mpg", ".mpeg", ".mid", ".midi", ".smf", ".jet",
                ".rtttl", ".imy", ".xmf", ".mp4", ".m4a",
                ".m4v", ".3gp", ".3gpp", ".3g2", ".3gpp2",
                ".amr", ".awb", ".wma", ".wmv"
        ]



        def assembleTaskName = "assemble" + toUpperCaseFirstOne(channel) + "Release"
        def assembleTask = project.tasks.getByName(assembleTaskName)
        if (assembleTask) {
            assembleTask.outputs.upToDateWhen { false }
            assembleTask.doLast {
                if (gradle.rootProject.needChannel.toBoolean()) {
                    println "======================buildChannels start================================================"
                    def srcFile
                    variant.outputs.each { output ->
                        def outputFile = output.outputFile
                        if (outputFile != null && outputFile.name.endsWith('.apk') && outputFile.name.contains('release')) {
                            srcFile = outputFile
                        }
                    }
                    def baseDir = "TingMainHost/Application/build/outputs/apk/"

                    if (srcFile) {
                        def tmpName = "tmp.apk"
                        def tmpPath = baseDir + tmpName
                        def originZipFile = new ZipFile(srcFile)
                        def outFile = new File(tmpPath)

                        outFile.withOutputStream { os ->
                            def zipOut = new ZipOutputStream(os)

                            // 完全遍历拷贝原 APK 的 entry
                            originZipFile.entries().each { entry ->

                                println "name: " + entry.name + "======================"
                                def arr = entry.name.split('\\.')
                                println "arr: " + arr + "================================="
                                if(arr.length > 0){
                                    def extension = "." + arr[arr.length -1]

                                    println "extension: " + extension  + "==================================="

                                    if(extension in kNoCompressExt){
                                        println extension + " in arr==================================="
                                        zipOut.putNextEntry(entry)
                                    }else {
                                        zipOut.putNextEntry(new ZipEntry(entry.name))
                                    }
                                }else{
                                    zipOut.putNextEntry(new ZipEntry(entry.name))
                                }


                                zipOut << originZipFile.getInputStream(entry).bytes
                                zipOut.closeEntry()
                            }

                            // 创建传入的空 entry
                            zipOut.putNextEntry(new ZipEntry("META-INF/ximalaya_" + gradle.rootProject.channel))
                            zipOut.closeEntry()

                            zipOut.close()
                        }
                        originZipFile.close()
                        srcFile.delete()
                        outFile.renameTo(new File(srcFile.getAbsolutePath()))
                    }

                    println "======================buildChannels end================================================"
                }
            }
        }

    }
}

static String toUpperCaseFirstOne(String s) {
    if (Character.isUpperCase(s.charAt(0)))
        return s;
    else
        return (new StringBuilder()).append(Character.toUpperCase(s.charAt(0))).append(s.substring(1)).toString();
}