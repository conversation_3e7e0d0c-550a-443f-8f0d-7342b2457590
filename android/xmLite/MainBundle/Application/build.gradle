apply plugin: 'com.android.application'
apply plugin: 'com.ximalaya.gradle.makeroute'
ext {
    //extChannels = "and-d3"//不能命名test/debug等字段开头
    extChannels = "ceshi"// 开发时渠道改为 ceshi
    //pChannels shell 输入参数
    shallReadChannelfromFile = "false"
    versionCode = "134"
    versionName = "6.3.84.dev"
    minSdkVersion = "16"
    targetSdkVersion = "23"
    isProguard = "false"
    isReleaseDebug = "true"
}

def readChannel() {
    if (shallReadChannelfromFile.toBoolean()) {
        println "------------------read Channels from file start-------------------------"
        // 渠道号配置文件路径
        def path = "build-files/channels.txt"
        file(path).eachLine { channel ->
            if (!channel.startsWith("//")) { //剔除注释行
                setManifestValue(channel)
            }
        }
        println "------------------read Channels from file end-------------------------"
    } else if (project.hasProperty('pChannels')) {
        println "------------------read Channels from property start---------------"
        project.properties['pChannels'].split(',').each { channel ->
            setManifestValue(channel)
        }
        println "------------------read Channels from property end---------------"
    } else if (project.hasProperty('extChannels')) {
        println "------------------read extChannels from ext property start---------------"
        project.properties['extChannels'].split(',').each { channel ->
            setManifestValue(channel)
        }
        println "------------------read extChannels from ext property end---------------"
    } else {
        println "------------------No Channels-------------------------"
    }
}

// 设置Manifest里面需要替换的字段
private void setManifestValue(channel) {
    println channel
    android.productFlavors.create(channel, {
        def buildDate = new Date().format("yyyy-MM-dd-HH-mm")

        if (isProguard.toBoolean() && !(isReleaseDebug.toBoolean())) {
            // 正式包
            manifestPlaceholders = [CHANNEL_VALUE: name, UMENG_APP_KEY: "5d022275570df31369000ccf", BUILD_DATE: buildDate]
        } else {
            manifestPlaceholders = [CHANNEL_VALUE: name, UMENG_APP_KEY: "5d022275570df31369000ccf", BUILD_DATE: buildDate]
        }
    })
}

def buildTime = new Date().format("yyMMdd")

dependencies {
    api fileTree(include: '*.jar', dir: 'libs')
    if (findProject(':MainModule') != null) {
        api project(':MainModule')
    } else {
        api project(':MainBundle:MainModule')
        compile project(':XAndroidFramework:XmLoader')
    }
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            assets.srcDirs = ['assets']
            jniLibs.srcDirs = ['libs']
        }

        androidTest.setRoot('tests')

        debug.setRoot('build-types/debug')
        release.setRoot('build-types/release')
    }

    defaultConfig {
        applicationId "com.ximalaya.ting.android"
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [moduleName: project.getName(), packageName: applicationId, isApplication: "true"]
            }
        }
        minSdkVersion Integer.parseInt(project.property('minSdkVersion'))
        targetSdkVersion Integer.parseInt(project.property('targetSdkVersion'))
        if (project.hasProperty('pVersionCode')) {
            versionCode Integer.parseInt(project.property('pVersionCode'))
        } else {
            versionCode Integer.parseInt(project.property('versionCode'))
        }
        if (project.hasProperty('pVersionName')) {
            versionName project.property('pVersionName')
        } else {
            versionName project.property('versionName')
        }
        if (project.hasProperty('pIsProguard')) {
            isProguard = project.property('pIsProguard')
        } else {
            isProguard = project.property('isProguard')
        }
        if (project.hasProperty('pIsReleaseDebug')) {
            isReleaseDebug = project.property('pIsReleaseDebug')
        } else {
            isReleaseDebug = project.property('isReleaseDebug')
        }
        //支持multidex 解决65536爆包问题
        multiDexEnabled true

        ndk {
            // 设置支持的SO库架构
            abiFilters 'armeabi'/*, 'x86'*///, 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }
    }

    productFlavors {
        readChannel()
    }

    // 签名
    signingConfigs {
        myConfig {
            storeFile file("build-files/ximalaya.keystore")
            storePassword "ximalaya!1203"
            keyAlias "ximalaya"
            keyPassword "ximalaya!1203"
            v2SigningEnabled false
        }
    }

    //自动移除不用资源
    buildTypes {
        release {
            debuggable project.property('isReleaseDebug').toBoolean()
            signingConfig signingConfigs.myConfig
            //是否混淆
            minifyEnabled project.property('isProguard').toBoolean()
            zipAlignEnabled project.property('isProguard').toBoolean()
            // 移除无用的resource文件
            shrinkResources false
            multiDexKeepProguard file('multidex-config.pro')
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-project.txt'
            applicationVariants.all { variant ->
                variant.outputs.all {
                    if (minifyEnabled.toBoolean()) {
                        println "------------------apk output proguard file-------------------------"
                        def fileName = "MainApp_v${defaultConfig.versionName}_c${defaultConfig.versionCode}_release_proguard_${buildTime}"
                        if (needChannel.toBoolean()) {
                            fileName += "_${variant.productFlavors[0].name}"
                        }
                        fileName += ".apk"
                        outputFileName = fileName
                    } else {
                        println "------------------apk output normal file-------------------------"
                        def fileName = "MainApp_v${defaultConfig.versionName}_c${defaultConfig.versionCode}_release_${buildTime}"
                        if (needChannel.toBoolean()) {
                            fileName += "_${variant.productFlavors[0].name}"
                        }
                        fileName += ".apk"
                        outputFileName = fileName
                    }
                }
            }
            android.applicationVariants.all { variant ->
                variant.assemble.doLast {
                    variant.outputs.each { output ->
                        println "aligned " + output.outputFile
                        println "unaligned " + output.packageApplication.outputFile

                        File unaligned = output.packageApplication.outputFile
                        File aligned = output.outputFile
                        if (!unaligned.getName().equalsIgnoreCase(aligned.getName())) {
                            println "deleting " + unaligned.getName()
                            unaligned.delete()
                        }
                    }
                }
            }
        }
    }

    lintOptions {
        abortOnError false
        lintConfig file("lint.xml")
    }

    compileOptions {
        sourceCompatibility rootProject.javaCompileVersion
        targetCompatibility rootProject.javaCompileVersion
    }

    //为所有的子项目设置一些通用配置
    subprojects {
        afterEvaluate {
            if (getPlugins().hasPlugin('android') ||
                    getPlugins().hasPlugin('android-library')) {
                android {
                    lintOptions {
//                        quiet true
                        abortOnError false
//                        ignoreWarnings true
//                        checkAllWarnings false
//                        checkReleaseBuilds false
                    }
                }
            }
        }
    }


    aaptOptions.cruncherEnabled = false
    aaptOptions.useNewCruncher = false

    dexOptions {
        maxProcessCount 4
        javaMaxHeapSize "2048M"
    }
}
android.variantFilter { variant ->
    if (variant.buildType.name.endsWith('debug')) {
        variant.setIgnore(true)
    }
}



