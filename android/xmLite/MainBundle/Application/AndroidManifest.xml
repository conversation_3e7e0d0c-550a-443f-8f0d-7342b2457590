﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.ximalaya.ting.lite"
    android:versionCode="124"
    android:versionName="5.4.69.3_live3">

    <uses-sdk
        android:minSdkVersion="14"
        android:targetSdkVersion="23"
        tools:overrideLibrary="com.android.inputmethod.pinyin" />

    <!-- Needfed only if your app targets Android 5.0 (API level 21) or higher. -->
    <uses-feature android:name="android.hardware.location.gps" />
    <uses-feature android:name="android.hardware.location.network" />

    <uses-permission android:name="android.permission.READ_SETTINGS" />
    <uses-permission android:name="com.xiaomi.permission.AUTH_SERVICE" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <!--<uses-permission android:name="android.permission.VIBRATE"/>-->
    <uses-feature android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- 魅族登录SDK使用权限 -->
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />


    <!-- 小米推送权限 -->
    <uses-permission android:name="com.ximalaya.ting.android.permission.MIPUSH_RECEIVE" />

    <!--oppo 推送权限-->
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />


    <permission
        android:name="com.ximalaya.ting.android.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature" />

    <!-- 建立桌面图标 -->
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <application
        android:name="com.ximalaya.ting.android.xmloader.XMApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/host_app_name"
        android:largeHeap="true"
        android:persistent="true"
        android:screenOrientation="portrait"
        android:supportsRtl="false"
        android:theme="@style/host_AppTheme"
        tools:replace="android:icon,android:theme,android:label,android:name,android:supportsRtl">

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="10298000"
            tools:replace="android:value" />

        <activity
            android:name="com.ximalaya.ting.android.main.activity.test.TestActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"></activity>

        <!-- MySpin -->

        <activity
            android:name="com.ximalaya.ting.android.host.car.MySpinActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:launchMode="singleTop"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="com.bosch.myspin.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service android:name="com.ximalaya.ting.android.car.myspin.service.MySpinAudioManagementService" />
        <!-- MySpin end -->
        <activity
            android:name="com.ximalaya.ting.android.car.welink.WeLinkActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:exported="true"
            android:launchMode="singleTop"></activity>
        <service
            android:name="com.ximalaya.ting.android.car.welink.WeLinkDataService"
            android:exported="true"
            android:process=":player" />

        <!-- CarLink -->
        <!--<service-->
        <!--android:name="com.ximalaya.ting.android.car.CarLinkService"-->
        <!--android:exported="false">-->
        <!--</service>-->
        <service
            android:name="com.ximalaya.ting.android.car.applink.AppLinkService"
            android:exported="false" />
        <!-- CarLink end-->
        <service
            android:name="com.ximalaya.ting.android.car.applink.FordSdlRouterService"
            android:exported="true">
            <!--android:process="com.smartdevicelink.router"-->
        </service>
        <!-- ChangCheng -->
        <service
            android:name="com.ximalaya.ting.android.car.ccsdl.SdlService"
            android:exported="false" />
        <!-- ChangCheng end-->


        <!-- 百度CarLife -->
        <activity
            android:name="com.ximalaya.ting.android.car.carlife.CarlifeActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/ActivityTheme"></activity>

        <!-- 智能硬件插件 -->
        <activity
            android:name="com.ximalaya.ting.android.smartdevice.activity.SmartDeviceMainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/ActivityTheme"
            android:windowSoftInputMode="stateHidden|adjustNothing"></activity>

        <!-- 华为手表 -->
        <activity
            android:name="com.ximalaya.ting.android.watch.activity.WatchMainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/ActivityTheme"
            android:windowSoftInputMode="stateHidden|adjustNothing"></activity>

        <service android:name="com.ximalaya.ting.android.smartdevice.service.DmcUpnpService"></service>

        <service
            android:name="com.ximalaya.ting.android.host.car.CarlifeHandlerService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.baidu.carlife.platform.ak96e005ac7d" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <service android:name="com.ximalaya.ting.android.host.service.DeviceIntentService"></service>
        <!--
        <service
            android:name="com.ximalaya.ting.android.carlink.carlife.CarlifeService"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.baidu.carlife.platform.ak96e005ac7d" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>
        -->
        <!-- UmengData start -->
        <!--
            <meta-data
            android:name="UMENG_APPKEY"
            android:value="5d022275570df31369000ccf" />
        -->
        <meta-data
            android:name="UMENG_APPKEY"
            android:value="${UMENG_APP_KEY}" />
        <!-- UmengData end -->

        <!--打包日期 start-->
        <meta-data
            android:name="BUILD_DATE"
            android:value="${BUILD_DATE}" />
        <!--打包日期 end-->

        <!-- 小米登录 -->
        <activity android:name="com.xiaomi.account.openauth.AuthorizeActivity" />
        <!--
        <activity
            android:name=".activity.CeshiActivity"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" >
        </activity>
        -->
        <activity
            android:name=".host.activity.MainActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:hardwareAccelerated="true"
            android:label="@string/host_app_name"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MainTheme"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize">
            <intent-filter>
                <data
                    android:host="open"
                    android:scheme="iting" />
                <data
                    android:host="component.xm"
                    android:scheme="iting" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <!-- 开屏时长按语音搜索键打开 -->
            <!--  <intent-filter>
                 <action android:name="android.speech.action.WEB_SEARCH" />
                 <category android:name="android.intent.category.DEFAULT" />
             </intent-filter>-->
            <!-- 关屏时长按语音搜索键打开 -->
            <!--  <intent-filter>

                 <action android:name="android.speech.action.VOICE_SEARCH_HANDS_FREE" />
                 <category android:name="android.intent.category.DEFAULT" />
             </intent-filter>-->
        </activity>
        <!--
        <activity
            android:name=".activity.AnchorDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" >
        </activity>
        -->

        <activity
            android:name=".host.activity.web.WebActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/ActivityTheme"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />
        <activity
            android:name="com.ximalaya.ting.android.main.accountModule.bind.activity.AuthorizeActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/ActivityTheme"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />

        <!--<activity android:nacom.ximalaya.ting.android.host.activityvity.QRCodeScanActivity"-->
        <!--android:theme="@style/ActivityTheme"-->
        <!--android:screenOrientation="portrait"-->
        <!--android:hardwareAccelerated="false"/>-->
        <!--
        <activity
            android:name=".activity.ReportActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/Transparent"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" >
        </activity>
        -->


        <!-- 百度CarLife -->
        <!--
         <activity
             android:name="com.ximalaya.ting.android.carlink.CarLifeActivity"
             android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
             android:launchMode="singleTop"
             android:screenOrientation="portrait" >
             <intent-filter>
                 <action android:name="com.baidu.carlife.platform.ak96e005ac7d" />

                 <category android:name="android.intent.category.DEFAULT" />
             </intent-filter>
         </activity>
        -->

        <activity
            android:name=".host.tts.activity.TTSShareActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:excludeFromRecents="true"
            android:label="@string/host_label_tts_activity"
            android:noHistory="true"
            android:screenOrientation="portrait"
            android:theme="@style/Transparent">
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/*" />
            </intent-filter>
        </activity>


        <service
            android:name="com.ximalaya.ting.android.opensdk.player.service.XmPlayerService"
            android:process=":player">
            <intent-filter android:priority="1000">
                <action android:name="com.ximalaya.ting.android.mainapp.player.service.XmPlayerService" />
            </intent-filter>
        </service>


        <receiver
            android:name="com.ximalaya.ting.android.opensdk.player.receive.PlayerReceiver"
            android:exported="true"
            android:process=":player">
            <intent-filter>
                <action android:name="com.ximalaya.ting.android.ACTION_CONTROL_START_PAUSE_MAIN" />
                <action android:name="com.ximalaya.ting.android.ACTION_CONTROL_PLAY_PRE_MAIN" />
                <action android:name="com.ximalaya.ting.android.ACTION_CONTROL_PLAY_NEXT_MAIN" />
                <action android:name="com.ximalaya.ting.android.ACTION_CLOSE_MAIN" />
                <action android:name="com.ximalaya.ting.android.ACTION_CONTROL_RELEASE_SERVICE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.ximalaya.ting.android.opensdk.player.receive.WireControlReceiver"
            android:exported="true"
            android:process=":player"></receiver>

        <!-- xiaomi push start -->
        <service
            android:name="com.xiaomi.push.service.XMPushService"
            android:enabled="true"
            android:process=":pushservice" />

        <service
            android:name="com.xiaomi.push.service.XMJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":pushservice" />
        <!--注：此service必须在3.0.1版本以后（包括3.0.1版本）加入-->

        <service
            android:name="com.xiaomi.mipush.sdk.PushMessageHandler"
            android:enabled="true"
            android:exported="true" />

        <service
            android:name="com.xiaomi.mipush.sdk.MessageHandleService"
            android:enabled="true" />
        <!--注：此service必须在2.2.5版本以后（包括2.2.5版本）加入-->

        <receiver
            android:name="com.xiaomi.push.service.receivers.NetworkStatusReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.xiaomi.push.service.receivers.PingReceiver"
            android:exported="false"
            android:process=":pushservice">
            <intent-filter>
                <action android:name="com.xiaomi.push.PING_TIMER" />
            </intent-filter>
        </receiver>

        <!--小米推送接收器-->
        <receiver android:name="com.ximalaya.ting.android.host.service.XiaoMiPushReceiver">
            <intent-filter>
                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.ERROR" />
            </intent-filter>
        </receiver>

        <!-- xiaomi push end -->

        <!--oppo push start-->
        <service
            android:name="com.xiaomi.assemble.control.COSPushMessageService"
            android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE">
            <intent-filter>
                <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service>

        <activity
            android:name="com.xiaomi.assemble.control.DistributeActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="com.xiaomi.push.MESSAGER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!--oppo push end-->


        <service android:name="com.ximalaya.ting.android.framework.service.DownloadService" />

        <activity
            android:name="com.ximalaya.ting.android.host.activity.WelComeActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:label="@string/host_app_name"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/WelComeTheme"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data
                    android:host="open"
                    android:scheme="itingwelcom" />
            </intent-filter>
        </activity>

        <!-- 分享类东西 -->
        <activity
            android:name="com.tencent.connect.common.AssistActivity"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.tencent.tauth.AuthActivity"
            android:launchMode="singleTask"
            android:noHistory="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="tencent100261563" />
            </intent-filter>
        </activity>

        <!-- 微信支付 -->
        <activity
            android:name=".main.payModule.WebWemartActivityNew"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Transparent"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize">

            <!--
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:scheme="wxb9371ecb5f0f05b1"/>
            </intent-filter>
            -->
        </activity>

        <!-- 有赞AppID -->
        <!--<meta-data-->
        <!--android:name="YZ_APP_ID"-->
        <!--android:value="e42fbb17774ed2e373" />-->
        <!-- 有赞AppSecret -->
        <!--<meta-data-->
        <!--android:name="YZ_APP_SECRET"-->
        <!--android:value="d0db70378aaebd80d7b4a048334dd9fe" />-->
        <!-- 易观  start -->
        <!--<receiver android:name="com.eguan.drivermonitor.receiver.SystemStartReceiver">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.USER_PRESENT" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="monitor_app_key"
            android:value="6266999801473727a" />
        <meta-data
            android:name="monitor_app_channel"
            android:value="${CHANNEL_VALUE}" />

        <service
            android:name="com.eguan.drivermonitor.service.MonitorService"
            android:enabled="true"
            android:process=":monitorService"></service>-->

        <!-- 易观  end -->


        <!-- 桌面小部件appwidget provider -->

        <receiver
            android:name="com.ximalaya.ting.android.opensdk.player.appwidget.WidgetProvider"
            android:exported="true"
            android:label="@string/host_app_widget_label_4x1"
            android:process=":player">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.appwidget.action.APPWIDGET_DELETED" />
                <action android:name="android.appwidget.action.APPWIDGET_DISABLED" />
                <action android:name="android.appwidget.action.APPWIDGET_ENABLED" />
                <action android:name="com.ximalaya.ting.android.ACTION_PLAY_PAUSE" />
                <action android:name="com.ximalaya.ting.android.ACTION_PLAY_START" />
                <action android:name="com.ximalaya.ting.android.ACTION_INIT_UI" />
                <action android:name="com.ximalaya.ting.android.ACTION_CONTROL_PLAY_PRE_MAIN" />
                <action android:name="com.ximalaya.ting.android.ACTION_CONTROL_PLAY_NEXT_MAIN" />
                <action android:name="com.ximalaya.ting.android.ACTION_COMPLETE" />
                <action android:name="android.intent.action.WALLPAPER_CHANGED" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/appwidget1" />
        </receiver>
        <!-- 易观 -->
        <!-- 预注册一些Activity，为了插件使用 -->


        <!-- 插件管理模块 -->
        <!--
        <service
            android:name="com.morgoo.droidplugin.PluginManagerService"
            android:label="@string/service_name_plugin_manager_service"/>

        <activity
            android:name="com.morgoo.droidplugin.stub.ShortcutProxyActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Light.NoTitleBar"/>
        -->


        <!-- 第1个进程 -->


        <!-- 插件管理模块 -->

        <provider
            android:name=".host.util.database.TrackContentProvider"
            android:authorities="com.ximalaya.ting.android.host.util.TrackContentProvider"
            android:exported="true" />
        <provider
            android:name=".host.util.database.TingSharedDataContentProvider"
            android:authorities="com.ximalaya.ting.android.host.util.TingSharedDataContentProvider"
            android:exported="true" />


        <!-- 蓝牙 && USB  -->
        <!-- 添加ABQ广播监听！ -->
        <receiver android:name="com.ximalaya.ting.android.host.receiver.ClAndSmReceiver">
            <meta-data
                android:name="mip_version"
                android:value="1.0" />

            <intent-filter>
                <action android:name="android.bluetooth.device.action.ACL_CONNECTED" />
                <action android:name="android.bluetooth.device.action.ACL_DISCONNECTED" />
                <action android:name="com.smartdevicelink.USB_ACCESSORY_ATTACHED" />
            </intent-filter>

            <intent-filter>
                <action android:name="HAP_BIND" />
            </intent-filter>

        </receiver>

        <receiver android:name="com.ximalaya.ting.android.host.receiver.BootBroadCastReceiver">
            <intent-filter>
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
            </intent-filter>
        </receiver>

        <service android:name="com.ximalaya.ting.android.host.service.UpdateService" />

        <service android:name="com.ximalaya.ting.android.live.newxchat.service.ChatRoomService" />

        <receiver android:name=".host.manager.statistic.BatteryBroadcast">
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.sina.util.dnscache.net.networktype.NetworkStateReceiver"
            android:label="NetworkConnection">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="android.intent.action.USER_PRESENT" />
            </intent-filter>
        </receiver>

        <provider
            android:name=".host.contentProvider.DatabaseProvider"
            android:authorities="com.ximalaya.ting.android.contentProvider.DatabaseProvider" />

        <activity
            android:name="com.ximalaya.ting.android.host.activity.multidex.LoadResActivity"
            android:alwaysRetainTaskState="false"
            android:excludeFromRecents="true"
            android:launchMode="singleTask"
            android:process=":mini"
            android:screenOrientation="portrait" />
        <activity
            android:name="sdk.meizu.auth.ui.AuthActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/ActivityTheme"></activity>

        <!--微博SDK分享相关-->
        <activity
            android:name=".main.activity.share.WBShareActivity"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:theme="@style/ActivityTheme">
            <!--<intent-filter>
                <action android:name="com.sina.weibo.sdk.action.ACTION_SDK_REQ_ACTIVITY" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>-->
        </activity>
        <activity
            android:name="com.sina.weibo.sdk.component.WeiboSdkBrowser"
            android:configChanges="keyboardHidden|orientation"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"></activity>

        <activity
            android:name="com.ximalaya.ting.android.main.activity.account.CollectUserActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:label="@string/host_app_name"
            android:screenOrientation="portrait"
            android:windowFullscreen="true"
            android:windowNoTitle="true" />

        <!-- Sharing Files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.ximalaya.ting.lite.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">

            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/host_file_paths" />

        </provider>

        <!--百度定位SDK begin-->
        <service
            android:name="com.baidu.location.f"
            android:enabled="true"
            android:process=":remote">
            <intent-filter>
                <action android:name="com.baidu.location.service_v2.2"></action>
            </intent-filter>
        </service>
        <meta-data
            android:name="com.baidu.lbsapi.API_KEY"
            android:value="8CMXrHrxRXLg7tKlYtL0wVTEnkskzKEG" />  <!--http://lbsyun.baidu.com/apiconsole/key-->
        <!--百度定位SDK end-->

        <!--广点通sdk begin-->
        <service
            android:name="com.qq.e.comm.DownloadService"
            android:exported="false" />
        <activity
            android:name="com.qq.e.ads.ADActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize" />
        <!--广点通sdk end-->

        <!--免流多进程读取share-->
        <provider
            android:name="com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowReadSPContentProvider"
            android:authorities="com.ximalaya.ting.android.freeflowreadsp" />

        <!--多进程操作share-->
        <provider
            android:name="com.ximalaya.ting.android.host.contentProvider.MulitProcessOperatingSPContentProvider"
            android:authorities="com.ximalaya.ting.android.mulitprocessoperatingsp" />

        <!--userOneData 多进程操作share-->
        <provider
            android:name="com.ximalaya.ting.android.host.manager.statistic.UserOneDataOperatingSPContentProvider"
            android:authorities="com.ximalaya.ting.android.useronedata_readandwritesp"
            android:process=":player" />

        <!--我两是一块的，不要拆散我们-->
        <service android:name=".host.XiMaLaYaService" />
        <service android:name="com.ximalaya.ting.android.host.AssistService" />
        <!--我两是一块的，不要拆散我们-->

<!--        &lt;!&ndash;华为推送start&ndash;&gt;-->
<!--        &lt;!&ndash; HMS AppID &ndash;&gt;-->
<!--        <meta-data-->
<!--            android:name="com.huawei.hms.client.appid"-->
<!--            android:value="101934183" />-->

<!--        &lt;!&ndash; HMS 相关组件 &ndash;&gt;-->
<!--        <receiver android:name="com.xiaomi.assemble.control.HmsPushReceiver">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.huawei.android.push.intent.REGISTRATION" />-->
<!--                <action android:name="com.huawei.android.push.intent.RECEIVE" />-->
<!--                <action android:name="com.huawei.android.push.intent.CLICK" />-->
<!--                <action android:name="com.huawei.intent.action.PUSH_STATE" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->
<!--        <receiver android:name="com.huawei.hms.support.api.push.PushEventReceiver">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.huawei.intent.action.PUSH" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->

<!--        &lt;!&ndash;华为推送end&ndash;&gt;-->


        <!--阿里认证 start-->
        <activity
            android:name="com.alibaba.security.biometrics.face.auth.FaceLivenessActivity"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:taskAffinity="com.alibaba.security.biometrics.faceliveness" />
        <activity
            android:name="com.alibaba.security.rp.activity.RPTakePhotoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.alibaba.security.rp.activity.RPH5Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.alibaba.wireless.security.framework.SGProxyActivity"
            android:exported="false"
            android:label="@string/app_name">
            <intent-filter>
                <action android:name="com.wireless.security.open.framework.DLProxyActivity.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!--阿里认证 end-->


        <!--小能客服聊天 start-->
        <activity
            android:name="cn.xiaoneng.activity.ChatActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar" />
        <activity
            android:name="cn.xiaoneng.activity.LeaveMsgActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar" />
        <activity
            android:name="cn.xiaoneng.activity.ShowCameraActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|navigation|layoutDirection|touchscreen|locale|mnc|mcc|fontScale"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MyDialogTopRight"></activity>
        <activity
            android:name="cn.xiaoneng.activity.ShowAlbumActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent"></activity>
        <activity
            android:name="cn.xiaoneng.activity.ValuationActivity"
            android:screenOrientation="portrait"
            android:theme="@style/host_valuationdialog"></activity>
        <activity
            android:name="cn.xiaoneng.activity.ShowPictureActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar"></activity>
        <activity
            android:name="cn.xiaoneng.activity.XNExplorerActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar"></activity>
        <activity
            android:name="cn.xiaoneng.video.VideoPreviewActivity"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar"></activity>
        <activity
            android:name="cn.xiaoneng.video.RecordVideoActivity"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar"></activity>
        <activity
            android:name="cn.xiaoneng.video.XNVideoPlayer"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar"></activity>

        <service android:name="cn.xiaoneng.xpush.pushxiaoneng.XPushIMService"></service>
        <receiver
            android:name="com.ximalaya.ting.android.host.xiaoneng.XPushReceiverProxy"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.USER_PRESENT" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="android.net.wifi.STATE_CHANGE" />
                <action android:name="cn.xiaoneng.xpush.service" />
            </intent-filter>
        </receiver>
        <!--小能客服聊天 end-->

        <!--&lt;!&ndash;Glide配置begin &ndash;&gt;-->
        <!--<meta-data-->
        <!--android:name="com.ximalaya.ting.android.framework.manager.image.OkHttpGlideModule"-->
        <!--android:value="GlideModule" />-->
        <!--<meta-data-->
        <!--android:name="com.ximalaya.ting.android.framework.manager.image.GlideConfigModule"-->
        <!--android:value="GlideModule" />-->
        <!--&lt;!&ndash;Glide配置end &ndash;&gt;-->

        <!--钉钉分享activity-->
        <activity
            android:name="com.ximalaya.ting.android.host.manager.share.customsharetype.ddshare.DDShareActivity"
            android:screenOrientation="portrait">
            <!--<intent-filter>-->
            <!--<action android:name="com.sina.weibo.sdk.action.ACTION_SDK_REQ_ACTIVITY" />-->
            <!--<category android:name="android.intent.category.DEFAULT" />-->
            <!--</intent-filter>-->
        </activity>
        <activity-alias
            android:name="com.ximalaya.ting.android.ddshare.DDShareActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:targetActivity="com.ximalaya.ting.android.host.manager.share.customsharetype.ddshare.DDShareActivity">
            <!--<intent-filter>-->
            <!--<action android:name="com.sina.weibo.sdk.action.ACTION_SDK_REQ_ACTIVITY" />-->
            <!--<category android:name="android.intent.category.DEFAULT" />-->
            <!--</intent-filter>-->
        </activity-alias>

    </application>

</manifest>
