package com.ximalaya.ting.lite.main.login;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;

import androidx.fragment.app.FragmentActivity;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.login.LoginPageTraceManager;
import com.ximalaya.ting.android.host.manager.login.mobquick.MobQuickManager;
import com.ximalaya.ting.android.host.util.CountDownTimerFix;
import com.ximalaya.ting.android.host.util.common.TextFontManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.lang.ref.SoftReference;

/**
 * Created by qinhuifeng on 2019-12-21
 * <p>
 * 半屏和全屏通用登录view，包含登录整套流程
 * <p>
 * 在Fragment中使用必须调用
 * <p>
 * initUi()
 * onMyResume();
 * onPause();
 * onDestroy();
 * onBackPressed();
 *
 * <AUTHOR>
 */
public class LoginView extends RelativeLayout {

    //当前所展示的布局类型为手机输入布局
    public final static int TYPE_SHOW_LAYOUT_INPUT_PHONE = 0;
    //当前所展示的布局类型为验证码输入布局
    public final static int TYPE_SHOW_LAYOUT_INPUT_SMS = 1;
    //短信验证码位数
    private static final int SMS_CODE_LENGTH = 4;

    //登录相关的需要基于BaseLoginFragment，需要调用init方法，设置mBaseLoginFragment
    private BaseLoginFragment mBaseLoginFragment;
    private FragmentActivity mActivity;

    //输入手机号布局
    private ViewGroup mLayoutPhoneInput;
    //输入手机号码
    private EditText mPhoneInputEtNumber;
    //手机号码登录
    private TextView mPhoneInputTvLogin;
    //一键登录
    private LinearLayout mPhoneInputLlQuickLogin;

    //输入短信验证码布局
    private ViewGroup mLayoutSmsInput;
    private TextView mSmsInputTitleName;
    private TextView mSmsInputTitlePhone;
    private TextView[] mSmsInputTvSmsNumbers;
    private EditText mSmsInputEtSmsNumbers;
    //sms验证码时间倒计时
    private TextView mSmsInputTimerDown;
    //验证码重试
    private TextView mSmsInputSmsRetry;
    private ViewGroup mSmsInputSmsRetryParentLayout;
    private CountDownTimerFix mSmsTimer;

    //当前正在展示的布局
    private int mCurrentShowLayput = TYPE_SHOW_LAYOUT_INPUT_PHONE;
    //当时是否正在短信验证码倒计时
    private boolean mIsSmsCodeInputCountDownTiming = false;
    //登录来源，当前是全屏登录，还是半屏登录
    private Bundle mLoginParams;

    //布局选中回调
    private LoginViewShowLayoutCallback mShowLayoutCallback;

    //需要使用外部实现的接口
    private ILoginViewHandle mILoginViewHandle;
    private Typeface mDinTypeface;

    public LoginView(Context context) {
        super(context);
    }

    public LoginView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public LoginView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /******************下面为必须在Fragmet中进行调用的方法**********************/

    /**
     * 初始化ui，必须在Fragment initUi中进行调用
     */
    public void initUi(BaseLoginFragment baseLoginFragment, Bundle loginParams, ILoginViewHandle iLoginViewHandle) {
        //加载字体
        mDinTypeface = TextFontManager.getFontForDIN_Alternate_Bold();
        mBaseLoginFragment = baseLoginFragment;
        mActivity = mBaseLoginFragment.getActivity();
        this.mLoginParams = loginParams;
        this.mILoginViewHandle = iLoginViewHandle;
        initView();
    }

    /**
     * 页面可见，必须在Fragment onMyResume中进行调用
     */
    public void onMyResume() {

    }

    /**
     * 页面不可见，必须在Fragment onPause中调用
     */
    public void onPause() {
        hideSoftInput();
    }

    /**
     * 页面不销毁，必须在Fragment onDestroyView中进行调用
     */
    public void onDestroy() {
        stopSmsTimer();
        hideSoftInput();
    }

    /**
     * 拦截返回操作，必须在Fragment onBackPressed进行调用
     */
    public boolean onBackPressed() {
        if (!canUpdateUi()) {
            return false;
        }
        //当前处于验证码输入布局，并且正在倒计时，返回进行提示
        if (mCurrentShowLayput == TYPE_SHOW_LAYOUT_INPUT_SMS && mIsSmsCodeInputCountDownTiming) {
            showDialogSmsTimerBackWarning();
            return true;
        }
        //当前处于验证码输入布局，没有再倒计时，直接返回到手机号输入布局
        if (mCurrentShowLayput == TYPE_SHOW_LAYOUT_INPUT_SMS) {
            showPhoneInputLayout();
            return true;
        }
        return false;
    }

    /******************上面为必须在Fragmet中进行调用的方法**********************/

    private void initView() {
        View loginView = LayoutInflater.from(getContext()).inflate(R.layout.main_view_login_custom_view, null);
        addView(loginView);
        //初始化手机号输入布局
        initPhoneInputLayout();
        //初始化短信输入布局
        initSmsInputLayout();
        //show手机号输入布局
        showPhoneInputLayout();
    }

    /**
     * 初始化手机号输入布局
     */
    private void initPhoneInputLayout() {
        mLayoutPhoneInput = findViewById(R.id.main_layout_phone_input);
        mPhoneInputEtNumber = findViewById(R.id.main_phone_input_et_number);
        mPhoneInputTvLogin = findViewById(R.id.main_phone_input_tv_login);
        mPhoneInputLlQuickLogin = findViewById(R.id.main_phone_input_ll_quick_login);

        //设置din字体
        mPhoneInputEtNumber.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String phone = mPhoneInputEtNumber.getText().toString();
                //手机号不为空设置为可点击
                mPhoneInputTvLogin.setEnabled(!TextUtils.isEmpty(phone));
                //不为空的时候使用20
                if (TextUtils.isEmpty(phone)) {
                    mPhoneInputEtNumber.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                    //恢复为默认的字体
                    mPhoneInputEtNumber.setTypeface(null);
                } else {
                    mPhoneInputEtNumber.setTextSize(TypedValue.COMPLEX_UNIT_SP, 22);
                    //数字使用加粗字体
                    if (mDinTypeface != null) {
                        mPhoneInputEtNumber.setTypeface(mDinTypeface);
                    }
                }
            }
        });

        //登录按钮点击
        mPhoneInputTvLogin.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                if (!NetworkType.isConnectTONetWork(getContext())) {
                    CustomToast.showFailToast(R.string.main_network_exeption_toast);
                    return;
                }
                String phone = mPhoneInputEtNumber.getText().toString();
                if (TextUtils.isEmpty(phone)) {
                    CustomToast.showFailToast("手机号为空！");
                    return;
                }
                if (!StringUtil.verifyGlobalPhone(mBaseLoginFragment.mCountryCode, phone)) {
                    showDialogInputWarning("请输入正确的手机号码");
                    return;
                }
                //隐私协议是否选中
                if (!checkAgreementSelectedAndShowHint()) {
                    return;
                }
                //登录点击埋点，在校验之后
                LoginPageTraceManager.traceLoginBtnClick(false, LoginBundleParamsManager.getLoginBy(mLoginParams) == LoginByConstants.LOGIN_BY_FULL_SCREEN);
                //手机号本地验证通过，请求验证码
                SoftReference<BaseLoginFragment> softReference = new SoftReference<>(mBaseLoginFragment);
                mBaseLoginFragment.getPhoneCheckCode(phone, mBaseLoginFragment.mCountryCode, softReference, new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        //验证码请求成功，选中验证码输入页面
                        showSmsInputLayout();
                        //开始倒计时
                    }
                });
            }
        });

        //一键登录被点击
        mPhoneInputLlQuickLogin.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                //切换到一键登录，关闭当前登录的Activity
                if (mActivity != null && !(mActivity instanceof MainActivity)) {
                    mActivity.finish();
                }
                //切换到一键登录
                int loginBy = LoginBundleParamsManager.getLoginBy(mLoginParams);
                MobQuickManager.openMobQuickLoginPageWithDefErrorHandle(getContext(), loginBy, mLoginParams);
                //切换到一键登录埋点
                LoginPageTraceManager.traceLoginSelectMobQuickClick(loginBy == LoginByConstants.LOGIN_BY_FULL_SCREEN);
            }
        });
        AutoTraceHelper.bindData(mPhoneInputTvLogin, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(mPhoneInputLlQuickLogin, AutoTraceHelper.MODULE_DEFAULT, "");
    }

    /**
     * 选中手机号输入布局
     */
    private void showPhoneInputLayout() {
        //记录当前正在展示手机号输入布局
        mCurrentShowLayput = TYPE_SHOW_LAYOUT_INPUT_PHONE;
        mLayoutPhoneInput.setVisibility(View.VISIBLE);
        mLayoutSmsInput.setVisibility(View.GONE);
        //一键登录是否可以展示
        mPhoneInputLlQuickLogin.setVisibility(MobQuickManager.isQuickMobLoginSupport() ? View.VISIBLE : GONE);
        //回退或者展示手机号输入页面，重置短信验证码相关数据
        resetSmsInputLayout();
        //每次展示手机号输入布局，停止验证码倒计时
        stopSmsTimer();
        //输入手机号EditText
        mPhoneInputEtNumber.setFocusable(true);
        mPhoneInputEtNumber.setFocusableInTouchMode(true);
        mPhoneInputEtNumber.requestFocus();
        mBaseLoginFragment.doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                mPhoneInputEtNumber.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        //强制弹出键盘
                        showSoftInput(mPhoneInputEtNumber);
                    }
                }, 200);
            }
        });

        if (mShowLayoutCallback != null) {
            mShowLayoutCallback.showLayoutPage(TYPE_SHOW_LAYOUT_INPUT_PHONE);
        }
    }

    /**
     * 初始化短信输入布局
     */
    private void initSmsInputLayout() {
        mLayoutSmsInput = findViewById(R.id.main_layout_sms_input);
        mSmsInputTitleName = findViewById(R.id.main_sms_input_title_name);
        mSmsInputTitlePhone = findViewById(R.id.main_sms_input_title_phone);

        mSmsInputEtSmsNumbers = findViewById(R.id.main_sms_input_et_number);
        mSmsInputTimerDown = findViewById(R.id.main_sms_input_timer_down);
        mSmsInputSmsRetry = findViewById(R.id.main_sms_input_sms_retry);
        mSmsInputSmsRetryParentLayout = findViewById(R.id.main_sms_input_sms_retry_parent);
        mSmsInputTvSmsNumbers = new TextView[SMS_CODE_LENGTH];
        mSmsInputTvSmsNumbers[0] = findViewById(R.id.main_tv_number_1);
        mSmsInputTvSmsNumbers[1] = findViewById(R.id.main_tv_number_2);
        mSmsInputTvSmsNumbers[2] = findViewById(R.id.main_tv_number_3);
        mSmsInputTvSmsNumbers[3] = findViewById(R.id.main_tv_number_4);
        mSmsInputEtSmsNumbers.setText("");

        //设置din字体
        if (mDinTypeface != null) {
            mSmsInputTitlePhone.setTypeface(mDinTypeface);
            mSmsInputTvSmsNumbers[0].setTypeface(mDinTypeface);
            mSmsInputTvSmsNumbers[1].setTypeface(mDinTypeface);
            mSmsInputTvSmsNumbers[2].setTypeface(mDinTypeface);
            mSmsInputTvSmsNumbers[3].setTypeface(mDinTypeface);
        }

        mSmsInputEtSmsNumbers.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @SuppressLint("SetTextI18n")
            @Override
            public void afterTextChanged(Editable s) {
                final String smsCode = mSmsInputEtSmsNumbers.getText().toString();
                char[] charArray = smsCode.toCharArray();
                for (int i = 0; i < mSmsInputTvSmsNumbers.length; i++) {
                    if (i > charArray.length - 1) {
                        mSmsInputTvSmsNumbers[i].setText("");
                    } else {
                        //注意不可以去掉强转的 ""
                        //只setText(charArray[i])实际调用的是setText(@StringRes int resid)方法
                        mSmsInputTvSmsNumbers[i].setText(charArray[i] + "");
                    }
                }
                if (s.length() == SMS_CODE_LENGTH) {
                    //触发完成操作,太快用户看不到最后一个输入，延时200毫秒，让用户可以看到最后一个输入
                    mSmsInputEtSmsNumbers.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (!canUpdateUi()) {
                                return;
                            }
                            //发起登录
                            if (!NetworkType.isConnectTONetWork(getContext())) {
                                CustomToast.showFailToast(R.string.main_network_exeption_toast);
                                return;
                            }
                            String phone = mPhoneInputEtNumber.getText().toString();
                            mBaseLoginFragment.doLoginWithoutPwd(phone, smsCode);
                        }
                    }, 200);
                }
            }
        });

        mSmsInputSmsRetryParentLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                if (!NetworkType.isConnectTONetWork(getContext())) {
                    CustomToast.showFailToast(R.string.main_network_exeption_toast);
                    return;
                }

                boolean isFullLogin = LoginBundleParamsManager.getLoginBy(mLoginParams) == LoginByConstants.LOGIN_BY_FULL_SCREEN;
                new XMTraceApi.Trace()
                        .click(11512)
                        .put("currPage", "logIn")
                        .put("item", "重新获取验证码")
                        .put("oneKeyLogin", "false")
                        .put("screenType", isFullLogin ? "fullScreen" : "halfScreen")
                        .createTrace();

                //验证码重试
                //手机号本地验证通过，请求验证码
                String phone = mPhoneInputEtNumber.getText().toString();
                SoftReference<BaseLoginFragment> softReference = new SoftReference<>(mBaseLoginFragment);
                mBaseLoginFragment.getPhoneCheckCode(phone, mBaseLoginFragment.mCountryCode, softReference, new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        //验证码请求成功，重新选中验证码输入页面
                        showSmsInputLayout();
                    }
                });
            }
        });
    }

    /**
     * 重置短信验证码布局，清空相关数据
     */
    private void resetSmsInputLayout() {
        mSmsInputTitleName.setText("验证码已发送至：");
        mSmsInputTitlePhone.setText("");
        mSmsInputEtSmsNumbers.setText("");
        mSmsInputSmsRetryParentLayout.setVisibility(View.GONE);
        mSmsInputTimerDown.setVisibility(VISIBLE);
        mSmsInputTimerDown.setText("");
    }

    /**
     * 选中短信输入布局
     */
    private void showSmsInputLayout() {
        //记录当前正在展示验证码输入布局
        mCurrentShowLayput = TYPE_SHOW_LAYOUT_INPUT_SMS;
        mLayoutPhoneInput.setVisibility(View.GONE);
        mLayoutSmsInput.setVisibility(View.VISIBLE);
        //重置短信验证码相关数据
        resetSmsInputLayout();
        String phone = mPhoneInputEtNumber.getText().toString();
        mSmsInputTitleName.setText("验证码已发送至：");
        mSmsInputTitlePhone.setText(phone);
        //启动短信倒计时
        startSmsTimer();

        //短信验证码EditText请求焦点，弹出输入框
        mSmsInputEtSmsNumbers.setFocusable(true);
        mSmsInputEtSmsNumbers.setFocusableInTouchMode(true);
        mSmsInputEtSmsNumbers.requestFocus();
        //不需要在增加doAfterAnimation判断
        mBaseLoginFragment.doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                mSmsInputEtSmsNumbers.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        //强制弹出键盘
                        showSoftInput(mSmsInputEtSmsNumbers);
                    }
                }, 200);
            }
        });

        if (mShowLayoutCallback != null) {
            mShowLayoutCallback.showLayoutPage(TYPE_SHOW_LAYOUT_INPUT_SMS);
        }
    }

    /**
     * 开始短信验证码倒计时timer
     */
    private void startSmsTimer() {
        if (mSmsTimer == null) {
            //倒计时60s
            mSmsTimer = new CountDownTimerFix(60000, 1000) {
                @SuppressLint("SetTextI18n")
                @Override
                public void onTick(long millisUntilFinished) {
                    long time = millisUntilFinished / 1000;
                    if (mSmsInputTimerDown != null) {
                        mSmsInputTimerDown.setText(time + "s 后再次发送");
                    }
                }

                @Override
                public void onFinish() {
                    mIsSmsCodeInputCountDownTiming = false;
                    //倒计时结束设置可以进行重置
                    if (mSmsInputTimerDown != null) {
                        mSmsInputTimerDown.setText("");
                    }
                    if (mSmsInputSmsRetryParentLayout != null) {
                        mSmsInputSmsRetryParentLayout.setVisibility(View.VISIBLE);
                    }
                }
            };
        }
        stopSmsTimer();
        mIsSmsCodeInputCountDownTiming = true;
        mSmsTimer.start();
    }

    /**
     * 停止短信验证码倒计时timer
     */
    private void stopSmsTimer() {
        if (mSmsTimer != null) {
            mSmsTimer.cancel();
        }
        mIsSmsCodeInputCountDownTiming = false;
    }

    /**
     * 输入错误提示
     */
    public void showDialogInputWarning(String msg) {
        if (mActivity != null && !mActivity.isFinishing()) {
            new DialogBuilder(mActivity).setMessage(msg).showWarning();
        } else {
            CustomToast.showFailToast(msg);
        }
    }


    /**
     * 短信正在倒计时返回进行提示
     */
    public void showDialogSmsTimerBackWarning() {
        DialogBuilder dialogBuilder = new DialogBuilder(mActivity)
                .setMessage("验证码短信可能略有延迟，要再等等吗？")
                .setOkBtn("再等等")
                .setcancelApplyToButton(false)
                .setCancelBtn("不等了", new DialogBuilder.DialogCallback() {
                    @Override
                    public void onExecute() {
                        //不等了，返回到手机输入布局，短信关闭到倒计时在showPhoneInputLayout中调用
                        showPhoneInputLayout();
                    }
                });
        dialogBuilder.showConfirm();
    }

    /**
     * 弹出键盘
     */
    private void showSoftInput(EditText editText) {
        if (mActivity == null) {
            return;
        }
        InputMethodManager inputMethodManager = (InputMethodManager) mActivity.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager == null) {
            return;
        }
        inputMethodManager.showSoftInput(editText, 0);
    }

    /**
     * 隐藏键盘
     */
    private void hideSoftInput() {
        if (mActivity == null) {
            return;
        }
        InputMethodManager inputMethodManager = (InputMethodManager) mActivity.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager == null) {
            return;
        }
        inputMethodManager.hideSoftInputFromWindow(getWindowToken(), 0);
    }

    /**
     * 异步后释放可以更新ui
     */
    private boolean canUpdateUi() {
        return mActivity != null && mBaseLoginFragment != null && mBaseLoginFragment.canUpdateUi();
    }

    public void setShowLayoutListener(LoginViewShowLayoutCallback callback) {
        mShowLayoutCallback = callback;
    }

    /**
     * 检查隐私协议是否已经选中的
     */
    private boolean checkAgreementSelectedAndShowHint() {
        //没有传入的时候，默认可以进行登录，返回true
        if (mILoginViewHandle == null) {
            return true;
        }
        return mILoginViewHandle.checkAgreementSelectedAndShowHint();
    }
}
