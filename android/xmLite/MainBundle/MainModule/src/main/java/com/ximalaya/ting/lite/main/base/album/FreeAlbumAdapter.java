package com.ximalaya.ting.lite.main.base.album;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.os.Bundle;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.ExpandTagResult;
import com.ximalaya.ting.android.host.model.album.TagResult;
import com.ximalaya.ting.android.host.model.search.SearchMetadata;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.home.fragment.PlayPageTagCategoryMetadataFragment;
import com.ximalaya.ting.lite.main.utils.ViewFactory;

import org.w3c.dom.Text;

import java.util.ArrayList;
import java.util.List;

public class FreeAlbumAdapter extends BaseAlbumAdapter {

    private Context mContext;

    public FreeAlbumAdapter(Context context, List<Album> listData) {
        super(context, listData);
        mContext = context;
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_free_album_home_lite;
    }

    /**
     * 建立视图Holder
     */
    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void onClick(View view, final Album t, final int position, BaseViewHolder holder) {

    }

    @Override
    protected void hideAllViews(BaseAlbumAdapter.ViewHolder viewHolder) {
        super.hideAllViews(viewHolder);
        ViewHolder holder = (ViewHolder) viewHolder;
        holder.offSale.setVisibility(View.INVISIBLE);
    }

    @Override
    public void bindViewDatas(final BaseViewHolder holder, final Album o, final int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        hideAllViews((ViewHolder) holder);
        if (!(o instanceof AlbumM)) {
            return;
        }
        final AlbumM albumM = (AlbumM) o;
        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, albumM);
        //添加专辑Item的ContentDescription
        if (viewHolder.root != null) {
            if (!TextUtils.isEmpty(albumM.getAlbumTitle())) {
                viewHolder.root.setContentDescription(albumM.getAlbumTitle());
            } else {
                viewHolder.root.setContentDescription("");
            }
        }
        if (AlbumTagUtil.getAlbumCoverTag((AlbumM) o) != -1) {
            viewHolder.ivTag.setImageResource(AlbumTagUtil.getAlbumCoverTag((AlbumM) o));
            viewHolder.ivTag.setVisibility(View.VISIBLE);
        } else {
            viewHolder.ivTag.setVisibility(View.INVISIBLE);
        }

        ImageManager.from(context).displayImage(viewHolder.cover, o.getLargeCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145, com.ximalaya.ting.android.host.R.drawable.host_default_album_145);
        int textSize = (int) viewHolder.title.getTextSize();
        Spanned richTitle = getRichTitle(o, context, textSize);
        viewHolder.title.setText(richTitle);

        if (albumM.getPrice() != -1 && albumM.getPrice() != 0) {
            viewHolder.tvPrice.setVisibility(View.VISIBLE);
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("¥").append(StringUtil.convertDouble(albumM.getPrice(), 2));
            viewHolder.tvPrice.setText(stringBuilder.toString());
            viewHolder.tvPrice.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG | Paint.ANTI_ALIAS_FLAG);
        } else {
            viewHolder.tvPrice.setVisibility(View.INVISIBLE);
        }
        List<ExpandTagResult> tags = getUpTags(albumM);
        if (ToolUtil.isEmptyCollects(tags) || ChildProtectManager.isChildProtectOpen(mContext)) {
            viewHolder.mLlAllTags.setVisibility(View.INVISIBLE);
        } else {
            viewHolder.mLlAllTags.setVisibility(View.VISIBLE);
            if (tags.size() >= 2) {
                viewHolder.mTvTag2.setVisibility(View.VISIBLE);
                viewHolder.mTvTag2.setText(tags.get(1).getTagName());
                viewHolder.mTvTag2.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        jumpToSelectedCategoryContentPage(tags.get(1));
                    }
                });
            }
            viewHolder.mTvTag1.setText(tags.get(0).getTagName());
            viewHolder.mTvTag1.setVisibility(View.VISIBLE);
            viewHolder.mTvTag1.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    jumpToSelectedCategoryContentPage(tags.get(0));
                }
            });
        }
    }


    /**
     * 加载标签模块
     */
    private List<ExpandTagResult> getUpTags(AlbumM object) {
        if (object == null) {
            return null;
        }
        List<TagResult> tagResults;
        // 只选取前两个标签
        final List<ExpandTagResult> tags = new ArrayList<>();
        if (object.getTagResults() != null) {
            tagResults = object.getTagResults();
            for (TagResult tagResult : tagResults) {
                int tagId = tagResult.getTagId();
                List<SearchMetadata> metadataList = tagResult.getMetadataList();
                int metadataId = -1;
                if (!ToolUtil.isEmptyCollects(metadataList)) {
                    for (SearchMetadata searchMetadata : metadataList) {
                        if (searchMetadata.getMetadataValueId() == tagId) {
                            metadataId = searchMetadata.getMetadataId();
                            break;
                        }
                    }
                }
                ExpandTagResult expandTagResult = new ExpandTagResult(tagResult.getTagName(), tagResult.getTagId(), metadataId);
                expandTagResult.setCategoryTag(tagResult.isCategoryTag());
                tags.add(expandTagResult);
            }
        }
        return tags;
    }

    private void jumpToSelectedCategoryContentPage(ExpandTagResult expandTagResult) {
        if (expandTagResult == null) {
            return;
        }
        //跳转到分类页面
        PlayPageTagCategoryMetadataFragment tabFragment = new PlayPageTagCategoryMetadataFragment();
        int type;
        if (expandTagResult.isCategoryTag()) {
            type = 1;
        } else {
            type = 2;
        }
        int tagId = expandTagResult.getTagId();
        Bundle bundle = PlayPageTagCategoryMetadataFragment.createArguments(expandTagResult.getTagName(), type, tagId, tagId);
        tabFragment.setArguments(bundle);
        startFragment(tabFragment);
    }

    @Nullable
    public static Spanned getRichTitle(Album data, Context context, int maxHeight) {
        if (!(data instanceof AlbumM)) {
            return null;
        }
        if (context == null) {
            return null;
        }
        AlbumM album = (AlbumM) data;
        boolean isCompleteTag = album.getSerialState() == 2 || album.isCompleted() || (album.getAttentionModel() != null && album.getAttentionModel().getSerialState() == 2);
        List<Integer> resList = new ArrayList<>();
        if (isCompleteTag) {
            // 完本标签
            resList.add(R.drawable.main_tag_complete_top_new);
        }
        String intro;
        if (resList.size() > 0) {
            intro = " " + data.getAlbumTitle();
        } else {
            intro = data.getAlbumTitle();
        }
        Spanned titleWithTag = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(context, intro, resList, resList.size(), maxHeight);
        return titleWithTag;
    }

    public static class ViewHolder extends BaseAlbumAdapter.ViewHolder {
        private ImageView offSale; //下架icon
        private ImageView ivTag;
        private TextView tvAuthor;
        private TextView tvPrice;
        private LinearLayout mLlAllTags;
        private TextView mTvTag1;
        private TextView mTvTag2;


        public ViewHolder(View convertView) {
            super(convertView);
            cover = convertView.findViewById(R.id.main_iv_album_cover);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
            border = convertView.findViewById(R.id.main_album_border);
            title = convertView.findViewById(R.id.main_tv_album_title);
            tvAuthor = convertView.findViewById(R.id.main_tv_album_author);
            tvPrice = convertView.findViewById(R.id.main_tv_album_price);
            layoutAlbumInfo = convertView.findViewById(R.id.main_layout_album_info);
            offSale = convertView.findViewById(R.id.main_iv_off_sale);
            mLlAllTags = convertView.findViewById(R.id.main_ll_free_album_tags);
            mTvTag1 = convertView.findViewById(R.id.main_tv_free_tag_1);
            mTvTag2 = convertView.findViewById(R.id.main_tv_free_tag_2);

        }
    }
}
