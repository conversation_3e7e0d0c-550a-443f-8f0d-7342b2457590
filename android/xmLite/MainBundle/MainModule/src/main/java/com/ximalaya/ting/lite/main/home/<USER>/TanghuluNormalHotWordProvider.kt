package com.ximalaya.ting.lite.main.home.adapter

import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder
import com.ximalaya.ting.lite.main.home.fragment.HomeCategoryContentTabFragment
import com.ximalaya.ting.lite.main.home.fragment.NewContentPoolListFragment
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel
import com.ximalaya.ting.lite.main.manager.ITingHandler
import com.ximalaya.ting.lite.main.model.album.TanghuluHotWord
import kotlinx.android.synthetic.main.main_item_vip_hot_word.view.*

/**
 * Created by dumingwei on 2020/5/7.
 *
 * Desc: 糖葫芦分类热词 cardClass = normal
 */
class TanghuluNormalHotWordProvider @JvmOverloads constructor(
        val mFragment: BaseFragment2,
        val mExtraViewModel: HomeRecommendExtraViewModel? = null
) : IMulitViewTypeViewAndData<TanghuluNormalHotWordProvider.Holder, ArrayList<TanghuluHotWord>> {

    private val TAG: String = "TanghuluNormalHotWordPr"

    private val SPAN_COUNT = 4

    private val mContext: Context? = mFragment.context

    private val iTingHandler: ITingHandler = ITingHandler()

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_vip_hot_word, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView)
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<ArrayList<TanghuluHotWord>>, convertView: View, position: Int) {
        val hotWordList = t.getObject()
        Logger.d(TAG, "bindViewDatas" + hotWordList.hashCode())
        val adapter = holder.rootView.rvAdapterVipHotWord.adapter
        if (adapter == null) {
            initAdapter(holder.rootView.rvAdapterVipHotWord, hotWordList)
        } else {
            if (adapter is TanghuluHotWordAdapter) {
                adapter.setValueList(hotWordList)
                adapter.notifyDataSetChanged()
            }
        }
    }

    private fun initAdapter(rv: RecyclerView, list: MutableList<TanghuluHotWord>) {
        if (rv.adapter == null) {
            rv.layoutManager = GridLayoutManager(mContext, SPAN_COUNT)
            mContext?.let {
                rv.adapter = object : MultiRecyclerAdapter<TanghuluHotWord, SuperRecyclerHolder>(mContext, list) {

                    override fun createMultiViewHolder(mCtx: Context?, itemView: View, viewType: Int): SuperRecyclerHolder {
                        return SuperRecyclerHolder.createViewHolder(mCtx, itemView)
                    }

                    override fun onBindMultiViewHolder(holder: SuperRecyclerHolder, t: TanghuluHotWord, viewType: Int, position: Int) {
                        holder.setBackgroundResource(R.id.rlHotWord, R.drawable.main_bg_dddddd_stroke_dp18)
                        holder.setTextColorResource(R.id.tvHotWordTitle, R.color.main_color_333333)

                        val itemBean: TanghuluHotWord.ItemBean? = t.item
                        val keywordName = itemBean?.name ?: ""

                        if (TanghuluHotWord.NAME_ALL == keywordName) {
                            holder.setVisibility(R.id.ivAllHotWord, View.VISIBLE)
                        } else {
                            holder.setVisibility(R.id.ivAllHotWord, View.GONE)
                        }
                        holder.setText(R.id.tvHotWordTitle, keywordName)
                        holder.setOnItemClickListenner {
                            t.item?.let { item ->
                                doClickByType(t.itemType, item)
                            }

                        }
                    }

                    override fun getMultiItemViewType(model: TanghuluHotWord?, position: Int): Int {
                        return 0
                    }

                    override fun getMultiItemLayoutId(viewType: Int): Int {
                        return R.layout.main_item_hot_word_adapter_item
                    }
                }
            }
        }
    }

    private fun doClickByType(itemType: String, t: TanghuluHotWord.ItemBean) {
        when (itemType) {
            TanghuluHotWord.ITEM_H5 -> {
                t.link?.let {
                    val bundle = Bundle()
                    bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, it)
                    mFragment.startFragment(NativeHybridFragment::class.java, bundle)
                }
            }
            TanghuluHotWord.ITEM_UTING -> {
                t.link?.let {
                    iTingHandler.handleITing(mFragment.activity, Uri.parse(it))
                }
            }
            TanghuluHotWord.ITEM_POOL -> {
                val contentPoolListFragment = NewContentPoolListFragment.newInstance(t.name, t.poolId,
                        mExtraViewModel?.from ?: 0)
                mFragment.startFragment(contentPoolListFragment)
            }
            TanghuluHotWord.ITEM_KEY_WORD -> {
                val categoryId = t.categoryId
                val name = t.name
                val keywordId = t.keywordId
                mExtraViewModel?.let {
                    val fragment = HomeCategoryContentTabFragment()
                    val argumentDef: Bundle = if (keywordId > 0) {
                        HomeCategoryContentTabFragment.createArguments(categoryId, keywordId,
                                mExtraViewModel.from, true)
                    } else {
                        HomeCategoryContentTabFragment.createArguments(categoryId, name,
                                mExtraViewModel.from, true)
                    }
                    fragment.arguments = argumentDef
                    mFragment.startFragment(fragment)
                }
            }
            TanghuluHotWord.ITEM_ALL -> {
                mExtraViewModel?.let {
                    //全部按钮被点击了，单独处理
                    val fragment = HomeCategoryContentTabFragment()
                    val argumentDef = HomeCategoryContentTabFragment.createArguments(mExtraViewModel.categoryId,
                            mExtraViewModel.from, true)
                    fragment.arguments = argumentDef
                    mFragment.startFragment(fragment)
                }
            }
        }
    }

    class Holder(var rootView: View) : BaseViewHolder()

}