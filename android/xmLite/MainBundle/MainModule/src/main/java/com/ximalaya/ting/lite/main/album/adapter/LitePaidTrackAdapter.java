package com.ximalaya.ting.lite.main.album.adapter;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;

import com.airbnb.lottie.LottieComposition;
import com.airbnb.lottie.LottieDrawable;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter;
import com.ximalaya.ting.android.host.business.unlock.callback.FullFreeTrackDownloadUnlockCallBack;
import com.ximalaya.ting.android.host.business.unlock.dialog.LiteVipBenefitDialog;
import com.ximalaya.ting.android.host.business.unlock.manager.VipTrackUnLockPaidManager;
import com.ximalaya.ting.android.host.business.unlock.model.AlbumPaidUnLockHintInfo;
import com.ximalaya.ting.android.host.business.unlock.view.UnlockPaidCountDownTextView;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.track.TraceParamsInTrack;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.album.contract.AlbumFragmentNewListContact;
import com.ximalaya.ting.lite.main.album.dialog.FreeUnlockBottomDialog;
import com.ximalaya.ting.lite.main.album.fragment.LiteAlbumFragmentNewList;
import com.ximalaya.ting.lite.main.download.AbstractTrackAdapterInMain;
import com.ximalaya.ting.lite.main.model.album.TraceWrapForTrack;
import com.ximalaya.ting.lite.main.play.manager.FreeAlbumDownloadTimesManager;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by dumingwei on 2021/5/31
 * <p>
 * Desc:
 */
public class LitePaidTrackAdapter extends AbstractTrackAdapterInMain {

    private static final String TAG = "LitePaidTrackAdapter";

    private int brightPosition = Integer.MIN_VALUE; // 需要高亮的位置 不用check播放状态 ,当切换声音的时候在做check
    private final int dp10;
    private final int dp20;

    //AlbumFragmentNewList的接口类
    private AlbumFragmentNewListContact.PageView mPageView;

    private final Activity mActivity;

    public LitePaidTrackAdapter(Activity activity, Fragment fragment, List<Track> listData, AlbumFragmentNewListContact.PageView pageView) {
        super(activity, listData);
        mPageView = pageView;
        mActivity = activity;
        dp10 = BaseUtil.dp2px(context, 10);
        dp20 = BaseUtil.dp2px(context, 20);
    }

    public void setAlbumFragmentNewList(AlbumFragmentNewListContact.PageView mPageView) {
        this.mPageView = mPageView;
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_lite_track;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }
    @Override
    public void bindViewDatas(BaseViewHolder h, Track track, int position) {
        super.bindViewDatas(h, track, position);

        if (!(track instanceof TrackM)) {
            return;
        }

        new XMTraceApi.Trace()
                .setMetaId(5130)
                .setServiceId("slipPage")
                .put("albumId", albumM != null ? String.valueOf(albumM.getId()) : "")
                .put("trackId", String.valueOf(track.getDataId()))
                .createTrace();

        ViewHolder holder = (ViewHolder) h;
        holder.download.setVisibility(View.VISIBLE);
        holder.download.setEnabled(true);
        //是否是新声音标志
        if (hasNewSoundFlag && !isAlbumTagsSingleLine) {
            holder.ivAlbumNewTag.setVisibility(((TrackM) track).isNewTrack() ? View.VISIBLE : View.GONE);
        } else {
            holder.ivAlbumNewTag.setVisibility(View.GONE);
        }
        //设置标题
        List<Integer> resIds = new ArrayList<>();
        holder.title.setText(ToolUtil.getTrackTitleWithPicAheadCenterAlign(holder.title.getContext(), track.getTrackTitle(), resIds, 2));

        if (albumM != null) {
            RelativeLayout.LayoutParams layoutParams = ((RelativeLayout.LayoutParams) holder.download.getLayoutParams());
            RelativeLayout.LayoutParams llNameLp = ((RelativeLayout.LayoutParams) holder.llNameGroup.getLayoutParams());
            //已完结
            if (albumM.getIsFinished() == 2) {
                holder.updateAt.setVisibility(View.INVISIBLE);
                //改变文字的右边距
                llNameLp.rightMargin = dp10;
                //下载按钮居中显示
                layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                layoutParams.addRule(RelativeLayout.CENTER_VERTICAL);
            } else {
                holder.updateAt.setVisibility(View.VISIBLE);
                //改变文字的右边距
                llNameLp.rightMargin = dp20;
                //下载按钮靠下显示
                layoutParams.removeRule(RelativeLayout.CENTER_VERTICAL);
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
            }
            holder.llNameGroup.setLayoutParams(llNameLp);
            holder.download.setLayoutParams(layoutParams);
        }
        int lastPos = XmPlayerManager.getInstance(context).getHistoryPos(track.getDataId());
        String ps = ToolUtil.getPlaySchedule(lastPos, track.getDuration());
        if (!TextUtils.isEmpty(ps)) {
            holder.playSchedule.setVisibility(View.VISIBLE);
            holder.playSchedule.setText(ps);
            holder.playSchedule.setTextColor(0xFF999999);
            holder.title.setTextColor(0xFF999999);
        } else {
            holder.title.setTextColor(context.getResources().getInteger(R.integer.main_color_111111));
            holder.playSchedule.setVisibility(View.GONE);
        }

        //展示索引
        showTrackNo(holder, track.getDataId(), ((TrackM) track).getOrderNo(), position);

        // 免费专辑 会员抢先听 则显示标签
        if (albumM != null && !albumM.isVipFree() && albumM.getVipFreeType() != 1 && !albumM.isPaid() && track.vipPriorListenStatus == 1) {
            holder.ivVipFirstListenTag.setVisibility(View.VISIBLE);
        } else {
            holder.ivVipFirstListenTag.setVisibility(View.GONE);
        }

        holder.download.clearAnimation();

        //展示下载按钮,AI朗读声音不展示下载按钮
        if (track.isTTsTrack() || (albumM != null && albumM.isTTsAlbum())) {
            holder.download.setVisibility(View.INVISIBLE);
        } else {
            holder.download.setVisibility(View.VISIBLE);
        }

        holder.download.setImageResource(R.drawable.host_album_download);
        //holder.download.setPadding(px_5, px_5, px_5, px_5);
        AlbumEventManage.setAlbumSoundDownloadStatus(context, holder.download, RouteServiceUtil.getDownloadService().getDownloadStatus(track), false);
        bindDownloadData(holder, track, "下载");

        boolean canPlayTrack = UnlockListenTimeManagerNew.INSTANCE.checkCanPlayPermissionSync(track, albumM);

        //处理可以解锁相关
        boolean isShowUnlockTips = dealVipPaidUnLock(holder, track, canPlayTrack);
        //处理vip标签是否展示
        dealWithVipTagShow(holder, track, isShowUnlockTips);
        //处理声音有效期倒计时
        dealTrackExpireTime(holder, track);
        //可视化埋点问题fix
        bindItemData(holder, track, isShowUnlockTips);

        if (holder.mIvFreeListenerVipTag != null) {
            if (UnlockListenTimeManagerNew.INSTANCE.isNeedListenTimeTrackSync(track)) {
                holder.mIvFreeListenerVipTag.setVisibility(View.VISIBLE);
            } else {
                holder.mIvFreeListenerVipTag.setVisibility(View.GONE);
            }
        }
    }

    private void dealWithVipTagShow(ViewHolder holder, Track track, boolean isShowUnlockTips) {
        if (track == null || holder == null) {
            return;
        }
        // 解锁图标显示  则不显示vip标记(在限免专辑里面有vip声音,可以免费听,但是极速版后台锁定该声音导致重复显示)
        if (!isVipAlbum() || isShowUnlockTips || UnlockListenTimeManagerNew.INSTANCE.isAllowUnlockVipAlbum()) {
            holder.ivVipTag.setVisibility(View.GONE);
            return;
        }
        if (track.isFree()) {
            holder.ivVipTag.setVisibility(View.GONE);
            return;
        }
        //当前非入包声音
        if (mPageView.getAlbumPaidUnLockHintInfo() == null) {
            holder.ivVipTag.setVisibility(View.VISIBLE);
            return;
        }
        //其他不展示
        holder.ivVipTag.setVisibility(View.GONE);
    }

    /**
     * 解决可视化埋点问题
     * <p>
     * 无法区分下载按钮状态
     * <p>
     * description 禁止随意修改
     */
    private void bindDownloadData(ViewHolder holder, Track track, String description) {
        TraceWrapForTrack traceWrapForTrack = new TraceWrapForTrack();
        traceWrapForTrack.track = track;
        traceWrapForTrack.description = description;
        AutoTraceHelper.bindData(holder.download, AutoTraceHelper.MODULE_DEFAULT, traceWrapForTrack);
    }

    /**
     * 解决可视化埋点问题
     */
    private void bindItemData(ViewHolder holder, Track track, boolean isUnlockTips) {
        if (!(track instanceof TrackM)) {
            return;
        }
        TrackM trackM = (TrackM) track;
        TraceParamsInTrack paramsInTrack = trackM.traceParamsInTrack;
        if (paramsInTrack == null) {
            paramsInTrack = new TraceParamsInTrack();
        }
        if (holder.vipUnlockShowUnlockRes.getVisibility() == View.VISIBLE) {
            paramsInTrack.traceVipUnlockStatus = isUnlockTips ? 1 : 2;
        } else {
            paramsInTrack.traceVipUnlockStatus = 0;
        }
        AlbumPaidUnLockHintInfo albumPaidUnLockHintInfo = mPageView.getAlbumPaidUnLockHintInfo();
        //前置条件是入包专辑。已开通VIP不报，免费专辑不报
        //可以解锁专辑或者已经解锁的专辑进行上报
        if (isTrackCanVipUnlock(track) || trackM.isAdUnlockAuthoried()) {
            //当前是解锁专辑，上报a，b方案
            paramsInTrack.vipUnlockAbTestPlan = "a";
            //可以解锁专辑或者已经解锁的专辑进行上报为true
            paramsInTrack.isVipUnlockTrack = true;
            //专辑达到上限或者今日达到上限进行上报
            if (albumPaidUnLockHintInfo != null) {
                paramsInTrack.isVipUnlockMaxLimit = albumPaidUnLockHintInfo.isUnlockAlbumLimit() || albumPaidUnLockHintInfo.isUnlockMaxLimit();
            } else {
                paramsInTrack.isVipUnlockMaxLimit = false;
            }
            paramsInTrack.payUnlockPlanVIP = "0";
        } else {
            paramsInTrack.vipUnlockAbTestPlan = "";
            paramsInTrack.isVipUnlockTrack = false;
            paramsInTrack.isVipUnlockMaxLimit = false;
            paramsInTrack.payUnlockPlanVIP = "";
        }
        trackM.traceParamsInTrack = paramsInTrack;
    }

    private boolean isVipAlbum() {
        if (mPageView == null) {
            return false;
        }
        Album album = mPageView.getAlbum();
        if (!(album instanceof AlbumM)) {
            return false;
        }
        return ((AlbumM) album).isVipAlbum();
    }

    /**
     * 处理解锁声音有效期倒计时
     */
    private void dealTrackExpireTime(ViewHolder holder, Track track) {
        if (UnlockListenTimeManagerNew.INSTANCE.isAllowUnlockVipAlbum()) {
            holder.adHintTipCountDown.setVisibility(View.GONE);
            holder.adHintTipCountDown.onPause();
            return;
        }
        holder.adHintTipCountDown.setVisibility(View.GONE);
        holder.adHintTipCountDown.onPause();
        //如果已经是vip，不展示
        //如果是体验vip也会返回ExpireTime字段，此处会出现异常的倒计时
        //未登录不能展示
        if (!UserInfoMannage.hasLogined() || UserInfoMannage.isVipUser()) {
            return;
        }
        //只有来自解锁权限的才展示倒计时，展示倒计时
        if (track.isAdUnlockAuthoried() && track.getExpireTime() > 0 && track.getExpireTime() - System.currentTimeMillis() > 0) {
            holder.adHintTipCountDown.setVisibility(View.VISIBLE);
            holder.adHintTipCountDown.setExpireTime(track.getExpireTime(), "解锁中", () -> {
                if (holder.adHintTipCountDown == null) {
                    return;
                }
                if (track == null) {
                    return;
                }
                if (ViewCompat.isAttachedToWindow(holder.adHintTipCountDown)) {
                    //时间到了，更新当前声音的权限状态
                    track.setExpireTime(0);
                    track.setAuthorized(false);
                    track.setAdUnlockAuthoried(false);
                    notifyDataSetChanged();
                }
            });
        }
    }

    /**
     * 处理付费声音解锁相关
     */
    private boolean dealVipPaidUnLock(ViewHolder holder, Track track, boolean canPlayTrack) {
        //检测付费解锁是否失效了
        //校验操作必须放在解锁逻辑最早的地方进行检测，禁止修改位置
        if (track.isAdUnlockAuthoried()) {
            //当前是adUnlockAuthoried为true，但是又不存在解锁的时间，认为是退出到后台等情况，付费解锁已经失效
            //推出到后台倒计时会cancel掉，已经失效了，重新切换到前台后解锁状态发生错误，此处做校验修正
            if (track.getExpireTime() - System.currentTimeMillis() <= 0) {
                track.setAuthorized(false);
                track.setAdUnlockAuthoried(false);
                track.setExpireTime(0);
            }
        }
        if (UnlockListenTimeManagerNew.INSTANCE.isAllowUnlockVipAlbum()) {
            holder.vipUnlockShowUnlockRes.setVisibility(View.GONE);
            return false;
        }
        //先隐藏解锁图标
        holder.vipUnlockShowUnlockRes.setVisibility(View.GONE);
        //是否是可以解锁的声音
        if (!isTrackCanVipUnlock(track)) {
            holder.vipUnlockShowUnlockRes.setVisibility(View.GONE);
            return false;
        }
        //可以解锁
        holder.vipUnlockShowUnlockRes.setVisibility(View.VISIBLE);
        return true;
    }

    public boolean isTrackCanVipUnlock(Track track) {
        if (track == null || mPageView == null) {
            return false;
        }
        //已登录并且是vip用户不处理解锁
        if (UserInfoMannage.isVipUser()) {
            return false;
        }
        //已经有权限了，不需要解锁
        if (track.isAuthorized()) {
            return false;
        }
        if (!track.isVipTrack()) {
            if (albumM != null && !albumM.isVipAlbum()) {
                return false;
            }
        }
        //是vip声音
        //免费试用声音不展示
        if (track.isFree()) {
            return false;
        }
        //解锁信息不存在，没有入包，不处理
        AlbumPaidUnLockHintInfo lockHintInfo = mPageView.getAlbumPaidUnLockHintInfo();
        //服务端有返回值，是可以解锁的专辑
        return lockHintInfo != null;
    }

    /**
     * 判断该声音是不是锁定状态
     */
    public boolean isTrackLock(Track track) {
        if (albumM != null && track != null) {
            // VIP专辑: 试听声音为非锁定状态，非试听声音为锁定状态
            if (albumM.isVipFree() || albumM.getVipFreeType() == 1) {
                return !(track.isFree() || track.isAuthorized());
            }
            // 精品专辑: 单集已购买、限时免费听的声音展示非锁定状态
            else if (albumM.isPaid()) {
                return !(track.isAuthorized() || track.isFree());
            }
            // 免费专辑：对非会员：抢先听的声音加标签且加锁
            else {
                return !UserInfoMannage.isVipUser() && track.vipPriorListenStatus == 1;
            }
        }
        return false;
    }

    private void showTrackNo(final ViewHolder holder, long dataId, int orderNo, int position) {
        String num = String.valueOf(orderNo);
        RelativeLayout.LayoutParams coverParams = (RelativeLayout.LayoutParams) holder.cover.getLayoutParams();
        coverParams.width = BaseUtil.dp2px(holder.cover.getContext(), 25);
        holder.cover.setLayoutParams(coverParams);
        RelativeLayout.LayoutParams playFlagParams = (RelativeLayout.LayoutParams) holder.playFlag.getLayoutParams();
        playFlagParams.width = BaseUtil.dp2px(holder.cover.getContext(), 25);
        holder.playFlag.setLayoutParams(coverParams);
        holder.cover.setVisibility(View.INVISIBLE);
        holder.playFlag.setVisibility(View.INVISIBLE);
        holder.orderNoContainer.setVisibility(View.VISIBLE);
        adjustTvTextSize(holder.orderNo, BaseUtil.dp2px(holder.cover.getContext(), 25), num);
        holder.orderNo.setText(num);

        // 处理播放中的动画
        boolean isPerformPlayingAnim = (PlayTools.isPlayCurrTrackById(context, dataId) && brightPosition < 0) || brightPosition == position;
        if (isPerformPlayingAnim) {
            holder.playingFlag.setVisibility(View.VISIBLE);
            holder.orderNo.setVisibility(View.GONE);
            //获取动画LottieDrawable
            final LottieDrawable lottieDrawable = new LottieDrawable();
            String lottiePath = "lottie" + File.separator + "album_ic_playing_two.json";
            LottieComposition.Factory.fromAssetFileName(context, lottiePath, composition -> {
                lottieDrawable.setComposition(composition);
                lottieDrawable.loop(true);
            });
            XmPlayerManager xManager = XmPlayerManager.getInstance(context);
            holder.title.setTextColor(0xFFE83F46);
            // 广告过来的标识 不需要根据状态进行改变

            if (brightPosition >= 0) {
                stopPlayingFlagLoading(holder.playingFlag);
                holder.playingFlag.setImageDrawable(lottieDrawable);
                lottieDrawable.cancelAnimation();
            } else if (!xManager.isPlaying()) {
                if (xManager.isBuffering()) {
                    startPlayingFlagLoading(holder.playingFlag);
                } else {
                    stopPlayingFlagLoading(holder.playingFlag);
                    holder.playingFlag.setImageDrawable(lottieDrawable);
                    lottieDrawable.cancelAnimation();
                }
            } else {
                stopPlayingFlagLoading(holder.playingFlag);
                holder.playingFlag.setImageDrawable(lottieDrawable);
                lottieDrawable.playAnimation();
            }
        } else {
            stopPlayingFlagLoading(holder.playingFlag);
            holder.playingFlag.setVisibility(View.GONE);
            holder.orderNo.setVisibility(View.VISIBLE);
        }
    }

    protected void startPlayingFlagLoading(final ImageView ivPlayFlag) {
        ivPlayFlag.setImageResource(R.drawable.main_album_ic_list_loading);
        AnimationUtil.rotateView(context, ivPlayFlag);
    }

    protected void stopPlayingFlagLoading(ImageView ivPlayFlag) {
        AnimationUtil.stopAnimation(ivPlayFlag);
    }

    public void adjustTvTextSize(TextView tv, int maxWidth, String text) {
        int avaiWidth = maxWidth - tv.getPaddingLeft() - tv.getPaddingRight();

        if (avaiWidth <= 0) {
            return;
        }

        TextPaint textPaintClone = new TextPaint(tv.getPaint());

        DisplayMetrics displayMetrics = tv.getContext().getResources().getDisplayMetrics();
        float trySize = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, 16.0f, displayMetrics);
        // 先用最大字体写字
        textPaintClone.setTextSize(trySize);
        while (textPaintClone.measureText(text) > avaiWidth) {
            trySize--;
            textPaintClone.setTextSize(trySize);
        }

        tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, trySize);
    }

    @Override
    public boolean canDownload(Track track) {
        return !track.isPayTrack() || track.isAuthorized();
    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        brightPosition = Integer.MIN_VALUE;
        super.onSoundSwitch(lastModel, curModel);
    }

    @Override
    public void onClick(View view, Track track, int position, BaseViewHolder h) {
        int i = view.getId();
        if (i == R.id.main_iv_cover) {
            play(track, false, true, view);
            return;
        }
        if (i == R.id.main_btn_download) {
            new XMTraceApi.Trace()
                    .setMetaId(4337)
                    .setServiceId("click")
                    .put("albumId", albumM != null ? String.valueOf(albumM.getId()) : "")
                    .put("trackId", String.valueOf(track.getDataId()))
                    .createTrace();
            //当前是可以解锁的图标点击了
            if (!UserInfoMannage.hasLogined()) {
                //会员抢先听需要登录
                //付费声音需要登录
                //vip专辑需要登录
                //纯免费试听的声音也需要引导登录
                if (track.isVipFirstListenTrack() || track.isPaid() || track.isVipTrack()) {
                    Bundle loginParams = new Bundle();
                    LoginBundleParamsManager.setLoginTitle(loginParams, "下载需登录哦");
                    UserInfoMannage.gotoLogin(context, LoginByConstants.LOGIN_BY_DEFUALT, loginParams);
                    return;
                }
            }
            if (!track.isHasCopyRight()) {
                CustomToast.showFailToast("版权方要求，该资源在该地区无法下载");
                return;
            }
            if (RouteServiceUtil.getDownloadService().isDownloaded(track)) {
                CustomToast.showFailToast("该声音已下载");
                return;
            }

            //3.纯付费声音（不区分是否有该声音的权限）：禁止下载icon,点击后toast文案：当前声音无法下载
            if (track.isPaid() && !track.isVipTrack()) {
                CustomToast.showFailToast("当前声音无法下载");
                return;
            }

            //2.会员声音/抢先听声音（不区分是否会员
            if ((track.isVipTrack() || track.isVipFirstListenTrack()) && !UserInfoMannage.isVipUser()) {
                showBuyVipDialog();
                return;
            }

            //已购买的声音非解锁声音可以下载
            if (UserInfoMannage.hasLogined() && track.isAuthorized() && !track.isAdUnlockAuthoried() && !track.isFree()) {
                download(track, view);
                return;
            }

            //if (isTrackLock(track)) {
            //    return;
            //}

            if (FreeAlbumDownloadTimesManager.shouldWatchVideoUnLock(track)) {
                FreeAlbumDownloadTimesManager.showDownloadLimitDialog(track,
                        new FullFreeTrackDownloadUnlockCallBack() {
                            @Override
                            public void onFullFreeTrackDownloadUnlockSuccess(Track track) {
                                //先清除记录
                                FreeAlbumDownloadTimesManager.clearDownloadTimes();
                                //记录一次下载并开始自动下载
                                FreeAlbumDownloadTimesManager.saveDownloadTimes();
                                download(track, view);
                            }

                            @Override
                            public void onFullFreeTrackDownloadUnlockError(int code, String msg) {
                                Logger.i(TAG, "code = " + code + ", msg = " + msg);
                                CustomToast.showFailToast("数据异常");
                            }
                        }
                );
                return;
            } else {//不需要弹窗看视频，但是也要累加次数
                if (FreeAlbumDownloadTimesManager.isFreeTrackInFreeAlbum(track)) {
                    //只累加免费专辑里面的免费声音的下载次数
                    FreeAlbumDownloadTimesManager.saveDownloadTimes();
                }
            }

            download(track, view);
        }
    }
    private void showBuyVipDialog() {
        if (albumM != null && albumM.getVipResourceTrackBtnsModel() != null) {
            String title = "该节目仅限VIP下载哦";
            String btnUrl = albumM.getVipResourceTrackBtnsModel().url;
            String btnText = albumM.getVipResourceTrackBtnsModel().buttonContent;
            Activity topActivity = BaseApplication.getTopActivity();
            if (topActivity != null) {
                //需要展示弹框，提示弹框
                LiteVipBenefitDialog dialog = new LiteVipBenefitDialog(topActivity);
                dialog.setAlbumId(albumM.getId());
                //设置标题
                dialog.setTitleText(title);
                //设置按钮跳转url
                dialog.setBtnUrl(btnUrl);
                //设置按钮文案
                dialog.setBtnText(btnText);
                //设置埋点来源
                dialog.setVipTipsType(LiteVipBenefitDialog.SHOW_TYPE_DOWNLOAD_NO_PERMISSION);
                dialog.show();
            }
        }
    }

    /**
     * 是否处理直接进行激励视频解锁
     */
    public boolean checkCanDirectIncentive(Track currentTrack, int currentTrackIndex) {
        //当前声音不能解锁，不处理
        if (!isTrackCanVipUnlock(currentTrack)) {
            return false;
        }
        AlbumPaidUnLockHintInfo albumPaidUnLockHintInfo = mPageView.getAlbumPaidUnLockHintInfo();
        if (albumPaidUnLockHintInfo == null) {
            return false;
        }

        if (albumPaidUnLockHintInfo.isUnlockAlbumLimit()) {
            return false;
            //CustomToast.showFailToast("该专辑下今日无可解锁的集数，明日再来或者试试其它专辑哦~");
            //return true;
        }

        if (albumPaidUnLockHintInfo.isUnlockMaxLimit()) {
            // VipUnlockBottomDialgV2 vipUnlockBottomDialgV2 = new VipUnlockBottomDialgV2(mActivity);
            //vipUnlockBottomDialgV2.setUnlockHintInfo(null, albumPaidUnLockHintInfo, VipUnlockBottomDialgV2.TYPE_RES_UNLOCK_PAID_MAX_LIMIT);
            //vipUnlockBottomDialgV2.show();
            //return true;
            return false;
        }

        //不能解锁的不直接解锁
        if (!albumPaidUnLockHintInfo.isCanUnlock()) {
            return false;
        }

        if (!VipTrackUnLockPaidManager.isAlbumItemAbTestForPlanB()) {
            return false;
        }

        /*
         * 解锁的逻辑开始
         */
        List<Track> listData = this.getListData();
        if (listData == null) {
            return false;
        }
        List<Track> unlockTrackList = new ArrayList<>();
        if (albumPaidUnLockHintInfo.trackNum <= 1) {
            unlockTrackList.add(currentTrack);
        } else {
            if (currentTrackIndex < listData.size()) {
                int needUnlockTrackNumber = 0;
                for (int i = currentTrackIndex; i < listData.size(); i++) {
                    Track forTrack = listData.get(i);
                    if (forTrack == null) {
                        continue;
                    }
                    if (currentTrackIndex == i) {
                        if (currentTrack.getDataId() != forTrack.getDataId()) {
                            //当前传入的第一条声音和通过index获取到的不是同一条声音，认为是数据异常
                            FuliLogger.log("vip付费解锁===出现异常，for循环终止");
                            break;
                        }
                    }
                    //添加可以解锁的声音
                    if (isTrackCanVipUnlock(forTrack)) {
                        unlockTrackList.add(forTrack);
                    }
                    //已经遍历了
                    needUnlockTrackNumber++;
                    //是要解锁的声音
                    //已经达到了需要遍历的解锁数量，break
                    if (needUnlockTrackNumber >= albumPaidUnLockHintInfo.trackNum) {
                        break;
                    }
                }
            }
            //没有查找到，使用当前声音
            if (unlockTrackList.size() == 0) {
                unlockTrackList.add(currentTrack);
            }
        }
        if (!UserInfoMannage.hasLogined()) {
            Bundle loginParams = new Bundle();
            LoginBundleParamsManager.setLoginTitle(loginParams, "解锁需登录哦");
            UserInfoMannage.gotoLogin(mActivity, LoginByConstants.LOGIN_BY_DEFUALT, loginParams);
            return true;
        }
        //声音列表进行直接解锁，返回true
        VipTrackUnLockPaidManager.startVipPaidTrackUnlock(mPageView.getAlbumPaidUnLockHintInfo(), mActivity, unlockTrackList, VipTrackUnLockPaidManager.START_VIP_PAID_TRACK_UNLOCK_FROM_ALBUM_PAGE, null);
        return true;
    }

    public boolean canUpdateUi() {
        if (mPageView == null) {
            return false;
        }
        return mPageView.canUpdateUi();
    }

    public static class ViewHolder extends AbstractTrackAdapter.ViewHolder {

        View itemView;
        ImageView playAnimationFlag;
        ImageView ivAlbumNewTag;

        RelativeLayout orderNoContainer;
        TextView orderNo;
        ImageView playingFlag;
        ImageView ivVipFirstListenTag;
        ImageView ivVipTag;

        //付费解锁声音有效期倒计时
        UnlockPaidCountDownTextView adHintTipCountDown;

        private final ImageView vipUnlockShowUnlockRes;

        private final LinearLayout llNameGroup;

        public ImageView mIvFreeListenerVipTag;

        public ViewHolder(View convertView) {
            super(convertView);
            itemView = convertView;
            cover = convertView.findViewById(R.id.main_iv_cover);
            playFlag = convertView.findViewById(R.id.main_play_icon);
            updateAt = convertView.findViewById(R.id.main_update_at);
            title = convertView.findViewById(R.id.main_sound_name);
            playAnimationFlag = convertView.findViewById(R.id.main_playing_flag);
            download = convertView.findViewById(R.id.main_btn_download);

            playCount = convertView.findViewById(R.id.main_playtimes_num);
            duration = convertView.findViewById(R.id.main_tv_total_time);
            playSchedule = convertView.findViewById(R.id.main_play_schedule);
            ivAlbumNewTag = convertView.findViewById(R.id.main_iv_new_flag);
            border = convertView.findViewById(R.id.main_track_divider);

            orderNoContainer = convertView.findViewById(R.id.main_rl_order_no_container);
            orderNo = convertView.findViewById(R.id.main_tv_order_no);
            playingFlag = convertView.findViewById(R.id.main_iv_playing_flag);

            ivVipFirstListenTag = convertView.findViewById(R.id.main_ic_vip_first_listen_tag);
            ivVipTag = convertView.findViewById(R.id.main_item_tag_vip);

            adHintTipCountDown = convertView.findViewById(R.id.main_single_track_ad_hint_unlock_count_down);

            vipUnlockShowUnlockRes = convertView.findViewById(R.id.main_vip_unlock_show_unlock_res);

            llNameGroup = convertView.findViewById(R.id.main_layout_name);
            mIvFreeListenerVipTag = convertView.findViewById(R.id.main_iv_free_listener_vip_flag);
        }
    }

}
