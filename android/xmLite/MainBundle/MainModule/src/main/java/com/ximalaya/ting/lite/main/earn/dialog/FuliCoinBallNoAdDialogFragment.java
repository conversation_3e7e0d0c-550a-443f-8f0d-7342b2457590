package com.ximalaya.ting.lite.main.earn.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.model.earn.FuliBallDialogDataModel;
import com.ximalaya.ting.android.host.model.earn.FuliBallType;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 未登录领取金币的弹框
 *
 * <AUTHOR>
 */
public class FuliCoinBallNoAdDialogFragment extends BaseDialogFragment {
    private static final String ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL = "listen_earn_dialog_data_model";
    private boolean mMaskIsShow = false; //解决fragment重复添加crash问题
    private TextView mTvCoinNumber;
    private TextView mTvEarnMore;
    private View mViewClose;
    private TextView mTvMyBalanceExchange;

    //奖励数据载体
    private FuliBallDialogDataModel mDataModel;

    public static Bundle newArgument(FuliBallDialogDataModel dialogDataModel) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL, dialogDataModel);
        return bundle;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mDataModel = arguments.getParcelable(ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL);
        }
        if (mDataModel == null) {
            mDataModel = new FuliBallDialogDataModel(FuliBallType.BALL_TYPE_DEF, FuliBallType.AWARD_TYPE_GET);
        }
        View inflate = inflater.inflate(R.layout.main_fra_dialog_fuli_coin_ball_no_ad, container, false);
        mViewClose = inflate.findViewById(R.id.main_iv_close);
        mTvCoinNumber = inflate.findViewById(R.id.main_tv_coin_number);
        mTvEarnMore = inflate.findViewById(R.id.main_tv_coin_earn_more);
        mTvMyBalanceExchange = inflate.findViewById(R.id.main_tv_my_balance_exchange);

        dealWithTvCoinNumberText();

        mViewClose.setAlpha(0.4f);
        String totalCoinBalance = mDataModel.myCoinBalance + "";
        BigDecimal decimal = new BigDecimal(totalCoinBalance);
        BigDecimal divide = decimal.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
        String myCoinExchangeInfo = totalCoinBalance + "≈" + divide.toString() + "元";

        SpannableString spannableExchangeInfo = new SpannableString(myCoinExchangeInfo);
        spannableExchangeInfo.setSpan(new ForegroundColorSpan(Color.parseColor("#999999")), 0, totalCoinBalance.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        mTvMyBalanceExchange.setText(spannableExchangeInfo);

        mViewClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!OneClickHelper.getInstance().onClick(view)) {
                    return;
                }
                //关闭点击埋点
                trackCloseClick();
                dismissAllowingStateLoss();
            }
        });
        mTvEarnMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!OneClickHelper.getInstance().onClick(view)) {
                    return;
                }
                trackOkClick();
                dismissAllowingStateLoss();
            }
        });
        AutoTraceHelper.bindData(mViewClose, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(mTvEarnMore, AutoTraceHelper.MODULE_DEFAULT, "");

        //曝光埋点
        trackShow();
        return inflate;
    }

    /**
     * 变色处理
     */
    public void dealWithTvCoinNumberText() {
        if (mTvCoinNumber == null) {
            return;
        }
        String amountString = mDataModel.amount + "";
        String amountTextInfo = "恭喜获得" + amountString + "金币";
        //如果配置了描述，优先使用
        if (!TextUtils.isEmpty(mDataModel.awardDesc)) {
            amountTextInfo = mDataModel.awardDesc;
        }
        //1.优先匹配"2x3金币",这种格式，匹配到就直接变颜色
        SpannableString spannableString = new SpannableString(amountTextInfo);
        Pattern pattern = Pattern.compile("\\d+x\\d+金币|\\d+倍x\\d+金币|\\d+x\\d+倍金币");
        Matcher matcher = pattern.matcher(amountTextInfo);
        boolean isFind = matcher.find();
        if (isFind) {
            int start = matcher.start();
            int end = matcher.end();
            if (start >= 0 || end >= 0) {
                spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#E83F46")), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                mTvCoinNumber.setText(spannableString);
                return;
            }
        }
        //匹配"100金币"这种格式
        int startIndex = amountTextInfo.indexOf(amountString);
        if (startIndex >= 0) {
            //包含可以了，变色处理
            SpannableString spannableAmount = new SpannableString(amountTextInfo);
            spannableAmount.setSpan(new ForegroundColorSpan(Color.parseColor("#E83F46")), startIndex, amountTextInfo.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            mTvCoinNumber.setText(spannableAmount);
        } else {
            //没有包含，直接设置进去
            mTvCoinNumber.setText(amountTextInfo);
        }
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        //去掉标题
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        Window window = dialog.getWindow();
        if (window != null) {
            //dialog背景设置透明，解决shape不生效问题
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setPadding(0, 0, 0, 0); //消除边距
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;   //设置宽度充满屏幕
            lp.height = WindowManager.LayoutParams.MATCH_PARENT;
            window.setAttributes(lp);
        }
        return dialog;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mMaskIsShow = false;
    }

    public boolean isShowing() {
        return mMaskIsShow;
    }

    @Override
    public int show(FragmentTransaction transaction, String tag) {
        if (mMaskIsShow) {
            return 0;
        }
        mMaskIsShow = true;
        return super.show(transaction, tag);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        if (mMaskIsShow) {
            return;
        }
        mMaskIsShow = true;
        super.show(manager, tag);
    }

    /**
     * 曝光埋点
     */
    private void trackShow() {
        if (mDataModel == null) {
            return;
        }
        new XMTraceApi.Trace()
                .setMetaId(12928)
                .setServiceId("dialogView")
                .put("positonName", mDataModel.adPositionName)
                .put("coinCount", mDataModel.amount + "")
                .createTrace();

        if (ConstantsOpenSdk.isDebug) {
            if (TextUtils.isEmpty(mDataModel.adPositionName)) {
                CustomToast.showFailToast("debug警告:数据model未传adPositionName，埋点异常");
            }
        }
    }

    /**
     * 关闭点击埋点
     */
    private void trackCloseClick() {
        if (mDataModel == null) {
            return;
        }
        new XMTraceApi.Trace()
                .setMetaId(12941)
                .setServiceId("dialogClick")
                .put("positonName", mDataModel.adPositionName)
                .put("coinCount", mDataModel.amount + "")
                .put("item", "关闭")
                .createTrace();
    }

    /**
     * "知道了",点击埋点
     */
    private void trackOkClick() {
        if (mDataModel == null) {
            return;
        }
        String item = "知道了";
        if (mTvEarnMore != null) {
            item = mTvEarnMore.getText().toString();
        }
        new XMTraceApi.Trace()
                .setMetaId(12942)
                .setServiceId("dialogClick")
                .put("positonName", mDataModel.adPositionName)
                .put("coinCount", mDataModel.amount + "")
                .put("item", item)
                .createTrace();
    }
}
