package com.ximalaya.ting.lite.main.album.fragment;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.PopupWindow.OnDismissListener;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter;
import com.ximalaya.ting.android.host.business.unlock.dialog.LiteVipBenefitDialog;
import com.ximalaya.ting.android.host.business.unlock.manager.VipTrackUnLockPaidManager;
import com.ximalaya.ting.android.host.business.unlock.model.AlbumPaidUnLockHintInfo;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.listener.ITrackCallBack;
import com.ximalaya.ting.android.host.mainactivity.view.NewGlobalFloatView;
import com.ximalaya.ting.android.host.manager.FixPopupWindowHeightManager;
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.download.DownloadXmlyFullManager;
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.UnLockRelationModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.AlbumUtils;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.PackageUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.album.adapter.AlbumPagerAdapter;
import com.ximalaya.ting.lite.main.album.adapter.AlbumPagerAdapter.PageIndex;
import com.ximalaya.ting.lite.main.album.adapter.LitePaidTrackAdapter;
import com.ximalaya.ting.lite.main.album.contract.AlbumFragmentNewListContact;
import com.ximalaya.ting.lite.main.album.dialog.FreeUnlockBottomDialog;
import com.ximalaya.ting.lite.main.album.dialog.VipFirstListenDialogFragment;
import com.ximalaya.ting.lite.main.constant.PreferenceConstantsInMain;
import com.ximalaya.ting.lite.main.download.BatchDownloadFragment;
import com.ximalaya.ting.lite.main.history.dialog.LiteNoSupportDialogFragment;
import com.ximalaya.ting.lite.main.manager.LiteNoSupportManager;
import com.ximalaya.ting.lite.main.request.HttpParamsConstantsInMain;
import com.ximalaya.ting.lite.main.view.StickyNavLayout;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by dumingwei on 2021/5/28
 * <p>
 * Desc: 参考AlbumFragmentNewList
 */
public class LiteAlbumFragmentNewList extends BaseFragment2 implements OnClickListener, OnItemClickListener, IRefreshLoadMoreListener, OnDismissListener, ITrackCallBack, ILoginStatusChangeListener, AlbumFragmentNewListContact.PageView {

    private static final String TAG = "LiteAlbumFragmentNewLis";

    private boolean isNoCopyright;

    public LiteAlbumFragmentNewList() {
        super(false, SlideView.TYPE_RELATIVELAYOUT, null);
    }

    protected boolean isChoosePage = false;// 是否是选择页数
    private boolean isChoosedPage = false; // 是否选择过页数
    private RefreshLoadMoreListView mListView;
    private TextView vPageSelectedValue;
    private TextView tvMainSort;
    private TextView tvDownload;
    private TextView mUpdateTip; // 加载数量tip
    private LinearLayout llContinuePlay;
    private RelativeLayout rlLastPlayedView;
    private TextView mLastPlayedView;
    private ImageView ivCancelContinuePlay;

    /*无法播放或者下架相关控件开始*/
    private View rlPaidOrOffline;
    private TextView tvNoContentTitle;
    private TextView tvNoContentIntro;
    private TextView tvDownloadFullXmly;
    /*无法播放或者下架相关控件结束*/

    // bundle数据
    private long mAlbumId;
    private int mFrom;
    private int mPlaySource;
    private int newTrackCount = 0;
    private String title;
    private boolean isAsc = true;// 默认排序
    private PopupWindow mPagerSelectorContainer;
    private View mPagerSelectorContent;
    private GridView mAlbumPager;
    private int mPageId = 1;
    private int mPrePageId = 0;
    private int curPage = 1;
    private int maxPageId;
    private int trackCounts;
    private AlbumPagerAdapter mAlbumPagerAdapter;
    private AlbumM mData = new AlbumM();
    private boolean isFirstLoad = true;// 是否第一次load

    private AbstractTrackAdapter trackAdapter;
    private Track mTrack;
    private Long mTrackIdToPlay;
    private boolean mAutoStart;
    private boolean mIsRecordDesc;
    private String mRecSrc;
    private String mRecTrack;
    private boolean changeAsc = false;
    private boolean hasInitUI = false;
    private boolean isOnRefresh = false; // 是否为下拉刷新
    private boolean isFirstPage = true; // 是否为第一页
    private int mVisibleFirstPageId = 1; // 可见列表第一页的pageId
    private int mVisibleLastPageId = 1; // 可见列表最后一页的pageId
    private boolean hasLastPlayTrackInAlbum; // 是否播放过改专辑
    private AlbumEventManage.AlbumFragmentOption mOption;
    private boolean isNeedLoadLastPlayTrackList; // 是否要加载上次播放声音所在列表

    private AlbumPaidUnLockHintInfo mUnLockHintInfo = null;
    //是否是首次请求楼层列表，增加曝光埋点使用
    private boolean mIsFirstRequestVipUnlock = true;
    private long mAlbumTrackTotalCount;
    private boolean isFirstResume = true;

    /**
     * 第一次进入页面的时候，不会触发onMyResume方法，也就无法调用 dealWithUnlockRequest 正确显示解锁信息。
     * 所以在第一次loadData结束以后，主动调用一次dealWithUnlockRequest方法。
     */
    private boolean shouldLoadUnlockHintInfo = true;
    private boolean isLoadingUnlockHintInfo = false;

    private IXmPlayerStatusListener listener = new IXmPlayerStatusListener() {
        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
            Logger.log("albumFragmentNew   onSoundSwitch   ");
            if (!canUpdateUi()) {
                return;
            }
            if (mData == null) {
                return;
            }
            if (!(curModel instanceof Track)) {
                return;
            }
            Track track = (Track) curModel;
            if (!PlayableModel.KIND_TRACK.equals(track.getKind())) {
                return;
            }
            SubordinatedAlbum album = track.getAlbum();
            if (album == null) {
                return;
            }
            if (album.getAlbumId() == mData.getId()) {
                mTrack = track;
                //updateLastPlayView(mTrack);
            }
        }

        @Override
        public void onSoundPrepared() {

        }

        @Override
        public void onSoundPlayComplete() {

        }

        @Override
        public void onPlayStop() {

        }

        @Override
        public void onPlayStart() {
            Logger.i(TAG, "onPlayStart");
            if (mTrack == null) {
                return;
            }
            updateLastPlayView(mTrack, true);
        }

        @Override
        public void onPlayProgress(int currPos, int duration) {

        }

        @Override
        public void onPlayPause() {
            Logger.i(TAG, "onPlayPause");
            if (mTrack == null) {
                return;
            }
            //updateLastPlayView(mTrack);
        }

        @Override
        public boolean onError(XmPlayerException exception) {
            return false;
        }

        @Override
        public void onBufferingStop() {

        }

        @Override
        public void onBufferingStart() {

        }

        @Override
        public void onBufferProgress(int percent) {

        }
    };

    @Override
    protected void initUi(Bundle savedInstanceState) {
        parseBundle();
        mListView = mContainerView.findViewById(R.id.main_id_stickynavlayout_innerscrollview);

        rlPaidOrOffline = findViewById(R.id.main_rl_pair_or_offline);
        rlPaidOrOffline.setOnClickListener(this);
        tvNoContentTitle = findViewById(R.id.main_tv_no_content_title);
        tvNoContentIntro = findViewById(R.id.main_tv_no_content_info);
        tvDownloadFullXmly = findViewById(R.id.main_tv_download_full_xmly);
        tvDownloadFullXmly.setOnClickListener(this);

        initHeader();
        initListView();
        hasInitUI = true;
        initListener();
        updatePageSelectedValue();
        onPageLoadingCompleted(LoadCompleteType.LOADING);
    }

    @Override
    protected String getPageLogicName() {
        return "albumNewList";
    }

    private void parseBundle() {
        Bundle mBundle = getArguments();
        if (mBundle != null) {
            mAlbumId = mBundle.getLong(BundleKeyConstants.KEY_ALBUM_ID, -1);
            mFrom = mBundle.getInt(BundleKeyConstants.KEY_FROM, -1);
            mPlaySource = mBundle.getInt(BundleKeyConstants.KEY_PLAY_SOURCE, -1);
            newTrackCount = mBundle.getInt(BundleKeyConstants.KEY_NEW_TRACK_COUNT);
            title = mBundle.getString(BundleKeyConstants.KEY_TITLE);
            mTrack = mBundle.getParcelable(BundleKeyConstants.KEY_TRACK);
            mRecSrc = mBundle.getString(BundleKeyConstants.KEY_REC_SRC);
            mRecTrack = mBundle.getString(BundleKeyConstants.KEY_REC_TRACK);
            Album album = mBundle.getParcelable(BundleKeyConstants.KEY_ALBUM);
            if (album instanceof AlbumM) {
                mData = (AlbumM) album;
                mIsRecordDesc = mData.isRecordDesc();
                mAutoStart = mData.isAutoStart();
                mTrackIdToPlay = mData.getPlayTrackId();
                mAlbumTrackTotalCount = mData.getIncludeTrackCount();
            }
            isNoCopyright = mBundle.getBoolean("isNoCopyright");
            mOption = (AlbumEventManage.AlbumFragmentOption) mBundle.getSerializable(BundleKeyConstants.KEY_OPTION);
        }
    }

    private void dealWithUnlockRequest() {
        //已经是vip不发起请求
        //只有vip专辑专辑请求解锁接口
        //非会员请求解锁
        if (isLoadingUnlockHintInfo) {
            return;
        }
        isLoadingUnlockHintInfo = true;
        if (mData.isVipAlbum() && !UserInfoMannage.isVipUser()) {
            VipTrackUnLockPaidManager.getAlbumHintUnLock(mAlbumId, null, new IDataCallBack<AlbumPaidUnLockHintInfo>() {
                @Override
                public void onSuccess(@Nullable AlbumPaidUnLockHintInfo object) {
                    isLoadingUnlockHintInfo = false;
                    if (!canUpdateUi()) {
                        return;
                    }
                    mUnLockHintInfo = object;
                    if (mUnLockHintInfo != null && trackAdapter != null) {
                        //本专辑需要解锁
                        trackAdapter.notifyDataSetChanged();
                    } else {
                        //没有入包，不用解锁
                        //交给父布局处理资源位
                        showParentFragmentResPosition();
                    }

                    //增加首屏曝光埋点
                    if (mIsFirstRequestVipUnlock) {
                        AutoTraceHelper.scrollViewItemExposure(getActivity(), mListView);
                        mIsFirstRequestVipUnlock = false;
                    }
                }

                @Override
                public void onError(int code, String message) {
                    isLoadingUnlockHintInfo = false;
                    //交给父布局处理资源位
                    showParentFragmentResPosition();
                }
            });
        } else {
            //交给父布局处理资源位
            showParentFragmentResPosition();

            //增加首屏曝光埋点
            if (mIsFirstRequestVipUnlock) {
                AutoTraceHelper.scrollViewItemExposure(getActivity(), mListView);
                mIsFirstRequestVipUnlock = false;
            }
        }
    }


    /**
     * 展示父布局的资源位
     */
    private void showParentFragmentResPosition() {
//        Fragment parentFragment = getParentFragment();
//        if (!(parentFragment instanceof LiteAlbumFragment)) {
//            return;
//        }
//        LiteAlbumFragment albumFragmentNew = (LiteAlbumFragment) parentFragment;
//        albumFragmentNew.showResPosition();
    }

    private void initListener() {
        vPageSelectedValue.setOnClickListener(this);
        tvMainSort.setOnClickListener(this);
        tvDownload.setOnClickListener(this);
        AutoTraceHelper.bindData(vPageSelectedValue, mData);
        AutoTraceHelper.bindData(tvMainSort, mData);

        mListView.addOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                NewGlobalFloatView globalFloatView = getGlobalFloatView();
                if (globalFloatView != null) {
                    globalFloatView.startAnimation(scrollState != SCROLL_STATE_IDLE);
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (getiGotoTop() != null) {
                    getiGotoTop().setState(firstVisibleItem >= DTransferConstants.DEFAULT_PAGE_SIZE * 2);
                }
                // 滚动判断当前在那页
                int lastVisibleItem = firstVisibleItem + visibleItemCount - 1 - mListView.getRefreshableView().getHeaderViewsCount();
                int i = lastVisibleItem / DTransferConstants.DEFAULT_PAGE_SIZE;
                int i1 = lastVisibleItem % DTransferConstants.DEFAULT_PAGE_SIZE;
                if (i1 > 0) {
                    int curP = mVisibleFirstPageId + i;
                    if (curP > maxPageId) {
                        curP = maxPageId;
                    }
                    if (curPage != curP && curP <= mVisibleLastPageId) {
                        curPage = curP;
                        if (isChoosedPage) {
                            updatePageSelectedValue();
                        }
                    }
                }
            }
        });
        UserInfoMannage.getInstance().addLoginStatusChangeListener(this);
    }

    private void initListView() {
        initOtherTrackAdapter();
        if (trackAdapter != null) {
            trackAdapter.setPlaySource(mPlaySource);
            trackAdapter.setPlayXDCSParams("album", mAlbumId, "");
            mListView.setAdapter(trackAdapter);
        }
        mListView.setMode(Mode.PULL_FROM_START);
        mListView.setOnItemClickListener(this);
        mListView.setOnRefreshLoadMoreListener(this);
        mListView.setPaddingForStatusBar(false);
    }

    /**
     * 别人的专辑
     */
    private void initOtherTrackAdapter() {
        LitePaidTrackAdapter adapter = new LitePaidTrackAdapter(mActivity, this, new ArrayList<>(), this);
        adapter.setTrackType(AbstractTrackAdapter.TYPE_SINGLE);
        adapter.setIsShowOrderNo(true);
        //判断是否为vip用户，需要用到
        adapter.setAlbumM(mData);
        adapter.setAlbumFragmentNewList(this);
        trackAdapter = adapter;
    }

    private void initHeader() {
        llContinuePlay = findViewById(R.id.main_ll_continue_play);

        rlLastPlayedView = findViewById(R.id.main_rl_last_played_view);
        mLastPlayedView = findViewById(R.id.main_tv_last_played_view);

        ivCancelContinuePlay = findViewById(R.id.main_ic_cancel_continue_play);
        ivCancelContinuePlay.setOnClickListener(this);
        vPageSelectedValue = findViewById(R.id.main_tv_page_selected_value);
        tvMainSort = findViewById(R.id.main_sort);
        tvDownload = findViewById(R.id.main_tv_download);
        isAsc = SharedPreferencesUtil.getInstance(mContext).getBoolean(PreferenceConstantsInHost.KEY_IS_ASC + mAlbumId, isAsc);
        updateSortStatus();
        mUpdateTip = findViewById(R.id.main_tv_update_tip);
        if (null != mData && !mData.isOfflineHidden()) {
            hasLastPlayTrackInAlbum = mTrack != null;
            updateLastPlayView(mTrack, true);
        }
    }

    /**
     * 优化点： 建议在满足要求后马上把boolean变量变为相反值，如果在数据返回之后再改变可能会导致数据混乱（比如网络慢时连续点击两次继续播放，会追加重复数据）
     */
    private static void staticLoadData(LiteAlbumFragmentNewList fra) {
        if (fra == null) {
            return;
        }
        final WeakReference<LiteAlbumFragmentNewList> reference = new WeakReference<LiteAlbumFragmentNewList>(fra);
        if (fra.isFirstLoad && fra.mData != null) {
            staticSetData(fra.mData, fra);
            return;
        }
        Map<String, String> params = new HashMap<>();
        params.put(DTransferConstants.PAGE, fra.mPageId + "");
        params.put(DTransferConstants.PRE_PAGE, fra.mPrePageId + "");//不要随便改，改前问我 by Hovi
        params.put(HttpParamsConstants.PARAM_URL_FROM, AlbumEventManage.URL_FROM_ALBUM_TRACKLIST);
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, fra.mAlbumId + "");
        params.put(HttpParamsConstants.PARAM_IS_ASC, String.valueOf(fra.isAsc));
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        if (AlbumEventManage.getAlbumFrom(fra.mFrom) == AlbumEventManage.FROM_FEEDPAGE && fra.newTrackCount > 0) {
            params.put(HttpParamsConstants.PARAM_NEWTRACKCOUNT, String.valueOf(fra.newTrackCount));
        }
        // 下面两参数用于 点击继续播放后 获取播放声音所在列表用于定位播放中的声音(对于继续播放时的请求，这两个参数务必传过来。)
        if (fra.isNeedLoadLastPlayTrackList) {
            params.put(HttpParamsConstants.PARAM_TRACK_ID, String.valueOf(fra.mTrack.getDataId())); // 需要定位的声音id
            params.put(HttpParamsConstantsInMain.PARAM_QUERYPREPAGEWHENPAGESIZELESSOREQUAL, String.valueOf(3)); // 当查询出的记录数少于或等于该值时，会查询出上一页的数据(可取值为3)
        }
        //是否查询有品牌请客
        params.put(HttpParamsConstants.PARAM_IS_QUERY_INVITATION_BRAND, "true");
        CommonRequestM.getAlbumInfo(params, new IDataCallBack<AlbumM>() {
            @Override
            public void onSuccess(final AlbumM object) {
                if (reference == null) {
                    return;
                }
                LiteAlbumFragmentNewList fra = reference.get();
                if (fra == null) {
                    return;
                }
                if (!fra.canUpdateUi()) {
                    return;
                }
                if (object != null) {
                    staticSetData(object, fra);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (reference == null) {
                    return;
                }
                LiteAlbumFragmentNewList fra = reference.get();
                if (fra == null) {
                    return;
                }
                if (!fra.canUpdateUi()) {
                    return;
                }
                if (fra.isFirstLoad) {
                    fra.onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                } else {
                    CustomToast.showFailToast(message);
                }
            }
        });
    }

    private static void staticSetData(AlbumM object, LiteAlbumFragmentNewList fra) {
        if (object.isOfflineHidden()) {
            fra.updateOfflineAlbumView();
            return;
        }
        //TTS专辑不展示下载按钮
        if (fra.tvDownload != null) {
            fra.tvDownload.setVisibility(object.isTTsAlbum() ? View.GONE : View.VISIBLE);
        }
        if (isPaidAlbum(object)) {
            fra.updatePaidAlbumView();
            return;
        }
        fra.setDataForView(object);
        fra.performAutoPlay();
        if (fra.shouldLoadUnlockHintInfo) {
            fra.shouldLoadUnlockHintInfo = false;
            fra.dealWithUnlockRequest();
        }
    }

    @Override
    protected void loadData() {
        staticLoadData(this);
    }

    public boolean isFirstLoad() {
        return isFirstLoad;
    }

    /**
     * 初始化选集面板
     */
    private void showPagerSelector(View view) {
        new XMTraceApi.Trace()
                .setMetaId(4342)
                .setServiceId("click")
                .createTrace();

        if (mPagerSelectorContainer == null && mData != null) {
            mPagerSelectorContent = LayoutInflater.from(mContext).inflate(R.layout.main_layout_album_pager_selector, mListView, false);
            mAlbumPager = (GridView) mPagerSelectorContent.findViewById(R.id.main_album_pager);
            mAlbumPager.setSelector(new ColorDrawable(Color.TRANSPARENT));
            mAlbumPagerAdapter = new AlbumPagerAdapter(mContext, AlbumPagerAdapter.computePagerIndex(DTransferConstants.DEFAULT_PAGE_SIZE, trackCounts, mData.isRecordDesc() ^ isAsc));
            mAlbumPager.setAdapter(mAlbumPagerAdapter);
            mPagerSelectorContent.findViewById(R.id.main_space).setOnClickListener(this);
            mPagerSelectorContent.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    mPagerSelectorContainer.dismiss();
                }
            });
            AutoTraceHelper.bindData(mPagerSelectorContent.findViewById(R.id.main_space), "");
            if (curPage > 0) {
                mAlbumPagerAdapter.setPageId(curPage);
            } else {
                mAlbumPagerAdapter.setPageId(0);
            }
            mAlbumPager.setOnItemClickListener(new OnItemClickListener() {

                @Override
                public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                    isChoosePage = true;
                    isChoosedPage = true;
                    PageIndex pi = (PageIndex) mAlbumPagerAdapter.getItem(position);
                    mPageId = pi.getPageIndex();
                    curPage = mPageId;
                    mPrePageId = mPageId - 1;
                    mAlbumPagerAdapter.setPageId(mPageId);
                    if (trackAdapter != null) {
                        trackAdapter.clear();
                    }
                    if (mListView != null) {
                        mListView.onRefreshComplete();
                    }
                    loadData();
                    mPagerSelectorContainer.dismiss();
                }
            });

            mPagerSelectorContainer = new PopupWindow(mContext);
            mPagerSelectorContainer.setContentView(mPagerSelectorContent);
            mPagerSelectorContainer.setAnimationStyle(R.style.host_popup_window_animation);
            mPagerSelectorContainer.setWidth(WindowManager.LayoutParams.MATCH_PARENT);
            mPagerSelectorContainer.setHeight(WindowManager.LayoutParams.MATCH_PARENT);
            mPagerSelectorContainer.setBackgroundDrawable(new ColorDrawable(Color.parseColor("#b0000000")));
            mPagerSelectorContainer.setOutsideTouchable(true);
            mPagerSelectorContainer.setOnDismissListener(this);
            mPagerSelectorContainer.setFocusable(true);
            mPagerSelectorContainer.update();
        }

        if (mPagerSelectorContainer != null) {
            int[] location = new int[2];
            view.getLocationInWindow(location);
            int screenHeight = BaseUtil.getScreenHeight(mContext);
            int fixedHeight = FixPopupWindowHeightManager.getInstance().getScreenHeightForToAppBottom();

            Logger.i(TAG, "screenHeight = " + screenHeight + " fixedHeight = " + fixedHeight);

            mPagerSelectorContainer.setHeight(fixedHeight - location[1] - view.getMeasuredHeight());
            //mPagerSelectorContainer.setHeight(BaseUtil.getScreenHeight(mContext) - location[1]);
            if (mAlbumPagerAdapter != null) {
                if (curPage > 0) {
                    mAlbumPagerAdapter.setPageId(curPage);
                } else {
                    mAlbumPagerAdapter.setPageId(0);
                }
            }
            if (mPagerSelectorContainer.isShowing()) {
                mPagerSelectorContainer.dismiss();
            } else {
                ToolUtil.showPopWindow(mPagerSelectorContainer, getWindow().getDecorView(), Gravity.NO_GRAVITY, 0, location[1] + view.getMeasuredHeight());
                tvMainSort.setTextColor(Color.parseColor("#666666"));
                updateSortStatus();
            }
            updatePagerSelectorHeader();
        }
    }

    private void updatePagerSelectorHeader() {
        if (null != mPagerSelectorContainer) {
            if (mPagerSelectorContainer.isShowing()) {
                tvMainSort.setAlpha(0.5f);
                tvMainSort.setEnabled(false);
                tvDownload.setAlpha(0.5f);
                tvDownload.setEnabled(false);
                vPageSelectedValue.setCompoundDrawablesWithIntrinsicBounds(LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_album_arrow_up_60_60), null, null, null);
            } else {
                tvMainSort.setAlpha(1f);
                tvMainSort.setEnabled(true);
                tvDownload.setAlpha(1f);
                tvDownload.setEnabled(true);
                vPageSelectedValue.setCompoundDrawablesWithIntrinsicBounds(LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_choose_tracks), null, null, null);
            }
        }
    }

    /**
     * 首次进入快速定位正在播放的声音
     * <p>
     * requirement:进入专辑页，若正在播该专辑的某条声音，则定位到正在播的声音分页&正在播放的声音条；
     */
    private void quickLocationPlayingTrack() {
        long albumId = -1;
        Track curTrack = null;
        XmPlayerManager playerManager = XmPlayerManager.getInstance(mContext);
        PlayableModel currSound = playerManager.getCurrSound();
        if (currSound instanceof Track) {
            curTrack = (Track) currSound;
            SubordinatedAlbum album = curTrack.getAlbum();
            if (null != album) {
                albumId = album.getAlbumId();
            }
        }
        if (albumId == mAlbumId) {
            if (playerManager.isPlaying()) {
                fastLocationTrack(curTrack, playerManager.isPlaying());
            }
        }
    }

    /**
     * 面板点击、刷新时更新按钮处的值
     */
    private void updatePageSelectedValue() {
        if (mData != null && vPageSelectedValue != null) {
            vPageSelectedValue.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 更新排序图标状态
     */
    private void updateSortStatus() {
        if (!canUpdateUi()) {
            return;
        }
        if (mData.isRecordDesc() == isAsc) {
            tvMainSort.setCompoundDrawablesWithIntrinsicBounds(R.drawable.main_album_sort_asc_new_60_60, 0, 0, 0);
        } else {
            tvMainSort.setCompoundDrawablesWithIntrinsicBounds(R.drawable.main_album_sort_desc_new_60_60, 0, 0, 0);
        }
    }

    /**
     * 为播放器中列表声音设置推荐信息
     */
    private void setRec2Track(Track track) {
        SubordinatedAlbum album = track.getAlbum();
        if (album == null) {
            album = new SubordinatedAlbum();
        }
        if (!TextUtils.isEmpty(mData.getAlbumTitle())) {
            album.setAlbumTitle(mData.getAlbumTitle());
        }
        if (!TextUtils.isEmpty(mData.getCategoryName())) {
            album.setCategoryName(mData.getCategoryName());
        }
        if (mData.getCategoryId() > 0) {
            album.setCategoryId(mData.getCategoryId());
        }
        if (!TextUtils.isEmpty(mRecSrc)) {
            album.setRecSrc(mRecSrc);
        }
        if (!TextUtils.isEmpty(mRecTrack)) {
            album.setRecTrack(mRecTrack);
        }
        track.setAlbum(album);
        track.setPlaySource(mPlaySource);
    }

    private void performAutoPlay() {
        if (mOption != null && mOption.isAutoPlay && !isNeedLoadLastPlayTrackList) {
            if (!PlayTools.isAlbumPlaying(mContext, mAlbumId)) {
                onPlayClick(null);
            }
        }
    }

    /**
     * 处理纯付费专辑的逻辑
     */
    private void updatePaidAlbumView() {
        onPageLoadingCompleted(LoadCompleteType.OK);
        rlPaidOrOffline.setVisibility(View.VISIBLE);
        tvNoContentTitle.setText(getString(R.string.main_album_cab_not_play));
        tvNoContentIntro.setText(getString(R.string.main_album_cab_not_play_open_main_app));

        final boolean isXimalayaFullInstalled = PackageUtil.isXimalyaFullInstalled(getActivity());

        if (isXimalayaFullInstalled) {
            tvDownloadFullXmly.setText("打开喜马拉雅完整版");
        } else {
            tvDownloadFullXmly.setText("去应用市场下载喜马拉雅完整版");
        }
    }

    /**
     * 处理已经下架专辑的逻辑
     */
    private void updateOfflineAlbumView() {
        onPageLoadingCompleted(LoadCompleteType.OK);
        rlPaidOrOffline.setVisibility(View.VISIBLE);
        //隐藏下载按钮
        tvDownloadFullXmly.setVisibility(View.INVISIBLE);
        //修改默认展示的文案
        tvNoContentTitle.setText(getString(R.string.main_album_is_offline));
        tvNoContentIntro.setText(getString(R.string.main_album_is_offline_can_not_play));
    }

    private void setDataForView(final AlbumM object) {
        Logger.logFuncRunTimeReset("setlistData begin" + System.currentTimeMillis());
        if (!hasInitUI) {
            return;
        }
        if (object == null && isFirstLoad) {
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            return;
        }
        if (object == null) {
            return;
        }
        if (mAlbumId <= 0) {
            mAlbumId = object.getId();
        }
        onPageLoadingCompleted(LoadCompleteType.OK);

        CommonTrackList<TrackM> commonTrackList = object.getCommonTrackList();
        if (commonTrackList == null) {
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
        } else {
            for (Track track : commonTrackList.getTracks()) {
                track.setHasCopyRight(!isNoCopyright);
            }
            if (commonTrackList.getTotalCount() == 0 && curPage == 1) {
                mListView.setFootViewText("该专辑声音数为0");
                return;
            }
            if (commonTrackList.getTracks() == null || commonTrackList.getTracks().isEmpty()) {
                mListView.onRefreshComplete(false);
                return;
            } else {
                maxPageId = commonTrackList.getTotalPage();
                curPage = object.getPageId();
                mPageId = object.getPageId();
                trackCounts = (int) object.getIncludeTrackCount();
                for (Track mTrack : commonTrackList.getTracks()) {
                    setRec2Track(mTrack);
                }
                //选集
                boolean isSelectTrack = isFirstLoad || isChoosePage || curPage == 1 && !isOnRefresh || isNeedLoadLastPlayTrackList;
                if (isSelectTrack) {
                    isChoosePage = false;
                    if (mData != null) {
                        mData.setCommonTrackList(commonTrackList);
                    }
                    if (trackAdapter != null) {
                        trackAdapter.clear();
                        List<Track> trackList = TrackM.convertTrackMList(commonTrackList.getTracks());
                        trackAdapter.addListData(trackList);
                    }
                    if (AlbumEventManage.getAlbumFrom(mFrom) == AlbumEventManage.FROM_FEEDPAGE) {
                        ToolUtil.stampNewFlag(newTrackCount, curPage, trackCounts, mIsRecordDesc == isAsc, maxPageId, commonTrackList.getTracks());
                    }
                    mVisibleFirstPageId = mVisibleLastPageId = mPageId;
                } else {
                    //加载更多
                    if (isOnRefresh) {
                        isOnRefresh = false;
                        if (!isFirstPage) {
                            if (mData.getCommonTrackList() != null) {
                                mData.getCommonTrackList().updateCommonTrackList(0, commonTrackList);
                            } else {
                                mData.setCommonTrackList(commonTrackList);
                            }
                            if (trackAdapter != null) {
                                trackAdapter.addListData(0, TrackM.convertTrackMList(commonTrackList.getTracks()));
                            }
                            // 更新数量tip
                            mUpdateTip.setVisibility(View.VISIBLE);
                            mUpdateTip.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    if (!canUpdateUi()) {
                                        return;
                                    }
                                    mUpdateTip.setVisibility(View.GONE);
                                }
                            }, 3000);
                        }
                        mVisibleFirstPageId = mPageId;
                    } else {
                        if (mData.getCommonTrackList() != null) {
                            mData.getCommonTrackList().updateCommonTrackList(commonTrackList);
                        } else {
                            mData.setCommonTrackList(commonTrackList);
                        }
                        if (trackAdapter != null) {
                            trackAdapter.addListData(TrackM.convertTrackMList(commonTrackList.getTracks()));
                        }
                        mVisibleLastPageId = mPageId;
                    }
                    if (AlbumEventManage.getAlbumFrom(mFrom) == AlbumEventManage.FROM_FEEDPAGE && newTrackCount > 0) {
                        ToolUtil.stampNewFlag(newTrackCount, curPage, trackCounts, mIsRecordDesc == isAsc, maxPageId, commonTrackList.getTracks());
                    }
                }

                if (isFirstLoad) {
                    quickLocationPlayingTrack();
                    updatePlayControl();
                }

                if (isChoosedPage && !isFirstLoad) {
                    updatePageSelectedValue();
                }
                // 自动播放标题党优先级：正在播放>当前专辑是否有历史播放记录>标题党声音
                if (mAutoStart && !changeAsc && isFirstLoad) {
                    if (trackAdapter != null && trackAdapter.getCount() > 0 && !XmPlayerManager.getInstance(getActivity()).isPlaying()) {
                        if (mTrackIdToPlay > 0 && trackAdapter.getListData() != null) {
                            int index = 0;
                            for (Track track : trackAdapter.getListData()) {
                                if (track.getDataId() == mTrackIdToPlay) {
                                    break;
                                }
                                index++;
                            }
                            PlayTools.playCommonList(getActivity(), mData.getCommonTrackList(), index, false, getContainerView());
                        }
                    }
                }
                isFirstLoad = false;

                // 点击继续播放后，上次声音无论在那页都定位
                if (isNeedLoadLastPlayTrackList) {
                    fastLocationTrack(mTrack, false);
                    isNeedLoadLastPlayTrackList = false;
                }
                if (commonTrackList.getTotalPage() > mPageId) {
                    mListView.onRefreshComplete(true);
                    mPageId++;
                } else {
                    if (mPageId == 1) {
                        mListView.onRefreshComplete(true);
                    }
                    mListView.setHasMoreNoFooterView(false);
                    mListView.setFootViewText("已经到底了～");
                }
            }
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_lite_fra_album_track_list;
    }

    private void updatePagerSelector() {
        if (null != mAlbumPagerAdapter && null != mData) {
            List<PageIndex> pageIndexList = AlbumPagerAdapter.computePagerIndex(DTransferConstants.DEFAULT_PAGE_SIZE, trackCounts, mData.isRecordDesc() ^ isAsc);
            mAlbumPagerAdapter.setListData(pageIndexList);
            mAlbumPagerAdapter.notifyDataSetChanged();
        }
    }

    public void onPlayControl() {
        if (!canUpdateUi()) {
            return;
        }
        //如果当前播放的是当前专辑，点击后暂停播放
        if (PlayTools.isAlbumPlaying(mContext, mAlbumId)) {
            PlayTools.pause(mActivity);
            showLastPlayedView(false);
            updatePlayControl();
            return;
        }
        onPlayClick(null);
    }

    public boolean getContinuePlayTipsVisibility() {
        if (llContinuePlay != null) {
            if (llContinuePlay.getVisibility() == View.VISIBLE) {
                return true;
            }
            return false;
        }
        return false;
    }

    @Override
    public void onClick(View v) {
        if (OneClickHelper.getInstance().onClick(v)) {
            int i = v.getId();
            if (i == R.id.main_tv_page_selected_value) {
                showPagerSelector(v);
                return;
            }
            if (i == R.id.main_sort) {
                new XMTraceApi.Trace()
                        .setMetaId(4343)
                        .setServiceId("click")
                        .createTrace();

                isAsc = !isAsc;
                //本地保存专辑的排序方式
                SharedPreferencesUtil.getInstance(mContext).saveBoolean(PreferenceConstantsInHost.KEY_IS_ASC + mAlbumId, isAsc);
                mPageId = 1;
                curPage = mPageId;
                changeAsc = true;
                updatePagerSelector();
                loadData();
                updateSortStatus();
                return;
            }
            if (i == R.id.main_space) {
                if (mPagerSelectorContainer != null) {
                    mPagerSelectorContainer.dismiss();
                }
                return;
            }
            if (i == R.id.main_rl_last_played_view) {
                new XMTraceApi.Trace()
                        .setMetaId(32291)
                        .setServiceId("click")
                        .createTrace();
                onAlbumPlayBtnClick(v, mTrack, true);
                showLastPlayedView(false);
                return;
            }
            if (i == R.id.main_ic_cancel_continue_play) {
                new XMTraceApi.Trace()
                        .setMetaId(32292)
                        .setServiceId("click")
                        .createTrace();
                showLastPlayedView(false);
                return;
            }
            if (i == R.id.main_tv_download) {
                dealWithBatchDownloadClick(v);
                return;
            }
            if (i == R.id.main_tv_download_full_xmly) {
                dealWithDownLoadFullXmly(v);
                return;
            }
            if (i == R.id.main_rl_pair_or_offline) {
                //在显示付费专辑或者下架专辑的时候，只是避免事件穿透
                return;
            }
        }
    }

    protected void jumpLastPlayBarFragment(View v) {
        new XMTraceApi.Trace()
                .setMetaId(32291)
                .setServiceId("click")
                .createTrace();
        onAlbumPlayBtnClick(v, mTrack, true);
        showLastPlayedView(false);
    }

    public void onAlbumPlayBtnClick(View v, Track track, boolean startPlayer) {
        if (getActivity() != null) {
            if (track != null) {
                mTrack = track;
                recordPlaySubscribeCurrentAlbumInfo();
                if (!PlayTools.isCurrentTrackPlaying(getActivity(), mTrack)) {
                    playLastPlayedSound(mTrack, v, startPlayer);
                    // 定位声音，包括其它页
                    if (null != trackAdapter && !trackAdapter.containItem(mTrack)) {
                        // 获取其它分页
                        isNeedLoadLastPlayTrackList = true;
                        loadData();
                    } else {
                        fastLocationTrack(track, false);
                    }
                } else {
                    if (startPlayer) {
                        ((MainActivity) getActivity()).showPlayFragment(v, PlayerManager.PLAY_TAG);
                    } else {
                        XmPlayerManager.getInstance(mActivity).pause();
                    }
                }
            } else {
                loadAndPlay(mData.getCommonTrackList(), 0, startPlayer, v);
            }
        }
    }

    /**
     * 定位到当前track在列表的位置
     */
    private void fastLocationTrack(Track track, final boolean isPlaying) {
        if (track != null && null != mListView && null != trackAdapter && trackAdapter.containItem(track)) {
            final ListView listView = mListView.getRefreshableView();
            final int position = trackAdapter.indexOf(track) + listView.getHeaderViewsCount();
            // 先滚动到top
            fastLocationByPosition(position, isPlaying);
        }
    }

    /**
     * 根据在列表的位置进行滚动
     */
    private void fastLocationByPosition(final int position, final boolean havNotif) {
        if (position > 3 && getParentFragment() instanceof LiteAlbumFragment) {
            final LiteAlbumFragment parentFragment = (LiteAlbumFragment) getParentFragment();
            final StickyNavLayout stickyNavLayout = parentFragment.getStickyNavLayout();
            //如果当前是目录界面才进行滚动
            if (null != stickyNavLayout && parentFragment.getCurrentShowFragmentIndex() == 1) {
                stickyNavLayout.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        View headView = parentFragment.getHeadView();
                        if (null != headView) {
                            int dy = headView.getMeasuredHeight() - stickyNavLayout.getTopOffset();
                            stickyNavLayout.startScroll(0, dy, 3000);
                        }
                    }
                }, 500);
            }
        }
        final ListView listView = mListView.getRefreshableView();
        listView.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!canUpdateUi()) {
                    return;
                }
                listView.setSelection(position - 1);
                // setSelection 无法触发onScrollStateChanged，下面模拟触发
                listView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        if (null != mListView && null != listView) {
                            mListView.onScrollStateChanged(listView, AbsListView.OnScrollListener.SCROLL_STATE_IDLE);
                        }
                    }
                }, 100);
                if (havNotif) {
                    boolean hasShowLocationToast = SharedPreferencesUtil.getInstance(mContext).getBoolean(PreferenceConstantsInMain.KEY_HAS_SHOW_LOCATION_TOAST, false);
                    if (!hasShowLocationToast) {
                        CustomToast.showToast("已定位至当前播放的节目");
                        SharedPreferencesUtil.getInstance(mContext).saveBoolean(PreferenceConstantsInMain.KEY_HAS_SHOW_LOCATION_TOAST, true);
                    }
                }
            }
        }, 500);
    }

    /**
     * 更新播放、暂停按钮状态
     */
    private void updatePlayControl() {
        if (getParentFragment() instanceof LiteAlbumFragment) {
            ((LiteAlbumFragment) getParentFragment()).updatePlayControl(mTrack);
        }
    }

    /**
     * 点击播放按钮
     */
    private void onPlayClick(View v) {
        onAlbumPlayBtnClick(v, mTrack, true);
        showLastPlayedView(false);
        updatePlayControl();
    }

    private void showLastPlayedView(final boolean isShow) {
        postOnUiThreadDelayed(new Runnable() {
            @Override
            public void run() {
                if (!canUpdateUi()) {
                    return;
                }
                if (llContinuePlay != null) {
                    llContinuePlay.setVisibility(isShow ? View.VISIBLE : View.GONE);
                    parentSetLineOfContinuePlayVisibility(!isShow);
                }
            }
        }, 200);
    }

    /**
     * @param track
     * @param checkVisible 是否在onMyResume里面调用的，
     *                     1. 用来解决解锁后继续播放tips不消失的问题。
     *                     2. 其他页面暂停以后，返回专辑页，不显示继续播放的tips
     */
    private void updateLastPlayView(Track track, boolean checkVisible) {
        if (mData != null && mData.isOfflineHidden()) {
            return;
        }
        if (track != null) {
            if (!PlayTools.isAlbumPlaying(mContext, mAlbumId) && hasLastPlayTrackInAlbum) {
                if (checkVisible) {
                    llContinuePlay.setVisibility(View.VISIBLE);
                    parentSetLineOfContinuePlayVisibility(false);
                } else {
                    llContinuePlay.setVisibility(View.GONE);
                    parentSetLineOfContinuePlayVisibility(true);
                }
            } else {
                llContinuePlay.setVisibility(View.GONE);
                parentSetLineOfContinuePlayVisibility(true);
            }

            rlLastPlayedView.setOnClickListener(this);
            AutoTraceHelper.bindData(rlLastPlayedView, mTrack);

            mLastPlayedView.setText(track.getTrackTitle());
            updatePlayControl();
        } else {
            if (null != llContinuePlay) {
                llContinuePlay.setVisibility(View.GONE);
                parentSetLineOfContinuePlayVisibility(true);
            }
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {

        Log.e("专辑条点击了====", "1111");
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        recordPlaySubscribeCurrentAlbumInfo();

        Log.e("专辑条点击了====", "222");
        if (mListView == null || mListView.getRefreshableView() == null) {
            return;
        }
        if (mData == null) {
            return;
        }
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        CommonTrackList<TrackM> commonTrackList = mData.getCommonTrackList();
        if (commonTrackList == null) {
            return;
        }
        List<TrackM> tracks = commonTrackList.getTracks();
        if (index < 0 || tracks == null || index >= tracks.size()) {
            return;
        }
        TrackM track = tracks.get(index);
        if (track == null || trackAdapter == null) {
            return;
        }

        new XMTraceApi.Trace()
                .setMetaId(4341)
                .setServiceId("click")
                .put("albumId", String.valueOf(mAlbumId))
                .put("trackId", String.valueOf(track.getDataId()))
                .createTrace();
        if (trackAdapter instanceof LitePaidTrackAdapter) {
            //已经直接进行激励视频解锁了，后续不在处理
            if (((LitePaidTrackAdapter) trackAdapter).checkCanDirectIncentive(track, index)) {
                return;
            }
        }
        int albumPageState = AlbumUtils.getAlbumPageState(mData);
        if (albumPageState == AlbumUtils.ALBUM_PAGE_STATE_VIP) {
            PlayTools.playCommonList(getActivity(), commonTrackList, index, !track.isRichAudio(), view);
        } else if (albumPageState == AlbumUtils.ALBUM_PAGE_STATE_PAID) {
            if (mData.isAuthorized() ||
                    track.isAuthorized() ||
                    track.isFree()) {
                PlayTools.playCommonList(getActivity(), commonTrackList, index, !track.isRichAudio(), view);

                //手动设置一次播放顺序
                if (commonTrackList.getParams() != null && commonTrackList.getParams().containsKey("isAsc")) {
                    try {
                        boolean isPlayAsc = Boolean.parseBoolean(commonTrackList.getParams().get("isAsc"));
                        XmPlayerManager.getInstance(mActivity).putUserSelfAlbumPlayOrder(mAlbumId, isPlayAsc);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } else {
                // 点锁定的情况下 精品的弹不支持弹框
                LiteNoSupportManager.checkAndShowLiteNoSupportDialogForTrack(getChildFragmentManager(), LiteNoSupportDialogFragment.PAGE_SOURCE_FROM_ALBUM_LIST, track, mAlbumId);
            }
        } else {
            // 非vip用户 并且是抢先听的声音
            if (!mData.isVip() && track.isVipFirstListenTrack()) {
                VipFirstListenDialogFragment vipFirstListenDialogFragment = new VipFirstListenDialogFragment();
                if (mData != null) {
                    vipFirstListenDialogFragment.setVipResourceInfo(mData.getVipResourceInfo());
                    vipFirstListenDialogFragment.setAlbumId(mData.getId());
                }
                vipFirstListenDialogFragment.setListener(url -> {
                    if (!TextUtils.isEmpty(url)) {
                        Bundle bundle = new Bundle();
                        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
                        startFragment(NativeHybridFragment.newInstance(bundle), null, 0, 0);
                    }
                });
                vipFirstListenDialogFragment.show(getFragmentManager(), "VipFirstListenDialogFragment");
            } else {
                PlayTools.playCommonList(getActivity(), commonTrackList, index, !track.isRichAudio(), view);
                //手动设置一次播放顺序
                if (commonTrackList.getParams() != null && commonTrackList.getParams().containsKey("isAsc")) {
                    try {
                        boolean isPlayAsc = Boolean.parseBoolean(commonTrackList.getParams().get("isAsc"));
                        XmPlayerManager.getInstance(mActivity).putUserSelfAlbumPlayOrder(mAlbumId, isPlayAsc);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    @Override
    public boolean onBackPressed() {
        return super.onBackPressed();
    }

    @Override
    public void onRefresh() {
        isOnRefresh = true;
        isFirstPage = mVisibleFirstPageId > 1 ? false : true;
        mPageId = mVisibleFirstPageId > 1 ? mVisibleFirstPageId - 1 : mVisibleFirstPageId;
        loadData();
    }

    @Override
    public void onMore() {
        mPageId = mVisibleLastPageId + 1;
        loadData();
    }

    public void reload() {
        mPageId = curPage;
        isChoosePage = true; // 模拟选集以刷新列表
        loadData();
    }

    @Override
    public void onDismiss() {
        if (!canUpdateUi()) {
            return;
        }
        tvMainSort.setTextColor(Color.parseColor("#666666"));
        updateSortStatus();
        updatePagerSelectorHeader();
    }

    @Override
    protected View getNetworkErrorView() {
        View view = LayoutInflater.from(getActivity()).inflate(R.layout.main_view_network_error, null);
        View imageView1 = view.findViewById(R.id.main_imageView1);
        if (imageView1 != null) {
            imageView1.setVisibility(View.INVISIBLE);
        }
        View textView1 = view.findViewById(R.id.main_textView1);
        if (textView1 != null) {
            textView1.setVisibility(View.GONE);
        }
        View textView2 = view.findViewById(R.id.main_textView2);
        if (textView2 instanceof TextView) {
            ((TextView) textView2).setText("网络异常，请点击屏幕重试");
        }
        return view;
    }

    @SuppressWarnings("unchecked")
    private void playLastPlayedSound(final Track track, final View v, final boolean startPlayer) {
        if (track == null) {
            CustomToast.showFailToast("获取声音信息异常，请重试");
            return;
        }
        final ProgressDialog mDialog = ToolUtil.createProgressDialog(getActivity(), "正在获取声音列表");
        final HashMap<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, track.getAlbum().getAlbumId() + "");
        params.put(HttpParamsConstants.PARAM_TRACK_ID, track.getDataId() + "");

        IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
        int order = historyManager != null
                ? historyManager.getAlbumSortByAlbumId(track.getAlbum() != null ? track.getAlbum().getAlbumId() : 0)
                : IHistoryManagerForMain.ASC_ORDER;
        //asc为true时貌似时倒序，和示意有点不符,服务端默认是倒序
        final boolean isAsc = (order == IHistoryManagerForMain.ASC_ORDER);
        params.put(HttpParamsConstants.PARAM_ASC, String.valueOf(isAsc));
        mDialog.show();
        CommonRequestM.getPlayHistory(params,
                new IDataCallBack<ListModeBase<TrackM>>() {
                    @Override
                    public void onSuccess(ListModeBase<TrackM> object) {
                        mDialog.dismiss();
                        if (object == null) {
                            CustomToast.showFailToast(R.string.main_network_error);
                            return;
                        }

                        int pageId = object.getPageId();
                        int maxPageId = object.getMaxPageId();
                        int totalCount = object.getTotalCount();
                        params.put(DTransferConstants.PAGE, pageId + "");
                        params.put(DTransferConstants.TOTAL_PAGE, maxPageId + "");
                        params.put(DTransferConstants.PRE_PAGE, pageId - 1 + "");
                        if (object.getList() != null) {
                            loadAndPlay(ListModeBase.toCommonTrackList(object), object.getList().indexOf(track), startPlayer, v);
                        } else {
                            CustomToast.showFailToast(object.getMsg());
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        mDialog.dismiss();
                        CustomToast.showFailToast(R.string.main_network_error);
                    }
                });
    }

    private void loadAndPlay(final CommonTrackList<TrackM> data, int position, boolean startPlayer, View view) {
        if (data == null || data.getTracks() == null) {
            return;
        }
        for (Track track : data.getTracks()) {
            setRec2Track(track);
        }
        PlayTools.playCommonList(mActivity, data, position, startPlayer, view);
    }

    @Override
    protected void onNoContentButtonClick(View view) {

    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            if (llContinuePlay != null && llContinuePlay.getVisibility() == View.VISIBLE) {
                parentSetLineOfContinuePlayVisibility(false);
            }
        }
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        Logger.i(TAG, "onMyResume");
        if (!isFirstLoad && !isFirstResume) {
            final Track track = XmPlayerManager.getInstance(getActivity()).getLastPlayTrackInAlbum(mAlbumId);
            if (getView() != null) {
                getView().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mTrack = track;
                        updateLastPlayView(mTrack, false);
                    }
                }, 500);
            }
        }
        isFirstResume = false;
        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(mTopBtnListener);
        }
        //每次页面可见的时候请求解锁信息
        dealWithUnlockRequest();
    }

    /**
     * 这个页面有些特殊,监听需要写到onResume,onMyResume方法在第一次进入页面时不会走进去
     */
    @Override
    public void onResume() {
        tabIdInBugly = 38304;
        super.onResume();
        Logger.i(TAG, "onResume");
        if (trackAdapter != null) {
            trackAdapter.setXmPlayerStatusListener(listener);
            trackAdapter.notifyDataSetChanged();
            XmPlayerManager.getInstance(mContext).addPlayerStatusListener(trackAdapter);
            RouteServiceUtil.getDownloadService().registerDownloadCallback(trackAdapter);
            XmPlayerManager.getInstance(getActivity()).addAdsStatusListener(trackAdapter);
        }
        updatePlayControl();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (trackAdapter != null && getActivity() != null) {
            RouteServiceUtil.getDownloadService().unRegisterDownloadCallback(trackAdapter);
            XmPlayerManager.getInstance(getActivity()).removeAdsStatusListener(trackAdapter);
            XmPlayerManager.getInstance(getActivity()).removePlayerStatusListener(trackAdapter);
        }
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

    }

    public void notifyTrackListChanged() {
        if (trackAdapter != null) {
            trackAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(this);
    }

    @Override
    public void updateStatus(Track track) {

    }

    @Override
    public void download(Track track) {

    }

    @Override
    public void deleteTrack(Track track) {
        if (track != null && trackAdapter != null) {
            trackAdapter.deleteListData(track);
        }
        // 同步删除本地mData模型里面的声音
        if (null != mData && null != mData.getCommonTrackList() && !ToolUtil.isEmptyCollects(mData.getCommonTrackList().getTracks())) {
            mData.getCommonTrackList().getTracks().remove(track);
        }
    }

    @Override
    public void onPageLoadingCompleted(LoadCompleteType loadCompleteType) {
        super.onPageLoadingCompleted(loadCompleteType);
        if (loadCompleteType == LoadCompleteType.LOADING) {
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
            layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP, RelativeLayout.TRUE);
            layoutParams.setMargins(0, 150, 0, 0);
            getLoadingView().setLayoutParams(layoutParams);
        }

    }

    /**
     * 批量下载点击
     */
    private void dealWithBatchDownloadClick(View v) {
        if (mData == null) {
            return;
        }

        new XMTraceApi.Trace()
                .setMetaId(4336)
                .setServiceId("click")
                .createTrace();

        if (mData.isOfflineHidden()) {
            //下架专辑不处理
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            Bundle loginParams = new Bundle();
            LoginBundleParamsManager.setLoginTitle(loginParams, "下载需登录哦");
            UserInfoMannage.gotoLogin(mContext, LoginByConstants.LOGIN_BY_DEFUALT, loginParams);
            return;
        }
        //对于没有开通vip的用户，vip专辑禁止下载
        if (mData.isVipAlbum() && !UserInfoMannage.isVipUser()) {
            //CustomToast.showSuccessToast("本节目仅限会员下载");
            showBuyVipDialog();
            return;
        }
        BatchDownloadFragment fragment = BatchDownloadFragment.newInstance(mData.isPaid() ? BatchDownloadFragment.ACTION_DOWNLOAD_BUY : BatchDownloadFragment.ACTION_DOWNLOAD, mData.getId());
        startFragment(fragment, v);
    }

    private void showBuyVipDialog() {
        if (mData != null && mData.getVipResourceTrackBtnsModel() != null) {
            String title = "该节目仅限VIP下载哦";
            String btnUrl = mData.getVipResourceTrackBtnsModel().url;
            String btnText = mData.getVipResourceTrackBtnsModel().buttonContent;
            Activity topActivity = BaseApplication.getTopActivity();
            if (topActivity != null) {
                //需要展示弹框，提示弹框
                LiteVipBenefitDialog dialog = new LiteVipBenefitDialog(topActivity);
                dialog.setAlbumId(mData.getId());
                //设置标题
                dialog.setTitleText(title);
                //设置按钮跳转url
                dialog.setBtnUrl(btnUrl);
                //设置按钮文案
                dialog.setBtnText(btnText);
                //设置埋点来源
                dialog.setVipTipsType(LiteVipBenefitDialog.SHOW_TYPE_DOWNLOAD_NO_PERMISSION);
                dialog.show();
            }
        }
    }

    private View myLoadingView;

    @Override
    protected View getLoadingView() {
        if (myLoadingView == null) {
            myLoadingView = super.getLoadingView();
        }
        return myLoadingView;
    }

    private IGotoTop.IGotoTopBtnClickListener mTopBtnListener = new IGotoTop.IGotoTopBtnClickListener() {
        @Override
        public void onClick(View v) {
            if (mListView != null && mListView.getRefreshableView() != null) {
                mListView.getRefreshableView().setSelection(0);
            }
        }
    };

    @Override
    public void onLogout(LoginInfoModelNew olderUser) {

    }

    @Override
    public void onLogin(LoginInfoModelNew model) {
        if (getView() != null) {
            getView().post(new Runnable() {
                @Override
                public void run() {
                    loadData();
                    //登录成功，重新请求解锁
                    dealWithUnlockRequest();
                }
            });
        }
    }

    @Override
    public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {

    }

    /**
     * 判断是否为精品（付费）专辑
     * 如果同是vip专辑，则优先认为是vip类型
     */
    private static boolean isPaidAlbum(AlbumM albumM) {
        if (albumM != null) {
            if (albumM.getVipFreeType() == 1 || albumM.isVipFree()) {
                return false;
            }
            return albumM.isPaid();
        }
        return false;
    }


    @Override
    public AlbumPaidUnLockHintInfo getAlbumPaidUnLockHintInfo() {
        return mUnLockHintInfo;
    }

    @Override
    public Album getAlbum() {
        return mData;
    }

    @Override
    public boolean isTrackAsc() {
        return isAsc;
    }

    private void parentSetLineOfContinuePlayVisibility(boolean visible) {
        if (getParentFragment() instanceof LiteAlbumFragment) {
            ((LiteAlbumFragment) getParentFragment()).setLineOfContinuePlayVisibility(visible);
        }
    }

    private void recordPlaySubscribeCurrentAlbumInfo() {
        if (getParentFragment() instanceof LiteAlbumFragment) {
            ((LiteAlbumFragment) getParentFragment()).recordPlaySubscribeCurrentAlbumInfo();
        }
    }

    /**
     * 下载完整版喜马拉雅
     */
    private void dealWithDownLoadFullXmly(View v) {
        if (!PackageUtil.isXimalyaFullInstalled(mActivity)) {
            //fix 不合规问题，主app下载走应用市场下载
            try {
                Uri uri = Uri.parse("market://details?id=com.ximalaya.ting.android");
                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mActivity.startActivity(intent);
            } catch (Throwable e) {
                e.printStackTrace();
                CustomToast.showFailToast("应用市场打开失败");
            }
            return;
        }
        if (mData == null) {
            return;
        }
        //已经按照了喜马拉雅完整版，打开本专辑
        long albumId = mData.getId();
        String url;
        if (albumId < 0) {
            url = "iting://open";
        } else {
            url = "iting://open?msg_type=13&album_id=" + albumId;
        }
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse(url));
            startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
            CustomToast.showFailToast("打开失败");
        }
    }

    public void findFirstPaidTrackPosition(IDataCallBack<UnLockRelationModel> callBack) {
        LiteAlbumFragmentNewList fra = this;
        if (fra.mAlbumId == 0) return;
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, fra.mAlbumId + "");
        params.put(HttpParamsConstants.PARAM_IS_ASC, String.valueOf(fra.isAsc));
        CommonRequestM.findFirstPaidTrackPosition(params, new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String object) {
                if (!TextUtils.isEmpty(object)) {
                    int tempPage = Integer.parseInt(object);
                    loadNeedUnLockData(tempPage, callBack);
                }
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    private void loadNeedUnLockData(int tempPageId, IDataCallBack<UnLockRelationModel> callBack) {
        boolean isTempLastPage = false;
        if (!isAsc) {
            isTempLastPage = tempPageId == maxPageId;
            tempPageId = maxPageId - tempPageId + 1;
        }
        final boolean isLastPage = isTempLastPage;
        int prePageId = tempPageId - 1;
        Map<String, String> params = new HashMap<>();
        params.put(DTransferConstants.PAGE, tempPageId + "");
        params.put(DTransferConstants.PRE_PAGE, mPrePageId + "");//不要随便改，改前问我 by Hovi
        params.put(HttpParamsConstants.PARAM_URL_FROM, AlbumEventManage.URL_FROM_ALBUM_TRACKLIST);
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, mAlbumId + "");
        params.put(HttpParamsConstants.PARAM_IS_ASC, String.valueOf(isAsc));
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        if (AlbumEventManage.getAlbumFrom(mFrom) == AlbumEventManage.FROM_FEEDPAGE && newTrackCount > 0) {
            params.put(HttpParamsConstants.PARAM_NEWTRACKCOUNT, String.valueOf(newTrackCount));
        }
        // 下面两参数用于 点击继续播放后 获取播放声音所在列表用于定位播放中的声音(对于继续播放时的请求，这两个参数务必传过来。)
        if (isNeedLoadLastPlayTrackList) {
            params.put(HttpParamsConstants.PARAM_TRACK_ID, String.valueOf(mTrack.getDataId())); // 需要定位的声音id
            params.put(HttpParamsConstantsInMain.PARAM_QUERYPREPAGEWHENPAGESIZELESSOREQUAL, String.valueOf(3)); // 当查询出的记录数少于或等于该值时，会查询出上一页的数据(可取值为3)
        }
        //是否查询有品牌请客
        params.put(HttpParamsConstants.PARAM_IS_QUERY_INVITATION_BRAND, "true");
        CommonRequestM.getAlbumInfo(params, new IDataCallBack<AlbumM>() {
            @Override
            public void onSuccess(final AlbumM object) {
                if (object == null) {
                    return;
                }
                CommonTrackList<TrackM> commonTrackList = object.getCommonTrackList();
                trackCanVipUnlock(isLastPage, isAsc, commonTrackList, prePageId, callBack);
            }

            @Override
            public void onError(int code, String message) {
                CustomToast.showFailToast("解锁失败");
            }
        });
    }

    private void trackCanVipUnlock(boolean isLastPage, boolean isAsc, CommonTrackList<TrackM> commonTrackList, int prePageId, IDataCallBack<UnLockRelationModel> callBack) {
        if (!(trackAdapter instanceof LitePaidTrackAdapter)) return;
        LitePaidTrackAdapter litePaidTrackAdapter = (LitePaidTrackAdapter) trackAdapter;
        try {
            if (isAsc) {
                int preAlbumTrackCount = prePageId * DTransferConstants.DEFAULT_PAGE_SIZE;
                for (int i = 0; i < commonTrackList.getTracks().size(); i++) {
                    TrackM trackM = commonTrackList.getTracks().get(i);
                    if (litePaidTrackAdapter.isTrackCanVipUnlock(trackM)) {
                        int currentIndex = i + preAlbumTrackCount;
                        callBack.onSuccess(new UnLockRelationModel(currentIndex + 1, trackM));
                        return;
                    }
                }
            } else {
                if (CollectionUtil.isNullOrEmpty(commonTrackList.getTracks())) {
                    callBack.onSuccess(null);
                    return;
                }
                int diffCount = (int) (mAlbumTrackTotalCount - prePageId * DTransferConstants.DEFAULT_PAGE_SIZE);
                Collections.reverse(commonTrackList.getTracks());
                for (int i = 0; i < commonTrackList.getTracks().size() - 1; i++) {
                    TrackM trackM = commonTrackList.getTracks().get(i);
                    if (litePaidTrackAdapter.isTrackCanVipUnlock(trackM)) {
                        int currentIndex = diffCount - i;
                        callBack.onSuccess(new UnLockRelationModel(currentIndex, trackM));
                        return;
                    }
                }
            }
            loadNeedUnLockData(prePageId + 2, callBack);
        } catch (Exception e) {
            e.printStackTrace();
        }
        callBack.onSuccess(null);
    }
}
