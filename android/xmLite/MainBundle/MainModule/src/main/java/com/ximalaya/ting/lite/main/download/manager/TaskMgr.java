package com.ximalaya.ting.lite.main.download.manager;

import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.download.bean.CommonTask;
import com.ximalaya.ting.lite.main.download.bean.Task;
import com.ximalaya.ting.lite.main.download.bean.TaskInfo;
import com.ximalaya.ting.lite.main.download.bean.TaskState;
import com.ximalaya.ting.lite.main.download.inter.DownloadListener;
import com.ximalaya.ting.lite.main.download.inter.ITaskInfoObserver;
import com.ximalaya.ting.lite.main.download.inter.ITaskStateObserver;
import com.ximalaya.ting.lite.main.download.utils.TaskCode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadFactory;

/**
 * <AUTHOR> feiwen
 * date   :
 * desc   :
 */
public class TaskMgr implements ITaskStateObserver, ITaskInfoObserver {

    public static final String tag = TaskMgr.class.getSimpleName();

    /**
     * 任务调度器
     */
    private NewTaskExecutor mExecutor;
    /**
     * 等待操作任务
     */
    private Map<String, Task<?, ?>> mTasksMap = new LinkedHashMap<>();

    private ITaskStateObserver mObserver;

    private TaskMgr() {
        initExecutor();
    }

    private void initExecutor() {
        ThreadFactory factory = new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r, "Download Thread");
            }
        };
        mExecutor = new NewTaskExecutor(factory, "DownloadExecutor", 1);
    }

    public static TaskMgr get() {
        return Holder.sInstance;
    }

    /**
     * 添加任务
     */
    private synchronized int add(Task<?, ?> paramsTask, boolean now) {
        TaskInfo taskInfo = paramsTask.getInfo();
        // 判断下载任务是否已经存在
        Task<?, ?> temp = search(taskInfo);

        if (temp != null) {
            taskInfo = temp.getInfo();
            Logger.d(tag, "已存在该任务:" + taskInfo.toString());

            // 判断是否正在下载
            if (mExecutor.contain(temp)) {
                Logger.d(tag, "该任务正在下载:" + taskInfo.toString());
                temp.getInfo().setObserver(this);
                temp.getState().setObserver(this);
                return TaskCode.EXIST_DOWNLOADING;
            }

            temp.getInfo().setObserver(this);
            temp.getState().setObserver(this);

            if (now) {
                temp.reset();
                temp.getState().updateState(TaskState.STATE_WAITING, temp);
                mExecutor.add(temp);

                Logger.d(tag, "加入到下载队列中:" + taskInfo.toString());
            } else {
                temp.getState().updateState(TaskState.STATE_PAUSE, temp);
            }

            return TaskCode.EXIST;
        }

        // 信息验证通过后，创建任务信息
        paramsTask.getInfo().setObserver(this);
        paramsTask.getState().setObserver(this);
        synchronized (mTasksMap) {
            mTasksMap.put(taskInfo.key(), paramsTask);
        }
        if (now) {
            paramsTask.getState().updateState(TaskState.STATE_WAITING, paramsTask);
            mExecutor.add(paramsTask);
        } else {
            paramsTask.getState().updateState(TaskState.STATE_PAUSE, paramsTask);
        }

        return TaskCode.SUCCESS;

    }

    public int add(TaskInfo info, DownloadListener downloadTaskCallback) {
        return add(info, downloadTaskCallback, true);
    }

    /**
     * 添加一个任务
     */
    public int add(TaskInfo info, DownloadListener downloadTaskCallback, boolean now) {
        // 验证任务信息
        if (info == null) {
            Logger.e(tag, "参数为空");
            return TaskCode.ERROR_INVALID_INFO;
        }

        int validationCode = info.validation();
        if (validationCode != TaskCode.SUCCESS) {
            Logger.e(tag, "验证信息不通过:" + info.toString());
            return validationCode;
        } else {
            // 信息验证通过的话，会初始化信息内容
            info.init();
        }

        Task<?, ?> task = generateTask(info, downloadTaskCallback);
        return add(task, now);
    }

    private Task<?, ?> generateTask(TaskInfo info, DownloadListener downloadTaskCallback) {
        return new CommonTask(info, new TaskState(), downloadTaskCallback);
    }

    /**
     * 停止任务
     */
    public void stop(TaskInfo taskInfo) {
        Task<?, ?> task = search(taskInfo);
        if (task != null) {
            if (task.stop() == TaskCode.SUCCESS) {
                this.mExecutor.remove(task);
            } else {
                Logger.e(tag, "停止请求失败:" + taskInfo.toString());
            }
        } else {
            Logger.e(tag, "无法找到任务：" + taskInfo.toString());
        }
    }

    /**
     * 删除全部任务
     */
    public void deleteAll() {
        synchronized (mTasksMap) {
            Iterator<Map.Entry<String, Task<?, ?>>> it = mTasksMap.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<String, Task<?, ?>> entry = it.next();
                Task<?, ?> task = entry.getValue();
                this.mExecutor.remove(task);
                it.remove();
                // 删除相关信息
                if (task.delete(true) == TaskCode.SUCCESS) {
                    Logger.d(tag, "成功删除任务:" + task.toString());
                    task.getState().setObserver(null);
                } else {
                    Logger.e(tag, "删除任务失败:" + task.toString());
                }
            }
        }
    }

    /**
     * 停止并且删除任务
     */
    public boolean delete(TaskInfo task) {
        boolean result = false;
        Task<?, ?> temp = search(task);
        if (temp == null) {
            Logger.e(tag, "无法找到要删除的任务" + task.toString());
            return false;
        }

        // 不管是否成功，如果在等待队列中，就移出等待队列。
        this.mExecutor.remove(temp);
        synchronized (mTasksMap) {
            this.mTasksMap.remove(temp.getInfo().key());
        }

        // 删除相关信息
        if (temp.delete(true) == TaskCode.SUCCESS) {
            Logger.d(tag, "成功删除任务:" + task.toString());
            temp.getState().setObserver(null);
            result = true;
        } else {
            Logger.e(tag, "删除任务失败:" + task.toString());
        }

        return result;
    }

    /**
     * 停止所有任务
     */
    public void stopAll() {
        Logger.d(tag, "请求暂停所有任务");
        ArrayList<TaskInfo> stopList = new ArrayList<TaskInfo>();
        synchronized (mTasksMap) {
            for (Map.Entry<String, Task<?, ?>> entry : mTasksMap.entrySet()) {
                Task<?, ?> task = entry.getValue();
                TaskState state = task.getState();
                switch (state.getState()) {
                    case TaskState.STATE_DONE:
                    case TaskState.STATE_DELETE:
                    case TaskState.STATE_PAUSE:
                    case TaskState.STATE_ERROR:
                        break;
                    default:
                        stopList.add(task.getInfo());
                        break;
                }
            }
        }

        stopMultiTasks(stopList);
    }

    /**
     * 停止多个任务
     */
    private void stopMultiTasks(List<TaskInfo> infos) {
        // 过滤任务信息
        LinkedList<Task<?, ?>> array = new LinkedList<>();
        for (TaskInfo info : infos) {
            Task<?, ?> task = search(info);
            if (task != null) {
                array.add(task);
            }
        }
        // 先从等待队列中移除，避免停止后又开始下载
        for (Task<?, ?> task : array) {
            this.mExecutor.remove(task);
        }
        // 最后，停止所有任务
        for (Task<?, ?> task : array) {
            if (task.stop() != TaskCode.SUCCESS) {
                Logger.e(tag, "停止请求失败:" + task.getInfo().toString());
            }
        }
    }

    /**
     * 重新开始所有任务
     */
    public void resumeAll() {
        synchronized (mTasksMap) {
            try {
                for (Map.Entry<String, Task<?, ?>> entry : mTasksMap.entrySet()) {
                    Task<?, ?> task = entry.getValue();
                    TaskState state = task.getState();
                    boolean resume = false;
                    switch (state.getState()) {
                        case TaskState.STATE_DONE:
                        case TaskState.STATE_DELETE:
                            break;
                        default:
                            resume = true;
                            break;
                    }
                    if (resume) {
                        state.setObserver(this);
                        add(task, true);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void update(TaskInfo info, TaskState state) {
        if (mObserver != null) {
            mObserver.update(info, state);
        }
    }

    public Task<?, ?> search(TaskInfo taskInfo) {
        String key = taskInfo.key();
        return mTasksMap.get(key);
    }

    public ArrayList<Runnable> fetchDownloadingTask() {
        return mExecutor.fetchAll();
    }

    public void destroy() {
        //先停止掉所有正在进行的线程及等待执行的线程
        stopAll();
        synchronized (mTasksMap) {
            if (!ToolUtil.isEmptyMap(mTasksMap)) {
                for (Task<?, ?> task : mTasksMap.values()) {
                    task.release();
                }
                mTasksMap.clear();
            }
        }
        mExecutor.destroy();
        // 重新初始化Executor
        initExecutor();
    }

    @Override
    public void update(TaskInfo oldInfo, TaskInfo newInfo) {
        synchronized (mTasksMap) {
            Task removedTask = mTasksMap.remove(oldInfo.key());
            //removedTask任务的信息已经是最新的。
            if (removedTask != null)
                mTasksMap.put(newInfo.key(), removedTask);
        }
    }

    public ITaskStateObserver getObserver() {
        return mObserver;
    }

    public void setObserver(ITaskStateObserver observer) {
        this.mObserver = observer;
    }

    private static class Holder {
        static TaskMgr sInstance = new TaskMgr();
    }

}
