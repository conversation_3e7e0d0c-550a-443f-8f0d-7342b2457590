package com.ximalaya.ting.lite.main.home.adapter

import android.graphics.Bitmap
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.view.drawable.FlexibleRoundDrawable
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.view.LocalImageUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList
import kotlinx.android.synthetic.main.main_item_album_operate.view.*

/**
 * Created by dumingwei on 2020/6/17
 *
 * Desc: 专辑运营模块
 */
class AlbumOperateProvider @JvmOverloads constructor(
        val mFragment: BaseFragment2,
        private val mExtraModel: HomeRecommendExtraViewModel? = null
) : IMulitViewTypeViewAndData<AlbumOperateProvider.Holder, MainAlbumMList> {

    private val TAG: String = "NetOperateProvider"

    private var mModel: MainAlbumMList? = null

    private val dp8: Int = BaseUtil.dp2px(mFragment.context, 8f)

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_album_operate, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView)
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<MainAlbumMList>, convertView: View, position: Int) {
        val model = t.getObject()
        mModel = model
        if (model is MainAlbumMList) {
            val list = model.albumOperateModelList
            if (CollectionUtil.isNotEmpty(list)) {
                val netOperateModel = list[0]

                netOperateModel?.let {
                    with(holder.itemView) {
                        //有配图
                        if (MainAlbumMList.ALBUM_OPERATE_HAS_CUSTOM_COVER == model.displayClass) {
                            mainCardViewNoImage.visibility = View.GONE
                            mainLlHaveImage.visibility = View.VISIBLE
                            if (netOperateModel.title.isNullOrEmpty()) {
                                llTitleInfo.visibility = View.GONE
                                //全部圆角
                                mainIvCover.corners = FlexibleRoundDrawable.CORNER_ALL
                                mainIvCover.cornerRadius
                                //这里修改clCoverInfo的bottomMargin
                                val layoutParams = clCoverInfo.layoutParams as LinearLayout.LayoutParams
                                layoutParams.bottomMargin = dp8
                                clCoverInfo.layoutParams = layoutParams
                            } else {
                                //这里修改clCoverInfo的bottomMargin
                                val layoutParams = clCoverInfo.layoutParams as LinearLayout.LayoutParams
                                layoutParams.bottomMargin = 0
                                clCoverInfo.layoutParams = layoutParams
                                llTitleInfo.visibility = View.VISIBLE
                                //左上角和右上角圆角
                                mainIvCover.corners = FlexibleRoundDrawable.CORNER_TOP_LEFT or FlexibleRoundDrawable.CORNER_TOP_RIGHT
                                mainTvTitle.text = netOperateModel.title
                                if (netOperateModel.subTitle.isNullOrEmpty()) {
                                    mainTvSubTitle.visibility = View.GONE
                                } else {
                                    mainTvSubTitle.visibility = View.VISIBLE
                                    mainTvSubTitle.text = netOperateModel.subTitle
                                }
                            }

                            ImageManager.from(context).displayImage(mainIvCover, netOperateModel.coverPath,
                                    R.drawable.host_default_focus_img, R.drawable.host_default_focus_img)
                            holder.itemView.setOnClickListener {
                                netOperateModel.url?.let {
                                    ToolUtil.clickUrlAction(mFragment, it, null)
                                }
                            }
                        } else {
                            mainCardViewNoImage.visibility = View.VISIBLE
                            mainLlHaveImage.visibility = View.GONE
                            //todo test code

                            mainTvNoImageTitle.text = netOperateModel.title
                            mainTvNoImageSubTitle.text = netOperateModel.subTitle

                            ImageManager.from(context).displayImage(mainIvAlbumCover, netOperateModel.coverPath,
                                    R.drawable.host_default_focus_img, object : ImageManager.DisplayCallback {
                                override fun onCompleteDisplay(lastUrl: String?, bitmap: Bitmap?) {
                                    bitmap?.let {
                                        //LocalImageUtil.setMainColor(mainCardViewNoImage, it)
                                        LocalImageUtil.getBitmapMainColor(it, object : LocalImageUtil.Callback {
                                            override fun onMainColorGot(color: Int) {
                                                val hsvColor = FloatArray(3)
                                                var dstColor: Int = 0xFF2D2D2D.toInt()
                                                var tempColor = color
                                                if (color == 0xFF4A4A4A.toInt()) {
                                                    // 取色失败
                                                    tempColor = bitmap.getPixel(2, 2)
                                                }
                                                Color.colorToHSV(tempColor, hsvColor)
                                                if (hsvColor[1] < 0.1 && hsvColor[2] > 0.9
                                                        || hsvColor[1] < 0.1 && hsvColor[2] < 0.1
                                                        || hsvColor[1] > 0.9 && hsvColor[2] < 0.1) {
                                                    //do nothing
                                                } else {
                                                    hsvColor[1] = 0.3f
                                                    hsvColor[2] = 0.5f
                                                    dstColor = Color.HSVToColor(255, hsvColor)
                                                }
                                                mainCardViewNoImage.setCardBackgroundColor(dstColor)
                                            }
                                        })
                                    }
                                }
                            })
                            holder.itemView.setOnClickListener {
                                netOperateModel.url?.let {
                                    ToolUtil.clickUrlAction(mFragment, it, null)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    class Holder(var itemView: View) : BaseViewHolder()

}