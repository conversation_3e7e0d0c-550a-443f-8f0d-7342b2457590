package com.ximalaya.ting.lite.main.model.album;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.host.data.model.category.Tag;
import com.ximalaya.ting.android.host.model.ad.BannerModel;
import com.ximalaya.ting.android.host.model.rank.BannerM;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CategoryRecommendMList {

    private List<MainAlbumMList> list;
    private List<BannerModel> banners;
    private List<BannerM> oldBanners;
    private List<Tag> keywords;

    private boolean hasMore = true;

    private String json;

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    private String gender;

    public CategoryRecommendMList(String json, boolean isOldBanner) {
        try {
            // 把原始json存下来，方便缓存到手机上。
            setJson(json);
            JSONObject object = new JSONObject(json);
            if (isOldBanner) {
                parseOldBanners(object.optJSONObject("focusImages"));
            } else {
                parseBanners(object.optJSONObject("focusImages"));
            }

            if (object.has("hasMore")) {
                hasMore = object.optBoolean("hasMore", true);
            }

            JSONArray array = null;
            if (object.has("categoryContents")) {
                array = object.optJSONObject("categoryContents")
                        .optJSONArray("list");
            } else if (object.has("content")) {
                array = object.optJSONObject("content")
                        .optJSONArray("list");
            } else if (object.has("modules")) {
                //新版首页，听书开始使用
                array = object.optJSONObject("modules")
                        .optJSONArray("list");
            }
            if (object.has("gender")) {
                setGender(object.optString("gender"));
            }

            if (object.has("keywords")) {
                try {
                    Type listType = new TypeToken<List<Tag>>() {
                    }.getType();
                    keywords = new Gson().fromJson(object.optString("keywords"), listType);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (array != null) {
                list = new ArrayList<>();
                for (int j = 0; j < array.length(); j++) {
                    try {
                        MainAlbumMList albumMList = new MainAlbumMList(array.optJSONObject(
                                j).toString());
                        list.add(albumMList);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public List<BannerM> getOldBanners() {
        return oldBanners;
    }

    public void setOldBanners(List<BannerM> oldBanners) {
        this.oldBanners = oldBanners;
    }

    public List<MainAlbumMList> getList() {
        return list;
    }

    public void setList(List<MainAlbumMList> list) {
        this.list = list;
    }

    public List<BannerModel> getBanners() {
        return banners;
    }

    public void setBanners(List<BannerModel> banners) {
        this.banners = banners;
    }

    /**
     * 解析焦点数据
     *
     * @param json
     */
    private void parseBanners(JSONObject json) {
        if (json == null) {
            return;
        }
        try {
            long responseID = json.optLong("responseId");
            String focusData = json.optString("data");
            if (TextUtils.isEmpty(focusData)) {
                return;
            }

            Type listType = new TypeToken<List<BannerModel>>() {
            }.getType();

            banners = new Gson().fromJson(focusData, listType);

            if (banners != null) {
                for (BannerModel banner : banners) {
                    if (banner != null) {
                        banner.setResponseId(responseID);
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void parseOldBanners(JSONObject object) {
        try {
            oldBanners = new ArrayList<>();
            JSONArray array = object.optJSONArray("list");
            if (array != null) {
                for (int i = 0; i < array.length(); i++) {
                    oldBanners.add(new BannerM(array.optString(i)));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<Tag> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<Tag> keywords) {
        this.keywords = keywords;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public boolean isHasMore() {
        return hasMore;
    }
}
