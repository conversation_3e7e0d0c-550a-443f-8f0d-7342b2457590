package com.ximalaya.ting.lite.main.model.album;

import com.ximalaya.ting.android.host.model.track.EverydayUpdateTrack;

import java.util.List;

/**
 * Created by qinhuifeng on 2020/11/23
 *
 * <AUTHOR>
 */
public class HomeTingUpdateModel {
    public int allCount;

    public int status;

    public long updateAt;

    public int unreadNum;     // ==>未读条数
    public int todayUpdateCnt;// ==>今日更新条数
    public long lastUpdateAt;// ==>最新一条内容的更新时间

    public List<EverydayUpdateTrack> showTracks;

    public List<EverydayUpdateTrack> allTracks;

    public boolean useProImage;

    //最后一条数据的时间线
    public long lastTimeline;
}
