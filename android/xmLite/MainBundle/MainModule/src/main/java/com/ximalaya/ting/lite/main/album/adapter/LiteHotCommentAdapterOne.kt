package com.ximalaya.ting.lite.main.album.adapter

import android.annotation.SuppressLint
import android.graphics.drawable.Drawable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RatingBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.host.util.view.EmotionUtil2
import com.ximalaya.ting.android.host.util.view.SpecialCenterAlignImageSpan
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter
import com.ximalaya.ting.lite.main.model.album.LiteHotComment

/**
 * Created by dumingwei on 2021/5/26
 *
 * Desc: 专辑简介界面的热评适配器
 */
class LiteHotCommentAdapterOne(
        private val fragment: BaseFragment2,
        private val mAlbumMList: List<LiteHotComment>
) : AbRecyclerViewAdapter<LiteHotCommentAdapterOne.ViewHolder>() {

    private var imageSpanHeight = 0

    init {
        //高度必须和文字大小一致
        imageSpanHeight = BaseUtil.dp2px(fragment.context, 15f)
    }

    var onLikeAction: ((position: Int, comment: LiteHotComment) -> Unit)? = null

    var onItemClickAction: (() -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = inflater.inflate(R.layout.main_item_hot_comment_one, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount() = mAlbumMList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        onBindAlbumRankViewHolder(holder, position)
    }

    @SuppressLint("SetTextI18n")
    private fun onBindAlbumRankViewHolder(holder: ViewHolder, position: Int) {
        val model: LiteHotComment = mAlbumMList[position]

        if (model == null) {
            return
        }

        val commentText = model.content
        //注意！！！第一个点替换成ImageSpan
        val spannableStringBuilder = SpannableStringBuilder(". ")
        spannableStringBuilder.append(EmotionUtil2.getInstance().convertEmotionText2Span(commentText))

        val drawable: Drawable? = fragment.context?.let { ContextCompat.getDrawable(it, R.drawable.main_ic_hot_comment_flag) }
        if (drawable != null) {
            val intrinsicWidth = drawable.intrinsicWidth
            val intrinsicHeight = drawable.intrinsicHeight

//            val imageSpanWidth = try {
//                imageSpanHeight * intrinsicWidth / intrinsicHeight
//            } catch (e: Exception) {
//                intrinsicWidth
//            }
//
//            drawable.setBounds(0, 0, imageSpanWidth, imageSpanHeight)
            drawable.setBounds(0, 0, intrinsicWidth, intrinsicHeight)
            //val imageSpan = FixedImageSpan(drawable, DynamicDrawableSpan.ALIGN_BASELINE)
            val imageSpan = SpecialCenterAlignImageSpan(drawable)
            spannableStringBuilder.setSpan(imageSpan, 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            holder.tvComment.text = spannableStringBuilder
        } else {
            holder.tvComment.text = commentText
        }

        ImageManager.from(fragment.context).displayImage(holder.ivAvatar, model.smallLogo, R.drawable.host_default_album, R.drawable.host_default_album)

        holder.tvCommentatorName.text = model.nickname

        val commentScore = model.albumScore ?: 6

        when {
            commentScore <= 7 -> {
                holder.ratingBar.rating = 3.5f
            }
            commentScore <= 8 -> {
                holder.ratingBar.rating = 4f
            }
            commentScore <= 9 -> {
                holder.ratingBar.rating = 4.5f
            }
            else -> {
                holder.ratingBar.rating = 5f
            }
        }

        holder.tvLikeCount.text = model.likeCount?.toString()

        if (model.liked == true) {
            holder.ivLike.background = fragment.context?.let { ContextCompat.getDrawable(it, R.drawable.main_ic_liked) }
        } else {
            holder.ivLike.background = fragment.context?.let { ContextCompat.getDrawable(it, R.drawable.main_ic_like_normal) }
        }

        holder.ivLike.setOnClickListener { onLikeAction?.invoke(position, model) }

        //注意！！！，加上这个可以让简介界面ScrollView正常滑动，可以去掉这行代码，
        // 滑动一下听友热评模块的RecyclerView试试，发现无法上下滑动
        holder.itemView.setOnClickListener {
            onItemClickAction?.invoke()
        }

    }

    override fun getItem(position: Int): Any? {
        val albumSize = mAlbumMList.size
        if (CollectionUtil.isNotEmpty(mAlbumMList) && position >= 0 && position < albumSize) {
            return mAlbumMList[position]
        }
        return null
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvComment: TextView = itemView.findViewById(R.id.main_tv_comment)

        //评论头像
        val ivAvatar: RoundImageView = itemView.findViewById(R.id.main_iv_avatar)
        val tvCommentatorName: TextView = itemView.findViewById(R.id.main_tv_commentator)
        val ratingBar: RatingBar = itemView.findViewById(R.id.main_comment_rating_star)

        val tvLikeCount: TextView = itemView.findViewById(R.id.main_tv_like_count)
        val ivLike: ImageView = itemView.findViewById(R.id.main_iv_like)
    }

}