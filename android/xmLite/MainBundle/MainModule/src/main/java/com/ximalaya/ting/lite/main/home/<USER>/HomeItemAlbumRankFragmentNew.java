package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.lite.main.home.adapter.HomeItemAlbumRankAdapterNew;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeItemAlbumRankModel;
import com.ximalaya.ting.lite.main.model.rank.GroupRankAlbumList;
import com.ximalaya.ting.lite.main.newhome.fragment.LiteHomeRecommendFragment;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by qinhuifeng on 2019-07-16
 * <p>
 * 首页楼层中的排行榜
 *
 * <AUTHOR>
 */
public class HomeItemAlbumRankFragmentNew extends BaseFragment2 {

    private final static String KEY_RANKING_LIST_ID = "key_ranking_list_id";
    private final static String KEY_RANKING_NEED_REQUEST_NUMBER = "key_ranking_need_request_number";
    private final static String KEY_MODULE_ID = "key_module_id";
    private final static String KEY_CUR_INDEX = "key_cur_index";
    private final static String KEY_IS_RECOMMEND = "key_is_recommend";
    private RecyclerView mRvList;
    private HomeItemAlbumRankAdapterNew mAdapter;
    private final List<HomeItemAlbumRankModel> mDataList = new CopyOnWriteArrayList<>();

    //是否正在请求，屏蔽可见请求和loadData请求接口，只允许请求1次
    private boolean isRequesting = false;
    private int mRankingListId = 0;
    private int mRankNeedRequestNumber = 0;

    private int mModuleId = 0;
    private int mCurIndex = 0;
    private String mCurTitle = "";
    private boolean mIsRecommendChannel = true;

    private HomeItemAlbumRankFragment.iGetCurIndexCallBack mGetCurIndexCallBack;

    public HomeItemAlbumRankFragmentNew() {
        super(false, null);
    }

    public static HomeItemAlbumRankFragmentNew newInstance(int rankingListId, int rankNeedRequestNumber, boolean isRecommendChannel) {
        Bundle args = new Bundle();
        args.putInt(KEY_RANKING_LIST_ID, rankingListId);
        args.putInt(KEY_RANKING_NEED_REQUEST_NUMBER, rankNeedRequestNumber);
        args.putBoolean(KEY_IS_RECOMMEND, isRecommendChannel);
        HomeItemAlbumRankFragmentNew fragment = new HomeItemAlbumRankFragmentNew();
        fragment.setArguments(args);
        return fragment;
    }

    public static HomeItemAlbumRankFragmentNew newInstance(int rankingListId, int rankNeedRequestNumber, int moduleId, int curIndex, boolean isRecommendChannel) {
        Bundle args = new Bundle();
        args.putInt(KEY_RANKING_LIST_ID, rankingListId);
        args.putInt(KEY_RANKING_NEED_REQUEST_NUMBER, rankNeedRequestNumber);
        args.putInt(KEY_MODULE_ID, moduleId);
        args.putInt(KEY_CUR_INDEX, curIndex);
        args.putBoolean(KEY_IS_RECOMMEND, isRecommendChannel);
        HomeItemAlbumRankFragmentNew fragment = new HomeItemAlbumRankFragmentNew();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        return "HomeItemPlayHistoryFragment";
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mRankingListId = arguments.getInt(KEY_RANKING_LIST_ID, 0);
            mRankNeedRequestNumber = arguments.getInt(KEY_RANKING_NEED_REQUEST_NUMBER, 0);
            mModuleId = arguments.getInt(KEY_MODULE_ID, 0);
            mCurIndex = arguments.getInt(KEY_CUR_INDEX, 0);
            mIsRecommendChannel = arguments.getBoolean(KEY_IS_RECOMMEND, true);
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRvList = findViewById(R.id.main_rv_list_rank);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(mContext, 2, LinearLayoutManager.VERTICAL, false);
        gridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                if (position == 0) {
                    return 2;
                }
                return 1;
            }
        });
        mAdapter = new HomeItemAlbumRankAdapterNew(this, mDataList, mCurTitle, mIsRecommendChannel);
        mRvList.setNestedScrollingEnabled(false);
        mRvList.setLayoutManager(gridLayoutManager);
        mRvList.setAdapter(mAdapter);
        mAdapter.notifyDataSetChanged();
    }

    @Override
    protected void loadData() {
        if (mDataList.isEmpty()) {
            requestRankData();
        }
    }

    public void setCurTitle(String mCurTitle) {
        this.mCurTitle = mCurTitle;
    }

    public void setGetCurIndexCallBack(HomeItemAlbumRankFragment.iGetCurIndexCallBack mGetCurIndexCallBack) {
        this.mGetCurIndexCallBack = mGetCurIndexCallBack;
    }

    public void requestRankData() {
        if (isRequesting) {
            return;
        }
        isRequesting = true;
        HashMap<String, String> p = new HashMap<>();
        p.put("pageId", String.valueOf(1));
        //最少请求8个
        int pageSize = mRankNeedRequestNumber;
        if (pageSize <= 0) {
            pageSize = 8;
        }
        p.put("pageSize", pageSize + "");
        p.put("rankingListId", String.valueOf(mRankingListId));
        p.put("random", "true");
        LiteCommonRequest.getNewRankGroupAlbumList(p, new IDataCallBack<GroupRankAlbumList>() {
            @Override
            public void onSuccess(@Nullable final GroupRankAlbumList object) {
                isRequesting = false;
                if (!canUpdateUi()) {
                    return;
                }
                if (object == null || object.list == null || object.list.size() == 0) {
                    checkShowErrorPage();
                    return;
                }
                List<AlbumM> albumMList = new ArrayList<>();
                for (int i = 0; i < object.list.size(); i++) {
                    Album album = object.list.get(i);
                    if (album instanceof AlbumM) {
                        albumMList.add((AlbumM) album);
                    }
                    if (albumMList.size() >= 8) {
                        break;
                    }
                }
                setDataList(albumMList, mModuleId, mRankingListId);

                checkShowErrorPage();
            }

            @Override
            public void onError(int code, String message) {
                isRequesting = false;
                checkShowErrorPage();
            }
        });
    }

    private void checkShowErrorPage() {
        if (!canUpdateUi()) {
            return;
        }
        if (mDataList.isEmpty()) {
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        }
    }

    public void setDataList(List<AlbumM> list, int moduleId, int rankingListId) {
        if (list == null) {
            return;
        }

        //填充数据
        List<HomeItemAlbumRankModel> fillData = new ArrayList<>();
        int maxSize = list.size();
        int addSize = 0;
        for (int i = 0; i < maxSize; i++) {
            AlbumM albumM = list.get(i);
            if (albumM == null) {
                continue;
            }
            HomeItemAlbumRankModel model = new HomeItemAlbumRankModel();
            model.albumM = albumM;
            model.viewType = HomeItemAlbumRankModel.ITEM_ALBUM_RANK;
            if (moduleId != 0) {
                model.moduleId = moduleId;
                model.rankingListId = rankingListId;
            }
            fillData.add(model);
            addSize++;
            if (addSize >= 7) {
                break;
            }
        }
        mDataList.clear();
        mDataList.addAll(fillData);

        //如果已经加载了就刷新一次
        if (mRvList == null || mAdapter == null || !isCurPage()) {
            return;
        }
        mAdapter.notifyDataSetChanged();
        FuliLogger.log("排行榜模块:updateDataList:" + rankingListId + " moduleId:" + moduleId);
    }

    @Override
    public void onMyResume() {
        //不做状态颜色处理
        setFilterStatusBarSet(true);
        super.onMyResume();

        if (isCurPage()) {
            FuliLogger.log("dqq排行榜模块:HomeItemAlbumRankFragment.onMyResume==" + mRankingListId + " mModuleId:" + mModuleId);
            onRealResume();
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);

        if (isVisibleToUser) {
            if (mModuleId == 0) {
                return;
            }
            //可见了
            if (isCurPage()) {
                FuliLogger.log("dqq排行榜模块:HomeItemAlbumRankFragment.setUserVisibleHint==" + mRankingListId + " mModuleId:" + mModuleId);
                onRealResume();
            }
        }
    }

    private boolean isCurPage() {
        if (mGetCurIndexCallBack != null) {
            return mGetCurIndexCallBack.getCurIndex() == mCurIndex;
        } else {
            return true;
        }
    }

    private void onRealResume() {
        if (!canUpdateUi()) {
            return;
        }
        //没有数据的时候需要单独请求
        if (mDataList.isEmpty()) {
            requestRankData();
        } else {
            if (mAdapter != null) {
                mAdapter.notifyItemRangeChanged(0, mAdapter.getItemCount(), "1");
            }
        }
    }


    @Override
    public int getTitleBarResourceId() {
        return -1;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_item_fragment_album_rank_new;
    }


    public int getRankingListId() {
        return mRankingListId;
    }

    @Override
    protected boolean isShowSevenDay() {
        return true;
    }

    //可视化埋点关闭单页面
    @Override
    public boolean isCloseBuryPageAndLayoutTag() {
        return true;
    }

    @Override
    public boolean isGlobalFloatViewGray() {
        if (getParentFragment() instanceof LiteHomeRecommendFragment) {
            return ((LiteHomeRecommendFragment) getParentFragment()).isGlobalFloatViewGray();
        }
        return false;
    }
}
