package com.ximalaya.ting.lite.main.home.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.model.homepage.HomeAwardSetting;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.manager.HomeExcitationVideoManager;

import java.util.Map;

/**
 * Created by le.xin on 2019-12-27.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class HomeRecommendExcitationAdapterProvider implements
        IMulitViewTypeViewAndData<HomeRecommendExcitationAdapterProvider.Holder,
                HomeAwardSetting.GameTimesBean> {

    private HomeRecommedExtraDataProvider mRecommedExtraDataProvider;

    public HomeRecommendExcitationAdapterProvider(BaseFragment2 fragment,
                                                  HomeRecommedExtraDataProvider recommedExtraDataProvider) {
        this.mRecommedExtraDataProvider = recommedExtraDataProvider;
    }

    @Override
    public void bindViewDatas(Holder holder, ItemModel<HomeAwardSetting.GameTimesBean> t,
                              View convertView, int position) {

        if (holder == null || t == null || t.getObject() == null) {
            return;
        }

        HomeAwardSetting.GameTimesBean object = t.getObject();

        if (object.isUsed() || object.getShowTime() > System.currentTimeMillis()) {
            holder.root.setVisibility(View.GONE);
            return;
        }
        holder.cover.setImageResource(R.drawable.main_red_packet);

        HomeAwardSetting.TitlesBean titlesBean = HomeExcitationVideoManager.getInstance().getNeedShowTitleBean(object.getRemaining());
        if (titlesBean == null) {
            return;
        }
        holder.title.setText(replaceStr(titlesBean.getTitle(), object.getAward() + ""));
        holder.subtitle.setText(dealRemaining(titlesBean.getSubTitle(), object.getRemaining() + ""));
        holder.peopleCount.setText(replaceStr(titlesBean.getSubTitle2(), object.getReceivedCount() + ""));
        holder.root.setVisibility(View.VISIBLE);
        holder.root.setOnClickListener(HomeExcitationVideoManager.getInstance().
                getAwardClickListener(object, mRecommedExtraDataProvider));

        AutoTraceHelper.bindData(holder.root, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, object));
    }

    private String replaceStr(String title, String num) {
        if (title != null && num != null) {
            return title.replaceAll("%s|%d", num);
        }
        return title;
    }

    /**
     * 客户端本地进行数字替换
     */
    private String dealRemaining(String title, String number) {
        if (title != null && number != null) {
            return title.replaceAll("\\d+|%s|%d", number);
        }
        return title;
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_recommend_excitation_layout, parent, false);
    }

    @Override
    public Holder buildHolder(View convertView) {
        return new Holder(convertView);
    }

    public class Holder extends HolderAdapter.BaseViewHolder {
        public View root;
        public View border;
        public ImageView cover;
        public TextView title;
        public TextView subtitle;
        public TextView peopleCount;

        public Holder(View convertView) {
            root = convertView.findViewById(R.id.main_rl_item_view);
            cover = (ImageView) convertView.findViewById(R.id.main_iv_album_cover);
            border = convertView.findViewById(R.id.main_album_border);
            title = (TextView) convertView.findViewById(R.id.main_tv_album_title);
            subtitle = (TextView) convertView.findViewById(R.id.main_tv_album_subtitle);
            peopleCount = convertView.findViewById(R.id.main_count);
        }
    }
}
