package com.ximalaya.ting.lite.main.album.fragment;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.StyleSpan;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.airbnb.lottie.LottieAnimationView;
import com.astuetz.PagerSlidingTabStrip;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter.FragmentHolder;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.AlbumCollectManager;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.Blur;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.HorizontalScrollViewInSlideView;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.framework.view.snackbar.SnackbarManager;
import com.ximalaya.ting.android.host.business.unlock.dialog.GlobalTrackUnlockSuccessCallback;
import com.ximalaya.ting.android.host.business.unlock.manager.UnlockSuccessCallBackManager;
import com.ximalaya.ting.android.host.business.unlock.manager.VipTrackUnLockPaidManager;
import com.ximalaya.ting.android.host.business.unlock.model.AlbumPaidUnLockHintInfo;
import com.ximalaya.ting.android.host.business.unlock.model.VideoUnLockResult;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.ICollectStatusCallback;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.listener.IXmPlayerStatusListenerImpl;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.mainactivity.view.NewGlobalFloatView;
import com.ximalaya.ting.android.host.manager.ShareResultManager;
import com.ximalaya.ting.android.host.listenertask.ShareGuideSoundManager;
import com.ximalaya.ting.android.host.manager.CheckFunSwitchControlManager;
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.ShareDialog;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.manager.track.AlbumListenSubscribeManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.ExpandTagResult;
import com.ximalaya.ting.android.host.model.album.TagResult;
import com.ximalaya.ting.android.host.model.album.TraceAlbumPageModel;
import com.ximalaya.ting.android.host.model.earn.ShareGuideSoundModel;
import com.ximalaya.ting.android.host.model.play.CommercialEntrance;
import com.ximalaya.ting.android.host.model.search.SearchMetadata;
import com.ximalaya.ting.android.host.monitor.TraceHelper;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.common.AlbumUtils;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.TextSizeUtils;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.constant.MMKVKeyConstantsKt;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.view.CenterAlignImageSpan;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.view.SpaceSpan;
import com.ximalaya.ting.android.host.view.SubscribeTipsBottomDialog;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.host.view.text.MarqueeTextView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil;
import com.ximalaya.ting.android.shareservice.base.IShareDstType;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.UiUtil;
import com.ximalaya.ting.lite.main.album.dialog.FreeUnlockBottomDialog;
import com.ximalaya.ting.lite.main.album.dialog.LiteFullIntroDialog;
import com.ximalaya.ting.lite.main.comment.CommentPagesRequest;
import com.ximalaya.ting.lite.main.comment.fragment.AlbumCommentTabFragment;
import com.ximalaya.ting.lite.main.home.fragment.PlayPageTagCategoryMetadataFragment;
import com.ximalaya.ting.lite.main.manager.AnchorPullNewManager;
import com.ximalaya.ting.lite.main.manager.DownQjApkManager;
import com.ximalaya.ting.lite.main.manager.ShareAlbumDialogUtils;
import com.ximalaya.ting.lite.main.play.manager.PlaySubscribeRecommendManager;
import com.ximalaya.ting.lite.main.playnew.component.ShareRewardComponent;
import com.ximalaya.ting.lite.main.playnew.manager.ShareRewardManager;
import com.ximalaya.ting.lite.main.playnew.view.PlayPageBackgroundView;
import com.ximalaya.ting.lite.main.utils.HiddenAlbumUtils;
import com.ximalaya.ting.lite.main.utils.ShareUtilsInMain;
import com.ximalaya.ting.lite.main.view.StickyNavLayout;
import com.ximalaya.ting.lite.main.view.SubscribeButtonWaveView;
import com.ximalaya.ting.lite.main.view.text.StaticLayoutManager;
import com.ximalaya.ting.lite.main.view.text.StaticLayoutView;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

import static com.ximalaya.ting.android.host.util.constant.MMKVKeyConstantsKt.SHOW_FIRST_SUBSCRIBE_SUCCESS_DIALOG;

import org.json.JSONObject;

/**
 * <AUTHOR> on 2017/6/29.
 */
public class LiteAlbumFragment extends BaseFragment2 implements OnClickListener, View.OnLayoutChangeListener, GlobalTrackUnlockSuccessCallback {

    private static final String TAG = "LiteAlbumFragment";

    //最底部的背景色相关开始
    private static final int mDefaultBgColor = 0xff444444;
    private String mCurCoverUrl;
    private Bitmap mBlurImage;
    private int mBackgroundColor;
    //最底部的背景色相关结束

    private int mRequestCode;

    private ViewPager mViewPager;

    private View mIndicatorBorder;
    private View mIndicatorTopBorder;
    private long mAlbumId;
    private int mFrom;
    private int newTrackCount = 0;
    private TabCommonAdapter mPagerAdapter;
    private AlbumM mData;
    //解锁相关model
    private AlbumPaidUnLockHintInfo mAlbumPaidUnLockHintInfo;
    private Bundle mBundle;
    private Track mTrack;// 上次播放历史

    private RelativeLayout rlIndicatorAndPlayBtn;//PagerSlidingTabStrip和播放按钮一栏
    private PagerSlidingTabStrip mIndicator;
    private TextView tvPlayControl;
    private StaticLayoutView slvAlbumIntro;

    private boolean isFirstLoading = true;
    private int curPage = 1;
    private int mPrePageId = 0;
    private boolean isAsc = true;// 默认排序
    private ViewGroup mHeadLayout;
    private StickyNavLayout mStickyNavLayout;

    private LottieAnimationView mLottieViewVipToast;
    private TextView mTvLiteAlbumCommentCounts;
    private boolean loadDataWhileResume = UserInfoMannage.hasLogined();

    private boolean mIsNeedReloadPageData;

    private boolean isFirstManualSelectItem = true;//是否是第一次设置选中目录页还是简介界面

    private boolean mIsNeedResetStickLayout = false;

    private SubscribeButtonWaveView vSubscribeWaveView;

    public TraceAlbumPageModel mTrackAlbumPageModel = new TraceAlbumPageModel();

    private HorizontalScrollViewInSlideView mHorizontalScrollTags;
    public long mCommentTotalCounts = 0;

    //引导蒙层相关的Views开始
    private RelativeLayout rlMask;
    private XmLottieAnimationView lottieViewGuide;
    //引导蒙层相关的Views结束

    private Typeface typeface;

    private IXmPlayerStatusListenerImpl mPlayerStatusListener;
    private static final String COMMENT_TXT = "评论";

    private final TraceHelper mTraceHelper = new TraceHelper("专辑页");
    //专辑活动入口
    private ImageView mAlbumCommericalIV;
    private boolean isLoadingUnlockHintInfo;
    private boolean isNeedRequestUnlockInfo;

    // 分享奖励
    private ShareRewardComponent mShareRewardComponent;

    private LinearLayout mShareGuideTip;
    private AnimatorSet mShareGuideTipsAnimal;
    private FrameLayout mFmMaskStartPlay;

    //看原著
    private View mConductiveIntoReadView;
    private TextView mConductiveText;
    private TextView mConductiveBtn;

    private AnchorPullNewManager mAnchorPullNewManager = new AnchorPullNewManager();

    //专辑订阅监听
    private final AlbumEventManage.CollectListener mAlbumCollectListener = new AlbumEventManage.CollectListener() {
        @Override
        public void onCollectChanged(boolean collect, long id) {
            if (mData == null) {
                return;
            }
            if (mData.getId() != id) {
                return;
            }
            //是当前专辑,更新当前订阅状态
            mData.setFavorite(collect);
            //更新专辑订阅状态
            if (canUpdateUi()) {
                setSubscribeButton(mData);
            }
        }
    };

    private final ILoginStatusChangeListener mLoginStatusChangeListener = new ILoginStatusChangeListener() {
        @Override
        public void onLogout(LoginInfoModelNew olderUser) {
            //dealWithUnlockRequest();
        }

        @Override
        public void onLogin(LoginInfoModelNew model) {
            //dealWithUnlockRequest();
        }

        @Override
        public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {
        }
    };

    public LiteAlbumFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_RELATIVELAYOUT, null);
    }

    /**
     * @param title      专辑名称
     * @param albumId    专辑id
     * @param from       页面来源
     * @param playSource 声音播放来源
     * @param unreadNum  feed页未读声音数
     */
    public static LiteAlbumFragment newInstance(String title, long albumId, int from, int playSource, int unreadNum) {
        return newInstance(title, null, null, albumId, from, playSource, unreadNum);
    }

    /**
     * @param title      专辑名称
     * @param albumId    专辑id
     * @param from       页面来源
     * @param playSource 声音播放来源
     */
    public static LiteAlbumFragment newInstance(String title, long albumId, int from, int playSource) {
        return newInstance(title, null, null, albumId, from, playSource, -1);
    }

    public static LiteAlbumFragment newInstance(String title, String recSrc, String recTrack, long albumId, int from, int playSource, int unreadNum) {
        return newInstance(title, recSrc, recTrack, albumId, from, playSource, unreadNum, null);
    }

    public static LiteAlbumFragment newInstance(String title, String recSrc, String recTrack, long albumId, int from, int playSource, int unreadNum, AlbumEventManage.AlbumFragmentOption option) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_TITLE, title);
        bundle.putLong(BundleKeyConstants.KEY_ALBUM_ID, albumId);
        bundle.putString(BundleKeyConstants.KEY_REC_SRC, recSrc);
        bundle.putString(BundleKeyConstants.KEY_REC_TRACK, recTrack);
        bundle.putInt(BundleKeyConstants.KEY_PLAY_SOURCE, playSource);
        bundle.putInt(BundleKeyConstants.KEY_FROM, from);
        bundle.putInt(BundleKeyConstants.KEY_NEW_TRACK_COUNT, unreadNum);
        bundle.putSerializable(BundleKeyConstants.KEY_OPTION, option);
        LiteAlbumFragment fra = new LiteAlbumFragment();
        fra.setArguments(bundle);
        return fra;
    }

    @Override
    protected String getPageLogicName() {
        return "LiteAlbumFragment";
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mTraceHelper.postPageStartNode();
    }

    private ImageView mBackButton;
    private MarqueeTextView vPageTitle;

    protected void initTitleBar() {
        View vTitleBar = findViewById(R.id.main_title_bar);
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            ViewGroup.LayoutParams titleBarLayoutParams = vTitleBar.getLayoutParams();
            if (null != titleBarLayoutParams) {
                titleBarLayoutParams.height = titleBarLayoutParams.height + BaseUtil.getStatusBarHeight(mContext);
                vTitleBar.setLayoutParams(titleBarLayoutParams);
                vTitleBar.setPadding(0, BaseUtil.getStatusBarHeight(mContext), 0, 0);
            }
        }
        if (vTitleBar != null && vTitleBar.getBackground() != null) {
            vTitleBar.getBackground().setAlpha(0);
        }
        mBackButton = findViewById(R.id.main_album_back_btn);
        vPageTitle = findViewById(R.id.main_album_single_page_title);
    }


    private void parseBundle() {
        mBundle = getArguments();
        if (mBundle != null) {
            mAlbumId = mBundle.getLong(BundleKeyConstants.KEY_ALBUM_ID, -1);
            mFrom = mBundle.getInt(BundleKeyConstants.KEY_FROM, -1);
            newTrackCount = mBundle.getInt(BundleKeyConstants.KEY_NEW_TRACK_COUNT);
            Album album = mBundle.getParcelable(BundleKeyConstants.KEY_ALBUM);
            if (album instanceof AlbumM) {
                mData = (AlbumM) album;
            }
            if (album != null && mAlbumId <= 0) {
                mAlbumId = album.getId();
                mBundle.putLong(BundleKeyConstants.KEY_ALBUM_ID, mAlbumId);
            }
            mRequestCode = mBundle.getInt(AppConstants.REQUEST_CODE_KEY_ALBUM_FRAGMENT);
        }
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_net_error_title_bar;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_lite_fra_album;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        parseBundle();
        initTitleBar();
        mPlayerStatusListener = new IXmPlayerStatusListenerImpl() {

            @Override
            public void onPlayStart() {
                notifyPlayStatusChanged();
            }

            @Override
            public void onPlayPause() {
                notifyPlayStatusChanged();
            }
        };

        XmPlayerManager.getInstance(mActivity).addPlayerStatusListener(mPlayerStatusListener);
        initViews();

        typeface = Typeface.createFromAsset(getResourcesSafe().getAssets(), "fonts/DIN_Alternate_Bold.ttf");

        isAsc = SharedPreferencesUtil.getInstance(mContext).getBoolean(PreferenceConstantsInHost.KEY_IS_ASC + mAlbumId, isAsc);
        StatusBarManager.setStatusBarColorDelay(getWindow(), false, this);

        //页面数据绑定
        bindPageData();

        //注册解锁成功回调
        UnlockSuccessCallBackManager.getInstance().registerGlobalTrackSuccess(this);

        //添加专辑订阅监听，全局监听，页面隐藏不移除监听
        AlbumEventManage.addListener(mAlbumCollectListener);

        UserInfoMannage.getInstance().addLoginStatusChangeListener(mLoginStatusChangeListener);

        mShareRewardComponent = new ShareRewardComponent(this, R.layout.main_album_detail_gold_coin_anim_and_guide,
                findViewById(R.id.main_fl_share_reward), mAlbumShare, false, "albumPage");

    }

    /**
     * 初始化viewPager,懒加载数据
     */
    private void initViewPager(AlbumM album) {
        if (album == null) {
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putAll(mBundle);
        if (mTrack != null) {
            bundle.putParcelable(BundleKeyConstants.KEY_TRACK, mTrack);
        }
        bundle.putParcelable(BundleKeyConstants.KEY_ALBUM, album);
        bundle.putBoolean("isNoCopyright", album.isNoCopyright());

        final ArrayList<FragmentHolder> fragmentlist = new ArrayList<>();
        ArrayList<String> titles = new ArrayList();
        titles.add("目录");
        fragmentlist.add(new FragmentHolder(LiteAlbumFragmentNewList.class, titles.get(titles.size() - 1), bundle));
        if (!album.isInBlacklist() && !CheckFunSwitchControlManager.isCloseAlbumOrTrackCommentForConfigCenter(album) && !ChildProtectManager.isChildProtectOpen(mContext)) {
            titles.add(COMMENT_TXT);
            bundle.putLong(AlbumCommentTabFragment.COMMENT_TOTAL_COUNTS, mCommentTotalCounts);
            fragmentlist.add(new FragmentHolder(AlbumCommentTabFragment.class, titles.get(titles.size() - 1), bundle));
            if (mCommentTotalCounts != 0) {
                updateAlbumCommentCounts(StringUtil.getFriendlyNumStrEn(mCommentTotalCounts));
            }
        }
        if (album.getEbookInfo() != null && album.getEbookInfo().getBookId() != 0 && !TextUtils.isEmpty(album.getEbookInfo().getH5Url()) && !ChildProtectManager.isChildProtectOpen(mContext)) {
            titles.add("看原著");
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, album.getEbookInfo().getH5Url());
            bundle.putBoolean(BundleKeyConstants.KEY_SHOW_TITLE, false);
            bundle.getBoolean("embedded", true);
            fragmentlist.add(new FragmentHolder(LiteAlbumLookEbookFragment.class, titles.get(titles.size() - 1), bundle));
        }
        int selectItem = 0;
        if (isFirstManualSelectItem) {
            isFirstManualSelectItem = false;
            //有声书类型
            if (album.getCategoryId() == 3) {
                //XmPlayerManager.getInstance(mActivity).getLastPlayTrackInAlbum(mAlbumId);
                if (album.isFavorite() || mTrack != null) {
                    //有收藏过或者播放过，选中目录界面
                    selectItem = 0;
                }
            } else {
                //选中目录界面
                selectItem = 0;
            }
        } else {
            selectItem = mViewPager.getCurrentItem();
        }
        //为0的时候，不会触发onPageSelected，手动埋一个点
        if (selectItem == 0) {
            new XMTraceApi.Trace()
                    .setMetaId(32241)
                    .setServiceId("others")
                    .put("albumId", String.valueOf(mAlbumId))
                    .put("tabId", String.valueOf((selectItem + 1)))
                    .createTrace();
        }

        mPagerAdapter = new TabCommonAdapter(getChildFragmentManager(), fragmentlist);
        mViewPager.setAdapter(mPagerAdapter);
        mIndicator.setViewPager(mViewPager);
        mIndicator.setOnTabClickListener(position -> {
            String title = fragmentlist.get(position).title;
            new XMTraceApi.Trace()
                    .setMetaId(32295)
                    .setServiceId("click")
                    .put("albumId", String.valueOf(mAlbumId))
                    .put("text", title)
                    .createTrace();
        });

        int titleSize = titles.size();
        if (titleSize >= 3) {
            mViewPager.setOffscreenPageLimit(titleSize - 1);
        }

        rlIndicatorAndPlayBtn.setVisibility(View.VISIBLE);

        mIndicatorTopBorder.setVisibility(View.GONE);

        mViewPager.setCurrentItem(selectItem);
//        if (mIsNeedResetStickLayout && mStickyNavLayout != null) {
//            mIsNeedResetStickLayout = false;
//            mStickyNavLayout.resetCurrentPageStatus();
//        }

        //initViewPager会重新创建viewpage中的Fragment，每次重新创建的时候都需要重置
        if (mStickyNavLayout != null) {
            mStickyNavLayout.resetCurrentPageStatus();
        }
        updatePlayControl(mTrack);
        if (titles.contains(COMMENT_TXT)) {
            dealWithCommentCountsLocation();
        }
    }

    /**
     * 处理评论显示的数字
     */
    private void dealWithCommentCountsLocation() {
        if (mTvLiteAlbumCommentCounts == null || mIndicator == null) {
            return;
        }
        mIndicator.post(new Runnable() {
            @Override
            public void run() {
                try {
                    if (mIndicator.getChildAt(0) instanceof LinearLayout) {
                        LinearLayout parentLayout = (LinearLayout) mIndicator.getChildAt(0);
                        View childDirectory = parentLayout.getChildAt(0);
                        View childComment = parentLayout.getChildAt(1);
                        int directoryWidth = childDirectory != null ? childDirectory.getMeasuredWidth() : 0;
                        int commentWidth = childComment != null ? childComment.getMeasuredWidth() : 0;
                        if (mTvLiteAlbumCommentCounts.getLayoutParams() instanceof RelativeLayout.LayoutParams) {
                            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mTvLiteAlbumCommentCounts.getLayoutParams();
                            layoutParams.setMarginStart(directoryWidth + commentWidth * 3 / 4);
                            mTvLiteAlbumCommentCounts.setLayoutParams(layoutParams);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    public void setCurrentItem(int index) {
        if (mViewPager != null) {
            mViewPager.setCurrentItem(index, true);
        }
    }

    private void initViews() {
        int topOffset = BaseUtil.dp2px(mContext, 50);
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            int statusBarHeight = BaseUtil.getStatusBarHeight(mContext);
            topOffset += statusBarHeight;
        }

        mStickyNavLayout = findViewById(R.id.main_album_stickynav);
        mStickyNavLayout.setTopOffset(topOffset);
        mStickyNavLayout.setVipBarHeight(BaseUtil.dp2px(mContext, 40));
        mStickyNavLayout.setScrollListener(new PageScrollListener(mContext));
        mStickyNavLayout.setAlbumPageSmoothCeiling(true);

        mHeadLayout = findViewById(R.id.main_id_stickynavlayout_topview);
        mAlbumCommericalIV = findViewById(R.id.main_album_commerical);
        findHeaderViews();

        rlIndicatorAndPlayBtn = findViewById(R.id.main_id_stickynavlayout_indicator);
        mIndicator = findViewById(R.id.main_psts_tabs);

        tvPlayControl = findViewById(R.id.main_tv_play_control);
        slvAlbumIntro = findViewById(R.id.main_slv_album_intro);

        tvPlayControl.setOnClickListener(this);
        AutoTraceHelper.bindData(tvPlayControl, mData);

        mViewPager = findViewById(R.id.main_id_stickynavlayout_content);
        mIndicatorBorder = findViewById(R.id.main_indicator_border);
        mIndicatorTopBorder = findViewById(R.id.main_indicator_top_border);
        mLottieViewVipToast = findViewById(R.id.main_lottie_vip_toast);
        mTvLiteAlbumCommentCounts = findViewById(R.id.main_album_count);
        mViewPager.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {

            @Override
            public void onPageSelected(int position) {
                new XMTraceApi.Trace()
                        .setMetaId(32241)
                        .setServiceId("others")
                        .put("albumId", String.valueOf(mAlbumId))
                        .put("tabId", String.valueOf((position + 1)))
                        .createTrace();
                updateAlbumCommentCounts(position);
            }

            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                if (getSlideView() != null) {
                    if (position == 0) {
                        //显示分割线
                        setLineOfContinuePlayVisibility(true);

                        if (positionOffset >= 0) {
                            getSlideView().setSlide(true);
                        } else {
                            getSlideView().setSlide(false);
                        }
                    } else {
                        getSlideView().setSlide(false);
                        //隐藏分割线
                        Fragment fragment = mPagerAdapter.getFragmentAtPosition(0);
                        if (fragment instanceof LiteAlbumFragmentNewList) {
                            boolean continuePlayTipsVisibility = ((LiteAlbumFragmentNewList) fragment).getContinuePlayTipsVisibility();
                            if (continuePlayTipsVisibility) {
                                setLineOfContinuePlayVisibility(false);
                            }
                        }
                    }
                }
            }
        });

        rlMask = findViewById(R.id.main_rl_mask);
        rlMask.setOnClickListener(this);
        lottieViewGuide = findViewById(R.id.main_guide_lottie_view);
        mShareGuideTip = findViewById(R.id.main_share_guide_container);
        mFmMaskStartPlay = findViewById(R.id.main_fm_mask_start_play);
        mConductiveIntoReadView = findViewById(R.id.main_host_cl_read);
        mConductiveText = findViewById(R.id.main_read_tv_text);
        mConductiveBtn = findViewById(R.id.bt_read_text);


    }

    private AnimatorSet mKanYuanZhuBreatheAnimatorSet = null;

    /**
     * 看原著-启动呼吸动效
     */
    private void startKanYuanZhuBreatheAnimator() {
        //没有创建和不可见不启动
        if (mConductiveIntoReadView == null || mConductiveIntoReadView.getVisibility() != View.VISIBLE) {
            return;
        }
        //停止呼吸动效
        stopKanYuanZhuBreatheAnimator();
        if (mKanYuanZhuBreatheAnimatorSet == null) {
            mKanYuanZhuBreatheAnimatorSet = new AnimatorSet();
            int animatorDuration = 320;
            //高度缩小动画
            ObjectAnimator changeSmallY = ObjectAnimator.ofFloat(mConductiveIntoReadView, "scaleY", 1.0f, 0.955f);
            changeSmallY.setDuration(animatorDuration);
            //宽度缩小动画
            ObjectAnimator changeSmallX = ObjectAnimator.ofFloat(mConductiveIntoReadView, "scaleX", 1.0f, 0.955f);
            changeSmallX.setDuration(animatorDuration);

            //高度变大动画
            ObjectAnimator changeBigY = ObjectAnimator.ofFloat(mConductiveIntoReadView, "scaleY", 0.955f, 1.0f);
            changeBigY.setDuration(animatorDuration);
            //宽度变大动画
            ObjectAnimator changeBigX = ObjectAnimator.ofFloat(mConductiveIntoReadView, "scaleX", 0.955f, 1.0f);
            changeBigX.setDuration(animatorDuration);

            //changeSmallX和changeSmallY一起播放
            mKanYuanZhuBreatheAnimatorSet.play(changeSmallY).with(changeSmallX);
            //changeSmallX在changeBigY后执行
            mKanYuanZhuBreatheAnimatorSet.play(changeBigY).after(changeSmallX);
            //changeBigY在600毫秒后执行
            mKanYuanZhuBreatheAnimatorSet.play(changeBigY).after(animatorDuration);
            //changeBigX跟随changeBigY一起执行
            mKanYuanZhuBreatheAnimatorSet.play(changeBigY).with(changeBigX);
            mKanYuanZhuBreatheAnimatorSet.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    FuliLogger.log("呼吸动画==onAnimationStart");
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    FuliLogger.log("呼吸动画==onAnimationEnd");
                    //弹框已经弹出，并且没有被销毁，可以展示
                    if (!isRealVisable()) {
                        return;
                    }
                    HandlerManager.postOnUIThreadDelay(new Runnable() {
                        @Override
                        public void run() {
                            //弹框已经弹出，并且没有被销毁，可以展示
                            //不可见不用弹出延时
                            if (!isRealVisable()) {
                                FuliLogger.log("呼吸动画==禁止延时");
                                return;
                            }
                            FuliLogger.log("呼吸动画==postOnUIThreadDelay1000");
                            startKanYuanZhuBreatheAnimator();
                        }
                    }, animatorDuration);
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    FuliLogger.log("呼吸动画==onAnimationCancel");
                }

                @Override
                public void onAnimationRepeat(Animator animation) {
                    FuliLogger.log("呼吸动画==onAnimationRepeat");
                }
            });
        }
        mKanYuanZhuBreatheAnimatorSet.start();
    }

    /**
     * 看原著-停止呼吸动效
     */
    private void stopKanYuanZhuBreatheAnimator() {
        if (mKanYuanZhuBreatheAnimatorSet != null) {
            mKanYuanZhuBreatheAnimatorSet.cancel();
        }
    }

    private long getCurAlbumPlayingTrackId() {
        long playingTrackId = -1;
        long albumId = -1;
        Track curTrack = null;
        XmPlayerManager playerManager = XmPlayerManager.getInstance(mContext);
        if (playerManager.isPlaying()) {
            PlayableModel currSound = playerManager.getCurrSound();
            if (currSound instanceof Track) {
                curTrack = (Track) currSound;
                SubordinatedAlbum album = curTrack.getAlbum();
                if (null != album) {
                    albumId = album.getAlbumId();
                }
            }
            if (albumId == mAlbumId && curTrack != null) {
                playingTrackId = curTrack.getDataId();
            }
        }
        return playingTrackId;
    }

    /**
     * 请求专辑信息
     */
    private void requestAlbumInfo() {
        if (canUpdateUi() && isFirstLoading) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        Map<String, String> params = new HashMap<>();
        isAsc = SharedPreferencesUtil.getInstance(mContext).getBoolean(PreferenceConstantsInHost.KEY_IS_ASC + mAlbumId, isAsc);
        params.put(HttpParamsConstants.PARAM_IS_ASC, String.valueOf(isAsc));
        params.put(HttpParamsConstants.PARAM_IS_VIDEO_ASC, String.valueOf(true));
        params.put(DTransferConstants.PAGE, curPage + "");
        params.put(DTransferConstants.PRE_PAGE, mPrePageId + "");//不要随便改，改前问我 by Hovi
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, HttpParamsConstants.NEW_DEFAULT_PAGE_SIZE + "");
        params.put(HttpParamsConstants.PARAM_URL_FROM, AlbumEventManage.URL_FROM_ALBUM_HOMEPAGE);
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, mAlbumId + "");
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        params.put(HttpParamsConstants.PARAM_SOURCE, String.valueOf(AlbumEventManage.getAlbumFrom(mFrom)));
        params.put(HttpParamsConstants.PARAM_AC, NetworkUtils.getNetworkClass(mActivity).toUpperCase());
        params.put(HttpParamsConstants.PARAM_SUPPORT_WEBP, String.valueOf(DeviceUtil.isDeviceSupportWebP()));
        mTrack = XmPlayerManager.getInstance(mActivity).getLastPlayTrackInAlbum(mAlbumId);
        if (mTrack != null) {
            params.put(HttpParamsConstants.PARAM_TRACK_ID, mTrack.getDataId() + "");
        }
        //增加此参数，服务端会清楚最近更新
        if (AlbumEventManage.getAlbumFrom(mFrom) == AlbumEventManage.FROM_FEEDPAGE && newTrackCount > 0) {
            params.put(HttpParamsConstants.PARAM_NEWTRACKCOUNT, String.valueOf(newTrackCount));
        }
        // 如果有正在播放的声音，则进入详情页时，会显示正在播放声音所在页的声音列表。
        if (getCurAlbumPlayingTrackId() != -1) {
            params.put("playingTrackId", String.valueOf(getCurAlbumPlayingTrackId()));
        }
        CommonRequestM.getAlbumInfo(params, new IDataCallBack<AlbumM>() {
            @Override
            public void onSuccess(final AlbumM object) {
                if (!canUpdateUi()) {
                    notifyTraceFinish();
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            notifyTraceFinish();
                            return;
                        }
                        if (null == object && isFirstLoading) {
                            notifyTraceFinish();
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                        } else if (object != null) {
                            mData = object;
                            mAnchorPullNewManager.checkShowActivity(mAlbumId, findViewById(R.id.main_host_cl_anchor));
                            // 之前没有专辑信息  解锁信息没有请求成功 重新触发
                            if (isNeedRequestUnlockInfo) {
                                isNeedRequestUnlockInfo = false;
                                //dealWithUnlockRequest();
                            }
                            setDataForView();
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            notifyTraceSuccess();
                        }
                        isFirstLoading = false;
                    }
                });
            }

            @Override
            public void onError(final int code, final String message) {
                notifyTraceFinish();
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        if (isFirstLoading) {
                            CustomToast.showFailToast(message);
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                        } else {
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            CustomToast.showFailToast(message);
                        }
                    }
                });
            }
        });
    }

    private void notifyTraceFinish() {
        mTraceHelper.notifyPageFailed();
    }

    private void notifyTraceSuccess() {
        mTraceHelper.postPageEndNodeAfterRenderComplete(getView());
    }

    @Override
    protected void loadData() {
        requestAlbumInfo();
        requestCommentCounts();
    }

    private void requestCommentCounts() {
        CommentPagesRequest.Companion.getAlbumCommentCount(mAlbumId, new IDataCallBack<Long>() {
            @Override
            public void onSuccess(@Nullable Long result) {
                if (result == null || result == 0) return;
                mCommentTotalCounts = result;
                try {
                    Fragment fragment = mPagerAdapter.getFragmentAtPosition(1);
                    if (fragment instanceof AlbumCommentTabFragment) {
                        ((AlbumCommentTabFragment) fragment).updateCommentTotalCounts(result);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                updateAlbumCommentCounts(StringUtil.getFriendlyNumStrEn(mCommentTotalCounts));
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    private void dealWithUnlockRequest() {
        //已经是vip不发起请求  只有vip专辑专辑请求解锁接口  非会员请求解锁
        if (isLoadingUnlockHintInfo) {
            FuliLogger.log(TAG, "请求中,拦截");
            return;
        }
        if (mData == null) {
            FuliLogger.log(TAG, "无专辑数据,等待");
            isNeedRequestUnlockInfo = true;
            return;
        }

        if (mData.isVipAlbum() && !UserInfoMannage.isVipUser()) {
            isLoadingUnlockHintInfo = true;
            VipTrackUnLockPaidManager.getAlbumHintUnLock(mAlbumId, null, new IDataCallBack<AlbumPaidUnLockHintInfo>() {
                @Override
                public void onSuccess(@Nullable AlbumPaidUnLockHintInfo object) {
                    isLoadingUnlockHintInfo = false;
                    mAlbumPaidUnLockHintInfo = object;
                    //performUnlockTips();
                }

                @Override
                public void onError(int code, String message) {
                    FuliLogger.log(TAG, "获取解锁信息失败, code:" + code + " msg:" + message);
                    isLoadingUnlockHintInfo = false;
                    mAlbumPaidUnLockHintInfo = null;
                    //performUnlockTips();
                }
            });
        } else {
            FuliLogger.log(TAG, "非vip专辑 或者会员用户,不显示");
            mAlbumPaidUnLockHintInfo = null;
            //performUnlockTips();
        }
    }

    private void setDataForView() {
        if (mData == null) {
            return;
        }
        setTitleView();

        initHeaderAlbumInfoViews();

        setHeaderAlbumInfoViews();
        setCommercialEntrance();
        initViewPager(mData);

    }

    //专辑页面-简介和目录上方活动入口
    private void setCommercialEntrance() {
        if (mData == null) {
            mAlbumCommericalIV.setVisibility(View.GONE);
            return;
        }
        CommercialEntrance commercialEntrance = mData.getCommercialEntrance();
        if (commercialEntrance != null && StringUtil.isNotBlank(commercialEntrance.getLink()) && AlbumUtils.isShowAlbumPageEntrancePageState(getAlbumM())) {
            String link = commercialEntrance.getLink();
            mAlbumCommericalIV.setOnClickListener(v -> {
                // 专辑页-列表顶活动banner  点击事件
                new XMTraceApi.Trace()
                        .click(41704)
                        .put("currPage", "albumPage")
                        .createTrace();
                ToolUtil.clickUrlAction(LiteAlbumFragment.this, link, mAlbumCommericalIV);
            });
            String imageUrl = commercialEntrance.getImageUrl();
            ImageManager.from(mContext).displayImage(mAlbumCommericalIV, imageUrl, -1, new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                    mAlbumCommericalIV.setVisibility(View.VISIBLE);
                    // 专辑页-列表顶活动banner  控件曝光
                    new XMTraceApi.Trace()
                            .setMetaId(41705)
                            .setServiceId("slipPage")
                            .put("currPage", "albumPage")
                            .put("exploreType", "albumPage")
                            .createTrace();
                }
            }, false);
        } else {
            mAlbumCommericalIV.setVisibility(View.GONE);
        }
    }

    public void updateAlbumCommentCounts(String count) {
        if (mData == null || mData.isInBlacklist() || CheckFunSwitchControlManager.isCloseAlbumOrTrackCommentForConfigCenter(mData)) {
            return;
        }
        mTvLiteAlbumCommentCounts.setText(count);
        mTvLiteAlbumCommentCounts.setVisibility(View.VISIBLE);
    }

    public void updateAlbumCommentCounts(int position) {
        try {
            Fragment fragment = mPagerAdapter.getFragmentAtPosition(position);
            if (fragment instanceof AlbumCommentTabFragment) {
                mTvLiteAlbumCommentCounts.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                mTvLiteAlbumCommentCounts.setTextColor(getResources().getColor(R.color.black));
            } else {
                mTvLiteAlbumCommentCounts.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                mTvLiteAlbumCommentCounts.setTextColor(getResources().getColor(R.color.host_color_999999));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setTitleView() {
        if (mData != null) {
            vPageTitle.setText(mData.getAlbumTitle());
            mBackButton.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    new XMTraceApi.Trace()
                            .setMetaId(4345)
                            .setServiceId("click")
                            .createTrace();
                    finishFragment();
                }
            });
        }
    }

    ///////////////////////////////header album info views//////////////////////////////////

    private CardView cvAlbumCoverGroup;

    private ConstraintLayout clRightPart;
    private ConstraintLayout mAlbumIntroContainer;

    private PlayPageBackgroundView mBackgroundView;//背景

    private FrameLayout flAlbumSubscribe;
    private ConstraintLayout llStar;
    private ConstraintLayout llPlayCount;
    private ConstraintLayout llSubscribeCount;

    private ImageView ivAlbumCover;
    private TextView tvAlbumPlayCount;

    private TextView tvAlbumTitle;

    private TextView tvRecommendReason;//推荐理由

    private TextView tvAlbumScore;//评分

    private TextView tvSubscribeCount;
    private TextView mTvSubscribeBtn;
    private RelativeLayout mRlSubscribe;
    private TextView mAlbumShare;

    private RelativeLayout rlSubscribeInTitleBar;
    private TextView tvSubscribeInTitleBar;

    private LinearLayout mLlAllTags;
    private TextView mTvTagFree;
    private TextView mTvTag1;
    private TextView mTvTag2;
    private TextView mTvTag3;
    private ImageView mIvVipTag;
    private ImageView mIvVipBoard;


    ///////////////////////////////header album info views//////////////////////////////////

    private void findHeaderViews() {
        mBackgroundView = findViewById(R.id.main_background_view);
        mBackgroundView.setDefaultColor(mDefaultBgColor);
        cvAlbumCoverGroup = findViewById(R.id.main_album_single_cover_group);
        clRightPart = findViewById(R.id.main_cl_right_part);

        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            int statusBarHeight = BaseUtil.getStatusBarHeight(mContext);
            ConstraintLayout.LayoutParams lp = ((ConstraintLayout.LayoutParams) cvAlbumCoverGroup.getLayoutParams());
            Logger.i(TAG, "findHeaderViews lp.topMargin =" + lp.topMargin + " statusBarHeight = " + statusBarHeight);
            lp.topMargin += statusBarHeight;
            cvAlbumCoverGroup.setLayoutParams(lp);
        }

        flAlbumSubscribe = findViewById(R.id.main_fl_album_subscribe);

        llStar = findViewById(R.id.main_ll_star);
        llStar.setOnClickListener(this);
        llPlayCount = findViewById(R.id.main_ll_play_count);
        llSubscribeCount = findViewById(R.id.main_ll_subscribe_count);

        ivAlbumCover = findViewById(R.id.main_album_single_album_cover);

        tvAlbumTitle = findViewById(R.id.main_album_single_album_title);

        tvRecommendReason = findViewById(R.id.main_tv_recommend_reason);

        tvAlbumPlayCount = findViewById(R.id.main_tv_play_count);

        mAlbumShare = findViewById(R.id.main_album_share);
        mIvVipTag = findViewById(R.id.main_iv_space_album_tag);

        tvAlbumScore = findViewById(R.id.main_tv_album_score);

        tvSubscribeCount = findViewById(R.id.main_album_single_subscribe_numb);

        vSubscribeWaveView = findViewById(R.id.main_album_single_subscribe_wave_view);

        mRlSubscribe = findViewById(R.id.main_rl_album_subscribe);
        mTvSubscribeBtn = findViewById(R.id.main_tv_album_subscribe);

        mTvSubscribeBtn.setTextSize(TextSizeUtils.autoTextSize(14, 1.1f));

        rlSubscribeInTitleBar = findViewById(R.id.main_rl_subscribe_in_title_bar);

        tvSubscribeInTitleBar = findViewById(R.id.main_tv_subscribe_in_title_bar);
        tvSubscribeInTitleBar.setTextSize(TextSizeUtils.autoTextSize(14, 1.1f));

        mHorizontalScrollTags = findViewById(R.id.main_horizontal_scroll_tags);
        mHorizontalScrollTags.setDisallowInterceptTouchEventView(((ViewGroup) getView()));

        mHorizontalScrollTags.setScrollChangedListener(new HorizontalScrollViewInSlideView.ScrollChangedListener() {
            @Override
            public void onScrollChanged(int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                //Logger.i(TAG, "onScrollChanged scrollX = " + scrollX + " oldScrollX = " + oldScrollX);
                if (scrollX > 0) {
                    //展示左边的阴影，隐藏右边的阴影


                } else {
                    //隐藏左边的阴影，展示右边的阴影

                }
            }
        });

        mLlAllTags = findViewById(R.id.main_ll_album_tags);
        mTvTagFree = findViewById(R.id.main_tv_tag_free);
        mTvTag1 = findViewById(R.id.main_tv_tag_1);
        mTvTag2 = findViewById(R.id.main_tv_tag_2);
        mTvTag3 = findViewById(R.id.main_tv_tag_3);
        mAlbumIntroContainer = findViewById(R.id.main_cl_desc_part);
        mIvVipBoard = findViewById(R.id.main_iv_vip_border);

        mHeadLayout.addOnLayoutChangeListener(this);
        ivAlbumCover.setOnClickListener(this);

        mRlSubscribe.setOnClickListener(this);
        tvSubscribeInTitleBar.setOnClickListener(this);
        mAlbumShare.setOnClickListener(this);
        mTvTagFree.setOnClickListener(this);

        if (ChildProtectManager.isChildProtectOpen(mContext)) {
            flAlbumSubscribe.setVisibility(View.INVISIBLE);
        }
    }

    private void initHeaderAlbumInfoViews() {
        if (!canUpdateUi()) {
            return;
        }
        if (isFirstLoading) {
            AutoTraceHelper.bindData(tvSubscribeInTitleBar, mData);
            AutoTraceHelper.bindData(mTvSubscribeBtn, mData);
            AutoTraceHelper.bindData(mAlbumShare, mData);
            AutoTraceHelper.bindData(ivAlbumCover, mData);
            checkAndShowSubscribeDialogOrBubbleTips();
        }
    }

    /**
     * 检查是展示收藏弹层提示还是气泡提示
     */
    private void checkAndShowSubscribeDialogOrBubbleTips() {
        if (!canUpdateUi()) {
            return;
        }
        boolean isSubscribe = false;
        if (mData != null && UserInfoMannage.hasLogined()) {
            isSubscribe = mData.isFavorite();
        }
        if (isSubscribe) {
            return;
        }
        AlbumListenSubscribeManager.checkAndShowSubscribeDialogOrBubbleTips(mData, new AlbumListenSubscribeManager.CheckResultCallBack() {
            @Override
            public void showDialog() {
                Logger.i(TAG, "checkAndShowSubscribeDialogOrBubbleTips showDialog");
                showSubscribeDialog();
            }

            @Override
            public void showBubble() {
                //不再展示tips小气泡
            }
        });
    }

    private void showSubscribeDialog() {
        if (mData != null) {
            SubscribeTipsBottomDialog bottomDialog = new SubscribeTipsBottomDialog(mData);
            bottomDialog.setSubscribeAction(new Function1<Album, Unit>() {
                @Override
                public Unit invoke(Album album) {
                    dealWithAlbumSubscribeClick(null);
                    return null;
                }
            });
            AlbumListenSubscribeManager.saveTodaySubscribeTipsInfo(mData.getId());
            bottomDialog.show(getChildFragmentManager(), "");
        }
    }

    public AlbumM getAlbumM() {
        return mData;
    }

    private void setHeaderAlbumInfoViews() {
        if (mData == null) {
            return;
        }
        int size = (int) getResources().getDimension(R.dimen.main_album_fragment_album_cover_width);
        ImageManager.from(mContext).displayImage(ivAlbumCover, mData.getLargeCover(),
                R.drawable.host_album_default_1_145, size, size);

        updateBackground(mData);

        tvAlbumTitle.setText(mData.getAlbumTitle());

        if (mData.getIsFinished() == 2) {
            SpannableStringBuilder ssb = new SpannableStringBuilder("完结");
            Drawable tagEnd = ContextCompat.getDrawable(mContext, R.drawable.main_ic_finish_tag);
            if (tagEnd != null) {
                tagEnd.setBounds(0, 0, BaseUtil.dp2px(mContext, 32), BaseUtil.dp2px(mContext, 18));
                CenterAlignImageSpan tagComplete = new CenterAlignImageSpan(tagEnd);
                ssb.setSpan(tagComplete, 0, ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                ssb.append(" ");
                SpaceSpan spaceSpan = new SpaceSpan(BaseUtil.dp2px(mContext, 6));
                ssb.setSpan(spaceSpan, ssb.length() - 1, ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                ssb.append(mData.getAlbumTitle());
                tvAlbumTitle.setText(ssb);
            }
        } else {
            tvAlbumTitle.setText(mData.getAlbumTitle());
        }

        String recReason = mData.getRecReason();
        if (TextUtils.isEmpty(recReason)) {
            tvRecommendReason.setVisibility(View.GONE);
        } else {
            tvRecommendReason.setVisibility(View.VISIBLE);
            tvRecommendReason.setText(recReason);
        }

        setRatingScore();//设置评分
        setPlayCount();//播放量
        setSubscribedCount(mData);//订阅数

        setAndStartWaveViewAnim();

        setSubscribeButton(mData);

        setUpTags(mData);
        setUpVipViews(mData);
        updateAlbumIntroLayout();
        //updateConductiveReadView();

    }

    private void setAndStartWaveViewAnim() {
        if (!canUpdateUi() || vSubscribeWaveView == null) {
            return;
        }
        boolean isSubscribe = false;
        if (mData != null && UserInfoMannage.hasLogined()) {
            isSubscribe = mData.isFavorite();
        }
        if (!isSubscribe) {
            if (ChildProtectManager.isChildProtectOpen(mContext)) {
                vSubscribeWaveView.setVisibility(View.INVISIBLE);
            } else {
                vSubscribeWaveView.setVisibility(View.VISIBLE);
                vSubscribeWaveView.stop();
                mRlSubscribe.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        if (mRlSubscribe.getMeasuredWidth() == 0) {
                            return;
                        }
                        vSubscribeWaveView.start();
                    }
                }, 200);
            }
        } else {
            vSubscribeWaveView.setVisibility(View.INVISIBLE);
            vSubscribeWaveView.stop();
        }
    }

    private void stopWaveViewAnim() {
        if (!canUpdateUi() || vSubscribeWaveView == null) {
            return;
        }
        vSubscribeWaveView.stop();
    }

    private void updateAlbumIntroLayout() {
        String albumIntro = "感谢您收听，喜欢记得订阅哦，第一时间获取最新节目动态！";
        if (mData != null && !TextUtils.isEmpty(mData.getAlbumIntro())) {
            albumIntro = mData.getAlbumIntro();
        }
        int width = BaseUtil.getScreenWidth(mContext) - BaseUtil.dp2px(mContext, 24f);
        TextPaint textPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        textPaint.setTextSize(BaseUtil.sp2px(mContext, 15f));
        textPaint.setColor(ContextCompat.getColor(mContext, R.color.main_color_FFFFFF));
        StaticLayout layout = StaticLayoutManager.getInstance().getLimitLayout(albumIntro, 2, width, textPaint, 1.5f, new IHandleOk() {
            @Override
            public void onReady() {
                LiteFullIntroDialog completeIntroductionDialog = LiteFullIntroDialog.newInstance(mData);
                completeIntroductionDialog.show(getChildFragmentManager(), "");
                // 专辑页-新-完整介绍  点击事件
                new XMTraceApi.Trace()
                        .click(46820) // 用户点击时上报
                        .put("albumId", String.valueOf(mAlbumId))
                        .put("currPage", "albumPage")
                        .createTrace();

            }
        });
        slvAlbumIntro.setLayout(layout);
        slvAlbumIntro.invalidate();
    }

    /**
     * 加载vip相关的view
     */
    private void setUpVipViews(AlbumM object) {
        if (object == null)
            return;
        mIvVipTag.setVisibility(View.VISIBLE);

        int albumCoverTagId = AlbumTagUtil.getAlbumCoverTag(object);
        if (albumCoverTagId != -1) {
            mIvVipTag.setImageResource(albumCoverTagId);
        }

        if (object.isVipFree() || object.getVipFreeType() == 1 && UserInfoMannage.isVipUser()) {
            mIvVipBoard.setVisibility(View.VISIBLE);
        }
        // 说明是vip专辑
        if (object.isVipFree() || object.getVipFreeType() == 1) {
            // VIP用户进入VIP专辑，且不是从播放页返回，展示会员提示条
            if (object.isVip() && UserInfoMannage.hasLogined() && mFrom != AlbumEventManage.FROM_ALBUM_BELONG) {
                if (getView() != null) {
                    getView().postDelayed(new Runnable() {

                        @Override
                        public void run() {
                            if (!canUpdateUi()) {
                                return;
                            }
                            mLottieViewVipToast.setVisibility(View.VISIBLE);
                            mLottieViewVipToast.removeAllAnimatorListeners();
                            mLottieViewVipToast.addAnimatorListener(new AnimatorListenerAdapter() {
                                @Override
                                public void onAnimationCancel(Animator animation) {
                                    super.onAnimationCancel(animation);
                                    if (mLottieViewVipToast != null && canUpdateUi()) {
                                        mLottieViewVipToast.setVisibility(View.GONE);
                                    }
                                }

                                @Override
                                public void onAnimationEnd(Animator animation) {
                                    super.onAnimationEnd(animation);
                                    if (mLottieViewVipToast != null && canUpdateUi()) {
                                        mLottieViewVipToast.setVisibility(View.GONE);
                                    }
                                }
                            });
                            mLottieViewVipToast.playAnimation();
                        }
                    }, 500);
                }
            }
        }
    }

    private void updateConductiveReadView() {
        if (mConductiveIntoReadView == null || mData == null) return;
        String AlbumDrainage = ConfigureCenter.getInstance().getString(CConstants.Group_Base.GROUP_NAME, "AlbumDrainage", "");
        JSONObject albumDrainageJson = null;
        try {
            albumDrainageJson = new JSONObject(AlbumDrainage);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (albumDrainageJson == null) {
            mConductiveIntoReadView.setVisibility(View.GONE);
            return;
        }
        boolean mSwitch = albumDrainageJson.optBoolean("switch", false);
        if (!mSwitch) {
            mConductiveIntoReadView.setVisibility(View.GONE);
            return;
        }
        String text = albumDrainageJson.optString("text", "");
        if (mConductiveText != null && !TextUtils.isEmpty(text)) {
            mConductiveText.setText(text);
        }
        String button = albumDrainageJson.optString("button", "");
        if (mConductiveBtn != null && !TextUtils.isEmpty(button)) {
            mConductiveBtn.setText(button);
        }
    }

    /**
     * 加载标签模块
     */
    private void setUpTags(AlbumM object) {
        if (object == null) {
            return;
        }
        List<TagResult> tagResults;
        // 只选取前两个标签
        final List<ExpandTagResult> tags = new ArrayList<>();
        if (object.getTagResults() != null) {
            tagResults = object.getTagResults();
            for (TagResult tagResult : tagResults) {
                int tagId = tagResult.getTagId();
                List<SearchMetadata> metadataList = tagResult.getMetadataList();
                int metadataId = -1;
                if (!ToolUtil.isEmptyCollects(metadataList)) {
                    for (SearchMetadata searchMetadata : metadataList) {
                        if (searchMetadata.getMetadataValueId() == tagId) {
                            metadataId = searchMetadata.getMetadataId();
                            break;
                        }
                    }
                }
                ExpandTagResult expandTagResult = new ExpandTagResult(tagResult.getTagName(), tagResult.getTagId(), metadataId);
                expandTagResult.setCategoryTag(tagResult.isCategoryTag());
                tags.add(expandTagResult);
            }
        }
        if (ToolUtil.isEmptyCollects(tags) || ChildProtectManager.isChildProtectOpen(mContext)) {
            mLlAllTags.setVisibility(View.GONE);
        } else {
            mLlAllTags.setVisibility(View.VISIBLE);
            if (tags.size() >= 3) {
                mTvTag3.setVisibility(View.VISIBLE);
                mTvTag3.setText(tags.get(2).getTagName());
                mTvTag3.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        jumpToSelectedCategoryContentPage(tags.get(2));
                    }
                });
            }
            if (tags.size() >= 2) {
                mTvTag2.setVisibility(View.VISIBLE);
                mTvTag2.setText(tags.get(1).getTagName());
                mTvTag2.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        jumpToSelectedCategoryContentPage(tags.get(1));
                    }
                });
            }
            mTvTag1.setText(tags.get(0).getTagName());
            mTvTag1.setVisibility(View.VISIBLE);
            mTvTag1.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    jumpToSelectedCategoryContentPage(tags.get(0));
                }
            });
        }
        mLlAllTags.post(new Runnable() {
            @Override
            public void run() {
                Logger.i(TAG, "mLlAllTags width = " + mLlAllTags.getWidth());
                Logger.i(TAG, "mHorizontalScrollTags width = " + mHorizontalScrollTags.getWidth());
            }
        });
    }

    private void jumpToSelectedCategoryContentPage(ExpandTagResult expandTagResult) {
        if (expandTagResult == null) {
            return;
        }

        new XMTraceApi.Trace()
                .setMetaId(32270)
                .setServiceId("click")
                .createTrace();

        //跳转到分类页面
        PlayPageTagCategoryMetadataFragment tabFragment = new PlayPageTagCategoryMetadataFragment();
        int type;
        if (expandTagResult.isCategoryTag()) {
            type = 1;
        } else {
            type = 2;
        }
        int tagId = expandTagResult.getTagId();
        Bundle bundle = PlayPageTagCategoryMetadataFragment.createArguments(expandTagResult.getTagName(), type, tagId, tagId);
        tabFragment.setArguments(bundle);
        startFragment(tabFragment);
    }

    /**
     * 设置评分
     */
    private void setRatingScore() {
        if (mData == null) {
            return;
        }
        String albumScore = mData.getAlbumScore();
        if (TextUtils.isEmpty(albumScore) ||
                "0".equals(albumScore) ||
                "0.0".equals(albumScore) ||
                "0.00".equals(albumScore)
        ) {
            String nonScore = getString(R.string.main_no_rate_now);
            SpannableString ss = new SpannableString(nonScore);
            AbsoluteSizeSpan absoluteSizeSpan = new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 14));
            //StyleSpan boldSpan = new StyleSpan(android.graphics.Typeface.BOLD);
            ss.setSpan(absoluteSizeSpan, 0, nonScore.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
            // ss.setSpan(boldSpan, 0, nonScore.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
            tvAlbumScore.setText(ss);
            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) tvAlbumScore.getLayoutParams();
            layoutParams.bottomMargin = layoutParams.bottomMargin + BaseUtil.dp2px(mContext, 3);
            tvAlbumScore.setLayoutParams(layoutParams);
        } else {
            try {
                BigDecimal bd = new BigDecimal(albumScore);
                //评分保留一位小数
                double d = bd.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
                //注意！！！i是要被SpaceSpan替代的。
                String result = d + "i分";

                SpannableString ss = new SpannableString(result);
                AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 18f));
                StyleSpan boldSpan = new StyleSpan(Typeface.BOLD);
                ss.setSpan(sizeSpan, 0, result.length() - 2, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                ss.setSpan(boldSpan, 0, result.length() - 2, Spanned.SPAN_INCLUSIVE_INCLUSIVE);

                int space = BaseUtil.dp2px(mContext, 2);
                //用来替换上面字符串中的i
                SpaceSpan spaceSpan = new SpaceSpan(space);
                ss.setSpan(spaceSpan, result.length() - 2, result.length() - 1, Spanned.SPAN_INCLUSIVE_INCLUSIVE);

                AbsoluteSizeSpan unitSizeSpan = new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 12f));
                StyleSpan unitBoldSpan = new StyleSpan(Typeface.NORMAL);
                ss.setSpan(unitSizeSpan, result.length() - 1, result.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                ss.setSpan(unitBoldSpan, result.length() - 1, result.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                tvAlbumScore.setTypeface(typeface);
                tvAlbumScore.setText(ss);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void setPlayCount() {
        if (mData == null) {
            return;
        }
        long playCount = mData.getPlayCount();

        if (playCount < 0) {
            tvAlbumPlayCount.setVisibility(View.INVISIBLE);
        } else {
            tvAlbumPlayCount.setVisibility(View.VISIBLE);
            String friendlyNumStr = StringUtil.getFriendlyNumStr(playCount);

            if (playCount < 10000) {
                SpannableString ss = new SpannableString(friendlyNumStr);
                AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 18f));
                StyleSpan boldSpan = new StyleSpan(android.graphics.Typeface.BOLD);
                ss.setSpan(sizeSpan, 0, friendlyNumStr.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                ss.setSpan(boldSpan, 0, friendlyNumStr.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                tvAlbumPlayCount.setTypeface(typeface);
                tvAlbumPlayCount.setText(ss);
            } else {
                //后缀，可能是万，或者亿
                String suffix = friendlyNumStr.substring(friendlyNumStr.length() - 1);
                String content = friendlyNumStr.substring(0, friendlyNumStr.length() - 1);
                String result = content + "i" + suffix;
                Logger.i(TAG, "setPlayCount content = " + content + " suffix =" + suffix + " result =" + result);
                SpannableString ss = new SpannableString(result);
                AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 18f));
                StyleSpan boldSpan = new StyleSpan(android.graphics.Typeface.BOLD);
                ss.setSpan(sizeSpan, 0, result.length() - 2, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                ss.setSpan(boldSpan, 0, result.length() - 2, Spanned.SPAN_INCLUSIVE_INCLUSIVE);

                int space = BaseUtil.dp2px(mContext, 3);
                //用来替换上面字符串中的i
                SpaceSpan spaceSpan = new SpaceSpan(space);
                ss.setSpan(spaceSpan, result.length() - 2, result.length() - 1, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                StyleSpan unitBoldSpan = new StyleSpan(Typeface.NORMAL);
                AbsoluteSizeSpan unitSizeSpan = new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 12f));
                ss.setSpan(unitSizeSpan, result.length() - 1, result.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                ss.setSpan(unitBoldSpan, result.length() - 1, result.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                tvAlbumPlayCount.setTypeface(typeface);
                tvAlbumPlayCount.setText(ss);
            }
        }
    }

    private void setSubscribedCount(AlbumM albumM) {
        if (albumM == null) {
            return;
        }
        tvSubscribeCount.setVisibility(View.VISIBLE);

        long subscribedCount = albumM.getSubscribeCount();
        if (!UserInfoMannage.hasLogined() && AlbumCollectManager.getInstance(mContext).isCollect(albumM)) {
            subscribedCount += 1;
            albumM.setSubscribeCount(subscribedCount);
        }
        String subscribedCountString = StringUtil.getFriendlyNumStr(subscribedCount);
        if (subscribedCount < 10000) {
            SpannableString ss = new SpannableString(subscribedCountString);
            AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 18f));
            StyleSpan boldSpan = new StyleSpan(Typeface.BOLD);
            ss.setSpan(sizeSpan, 0, subscribedCountString.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
            ss.setSpan(boldSpan, 0, subscribedCountString.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
            tvSubscribeCount.setTypeface(typeface);
            tvSubscribeCount.setText(ss);
        } else {
            String suffix = subscribedCountString.substring(subscribedCountString.length() - 1, subscribedCountString.length());
            String content = subscribedCountString.substring(0, subscribedCountString.length() - 1);
            //注意！！！！
            //这个i是要被SpaceSpan替代的，
            String result = content + "i" + suffix;

            Logger.i(TAG, "content = " + content + " suffix =" + suffix + " result =" + result);

            SpannableString ss = new SpannableString(result);
            AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 18f));
            StyleSpan boldSpan = new StyleSpan(Typeface.BOLD);
            ss.setSpan(sizeSpan, 0, result.length() - 2, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
            ss.setSpan(boldSpan, 0, result.length() - 2, Spanned.SPAN_INCLUSIVE_INCLUSIVE);

            int space = BaseUtil.dp2px(mContext, 3);
            //用来替换上面字符串中的i
            SpaceSpan spaceSpan = new SpaceSpan(space);
            ss.setSpan(spaceSpan, result.length() - 2, result.length() - 1, Spanned.SPAN_INCLUSIVE_INCLUSIVE);

            AbsoluteSizeSpan unitSizeSpan = new AbsoluteSizeSpan(BaseUtil.sp2px(mContext, 12f));
            StyleSpan unitBoldSpan = new StyleSpan(Typeface.NORMAL);
            ss.setSpan(unitSizeSpan, result.length() - 1, result.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
            ss.setSpan(unitBoldSpan, result.length() - 1, result.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);

            tvSubscribeCount.setTypeface(typeface);
            tvSubscribeCount.setText(ss);
        }
    }

    private void setSubscribeButton(AlbumM albumM) {
        if (albumM == null) {
            return;
        }
        if (!UserInfoMannage.hasLogined() && AlbumCollectManager.getInstance(mContext).isCollect(albumM)) {
            albumM.setFavorite(true);
        }
        if (mTvSubscribeBtn == null || tvSubscribeInTitleBar == null) {
            return;
        }
        if (albumM.isFavorite()) {
            mTvSubscribeBtn.setText(getString(R.string.main_have_collect));
            mTvSubscribeBtn.setTextColor(mContext.getResources().getColor(R.color.main_color_7fffffff));
            mRlSubscribe.setBackground(LocalImageUtil.getDrawable(mContext, R.drawable.main_round_bg_radius_0dffffff_dp100));

            tvSubscribeInTitleBar.setText(getString(R.string.main_have_collect));
            tvSubscribeInTitleBar.setTextColor(mContext.getResources().getColor(R.color.main_color_7fffffff));

            rlSubscribeInTitleBar.setBackground(LocalImageUtil.getDrawable(mContext, R.drawable.main_round_bg_radius_0dffffff_dp100));

            tvSubscribeInTitleBar.setCompoundDrawables(null, null, null, null);
            mTvSubscribeBtn.setCompoundDrawables(null, null, null, null);
        } else {
            mTvSubscribeBtn.setTextColor(mContext.getResources().getColor(R.color.main_color_666666));
            tvSubscribeInTitleBar.setTextColor(mContext.getResources().getColor(R.color.main_color_666666));

            mTvSubscribeBtn.setText(getString(R.string.main_subscribe));
            tvSubscribeInTitleBar.setText(getString(R.string.main_subscribe));
            mTvSubscribeBtn.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_tag_add), null, null, null);
            tvSubscribeInTitleBar.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_tag_add), null, null, null);

            if (mData.isOfflineHidden()) {
                mRlSubscribe.setEnabled(false);
                tvSubscribeInTitleBar.setEnabled(false);
                mRlSubscribe.setBackground(LocalImageUtil.getDrawable(mContext, R.drawable.main_round_bg_radius_80ffffff_dp100));
                rlSubscribeInTitleBar.setBackground(LocalImageUtil.getDrawable(mContext, R.drawable.main_round_bg_radius_80ffffff_dp100));
            } else {
                mRlSubscribe.setEnabled(true);
                tvSubscribeInTitleBar.setEnabled(true);
                mRlSubscribe.setBackground(LocalImageUtil.getDrawable(mContext, R.drawable.main_round_bg_radius_ffffff_dp100));
                rlSubscribeInTitleBar.setBackground(LocalImageUtil.getDrawable(mContext, R.drawable.main_round_bg_radius_ffffff_dp100));
            }
        }
    }

    @Override
    public void onClick(final View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        //专辑订阅被点击
        if (id == R.id.main_tv_subscribe_in_title_bar) {
            new XMTraceApi.Trace()
                    .setMetaId(32296)
                    .setServiceId("click")
                    .put("albumId", String.valueOf(mAlbumId))
                    .createTrace();
            dealWithAlbumSubscribeClick(v);
        } else if (id == R.id.main_rl_album_subscribe) {
            new XMTraceApi.Trace()
                    .setMetaId(4335)
                    .setServiceId("click")
                    .put("albumId", String.valueOf(mAlbumId))
                    .createTrace();
            dealWithAlbumSubscribeClick(v);
        }
        //专辑图被点击
        else if (id == R.id.main_album_single_album_cover) {
            dealWithAlbumCoverClick(v);
        } else if (id == R.id.main_album_share) {
            dealWithAlbumShareClick();
        } else if (id == R.id.main_tv_play_control) {
            //如果当前不是目录界面，切换到目录界面
            if (mPagerAdapter == null || mData == null || mViewPager == null) {
                return;
            }
            // 专辑页-新-开始播放按钮  点击事件
            new XMTraceApi.Trace()
                    .click(46822) // 用户点击时上报
                    .put("item", tvPlayControl.getText().toString())
                    .put("currPage", "albumPage")
                    .createTrace();
            int currentItem = mViewPager.getCurrentItem();
            if (mData != null && (mData.isOfflineHidden() || isPaidAlbum(mData))) {
                if (currentItem != 0) {
                    mViewPager.setCurrentItem(0);
                }
                if (mData.isOfflineHidden()) {
                    CustomToast.showSuccessToast("节目已下架");
                    return;
                }
                if (isPaidAlbum(mData)) {
                    CustomToast.showSuccessToast("节目无法播放");
                    return;
                }
            }
            if (!PlayTools.isAlbumPlaying(mContext, mAlbumId)) {
                mViewPager.setCurrentItem(0);
            }
            Fragment fragment = mPagerAdapter.getFragmentAtPosition(0);
            if (fragment instanceof LiteAlbumFragmentNewList) {
                ((LiteAlbumFragmentNewList) fragment).onPlayControl();
            }
        } else if (id == R.id.main_rl_mask) {
            lottieViewGuide.cancelAnimation();
            rlMask.setVisibility(View.GONE);
        } else if (id == R.id.main_ll_star) {
            if (mPagerAdapter == null) {
                return;
            }

            new XMTraceApi.Trace()
                    .setMetaId(32289)
                    .setServiceId("click")
                    .createTrace();

            Fragment fragment = mPagerAdapter.getFragmentAtPosition(0);
            if (fragment instanceof LiteAlbumIntroFragment) {
                ((LiteAlbumIntroFragment) fragment).seeMoreComment();
            }
        } else if (id == R.id.main_tv_tag_free) {
            // 专辑页-免费畅听标签  点击事件
            new XMTraceApi.Trace()
                    .click(45433)
                    .setServiceId("click")
                    .put("currAlbumId", String.valueOf(mAlbumId))
                    .put("currPage", "albumPage")
                    .createTrace();
            if (mData.isVipAlbum() && !UserInfoMannage.isVipUser()) {
                LiteAlbumFragmentNewList fragmentNewList = getLiteAlbumFragmentNewList();
                AlbumPaidUnLockHintInfo paidUnLockHintInfo = fragmentNewList != null ? fragmentNewList.getAlbumPaidUnLockHintInfo() : null;
                FreeUnlockBottomDialog freeUnlockBottomDialog = new FreeUnlockBottomDialog(mActivity, mData);
                freeUnlockBottomDialog.setUnlockHintInfo(null, paidUnLockHintInfo);
                freeUnlockBottomDialog.setCurrentFragment(fragmentNewList);
                freeUnlockBottomDialog.show();
            } else {
                CustomToast.showToast("你可以免费畅听本专辑哦~");
            }
        }
    }

    /**
     * 专辑分享被点击
     */
    private void dealWithAlbumShareClick() {
        if (mData == null) {
            return;
        }
        String channelAb = ABTest.getString("SharingChannelsABtest", "1");

        new XMTraceApi.Trace()
                .setMetaId(4339)
                .setServiceId("click")
                .put("albumId", String.valueOf(mAlbumId))
                .createTrace();

        if (mShareRewardComponent != null) {
            mShareRewardComponent.mIShareResultCallBack.startShare();
        }

        ShareResultManager.getInstance().setShareFinishListener(new ShareResultManager.ShareListener() {
            @Override
            public void onShareSuccess(String thirdName) {
                ShareResultManager.getInstance().clearShareFinishListener();
                if (mShareRewardComponent != null) {
                    mShareRewardComponent.mIShareResultCallBack.shareSuccess();
                }
            }

            @Override
            public void onShareFail(String thirdName) {
                ShareResultManager.getInstance().clearShareFinishListener();
            }
        });

        if ("1".equals(channelAb)) {
            ShareUtilsInMain.shareAlbum(mActivity, mData, IShareDstType.SHARE_TYPE_WX_FRIEND, ICustomShareContentType.SHARE_TYPE_ALBUM);
        } else {
            ShareDialog shareDialog = ShareUtilsInMain.shareAlbumNew(mActivity, mData, ICustomShareContentType.SHARE_TYPE_ALBUM);
            ShareAlbumDialogUtils.INSTANCE.execAddShareDialogAdvert("albumPage", shareDialog);
        }
    }

    /**
     * 专辑图被点击
     **/
    private void dealWithAlbumCoverClick(View v) {
        if (mData != null && mData.getIntro() != null) {
            new XMTraceApi.Trace()
                    .setMetaId(32288)
                    .setServiceId("click")
                    .put("albumId", String.valueOf(mAlbumId))
                    .createTrace();

            startDetailFragment();
        } else {
            CustomToast.showFailToast("错误的数据");
        }
    }

    /**
     * 专辑订阅被点击
     */
    private void dealWithAlbumSubscribeClick(View v) {
        if (!HiddenAlbumUtils.INSTANCE.isAllowClick(mData, "本声音不支持订阅")) {
            return;
        }

        if (!UserInfoMannage.hasLogined()) {
            Bundle loginParams = new Bundle();
            LoginBundleParamsManager.setLoginTitle(loginParams, "订阅需登录哦");
            UserInfoMannage.gotoLogin(mContext, LoginByConstants.LOGIN_BY_DEFUALT, loginParams);
            return;
        }

        AlbumEventManage.doCollectActionV2(mData, this, new ICollectStatusCallback() {
            @Override
            public void onCollectSuccess(int code, final boolean isCollected) {
                if (!canUpdateUi()) return;
                mData.setFavorite(isCollected);
                setSubscribeButton(mData);
                if (mRequestCode == AppConstants.REQUEST_CODE_ALBUM_FRAGMENT_SUBSCRIBE) {
                    setFinishCallBackData(mRequestCode, mData);
                }
                if (isCollected) {
                    //取消水波纹动画
                    //是展示第一次收藏成功的弹窗呢，还是toast
                    boolean showFirstSubscribeSuccessDialog = MmkvCommonUtil.getInstance(mContext).getBoolean(
                            SHOW_FIRST_SUBSCRIBE_SUCCESS_DIALOG, true);
                    if (!showFirstSubscribeSuccessDialog) {
                        CustomToast.showSuccessToast(getString(R.string.host_subscribe_success_in_i_listen));
                    }
                    stopWaveViewAnim();
                } else {
                    //显示水波纹动画
                    if (mData.isOfflineHidden()) {
                        stopWaveViewAnim();
                        return;
                    }
                    setAndStartWaveViewAnim();
                }
            }

            @Override
            public void onError() {

            }
        });
    }

    private void startDetailFragment() {
        LiteFullIntroDialog completeIntroductionDialog = LiteFullIntroDialog.newInstance(mData);
        completeIntroductionDialog.show(getChildFragmentManager(), "");
    }

    @Override
    protected boolean onPrepareNoContentView() {
        setNoContentImageView(R.drawable.host_no_content);
        setNoContentTitle("暂无内容");
        return false;
    }

    @Override
    public void onPause() {
        super.onPause();
        SnackbarManager.dismiss();
        stopWaveViewAnim();
        ShareRewardManager.INSTANCE.onPause(mShareRewardComponent);
        //停止看原著动效
        stopKanYuanZhuBreatheAnimator();
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38436;
        super.onMyResume();
        if (!isFirstLoading) {
            final Track track = XmPlayerManager.getInstance(getActivity()).getLastPlayTrackInAlbum(mAlbumId);
            if (getView() != null) {
                getView().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        setSubscribeButton(mData);
                        mTrack = track;
                    }
                }, 500);
            }

            checkAndShowSubscribeDialogOrBubbleTips();
            setAndStartWaveViewAnim();
        }
        checkToRefreshData();

        Logger.i(TAG, "onMyResume");
        //处理引导弹层

        showGuideMask();

        new XMTraceApi.Trace()
                .pageView(4333, "albumPage")
                .put("currPage", "albumPage")
                .put("albumId", String.valueOf(mAlbumId))
                .createTrace();

        ShareRewardManager.INSTANCE.onMyResume(mShareRewardComponent);


        //启动看原著呼吸动效
        //startKanYuanZhuBreatheAnimator();
    }

    private void showGuideMask() {
        boolean showGuide = MmkvCommonUtil.getInstance(mContext).getBoolean(MMKVKeyConstantsKt.MMKV_SHOW_ALBUM_PAGE_GESTURE_GUIDE, true);
        if (showGuide) {
            rlMask.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (!canUpdateUi()) {
                        return;
                    }
                    MmkvCommonUtil.getInstance(mContext).saveBoolean(MMKVKeyConstantsKt.MMKV_SHOW_ALBUM_PAGE_GESTURE_GUIDE, false);
                    rlMask.setVisibility(View.VISIBLE);
                    int[] location = new int[2];
                    mIndicator.getLocationInWindow(location);
                    lottieViewGuide.playAnimation();
                }
            }, 3000);
        }
    }

    @Override
    public void onDestroyView() {
        if (mHeadLayout != null) {
            mHeadLayout.removeOnLayoutChangeListener(this);
        }
        if (mRequestCode == AppConstants.REQUEST_CODE_ALBUM_FRAGMENT_COMMENT
                && mData != null) {
            setFinishCallBackData(mData.isCommented());
        }
        if (mLottieViewVipToast != null) {
            mLottieViewVipToast.clearAnimation();
        }
        //反注册解锁回调
        UnlockSuccessCallBackManager.getInstance().unRegisterGlobalTrackSuccess(this);

        //移除监听
        AlbumEventManage.removeListener(mAlbumCollectListener);

        UserInfoMannage.getInstance().removeLoginStatusChangeListener(mLoginStatusChangeListener);
        if (mShareGuideTip != null && mShareGuideTip.getVisibility() == View.VISIBLE) {
            mShareGuideTip.setVisibility(View.GONE);
        }
        super.onDestroyView();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        //移除监听
        AlbumEventManage.removeListener(mAlbumCollectListener);
        XmPlayerManager.getInstance(mActivity).removePlayerStatusListener(mPlayerStatusListener);
        ShareRewardManager.INSTANCE.onDestroy(mShareRewardComponent);
    }

    public int getCurrentShowFragmentIndex() {
        if (mIndicator != null) {
            return mIndicator.getCurrentItem();
        }
        return -1;
    }

    private void checkToRefreshData() {
        if (loadDataWhileResume == UserInfoMannage.hasLogined() && !mIsNeedReloadPageData)
            return;

        loadDataWhileResume = UserInfoMannage.hasLogined();
        mIsNeedReloadPageData = false;

        mIsNeedResetStickLayout = true;

        if (getView() != null) {
            getView().post(new Runnable() {
                @Override
                public void run() {
                    loadData();
                    if (mPagerAdapter != null) {
                        int pageIndex = getCurrentShowFragmentIndex();
                        if (pageIndex != -1) {
                            Fragment fra = mPagerAdapter.getFragmentAtPosition(pageIndex);
                            if (fra instanceof LiteAlbumFragmentNewList) {
                                ((LiteAlbumFragmentNewList) fra).reload();
                            }
                        }
                    }
                }
            });
        }
    }

    @Override
    public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
        //注意!!!，这里去掉，解决滑动定位到正在播放的声音，onScrollToEdge无法触发的问题。
        //if (mStickyNavLayout != null && mHeadLayout != null) {
        //int topViewHeight = mHeadLayout.getMeasuredHeight();
        //if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
        //topViewHeight += BaseUtil.getStatusBarHeight(mContext);
        //}
        // mStickyNavLayout.setTopViewHeight(topViewHeight);
        //}
    }

    @Override
    public boolean canRepeatInActivity() {
        return true;
    }

    @Override
    protected boolean isShowPlayButton() {
        if (mData != null && mData.isOfflineHidden()) {
            return false;
        }
        return super.isShowPlayButton();
    }

    public StickyNavLayout getStickyNavLayout() {
        return mStickyNavLayout;
    }

    public View getHeadView() {
        return mHeadLayout;
    }

    private LiteAlbumFragmentNewList getLiteAlbumFragmentNewList() {
        Fragment fra = mPagerAdapter.getFragmentAtPosition(0);
        if (fra instanceof LiteAlbumFragmentNewList) {
            return (LiteAlbumFragmentNewList) fra;
        }
        return null;
    }

    private boolean mIsTitleDarkIcon = false;

    @Override
    public void onGlobalTrackUnlockSuccess(List<Track> trackList, VideoUnLockResult result) {
        //全局vip解锁监听
        if (trackList == null || trackList.size() == 0) {
            return;
        }
        Track track = trackList.get(0);
        if (track == null || track.getAlbum() == null) {
            return;
        }
        //不是当前专辑，不处理
        if (track.getAlbum().getAlbumId() != mAlbumId) {
            return;
        }
        //是当前专辑，先刷新下界面，修改当前已经解锁的声音状态，再重新请求下接口，刷新其他解锁的声音的状态
//       if (trackAdapter != null) {
//           trackAdapter.notifyDataSetChanged();
//       }
        //需要延时后，重新刷新界面和定位正在播放的声音
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                if (!canUpdateUi()) {
                    return;
                }
                FuliLogger.log("视频解锁，专辑，重新请求==222=");
                loadData();
            }
        }, 1000);
    }

    private class PageScrollListener implements StickyNavLayout.ScrollListener {

        int mTotalScrollY;

        private PageScrollListener(Context context) {
            mTotalScrollY = BaseUtil.dp2px(context, 30);
        }

        @Override
        public void onScroll(int scrollY, int totalScrollY) {
//            Logger.i(TAG, "onScroll scrollY1 = " + scrollY + " totalScrollY = " + totalScrollY);
            //if (mIsTitleDarkIcon && scrollY < mTotalScrollY) {
            if (mIsTitleDarkIcon && scrollY < totalScrollY) {
                //StatusBarManager.setStatusBarColor(getWindow(), false);
                mIsTitleDarkIcon = false;
                //mBackButton.setImageResource(R.drawable.main_arrow_white_normal_left);
                //mAlbumShare.setImageResource(R.drawable.main_icon_share_white);
                hidePartOfTitleBarViews();
                showPartOfHeadViews();
                Logger.i(TAG, "onScroll scrollY2 = " + scrollY + " totalScrollY = " + totalScrollY);
                //} else if (!mIsTitleDarkIcon && scrollY >= mTotalScrollY) {
            } else if (!mIsTitleDarkIcon && scrollY >= totalScrollY) {
                //hidePartOfHeadViews();
                //StatusBarManager.setStatusBarColor(getWindow(), true);
                mIsTitleDarkIcon = true;
                //mBackButton.setImageResource(R.drawable.host_arrow_orange_normal_left);
                //mAlbumShare.setImageResource(R.drawable.main_icon_share_black);
                //vPageTitle.setVisibility(View.VISIBLE);
                //tvSubscribeInTitleBar.setVisibility(View.VISIBLE);
                Logger.i(TAG, "onScroll scrollY3 = " + scrollY + " totalScrollY = " + totalScrollY);
            }
            alphaTitleBarViews(scrollY, totalScrollY);

            NewGlobalFloatView globalFloatView = getGlobalFloatView();
            if (globalFloatView != null) {
                globalFloatView.startAnimation(true);
            }
        }

        @Override
        public void onScrollStop(int orient, int scrollY, int totalScrollY) {
            if (orient == StickyNavLayout.SCROLL_UP && scrollY != 0 && scrollY != totalScrollY) {
                mStickyNavLayout.smoothScroll(true, 0);
            }

            NewGlobalFloatView globalFloatView = getGlobalFloatView();
            if (globalFloatView != null) {
                globalFloatView.startAnimation(false);
            }
        }

        @Override
        public void onScrollToEdge(int scrollY, int totalScrollY) {
            if (scrollY == totalScrollY) {
                showPartOfTitleBarViews();
                hidePartOfHeadViews();
                //vPageTitle.setVisibility(View.VISIBLE);
                //tvSubscribeInTitleBar.setVisibility(View.VISIBLE);
            } else {
                hidePartOfTitleBarViews();
                showPartOfHeadViews();
            }
//            if (vTitleBar != null) {
//                int alpha = 0;
//                if (scrollY == 0) {
//                    alpha = 0;
//                } else if (scrollY == totalScrollY) {
//                    alpha = 0xFF;
//                }
//                vTitleBar.getBackground().setAlpha(alpha);
//            }
        }

        @Override
        public void onStateChange(boolean isStick) {

        }

    }

    private float mCurAlpha = 1.0f;

    /**
     * 隐藏解锁提示
     */
    private void alphaTitleBarViews(int scrollY, int totalScrollY) {
        float maxHeight = BaseUtil.dp2px(mContext, 50);
        float alpha = 0;
        if (scrollY <= maxHeight) {
            alpha = (maxHeight - scrollY) / maxHeight;
        }

        if (mCurAlpha != alpha) {
            mCurAlpha = alpha;

            if (mAlbumShare != null) {
                mAlbumShare.setAlpha(mCurAlpha);
                if (mCurAlpha == 0.0f) {
                    mAlbumShare.setVisibility(View.INVISIBLE);
                } else {
                    if (mAlbumShare.getVisibility() != View.VISIBLE) {
                        mAlbumShare.setVisibility(View.VISIBLE);
                    }
                }
            }
        }

        if (scrollY == totalScrollY) {
            // 滑到中间位置了
            if (mAlbumShare != null && mAlbumShare.getVisibility() != View.VISIBLE) {
                mAlbumShare.setVisibility(View.VISIBLE);
                mAlbumShare.setAlpha(1);
            }
        } else if (scrollY > maxHeight) {
            if (mAlbumShare != null && mAlbumShare.getVisibility() != View.INVISIBLE) {
                mAlbumShare.setVisibility(View.INVISIBLE);
            }
        }

    }

    /**
     * 隐藏标题栏上的标题和分享按钮
     */
    private void hidePartOfTitleBarViews() {
        if (vPageTitle == null || rlSubscribeInTitleBar == null) {
            return;
        }
        if (vPageTitle.getVisibility() != View.INVISIBLE) {
            vPageTitle.setVisibility(View.INVISIBLE);
        }
        if (rlSubscribeInTitleBar.getVisibility() != View.INVISIBLE) {
            rlSubscribeInTitleBar.setVisibility(View.INVISIBLE);
        }
    }

    /**
     * 显示标题栏上的标题和分享按钮
     */
    private void showPartOfTitleBarViews() {
        if (vPageTitle == null || rlSubscribeInTitleBar == null) {
            return;
        }
        if (vPageTitle.getVisibility() != View.VISIBLE) {
            vPageTitle.setVisibility(View.VISIBLE);
        }
        if (rlSubscribeInTitleBar.getVisibility() != View.VISIBLE) {
            if (ChildProtectManager.isChildProtectOpen(mContext)) {
                rlSubscribeInTitleBar.setVisibility(View.INVISIBLE);
            } else {
                rlSubscribeInTitleBar.setVisibility(View.VISIBLE);
            }
        }
    }

    /**
     * 隐藏head布局上的标签、分享按钮、评分、播放量和订阅数。
     */
    private void hidePartOfHeadViews() {
        if (mLlAllTags == null || flAlbumSubscribe == null || llStar == null ||
                llPlayCount == null || llSubscribeCount == null || cvAlbumCoverGroup == null || mAlbumIntroContainer == null) {
            return;
        }
        if (mLlAllTags.getVisibility() != View.INVISIBLE) {
            mLlAllTags.setVisibility(View.INVISIBLE);
        }
        if (flAlbumSubscribe.getVisibility() != View.INVISIBLE) {
            flAlbumSubscribe.setVisibility(View.INVISIBLE);
        }
        if (llStar.getVisibility() != View.INVISIBLE) {
            llStar.setVisibility(View.INVISIBLE);
        }
        if (llPlayCount.getVisibility() != View.INVISIBLE) {
            llPlayCount.setVisibility(View.INVISIBLE);
        }
        if (llSubscribeCount.getVisibility() != View.INVISIBLE) {
            llSubscribeCount.setVisibility(View.INVISIBLE);
        }
        if (cvAlbumCoverGroup.getVisibility() != View.INVISIBLE) {
            cvAlbumCoverGroup.setVisibility(View.INVISIBLE);
        }
        if (mAlbumIntroContainer.getVisibility() != View.INVISIBLE) {
            mAlbumIntroContainer.setVisibility(View.INVISIBLE);
        }
    }

    /**
     * 显示head布局上的标签、分享按钮、评分、播放量和订阅数。
     */
    private void showPartOfHeadViews() {
        if (mLlAllTags == null || flAlbumSubscribe == null || llStar == null ||
                llPlayCount == null || llSubscribeCount == null || cvAlbumCoverGroup == null || mAlbumIntroContainer == null) {
            return;

        }
        if (mLlAllTags.getVisibility() != View.VISIBLE) {
            mLlAllTags.setVisibility(View.VISIBLE);
        }
        if (flAlbumSubscribe.getVisibility() != View.VISIBLE) {
            if (ChildProtectManager.isChildProtectOpen(mContext)) {
                flAlbumSubscribe.setVisibility(View.INVISIBLE);
            } else {
                flAlbumSubscribe.setVisibility(View.VISIBLE);
            }
        }
        if (llStar.getVisibility() != View.VISIBLE) {
            llStar.setVisibility(View.VISIBLE);
        }
        if (llPlayCount.getVisibility() != View.VISIBLE) {
            llPlayCount.setVisibility(View.VISIBLE);
        }
        if (llSubscribeCount.getVisibility() != View.VISIBLE) {
            llSubscribeCount.setVisibility(View.VISIBLE);
        }
        if (cvAlbumCoverGroup.getVisibility() != View.VISIBLE) {
            cvAlbumCoverGroup.setVisibility(View.VISIBLE);
        }
        if (mAlbumIntroContainer.getVisibility() != View.VISIBLE) {
            mAlbumIntroContainer.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 获取当前页面专辑id
     */
    public long getAlbumId() {
        if (mData != null) {
            return mData.getId();
        } else {
            return mAlbumId;
        }
    }

    /**
     * 判断是否为精品（付费）专辑
     * 如果同是vip专辑，则优先认为是vip类型
     */
    private boolean isPaidAlbum(AlbumM albumM) {
        if (albumM != null) {
            if (albumM.getVipFreeType() == 1 || albumM.isVipFree()) {
                return false;
            }
            return albumM.isPaid();
        }
        return false;
    }

    /**
     * 可视化埋点页面绑定
     */
    private void bindPageData() {
        AutoTraceHelper.bindPageDataCallback(this, new AutoTraceHelper.IDataProvider() {
            @Override
            public Object getData() {
                if (mData == null) {
                    return null;
                }
                Map<String, String> params = new HashMap<>();
                boolean isVipAlbum = false;
                if (mData != null && mData.isVipAlbum()) {
                    isVipAlbum = true;
                }
                params.put("album_id", String.valueOf(mAlbumId));
                params.put("isVipAlbum", String.valueOf(isVipAlbum));
                params.put("payUnlockPlanVIP", "0");
                return params;
            }

            @Override
            public Object getModule() {
                return null;
            }

            @Override
            public String getModuleType() {
                return AutoTraceHelper.MODULE_DEFAULT;
            }
        });
    }

    private void performUnlockTips() {
        //performUnlockTips(true);
    }

    private void performUnlockTips(boolean isReport) {
        FuliLogger.log(TAG, "是否显示提示:" + (mAlbumPaidUnLockHintInfo != null));
        if (mAlbumPaidUnLockHintInfo != null && !UnlockListenTimeManagerNew.INSTANCE.isAllowUnlockVipAlbum()) {
            mTvTagFree.setVisibility(View.VISIBLE);
        } else {
            mTvTagFree.setVisibility(View.GONE);
        }
        updateTvTagOneParams();
    }

    private void updateTvTagOneParams() {
        if (mTvTag1 == null || !(mTvTag1.getLayoutParams() instanceof LinearLayout.LayoutParams))
            return;
        boolean isMargin = false;
        if (mTvTagFree != null && mTvTagFree.getVisibility() == View.VISIBLE) {
            isMargin = true;
        }
        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mTvTag1.getLayoutParams();
        params.leftMargin = isMargin ? (int) UiUtil.dp2px(5) : 0;
        mTvTag1.setLayoutParams(params);
    }

    private void updateBackground(AlbumM albumM) {
        if (albumM != null && !TextUtils.isEmpty(albumM.getLargeCover())) {
            mCurCoverUrl = albumM.getValidCover();
            ImageManager.from(getActivity()).downloadBitmap(mCurCoverUrl, (lastUrl, bitmap) -> {
                if (!TextUtils.isEmpty(lastUrl) && lastUrl.equals(mCurCoverUrl)) {
                    //mFinishedBackgroundViewTaskCount = 0;
                    //doBlurTask(mCurCoverUrl, bitmap);
                    doFetchColorTask(mCurCoverUrl, bitmap);
                }
            });
        } else {
            mCurCoverUrl = null;
            mBlurImage = null;
            mBackgroundColor = mDefaultBgColor;
            mBackgroundView.setImageAndColor(null, mBackgroundColor);
        }
    }

    @SuppressLint("StaticFieldLeak")
    private void doBlurTask(String url, Bitmap bitmap) {
        //小于等于Android5.1版本不再启用高斯模糊，性能太差了
        //vivo5.1会出现大量native崩溃，无法try
        if (Build.VERSION.SDK_INT <= 22) {
            onBackgroundViewTaskFinish();
        } else {
            Context context = getContext();
            new MyAsyncTask<Void, Void, Bitmap>() {
                @Override
                protected Bitmap doInBackground(Void... voids) {
                    return Blur.fastBlur(context, bitmap, 30);
                }

                @Override
                protected void onPostExecute(Bitmap bitmap) {
                    if (!TextUtils.isEmpty(url) && url.equals(mCurCoverUrl)) {
                        mBlurImage = bitmap;
                        onBackgroundViewTaskFinish();
                    }
                }
            }.execute();
        }
    }

    private void doFetchColorTask(String url, Bitmap bitmap) {
        LocalImageUtil.getDomainColor(bitmap, mDefaultBgColor, color -> {
            mBackgroundColor = ColorUtil.covertColorToDarkMuted(color);
            doBlurTask(url, bitmap);
        });
    }

    private void onBackgroundViewTaskFinish() {
        if (canUpdateUi()) {
            Logger.i(TAG, "onBackgroundViewTaskFinish");
            mBackgroundView.setImageAndColor(mBlurImage, mBackgroundColor);
        }
    }

    private void notifyPlayStatusChanged() {
        PlayableModel currSound = XmPlayerManager.getInstance(mActivity).getCurrSound();
        if (currSound == null) {
            return;
        }
        if (currSound.getDataId() <= 0) {
            return;
        }
        if (!(currSound instanceof Track)) {
            return;
        }
        updatePlayControl(((Track) currSound));
    }

    /**
     * 更新播放、暂停按钮状态
     */
    public void updatePlayControl(Track track) {
        if (!canUpdateUi()) {
            return;
        }
        if (tvPlayControl == null) {
            return;
        }
        if (mData != null && (mData.isOfflineHidden() || isPaidAlbum(mData))) {
            tvPlayControl.setText(getString(R.string.main_play_now));
            return;
        }
        boolean currentTrackPlaying = PlayTools.isCurrentTrackPlaying(mContext, track);
        Drawable drawable;
        if (currentTrackPlaying) {
            drawable = LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_pause_album);
        } else {
            drawable = LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_play_album);
        }
        tvPlayControl.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null);
        if (null != track) {
            tvPlayControl.setText(currentTrackPlaying ? "暂停播放" : "继续播放");
        } else {
            tvPlayControl.setText(getString(R.string.main_play_now));
        }

    }

    /**
     * 由于目录界面是否展示继续播放的tips，来处理mIndicator下面的哪根线的可见性
     */
    public void setLineOfContinuePlayVisibility(boolean visibility) {
        if (!canUpdateUi()) {
            return;
        }
        if (mIndicatorBorder != null) {
            if (visibility) {
                mIndicatorBorder.setVisibility(View.VISIBLE);
            } else {
                mIndicatorBorder.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 记录播放订阅列表专辑的信息
     */
    public void recordPlaySubscribeCurrentAlbumInfo() {
        if (mFrom == AlbumEventManage.FROM_MY_SUBSCRIBE) {
            PlaySubscribeRecommendManager.INSTANCE.saveSubscribeRecommendPlayRecordAlbumInfo(
                    mAlbumId, false, System.currentTimeMillis());
        }
    }

    public void showShareGuideAnimation() {
        ShareGuideSoundModel guideSoundModel = ShareGuideSoundManager.getSoundRemindConfig();
        if (mShareGuideTip == null || guideSoundModel == null) {
            return;
        }
        ShareGuideSoundManager.updateShareComponentState(false);
        String mainTitle = guideSoundModel.title;
        String subTitle = guideSoundModel.subTitle;
        TextView tvMainTitle = mShareGuideTip.findViewById(R.id.main_tv_share_title);
        TextView tvSubTitle = mShareGuideTip.findViewById(R.id.main_tv_share_sub_title);
        if (tvMainTitle != null && !TextUtils.isEmpty(mainTitle)) {
            tvMainTitle.setText(mainTitle);
        }
        if (tvSubTitle != null && !TextUtils.isEmpty(subTitle)) {
            tvSubTitle.setText(subTitle);
        }
        if (mShareGuideTipsAnimal != null) {
            mShareGuideTipsAnimal.cancel();
        }
        ObjectAnimator translationY = ObjectAnimator.ofFloat(mShareGuideTip, "translationY", 0, UiUtil.dp2px(45));
        ObjectAnimator alpha = ObjectAnimator.ofFloat(mShareGuideTip, "alpha", 0, 1.0f);
        mShareGuideTipsAnimal = new AnimatorSet();
        mShareGuideTipsAnimal.playTogether(translationY, alpha);
        mShareGuideTipsAnimal.setDuration(400);
        mShareGuideTipsAnimal.start();
        mShareGuideTip.setVisibility(View.VISIBLE);
        mShareGuideTip.findViewById(R.id.main_tv_share_btn).setOnClickListener(v -> {
            if (mAlbumShare != null) {
                mAlbumShare.callOnClick();
            }
            mShareGuideTip.setVisibility(View.GONE);

        });
        mShareGuideTip.findViewById(R.id.main_iv_share_close).setOnClickListener(v -> {
            mShareGuideTip.setVisibility(View.GONE);
        });
    }
}