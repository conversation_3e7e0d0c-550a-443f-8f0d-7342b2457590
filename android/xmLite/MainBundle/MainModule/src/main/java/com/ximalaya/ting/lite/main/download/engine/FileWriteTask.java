package com.ximalaya.ting.lite.main.download.engine;

import android.util.SparseArray;

import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.download.bean.BufferData;
import com.ximalaya.ting.lite.main.download.bean.MultiTaskInfo;
import com.ximalaya.ting.lite.main.download.bean.SingleTaskInfo;
import com.ximalaya.ting.lite.main.download.inter.ITask;
import com.ximalaya.ting.lite.main.download.inter.ITaskCallback;
import com.ximalaya.ting.lite.main.download.utils.BufferDataMgr;
import com.ximalaya.ting.lite.main.download.utils.TaskStateConst;
import com.ximalaya.ting.lite.main.download.utils.TaskUtil;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   : 写数据的任务，从指定的BlockingQueue中读数据，并保存到指定文件中
 */
public class FileWriteTask implements Runnable, ITask {
    private static final String tag = FileWriteTask.class.getSimpleName();
    private String filePath;
    private BlockingQueue<BufferData> memoryBufferQueue;
    private MultiTaskInfo taskInfos;
    private File infoFile;
    private CountDownLatch countDown;
    private Thread currentThread = null;
    private int state = TaskStateConst.STATE_PENDING;
    private boolean requestFlush = false;
    private static final long INTERVAL = 1000;
    private BufferDataMgr memoryBufferMgr;
    private ITaskCallback callback;
    private SparseArray<SingleTaskInfo> array = new SparseArray<>();

    FileWriteTask(String path, BlockingQueue<BufferData> buf, MultiTaskInfo infos, File file, BufferDataMgr memoryMgr) {
        filePath = path;
        memoryBufferQueue = buf;
        taskInfos = infos;
        infoFile = file;
        memoryBufferMgr = memoryMgr;
        // 为taskInfo创建索引
        for (SingleTaskInfo task : infos.list) {
            array.append(task.taskId, task);
        }
    }

    public boolean isBusy() {
        return state == TaskStateConst.STATE_RUNNING;
    }

    public void flush(CountDownLatch object) {
        requestFlush = true;
        countDown = object;

        if (currentThread != null) {
            currentThread.interrupt();
        }
    }

    @Override
    public void run() {
        Logger.d(tag, "开启读线程");
        currentThread = Thread.currentThread();
        start();
        Logger.d(tag, "写线程关闭");
    }

    public void start() {
        changeTask(TaskStateConst.STATE_RUNNING);
        try {
            exc();
        } catch (IOException ie) {
            Logger.d(tag, "读线程异常");
            Logger.e("DownThreadTask", ie.toString());
        } finally {
            changeTask(TaskStateConst.STATE_PENDING);
        }

        if (countDown != null) {
            countDown.countDown();
        }
    }

    private void changeTask(int value) {
        state = value;
        callback.onStateChanged(value);
    }

    public void stop() {
        // 确保数据被全部写入到文件中
        requestFlush = true;
        if (currentThread != null) {
            currentThread.interrupt();
        }
    }

    private void exc() throws IOException {
        RandomAccessFile raf = null;
        try {
            raf = new RandomAccessFile(filePath, "rw");
            long preTime = System.currentTimeMillis();
            while (true) {
                BufferData data = null;
                // 两种方式获取内存中的数据
                if (requestFlush) {
                    data = memoryBufferQueue.poll();
                    if (data == null) {
                        requestFlush = false;
                        break;
                    }
                } else {
                    try {
                        data = memoryBufferQueue.take();
                    } catch (InterruptedException ex) {
                        if (requestFlush) {
                            continue;
                        } else {
                            break;
                        }
                    }
                }

                raf.seek(data.beginPos);
                raf.write(data.buffer, 0, (int) data.len);

                SingleTaskInfo info = array.get(data.taskId);
                info.haveDoneSize += data.len;

                if (callback != null) {
                    callback.onProgressUpdate(data.len, 0, 0);
                }

                memoryBufferMgr.recycle(data);
                // 一秒写一次config文件
                long time = System.currentTimeMillis() - preTime;
                if (time > INTERVAL) {
                    TaskUtil.writeConfigInfo(infoFile, taskInfos);
                    preTime = System.currentTimeMillis();
                }
            }

            // 结束之后写一次
            TaskUtil.writeConfigInfo(infoFile, taskInfos);
        } finally {
            if (raf != null) {
                raf.close();
            }
        }
    }

    @Override
    public void setCallback(ITaskCallback back) {
        callback = back;
    }
}
