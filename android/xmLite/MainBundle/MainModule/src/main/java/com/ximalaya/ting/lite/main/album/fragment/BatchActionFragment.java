package com.ximalaya.ting.lite.main.album.fragment;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;

import androidx.annotation.Nullable;

import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.GridView;
import android.widget.PopupWindow;
import android.widget.PopupWindow.OnDismissListener;
import android.widget.TextView;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.downloadservice.base.IDownloadCallback;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.SlideView.IOnFinishListener;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder.DialogCallback;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseListFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.FileSizeUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.host.view.BadgeView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.album.adapter.AlbumPagerAdapter;
import com.ximalaya.ting.lite.main.album.adapter.AlbumPagerAdapter.PageIndex;
import com.ximalaya.ting.lite.main.album.adapter.BatchActionAdapter;
import com.ximalaya.ting.lite.main.download.DownloadingFragment;
import com.ximalaya.ting.lite.main.request.HttpParamsConstantsInMain;
import com.ximalaya.ting.lite.main.play.fragment.ChooseTrackQualityDialog;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ximalaya.ting.lite.main.download.BatchDownloadFragment.ACTION_DOWNLOAD_BUY;


/**
 * 批量下载或购买界面
 *
 * <AUTHOR>
 */
public class BatchActionFragment extends BaseListFragment2 implements IDownloadCallback {

    public static final int ACTION_DOWNLOAD = 1;// 批量下载，只针对免费

    private static int pageSize = 50;//默认50,服务端会在第一次返回数据时候给出
    private int type;
    private List<Track> mData = new ArrayList<>();
    private boolean isLoading;
    private int mPageId = 1;
    private long mAlbumId;
    private AlbumM mAlbumM;

    // 购买和下载页面公用
    private RefreshLoadMoreListView mListView;
    private BatchActionAdapter mAdapter;
    private TextView mTvSoundCount;
    private TextView mTvPageSelector;
    private PopupWindow mPagerSelectorContainer;
    private View mPagerSelectorContent;
    private GridView mAlbumPager;
    private AlbumPagerAdapter mAlbumPagerAdapter;
    private TextView tvTitleRight;

    // 下载独有的
    private BadgeView mTvDownloadingCount;
    private CheckBox mCheckAllCheckBox;
    private Button mDownloadBtn;
    private TextView mDownloadInfoBar;

    public BatchActionFragment() {
        super(false, null);
    }

    @SuppressLint("ValidFragment")
    public BatchActionFragment(boolean canSlide, IOnFinishListener onFinishListener) {
        super(canSlide, onFinishListener);
    }

    public static BatchActionFragment newInstance(long albumId, int type) {
        BatchActionFragment fra = new BatchActionFragment(
                AppConstants.isPageCanSlide, null);
        Bundle bundle = new Bundle();
        bundle.putLong(BundleKeyConstants.KEY_ALBUM_ID, albumId);
        bundle.putInt(BundleKeyConstants.KEY_FLAG, type);
        fra.setArguments(bundle);
        return fra;
    }

    public static BatchActionFragment newInstance(long albumId, long uid, int type) {
        BatchActionFragment fra = new BatchActionFragment(
                AppConstants.isPageCanSlide, null);
        Bundle bundle = new Bundle();
        bundle.putLong(BundleKeyConstants.KEY_ALBUM_ID, albumId);
        bundle.putLong(BundleKeyConstants.KEY_ALBUM_UID, uid);
        bundle.putInt(BundleKeyConstants.KEY_FLAG, type);
        fra.setArguments(bundle);
        return fra;
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38328;
        super.onMyResume();
        if (type == ACTION_DOWNLOAD) {
            updateDownloadingCount();
            RouteServiceUtil.getDownloadService().registerDownloadCallback(this);
        }
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public boolean isShowTruckFloatPlayBar() {
        return false;
    }

    @Override
    public void onPause() {
        super.onPause();
        if (type == ACTION_DOWNLOAD) {
            RouteServiceUtil.getDownloadService().unRegisterDownloadCallback(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onRefresh() {
    }

    @Override
    public void onMore() {
    }

    private void batchDownload() {
        if (RouteServiceUtil.getDownloadService().isTrackQualitySettingActive()) {
            addBatchDownloadTask();
        } else {
            ChooseTrackQualityDialog.ActionCallBack callBack = new ChooseTrackQualityDialog.ActionCallBack() {
                @Override
                public void onConfirm() {
                    addBatchDownloadTask();  //用户选择下载音质后不用刷新页面，直接下载便可
                }

                @Override
                public void onCancel() {
                }
            };
            ChooseTrackQualityDialog.newInstance(getActivity(), callBack).show();
        }
    }

    private void addBatchDownloadTask() {
        if (mAdapter != null) {
            RouteServiceUtil.getDownloadService().addTasksByTrackList(mAdapter.getCheckedTracks(), new IDataCallBack<AlbumM>() {

                @Override
                public void onSuccess(AlbumM object) {
                    if (canUpdateUi()) {
                        mAdapter.notifyDataSetChanged();
                    }
                }

                @Override
                public void onError(int code, String message) {

                }
            });


            mAdapter.uncheckAll();
            updateDownloadBarInfo();
        }
    }

    @Override
    public void onClick(final View v) {
        if (OneClickHelper.getInstance().onClick(v)) {
            int i = v.getId();
            if (i == R.id.main_download) {
                batchDownload();
            } else if (i == R.id.main_page_selector) {
                showPagerSelector(v);
            } else if (i == R.id.main_space) {
                if (mPagerSelectorContainer != null)
                    mPagerSelectorContainer.dismiss();
            } else {
                CustomToast.showFailToast("请先勾选声音");
            }
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position,
                            long id) {
        if (OneClickHelper.getInstance().onClick(view)) {
            int index = position
                    - mListView.getRefreshableView().getHeaderViewsCount();
            Track track = (Track) mAdapter.getItem(index);
            if (track == null) {
                return;
            }

            if (type == ACTION_DOWNLOAD) {
                if (!RouteServiceUtil.getDownloadService().isAddToDownload(track)) {
                    if (!track.getExtra()) {
                        track.setExtra(true);
                    } else {
                        track.setExtra(!track.getExtra());
                    }
                    mCheckAllCheckBox.setChecked(mAdapter.isAllChecked());
                    updateDownloadBarInfo();
                    mAdapter.notifyDataSetChanged();
                }
            }
        }
    }

    /**
     * 更新下载底栏界面
     */
    private void updateDownloadBarInfo() {

        if (mDownloadInfoBar == null)
            return;

        int checkedItem = 0;
        long checkedFileLength = 0;
        if (mAdapter != null) {
            if (mDownloadBtn != null) {
                mDownloadBtn.setEnabled(mAdapter.isOneChecked());
            }

            if (mAdapter.getCount() > 0) {
                for (Track track : mAdapter.getListData()) {
                    if (track.getExtra()) {
                        checkedItem++;
                        checkedFileLength += track.getDownloadSize();
                    }
                }
                if (checkedItem > 0) {
                    mDownloadInfoBar.setVisibility(View.VISIBLE);
                    long availableMemorySize = FileSizeUtil
                            .getAvailableMemorySize(RouteServiceUtil.getStoragePathManager()
                                    .getCurSavePath());

                    if (checkedFileLength > availableMemorySize) {
                        mDownloadInfoBar.setText(R.string.main_no_enough_storage);
                    } else {
                        String text = getStringSafe(
                                R.string.main_select_counts_occupy_size,
                                checkedItem,
                                StringUtil
                                        .getFriendlyFileSize(checkedFileLength),
                                StringUtil
                                        .getFriendlyFileSize(availableMemorySize));
                        mDownloadInfoBar.setText(text);
                    }
                    return;
                }
                mCheckAllCheckBox.setChecked(checkedItem > 0);
            }
        }
        mDownloadInfoBar.setVisibility(View.GONE);
    }

    @Override
    protected void getListData(int pageId, Map<String, Object> map) {

    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
        String tagTitleRight = "tagTitleRight";

        int rightText = R.string.main_downloading_current;
        TitleBar.ActionType titleRight = new TitleBar.ActionType(
                tagTitleRight, TitleBar.RIGHT, rightText, 0,
                R.color.main_tab_text_color, TextView.class);
        titleRight.setFontSize(14);
        titleBar.addAction(titleRight, new TitleRightClickListener());

        titleBar.update();
        tvTitleRight = (TextView) titleBar.getActionView(tagTitleRight);
        tvTitleRight.setPadding(0, 0, BaseUtil.dp2px(getActivity(), 6), 0);

        mTvDownloadingCount = new BadgeView(getActivity());
        mTvDownloadingCount.setTargetView(tvTitleRight);
        mTvDownloadingCount.setBadgeMargin(TitleBar.BADGE_LEFT_MARGIN, TitleBar.BADGE_TOP_MARGIN, TitleBar.BADGE_RIGHT_MARGIN, TitleBar.BADGE_BOTTOM_MARGIN);
        mTvDownloadingCount.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
        mTvDownloadingCount.setBackground(9, Color.parseColor("#F55233"));
    }

    private @Nullable
    View mBottomBar;

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        parseBundle();
        super.onActivityCreated(savedInstanceState);
    }

    private void parseBundle() {
        if (getArguments() != null) {
            mAlbumId = getArguments().getLong(BundleKeyConstants.KEY_ALBUM_ID);
            type = getArguments().getInt(BundleKeyConstants.KEY_FLAG);
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("批量下载");
        mBottomBar = findViewById(R.id.main_bottom_bar);
        mBottomBar.setVisibility((type == ACTION_DOWNLOAD) ? View.VISIBLE : View.GONE);

        findViewById(R.id.main_buy_bar).setVisibility(View.GONE);

        findViewById(R.id.main_sort).setVisibility(View.GONE);
        mTvSoundCount = (TextView) findViewById(R.id.main_sound_count);
        mTvPageSelector = (TextView) findViewById(R.id.main_page_selector);
        mTvPageSelector.setVisibility(View.VISIBLE);
        mTvPageSelector
                .setCompoundDrawables(LocalImageUtil.getDrawable(mContext,
                        R.drawable.main_pager_selector), null, LocalImageUtil
                        .getDrawable(mContext, R.drawable.main_arrow_gray_down), null);

        if (type == ACTION_DOWNLOAD || type == ACTION_DOWNLOAD_BUY) {
            initDownloadUi();
        }

        mListView = (RefreshLoadMoreListView) findViewById(R.id.main_listview);
        mListView.setMode(Mode.DISABLED);
        mAdapter = new BatchActionAdapter(mContext, mData, type);
        mListView.setAdapter(mAdapter);

        mListView.setOnItemClickListener(this);
        mTvPageSelector.setOnClickListener(this);
        AutoTraceHelper.bindData(mTvPageSelector, "");
    }


    private void updateDownloadingCount() {
        int size = RouteServiceUtil.getDownloadService().getUnfinishedTasks().size();
        if (size > 0) {
            mTvDownloadingCount.setVisibility(View.VISIBLE);
            mTvDownloadingCount.setText("" + size);
        } else {
            mTvDownloadingCount.setVisibility(View.GONE);
        }
    }

    private void initDownloadUi() {
        mCheckAllCheckBox = (CheckBox) findViewById(R.id.main_checkall);
        mDownloadBtn = (Button) findViewById(R.id.main_download);
        mDownloadInfoBar = (TextView) findViewById(R.id.main_bottom_info_bar);
        updateDownloadingCount();

        mCheckAllCheckBox.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mAdapter != null) {
                    if (mCheckAllCheckBox.isChecked()) {
                        mAdapter.checkAll();
                    } else {
                        mAdapter.uncheckAll();
                    }
                    updateDownloadBarInfo();
                }
            }
        });
        AutoTraceHelper.bindData(mCheckAllCheckBox, "");
        mDownloadBtn.setOnClickListener(this);
        AutoTraceHelper.bindData(mDownloadBtn, "");
    }

    @Override
    protected void loadData() {
        if (isLoading) {
            return;
        }
        isLoading = true;
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (!canUpdateUi()) return;
                if (type == ACTION_DOWNLOAD) {
                    getBatchDownloadList();
                } else {
                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                }
            }
        });

    }

    /**
     * 重置选集控件
     */
    protected void resetSelector() {
        if (mAlbumM == null) {
            return;
        }
        int toCount = pageSize * mPageId;
        int small = pageSize * mPageId - (pageSize - 1);
        long big = (toCount < mAlbumM.getIncludeTrackCount() ? toCount
                : mAlbumM.getIncludeTrackCount());
        mTvPageSelector.setText("选集（" + small + "~" + big + "）");
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_batch_action;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    /**
     * 初始化选集面板
     */
    private void showPagerSelector(View view) {
        if (mPagerSelectorContainer == null && mAlbumM != null && mData != null) {
            mPagerSelectorContent = LayoutInflater.from(getActivity()).inflate(
                    R.layout.main_layout_album_pager_selector, mListView, false);
            mAlbumPager = (GridView) mPagerSelectorContent
                    .findViewById(R.id.main_album_pager);
            mAlbumPagerAdapter = new AlbumPagerAdapter(mContext,
                    AlbumPagerAdapter.computePagerIndex(pageSize,
                            (int) mAlbumM.getIncludeTrackCount()));
            mAlbumPager.setAdapter(mAlbumPagerAdapter);

            mPagerSelectorContent.findViewById(R.id.main_space).setOnClickListener(
                    this);
            AutoTraceHelper.bindData(mPagerSelectorContent.findViewById(R.id.main_space), "");

            mAlbumPager.setOnItemClickListener(new OnItemClickListener() {

                @Override
                public void onItemClick(AdapterView<?> parent, View view,
                                        int position, long id) {
                    PageIndex pi = (PageIndex) mAlbumPagerAdapter
                            .getItem(position);

                    if (mPageId == pi.getPageIndex()) {//没有切换不处理
                        mPagerSelectorContainer.dismiss();
                        return;
                    }

                    mPageId = pi.getPageIndex();
                    mAlbumPagerAdapter.setPageId(mPageId);
                    mAdapter.clear();
                    loadData();
                    mPagerSelectorContainer.dismiss();
                    resetSelector();
                    if (mCheckAllCheckBox != null && mCheckAllCheckBox.isChecked()) {
                        mAdapter.uncheckAll();
                        mCheckAllCheckBox.setChecked(false);
                    }
                    updateDownloadBarInfo();
                }
            });

            mPagerSelectorContainer = new PopupWindow(mContext);
            mPagerSelectorContainer.setContentView(mPagerSelectorContent);
            mPagerSelectorContainer
                    .setAnimationStyle(R.style.host_popup_window_animation);
            mPagerSelectorContainer
                    .setWidth(WindowManager.LayoutParams.MATCH_PARENT);
            mPagerSelectorContainer
                    .setHeight(WindowManager.LayoutParams.MATCH_PARENT);
            mPagerSelectorContainer.setBackgroundDrawable(new ColorDrawable(
                    Color.parseColor("#b0000000")));
            mPagerSelectorContainer.setOutsideTouchable(true);
            mPagerSelectorContainer.setFocusable(true);
            mPagerSelectorContainer
                    .setOnDismissListener(new OnDismissListener() {

                        @Override
                        public void onDismiss() {
                            mTvPageSelector.setCompoundDrawables(LocalImageUtil
                                            .getDrawable(mContext,
                                                    R.drawable.main_pager_selector), null,
                                    LocalImageUtil.getDrawable(mContext,
                                            R.drawable.main_arrow_gray_down), null);
                        }
                    });
            mPagerSelectorContainer.update();
        }

        if (mPagerSelectorContainer == null) {
            return;
        }

        if (mPagerSelectorContainer.isShowing()) {
            mTvPageSelector.setCompoundDrawables(LocalImageUtil.getDrawable(
                    mContext, R.drawable.main_pager_selector), null, LocalImageUtil
                    .getDrawable(mContext, R.drawable.main_arrow_gray_down), null);
            mPagerSelectorContainer.dismiss();
        } else {
            mTvPageSelector.setCompoundDrawables(LocalImageUtil.getDrawable(
                    mContext, R.drawable.main_pager_selector), null, LocalImageUtil
                    .getDrawable(mContext, R.drawable.main_ic_gray_triangle), null);
            mPagerSelectorContainer.showAsDropDown(view, 0, 0);
        }
    }

    @Override
    public void onDownloadProgress(Track track) {
    }

    @Override
    public void onCancel(Track track) {
        updateDownloadingCount();
    }

    @Override
    public void onComplete(Track track) {

        if (mTvDownloadingCount == null)
            return;

        updateDownloadingCount();
    }

    @Override
    public void onUpdateTrack(Track track) {

    }

    @Override
    public void onStartNewTask(Track track) {
        mAdapter.notifyDataSetChanged();

        updateDownloadingCount();
    }

    @Override
    public void onError(Track track) {
        updateDownloadingCount();
    }

    @Override
    public void onDelete() {
        updateDownloadingCount();
    }

    @Override
    protected boolean onPrepareNoContentView() {
        return false;
    }

    @Override
    protected void onNoContentButtonClick(View view) {
    }

    /**
     * 获取批量下载的数据
     */
    private void getBatchDownloadList() {
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, mAlbumId + "");
        params.put(HttpParamsConstants.PARAM_PAGE_ID, mPageId + "");
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        int trackQualityLevel = RouteServiceUtil.getDownloadService().getTrackQualityLevel();
        params.put(HttpParamsConstantsInMain.PARAM_TRACK_QUALITY_LEVEL, String.valueOf(trackQualityLevel));
        LiteCommonRequest.getAlbumBatchDownloadInfo(
                params,
                new IDataCallBack<JSONObject>() {

                    @Override
                    public void onSuccess(JSONObject jsonObject) {
                        isLoading = false;
                        AlbumM object = null;
                        if (jsonObject.optInt("ret") == 0) {
                            try {
                                object = new AlbumM(jsonObject.getJSONObject(
                                        "album").toString());
                                object.parseTracks(jsonObject
                                        .getJSONObject("tracks"), false);
                                pageSize = object.getPageSize();
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        } else if (jsonObject.optInt("ret") == 50) {
                            // 未登陆
                            new DialogBuilder(getActivity())
                                    .setMessage("批量下载功能仅登录用户才能使用哦！")
                                    .setCancelBtn("稍后再说")
                                    .setOkBtn("去登录", new DialogCallback() {

                                        @Override
                                        public void onExecute() {
                                            UserInfoMannage.gotoLogin(getActivity());
                                        }
                                    }).showConfirm();
                            finish();
                            return;
                        }
                        if (object == null
                                || object.getCommonTrackList() == null
                                || object.getCommonTrackList().getTracks() == null
                                || object.getCommonTrackList().getTracks()
                                .size() < 1) {
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                            mTvPageSelector.setVisibility(View.GONE);
                        } else {
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            mAlbumM = object;
                            mData.clear();
                            mData.addAll(object.getCommonTrackList()
                                    .getTracks());
                            mTvSoundCount.setText("共"
                                    + mAlbumM.getIncludeTrackCount() + "集");
                            resetSelector();
                            mAdapter.notifyDataSetChanged();
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                        isLoading = false;
                    }
                });
    }

    class TitleRightClickListener implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            // 跳转到下载听-下载中界面
            if (type == ACTION_DOWNLOAD) {
                Bundle bundle = new Bundle();
                bundle.putInt(BundleKeyConstants.KEY_POSITION, 2);
                startFragment(DownloadingFragment.class, bundle);
            }
        }
    }

    @Override
    public void onPageLoadingCompleted(LoadCompleteType loadCompleteType) {
        super.onPageLoadingCompleted(loadCompleteType);

        if (loadCompleteType == LoadCompleteType.OK) {
            if (type == ACTION_DOWNLOAD && mBottomBar != null) {
                mBottomBar.setVisibility(View.VISIBLE);
            }
        } else {
            if (type == ACTION_DOWNLOAD && mBottomBar != null) {
                mBottomBar.setVisibility(View.GONE);
            }
        }
    }
}
