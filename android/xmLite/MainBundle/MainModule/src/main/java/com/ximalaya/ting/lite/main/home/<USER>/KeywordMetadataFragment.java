package com.ximalaya.ting.lite.main.home.fragment;

import android.graphics.Color;
import android.os.Bundle;

import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.framework.adapter.AbstractAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.category.CategoryMetadata;
import com.ximalaya.ting.android.host.util.RequestParamsUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.lite.main.base.GotoTopFragment;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.home.view.ChooseMetadataView;
import com.ximalaya.ting.lite.main.request.HttpParamsConstantsInMain;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2017/11/29.
 * <p>
 * 分类关键字筛选页
 */
public class KeywordMetadataFragment extends GotoTopFragment implements IRefreshLoadMoreListener, ChooseMetadataView.OnMetadataChangeListener {
    private FrameLayout mPullDownMenuContainer;
    private RefreshLoadMoreListView mListView;
    //已选的caldimen 和 metadata 组合成的提示
    private TextView mTvChosenHint;
    private FrameLayout mHeadPanelContainer;
    //提示用户无内容或网络错误的图片容器
    private LinearLayout mLayoutHintContainer;
    //提示用户无内容或网络错误的图片
    private ImageView mIvHint;
    private ChooseMetadataView mChooseMetadataView;
    private AbstractAdapter mAdapter;
    private View mRlTitleBar; //状态栏

    private int mCategoryId = -1;
    private int mKeywordId = -1;
    private int from = -1;
    private String mKeywordName;
    private String mCalDimension = ChooseMetadataView.CAL_DIMEN_DEFAULT;
    private String mMetadatas;
    private int mPageId = 1;
    private boolean mIsPullDownMenuShowing;

    //是否有正在请求的网络
    private boolean mIsLoadding = false;
    //listview只能在metadata加载成功后才能加载数据
    private boolean mLoadMetaDataSuccess = false;
    private boolean mNeedTitleBar = false; //是否需要标题栏，默认是不需要，默认在首页tab使用

    private boolean mIsFirstResume = true;

    public KeywordMetadataFragment() {
        super(false, null);
    }

    /**
     * 在tab下使用，所需要的argument
     */
    public static Bundle createArgumentFromTab(int keywordId, int categoryId, int from) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_KEYWORD_ID, keywordId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_FROM, from);
        bundle.putBoolean(BundleKeyConstantsInMain.KEY_NEED_TITLE_BAR, false);
        return bundle;
    }

    /**
     * 作为单一页面使用，所需要的的argument
     */
    public static Bundle createArgumentFromSinglePage(int categoryId, int keywordId, String keywordName) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_KEYWORD_ID, keywordId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putString(BundleKeyConstantsInMain.KEY_KEYWORD_NAME, keywordName);
        bundle.putBoolean(BundleKeyConstantsInMain.KEY_NEED_TITLE_BAR, true);
        return bundle;
    }

    /**
     * 作为单一页面使用，所需要的的argument
     */
    public static Bundle createArgumentFromSinglePage(int categoryId, int keywordId, String keywordName, int from) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_KEYWORD_ID, keywordId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putString(BundleKeyConstantsInMain.KEY_KEYWORD_NAME, keywordName);
        bundle.putInt(BundleKeyConstantsInMain.KEY_FROM, from);
        bundle.putBoolean(BundleKeyConstantsInMain.KEY_NEED_TITLE_BAR, true);
        return bundle;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mCategoryId = arguments.getInt(BundleKeyConstants.KEY_CATEGORY_ID, -1);
            mKeywordId = arguments.getInt(BundleKeyConstantsInMain.KEY_KEYWORD_ID);
            mKeywordName = arguments.getString(BundleKeyConstantsInMain.KEY_KEYWORD_NAME, "");
            from = arguments.getInt(BundleKeyConstantsInMain.KEY_FROM, -1);
            mNeedTitleBar = arguments.getBoolean(BundleKeyConstantsInMain.KEY_NEED_TITLE_BAR, false);
        }
        //需要标题栏，设置为可滑动返回
        setCanSlided(mNeedTitleBar);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRlTitleBar = findViewById(R.id.main_title_bar);
        mPullDownMenuContainer = (FrameLayout) findViewById(R.id.main_fl_pull_down_menu_container);

        mListView = (RefreshLoadMoreListView) findViewById(R.id.main_list_view);
        mListView.setOnRefreshLoadMoreListener(this);
        AlbumAdapter adapter = new AlbumAdapter(getActivity(), new ArrayList<Album>(), true, true);
        adapter.setTypeFrom(BaseAlbumAdapter.TYPE_KEYWORD_METADATA);
        mAdapter = adapter;

        mChooseMetadataView = new ChooseMetadataView(getActivity());
        mChooseMetadataView.setFrom(ChooseMetadataView.FROM_KEYWORD);
        mChooseMetadataView.setCategoryId(String.valueOf(mCategoryId));
        mChooseMetadataView.setKeywordId(String.valueOf(mKeywordId));
        mChooseMetadataView.addMetadataChangeListener(this);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.WRAP_CONTENT);
        mChooseMetadataView.setLayoutParams(params);

        mHeadPanelContainer = new FrameLayout(mContext);
        mHeadPanelContainer.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));

        mHeadPanelContainer.addView(mChooseMetadataView);
        mListView.getRefreshableView().addHeaderView(mHeadPanelContainer);

        mLayoutHintContainer = new LinearLayout(mContext);
        mLayoutHintContainer.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        mLayoutHintContainer.setGravity(Gravity.CENTER);
        mIvHint = new ImageView(mContext);
        mIvHint.setPadding(0, BaseUtil.dp2px(mContext, 30), 0, 0);
        mIvHint.setImageResource(R.drawable.main_bg_meta_nocontent);
        mLayoutHintContainer.addView(mIvHint);
        mIvHint.setVisibility(View.GONE);
        mListView.getRefreshableView().addFooterView(mLayoutHintContainer);

        mListView.setAdapter(mAdapter);
        mTvChosenHint = findViewById(R.id.main_tv_chosen_hint);

        initListeners();

        //设置状态栏展示
        if (mNeedTitleBar) {
            mRlTitleBar.setVisibility(View.VISIBLE);
            setTitle(mKeywordName);

            //单独页面，滑动冲突处理
            SlideView slideView = getSlideView();
            if (slideView != null) {
                mChooseMetadataView.setSlideView(slideView);
            }
        }
    }


    private class CustomOnScrollListener implements AbsListView.OnScrollListener {

        CustomOnScrollListener() {

        }

        @Override
        public void onScrollStateChanged(AbsListView view, int scrollState) {

        }

        @Override
        public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
            if (firstVisibleItem <= 1) {
                mPullDownMenuContainer.setVisibility(View.INVISIBLE);
            } else {
                mPullDownMenuContainer.setVisibility(View.VISIBLE);
                showPullDownWindow(false);
            }
            if (getiGotoTop() != null) {
                getiGotoTop().setState(firstVisibleItem > 12);
            }
        }
    }

    private void initListeners() {
        CustomOnScrollListener listener = new CustomOnScrollListener();
        mListView.setOnScrollListener(listener);  //NOTICE: 不能直接调用ListView的setOnScrollListener方法，否则不能上拉刷新
        findViewById(R.id.main_fl_pull_down_menu_container).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPullDownWindow(true);
            }
        });
        AutoTraceHelper.bindData(findViewById(R.id.main_fl_pull_down_menu_container), "");

        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
                if (!OneClickHelper.getInstance().onClick(view)) {
                    return;
                }
                if (mAdapter.getListData() == null) {
                    return;
                }
                int index = (int) id;
                if (index < 0 || index >= mAdapter.getListData().size()) {
                    return;
                }
                AlbumM albumM = (AlbumM) mAdapter.getListData().get(index);
                if (albumM == null) {
                    return;
                }

                // 分类页-热词内页-专辑item  点击事件
                new XMTraceApi.Trace()
                        .click(45533)
                        .put("albumId", String.valueOf(albumM.getId()))
                        .put("currPage", "Hot word inner page")
                        .createTrace();

                AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_DISCOVERY_CATEGORY, ConstantsOpenSdk.PLAY_FROM_FIND_CLASSIFICATION, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
            }
        });
    }

    @Override
    protected void loadData() {
        if (!mLoadMetaDataSuccess) {
            loadMetadata();
        } else {
            loadAlbum();
        }
    }

    /**
     * 加载分类筛选数据，加载成功后才能加载页面数据
     */
    private void loadMetadata() {
        if (mIsLoadding) {
            return;
        }
        mIsLoadding = true;
        onPageLoadingCompleted(BaseFragment.LoadCompleteType.LOADING);
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstantsInMain.PARAM_CATEGORY_ID, String.valueOf(mCategoryId));
        params.put(HttpParamsConstantsInMain.PARAM_KEYWORD_ID, String.valueOf(mKeywordId));
        params.put("deviceId", DeviceUtil.getDeviceToken(getActivity()));
        if (from == BundleValueConstantsInMain.FROM_VIP_PAGE) {
            params.put("speed", "1");
        } else {
            params.put("speed", "2");
        }
        LiteCommonRequest.getKeywordMetadatas(params, new IDataCallBack<List<CategoryMetadata>>() {
            @Override
            public void onSuccess(@Nullable List<CategoryMetadata> object) {
                mIsLoadding = false;
                if (!canUpdateUi()) {
                    return;
                }
                if (object == null) {
                    mLoadMetaDataSuccess = false;
                    onPageLoadingCompleted(BaseFragment.LoadCompleteType.NOCONTENT);
                    return;
                }
                mLoadMetaDataSuccess = true;
                onPageLoadingCompleted(BaseFragment.LoadCompleteType.OK);
                mChooseMetadataView.setMetadata(object);
                mChooseMetadataView.setFold(true);
            }

            @Override
            public void onError(int code, String message) {
                mIsLoadding = false;
                if (!canUpdateUi()) {
                    return;
                }
                onPageLoadingCompleted(BaseFragment.LoadCompleteType.NETWOEKERROR);
            }
        });
    }

    /**
     * 加载页面数据，分类筛选数据加载成功后才可以进行请求
     */
    private void loadAlbum() {
        if (mIsLoadding) {
            return;
        }

        if (mListView != null) {
            mListView.setMode(PullToRefreshBase.Mode.PULL_FROM_START);
        }
        mIsLoadding = true;
        onPageLoadingCompleted(BaseFragment.LoadCompleteType.LOADING);
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstantsInMain.PARAM_CALC_DIMENSION, mCalDimension);
        params.put(HttpParamsConstantsInMain.PARAM_CATEGORY_ID, String.valueOf(mCategoryId));
        params.put(HttpParamsConstantsInMain.PARAM_KEYWORD_ID, String.valueOf(mKeywordId));
        params.put(HttpParamsConstantsInMain.PARAM_DEVICE, "android");
        params.put(HttpParamsConstantsInMain.PARAM_VERSION, DeviceUtil.getVersion(getActivity()));
        params.put(HttpParamsConstantsInMain.PARAM_METADATAS, mMetadatas);
        params.put(HttpParamsConstants.PARAM_PAGE_ID, String.valueOf(mPageId));
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        params.put("scale", "1");
        params.put("network", CommonRequestM.getInstanse().getNetWorkType());
        params.put("operator", NetworkType.getOperator(mContext) + "");
        params.put("deviceId", DeviceUtil.getDeviceToken(mContext));
        params.put("appid", "0");
        if (UserInfoMannage.hasLogined()) {
            params.put("uid", UserInfoMannage.getUid() + "");
        }
        RequestParamsUtil.addVipShowParam(params);
        if (from == BundleValueConstantsInMain.FROM_VIP_PAGE) {
            params.put("vipPage", "1");
        }
        IDataCallBack<ListModeBase<AlbumM>> callBack = new IDataCallBack<ListModeBase<AlbumM>>() {
            @Override
            public void onSuccess(@Nullable final ListModeBase<AlbumM> object) {
                mIsLoadding = false;
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (object == null || ToolUtil.isEmptyCollects(object.getList())) {
                            if (mPageId == 1) {
                                if (mAdapter != null) {
                                    mAdapter.clear();
                                    mAdapter.notifyDataSetChanged();
                                }
                                onPageLoadingCompleted(BaseFragment.LoadCompleteType.NOCONTENT);
                            } else {
                                onPageLoadingCompleted(BaseFragment.LoadCompleteType.OK);
                            }
                            mListView.onRefreshComplete(false);
                            return;
                        }
                        if (mPageId == 1 && mAdapter != null) {
                            mAdapter.clear();
                        }

                        if (mPageId < object.getMaxPageId()) {
                            mListView.onRefreshComplete(true);
                        } else {
                            mListView.onRefreshComplete(false);
                        }
                        if (mAdapter != null) {
                            mAdapter.addListData(object.getList());
                            mAdapter.notifyDataSetChanged();
                        }
                        onPageLoadingCompleted(BaseFragment.LoadCompleteType.OK);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                mIsLoadding = false;
                onPageLoadingCompleted(BaseFragment.LoadCompleteType.NETWOEKERROR);
                Logger.i("KeywordMetadataFragment", "code:" + code + "  message:" + message);
            }
        };
        LiteCommonRequest.getAlbumsByMetadata(params, callBack);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_keyword_metadata;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        // 分类页-热词内页  页面展示
        new XMTraceApi.Trace()
                .pageView(45531, "Hot word inner page")
                .put("currPage", mKeywordName)
                .createTrace();

        if (mIsFirstResume) {
            mIsFirstResume = false;
        } else {
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void onMore() {
        showPullDownWindow(false);
        mPageId++;
        loadData();
    }

    @Override
    public void onRefresh() {
        mPageId = 1;
        loadData();
    }


    private void showPullDownWindow(boolean showPullDownWindow) {
        boolean isLoading = showPullDownWindow == mIsPullDownMenuShowing || (showPullDownWindow && mIsLoadding);
        if (isLoading) {  //正在刷新页面时不显示下拉弹窗
            return;
        }
        if (mIsPullDownMenuShowing) {
            mIsPullDownMenuShowing = false;
            mPullDownMenuContainer.removeView(mChooseMetadataView);
            mChooseMetadataView.showFoldButton(true);
            mChooseMetadataView.showBottomDivider(true);
            mHeadPanelContainer.addView(mChooseMetadataView);
            mChooseMetadataView.setBackgroundColor(Color.TRANSPARENT);
        } else {
            mIsPullDownMenuShowing = true;
            mHeadPanelContainer.removeView(mChooseMetadataView);
            mChooseMetadataView.setFold(false);
            mChooseMetadataView.showFoldButton(false);
            mChooseMetadataView.showBottomDivider(false);
            mPullDownMenuContainer.addView(mChooseMetadataView, mPullDownMenuContainer.getChildCount() - 1);
            mChooseMetadataView.setBackgroundColor(Color.WHITE);
        }
    }

    private String mCurFilterText = "综合排序";

    @Override
    public void onMetadataChange(String calDimension, String metadataRequestParam, String hintStr) {
        showPullDownWindow(false);

        mListView.getRefreshableView().setSelection(0);

        mCalDimension = calDimension;
        mMetadatas = metadataRequestParam;
        mTvChosenHint.setText(hintStr);

        // 只上报 综合排序那一行的筛选
        if (TextUtils.isEmpty(metadataRequestParam) && mAdapter != null && mAdapter.getListData() != null && !mAdapter.getListData().isEmpty()) {
            String text = hintStr;
            if (hintStr != null) {
                int index = hintStr.indexOf(" ·");
                if (index > 0) {
                    text = hintStr.substring(0, index - 1);
                }
            }
            if (text != null) {
                text = text.trim();
            }
            // 相同的不重复上报
            if (mCurFilterText == null || !mCurFilterText.equals(text)) {
                mCurFilterText = text;
                // 分类页-热词内页-排序类型  点击事件
                new XMTraceApi.Trace()
                        .click(45535)
                        .put("tabName", text)
                        .put("currPage", "Hot word inner page")
                        .createTrace();
            }
        }

        mPageId = 1;
        loadData();
    }

    @Override
    public void gotoTop() {
        if (mListView == null) {
            return;
        }
        mListView.getRefreshableView().setSelection(0);
    }

    @Override
    public void onPageLoadingCompleted(BaseFragment.LoadCompleteType loadCompleteType) {
        switch (loadCompleteType) {
            case OK:
            case LOADING:
                super.onPageLoadingCompleted(loadCompleteType);
                mIvHint.setVisibility(View.GONE);
                break;
            case NOCONTENT:
                if (mAdapter != null) {
                    mAdapter.clear();
                }
                super.onPageLoadingCompleted(BaseFragment.LoadCompleteType.OK);
                mIvHint.setImageResource(R.drawable.main_bg_meta_nocontent);
                mIvHint.setVisibility(View.VISIBLE);
                break;
            case NETWOEKERROR:
                if (mAdapter != null) {
                    mAdapter.clear();
                }
                super.onPageLoadingCompleted(BaseFragment.LoadCompleteType.OK);
                CustomToast.showFailToast(R.string.main_network_error);
                mIvHint.setImageResource(R.drawable.host_no_net);
                mIvHint.setVisibility(View.VISIBLE);
                break;
            default:
                break;
        }
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }
}
