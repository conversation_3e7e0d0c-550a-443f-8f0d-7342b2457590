package com.ximalaya.ting.lite.main.album.adapter;

import android.graphics.Color;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;

import java.util.List;

/**
 * Created by easoll on 17/6/23.
 *
 * <AUTHOR>
 */

public class AggregateAlbumRankAdapter extends BaseAlbumAdapter {

    public AggregateAlbumRankAdapter(MainActivity activity, List<Album> listData) {
        super(activity, listData);
    }

    private void refreshRankPosition(ViewHolder viewHolder, int rankPos) {
        if (viewHolder == null) {
            return;
        }
        int rank = rankPos + 1;
        String rankNumber = rank + "";
        if (rank < 10) {
            rankNumber = "0" + rankNumber;
        }
        viewHolder.tvRankNumber.setText(rankNumber);
        if (rank == 1) {
            viewHolder.tvRankNumber.setTextColor(Color.parseColor("#ED244B"));
            viewHolder.ivRankFlag.setVisibility(View.VISIBLE);
            viewHolder.ivRankFlag.setImageResource(R.drawable.main_icon_rank_first);
        } else if (rank == 2) {
            viewHolder.ivRankFlag.setVisibility(View.VISIBLE);
            viewHolder.ivRankFlag.setImageResource(R.drawable.main_icon_rank_second);
            viewHolder.tvRankNumber.setTextColor(Color.parseColor("#F3731F"));
        } else if (rank == 3) {
            viewHolder.ivRankFlag.setVisibility(View.VISIBLE);
            viewHolder.ivRankFlag.setImageResource(R.drawable.main_icon_rank_third);
            viewHolder.tvRankNumber.setTextColor(Color.parseColor("#C67749"));
        } else if (rank > 3) {
            viewHolder.ivRankFlag.setVisibility(View.INVISIBLE);
            viewHolder.ivRankFlag.setImageResource(0);
            viewHolder.tvRankNumber.setTextColor(Color.parseColor("#AAAAAA"));
        }
    }

    @Override
    public void onClick(View view, Album album, int position, BaseViewHolder holder) {

    }


    @Override
    public void bindViewDatas(BaseViewHolder holder, Album t, int position) {
        super.bindViewDatas(holder, t, position);

        ViewHolder viewHolder = (ViewHolder) holder;
        hideAllViews(viewHolder);
        if (!(t instanceof AlbumM)) {
            return;
        }
        final AlbumM albumM = (AlbumM) t;
        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, albumM);
        //添加专辑Item的ContentDescription
        if (viewHolder.root != null) {
            if (!TextUtils.isEmpty(albumM.getAlbumTitle())) {
                viewHolder.root.setContentDescription(albumM.getAlbumTitle());
            } else {
                viewHolder.root.setContentDescription("");
            }
        }
        refreshRankPosition(viewHolder, position);
        ImageManager.from(context).displayImage(viewHolder.cover, albumM.getLargeCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145, com.ximalaya.ting.android.host.R.drawable.host_default_album_145);
        int textSize = (int) viewHolder.title.getTextSize();
        Spanned titleWithTag = AlbumAdapter.getRichTitle(albumM, context, textSize);
        viewHolder.title.setText(titleWithTag);
        String subTitle = getDefaultSubTitle(albumM);
        viewHolder.subtitle.setText(subTitle);
        String playCountStr = StringUtil.getFriendlyNumStr(albumM.getPlayCount());
        int drawable = R.drawable.main_ic_common_play_count;
        addAlbumInfo(context, viewHolder.layoutAlbumInfo, drawable, playCountStr, Color.parseColor("#aaaaaa"), false, false);
        addAlbumInfo(context, viewHolder.layoutAlbumInfo, R.drawable.main_ic_common_track_count, StringUtil.getFriendlyNumStr(albumM.getIncludeTrackCount()) + " 集", Color.parseColor("#aaaaaa"));
        if(AlbumTagUtil.getAlbumCoverTag(albumM) != -1) {
            viewHolder.ivTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable(albumM, context, AlbumTagUtil.ZOOM_IN_RATIO_78_percent));
            viewHolder.ivTag.setVisibility(View.VISIBLE);
        } else {
            viewHolder.ivTag.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_album_rank_normal_aggregate_album;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    public static class ViewHolder extends BaseAlbumAdapter.ViewHolder {
        private ImageView offSale; //下架icon
        private TextView tvRankNumber;
        private ImageView ivRankFlag;
        private ImageView ivTag;

        public ViewHolder(View convertView) {
            super(convertView);
            cover = (ImageView) convertView.findViewById(R.id.main_iv_album_cover);
            border = convertView.findViewById(R.id.main_album_border);
            title = (TextView) convertView.findViewById(R.id.main_tv_album_title);
            subtitle = (TextView) convertView.findViewById(R.id.main_tv_album_subtitle);
            layoutAlbumInfo = (LinearLayout) convertView.findViewById(R.id.main_layout_album_info);
            offSale = (ImageView) convertView.findViewById(R.id.main_iv_off_sale);
            tvRankNumber = convertView.findViewById(R.id.main_tv_album_rank_num);
            ivRankFlag = convertView.findViewById(R.id.main_iv_rank_flag);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
        }
    }
}
