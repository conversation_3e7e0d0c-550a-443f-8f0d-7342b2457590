package com.ximalaya.ting.lite.main.album.fragment;

import android.os.Bundle;

import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.RequestParamsUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.album.adapter.MultiSubscribeAlbumAdapter;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 专辑--找相似
 *
 * <AUTHOR> on 2017/7/24.
 */
public class AlbumRecListFragment extends BaseFragment2 implements AdapterView.OnItemClickListener, IRefreshLoadMoreListener {

    private static final String KEY_FROM = "key_from";
    public static final String KEY_ALBUM_ANCHOR_UID = "album_anchor_uid";
    public static final int FROM_DEF = 0;
    //播放页推荐tab中的找相似
    public static final int FROM_PLAY_PAGE_RECOMMEND = 1;
    //来源：播放历史找相似
    public static final int FROM_PLAY_HISTORY = 2;

    private static final String KEY_ARGUMENT_NEED_TITLE_BAR = "key_need_title_bar";
    private static final String KEY_ARGUMENT_ALBUM_ID = "key_album_id";
    private static final String KEY_ARGUMENT_TRACK_ID = "key_track_id";

    private RefreshLoadMoreListView mListView;
    private RelativeLayout mRlTitleBar; //状态栏
    private MultiSubscribeAlbumAdapter mAdapter;

    //请求接口使用专辑id或者声音id
    private long albumId = -1;
    private long trackId = -1;
    private boolean mNeedTitleBar = false; //是否需要标题栏，默认是需要的
    private int mFrom = FROM_DEF;
    //可选择参数，不是所有来源都会有
    private long mCurrentAlbumAnchorUid = 0;

    private boolean mIsFirstResume = true;

    /**
     * tab使用，所需要的argument
     * <p>
     * 使用专辑id请求接口
     * <p>
     * 设置状态栏为false
     */
    public static Bundle createArgumentUseAlbumIdFromTab(long albumId) {
        Bundle bundle = new Bundle();
        bundle.putLong(KEY_ARGUMENT_ALBUM_ID, albumId);
        bundle.putBoolean(KEY_ARGUMENT_NEED_TITLE_BAR, false);
        return bundle;
    }

    /**
     * 默认使用的参数
     * <p>
     * 使用专辑id请求接口
     * <p>
     * 单独页面，需要titleBar
     */
    public static Bundle createArgumentUseAlbumIdFromDef(long albumId, int form) {
        Bundle bundle = new Bundle();
        bundle.putLong(KEY_ARGUMENT_ALBUM_ID, albumId);
        bundle.putBoolean(KEY_ARGUMENT_NEED_TITLE_BAR, true);
        bundle.putInt(KEY_FROM, form);
        return bundle;
    }

    /**
     * 添加专辑主播uid
     */
    public static void addArgumentAlbumAnchorUid(Bundle bundle, long anchorUid) {
        if (bundle == null) {
            return;
        }
        bundle.putLong(KEY_ALBUM_ANCHOR_UID, anchorUid);
    }

    /**
     * 默认使用的参数
     * <p>
     * 使用专辑id请求接口
     * <p>
     * 单独页面，需要titleBar
     */
    public static Bundle createArgumentUseTrackIdFromDef(long trackId) {
        Bundle bundle = new Bundle();
        bundle.putLong(KEY_ARGUMENT_TRACK_ID, trackId);
        bundle.putBoolean(KEY_ARGUMENT_NEED_TITLE_BAR, true);
        return bundle;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            albumId = arguments.getLong(KEY_ARGUMENT_ALBUM_ID, -1);
            trackId = arguments.getLong(KEY_ARGUMENT_TRACK_ID, -1);
            mNeedTitleBar = arguments.getBoolean(KEY_ARGUMENT_NEED_TITLE_BAR, true);
            mFrom = arguments.getInt(KEY_FROM, FROM_DEF);
            mCurrentAlbumAnchorUid = arguments.getLong(KEY_ALBUM_ANCHOR_UID, 0);
        }
        setCanSlided(mNeedTitleBar);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRlTitleBar = findViewById(R.id.main_title_bar);
        mListView = findViewById(R.id.main_id_stickynavlayout_innerscrollview);
        mAdapter = new MultiSubscribeAlbumAdapter(mActivity, null, mFrom);
        mAdapter.setTypeFrom(BaseAlbumAdapter.TYPE_RELATIVE_RECOMMEND);
        mAdapter.setCurrentAlbumAnchorUid(mCurrentAlbumAnchorUid);
        mListView.setAdapter(mAdapter);
        mListView.setMode(PullToRefreshBase.Mode.PULL_FROM_START);
        mListView.setOnRefreshLoadMoreListener(this);
        mListView.setPaddingForStatusBar(false);
        mListView.setOnItemClickListener(this);

        //设置状态栏展示
        if (mNeedTitleBar) {
            mRlTitleBar.setVisibility(View.VISIBLE);
            setTitle("相似推荐");
        } else {
            mRlTitleBar.setVisibility(View.GONE);
        }
        setNoContentTitle("未找到相似节目");
    }

    @Override
    protected String getPageLogicName() {
        return "albumRecList";
    }

    @Override
    protected void loadData() {
        staticLoadData(this);
    }

    private static void staticLoadData(AlbumRecListFragment fra) {
        if (fra == null) return;
        final WeakReference<AlbumRecListFragment> reference = new WeakReference<>(fra);
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        if (fra.mListView != null) {
            fra.mListView.setHasMoreNoFooterView(false);
        }
        fra.onPageLoadingCompleted(LoadCompleteType.LOADING);
        params.put("scale", "1");
        params.put("version", DeviceUtil.getVersion(fra.mContext));
        params.put("device", "android");
        params.put("network", CommonRequestM.getInstanse().getNetWorkType());
        params.put("operator", NetworkType.getOperator(fra.mContext) + "");
        params.put("deviceId", DeviceUtil.getDeviceToken(fra.mContext));
        params.put("appid", "0");
        params = RequestParamsUtil.addVipShowParam(params);
        if (UserInfoMannage.hasLogined()) {
            params.put("uid", UserInfoMannage.getUid() + "");
        }
        IDataCallBack<List<AlbumM>> callback = new IDataCallBack<List<AlbumM>>() {
            @Override
            public void onSuccess(final List<AlbumM> object) {
                if (reference == null) return;
                AlbumRecListFragment fra = reference.get();
                if (fra == null) return;
                fra.doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (reference == null) return;
                        AlbumRecListFragment fra = reference.get();
                        if (fra == null) return;
                        AlbumEventManage.getCollectAlbums(fra, object, new AlbumEventManage.ILoadHandler() {

                            @Override
                            public void onReady(final List<AlbumM> result) {
                                if (reference == null) return;
                                AlbumRecListFragment fra = reference.get();
                                if (fra == null) return;
                                if (!fra.canUpdateUi()) return;
                                fra.mAdapter.clear();
                                if ((result == null || result.isEmpty())) {
                                    fra.onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                                    fra.mListView.onRefreshComplete();
                                    return;
                                }
                                if (fra.mAdapter.getListData() == null) {
                                    fra.onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                                    fra.mListView.onRefreshComplete();
                                    return;
                                }
                                fra.onPageLoadingCompleted(LoadCompleteType.OK);
                                fra.mAdapter.getListData().addAll(result);
                                fra.mAdapter.notifyDataSetChanged();
                                fra.mListView.onRefreshComplete(false);
                            }
                        });
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                if (reference == null) return;
                AlbumRecListFragment fra = reference.get();
                if (fra == null) return;
                if (fra.canUpdateUi()) {
                    ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
                    fra.onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                }
            }
        };
        //来源为播放页tab推荐请求,包含主播相关专辑
        if (fra.mFrom == AlbumRecListFragment.FROM_PLAY_PAGE_RECOMMEND) {
            params.put(HttpParamsConstants.PARAM_ALBUM_ID, fra.albumId + "");
            LiteCommonRequest.getRelaCommentByAlbumIdAndAnchor(params, callback);
        } else {
            //使用专辑id或者声音id请求接口
            if (fra.trackId >= 0) {
                //有声音id，使用声音id
                params.put(HttpParamsConstants.PARAM_TRACK_ID, fra.trackId + "");
                //通过专辑id获取找相似内容
                LiteCommonRequest.getRelaCommentByTrackId(params, callback);
            } else {
                //有声音id，使用声音id
                params.put(HttpParamsConstants.PARAM_ALBUM_ID, fra.albumId + "");
                //通过专辑id获取找相似内容
                LiteCommonRequest.getRelaCommentByAlbumId(params, callback);
            }
        }
    }

    public int getContainerLayoutId() {
        return R.layout.main_fra_album_new_reclist;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        // 相似推荐页  页面展示
        new XMTraceApi.Trace()
                .pageView(45527, "SimilarlyRecommended")
                .put("currPage", "SimilarlyRecommended")
                .createTrace();

        if (mIsFirstResume) {
            mIsFirstResume = false;
        } else {
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        if (index < 0 || index >= mAdapter.getCount()) {
            return;
        }

        Object item = mAdapter.getItem(index);
        if (!(item instanceof AlbumM)) {
            return;
        }
        final AlbumM album = (AlbumM) item;

        // 相似推荐页-专辑item  点击事件
        new XMTraceApi.Trace()
                .click(45529)
                .put("albumId", String.valueOf(album.getId()))
                .put("currPage", "SimilarlyRecommended")
                .createTrace();

        AlbumEventManage.setAlbumFragmentFinishCallback(new IFragmentFinish() {
            @Override
            public void onFinishCallback(Class<?> cls, int fid, Object... params) {
                if (params != null && params[0] != null && params[1] != null) {
                    int requestCode = (int) params[0];
                    if (requestCode == AppConstants.REQUEST_CODE_ALBUM_FRAGMENT_SUBSCRIBE) {
                        AlbumM albumM = (AlbumM) params[1];
                        List<Album> list = mAdapter.getListData();
                        if (!ToolUtil.isEmptyCollects(list)) {
                            for (Album am : list) {
                                if (am.getId() == albumM.getId() && am instanceof AlbumM) {
                                    ((AlbumM) am).setFavorite(albumM.isFavorite());
                                    mAdapter.notifyDataSetChanged();
                                    return;
                                }
                            }
                        }
                    }

                }
            }
        });
        AlbumEventManage.startMatchAlbumFragment(album, AlbumEventManage.FROM_OTHER, ConstantsOpenSdk.PLAY_FROM_OTHER, album.getRecSrc(), album.getRecTrack(), -1, getActivity());
    }

    @Override
    public void onPageLoadingCompleted(LoadCompleteType loadCompleteType) {
        super.onPageLoadingCompleted(loadCompleteType);
        //需要状态栏，正常使用
        if (mNeedTitleBar) {
            return;
        }
        View changeParamsView = null;
        //不需要标题栏，在tab中使用，整个加载的状态区域向上移动
        switch (loadCompleteType) {
            case NETWOEKERROR:
                changeParamsView = mNetworkErrorView;
                break;
            case NOCONTENT:
                changeParamsView = mNoContentView;
                break;
            case LOADING:
                changeParamsView = mLoadingView;
                break;
            default:
                break;
        }
        if (changeParamsView == null) {
            return;
        }
        ViewGroup.LayoutParams layoutParams = changeParamsView.getLayoutParams();
        //此页面的类型是FrameLayout.LayoutParams
        if (!(layoutParams instanceof FrameLayout.LayoutParams)) {
            return;
        }
        FrameLayout.LayoutParams frParams = (FrameLayout.LayoutParams) layoutParams;
        frParams.gravity = Gravity.CENTER;
        frParams.bottomMargin = BaseUtil.dp2px(mContext, 100);
        changeParamsView.setLayoutParams(layoutParams);
    }


    @Override
    public void onRefresh() {
        mListView.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!canUpdateUi()) {
                    return;
                }
                mListView.onRefreshComplete(true);
                mListView.setHasMoreNoFooterView(false);
            }
        }, 500);
    }


    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }


    @Override
    public void onMore() {

    }
}
