package com.ximalaya.ting.lite.main.manager

import android.view.View
import android.view.ViewGroup
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.constant.MMKV_ANCHOR_RECEIVE_GIFT
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.album.dialog.AnchorPullNewGiftDialog
import org.json.JSONObject


class AnchorPullNewManager {

    private val mTAG = "AnchorPullNewManager"

    fun checkShowActivity(albumId: Long, view: ViewGroup) {
        if (MmkvCommonUtil.getInstance(BaseApplication.mAppInstance).getBoolean(MMKV_ANCHOR_RECEIVE_GIFT, false)) {
            FuliLogger.log(mTAG, "已领取过礼包,不再请求")
            return
        }

        getAnchorPullNewConfig(albumId, object : IRequestCallBack<Boolean> {
            override fun onResult(result: Boolean) {
                FuliLogger.log(mTAG, "邀请入口配置 result:$result")
                if (result) {
                    view.visibility = View.VISIBLE
                    view.setOnClickListener {
                        performClickView(view, albumId)
                    }

                    // 专辑页-主播拉新入口  控件曝光
                    XMTraceApi.Trace()
                        .setMetaId(49188)
                        .setServiceId("slipPage")
                        .put("currAlbumId", albumId.toString())
                        .put("currPage", "albumPage")
                        .createTrace()
                } else {
                    view.visibility = View.GONE
                }
            }
        })
    }

    private fun performClickView(view: ViewGroup, albumId: Long) {
        // 专辑页-主播拉新入口  点击事件
        XMTraceApi.Trace()
            .click(49187) // 用户点击时上报
            .put("currAlbumId", albumId.toString())
            .put("currPage", "albumPage")
            .createTrace()

        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(view.context)
            return
        }

        val activity = BaseApplication.getMainActivity()
        if (activity is MainActivity) {
            val dialog = AnchorPullNewGiftDialog()
            dialog.activityEnterView = view
            dialog.show(activity.supportFragmentManager, "AnchorPullNewGiftDialog")
        }
    }

    private fun getAnchorPullNewConfig(albumId: Long, callback: IRequestCallBack<Boolean>?) {
        val map = mutableMapOf<String, String?>()

        val url = UrlConstants.getInstanse().serverNetAddressHost + "lite-mobile/anchor/pullnew/config/${albumId}"

        CommonRequestM.baseGetRequest(url, map, object : IDataCallBack<Boolean> {
            override fun onSuccess(result: Boolean?) {
                if (result != null) {
                    callback?.onResult(result)
                } else {
                    FuliLogger.log(mTAG, "邀请入口配置请求失败 result is null")
                }
            }

            override fun onError(code: Int, message: String?) {
                FuliLogger.log(mTAG, "邀请入口配置请求失败 code:$code message:$message")
            }
        }) { content ->
            var json: JSONObject? = JSONObject(content)
            val ret = json?.optInt("ret", -1)
            if (ret == 0) {
                json = json?.optJSONObject("data")
                json?.optBoolean("activitySwitch")
            } else null
        }
    }

    interface IRequestCallBack<T> {
        fun onResult(result: T)
    }
}