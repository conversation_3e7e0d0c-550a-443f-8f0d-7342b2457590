package com.ximalaya.ting.lite.main.manager

import android.content.Intent
import android.net.Uri
import androidx.fragment.app.FragmentActivity
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.hybrid.utils.MD5Tool
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.device.ClipManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.album.EbookInfo
import com.ximalaya.ting.android.host.util.ContextUtils
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.android.xmsysteminvoke.XmSystemInvokeManager
import com.ximalaya.ting.lite.main.dialog.GuideDownQiApkDialog
import com.ximalaya.ting.lite.main.download.bean.TaskInfo
import com.ximalaya.ting.lite.main.download.inter.DownloadListener
import com.ximalaya.ting.lite.main.model.DownQjDialogConfig
import com.ximalaya.ting.lite.main.model.QJAppInfoModel
import com.ximalaya.ting.lite.main.model.QjSchemeConfig
import org.json.JSONObject

object DownQjApkManager {

    private const val TAG = "DownQjApkManager"
    private var isRequestDownInfo = false
    private var mQJAppInfoModel: QJAppInfoModel? = null

    private var mScheme = ""
    private var mQjSchemeConfig: QjSchemeConfig? = null

    private var mDownloadListener = mutableListOf<DownloadListener>()


    fun addDownListener(listener: DownloadListener?) {
        if (listener == null) {
            return
        }
        if (!mDownloadListener.contains(listener)) {
            mDownloadListener.add(listener)
        }
    }

    fun removeDownListener(listener: DownloadListener?) {
        mDownloadListener.remove(listener)
    }

    fun initSchemeConfig() {
        val config = ConfigureCenter.getInstance().getJsonString(
            CConstants.Group_Base.GROUP_NAME,
            CConstants.Group_Base.ITEM_DIVERSION_SCHEME,
            ""
        )
        if (config.isNotEmpty()) {
            try {
                mQjSchemeConfig = Gson().fromJson(config, QjSchemeConfig::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun checkSchemeConfig(eBookInfo: EbookInfo, channelName: String) {
        initSchemeConfig()
        mScheme =
            "xread://open?msg_type=7&businessType=0&channelName=${channelName}&book_id=${eBookInfo.bookId}&chapterId=0"
        if (mQjSchemeConfig != null) {
            if (channelName == "jisubanplayer") {
                if (!mQjSchemeConfig!!.jisubanplayer_scheme.isNullOrEmpty()) {
                    mScheme = mQjSchemeConfig!!.jisubanplayer_scheme!!
                    mScheme = mScheme.replace("__BOOKID__", eBookInfo.bookId.toString())
                        .replace("__CHAPTERID__", "0")
                }
            } else if (channelName == "jisubanalbum") {
                if (!mQjSchemeConfig!!.jisubanalbum_scheme.isNullOrEmpty()) {
                    mScheme = mQjSchemeConfig!!.jisubanalbum_scheme!!
                    mScheme = mScheme.replace("__BOOKID__", eBookInfo.bookId.toString())
                        .replace("__CHAPTERID__", "0")
                }
            }
        }
    }


    @JvmStatic
    fun startQJApp(): Boolean {
        val context = BaseApplication.getMyApplicationContext()
        val qjAppId = "reader.com.xmly.xmlyreader"
        if (XmSystemInvokeManager.isAppInstalled(context, qjAppId)) {
            val activity = BaseApplication.getTopActivity()
            try {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse(mScheme)
                activity?.startActivity(intent)
                return true
            } catch (e: Exception) {
                e.printStackTrace()
                FuliLogger.log(TAG, "启动应用失败:${e.message}")
            }
        }
        return false
    }

    @JvmStatic
    fun playPageCheckQJApp(eBookInfo: EbookInfo) {
        checkQJApp(eBookInfo, "jisubanplayer", "playPage")
    }

    @JvmStatic
    fun albumDetailCheckQJApp(eBookInfo: EbookInfo) {
        checkQJApp(eBookInfo, "jisubanalbum", "albumDetail")
    }

    @JvmStatic
    private fun checkQJApp(eBookInfo: EbookInfo, channelName: String, curPage: String) {
        checkSchemeConfig(eBookInfo, channelName)

        if (startQJApp()) {
            FuliLogger.log(TAG, "启动应用成功")
            return
        }

        val config = ConfigureCenter.getInstance().getJsonString(
            CConstants.Group_Base.GROUP_NAME,
            CConstants.Group_Base.ITEM_DIVERSION_CATR,
            ""
        )

        var mDownQjDialogConfig: DownQjDialogConfig? = null
        if (config.isEmpty()) {
            mDownQjDialogConfig = DownQjDialogConfig(true, "", "")
        } else {
            try {
                mDownQjDialogConfig = Gson().fromJson(config, DownQjDialogConfig::class.java)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        if (mDownQjDialogConfig == null) {
            CustomToast.showToast("配置获取失败")
            return
        }

        if (mDownQjDialogConfig.Switch) {
            FuliLogger.log(TAG, "开关开启,显示弹窗")
            val activity = BaseApplication.getTopActivity()
            if (ContextUtils.checkActivity(activity) && activity is FragmentActivity) {
                val dialog = GuideDownQiApkDialog(curPage, mDownQjDialogConfig)
                dialog.mIClickCallBack = object : GuideDownQiApkDialog.IClickCallBack {
                    override fun onClick() {
                        if (startQJApp()) {
                            return
                        }
                        checkDownInfo(false)
                    }
                }
                dialog.show(
                    activity.supportFragmentManager,
                    "GuideDownQiApkDialog"
                )
            }
        } else {
            FuliLogger.log(TAG, "开关关闭,直接下载")
            checkDownInfo(false)
        }
    }

    @JvmStatic
    fun checkSilentDownQJApp(model: QJAppInfoModel?) {
        if (model?.available() != true) {
            checkDownInfo(true)
        } else {
            downloadApp(model, true)
        }
    }

    /**
     *  @param isSilentDown 是否静默下载
     */
    fun checkDownInfo(isSilentDown: Boolean) {
        getDownloadInfo(object : IDataCallBack<QJAppInfoModel> {
            override fun onSuccess(result: QJAppInfoModel?) {
                if (result?.available() != true) {
                    if (!isSilentDown) {
                        CustomToast.showToast("下载信息获取失败")
                    }
                    return
                }

                downloadApp(result, isSilentDown)
            }

            override fun onError(code: Int, message: String?) {
                if (!isSilentDown) {
                    CustomToast.showToast("下载信息获取失败")
                }
            }
        })
    }

    fun downloadApp(model: QJAppInfoModel, isSilentDown: Boolean) {
        val tasInfo = DownFileManager.createTaskInfo(model)

        DownFileManager.mDownloadListener = object : DownloadListener {
            override fun onTaskStart(task: TaskInfo?) {
                if (!isSilentDown) {
                    CustomToast.showToast("开始下载")
                }
                mDownloadListener.forEach {
                    it.onTaskStart(task)
                }
            }

            override fun onTaskSuccess(task: TaskInfo?) {
                if (!isSilentDown) {
                    CustomToast.showToast("下载成功,开始安装")
                }
                mDownloadListener.forEach {
                    it.onTaskSuccess(task)
                }

                SilentDownQJManager.checkSaveData(task?.file)

                if (!isSilentDown) {
                    DownFileManager.installApp(
                        BaseApplication.getMyApplicationContext(),
                        task?.file
                    )
                }
            }

            override fun onTaskFailed(task: TaskInfo?) {
                if (!isSilentDown) {
                    CustomToast.showToast("下载失败,请重试")
                }
                mDownloadListener.forEach {
                    it.onTaskFailed(task)
                }
            }

            override fun onTaskProgress(task: TaskInfo?, progress: Int) {
                mDownloadListener.forEach {
                    it.onTaskProgress(task, progress)
                }
            }
        }

        ClipManager.setClipContent("qj_label", mScheme)
        DownFileManager.downloadApkFile(tasInfo, model.digest)
    }

    fun getDownloadInfo(callBack: IDataCallBack<QJAppInfoModel>?) {
        if (isRequestDownInfo) {
            FuliLogger.log(TAG, "下载配置请求中,直接返回:$mQJAppInfoModel")
            callBack?.onSuccess(mQJAppInfoModel)
            return
        }
        isRequestDownInfo = true

        val map = mutableMapOf<String, String?>()
        val url = UrlConstants.getInstanse().qijiAddressHost + "qiji-mobile/app/download/latest"

        CommonRequestM.baseGetRequest(url, map, object : IDataCallBack<QJAppInfoModel?> {
            override fun onSuccess(result: QJAppInfoModel?) {
                FuliLogger.log(TAG, "获取奇迹下载地址onSuccess result:$result")
                isRequestDownInfo = false
                if (result != null) {
                    mQJAppInfoModel = result
                }
                callBack?.onSuccess(result)
            }

            override fun onError(code: Int, message: String?) {
                isRequestDownInfo = false
                callBack?.onError(code, message)
                FuliLogger.log(TAG, "获取奇迹下载地址onError code:$code message:$message")
            }

        }) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            if (ret == 0) {
                JsonUtilKt.instance.toObjectOfType<QJAppInfoModel>(
                    json.optString("data"),
                    object : TypeToken<QJAppInfoModel>() {}.type
                )
            } else null
        }
    }

}