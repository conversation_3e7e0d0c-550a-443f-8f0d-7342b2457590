package com.ximalaya.ting.lite.main.album.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.viewpager.widget.ViewPager;

import com.astuetz.PagerSlidingTabStrip;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankArgsModel;
import com.ximalaya.ting.lite.main.model.rank.NewRankGroup;
import com.ximalaya.ting.lite.main.model.rank.NewRankListGroupResp;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by dumingwei on 2021/1/8
 * <p>
 * Desc: 新的聚合排行榜
 */
public class NewAggregateRankFragment extends BaseFragment2 implements View.OnClickListener {

    private PagerSlidingTabStrip mTabs;
    private ViewPager mPager;
    private TabCommonAdapter mAdapter;

    public static final String ARGS_SELECT_RANK_MODEL = "args_select_rank_model";

    //分组信息，类型分类信息
    public List<NewRankGroup> mAggregateRankList = new ArrayList<>();
    private List<TabCommonAdapter.FragmentHolder> mFragmentList = new CopyOnWriteArrayList<>();

    private AggregateRankArgsModel mSelectRankModel;

    public static Bundle newArgument(AggregateRankArgsModel selectRankModel) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ARGS_SELECT_RANK_MODEL, selectRankModel);
        return bundle;
    }

    public static NewAggregateRankFragment newInstance(AggregateRankArgsModel selectRankModel) {
        Bundle args = new Bundle();
        NewAggregateRankFragment fragment = new NewAggregateRankFragment();
        args.putParcelable(ARGS_SELECT_RANK_MODEL, selectRankModel);
        fragment.setArguments(args);
        return fragment;
    }

    public NewAggregateRankFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_RELATIVELAYOUT, null);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mSelectRankModel = arguments.getParcelable(ARGS_SELECT_RANK_MODEL);
        }
        if (mSelectRankModel == null) {
            mSelectRankModel = new AggregateRankArgsModel();
        }
    }

    @Override
    public int getTitleBarResourceId() {
        //不使用统一的titleBar
        return -1;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_new_aggregate_rank;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mTabs = findViewById(R.id.main_tabs);
        mPager = findViewById(R.id.main_content);
        mTabs.setTabPaddingLeftRight(BaseUtil.dp2px(getActivity(), 17));
        mTabs.setDisallowInterceptTouchEventView((ViewGroup) mTabs.getParent());
        ImageView ivBack = findViewById(R.id.main_iv_back);
        ImageView ivSearch = findViewById(R.id.main_iv_search);
        ivBack.setOnClickListener(this);
        ivSearch.setOnClickListener(this);
        AutoTraceHelper.bindData(ivBack, "");
        AutoTraceHelper.bindData(ivSearch, "");

        mPager.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {
            @Override
            public void onPageSelected(int page) {
                Logger.log("GroupRank" + "onPageSelected" + page);
                if (getSlideView() != null) {
                    if (page == 0) {
                        getSlideView().setSlide(true);
                    } else {
                        getSlideView().setSlide(false);
                    }
                }
            }
        });

        mTabs.setOnTabClickListener(position -> {
            // 听书排行榜（代码）-顶部tab按钮  点击事件
            new XMTraceApi.Trace()
                    .click(45522)
                    .put("tabName", mFragmentList.get(position).title)
                    .put("currPage", "BookLeaderboards")
                    .createTrace();
        });
    }

    @Override
    protected void loadData() {
        requestRankCategory();
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        // 听书排行榜（代码）  页面展示
        new XMTraceApi.Trace()
                .pageView(45518, "BookLeaderboards")
                .put("currPage", "BookLeaderboards")
                .createTrace();
    }

    /**
     * 请求排行榜分组信息
     */
    private void requestRankCategory() {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        Map<String, String> params = new ArrayMap<>();
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        LiteCommonRequest.getNewAggregateRank(params, new IDataCallBack<NewRankListGroupResp>() {
            @Override
            public void onSuccess(@Nullable final NewRankListGroupResp object) {
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        if (object == null || CollectionUtil.isNullOrEmpty(object.getRankListGroups())) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }

                        List<NewRankGroup> rankingListGroups = object.getRankListGroups();

                        mAggregateRankList.clear();
                        mFragmentList.clear();

                        //通过selectRankClusterId或者selectRankingListId反向查找selectClusterType
                        findSelectClusterType(object);

                        for (int i = 0; i < rankingListGroups.size(); i++) {
                            NewRankGroup itemRsp = rankingListGroups.get(i);
                            if (itemRsp == null || itemRsp.getGroupId() == null || itemRsp.getGroupName() == null || CollectionUtil.isNullOrEmpty(itemRsp.getRankingLists())) {
                                continue;
                            }
                            mAggregateRankList.add(itemRsp);
                            int clusterType = itemRsp.getGroupId();
                            long selectRankListId = -1;
                            //找到对应的tab，设置选中item
                            if (clusterType == mSelectRankModel.selectClusterType) {
                                selectRankListId = mSelectRankModel.selectRankingListId;
                            }
                            Bundle bundle = NewAggregateAlbumRankFragment.newArgument(clusterType, selectRankListId);
                            TabCommonAdapter.FragmentHolder recommendHolder = new TabCommonAdapter.FragmentHolder(NewAggregateAlbumRankFragment.class, itemRsp.getGroupName(), bundle);
                            mFragmentList.add(recommendHolder);
                        }
                        if (mFragmentList.size() == 0) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }
                        mAdapter = new TabCommonAdapter(getChildFragmentManager(), mFragmentList);
                        //只有一个的情况下，不展示tab
                        mTabs.setVisibility(mFragmentList.size() < 2 ? View.GONE : View.VISIBLE);
                        //4个内，平分布局，超过4个往后排列
                        mTabs.setShouldExpand(mFragmentList.size() < 5);
                        mPager.setAdapter(mAdapter);
                        //设置可以缓存3个
                        mPager.setOffscreenPageLimit(3);
                        mTabs.setViewPager(mPager);

                        //上个循环会做数据校验操作，重新遍历，计算选中的tab
                        int selectTabPosition = 0;
                        for (int i = 0; i < mAggregateRankList.size(); i++) {
                            NewRankGroup itemRsp = rankingListGroups.get(i);
                            if (itemRsp == null || itemRsp.getGroupId() == null || itemRsp.getGroupName() == null || CollectionUtil.isNullOrEmpty(itemRsp.getRankingLists())) {
                                continue;
                            }
                            if (itemRsp.getGroupId() == mSelectRankModel.selectClusterType) {
                                selectTabPosition = i;
                            }
                        }
                        mPager.setCurrentItem(selectTabPosition);

                        if (getSlideView() != null) {
                            getSlideView().setSlide(selectTabPosition == 0);
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
            }
        });
    }

    /**
     * 根据默认选中的selectClusterType或者selectRankClusterId反向查找selectClusterType
     */
    private void findSelectClusterType(NewRankListGroupResp object) {
        if (object == null || object.getRankListGroups() == null) {
            return;
        }
        //全部大于0，不在做查找操作
        if (mSelectRankModel.selectClusterType > 0 && mSelectRankModel.selectRankingListId > 0) {
            return;
        }
        //根据selectRankClusterId和selectRankingListId查找selectClusterType
        if (mSelectRankModel.selectRankingListId > 0) {
            for (int i = 0; i < object.getRankListGroups().size(); i++) {
                NewRankGroup itemRsp = object.getRankListGroups().get(i);
                if (itemRsp == null || itemRsp.getGroupId() == null || itemRsp.getGroupName() == null ||
                        CollectionUtil.isNullOrEmpty(itemRsp.getRankingLists())) {
                    continue;
                }
                List<NewRankGroup.SingleCategory> categoryList = itemRsp.getRankingLists();
                for (int j = 0; j < categoryList.size(); j++) {
                    NewRankGroup.SingleCategory rankCategoryModel = categoryList.get(j);
                    if (rankCategoryModel == null) {
                        continue;
                    }
                    if (mSelectRankModel.selectRankingListId > 0 && mSelectRankModel.selectRankingListId == rankCategoryModel.getRankingListId()) {
                        //selectRankingListId>0,使用selectRankingListId反向查找
                        //mSelectRankModel.selectRankClusterId = rankCategoryModel.rankClusterId;
                        mSelectRankModel.selectClusterType = itemRsp.getGroupId();
                        break;
                    } /*else if (mSelectRankModel.selectRankClusterId > 0 && mSelectRankModel.selectRankClusterId == rankCategoryModel.rankClusterId) {
                        //selectRankClusterId>0,使用rankClusterId反向查找
                        mSelectRankModel.selectRankingListId = rankCategoryModel.rankingListId;
                        mSelectRankModel.selectClusterType = itemRsp.aggregateListConfig.clusterType;
                        break;
                    }*/
                }
                //内部for循环已经查询到了，终止外层for循环
                if (mSelectRankModel.selectClusterType > 0) {
                    break;
                }
            }
        }
        //最终没有查询到selectClusterType，selectRankClusterId和selectRankingListId也没有意义，防止滑动到其他tab默认选中情况
        //清除selectRankingListId和selectRankClusterId
        if (mSelectRankModel.selectClusterType <= 0) {
            mSelectRankModel.selectRankingListId = -1;
        }
    }

    /**
     * 获取子页面相关的分组和分类信息
     */
    public NewRankGroup getAggregateRankCategoryModelByClusterType(long clusterType) {
        for (int i = 0; i < mAggregateRankList.size(); i++) {
            NewRankGroup itemRsp = mAggregateRankList.get(i);
            if (itemRsp == null || itemRsp.getGroupId() == null || itemRsp.getGroupName() == null
                    || CollectionUtil.isNullOrEmpty(itemRsp.getRankingLists())) {
                continue;
            }
            if (itemRsp.getGroupId() == clusterType) {
                return itemRsp;
            }
        }
        return null;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_iv_back) {
            // 听书排行榜（代码）-返回键  点击事件
            new XMTraceApi.Trace()
                    .click(45520)
                    .put("currPage", "BookLeaderboards")
                    .createTrace();
            finishFragment();
        } else if (id == R.id.main_iv_search) {
            // 听书排行榜（代码）-搜索键  点击事件
            new XMTraceApi.Trace()
                    .click(45521)
                    .put("currPage", "BookLeaderboards")
                    .createTrace();
            dealWithSearchClick();
        }
    }

    /**
     * 处理搜索点击
     */
    private void dealWithSearchClick() {
        try {
            BaseFragment fragment = Router.getSearchActionRouter().getFragmentAction().newSearchFragment();
            if (fragment != null) {
                startFragment(fragment);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected String getPageLogicName() {
        return "NewAggregateRankFragment";
    }
}
