package com.ximalaya.ting.lite.main.album.adapter;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.lite.main.base.BaseMainAlbumAdapter;

import java.util.List;

/**
 * Created by easoll on 17/6/23.
 *
 * <AUTHOR>
 */

public class RankAlbumAdapter extends BaseMainAlbumAdapter {
    public RankAlbumAdapter(MainActivity activity, List<Album> listData) {
        super(activity, listData);
    }

    private static final int VIEW_TYPE_NORMAL = 1;
    public static final int RANK_TYPE_ALL = 2;//总榜
    private int mType;

    @Override
    public int getOtherViewTypeCount() {
        return 1;
    }

    public void setType(int type) {
        this.mType = type;
    }

    @Override
    protected int getOtherViewType(int position, Object object) {
        if (object instanceof Album) {
            return VIEW_TYPE_NORMAL;
        }
        return super.getOtherViewType(position, object);
    }

    @Override
    public int getConvertViewIdByPosition(int position) {
        return R.layout.main_item_album_rank_normal;
    }

    @Override
    public HolderAdapter.BaseViewHolder buildHolderByPosition(View convertView, int position) {
        return new NormalRankAlbumHolder(convertView);
    }

    private void refreshRankPosition(TextView rankNumView, int rankPos) {
        rankNumView.setText(++rankPos + "");
        if (rankPos == 1) {
            rankNumView.setTextColor(Color.parseColor("#ff3131"));
        } else if (rankPos == 2) {
            rankNumView.setTextColor(Color.parseColor("#f6a623"));
        } else if (rankPos == 3) {
            rankNumView.setTextColor(Color.parseColor("#4990e2"));
        } else if (rankPos > 3) {
            rankNumView.setTextColor(Color.parseColor("#999999"));
        }
    }

    @Override
    public void bindViewDatas(HolderAdapter.BaseViewHolder h, Album t, int position) {
        super.bindViewDatas(h, t, position);
        AlbumM albumM;
        if (!(t instanceof AlbumM)) {
            return;
        }
        albumM = (AlbumM) t;
        NormalRankAlbumHolder holder = (NormalRankAlbumHolder) h;
        refreshRankPosition(holder.rankNum, position);
        holder.subTitle.setText(getDefaultSubTitle(albumM));
        holder.playCount.setText(StringUtil.getFriendlyNumStr(albumM.getPlayCount()));
        holder.trackCount.setText(StringUtil.getFriendlyNumStr(albumM.getIncludeTrackCount()) + " 集");
        if (!TextUtils.isEmpty(albumM.getCategoryTag()) && mType == RANK_TYPE_ALL) {
            holder.categoryTag.setText(albumM.getCategoryTag());
            holder.categoryTag.setVisibility(View.VISIBLE);
        } else {
            holder.categoryTag.setVisibility(View.GONE);
        }
    }

    public static class NormalRankAlbumHolder extends BaseMainAlbumHolder {
        public TextView rankNum;  //排名
        public TextView subTitle;  //副标题
        public TextView playCount; //播放量
        public TextView trackCount; //集数
        public TextView categoryTag;

        public NormalRankAlbumHolder(View convertView) {
            super(convertView);
            rankNum = (TextView) convertView.findViewById(R.id.main_tv_album_rank_num);
            subTitle = (TextView) convertView.findViewById(R.id.main_tv_album_subtitle);
            playCount = (TextView) convertView.findViewById(R.id.main_tv_play_count);
            trackCount = (TextView) convertView.findViewById(R.id.main_tv_track_count);
            categoryTag = (TextView) convertView.findViewById(R.id.main_tv_category_tag);
        }
    }
}
