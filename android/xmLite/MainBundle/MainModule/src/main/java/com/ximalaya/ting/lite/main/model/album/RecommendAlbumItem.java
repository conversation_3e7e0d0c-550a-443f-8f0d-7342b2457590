package com.ximalaya.ting.lite.main.model.album;


import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.model.track.TrackM;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by WolfXu on 2018/5/29.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class RecommendAlbumItem extends AlbumMInMain {

    //服务端类型，禁止随意修改
    public static int ALBUM_ITEM_UI_TYPE_DEF = 0;
    public static int ALBUM_ITEM_UI_TYPE_STYLE_V2 = 1; //纯专辑新样式
    public static int ALBUM_ITEM_UI_TYPE_STYLE_V3 = 2; //专辑+声音样式
    public static int ALBUM_ITEM_UI_TYPE_STYLE_V4 = 3; //专辑+评分样式
    @SerializedName("uiType")
    private int uiType;  // 1：普通专辑条；2：热门专辑条

    @SerializedName("topTracks")
    private List<TrackM> topTracks; //专辑中的声音

    public boolean isAsc;

    public RecommendAlbumItem(String json) {
        super(json);
    }

    @Override
    public void parseAlbum(JSONObject album) throws JSONException {
        super.parseAlbum(album);
        if (album == null) {
            return;
        }
        if (album.has("uiType")) {
            uiType = album.optInt("uiType", ALBUM_ITEM_UI_TYPE_DEF);
        }
        isAsc = album.optBoolean("isAsc", true);
        if (album.has("topTracks")) {
            JSONArray jsonTrackArray = album.optJSONArray("topTracks");
            if (jsonTrackArray != null && jsonTrackArray.length() > 0) {
                for (int i = 0; i < jsonTrackArray.length(); i++) {
                    String object = jsonTrackArray.optString(i);
                    if (TextUtils.isEmpty(object)) {
                        continue;
                    }
                    if (topTracks == null) {
                        topTracks = new ArrayList<>();
                    }
                    TrackM t = new TrackM(object);
                    topTracks.add(t);
                }
            }
        }
    }

    public int getUiType() {
        return uiType;
    }

    public void setUiType(int uiType) {
        this.uiType = uiType;
    }

    public List<TrackM> getTopTracks() {
        return topTracks;
    }

    public void setTopTracks(List<TrackM> topTracks) {
        this.topTracks = topTracks;
    }
}
