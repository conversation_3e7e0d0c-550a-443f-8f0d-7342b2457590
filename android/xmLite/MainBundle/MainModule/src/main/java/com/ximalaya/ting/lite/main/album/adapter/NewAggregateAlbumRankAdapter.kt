package com.ximalaya.ting.lite.main.album.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.util.TypedValue
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter
import com.ximalaya.ting.android.host.constants.LoginByConstants
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.ICollectStatusCallback
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.model.album.Album
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter


/**
 * Created by dumingwei on 2021/1/8
 *
 *
 * Desc:
 */
class NewAggregateAlbumRankAdapter(val baseFragment2: BaseFragment2?, activity: MainActivity?, listData: List<Album>?) : BaseAlbumAdapter(activity, listData) {

    private val dp11: Int = BaseUtil.dp2px(activity, 11f)
    private val dp13: Int = BaseUtil.dp2px(activity, 13f)

    override fun onClick(view: View, album: Album, position: Int, holder: BaseViewHolder) {
        //do nothing
    }

    override fun getConvertViewId(): Int {
        return R.layout.main_item_new_album_rank_normal_aggregate_album
    }

    @SuppressLint("SetTextI18n")
    override fun bindViewDatas(holder: BaseViewHolder, t: Album, position: Int) {
        super.bindViewDatas(holder, t, position)
        val viewHolder = holder as ViewHolder
        hideAllViews(viewHolder)
        if (t !is AlbumM) {
            return
        }

        // 听书排行榜（代码）-专辑item  控件曝光
        XMTraceApi.Trace()
            .setMetaId(45525)
            .setServiceId("slipPage")
            .put("albumId", t.id.toString())
            .put("currPage", "BookLeaderboards")
            .put("exploreType", "BookLeaderboards")
            .createTrace()

        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, t)
        //添加专辑Item的ContentDescription
        if (viewHolder.root != null) {
            if (!TextUtils.isEmpty(t.albumTitle)) {
                viewHolder.root.contentDescription = t.albumTitle
            } else {
                viewHolder.root.contentDescription = ""
            }
        }
        refreshRankPosition(viewHolder, position)
        ImageManager.from(context).displayImage(viewHolder.cover,
            t.largeCover, R.drawable.host_default_album_145, R.drawable.host_default_album_145)
        val textSize = viewHolder.title.textSize.toInt()
        val titleWithTag = AlbumAdapter.getRichTitle(t, context, textSize)
        viewHolder.title.text = titleWithTag

        //专辑评分
        if (t.score > 0.5) {
            //展示评分
            viewHolder.tvAlbumScore.visibility = View.VISIBLE
            viewHolder.tvAlbumScore.text = context.getString(R.string.main_album_score_format, t.score)

            //隐藏播放量
            viewHolder.tvAlbumAllCount.visibility = View.GONE
            viewHolder.tvAlbumAllCount.text = ""
        } else {
            //隐藏评分
            viewHolder.tvAlbumScore.visibility = View.GONE
            viewHolder.tvAlbumScore.text = ""

            //展示播放量
            viewHolder.tvAlbumAllCount.visibility = View.VISIBLE
            viewHolder.tvAlbumAllCount.text = StringUtil.getFriendlyNumStr(t.getPlayCount()) + " 播放"
        }

        //展示标签
        val albumTags = getFirstAlbumTags(t)
        if (TextUtils.isEmpty(albumTags)) {
            //隐藏专辑标签
            viewHolder.tvAlbumTags.visibility = View.GONE
            viewHolder.tvAlbumTags.text = ""
            //隐藏分割线
            viewHolder.viewAlbumTagSplitLine.visibility = View.GONE
        } else {
            //展示专辑标签
            viewHolder.tvAlbumTags.visibility = View.VISIBLE
            viewHolder.tvAlbumTags.text = albumTags
            //展示分割线
            viewHolder.viewAlbumTagSplitLine.visibility = View.VISIBLE
        }

        //设置订阅按钮
        if (t.isFavorite) {
            viewHolder.tvAlbumSubscribe.text = "已订"
            viewHolder.tvAlbumSubscribe.background = ContextCompat.getDrawable(context, R.drawable.main_bg_cccccc_stroke_radius_dp100)
            viewHolder.tvAlbumSubscribe.setTextColor(Color.parseColor("#cccccc"))
        } else {
            viewHolder.tvAlbumSubscribe.text = "订阅"
            viewHolder.tvAlbumSubscribe.background = ContextCompat.getDrawable(context, R.drawable.main_bg_e83f46_stroke_radius_dp100)
            viewHolder.tvAlbumSubscribe.setTextColor(Color.parseColor("#e83f46"))
        }
        //设置订阅点击
        viewHolder.tvAlbumSubscribe.setOnClickListener {
            dealWithAlbumSubscribeClick(album = t)
        }
    }


    /**
     * 专辑订阅被点击
     */
    private fun dealWithAlbumSubscribeClick(album: Album) {
        // 听书排行榜（代码）-订阅btn  点击事件
        XMTraceApi.Trace()
            .click(45526)
            .put("albumId", album.id.toString())
            .put("currPage", "BookLeaderboards")
            .createTrace()

        if (!UserInfoMannage.hasLogined()) {
            val loginParams = Bundle()
            LoginBundleParamsManager.setLoginTitle(loginParams, "订阅需登录哦")
            UserInfoMannage.gotoLogin(context, LoginByConstants.LOGIN_BY_DEFUALT, loginParams)
            return
        }
        if (album !is AlbumM) {
            return
        }
        val albumM: AlbumM = album
        AlbumEventManage.doCollectActionV2(albumM, baseFragment2, object : ICollectStatusCallback {
            override fun onCollectSuccess(code: Int, isCollected: Boolean) {
                if (baseFragment2 == null) {
                    return
                }
                if (!baseFragment2.canUpdateUi()) {
                    return
                }
                albumM.isFavorite = isCollected
                notifyDataSetChanged()
            }

            override fun onError() {

            }
        })
    }

    private fun refreshRankPosition(viewHolder: ViewHolder?, rankPos: Int) {
        if (viewHolder == null) {
            return
        }
        val rank = rankPos + 1
        val rankNumber = rank.toString() + ""
        viewHolder.tvRankNumber.text = rankNumber
        if (rank == 1) {
            viewHolder.ivRankFlag.setImageResource(R.drawable.main_ic_new_rank_no_1)
            viewHolder.tvRankNumber.visibility = View.INVISIBLE
        } else if (rank == 2) {
            viewHolder.ivRankFlag.setImageResource(R.drawable.main_ic_new_rank_no_2)
            viewHolder.tvRankNumber.visibility = View.INVISIBLE
        } else if (rank == 3) {
            viewHolder.ivRankFlag.setImageResource(R.drawable.main_ic_new_rank_no_3)
            viewHolder.tvRankNumber.visibility = View.INVISIBLE
        } else if (rank < 10) {
            viewHolder.ivRankFlag.setImageResource(R.drawable.main_ic_new_rank_no_4_to_9)
            viewHolder.tvRankNumber.visibility = View.VISIBLE
            viewHolder.tvRankNumber.text = rankNumber
            viewHolder.tvRankNumber.setTextSize(TypedValue.COMPLEX_UNIT_PX, dp13.toFloat())
        } else if (rank < 100) {
            viewHolder.ivRankFlag.setImageResource(R.drawable.main_ic_new_rank_no_10_to_99)
            viewHolder.tvRankNumber.visibility = View.VISIBLE
            viewHolder.tvRankNumber.setTextSize(TypedValue.COMPLEX_UNIT_PX, dp13.toFloat())
            viewHolder.tvRankNumber.text = rankNumber
        } else {
            viewHolder.ivRankFlag.setImageResource(R.drawable.main_ic_new_rank_no_100)
            viewHolder.tvRankNumber.visibility = View.VISIBLE
            viewHolder.tvRankNumber.setTextSize(TypedValue.COMPLEX_UNIT_PX, dp11.toFloat())
            viewHolder.tvRankNumber.text = rankNumber
        }
    }

    private fun getFirstAlbumTags(albumM: AlbumM): String {
        if (albumM.tagResults == null || albumM.tagResults.size == 0) {
            return "";
        }
        if (albumM.tagResults[0] == null) {
            ""
        }
        return albumM.tagResults[0].tagName
    }


    override fun buildHolder(convertView: View): BaseViewHolder {
        return ViewHolder(convertView)
    }

    class ViewHolder(convertView: View) : BaseAlbumAdapter.ViewHolder(convertView) {
        val tvRankNumber: TextView
        val ivRankFlag: ImageView
        val tvAlbumScore: TextView
        val tvAlbumTags: TextView
        val tvAlbumAllCount: TextView
        val tvAlbumSubscribe: TextView
        val viewAlbumTagSplitLine: View

        init {
            cover = convertView.findViewById(R.id.main_iv_album_cover)
            border = convertView.findViewById(R.id.main_album_border)
            title = convertView.findViewById(R.id.main_tv_album_title)
            tvRankNumber = convertView.findViewById(R.id.main_tv_album_rank_num)
            ivRankFlag = convertView.findViewById(R.id.main_iv_rank_flag)
            tvAlbumScore = convertView.findViewById(R.id.main_tv_album_score)
            tvAlbumTags = convertView.findViewById(R.id.main_tv_album_tags)
            viewAlbumTagSplitLine = convertView.findViewById(R.id.main_tv_album_tag_split_line)
            tvAlbumAllCount = convertView.findViewById(R.id.main_tv_album_all_count)
            tvAlbumSubscribe = convertView.findViewById(R.id.main_rank_subscribe)
        }
    }
}