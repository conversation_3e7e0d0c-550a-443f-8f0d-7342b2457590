package com.ximalaya.ting.lite.main.model.album;

import android.text.TextUtils;

import com.ximalaya.ting.android.host.model.base.BaseModel;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * Created by WolfXu on 2018/12/11.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class RecommendRealTimeFeedModel extends BaseModel {

    public static final String INSERT_POSITION_TYPE_BEHIND = "behind"; // 插入到操作位置后
    public static final String INSERT_POSITION_TYPE_NEXT_BATCH_FIRST = "nextBatchFirst"; // 插入到未展示数据第一位

    public static final int DISPLAY_TYPE_VERTICAL = 1;
    public static final int DISPLAY_TYPE_HORIZONTAL = 2;

    private long contentId;
    private String dataPositionType;
    private int displayType; // 1:默认竖排展示 2：横排展示
    private List<RecommendItemNew> data;

    public RecommendRealTimeFeedModel(String jsonStr) {
        if (!TextUtils.isEmpty(jsonStr)) {
            parseJson(jsonStr);
        }
    }

    public void parseJson(String jsonStr) {
        try {
            JSONObject jsonObject = new JSONObject(jsonStr);
            setRet(jsonObject.optInt("ret"));
            setMsg(jsonObject.optString("msg"));
            setContentId(jsonObject.optLong("contentId"));
            setDataPositionType(jsonObject.optString("dataPositionType"));
            setData(RecommendItemNew.parseRecommendItemList(jsonObject.optJSONArray("data")));
            setDisplayType(jsonObject.optInt("displayType"));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public long getContentId() {
        return contentId;
    }

    public void setContentId(long contentId) {
        this.contentId = contentId;
    }

    public String getDataPositionType() {
        return dataPositionType;
    }

    public void setDataPositionType(String dataPositionType) {
        this.dataPositionType = dataPositionType;
    }

    public List<RecommendItemNew> getData() {
        return data;
    }

    public void setData(List<RecommendItemNew> data) {
        this.data = data;
    }

    public int getDisplayType() {
        return displayType;
    }

    public void setDisplayType(int displayType) {
        this.displayType = displayType;
    }

    public RecommendItemNew covertToRecommendItemNew() {
        RecommendItemNew recommendItemNew = new RecommendItemNew();
        recommendItemNew.setItemType(RecommendItemNew.RECOMMEND_TYPE_LOCAL_REAL_TIME_RECOMMEND);
        recommendItemNew.setItem(getData());
        return recommendItemNew;
    }
}
