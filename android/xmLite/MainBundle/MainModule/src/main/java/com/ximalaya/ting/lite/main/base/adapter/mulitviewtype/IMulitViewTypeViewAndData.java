package com.ximalaya.ting.lite.main.base.adapter.mulitviewtype;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;

/**
 * @param <T>
 * @param <Model>
 * <AUTHOR>
 */
public interface IMulitViewTypeViewAndData<T extends HolderAdapter.BaseViewHolder, Model> {

    View getView(LayoutInflater layoutInflater, int position, ViewGroup parent);

    T buildHolder(View convertView);

    void bindViewDatas(T holder, ItemModel<Model> t, View convertView, int position);

}