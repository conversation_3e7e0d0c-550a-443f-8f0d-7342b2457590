package com.ximalaya.ting.lite.main.model.album;

import org.json.JSONObject;

public class AlbumUserInfo {
    private long uid;
    private String nickName;
    private String photoUrl;

    public AlbumUserInfo(JSONObject json) {
        this.uid = json.optLong("uid");
        this.nickName = json.optString("nickName");
        this.photoUrl = json.optString("photoUrl");
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhotoUrl() {
        return photoUrl;
    }

    public void setPhotoUrl(String photoUrl) {
        this.photoUrl = photoUrl;
    }
}
