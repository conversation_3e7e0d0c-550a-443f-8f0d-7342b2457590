package com.ximalaya.ting.lite.main.manager;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.util.common.TimeHelper;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.io.Serializable;
import java.util.Locale;
import java.util.Map;

public class ShortPlayFreeListenConfigManager {
    public static final int REQUEST_CONFIG_DEFAULT = 1000 * 60 * 30;

    private static int sRequestConfigIntervalMillis;   //请求配置的间隔时间

    private ShortPlayFreeListenConfigManager() {
    }

    /**
     * 新版本是否还支持自动解锁
     * @return
     */
    public static boolean isCompatAutoUnlock() {
        try {
            boolean support = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME,
                    "short_play_free_unlock_compat_auto_unlock_0108");
            if (ConstantsOpenSdk.isDebug) {
                Logger.w("ShortPlayFree", "isCompatAutoUnlock: " + support);
            }
            return support;
        } catch (Exception e) {
            e.printStackTrace();
            if (ConstantsOpenSdk.isDebug) {
                Logger.w("ShortPlayFree", "isCompatAutoUnlock getError: " + e.getMessage());
            }
        }
        return true;
    }

    private static class Holder {
        private static final ShortPlayFreeListenConfigManager MANAGER = new ShortPlayFreeListenConfigManager();
    }

    public static ShortPlayFreeListenConfigManager getInstance() {
        return Holder.MANAGER;
    }

    private ListenConfig mListenConfig;
    private IHandleOk mRewardConfigCallBack;
    private boolean mHasRequestRewardConfig;
    private boolean hasInitFreeUnlockTipsConfig;
    private String configureJson;
    private TipsConfig mTipsConfig;

    public void requestConfig() {
        if (!isFreeListenOpen()) {
            Logger.d("ShortPlayFree", "requestConfig return ,not open >>> ");
            return;
        }
        Logger.d("ShortPlayFree", "requestConfig >>> ");

        getListenConfigRequestIntervalFromSetting();

        Map<String, String> params = new ArrayMap<>();
        getFreeListenConfigNew(params, new IDataCallBack<ListenConfig>() {
            @Override
            public void onSuccess(@Nullable ListenConfig object) {
                mListenConfig = object;
                Logger.d("ShortPlayFree", "requestConfig succeed >> " + object);

                if (mListenConfig != null) {
                    MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).saveInt(PreferenceConstantsInHost.KEY_SHORT_PLAY_FREE_LISTEN_REMOTE_SYNC_TIME , mListenConfig.syncTime);
//                    MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).saveInt(PreferenceConstantsInHost.KEY_FREE_LISTEN_NEED_COUNTDOWN_TIME , mListenConfig.rewardDuration);
                }
            }

            @Override
            public void onError(int code, String message) {
                Log.d("ShortPlayFree", "onError:" + code + ", " + message);
            }
        });
    }

    private void initFreeUnlockConfigs() {
        if (hasInitFreeUnlockTipsConfig) return;
        hasInitFreeUnlockTipsConfig = true;
        try {
            configureJson = ConfigureCenter.getInstance().getString(CConstants.Group_toc.GROUP_NAME,
                    "short_play_free_unlock_tips_assemble_0111");
            if (ConstantsOpenSdk.isDebug) {
                Logger.w("ShortPlayFree", "configureJson: " + configureJson);
            }
        } catch (Exception e) {
            e.printStackTrace();
            configureJson = DEFAULT_CONFIG_TIPS;
            if (ConstantsOpenSdk.isDebug) {
                Logger.w("ShortPlayFree", "configureJson getError: " + configureJson);
            }
        }
        initTipsConfig();
    }

    public TipsConfig getTipsConfig() {
        getInstance().initFreeUnlockConfigs();
        return mTipsConfig;
    }

    public static String getCoverUnlockButtonDescription() {
        TipsConfig config = getInstance().getTipsConfig();
        if (config == null) {
            return null;
        }
        return config.cover_text_button;
    }

    public static String getCoverTitleHint() {
        TipsConfig config = getInstance().getTipsConfig();
        if (config == null) {
            return null;
        }
        return config.cover_text_title;
    }

    public static String getCoverSubTitleHint() {
        TipsConfig config = getInstance().getTipsConfig();
        if (config == null) {
            return null;
        }
        return config.cover_text_subtitle;
    }

    public static String getNoRemainTimeTips() {
        TipsConfig config = getInstance().getTipsConfig();
        if (config == null) {
            return "";
        }
        return config.unlock_tips_no_remain_time;
    }

    public static String getUnlockSucceedToast() {
        TipsConfig config = getInstance().getTipsConfig();
        if (config == null) {
            return "成功领取%s全剧观看权益！";
        }
        return config.unlock_result_succeed;
    }

    public static String getUnlockFailedToast() {
        TipsConfig config = getInstance().getTipsConfig();
        if (config == null) {
            return "广告未看完，解锁失败";
        }
        return config.unlock_result_failed_not_watch_enough;
    }

    public static String getRemainTimeTips(int second) {
        TipsConfig config = getInstance().getTipsConfig();;
        if (config == null) {
            return "";
        }
        String string = config.unlock_tips_has_remain_time;
        try {
            return String.format(Locale.getDefault(), string, TimeHelper.getFriendlyLongTime(second));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static boolean enableCountDownInSelectPlayDialog() {
        TipsConfig config = getInstance().getTipsConfig();
        if (config == null) {
            return false;
        }
        int value = config.select_play_dialog_count_down;
        Logger.e("ShortPlayFree", "enableCountDownInSelectPlayDialog value:" + value);
        return value == 1;
    }

    public static int getRequestConfigIntervalMillis() {
        if (sRequestConfigIntervalMillis <= 0) {
            Logger.e("ShortPlayFree", "sRequestConfigIntervalMillis invalid, return default: " + REQUEST_CONFIG_DEFAULT);
            return REQUEST_CONFIG_DEFAULT;
        }
        return sRequestConfigIntervalMillis;
    }

    /**
     * 一个广告兑换多久时长，单位秒
     * @return
     */
    public int getRewardDuration() {
        if (getListenConfig() != null) {
            return getListenConfig().rewardDuration;
        }
        return 300;
    }

    public int getAdDuration() {
        if (getListenConfig() != null) {
            return getListenConfig().adViewDuration;
        }
        return 30;
    }

    public void setOnReplaceConfigCallback(IHandleOk callback){
        if (mHasRequestRewardConfig){
            if (callback != null){
                callback.onReady();
            }
        } else {
            mRewardConfigCallBack = callback;
        }
    }

    private void notifyRewardFinish(){
        mHasRequestRewardConfig = true;
        if (mRewardConfigCallBack != null){
            mRewardConfigCallBack.onReady();
        }
    }

    // 获取时长模式相关配置
    public ListenConfig getListenConfig() {
        if (mListenConfig == null) {
            mListenConfig = getListenConfigFromSetting();
        }
        return mListenConfig;
    }

    public void initTipsConfig() {
        if (mTipsConfig != null) {
            return;
        }

        try {
            mTipsConfig = JsonUtilKt.getInstance().toObject(configureJson, TipsConfig.class);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
    }

    // 时长模式总开关判断
    public static boolean isFreeListenOpen() {
        if (ConstantsOpenSdk.isDebug) {
            return true;
        }
        Context context = BaseApplication.getMyApplicationContext();
        boolean enable = MmkvCommonUtil.getInstance(context).getBoolean(PreferenceConstantsInOpenSdk.KEY_SHORT_PLAY_FREE_LISTEN_ENABLED, false);
        Logger.d("ShortPlayFree", "isFreeListenOpen: " + enable);
        return enable;
    }

    /**
     * 配置从获取收听配置，兜底用
     * @return
     */
    public static ListenConfig getListenConfigFromSetting() {
        if (ConstantsOpenSdk.isDebug) {
            Logger.w("ShortPlayFree", "getListenConfigFromSetting >>>> ");
        }

        try {
            String json = ConfigureCenter.getInstance().getString(CConstants.Group_toc.GROUP_NAME,
                    "playletDurationSpareConfig");
            if (ConstantsOpenSdk.isDebug) {
                Logger.w("ShortPlayFree", "getListenConfigFromSetting: " + json);
            }
            return JsonUtilKt.getInstance().toObject(json, ListenConfig.class);
        } catch (Exception e) {
            e.printStackTrace();
            if (ConstantsOpenSdk.isDebug) {
                Logger.w("ShortPlayFree", "getListenConfigFromSetting getError: " + e.getMessage());
            }
        }
        return ListenConfig.DEFAULT;
    }

    public static void getListenConfigRequestIntervalFromSetting() {
        if (ConstantsOpenSdk.isDebug) {
            Logger.w("ShortPlayFree", "getListenConfigRequestIntervalFromSetting >>>> " + sRequestConfigIntervalMillis);
        }

        if (sRequestConfigIntervalMillis > 0) {
            return;
        }

        try {
            int intervalMS = ConfigureCenter.getInstance().getInt(CConstants.Group_toc.GROUP_NAME,
                    "playletDurationConfigCycleIntervalMS");

            sRequestConfigIntervalMillis = intervalMS;
            if (ConstantsOpenSdk.isDebug) {
                Logger.w("ShortPlayFree", "getListenConfigRequestIntervalFromSetting  intervalMS: " + intervalMS);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (ConstantsOpenSdk.isDebug) {
                Logger.w("ShortPlayFree", "getListenConfigRequestIntervalFromSetting getError: " + e.getMessage());
            }
        }
    }

    public static void getFreeListenConfigNew(Map<String, String> params, IDataCallBack<ShortPlayFreeListenConfigManager.ListenConfig> callBack) {
        String url = UrlConstants.getInstanse().getShortPlayFreeListenConfigUrl();
        Logger.w("ShortPlayFree", "requestConfig >>> " + url);
        CommonRequestM.baseGetRequest(url, params, callBack, new CommonRequestM.IRequestCallBack<ListenConfig>() {
            @Override
            public ShortPlayFreeListenConfigManager.ListenConfig success(String content) throws Exception {
                if (TextUtils.isEmpty(content)) {
                    return null;
                }
                JSONObject jsonObject = new JSONObject(content);
                if (jsonObject.optInt("ret") == 0 && jsonObject.has("data")) {
                    return JsonUtilKt.getInstance().toObject(jsonObject.optString("data"), ShortPlayFreeListenConfigManager.ListenConfig.class);
                }
                return null;
            }
        });
    }

    public static class ListenConfig implements Serializable {
        public static final ListenConfig DEFAULT = new ListenConfig(3000, 30, 300, 30);

        @SerializedName("playDurationLimit")
        public int playDurationLimit; // 收听时长动态上限，单位秒
        @SerializedName("adViewDuration")
        public int adViewDuration;// 激励广告观看时长，单位秒
        @SerializedName("rewardDuration")
        public int rewardDuration; // 激励广告兑价，单位秒
        @SerializedName("syncTime")
        public int syncTime ;// 播放时每30s进行扣减

        public ListenConfig() {
        }

        public ListenConfig(int playDurationLimit, int adViewDuration, int rewardDuration, int syncTime) {
            this.playDurationLimit = playDurationLimit;
            this.adViewDuration = adViewDuration;
            this.rewardDuration = rewardDuration;
            this.syncTime = syncTime;
        }

        @Override
        public String toString() {
            return "ListenConfig{" +
                    "playDurationLimit=" + playDurationLimit +
                    ", adViewDuration=" + adViewDuration +
                    ", rewardDuration=" + rewardDuration +
                    ", syncTime=" + syncTime +
                    '}';
        }
    }

    public static class TipsConfig implements Serializable {
        public String cover_text_title;
        public String cover_text_subtitle;
        public String cover_text_button;
        public String unlock_result_failed_not_watch_enough;
        public String unlock_result_succeed;
        public String unlock_tips_no_remain_time;
        public String unlock_tips_has_remain_time;
        public int select_play_dialog_count_down;
    }


    public static String DEFAULT_CONFIG_TIPS = "{\n" +
            "    \"cover_text_title\":\"精彩剧情等你来解锁\",\n" +
            "    \"cover_text_subtitle\":\"看激励视频解锁%s观看权益\",\n" +
            "    \"cover_text_button\":\"观看激励视频\",\n" +
            "    \"unlock_result_failed_not_watch_enough\":\"广告未看完，解锁失败\",\n" +
            "    \"unlock_result_succeed\":\"成功领取%s全剧观看权益\",\n" +
            "    \"unlock_tips_no_remain_time\":\"本剧支持看广告解锁\",\n" +
            "    \"unlock_tips_has_remain_time\":\"解锁权益剩余时长：%s\",\n" +
            "    \"select_play_dialog_count_down\":1\n" +
            "}";
}
