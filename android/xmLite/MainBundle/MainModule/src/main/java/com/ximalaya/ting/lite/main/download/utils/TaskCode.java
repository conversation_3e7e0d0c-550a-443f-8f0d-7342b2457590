package com.ximalaya.ting.lite.main.download.utils;

/**
 * <AUTHOR> feiwen
 * date   : 2019/4/26
 * desc   :
 */
public class TaskCode {
    // 通用 -10~10
    /**
     * 无效错误代码
     */
    public static final int INVALID = -1;
    /**
     * 成功
     */
    public static final int SUCCESS = 0;
    /**
     * 失败
     */
    public static final int FAILURE = -2;
    /**
     * 未知错误
     */
    public static final int ERROR = -3;

    // 任务相关的返回信息 1~100

    /**
     * 任务已经存在
     */
    public static final int EXIST = 1;
    /**
     * 任务已经存在,并且正在下载
     */
    public static final int EXIST_DOWNLOADING = 2;
    /**
     * 任务已经存在,并且已完成
     */
    public static final int EXIST_DONE = 3;
    /**
     * 下载服务未开启
     */
    public static final int ERROR_SERVICE_PENDDING = 4;

    // 错误信息 -10~-100
    /**
     * 任务信息验证不通过
     */
    public static final int ERROR_INVALID_INFO = -10;
    /**
     * 创建任务失败
     */
    public static final int ERROR_CREATE_TASK_FAILED = -11;
    /**
     * 网络失败
     */
    public static final int ERROR_NET = -12;
    /**
     * 下载前，断定了空间不足
     */
    public static final int ERROR_INSUFFICIENT_DISK_SPACE = -13;
    /**
     * 下载地址异常，为空或者是解析失败
     */
    public static final int ERROR_URL_EXCEPTION = -14;
    /**
     * 传输过程中中断或者不完整
     */
    public static final int ERROR_TRANSPORT_BREAK = -15;
    /**
     * 传输过程其他错误
     */
    public static final int ERROR_UNCAPTURED_EXCEPTION = -16;

    // 软件应用相关错误 -200～-300
    /**
     * APK包验证失败
     */
    public static final int ERROR_APK_INVAID = -201;

    /**
     * 207接口访问后，返回了错误的代码或者JSON解析失败
     */
    public static final int ERROR_JSON_FAILED = -204;

    // 网络错误的细化 -400～-500
    /**
     * 服务端无响应
     */
    public static final int ERROR_NO_HTTP_RESPONSE_EXCEPTION = -401;
    /**
     * 连接到服务端超时，或者无法从连接池中获取可用连接（指当前无网络的时候报出异常，或者服务端响应超过指定的时间）
     */
    public static final int ERROR_CONNECT_TIMEOUT_EXCEPTION = -402;
    /**
     * 套接字读取超时异常（可能由于突然断网或者连接突然断开引起）
     */
    public static final int ERROR_SOCKET_TIMEOUT_EXCEPTION = -403;
    /**
     * 无法通过指定的端口连接到远程主机或者说无法与主机交互
     */
    public static final int ERROR_CONNECT_EXCEPTION = -404;
    /**
     * 继承ERROR_CONNECTEXCEPTION错误，专门指定该错误为连接主机时错误
     */
    public static final int ERROR_HTTP_HOST_CONNECT_EXCEPTION = -405;
    /**
     * 抛出这一异常指示出现了未知服务异常可能是 URL 连接返回的 MIME 类型无意义，或者应用程序试图进行对只读的 URL 连接写入
     */
    public static final int ERROR_UNKNOWN_SERVICE_EXCEPTION = -406;
    /**
     * 指示主机 IP 地址无法确定而抛出的异常
     */
    public static final int ERROR_UNKNOWN_HOST_EXCEPTION = -407;
    /**
     * 获取不到下载长度的异常
     */
    public static final int ERROR_CANNOT_FETCH_DOWNLOAD_SIZE = -409;
}
