package com.ximalaya.ting.lite.main.customize;

import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.customize.CustomizeManager;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.user.CustomizeCategory;
import com.ximalaya.ting.android.host.model.user.InterestCardModel;
import com.ximalaya.ting.android.host.model.user.InterestCardSetting;
import com.ximalaya.ting.android.host.model.user.NewInterestCardModel;
import com.ximalaya.ting.android.host.model.user.NewInterestCardResp;
import com.ximalaya.ting.android.host.util.AssertManagerUtil;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.host.fragment.earn.DeleteSignInEventPromptFragment;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> on 2017/6/29.
 */
public class CustomizeFragment extends BaseFragment2 implements View.OnClickListener {

    private static final String TAG = "CustomizeFragment";

    private static final String INTERESTCARDSETTING = "INTERESTCARDSETTING";

    private static final int MIN_CAN_SELECT = 3;           //最少要选择3个兴趣卡片
    private static final int MAX_CAN_SELECT = 10;           //最多可选中10个兴趣卡片

    private static final int SPAN_COUNT = 3;               //网格一行有几个item

    private TextView tvCustomizeTitle;
    private TextView tvCustomizeSubTitle;
    private Button mBtnComplete;

    //用户选择的数据
    private InterestCardModel mSelectData;
    private LinearLayout mHobbyContentLayout;
    private final ArrayList<CustomizeCategory> mCategoryList = new ArrayList<>();
    private RecyclerView mRvCategory;

    private int from = -1;
    private String mRecommTaitKey = "defaultKey";

    private int mMinCanSelected = MIN_CAN_SELECT;
    private int mMaxCanSelected = MAX_CAN_SELECT;

    private final List<String> interestBgColorList = new ArrayList<>(12);

    /**
     * 铺平后的兴趣卡片列表
     */
    private final ArrayList<NewInterestCardModel> flatInterestList = new ArrayList<>();
    private InterestCardAdapter mInterestAdapter;

    private DeleteSignInEventPromptFragment promptDialogFragment;

    private long mLastClickTime = 0;

    public static CustomizeFragment newInstance(InterestCardSetting setting) {
        CustomizeFragment customizeFragment = new CustomizeFragment();
        Bundle extra = new Bundle();
        extra.putParcelable(INTERESTCARDSETTING, setting);
        customizeFragment.setArguments(extra);
        return customizeFragment;
    }

    public CustomizeFragment() {
        super(false, null);
    }

    @Override
    protected String getPageLogicName() {
        getClass();
        return getClass().getSimpleName();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle args = getArguments();
        if (args != null) {
            InterestCardSetting setting = args.getParcelable(INTERESTCARDSETTING);
            if (setting != null) {
                from = setting.getFrom();
                mMinCanSelected = setting.getInterestCardMinCount();
                mMaxCanSelected = setting.getInterestCardMaxCount();
            }
        }

        mSelectData = new InterestCardModel();
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
        setTitle("");
        LocalImageUtil.setBackgroundDrawable(titleBar.getTitleBar(), null);
        if (from == 0) {//来自首页
            titleBar.getBack().setVisibility(View.INVISIBLE);
        } else {
            titleBar.getBack().setVisibility(View.VISIBLE);
        }
        if (titleBar.getBack().getVisibility() == View.VISIBLE) {
            titleBar.getBack().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mSelectData != null && CollectionUtil.isNotEmpty(mSelectData.interestedCategories)) {
                        showPromptDialog();
                    } else {
                        exit();
                    }
                }
            });
            AutoTraceHelper.bindData(titleBar.getBack(), "");
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        tvCustomizeTitle = findViewById(R.id.main_customize_title);
        tvCustomizeSubTitle = findViewById(R.id.main_customize_subtitle);
        mBtnComplete = findViewById(R.id.main_btn_complete);
        mBtnComplete.setOnClickListener(this);
        AutoTraceHelper.bindData(mBtnComplete, mSelectData);
        mHobbyContentLayout = findViewById(R.id.main_custom_hobby_content);
        mHobbyContentLayout.setClipChildren(false);
        mRvCategory = findViewById(R.id.main_rv_custom_category);
        initInterestColorList();
        initRvCategory();

        chooseInterest();

        trackOnShow();
    }

    private void initInterestColorList() {
        interestBgColorList.add("#EDF3EE");
        interestBgColorList.add("#EAF2F4");
        interestBgColorList.add("#F4F4FA");

        interestBgColorList.add("#F7F1ED");
        interestBgColorList.add("#F7F4ED");
        interestBgColorList.add("#E9EEF1");

        interestBgColorList.add("#EDF3EE");
        interestBgColorList.add("#EAF2F4");
        interestBgColorList.add("#F4F4FA");

        interestBgColorList.add("#F7F1ED");
        interestBgColorList.add("#F7F4ED");
        interestBgColorList.add("#E9EEF1");
    }

    private void initRvCategory() {
        mInterestAdapter = new InterestCardAdapter(mActivity, SPAN_COUNT, flatInterestList);
        mInterestAdapter.setOnSelectedInterface(new InterestCardAdapter.OnSelectedInterface() {
            @Override
            public void onSelected(int position, @NotNull NewInterestCardModel model) {
                long currentTimeMillis = System.currentTimeMillis();
                if (currentTimeMillis - mLastClickTime < 300) {
                    return;
                }

                mLastClickTime = currentTimeMillis;

                boolean selected = model.getChosen();

                //添加到mSelectData.interestedCategories中
                List<String> interestedCategories = mSelectData.interestedCategories;

                if (selected) {
                    //先前是选中，现在取消选中
                    interestedCategories.remove(String.valueOf(model.getCode()));
                } else {
                    //在选中之前要检查是否已经选的够多了
                    if (interestedCategories.size() < mMaxCanSelected) {
                        new XMTraceApi.Trace()
                                .click(25791)
                                .put("currPage", "interestPage")
                                .put("item", model.getCategoryName())
                                .put("traitCode", mRecommTaitKey)
                                .createTrace();
                        interestedCategories.add(String.valueOf(model.getCode()));
                    } else {
                        CustomToast.showSuccessToast(getString(R.string.main_have_selected_too_many));
                        return;
                    }
                }
                //设置选中状态
                model.setChosen(!selected);

                List<NewInterestCardModel> subCategories = model.getSubCategories();
                mInterestAdapter.notifyItemChanged(position);

                if (CollectionUtil.isNotEmpty(subCategories)) {
                    //添加或者移除的开始位置
                    int startInsertPosition = position + 1;
                    int size = subCategories.size();
                    if (model.getChosen()) {//选中，如果有子级兴趣，则添加到适配器中
                        boolean childNotInFlatList = true;
                        for (NewInterestCardModel subCategory : subCategories) {
                            if (flatInterestList.contains(subCategory)) {
                                //至少有一个子级兴趣被选中
                                childNotInFlatList = false;
                                break;
                            }
                        }
                        //已经添加过了，就不再添加了
                        if (childNotInFlatList) {
                            for (int i = 0; i < size; i++) {
                                flatInterestList.add(startInsertPosition + i, subCategories.get(i));
                            }
                            mInterestAdapter.notifyItemRangeInserted(startInsertPosition, size);

                            mInterestAdapter.notifyItemRangeChanged(startInsertPosition, flatInterestList.size() - startInsertPosition);
                        }
                    } else {//取消选中，如果有子级兴趣，且没有一个子级兴趣被选中，则全部移除。
                        boolean noChildSelected = true;
                        for (NewInterestCardModel subCategory : subCategories) {
                            if (subCategory.getChosen()) {
                                //至少有一个子级兴趣被选中
                                noChildSelected = false;
                                break;
                            }
                        }
                        if (noChildSelected) {
                            flatInterestList.removeAll(subCategories);
                            mInterestAdapter.notifyItemRangeRemoved(startInsertPosition, size);
                            mInterestAdapter.notifyItemRangeChanged(startInsertPosition, flatInterestList.size() - startInsertPosition);
                        }
                    }
                }

                updatePageUi();
            }
        });

        mRvCategory.setAdapter(mInterestAdapter);
        mRvCategory.setLayoutManager(new GridLayoutManager(mActivity, SPAN_COUNT));

        DefaultItemAnimator defaultItemAnimator = new DefaultItemAnimator();
        mRvCategory.setItemAnimator(defaultItemAnimator);
    }

    @Override
    protected void loadData() {
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                onPageLoadingCompleted(LoadCompleteType.LOADING);
                HashMap<String, String> params = new HashMap<>();
                params.put("speed", "1");
                CommonRequestM.getCustomizeCategoriesV8(params, new IDataCallBack<NewInterestCardResp>() {
                    @Override
                    public void onSuccess(@Nullable NewInterestCardResp resp) {
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        Logger.i(TAG, "onSuccess setDataNoMatterSuccessOrFail");
                        setDataNoMatterSuccessOrFail(resp);
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (!canUpdateUi()) {
                            return;
                        }
                        Logger.i(TAG, "onError setDataNoMatterSuccessOrFail");
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        setDataNoMatterSuccessOrFail(null);
                    }
                });
            }
        });
    }

    private void setDataNoMatterSuccessOrFail(@Nullable NewInterestCardResp resp) {
        if (resp == null) {
            resp = new NewInterestCardResp();
        }
        if (CollectionUtil.isNullOrEmpty(resp.getList())) {
            ArrayList<NewInterestCardModel> localList = getInterestListFromLocal();
            //本地获取失败，只能展示没有内容的信息了
            if (CollectionUtil.isNullOrEmpty(localList)) {
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                return;
            } else {
                resp.setList(localList);
            }
        }
        if (!TextUtils.isEmpty(resp.getRecommTaitKey())) {
            mRecommTaitKey = resp.getRecommTaitKey();
        }

        List<NewInterestCardModel> interestCardModelList = resp.getList();
        flatInterestList.clear();

        //最多展示12个
        int maxShowSize = 12;
        if (interestCardModelList.size() <= maxShowSize) {
            flatInterestList.addAll(interestCardModelList);
        } else {
            List<NewInterestCardModel> tempList = interestCardModelList.subList(0, maxShowSize);
            flatInterestList.addAll(tempList);
        }

        if (CollectionUtil.isNotEmpty(flatInterestList)) {
            int colorListSize = interestBgColorList.size();
            for (int i = 0; i < flatInterestList.size(); i++) {
                flatInterestList.get(i).setBgColor(interestBgColorList.get(i % colorListSize));
            }
            onInterestCardFetched();
        }
    }

    private void onInterestCardFetched() {
        mInterestAdapter.notifyDataSetChanged();
        updatePageUi();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_customization;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    /**
     * 加载兴趣卡片页面
     */
    private void chooseInterest() {
        mHobbyContentLayout.setVisibility(View.VISIBLE);
        //需要重置，返回页面可能出现断网状态重叠
        onPageLoadingCompleted(LoadCompleteType.OK);
        updatePageUi();
        //每次选中兴趣卡片，都清除保存的数据，年龄和性别不同所请求到的兴趣卡片不同，所以需要重新loadData()
        mCategoryList.clear();
        mInterestAdapter.notifyDataSetChanged();
        mSelectData.interestedCategories.clear();
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public boolean isShowTruckFloatPlayBar() {
        return false;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_btn_complete) {
            postCustomizationInfo();
        }
    }

    /**
     * 用户点击按钮提交，带loading，带回调处理
     */
    private void postCustomizationInfo() {
        if (CollectionUtil.isNullOrEmpty(flatInterestList)) {
            return;
        }

        mBtnComplete.setEnabled(false);
        onPageLoadingCompleted(LoadCompleteType.LOADING);

        //埋点用
        //选中的个数
        int count = 0;
        int size = flatInterestList.size();
        StringBuilder builder = new StringBuilder();

        String categories = null;
        List<String> tempList = new ArrayList<>();

        for (int index = 0; index < size; index++) {
            NewInterestCardModel cardModel = flatInterestList.get(index);
            if (cardModel.getChosen() && !TextUtils.isEmpty(cardModel.getCode())) {
                tempList.add(cardModel.getCode());
                if (index > 0) {
                    builder.append(",");
                }
                builder.append(cardModel.getCategoryName());
                count++;
            }
        }

        if (CollectionUtil.isNotEmpty(tempList)) {
            categories = JsonUtilKt.getInstance().toJson(tempList);
        }

        Map<String, String> params = new HashMap<>();
        if (UserInfoMannage.hasLogined()) {
            params.put("uid", UserInfoMannage.getUid() + "");
        }
        params.put("deviceId", DeviceUtil.getDeviceToken(mContext));
        if (!TextUtils.isEmpty(categories)) {
            params.put("newCodes", categories);
        }
        LiteCommonRequest.postCustomizationInfo(params, new
                IDataCallBack<JSONObject>() {
                    @Override
                    public void onSuccess(JSONObject object) {
                        if (!canUpdateUi()) {
                            return;
                        }
                        setFinishCallBackData(true);
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        //兴趣卡片缓存到本地,要在首页刷新前保存
                        CustomizeManager.getInstance().saveLocalCustomize(mSelectData);
                        showSuccessToastAndFinish();
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        mBtnComplete.setEnabled(true);
                        ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
                    }
                });

        trackOnSubmit(builder.toString(), count);
    }

    protected void showSuccessToastAndFinish() {
        if (from != 0) {
            CustomToast.showSuccessToast("修改成功");
        }
        try {
            finishFragment();
        } catch (Exception e) {
            //https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/24520679?pid=1
            e.printStackTrace();
        }
    }

    /**
     * upDate
     * 刷新当前页面的UI
     */
    private void updatePageUi() {
        tvCustomizeTitle.setText(getString(R.string.main_select_your_prefer_category));
        tvCustomizeSubTitle.setText(getString(R.string.main_first_recommend_for_you));

        List<String> interestedCategories = mSelectData.interestedCategories;
        if (CollectionUtil.isNullOrEmpty(interestedCategories)) {
            mBtnComplete.setEnabled(false);
            mBtnComplete.setText(getString(R.string.main_selected_at_least, mMinCanSelected));
        } else {
            int size = interestedCategories.size();
            if (size < mMinCanSelected) {
                mBtnComplete.setEnabled(false);
                mBtnComplete.setText(getString(R.string.main_have_selected, size, mMinCanSelected));
            } else {
                mBtnComplete.setEnabled(true);
                mBtnComplete.setText(getString(R.string.main_enjoy_now));
            }
        }

    }

    @Override
    public boolean onBackPressed() {
        statCloseAction();
        return super.onBackPressed();
    }

    private void showPromptDialog() {
        if (promptDialogFragment == null) {
            promptDialogFragment = new DeleteSignInEventPromptFragment();
            promptDialogFragment.setOnDialogConfirmListener(new BaseDialogFragment.OnDialogConfirmListener() {
                @Override
                public void onConfirm() {
                    exit();
                }
            });
            promptDialogFragment.setDialogTitle(getString(R.string.main_your_interest_not_saved));
        }
        if (canUpdateUi()) {
            promptDialogFragment.show(((MainActivity) mActivity).getSupportFragmentManager(), "");
        }
    }

    /**
     * 退出兴趣选择，没有完成提交，然后退出兴趣选择页，统一调用这个方法
     */
    private void exit() {
        finishFragment();
        statCloseAction();
    }

    private void statCloseAction() {
        new XMTraceApi.Trace()
                .pageExit2(25787)
                .put("currPage", "interestPage")
                .put("status", String.valueOf(from))//0表示是首页自动弹出的
                .createTrace();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    private void trackOnSubmit(String selectedCodes, int size) {
        if (!TextUtils.isEmpty(selectedCodes) && size > 0) {
            new XMTraceApi.Trace()
                    .click(25792)
                    .put("currPage", "interestPage")
                    .put("traitCode", mRecommTaitKey)
                    .put("status", String.valueOf(from))
                    .put("currItemId", selectedCodes)
                    .put("currItem", String.valueOf(size))
                    .createTrace();
        }
    }


    private void trackOnShow() {
        new XMTraceApi.Trace()
                .pageView(25786, "interestPage")
                .put("status", String.valueOf(from))//0表示是首页自动弹出的
                .createTrace();
    }

    /**
     * 跳过埋点
     */
    private void trackSkip() {
        new XMTraceApi.Trace()
                .click(25793)
                .put("currPage", "interestPage")
                .createTrace();
    }

    @Override
    protected boolean isShowCoinGuide() {
        return false;
    }

    private ArrayList<NewInterestCardModel> getInterestListFromLocal() {
        ArrayList<NewInterestCardModel> resultList = new ArrayList<>();
        List<NewInterestCardModel> localList = null;

        try {
            String json = AssertManagerUtil.getJson("interestCardV9.json", mContext);
            localList = JsonUtilKt.getInstance().toList(json,
                    new TypeToken<List<NewInterestCardModel>>() {
                    }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CollectionUtil.isNotEmpty(localList)) {
            resultList.addAll(localList);
        }
        return resultList;
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        if (mInterestAdapter != null) {
            mInterestAdapter.notifyDataSetChanged();
        }
    }
}
