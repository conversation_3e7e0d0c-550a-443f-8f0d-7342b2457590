package com.ximalaya.ting.lite.main.home.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.ximalaya.ting.android.framework.adapter.HolderAdapter
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.model.newhome.LiteChildTab
import com.ximalaya.ting.lite.main.model.newhome.LiteChildTabProviderModel
import com.ximalaya.ting.lite.main.view.LinearItemDecoration
import com.ximalaya.ting.lite.main.view.LiteSingleChildTabView
import com.ximalaya.ting.lite.main.view.RecyclerViewCanDisallowIntercept
import com.ximalaya.ting.lite.main.vip.listener.LiteChildTabChangeListener


/**
 * Created by dumingwei on 2021/6/16
 *
 * Desc: 子Tab楼层
 */
class LiteChildTabProvider @JvmOverloads constructor(
    val mFragment: BaseFragment2,
    val tabChangeListener: LiteChildTabChangeListener?
) : IMulitViewTypeViewAndData<LiteChildTabProvider.Holder, LiteChildTabProviderModel> {

    private val TAG: String = "LiteChildTabProvider"

    private var dp60: Int = 0
    private var dp50: Int = 0
    private var dp30: Int = 0

    private var halfSpacing: Int = 0
    private var margin: Int = 0

    private var isTabChange = false

    init {
        dp60 = BaseUtil.dp2px(mFragment.context, 60f)
        dp50 = BaseUtil.dp2px(mFragment.context, 50f)
        dp30 = BaseUtil.dp2px(mFragment.context, 30f)

        halfSpacing = BaseUtil.dp2px(mFragment.context, 20f)
        margin = BaseUtil.dp2px(mFragment.context, 12f)

    }

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_child_tab_provider, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView)
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<LiteChildTabProviderModel>, convertView: View, position: Int) {
        val model = t.getObject()
        val tabList = model.tabList
        Logger.d(TAG, "bindViewDatas" + tabList.hashCode())
        val size = tabList.size
        if (size <= 4) {
            bindAverageViews(holder, size, tabList)
        } else {
            bindRvScrollViews(holder, size, tabList)
        }
    }

    private fun bindAverageViews(holder: Holder, size: Int, tabList: List<LiteChildTab>) {
        //使用线性布局均分
        val viewList = arrayListOf<LiteSingleChildTabView>()

        holder.llAverageLayout.visibility = View.VISIBLE
        AutoTraceHelper.setLabelForCTWithMultiSameSubView(holder.llAverageLayout)

        holder.rvScrollView.visibility = View.GONE
        with(holder) {
            when (size) {
                2 -> {
                    childTabView0.visibility = View.VISIBLE
                    childTabView1.visibility = View.VISIBLE

                    childTabView2.visibility = View.GONE
                    childTabView3.visibility = View.GONE

                    //修改间距
                    val layoutParams1 = childTabView1.layoutParams as LinearLayout.LayoutParams
                    layoutParams1.marginStart = dp60
                    childTabView1.layoutParams = layoutParams1

                    val drawable0 = mFragment.context?.resources?.getDrawable(R.drawable.main_bg_gradient_7bcfff_ebf8ff_radius_4)
                    childTabView0.setData(tabList[0], drawable0)

                    val drawable1 = mFragment.context?.resources?.getDrawable(R.drawable.main_bg_gradient_fd639d_ffd8e7_radius_4)
                    childTabView1.setData(tabList[1], drawable1)

                    //childTabView0.setSelectedStatus(false)
                    //childTabView1.setSelectedStatus(false)

                    viewList.add(childTabView0)
                    viewList.add(childTabView1)
                }
                3 -> {
                    childTabView0.visibility = View.VISIBLE
                    childTabView1.visibility = View.VISIBLE
                    childTabView2.visibility = View.VISIBLE

                    childTabView3.visibility = View.GONE

                    //修改间距
                    val layoutParams1 = childTabView1.layoutParams as LinearLayout.LayoutParams
                    layoutParams1.marginStart = dp50
                    childTabView1.layoutParams = layoutParams1

                    val layoutParams2 = childTabView2.layoutParams as LinearLayout.LayoutParams
                    layoutParams2.marginStart = dp50
                    childTabView2.layoutParams = layoutParams2

                    val drawable = mFragment.context?.resources?.getDrawable(R.drawable.main_bg_gradient_ffd9dc_fa696f_radius_4)

                    childTabView0.setData(tabList[0], drawable)
                    childTabView1.setData(tabList[1], drawable)
                    childTabView2.setData(tabList[2], drawable)

                    //childTabView0.setSelectedStatus(false)

                    //childTabView1.setSelectedStatus(false)
                    //childTabView2.setSelectedStatus(false)

                    viewList.add(childTabView0)
                    viewList.add(childTabView1)
                    viewList.add(childTabView2)

                }
                else -> {
                    childTabView0.visibility = View.VISIBLE
                    childTabView1.visibility = View.VISIBLE
                    childTabView2.visibility = View.VISIBLE
                    childTabView3.visibility = View.VISIBLE

                    //修改间距
                    val layoutParams1 = childTabView1.layoutParams as LinearLayout.LayoutParams
                    layoutParams1.marginStart = dp30
                    childTabView1.layoutParams = layoutParams1

                    val layoutParams2 = childTabView2.layoutParams as LinearLayout.LayoutParams
                    layoutParams2.marginStart = dp30
                    childTabView2.layoutParams = layoutParams2

                    val layoutParams3 = childTabView3.layoutParams as LinearLayout.LayoutParams
                    layoutParams3.marginStart = dp30
                    childTabView3.layoutParams = layoutParams3

                    val drawable = mFragment.context?.resources?.getDrawable(R.drawable.main_bg_gradient_ffd9dc_fa696f_radius_4)

                    childTabView0.setData(tabList[0], drawable)
                    childTabView1.setData(tabList[1], drawable)
                    childTabView2.setData(tabList[2], drawable)
                    childTabView3.setData(tabList[3], drawable)

                    //childTabView0.setSelectedStatus(false)

                    //childTabView1.setSelectedStatus(false)
                    //childTabView2.setSelectedStatus(false)
                    //childTabView3.setSelectedStatus(false)

                    viewList.add(childTabView0)
                    viewList.add(childTabView1)
                    viewList.add(childTabView2)
                    viewList.add(childTabView3)

                }
            }

            // 切换tab时会触发刷新
            if (isTabChange) {
                isTabChange = false
            } else {
                tabList.forEach { item ->
                    // 分类页-子Tab  控件曝光
                    XMTraceApi.Trace()
                        .setMetaId(32938)
                        .setServiceId("slipPage")
                        .put("moduleId", item.moduleId)
                        .put("tabId", item.tabId.toString()) // 子tab所属的pageid
                        .put("currModule", item.title)
                        .put("currPage", "categoryPageV2")
                        .createTrace()
                }
            }

            if (viewList.size == size) {
                for (i in tabList.indices) {
                    if (tabList[i].selectd) {
                        viewList[i].setSelectedStatus(true)
                    } else {
                        viewList[i].setSelectedStatus(false)
                    }
                }
            }

            childTabView0.setOnClickListener {
                //已经选中，不再做操作
                if (childTabView0.getData()?.selectd == true) {
                    return@setOnClickListener
                }

                // 分类页-子Tab  点击事件
                XMTraceApi.Trace()
                    .click(32937)
                    .put("moduleId", tabList[0].moduleId)
                    .put("tabId", tabList[0].tabId.toString())
                    .put("currModule", tabList[0].title)
                    .put("currPage", "categoryPageV2")
                    .createTrace()

                childTabView0.setSelectedStatus(true)

                childTabView1.setSelectedStatus(false)
                if (childTabView2.visibility == View.VISIBLE) {
                    childTabView2.setSelectedStatus(false)
                }
                if (childTabView3.visibility == View.VISIBLE) {
                    childTabView3.setSelectedStatus(false)
                }

                onChildTabChanged(childTabView0.getData())

            }
            childTabView1.setOnClickListener {
                if (childTabView1.getData()?.selectd == true) {
                    return@setOnClickListener
                }

                // 分类页-子Tab  点击事件
                XMTraceApi.Trace()
                    .click(32937)
                    .put("moduleId", tabList[1].moduleId)
                    .put("tabId", tabList[1].tabId.toString())
                    .put("currModule", tabList[1].title)
                    .put("currPage", "categoryPageV2")
                    .createTrace()

                childTabView0.setSelectedStatus(false)
                childTabView1.setSelectedStatus(true)
                onChildTabChanged(childTabView1.getData())

                if (childTabView2.visibility == View.VISIBLE) {
                    childTabView2.setSelectedStatus(false)
                }
                if (childTabView3.visibility == View.VISIBLE) {
                    childTabView3.setSelectedStatus(false)
                }
            }
            childTabView2.setOnClickListener {
                if (childTabView2.getData()?.selectd == true) {
                    return@setOnClickListener
                }

                // 分类页-子Tab  点击事件
                XMTraceApi.Trace()
                    .click(32937)
                    .put("moduleId", tabList[2].moduleId)
                    .put("tabId", tabList[2].tabId.toString())
                    .put("currModule", tabList[2].title)
                    .put("currPage", "categoryPageV2")
                    .createTrace()

                childTabView0.setSelectedStatus(false)
                childTabView1.setSelectedStatus(false)
                childTabView2.setSelectedStatus(true)

                onChildTabChanged(childTabView2.getData())


                if (childTabView3.visibility == View.VISIBLE) {
                    childTabView3.setSelectedStatus(false)
                }
            }
            childTabView3.setOnClickListener {
                if (childTabView3.getData()?.selectd == true) {
                    return@setOnClickListener
                }

                // 分类页-子Tab  点击事件
                XMTraceApi.Trace()
                    .click(32937)
                    .put("moduleId", tabList[3].moduleId)
                    .put("tabId", tabList[3].tabId.toString())
                    .put("currModule", tabList[3].title)
                    .put("currPage", "categoryPageV2")
                    .createTrace()

                childTabView0.setSelectedStatus(false)
                childTabView1.setSelectedStatus(false)
                childTabView2.setSelectedStatus(false)

                childTabView3.setSelectedStatus(true)

                onChildTabChanged(childTabView3.getData())
            }
        }
    }

    private fun onChildTabChanged(childTab: LiteChildTab?) {
        childTab?.let {
            isTabChange = true
            tabChangeListener?.onChildTabChanged(it)
        }
    }

    private fun bindRvScrollViews(holder: Holder, size: Int, tabList: List<LiteChildTab>) {
        //使用线性布局均分
        holder.llAverageLayout.visibility = View.GONE
        holder.rvScrollView.visibility = View.VISIBLE
        holder.rvScrollView.setDisallowInterceptTouchEventView(mFragment.view as ViewGroup)
        with(holder) {
            if (rvScrollView.adapter == null) {

                val targetPosition: Int = tabList.indexOfFirst {
                    it.selectd
                }

                rvScrollView.layoutManager = LinearLayoutManager(mFragment.context, LinearLayoutManager.HORIZONTAL, false)
                rvScrollView.addItemDecoration(LinearItemDecoration(halfSpacing, margin))
                val adapter = LiteChildTabScrollStyleAdapter(mFragment, tabList)
                adapter.itemClickAction = {
                    //Logger.i(TAG, "itemClickAction = $it")
                    tabChangeListener?.onChildTabChanged(it)
                }
                rvScrollView.adapter = adapter

                if (targetPosition != -1) {
                    rvScrollView.post {
                        rvScrollView.scrollToPosition(targetPosition)
                    }

                } else {
                    Logger.i(TAG, "do nothing")
                    // do nothing
                }

            } else {
                (rvScrollView.adapter as LiteChildTabScrollStyleAdapter?)?.mDataList = tabList
                rvScrollView.adapter?.notifyDataSetChanged()
            }
        }
    }

    class Holder(var rootView: View) : HolderAdapter.BaseViewHolder() {

        val llAverageLayout: LinearLayout = rootView.findViewById(R.id.main_ll_average_layout)
        val childTabView0: LiteSingleChildTabView = rootView.findViewById(R.id.main_single_child_tabview_0)
        val childTabView1: LiteSingleChildTabView = rootView.findViewById(R.id.main_single_child_tabview_1)
        val childTabView2: LiteSingleChildTabView = rootView.findViewById(R.id.main_single_child_tabview_2)
        val childTabView3: LiteSingleChildTabView = rootView.findViewById(R.id.main_single_child_tabview_3)

        val rvScrollView: RecyclerViewCanDisallowIntercept = rootView.findViewById(R.id.main_rv_scroll)

    }

}