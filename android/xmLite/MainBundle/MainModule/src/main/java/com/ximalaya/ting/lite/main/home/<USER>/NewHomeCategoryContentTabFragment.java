package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

import com.astuetz.PagerSlidingTabStrip;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter.FragmentHolder;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.data.model.category.Tag;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.search.ISearchFragmentActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 首页-具体分类页面
 *
 * <AUTHOR>
 */
public class NewHomeCategoryContentTabFragment extends BaseFragment2 implements OnClickListener {

    public static final String KEY_GENDER = "KEY_GENDER";

    private RelativeLayout rlTitleBar;
    private PagerSlidingTabStrip mTabs;
    private ViewPager mPager;
    private TabCommonAdapter mAdapter;

    //当前页面的分类
    private int mCategoryId = -1;

    //跳转进来，选中的metaid
    private int mMetadataValueId = -1;

    private int from = -1;
    private boolean useV2Api;

    private final List<FragmentHolder> mFragmentList = new CopyOnWriteArrayList<>();
    private final List<Tag> mTagList = new CopyOnWriteArrayList<>();

    //默认是男频
    private int mGender = 1;

    /**
     * 通过keywordId选中对应的tab
     *
     * @param gender   1 男频，2女频
     * @param useV2Api 是否使用v2版本的api
     */
    public static Bundle createArgumentSelectByMetaId(int categoryId, int metaid, int gender, boolean useV2Api) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_METADATA_VALUE_ID, metaid);
        bundle.putInt(KEY_GENDER, gender);
        bundle.putBoolean(BundleKeyConstantsInMain.KEYWORD_ALL_USE_V2_API, useV2Api);
        return bundle;
    }

    public NewHomeCategoryContentTabFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_RELATIVELAYOUT, null);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mCategoryId = arguments.getInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, -1);
            mGender = arguments.getInt(KEY_GENDER, 1);
            mMetadataValueId = arguments.getInt(BundleKeyConstantsInMain.KEY_METADATA_VALUE_ID, -1);
            from = arguments.getInt(BundleKeyConstantsInMain.KEY_FROM);
            useV2Api = arguments.getBoolean(BundleKeyConstantsInMain.KEYWORD_ALL_USE_V2_API);
        }
        setTitle("");
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_new_home_category_content_tab;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        rlTitleBar = findViewById(R.id.main_title_bar);
        mPager = findViewById(R.id.main_view_page_content);
        mTabs = findViewById(R.id.main_tabs);
        mTabs.setTabPaddingLeftRight(BaseUtil.dp2px(getActivity(), 17));
        ViewParent parent = mTabs.getParent();
        if (parent instanceof ViewGroup) {
            mTabs.setDisallowInterceptTouchEventView((ViewGroup) mTabs.getParent());
        }
        fitStatusBar();
        initListeners();
        buildTabs();
    }

    /**
     * 将titleBar往下移动
     */
    private void fitStatusBar() {
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rlTitleBar.getLayoutParams();
            params.topMargin = params.topMargin + BaseUtil.getStatusBarHeight(mContext);
            rlTitleBar.setLayoutParams(params);
        }
    }

    @Override
    protected void loadData() {
        /*final Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_CATEGORY_ID, mCategoryId + "");
        params.put(HttpParamsConstants.PARAM_CHANNEL, DeviceUtil.getChannelInApk(getActivity()));
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        params.put(HttpParamsConstants.PARAM_VERSION, DeviceUtil.getVersion(getActivity()));
        params.put(HttpParamsConstants.PARAM_CONTENT_TYPE, "album");
        params.put(HttpParamsConstants.PARAM_CATEGORY_GENDER, "9");
        params.put("deviceId", DeviceUtil.getDeviceToken(getActivity()));
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        String url = LiteUrlConstants.getHomeCategoryKeywords();
        if (useV2Api) {
            url = LiteUrlConstants.getHomeCategoryKeywordsV2();
            params.put("vipPage", String.valueOf(from));
        }
        LiteCommonRequest.getHomeCategoryKeywordsTag(url, params, new IDataCallBack<CategoryTagList>() {
            @Override
            public void onSuccess(final CategoryTagList object) {
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        if (object == null) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }
                        setTitle(object.getTitle());
                        doBuildTabs(object);
                        onPageLoadingCompleted(LoadCompleteType.OK);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
            }
        });*/
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
    }

    @Override
    protected String getPageLogicName() {
        return "NewHomeCategoryContentTabFragment";
    }

    /**
     * 初始化监听事件
     */
    private void initListeners() {
        ImageView ivBack = findViewById(R.id.main_iv_back);
        ImageView ivSearch = findViewById(R.id.main_iv_search);

        ivBack.setOnClickListener(this);
        ivSearch.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int id = view.getId();
        if (id == R.id.main_iv_back) {
            finishFragment();
        } else if (id == R.id.main_iv_search) {
            toSearchPage();
        }
    }


    private void buildTabs() {
        mFragmentList.clear();
        mTagList.clear();
        //默认选中哪个页面
        int selectDefPagePosition = 0;

        if (mGender == 2) {//选中女频
            selectDefPagePosition = 1;
        }

        if (selectDefPagePosition == 0) {//选中男频
            Bundle maleArgument = NewCategoryMetadataFragment.createArgumentFromTab(
                    mCategoryId, -1, mMetadataValueId, from, 1);
            FragmentHolder maleHolder = new FragmentHolder(NewCategoryMetadataFragment.class, "男生频道", maleArgument);
            //添加页面
            mFragmentList.add(maleHolder);

            //不把mMetadataValueId传给女频，让女频筛选项是默认状态
            Bundle femaleArgument = NewCategoryMetadataFragment.createArgumentFromTab(
                    mCategoryId, -1, -1, from, 2);
            FragmentHolder femaleHolder = new FragmentHolder(NewCategoryMetadataFragment.class, "女生频道", femaleArgument);
            //添加页面
            mFragmentList.add(femaleHolder);
        } else {//选中女频
            //不把mMetadataValueId传给男频，让男频筛选项是默认状态
            Bundle maleArgument = NewCategoryMetadataFragment.createArgumentFromTab(
                    mCategoryId, -1, -1, from, 1);
            FragmentHolder maleHolder = new FragmentHolder(NewCategoryMetadataFragment.class, "男生频道", maleArgument);
            //添加页面
            mFragmentList.add(maleHolder);

            Bundle femaleArgument = NewCategoryMetadataFragment.createArgumentFromTab(
                    mCategoryId, -1, mMetadataValueId, from, 2);
            FragmentHolder femaleHolder = new FragmentHolder(NewCategoryMetadataFragment.class, "女生频道", femaleArgument);
            //添加页面
            mFragmentList.add(femaleHolder);
        }

        mAdapter = new TabCommonAdapter(getChildFragmentManager(), mFragmentList);
        mPager.setAdapter(mAdapter);
        mPager.setCurrentItem(selectDefPagePosition);
        mTabs.setViewPager(mPager);

        //选中的时候设置一次
        setSlideAble(selectDefPagePosition == 0);

        mPager.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {

            @Override
            public void onPageSelected(int position) {
                setSlideAble(position == 0);
            }
        });
    }

    //不使用默认的TitleBar
    @Override
    public int getTitleBarResourceId() {
        return -1;
    }

    private void toSearchPage() {
        ISearchFragmentActionRouter fragmentAction = SearchActionRouter.getInstance().getFragmentAction();
        if (fragmentAction != null) {
            BaseFragment fragment = fragmentAction.newSearchFragmentByHotWord(SearchActionRouter.TYPE_CATEGORY,
                    mCategoryId, null);
            if (fragment != null) {
                startFragment(fragment);
            }
        } else {
            CustomToast.showFailToast("搜索模块加载失败，请联系客服");
        }
    }

    @Override
    public boolean canRepeatInActivity() {
        return true;
    }
}
