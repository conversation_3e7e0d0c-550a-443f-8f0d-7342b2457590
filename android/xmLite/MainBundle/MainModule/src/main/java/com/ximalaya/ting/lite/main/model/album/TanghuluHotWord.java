package com.ximalaya.ting.lite.main.model.album;


import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/8/14.
 * <p>
 * Desc:热词糖葫芦优化
 */
public class TanghuluHotWord {

    /**
     * item : {"categoryId":12,"keywordId":71,"keywordName":"热词1"}
     * itemType : keyword
     */

    public static String ITEM_ALL = "local_all";

    public static String ITEM_FEED = "feed";
    public static String ITEM_H5 = "H5";
    public static String ITEM_POOL = "pool";
    public static String ITEM_UTING = "uting";
    public static String ITEM_KEY_WORD = "keyword";

    public static String NAME_ALL = "全部";

    @Nullable
    public ItemBean item;
    public String itemType;
    public boolean selected;//不是接口返回的字段，用来标记是否被选中

    public static class ItemBean {
        /**
         * categoryId : 12
         * keywordId : 71
         * keywordName : 热词1
         */

        public int id;
        public int categoryId;
        public int keywordId;
        public String streamId;

        @SerializedName(value = "h5", alternate = {"uting"})
        public String link;

        @SerializedName(value = "name", alternate = {"keywordName"})
        public String name;

        public int poolId;//内容池id


    }


}
