package com.ximalaya.ting.lite.main.home.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentManager;
import androidx.viewpager.widget.PagerAdapter;

import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;

import java.util.List;

/**
 * 首页-tab-Adapter
 *
 * <AUTHOR>
 */
public class HomePageAdapter extends TabCommonAdapter {

    public HomePageAdapter(FragmentManager fm, List<FragmentHolder> fragList) {
        super(fm, fragList);
    }

    @Override
    public int getItemPosition(@NonNull Object object) {
        return PagerAdapter.POSITION_NONE;
    }
}
