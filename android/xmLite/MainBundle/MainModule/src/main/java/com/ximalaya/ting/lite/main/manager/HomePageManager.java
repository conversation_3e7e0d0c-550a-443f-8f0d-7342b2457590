package com.ximalaya.ting.lite.main.manager;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;

public class HomePageManager {
    public static final String KET_isFromHomeFragment = "key_isFromHomeFragment";


    public static void adapterFromHomePageUi(Bundle pageBundle, View view) {
        if (pageBundle == null || view == null) {
            return;
        }
        boolean isFromHomeFragment = pageBundle.getBoolean(KET_isFromHomeFragment, false);
        if (!isFromHomeFragment) {
            return;
        }
        //状态栏高度
        int statusBarHeight = 0;
        statusBarHeight = BaseUtil.getStatusBarHeight(BaseApplication.getMyApplicationContext());
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            statusBarHeight = BaseUtil.getStatusBarHeight(BaseApplication.getMyApplicationContext());
        }
        //main_fra_lite_home_page.xml
        //搜索框是 46dp，layout_marginBottom是2，
        //tab左右切换的是52dp，layout_marginBottom是0
        //底部线条是1px
        int topLayoutHeight = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), (46 + 2 + 52)) + 1;
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) layoutParams;
            params.topMargin = params.topMargin + statusBarHeight + topLayoutHeight;
            view.setLayoutParams(params);
        }
    }
}
