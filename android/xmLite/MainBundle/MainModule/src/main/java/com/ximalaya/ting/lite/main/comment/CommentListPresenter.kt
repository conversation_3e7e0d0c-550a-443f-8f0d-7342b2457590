package com.ximalaya.ting.lite.main.comment

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.album.Album
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.comment.entities.*
import com.ximalaya.ting.lite.main.request.LiteUrlConstants

/**
 *  @Author: Junxiang Cheng
 *  @Mail: <EMAIL>
 *  @CreateTime: 12/29/21
 *
 *  @Description: 评论&回复功能中控（x
 */
class CommentListPresenter {

    companion object {
        const val TAG = "CommentListPresenter"
        const val FETCH_MODE_HOT = "1"
        const val FETCH_MODE_NEW = "0"

        const val FRAGMENT_PAGE_TYPE_COMMENT = "COMMENT"
        const val FRAGMENT_PAGE_TYPE_REPLY = "REPLY"
    }

    var mCurTrack: Track? = null
    var mAlbum: Album? = null

    private var mCommentPageId = 0
    private var commentIsRequesting = false
    private var commentHasMore = false

    var currentFetchMode = FETCH_MODE_HOT
    var sourceId = 0L

    var commentList: MutableList<CommentListItemBean> = ArrayList()
    var commentListListener: CommentListListener? = null

    private var mReplyPageId = 0
    private var mTotalReplyCount: Long = -1
    private var replyIsRequesting = false
    private var replyHasMore = false

    var replyList: MutableList<CommentListItemBean> = ArrayList()
    var replyListListener: ReplyListListener? = null
    private var mCurrentParentCommentId: Long = -1L

    private var isAddCommentRequesting = false
    private var isDeleteCommentRequesting = false


    fun switchFetchMode(fetchMode: String) {
        currentFetchMode = fetchMode
        requestCommentList(refresh = true)
    }

    /**
     * 评论列表
     */
    fun requestCommentList(refresh: Boolean) {
        if (commentIsRequesting) {
            return
        }
        commentIsRequesting = true

        if (refresh) {
            mCommentPageId = 0
        }
        mCommentPageId++
        val params = HashMap<String, String>()
        params["fetchMode"] = currentFetchMode
        params["sourceType"] = "1" //原内容类型：1-声音；2-专辑
        params["sourceId"] = sourceId.toString()
        params["pageSize"] = "20"
        params["pageId"] = mCommentPageId.toString()
        CommentPagesRequest.getCommentList(params, object : IDataCallBack<CommentListBean> {
            override fun onSuccess(bean: CommentListBean?) {
                commentIsRequesting = false

                bean?.let {
                    commentHasMore = it.pageId < it.maxPageId

                    it.dataList?.let {
                        if (refresh) {
                            commentList.clear()
                        }
                        commentList.addAll(it)
                    }
                    commentListListener?.onSuccessUpdateCommentList(
                        commentList,
                        refresh,
                        commentHasMore
                    )
                }
            }

            override fun onError(code: Int, message: String?) {
                commentIsRequesting = false
                mCommentPageId--

                commentListListener?.onRequestFailed()
                Logger.i(TAG, "getTodayRecommend 失败 code = $code msg = $message")
            }
        })
    }

    fun requestNewCommentList(refresh: Boolean, order: String = "0") {
        requestHotCommentList(refresh, order)
    }

    /**
     * 播放页评论列表
     */
    fun requestHotCommentList(refresh: Boolean, order: String = "1") {
        if (commentIsRequesting) {
            return
        }
        commentIsRequesting = true

        if (refresh) {
            mCommentPageId = 0
        }
        mCommentPageId++
        val params = HashMap<String, String>()
        params["trackId"] = sourceId.toString()
        params["pageId"] = mCommentPageId.toString()
        params["hotPageId"] = 1.toString()
        params["order"] = order
        params["source"] = "2"
        params["pageSize"] = 30.toString()
        params["hotPageSize"] = 30.toString()
        params["showVersion"] = "1"
        CommentPagesRequest.getHotCommentList(params, object : IDataCallBack<CommentListBean> {
            override fun onSuccess(bean: CommentListBean?) {
                commentIsRequesting = false

                bean?.let {
                    commentHasMore = it.pageId < it.maxPageId
                    it.dataList?.let {
                        if (refresh) {
                            commentList.clear()
                        }
                        commentList.addAll(it)
                    }
                    commentListListener?.onSuccessUpdateCommentList(
                        commentList,
                        refresh,
                        commentHasMore
                    )
                }

            }

            override fun onError(code: Int, message: String?) {
                commentIsRequesting = false
                mCommentPageId--

                commentListListener?.onRequestFailed()
                Logger.i(TAG, "getTodayRecommend 失败 code = $code msg = $message")
            }

        })
    }

    fun requestAlbumCommentList(refresh: Boolean, order: String = "time-desc") {
        requestAlbumHotCommentList(refresh, order)
    }

    /**
     * 专辑页评论列表
     */
    fun requestAlbumHotCommentList(refresh: Boolean, order: String = "content-score-desc") {
        if (commentIsRequesting) {
            return
        }
        commentIsRequesting = true

        if (refresh) {
            mCommentPageId = 0
        }
        mCommentPageId++
        val params = HashMap<String, String>()
        params["albumId"] = sourceId.toString()
        params["pageId"] = mCommentPageId.toString()
        params["pageSize"] = "20"
        params["order"] = order
        CommentPagesRequest.getAlbumHotCommentList(params, object : IDataCallBack<CommentListBean> {
            override fun onSuccess(bean: CommentListBean?) {
                commentIsRequesting = false

                bean?.let {
                    commentHasMore = it.pageId < it.maxPageId
                    it.dataList?.let {
                        if (refresh) {
                            commentList.clear()
                        }
                        commentList.addAll(it)
                    }
                    commentListListener?.onSuccessUpdateCommentList(
                        commentList, refresh, commentHasMore
                    )
                }

            }

            override fun onError(code: Int, message: String?) {
                commentIsRequesting = false
                mCommentPageId--

                commentListListener?.onRequestFailed()
                Logger.i(TAG, "getTodayRecommend 失败 code = $code msg = $message")
            }

        })
    }

    /**
     * 评论回复列表
     */
    fun requestReplyList(parentCommentId: Long, refresh: Boolean) {
        if (replyIsRequesting) {
            return
        }
        replyIsRequesting = true

        mCurrentParentCommentId = parentCommentId

        if (refresh) {
            mReplyPageId = 0
        }
        mReplyPageId++
        val params = HashMap<String, String>()
        params["trackId"] = sourceId.toString()
        params["commentId"] = mCurrentParentCommentId.toString()
        params["pageId"] = "" + mReplyPageId.toString()
        params["pageSize"] = "15"
        params["order"] = "0"
        CommentPagesRequest.getCommentReplyList(params, object : IDataCallBack<CommentListBean> {
            override fun onSuccess(bean: CommentListBean?) {
                replyIsRequesting = false

                bean?.let {
                    mTotalReplyCount = it.totalCount ?: 0
                    replyHasMore = it.pageId < it.maxPageId
                    it.dataList?.let { list ->
                        if (refresh) {
                            replyList.clear()
                        }
                        replyList.addAll(list)
                    }
                    replyCountListener?.onReplyCountUpdated(mTotalReplyCount)
                    replyListListener?.onSuccessUpdateReplyList(replyList, refresh, replyHasMore)
                }

            }

            override fun onError(code: Int, message: String?) {
                replyIsRequesting = false
                mReplyPageId--

                replyListListener?.onRequestFailed()
                Logger.i(TAG, "getCommentReplyList 失败 code = $code msg = $message")
            }
        })
    }

    /**
     * 评论回复列表
     */
    fun requestAlbumReplyList(parentCommentId: Long, refresh: Boolean) {
        if (replyIsRequesting) {
            return
        }
        replyIsRequesting = true

        mCurrentParentCommentId = parentCommentId

        if (refresh) {
            mReplyPageId = 0
        }
        mReplyPageId++
        val params = HashMap<String, String>()
        params["albumId"] = sourceId.toString()
        params["commentId"] = mCurrentParentCommentId.toString()
        params["pageId"] = "" + mReplyPageId.toString()
        params["pageSize"] = "20"
        CommentPagesRequest.getAlbumCommentReplyList(
            params,
            object : IDataCallBack<CommentListBean> {
                override fun onSuccess(bean: CommentListBean?) {
                    replyIsRequesting = false

                    bean?.let {
                        mTotalReplyCount = it.totalCount ?: 0
                        replyHasMore = it.pageId < it.maxPageId
                        it.dataList?.let { list ->
                            if (refresh) {
                                replyList.clear()
                            }
                            replyList.addAll(list)
                        }
                        replyCountListener?.onReplyCountUpdated(mTotalReplyCount)
                        replyListListener?.onSuccessUpdateReplyList(
                            replyList,
                            refresh,
                            replyHasMore
                        )
                    }

                }

                override fun onError(code: Int, message: String?) {
                    replyIsRequesting = false
                    mReplyPageId--

                    replyListListener?.onRequestFailed()
                    Logger.i(TAG, "getCommentReplyList 失败 code = $code msg = $message")
                }
            })
    }


    /**
     * 添加声音评论
     */
    fun addComment(
        pageType: String,
        sourceId: Long,
        content: String,
        targetCommentId: Long? = -1L,
        commentRequestListener: ICommentRequestListener
    ) {
        if (isAddCommentRequesting) {
            return
        }
        isAddCommentRequesting = true

        val requestParams = HashMap<String, String>()
        val curTime: Int = 0
        requestParams["startTime"] = curTime.toString()
        requestParams["endTime"] = curTime.toString()
        requestParams["uid"] = UserInfoMannage.getUid().toString()
        requestParams["token"] = UserInfoMannage.getToken()
        requestParams["trackId"] = "" + sourceId.toString()
        requestParams["content"] = content
        requestParams["setTop"] = "false"
        requestParams["topExpireTime"] = "0"
        if (targetCommentId != -1L) {
            requestParams["parentId"] = targetCommentId.toString()
        }
        requestParams["synchaos"] = "0"
        CommentPagesRequest.postAddComment(requestParams, object : IDataCallBack<String> {
            override fun onSuccess(bean: String?) {
                isAddCommentRequesting = false

                val obj = JsonUtilKt.instance.toObject(bean, CommentModel::class.java)
                if (obj == null) {
                    commentRequestListener.onError()
                    return
                }
                dealAddComment(
                    pageType,
                    targetCommentId ?: -1L, CommentModel.commentTo2CommentListItemBean(obj)
                )

                commentRequestListener.onSuccess()
            }

            override fun onError(code: Int, message: String?) {
                isAddCommentRequesting = false
                commentRequestListener.onError(message)
            }

        })
    }

    /**
     * 添加专辑评论
     */
    fun addAlbumComment(
        pageType: String,
        targetCommentId: Long,
        requestParams: Map<String, String>,
        commentRequestListener: ICommentRequestListener
    ) {
        if (isAddCommentRequesting) {
            return
        }
        isAddCommentRequesting = true
        CommentPagesRequest.postReplyAlbumComment(requestParams, object : IDataCallBack<String> {
            override fun onSuccess(bean: String?) {
                isAddCommentRequesting = false

                val obj = JsonUtilKt.instance.toObject(bean, AlbumCommentModel::class.java)
                if (obj == null) {
                    commentRequestListener.onError()
                    return
                }
                dealAddComment(
                    pageType,
                    targetCommentId ?: -1L,
                    obj.commentTo2CommentListItemBean()
                )

                commentRequestListener.onSuccess()
            }

            override fun onError(code: Int, message: String?) {
                isAddCommentRequesting = false
                commentRequestListener.onError(message)
            }

        })
    }

    private fun dealAddComment(
        pageType: String,
        targetCommentId: Long,
        data: CommentListItemBean
    ) {
        when (pageType) {
            FRAGMENT_PAGE_TYPE_COMMENT -> {
                //在主楼回复
                data.parentCommentId = null
                data.parentUser = null
                if (targetCommentId == -1L) {
                    //在主楼直接回复
                    commentList.add(0, data)
                } else {
                    //在主楼点击item热区回复
                    for (item in commentList) {
                        if (item.commentId == targetCommentId) {
                            item.replyCount++
                            if (item.replys == null) {
                                item.replys = arrayListOf(data)
                            } else {
                                item.replys?.add(0, data)
                            }
                        }
                    }
                }
            }
            FRAGMENT_PAGE_TYPE_REPLY -> {
                //在回复楼中回复
                if (mCurrentParentCommentId == -1L || targetCommentId == -1L) {
                    return
                }
                //点击回复item热区回复
                replyList.add(0, data)
                mTotalReplyCount++
                replyCountListener?.onReplyCountUpdated(mTotalReplyCount)

                for (item in commentList) {
                    if (item.commentId == mCurrentParentCommentId) {
                        item.replyCount++
                        if (item.replys == null) {
                            item.replys = arrayListOf(data)
                        } else {
                            item.replys?.add(0, data)
                        }
                    }
                }
            }
        }
        commentCountListener?.onCommentCountUpdated(1, isAdd = true)
        commentListListener?.onUpdateList()
    }


    /**
     * 删除声音评论
     */
    fun deleteComment(
        pageType: String,
        data: CommentListItemBean,
        commentRequestListener: ICommentRequestListener
    ) {
        if (isDeleteCommentRequesting) {
            return
        }
        isDeleteCommentRequesting = true

        val params = HashMap<String, String>()
        params["trackId"] = sourceId.toString()
        params["commentId"] = data.commentId.toString()
        params["device"] = "android"
        CommentPagesRequest.postDeleteComment(params, object : IDataCallBack<String> {
            override fun onSuccess(content: String?) {
                isDeleteCommentRequesting = false

                dealDeleteComment(pageType, data)
                commentRequestListener.onSuccess()
            }

            override fun onError(code: Int, message: String?) {
                isDeleteCommentRequesting = false
                commentRequestListener.onError(message)
            }
        })
    }

    /**
     * 删除专辑评论
     */
    fun deleteAlbumComment(
        pageType: String,
        data: CommentListItemBean,
        commentRequestListener: ICommentRequestListener
    ) {
        if (isDeleteCommentRequesting) {
            return
        }
        isDeleteCommentRequesting = true
        val params = HashMap<String, String>()
        params["albumId"] = sourceId.toString()
        params["replyId"] = data.replyId.toString()
        CommentPagesRequest.postAlbumDeleteComment(params, object : IDataCallBack<String> {
            override fun onSuccess(content: String?) {
                isDeleteCommentRequesting = false

                dealDeleteComment(pageType, data)
                commentRequestListener.onSuccess()
            }

            override fun onError(code: Int, message: String?) {
                isDeleteCommentRequesting = false
                commentRequestListener.onError(message)
            }
        })
    }

    fun dealDeleteComment(pageType: String, data: CommentListItemBean) {
        when (pageType) {
            FRAGMENT_PAGE_TYPE_COMMENT -> {
                //在主楼删除
                for (item in commentList) {
                    if (item.commentId == data.commentId) {
                        commentList.remove(item)
                        break
                    }
                }
            }
            FRAGMENT_PAGE_TYPE_REPLY -> {
                //删除回复
                for (item in replyList) {
                    if (item.commentId == data.commentId) {
                        replyList.remove(item)
                        break
                    }
                }
                mTotalReplyCount--
                replyCountListener?.onReplyCountUpdated(mTotalReplyCount)
                for (itemP in commentList) {
                    if (itemP.commentId == mCurrentParentCommentId) {
                        if (CollectionUtil.isNullOrEmpty(replyList)) {
                            itemP.replyCount = 0
                            itemP.replys = null
                        } else {
                            val t = replyList[0]
                            itemP.replyCount--
                            itemP.replys = arrayListOf(t)
                        }
                    }
                }
            }
        }
        commentCountListener?.onCommentCountUpdated(++data.replyCount, isAdd = false)
        commentListListener?.onUpdateList()
    }

    /**
     * 在回复页删除父评论
     */
    fun deleteParentCommentInReplyPage(
        data: CommentListItemBean,
        commentRequestListener: ICommentRequestListener
    ) {
        if (isDeleteCommentRequesting) {
            return
        }
        isDeleteCommentRequesting = true

        val params = HashMap<String, String>()
        params["trackId"] = sourceId.toString()
        params["commentId"] = data.commentId.toString()
        params["device"] = "android"
        CommentPagesRequest.postDeleteComment(params, object : IDataCallBack<String> {
            override fun onSuccess(content: String?) {
                isDeleteCommentRequesting = false
                commentList.remove(data)
                commentListListener?.onUpdateList(force = false)
                commentRequestListener.onSuccess()
            }

            override fun onError(code: Int, message: String?) {
                isDeleteCommentRequesting = false
                commentRequestListener.onError(message)
            }
        }
        )
    }

    private var isGetCountRequesting = false
    private var commentCountListener: ICommentCountListener? = null
    private var replyCountListener: IReplyCountListener? = null

    fun setCommentCountListener(l: ICommentCountListener) {
        commentCountListener = l
    }

    fun setReplyCountListener(l: IReplyCountListener) {
        replyCountListener = l
    }

    /**
     * 评论喜欢或不喜欢
     *
     * @param commentId
     * @param isLike
     * @param callback
     */
    fun commentLikeOrUnLike(
        commentId: Long,
        isLike: Boolean,
        callback: IDataCallBack<Boolean>
    ) {
        val params = HashMap<String, String>()
        params["uid"] = UserInfoMannage.getUid().toString()
        params["trackId"] = sourceId.toString() + ""
        params["commentId"] = commentId.toString() + ""
        params["device"] = "android"
        params["islike"] = isLike.toString()
        CommentPagesRequest.commentLikeOrUnLike(params, object : IDataCallBack<Boolean> {
            override fun onSuccess(isSuccess: Boolean?) {
                isDeleteCommentRequesting = false
                if (isSuccess == true) {
                    callback.onSuccess(isSuccess)
                } else {
                    callback.onError(400, "")
                }
            }

            override fun onError(code: Int, message: String?) {
                isGetCountRequesting = false
            }
        })
    }

    /**
     * 专辑评论喜欢
     *
     * @param commentUid
     * @param commentId
     * @param callback
     */
    fun requestLikeAlbumComment(
        commentUid: Long,
        commentId: Long,
        callback: IDataCallBack<Boolean>
    ) {
        val params = HashMap<String, String>()
        params["commentUid"] = commentUid.toString()
        params["albumId"] = sourceId.toString() + ""
        params["commentId"] = commentId.toString() + ""
        CommentPagesRequest.albumLikeComment(params, object : IDataCallBack<Boolean> {
            override fun onSuccess(isSuccess: Boolean?) {
                isDeleteCommentRequesting = false
                if (isSuccess == true) {
                    callback.onSuccess(isSuccess)
                } else {
                    callback.onError(400, "")
                }
            }

            override fun onError(code: Int, message: String?) {
                isGetCountRequesting = false
            }
        })
    }

    /**
     * 专辑评论不喜欢
     *
     * @param commentUid
     * @param commentId
     * @param callback
     */
    fun requestDisLikeAlbumComment(
        commentUid: Long,
        commentId: Long,
        callback: IDataCallBack<Boolean>
    ) {
        val params = HashMap<String, String>()
        params["commentUid"] = commentUid.toString()
        params["albumId"] = sourceId.toString() + ""
        params["commentId"] = commentId.toString() + ""
        CommentPagesRequest.albumDisLikeComment(params, object : IDataCallBack<Boolean> {
            override fun onSuccess(isSuccess: Boolean?) {
                isDeleteCommentRequesting = false
                if (isSuccess == true) {
                    callback.onSuccess(isSuccess)
                } else {
                    callback.onError(400, "")
                }
            }

            override fun onError(code: Int, message: String?) {
                isGetCountRequesting = false
            }
        })
    }

    /**
     * 专辑回复评论喜欢
     * @param commentReplyUid
     * @param commentReplyId
     * @param callback
     */
    fun requestLikeAlbumReplyComment(
        commentReplyUid: Long,
        commentReplyId: Long,
        callback: IDataCallBack<Boolean>
    ) {
        val params = HashMap<String, String>()
        params["commentReplyUid"] = commentReplyUid.toString()
        params["albumId"] = sourceId.toString()
        params["commentReplyId"] = commentReplyId.toString()
        CommentPagesRequest.albumReplyLikeComment(params, object : IDataCallBack<Boolean> {
            override fun onSuccess(isSuccess: Boolean?) {
                isDeleteCommentRequesting = false
                if (isSuccess == true) {
                    callback.onSuccess(isSuccess)
                } else {
                    callback.onError(400, "")
                }
            }

            override fun onError(code: Int, message: String?) {
                isGetCountRequesting = false
            }
        })
    }

    /**
     * 专辑回复评论不喜欢
     *
     * @param commentReplyUid
     * @param commentReplyId
     * @param callback
     */
    fun requestDisLikeAlbumReplyComment(
        commentReplyUid: Long,
        commentReplyId: Long,
        callback: IDataCallBack<Boolean>
    ) {
        val params = HashMap<String, String>()
        params["commentReplyUid"] = commentReplyUid.toString()
        params["albumId"] = sourceId.toString()
        params["commentReplyId"] = commentReplyId.toString()
        CommentPagesRequest.albumReplyDisLikeComment(params, object : IDataCallBack<Boolean> {
            override fun onSuccess(isSuccess: Boolean?) {
                isDeleteCommentRequesting = false
                if (isSuccess == true) {
                    callback.onSuccess(isSuccess)
                } else {
                    callback.onError(400, "")
                }
            }

            override fun onError(code: Int, message: String?) {
                isGetCountRequesting = false
            }
        })
    }
}

interface CommentListListener {
    fun onSuccessUpdateCommentList(
        data: List<CommentListItemBean>?,
        refresh: Boolean,
        hasMore: Boolean
    )

    fun onUpdateList(force: Boolean = true)
    fun onRequestFailed()
}

interface ReplyListListener {
    fun onSuccessUpdateReplyList(
        data: List<CommentListItemBean>?,
        refresh: Boolean,
        hasMore: Boolean
    )

    fun onRequestFailed()
}

interface ICommentRequestListener {
    fun onSuccess()
    fun onError(message: String? = null)
}

interface ICommentCountListener {
    fun onCommentCountUpdated(commentCount: Long, isAdd: Boolean)
}

interface IReplyCountListener {
    fun onReplyCountUpdated(replyCount: Long)
}