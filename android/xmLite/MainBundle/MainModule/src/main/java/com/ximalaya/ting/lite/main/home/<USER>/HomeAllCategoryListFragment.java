package com.ximalaya.ting.lite.main.home.fragment;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.Nullable;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.MulitViewTypeAdapter;
import com.ximalaya.ting.lite.main.home.adapter.HomeAllCategoryListProvider;
import com.ximalaya.ting.lite.main.model.album.HomeAllCategoryModel;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by qinhuifeng on 2019-07-16
 * <p>
 * 首页糖葫芦点击全部--分类列表跳转进来
 *
 * <AUTHOR>
 */
public class HomeAllCategoryListFragment extends BaseFragment2 implements View.OnClickListener {

    private final static String KEY_FROM = "key_from";

    //来源，首页，服务端约定，禁止修改
    public final static String SOURCE_HOME = "-1";
    public final static String SOURCE_TRUCK_MODE_HOME = "2";
    //来源，听书，服务端约定，禁止修改
    public final static String SOURCE_TING_BOOK = "-3";

    private static int VIEW_TYPE_DEF = 0;
    private RefreshLoadMoreListView mListView;
    private MulitViewTypeAdapter mAdapter;
    //来源
    private String mFromSource = "-2";

    public HomeAllCategoryListFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_LINEARLAYOUT, null);
    }

    @Override
    protected String getPageLogicName() {
        return "HomeAllCategoryListFragment";
    }

    public static HomeAllCategoryListFragment newInstance(String sourceFrom) {
        HomeAllCategoryListFragment fragment = new HomeAllCategoryListFragment();
        Bundle bundle = new Bundle();
        bundle.putString(KEY_FROM, sourceFrom);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Bundle arguments = getArguments();
        if (arguments != null) {
            mFromSource = arguments.getString(KEY_FROM, SOURCE_HOME);
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("全部分类");

        mListView = findViewById(R.id.main_listview);
        Map<Integer, IMulitViewTypeViewAndData> itemTypeMap = new HashMap<Integer, IMulitViewTypeViewAndData>() {
            {
                put(VIEW_TYPE_DEF, new HomeAllCategoryListProvider(HomeAllCategoryListFragment.this));
            }
        };
        mListView.setMode(PullToRefreshBase.Mode.DISABLED);
        mAdapter = new MulitViewTypeAdapter(getActivity(), itemTypeMap);
        LayoutInflater inflater = getLayoutInflaterInner();
        mAdapter.setLayoutInflater(inflater);
        mListView.setAdapter(mAdapter);
    }

    @Override
    protected void loadData() {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        HashMap<String, String> params = new HashMap<>();
        //-2:听节目页,首页，-3:听书页
        if (TextUtils.isEmpty(mFromSource)) {
            mFromSource = SOURCE_HOME;
        }
        params.put("source", mFromSource);
        LiteCommonRequest.getHomeAllCategory(params, new IDataCallBack<List<HomeAllCategoryModel>>() {
            @Override
            public void onSuccess(@Nullable List<HomeAllCategoryModel> object) {
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        if (object == null || object.size() == 0) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }
                        mAdapter.clear();
                        mAdapter.addAll(object, VIEW_TYPE_DEF);
                        mAdapter.notifyDataSetChanged();
                        onPageLoadingCompleted(LoadCompleteType.OK);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
            }
        });
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_all_category_list;
    }

    @Override
    public void onClick(View v) {

    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    public LayoutInflater getLayoutInflaterInner() {
        Activity activity = getActivity();
        if (activity != null) {
            return activity.getLayoutInflater();
        } else {
            return super.getLayoutInflater();
        }
    }
}
