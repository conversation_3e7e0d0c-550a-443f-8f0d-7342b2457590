package com.ximalaya.ting.lite.main.album.fragment;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;


/**
 * Created by anthony
 * <p>
 * Desc: LiteAlbumLookEbookFragment
 */
public class LiteAlbumLookEbookFragment extends NativeHybridFragment implements IMainFunctionAction.IWebTabFragment {

    private static final String TAG = "LiteAlbumLookEbookFragment";
    private static final String EBOOK_DATA_KEY = "ebook_data_key";

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        super.initUi(savedInstanceState);
        statusBarSpace.setVisibility(View.GONE);
        if (defaultFadeTitleView != null) {
            defaultFadeTitleView.setVisibility(View.GONE);
        } else if (titleDefaultTitleView != null) {
            titleDefaultTitleView.setVisibility(View.GONE);
        }
    }

    @Override
    public View getInnerScrollView() {
        return getHybridView();
    }


}
