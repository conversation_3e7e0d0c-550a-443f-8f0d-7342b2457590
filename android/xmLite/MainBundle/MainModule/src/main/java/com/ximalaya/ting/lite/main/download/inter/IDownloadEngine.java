package com.ximalaya.ting.lite.main.download.inter;


import com.ximalaya.ting.lite.main.download.bean.TaskInfo;

/**
 * <AUTHOR> feiwen
 * date   : 2019/4/8
 * desc   :
 */
public interface IDownloadEngine {
    void setData(TaskInfo taskInfo, IEngineObserver observer);

    void start() throws Exception;

    void stop();

    void delete(boolean deletetemp);

    boolean busy();

    void reset();
}
