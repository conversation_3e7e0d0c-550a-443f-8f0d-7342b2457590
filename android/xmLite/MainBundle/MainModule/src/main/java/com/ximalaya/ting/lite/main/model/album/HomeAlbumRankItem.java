package com.ximalaya.ting.lite.main.model.album;

import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.opensdk.model.album.Album;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qinhuifeng on 2021/1/20
 *
 * <AUTHOR>
 */
public class HomeAlbumRankItem {
    public int rankingListId;
    public String rankingListName;
    public boolean isDefault;

    public List<AlbumM> albumList;

    public int moduleId;

    /**
     * 因为需要解析AlbumM，需要手动进行解析
     */
    public static HomeAlbumRankItem parseHomeAlbumRankItem(JSONObject object) {
        if (object == null) {
            return null;
        }
        try {
            return parseHomeAlbumRank(object);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static HomeAlbumRankItem parseHomeAlbumRank(JSONObject object) {
        if (object == null) {
            return null;
        }
        HomeAlbumRankItem item = new HomeAlbumRankItem();
        item.rankingListId = object.optInt("rankingListId", 0);
        item.rankingListName = object.optString("rankingListName", "");
        item.isDefault = object.optBoolean("isDefault", false);
        try {
            JSONArray list = object.optJSONArray("list");
            if (list != null) {
                for (int i = 0; i < list.length(); i++) {
                    AlbumM albumM = new AlbumM(list.getString(i));
                    if (item.albumList == null) {
                        item.albumList = new ArrayList<>();
                    }
                    item.albumList.add(albumM);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return item;
    }


    /**
     * 因为需要解析AlbumM，需要手动进行解析
     */
    public static HomeAlbumRankItem parseHomeAlbumRankItem(JSONObject object, int moduleId) {
        if (object == null) {
            return null;
        }
        try {
            return parseHomeAlbumRank(object, moduleId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static HomeAlbumRankItem parseHomeAlbumRank(JSONObject object, int moduleId) {
        if (object == null) {
            return null;
        }
        HomeAlbumRankItem item = new HomeAlbumRankItem();
        item.moduleId = moduleId;
        item.rankingListId = object.optInt("rankingListId", 0);
        item.rankingListName = object.optString("rankingListName", "");
        item.isDefault = object.optBoolean("isDefault", false);
        try {
            JSONArray list = object.optJSONArray("list");
            if (list != null) {
                for (int i = 0; i < list.length(); i++) {
                    AlbumM albumM = new AlbumM(list.getString(i));
                    albumM.moduleId = moduleId;
                    if (item.albumList == null) {
                        item.albumList = new ArrayList<>();
                    }
                    item.albumList.add(albumM);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return item;
    }
}
