package com.ximalaya.ting.lite.main.newhome.adapter;

import android.os.Bundle;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import androidx.collection.ArrayMap;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.business.unlock.manager.AlbumUnlockABManager;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.GlobalGrayManager;
import com.ximalaya.ting.android.host.manager.configurecenter.ConfigureNeedCacheLocalManager;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommedExtraDataProvider;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendExcitationAdapterProvider;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendExcitationStyle3AdapterProvider;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendExcitationStyle4AdapterProvider;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendFeedTrackStyleV2AdapterProvider;
import com.ximalaya.ting.lite.main.home.adapter.ListViewNoContentProvider;
import com.ximalaya.ting.lite.main.home.adapter.LiteChildTabProvider;
import com.ximalaya.ting.lite.main.home.adapter.LiteCloudTagProvider;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel;
import com.ximalaya.ting.lite.main.manager.HomeFeedAdABManager;
import com.ximalaya.ting.lite.main.model.newhome.LiteFloorModel;
import com.ximalaya.ting.lite.main.model.newhome.LiteTabModel;
import com.ximalaya.ting.lite.main.newhome.fragment.LiteHomeRecommendFragment;
import com.ximalaya.ting.lite.main.newhome.presenter.LiteHomeRecommendContact;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发现-分类 具体分类下的推荐数据适配器
 *
 * <AUTHOR>
 */
public class LiteHomeRecommendAdapter extends BaseAdapter implements View.OnClickListener, HomeRecommedExtraDataProvider {

    private static final String TAG = "LiteHomeRecommendAdapte";

    public static final String KEY_PREVIOUS_FLOOR_TYPE = "key_previous_floor_type";
    public static final String KEY_PREVIOUS_FLOOR_TYPE_STRING = "key_previous_floor_type_string";

    /*****type 添加 start--注意：禁止在中间添加，只能在最后的type后面自增，可视化埋点在使用这个type作为唯一路径*****************************************************/
    private static int VIEW_TYPE_BASE = 0;
    private static final int VIEW_TYPE_NONE = VIEW_TYPE_BASE;
    public static final int VIEW_TYPE_TITLE_NORMAL = VIEW_TYPE_BASE++;//title(titleName  更多)
    public static final int VIEW_TYPE_ALBUM_VERTICAL = VIEW_TYPE_BASE++;//垂直排列专辑item   多行内容池
    public static final int VIEW_TYPE_ALBUM_HORIZONTAL = VIEW_TYPE_BASE++;//水平排列专辑item  四列内容池

    public final static int VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION = VIEW_TYPE_BASE++; // 信息流激励视频广告
    public final static int VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3 = VIEW_TYPE_BASE++; // 信息流激励视频广告样式3
    public final static int VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4 = VIEW_TYPE_BASE++; // 信息流激励视频广告样式3
    public final static int VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_ALBUM = VIEW_TYPE_BASE++;//横向滑动单行专辑模块  单行内容池
    public final static int VIEW_TYPE_NET_OPERATE = VIEW_TYPE_BASE++;//网页运营  目前是购买会员  对应的 新首页-图片运营位

    public static final int VIEW_TYPE_FEED_TRACK_STYLE_V2 = VIEW_TYPE_BASE++; // 信息流中的声音条styleV2版本
    public static final int VIEW_TYPE_FEED_ALBUM_STYLE_V4 = VIEW_TYPE_BASE++; // 信息流中的专辑条,styleV4版本，带评分

    public static final int VIEW_TYPE_TANGHULU_HOME_V2 = VIEW_TYPE_BASE++;//首页分类糖葫芦V2版本，不可滑动

    public static final int VIEW_TYPE_TANGHULU_HOME_V2_CAN_SCROLL = VIEW_TYPE_BASE++;//首页分类糖葫芦V2版本，可滑动

    //因为嵌套viewpager的样式只允许缓存一个，viewpager只允许初始化一次，后续都不允许再次做其变更成操作
    //此处添加5个相同的type，支持同一个页面添加5个排行榜楼层，支持一个页面多个type
    //如果需要判断排行榜楼层，需要这几个类型全部都判断
    public static final int VIEW_TYPE_ALBUM_RANK_1 = VIEW_TYPE_BASE++; // 专辑排行榜
    public static final int VIEW_TYPE_ALBUM_RANK_2 = VIEW_TYPE_BASE++; // 专辑排行榜
    public static final int VIEW_TYPE_ALBUM_RANK_3 = VIEW_TYPE_BASE++; // 专辑排行榜
    public static final int VIEW_TYPE_ALBUM_RANK_4 = VIEW_TYPE_BASE++; // 专辑排行榜
    public static final int VIEW_TYPE_ALBUM_RANK_5 = VIEW_TYPE_BASE++; // 专辑排行榜

    public static final int VIEW_TYPE_FILTER_PANEL = VIEW_TYPE_BASE++; //筛选面板
    public static final int VIEW_TYPE_FILTER_STREAM_ALBUM = VIEW_TYPE_BASE++; //筛选流专辑

    public static final int VIEW_TYPE_BANNER_ONE_KEY_TING = VIEW_TYPE_BASE++; //一键听banner  电台卡片

    /**
     * 这个并不是正常的数据类型
     */
    public final static int VIEW_TYPE_LIST_VIEW_NO_CONTENT = VIEW_TYPE_BASE++;

    public final static int VIEW_TYPE_FOCUS_IMAGE = VIEW_TYPE_BASE++;

    public static final int VIEW_TYPE_MY_SUBSCRIPTION = VIEW_TYPE_BASE++;//我的订阅

    public final static int VIEW_TYPE_CLOUD_TAG = VIEW_TYPE_BASE++;//云标签 分类页标签

    public final static int VIEW_TYPE_CHILD_TAB = VIEW_TYPE_BASE++;//子tab

    public final static int VIEW_TYPE_SKITS = VIEW_TYPE_BASE++;//短剧单元
    public final static int VIEW_TYPE_SKITS_NEW = VIEW_TYPE_BASE++;//短剧新模块

    public final static int VIEW_TYPE_AD_BIG_PICTURE_OR_VIDEO = VIEW_TYPE_BASE++;//首页信息流大图或者视频

    public final static int VIEW_TYPE_SMOOTH_FREE_ALBUM_HORIZONTAL = VIEW_TYPE_BASE++;//免费畅听会员专辑|畅听ViP专辑

    public final static int VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL = VIEW_TYPE_BASE++;//免费畅听会员声音

    public final static int VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL_LIST = VIEW_TYPE_BASE++;//免费畅听会员声音组合

    public final static int VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL_LIST2 = VIEW_TYPE_BASE++;//免费畅听会员声音组合2

    public static final int VIEW_TYPE_NOVEL_HORIZONTAL = VIEW_TYPE_BASE++;//首页小说模块横向内容池

    public final static int VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_NOVEL = VIEW_TYPE_BASE++;//首页小说模块横向滑动的内容池


    //NOTE:注意可视化埋点在使用上面的type，上面的顺序禁止修改，只能在最后增加
    //所有类型必须放在VIEW_TYPE_MAX_COUNT之前，VIEW_TYPE_MAX_COUNT是返回listview的type的个数的，前面有个VIEW_TYPE_NONE
    //只使用map的size返回，不准确，如果类型不添加到map里会造成返回的type个数有问题
    public static final int VIEW_TYPE_MAX_COUNT = VIEW_TYPE_BASE++; //必须放在最后，禁止修改
    /***type end--注意：禁止在中间添加，只能在最后的type后面自增，可视化埋点在使用这个type作为唯一路径*****************************************************/

    private Map<Integer, IMulitViewTypeViewAndData> adapterMap;
    // 广告适配器
    private final Map<Integer, IMulitViewTypeViewAndData> adProviderMap = new HashMap<>();

    private LiteHomeRecommendContact.IFragmentView iFragmentView;
    private final BaseFragment2 mFragment;
    private final MainActivity mActivity;
    private LayoutInflater inflater;
    private List<ItemModel> listData;
    private final HashMap<String, Boolean> globalModelMap = new HashMap<>();
    private IRecommendFeedItemActionListener recommendFeedItemActionListener;

    //保护的额外信息，所有的子卡片使用同一个对象，如果需要更新可以使用updateSelf进行更新
    private final HomeRecommendExtraViewModel mExtraViewModel = new HomeRecommendExtraViewModel();

    public static final int DEFAULT_FLOOR_TYPE = -20210721;
    private static final String DEFAULT_FLOOR_TYPE_STRING = "DEFAULT_FLOOR_TYPE：没有类型";
    private int previousFloorType = DEFAULT_FLOOR_TYPE;
    private SparseArray<String> typeStringArray;
    // todo 因为播放状态变化会导致adapter刷新, 然后不需要刷新的module可以用这个标记拦截下
    public static boolean sIsReportData = true;

    public LiteHomeRecommendAdapter(LiteHomeRecommendContact.IFragmentView fragmentView, HomeRecommendExtraViewModel viewModel) {
        this.iFragmentView = fragmentView;
        mFragment = this.iFragmentView.getBaseFragment2();
        mActivity = (MainActivity) this.iFragmentView.getActivity();
        inflater = LayoutInflater.from(mActivity);
        listData = new ArrayList<>(0);
        if (viewModel != null) {
            mExtraViewModel.updateSelf(viewModel);
        }
        globalModelMap.clear();
        createAdapterMap();
        createTypeStringArray();
    }

    public void setRecommendFeedItemActionListener(IRecommendFeedItemActionListener recommendFeedItemActionListener) {
        this.recommendFeedItemActionListener = recommendFeedItemActionListener;
    }

    /**
     * 注意：添加了一个楼层以后，需要在{@link #createTypeStringArray()}方法中，添加对应的楼层字符串信息！！！
     * 注意：添加了一个楼层以后，需要在{@link #createTypeStringArray()}方法中，添加对应的楼层字符串信息！！！
     * 注意：添加了一个楼层以后，需要在{@link #createTypeStringArray()}方法中，添加对应的楼层字符串信息！！！
     */
    private void createAdapterMap() {
        adapterMap = new ArrayMap<>();
        adapterMap.put(VIEW_TYPE_TITLE_NORMAL, new LiteHomeRecommendNormalTitleProvider(this, iFragmentView));
        adapterMap.put(VIEW_TYPE_ALBUM_VERTICAL, new LiteHomeVerticalAlbumAdapterProvider(mFragment, this, null)); //未分离出去
        adapterMap.put(VIEW_TYPE_ALBUM_HORIZONTAL, new LiteHomeRecommendAlbumHorizontalProvider(mFragment, this, recommendFeedItemActionListener));
        adapterMap.put(VIEW_TYPE_FEED_TRACK_STYLE_V2, new HomeRecommendFeedTrackStyleV2AdapterProvider(mFragment, this, recommendFeedItemActionListener));
        adapterMap.put(VIEW_TYPE_FEED_ALBUM_STYLE_V4, new LiteHomeRecommendFeedAlbumStyleV4AdapterProvider(mFragment, this, recommendFeedItemActionListener));

        adapterMap.put(VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION, new HomeRecommendExcitationAdapterProvider(mFragment, this));
        adapterMap.put(VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3, new HomeRecommendExcitationStyle3AdapterProvider(mFragment, this));
        adapterMap.put(VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4, new HomeRecommendExcitationStyle4AdapterProvider(mFragment, this));

        adapterMap.put(VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_ALBUM, new LiteHorizontalScrollOneLineAlbumProvider(mFragment, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_NET_OPERATE, new LiteNetOperateProvider(mFragment, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_TANGHULU_HOME_V2, new LiteHorizontalResPositionProvider(mFragment, mExtraViewModel));
        adapterMap.put(VIEW_TYPE_TANGHULU_HOME_V2_CAN_SCROLL, new LiteHorizontalResPositionCanScrollProvider(mFragment, mExtraViewModel));

        //添加3份一样的楼层,每个楼层必须传入唯一的id，来重置viewpager的唯一id
        if (AlbumUnlockABManager.INSTANCE.getHomeLeaderBoardConfig() == 2) {
            adapterMap.put(VIEW_TYPE_ALBUM_RANK_1, new LiteHomeAlbumRankProviderNew(mFragment, this, VIEW_TYPE_ALBUM_RANK_1));
            adapterMap.put(VIEW_TYPE_ALBUM_RANK_2, new LiteHomeAlbumRankProviderNew(mFragment, this, VIEW_TYPE_ALBUM_RANK_2));
            adapterMap.put(VIEW_TYPE_ALBUM_RANK_3, new LiteHomeAlbumRankProviderNew(mFragment, this, VIEW_TYPE_ALBUM_RANK_3));
            adapterMap.put(VIEW_TYPE_ALBUM_RANK_4, new LiteHomeAlbumRankProviderNew(mFragment, this, VIEW_TYPE_ALBUM_RANK_4));
            adapterMap.put(VIEW_TYPE_ALBUM_RANK_5, new LiteHomeAlbumRankProviderNew(mFragment, this, VIEW_TYPE_ALBUM_RANK_5));
        } else {
            adapterMap.put(VIEW_TYPE_ALBUM_RANK_1, new LiteHomeAlbumRankProvider(mFragment, this, VIEW_TYPE_ALBUM_RANK_1));
            adapterMap.put(VIEW_TYPE_ALBUM_RANK_2, new LiteHomeAlbumRankProvider(mFragment, this, VIEW_TYPE_ALBUM_RANK_2));
            adapterMap.put(VIEW_TYPE_ALBUM_RANK_3, new LiteHomeAlbumRankProvider(mFragment, this, VIEW_TYPE_ALBUM_RANK_3));
            adapterMap.put(VIEW_TYPE_ALBUM_RANK_4, new LiteHomeAlbumRankProvider(mFragment, this, VIEW_TYPE_ALBUM_RANK_4));
            adapterMap.put(VIEW_TYPE_ALBUM_RANK_5, new LiteHomeAlbumRankProvider(mFragment, this, VIEW_TYPE_ALBUM_RANK_5));
        }

        adapterMap.put(VIEW_TYPE_FILTER_PANEL, new LiteSelectPanelProvider(mFragment, this, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_FILTER_STREAM_ALBUM, new LiteHomeRecommendFilterAlbumProvider(mFragment, this));
        adapterMap.put(VIEW_TYPE_BANNER_ONE_KEY_TING, new LiteOnekeyTingBannerProvider(mFragment, this));

        adapterMap.put(VIEW_TYPE_LIST_VIEW_NO_CONTENT, new ListViewNoContentProvider(mFragment, mExtraViewModel.refreshListener));

        adapterMap.put(VIEW_TYPE_FOCUS_IMAGE, new LiteFocusImageProvider(mFragment, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_MY_SUBSCRIPTION, new LiteHomeSubscriptionProvider(mFragment, iFragmentView));

        adapterMap.put(VIEW_TYPE_CLOUD_TAG, new LiteCloudTagProvider(mFragment, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_CHILD_TAB, new LiteChildTabProvider(mFragment, mExtraViewModel.childTabChangeListener));

//        adapterMap.put(VIEW_TYPE_SKITS, new LiteHomeSkitsProvider(mFragment));
//        adapterMap.put(VIEW_TYPE_SKITS_NEW, new LiteHomeSkitsProviderNew(mFragment));


        LiteHomeAdPictureOrVideoProvider liteHomeAdPictureOrVideoProvider = new LiteHomeAdPictureOrVideoProvider(mFragment, mExtraViewModel);
        liteHomeAdPictureOrVideoProvider.setMAdapter(this);
        adapterMap.put(VIEW_TYPE_AD_BIG_PICTURE_OR_VIDEO, liteHomeAdPictureOrVideoProvider);

        adapterMap.put(VIEW_TYPE_SMOOTH_FREE_ALBUM_HORIZONTAL, new LiteHomeFreeRecommendAlbumHorizontalProviderNew(mFragment, recommendFeedItemActionListener));

        adapterMap.put(VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL, new LiteHomeFreeRecommendTrackHorizontalProvider(mFragment, recommendFeedItemActionListener));

        adapterMap.put(VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL_LIST, new LiteHomeTrackRankCardProvider(mFragment, this));

        adapterMap.put(VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL_LIST2, new LiteHomeTrackRankTabProvider(mFragment, this));

        adapterMap.put(VIEW_TYPE_NOVEL_HORIZONTAL, new LiteNovelContentPoolProvider(mFragment));

        adapterMap.put(VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_NOVEL, new LiteNovelScrollContentPoolProvider(mFragment, mExtraViewModel));


    }


    private void createTypeStringArray() {
        //和类型大小保持一致
        typeStringArray = new SparseArray<>(adapterMap.size());
        typeStringArray.put(VIEW_TYPE_TITLE_NORMAL, "VIEW_TYPE_TITLE_NORMAL：正常的title类型 = " + VIEW_TYPE_TITLE_NORMAL);
        typeStringArray.put(VIEW_TYPE_ALBUM_VERTICAL, "VIEW_TYPE_ALBUM_VERTICAL：竖排专辑样式 = " + VIEW_TYPE_ALBUM_VERTICAL); //未分离出去
        typeStringArray.put(VIEW_TYPE_ALBUM_HORIZONTAL, "VIEW_TYPE_ALBUM_HORIZONTAL：横排专辑样式 = " + VIEW_TYPE_ALBUM_HORIZONTAL);
        typeStringArray.put(VIEW_TYPE_FEED_TRACK_STYLE_V2, "VIEW_TYPE_FEED_TRACK_STYLE_V2：信息流声音类型2 = " + VIEW_TYPE_FEED_TRACK_STYLE_V2);
        typeStringArray.put(VIEW_TYPE_FEED_ALBUM_STYLE_V4, "VIEW_TYPE_FEED_TRACK_STYLE_V2：信息流声音类型4 = " + VIEW_TYPE_FEED_ALBUM_STYLE_V4);

        typeStringArray.put(VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION, "VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION：信息流中插入的红包样式1 = " + VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION);
        typeStringArray.put(VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3, "VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3：信息流中插入的红包样式3 = " + VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3);
        typeStringArray.put(VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4, "VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4：信息流中插入的红包样式4 = " + VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4);

        typeStringArray.put(VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_ALBUM, "VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_ALBUM：横向一行滑动的专辑类型 = " + VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_ALBUM);

        typeStringArray.put(VIEW_TYPE_NET_OPERATE, "VIEW_TYPE_NET_OPERATE：专辑运营位 = " + VIEW_TYPE_NET_OPERATE);

        typeStringArray.put(VIEW_TYPE_TANGHULU_HOME_V2, "VIEW_TYPE_TANGHULU_HOME_V2：糖葫芦样式V2 = " + VIEW_TYPE_TANGHULU_HOME_V2);
        typeStringArray.put(VIEW_TYPE_TANGHULU_HOME_V2_CAN_SCROLL, "VIEW_TYPE_TANGHULU_HOME_V2_CAN_SCROLL：糖葫芦样式V2，横向滑动 = " + VIEW_TYPE_TANGHULU_HOME_V2_CAN_SCROLL);

        //添加3份一样的楼层,每个楼层必须传入唯一的id，来重置viewpager的唯一id
        typeStringArray.put(VIEW_TYPE_ALBUM_RANK_1, "VIEW_TYPE_ALBUM_RANK_1：排行榜1 = " + VIEW_TYPE_ALBUM_RANK_1);
        typeStringArray.put(VIEW_TYPE_ALBUM_RANK_2, "VIEW_TYPE_ALBUM_RANK_2：排行榜2 = " + VIEW_TYPE_ALBUM_RANK_2);
        typeStringArray.put(VIEW_TYPE_ALBUM_RANK_3, "VIEW_TYPE_ALBUM_RANK_3：排行榜3 = " + VIEW_TYPE_ALBUM_RANK_3);
        typeStringArray.put(VIEW_TYPE_ALBUM_RANK_4, "VIEW_TYPE_ALBUM_RANK_4：排行榜4 = " + VIEW_TYPE_ALBUM_RANK_4);
        typeStringArray.put(VIEW_TYPE_ALBUM_RANK_5, "VIEW_TYPE_ALBUM_RANK_5：排行榜5 = " + VIEW_TYPE_ALBUM_RANK_5);

        typeStringArray.put(VIEW_TYPE_FILTER_PANEL, "VIEW_TYPE_FILTER_PANEL：筛选面板 = " + VIEW_TYPE_FILTER_PANEL);

        typeStringArray.put(VIEW_TYPE_FILTER_STREAM_ALBUM, "VIEW_TYPE_FILTER_STREAM_ALBUM：筛选流中的专辑样式 = " + VIEW_TYPE_FILTER_STREAM_ALBUM);
        typeStringArray.put(VIEW_TYPE_BANNER_ONE_KEY_TING, "VIEW_TYPE_BANNER_ONE_KEY_TING：一键听Banner = " + VIEW_TYPE_BANNER_ONE_KEY_TING);

        typeStringArray.put(VIEW_TYPE_LIST_VIEW_NO_CONTENT, "VIEW_TYPE_LIST_VIEW_NO_CONTENT：楼层中没有内容的空界面类型 = " + VIEW_TYPE_LIST_VIEW_NO_CONTENT);

        typeStringArray.put(VIEW_TYPE_FOCUS_IMAGE, "VIEW_TYPE_FOCUS_IMAGE：焦点图 = " + VIEW_TYPE_FOCUS_IMAGE);

        typeStringArray.put(VIEW_TYPE_MY_SUBSCRIPTION, "VIEW_TYPE_MY_SUBSCRIPTION：我的订阅 = " + VIEW_TYPE_MY_SUBSCRIPTION);

        typeStringArray.put(VIEW_TYPE_CLOUD_TAG, "VIEW_TYPE_CLOUD_TAG：云标签模块 = " + VIEW_TYPE_CLOUD_TAG);

        typeStringArray.put(VIEW_TYPE_CHILD_TAB, "VIEW_TYPE_CHILD_TAB：子Tab模块 = " + VIEW_TYPE_CHILD_TAB);

        typeStringArray.put(VIEW_TYPE_SKITS, "VIEW_TYPE_SKITS：短剧单元模块 = " + VIEW_TYPE_SKITS);
        typeStringArray.put(VIEW_TYPE_SKITS_NEW, "VIEW_TYPE_SKITS：短剧新单元模块 = " + VIEW_TYPE_SKITS_NEW);

        typeStringArray.put(VIEW_TYPE_AD_BIG_PICTURE_OR_VIDEO, "VIEW_TYPE_AD_BIG_PICTURE_OR_VIDEO：大图广告或视频 = " + VIEW_TYPE_AD_BIG_PICTURE_OR_VIDEO);

        typeStringArray.put(VIEW_TYPE_SMOOTH_FREE_ALBUM_HORIZONTAL, "VIEW_TYPE_SMOOTH_FREE_ALBUM_HORIZONTAL：免费畅听会员专辑 = " + VIEW_TYPE_SMOOTH_FREE_ALBUM_HORIZONTAL);

        typeStringArray.put(VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL, "VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL：免费畅听会员声音 = " + VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL);

        typeStringArray.put(VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL_LIST, "VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL_LIST：免费畅听会员声音 = " + VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL_LIST);

        typeStringArray.put(VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL_LIST2, "VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL_LIST2：免费畅听会员声音 = " + VIEW_TYPE_SMOOTH_FREE_TRACK_HORIZONTAL_LIST2);

        typeStringArray.put(VIEW_TYPE_NOVEL_HORIZONTAL, "VIEW_TYPE_ALBUM_HORIZONTAL：横排小说模块样式 = " + VIEW_TYPE_NOVEL_HORIZONTAL);

        typeStringArray.put(VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_NOVEL, "VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_ALBUM：横向一行滑动的小说模块类型 = " + VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_NOVEL);
    }


    private void checkViewType(int viewType) {
        if (adapterMap == null || !adapterMap.containsKey(viewType)) {
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException("设置ViewType时要先进行配置");
            }
        }
    }

    public ItemModel add(Object dataObj, int viewType) {
        return add(dataObj, viewType, null);
    }

    /**
     * 添加楼层
     *
     * @param dataObj  楼层数据
     * @param viewType 楼层类型
     * @param extra    额外信息
     * @return
     */
    public ItemModel add(int position, Object dataObj, int viewType, Object extra) {
        if (dataObj == null) return null;
        checkViewType(viewType);
        ItemModel itemModel = new ItemModel(dataObj, viewType);
        itemModel.setViewTypeString(getPreviousFloorTypeString(viewType));
        Bundle bundle = new Bundle();

        bundle.putInt(KEY_PREVIOUS_FLOOR_TYPE, previousFloorType);
        bundle.putString(KEY_PREVIOUS_FLOOR_TYPE_STRING, getPreviousFloorTypeString(previousFloorType));
        itemModel.setTag(extra);
        itemModel.setBundle(bundle);
        listData.add(position, itemModel);
        previousFloorType = viewType;
        return itemModel;
    }

    /**
     * 添加楼层
     *
     * @param dataObj  楼层数据
     * @param viewType 楼层类型
     * @param extra    额外信息
     * @return
     */
    public ItemModel add(Object dataObj, int viewType, Object extra) {
        if (dataObj == null) return null;
        checkViewType(viewType);
        ItemModel itemModel = new ItemModel(dataObj, viewType);
        itemModel.setViewTypeString(getPreviousFloorTypeString(viewType));
        Bundle bundle = new Bundle();

        bundle.putInt(KEY_PREVIOUS_FLOOR_TYPE, previousFloorType);
        bundle.putString(KEY_PREVIOUS_FLOOR_TYPE_STRING, getPreviousFloorTypeString(previousFloorType));
        itemModel.setTag(extra);
        itemModel.setBundle(bundle);
        listData.add(itemModel);
        previousFloorType = viewType;
        return itemModel;
    }

    public ItemModel add(ItemModel itemModel) {
        listData.add(itemModel);
        return itemModel;
    }

    @Override
    public List<ItemModel> getListData() {
        if (listData == null) {
            listData = new ArrayList<>();
        }
        return listData;
    }

    public void clear() {
        if (listData != null) {
            listData.clear();
            notifyDataSetChanged();
        } else {
            listData = new ArrayList<>();
        }
    }

    @Override
    public int getCount() {
        return listData.size();
    }

    @Override
    public ItemModel getItem(int position) {
        if (listData != null && listData.size() > 0 && position < listData.size() && position >= 0) {
            return listData.get(position);
        }
        return null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getViewTypeCount() {
//      int viewTypeSize = 0;
//      if (adapterMap != null) {
//          viewTypeSize = adapterMap.size();
//      }
//      // 有一个默认的VIEW_TYPE_NONE类型
//      return viewTypeSize + 1;
        //如果viewType没有添加到adapterMap中，那使用size的数量会不一致，造成无法展示
        return VIEW_TYPE_MAX_COUNT;
    }

    @Override
    public int getItemViewType(int position) {
        ItemModel itemModel = getItem(position);
        if (itemModel != null) {
            return itemModel.getViewType();
        }
        if (ConstantsOpenSdk.isDebug) {
            throw new RuntimeException(getClass().getName() + " : 相关的viewType 没有注册");
        }
        return VIEW_TYPE_NONE;
    }

    @Override
    public int getFrom() {
        if (mExtraViewModel != null) {
            return mExtraViewModel.from;
        }
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ItemModel itemModel = listData.get(position);
        int viewType = getItemViewType(position);

        HolderAdapter.BaseViewHolder viewHolder;
        IMulitViewTypeViewAndData adapterProvider = getProvider(itemModel, viewType);
        if (convertView == null) {
            convertView = adapterProvider.getView(inflater, position, parent);
            viewHolder = adapterProvider.buildHolder(convertView);
            convertView.setTag(viewHolder);
        } else {
            //不需要使用第二tag校验，直接使用缓存
            viewHolder = (HolderAdapter.BaseViewHolder) convertView.getTag();
        }
        setGlobalHomeRecommendGrayView(position, viewType, convertView);
        adapterProvider.bindViewDatas(viewHolder, itemModel, convertView, position);
        return convertView;
    }

    private void setGlobalHomeRecommendGrayView(int position, int viewType, View convertView) {
        if (convertView == null || !ConfigureNeedCacheLocalManager.getGlobalGraySwitch()) return;
        if (!(mFragment instanceof LiteHomeRecommendFragment)) return;
        if (((LiteHomeRecommendFragment) mFragment).mCurrPageType != LiteTabModel.TYPE_RECOMMEND)
            return;
        Boolean isGray = globalModelMap.get(viewType + "");
        if (position < ConfigureNeedCacheLocalManager.getGlobalGrayModelNumber()) {
            globalModelMap.put(viewType + "", true);
            GlobalGrayManager.globalGrayInner(convertView);
        } else if (isGray != null && isGray) {
            GlobalGrayManager.cancelGlobalGrayInner(convertView);
        }
    }

    private IMulitViewTypeViewAndData<?, ?> getProvider(ItemModel<?> itemModel, int viewType) {
        // 广告因为有多个 使用原有的模式实现不了  单独处理
        if (viewType == VIEW_TYPE_AD_BIG_PICTURE_OR_VIDEO && HomeFeedAdABManager.INSTANCE.isNewProvider()) {
            Object object = itemModel.getObject();
            if (object instanceof LiteFloorModel) {
                LiteFloorModel floorModel = (LiteFloorModel) object;
                if (floorModel.adContentList != null && !floorModel.adContentList.isEmpty()) {
                    int moduleId = floorModel.adContentList.get(0).getModuleId();
                    IMulitViewTypeViewAndData<?, ?> viewTypeViewAndData = adProviderMap.get(moduleId);
                    if (viewTypeViewAndData == null) {
                        viewTypeViewAndData = new LiteHomeAdPictureOrVideoProviderNew(mFragment, this);
                        adProviderMap.put(moduleId, viewTypeViewAndData);
                    }
                    return viewTypeViewAndData;
                }
            }
        }

        return adapterMap.get(viewType);
    }

    @Override
    public void onClick(View v) {

    }

    public void release() {
        for (Map.Entry<Integer, IMulitViewTypeViewAndData> viewAndDataEntry : adapterMap.entrySet()) {
            Object obj = viewAndDataEntry.getValue();
            if (obj instanceof IAdapterRelease) {
                ((IAdapterRelease) obj).onRelease();
            }
        }
        adapterMap.clear();

        for (Map.Entry<Integer, IMulitViewTypeViewAndData> viewTypeViewAndDataEntry : adProviderMap.entrySet()) {
            Object obj = viewTypeViewAndDataEntry.getValue();
            if (obj instanceof IAdapterRelease) {
                ((IAdapterRelease) obj).onRelease();
            }
        }
        adProviderMap.clear();
    }

    @Override
    public HomeRecommendExtraViewModel getHomeRecommendExtraViewModel() {
        return mExtraViewModel;
    }

    @Override
    public int getCategoryId() {
        if (getHomeRecommendExtraViewModel() != null) {
            return getHomeRecommendExtraViewModel().categoryId;
        }
        return -1;
    }

    @Override
    public void removeItem(int position) {
        if (listData == null) {
            return;
        }
        if (position < 0) {
            return;
        }
        if (position >= listData.size()) {
            return;
        }
        listData.remove(position);
        notifyDataSetChanged();
    }

    public interface IAdapterRelease {
        void onRelease();
    }

    /**
     * 设置额外使用的各种信息
     */
    public void setHomeRecommendExtraViewModel(HomeRecommendExtraViewModel viewModel) {
        mExtraViewModel.updateSelf(viewModel);
    }

    public IMulitViewTypeViewAndData getProvider(int type) {
        if (adapterMap != null) {
            return adapterMap.get(type);
        }
        return null;
    }

    private String getPreviousFloorTypeString(int previousFloorType) {
        return typeStringArray.get(previousFloorType, DEFAULT_FLOOR_TYPE_STRING);
    }

}