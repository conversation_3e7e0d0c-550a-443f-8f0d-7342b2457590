package com.ximalaya.ting.lite.main.manager;

import com.ximalaya.ting.android.host.activity.SmsLoginDialogActivity;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainActivityAction;
import com.ximalaya.ting.lite.main.login.LoginActivity;
import com.ximalaya.ting.lite.main.login.qucikmob.QuickLoginForMobFullPageActivity;
import com.ximalaya.ting.lite.main.login.qucikmob.QuickLoginForMobHalfPageActivity;

/**
 * <AUTHOR> on 2017/3/8.
 */

public class MainActivityActionImpl implements IMainActivityAction {
    @Override
    public Class getLoginActivity() {
        return LoginActivity.class;
    }

    @Override
    public Class getHalfScreenLoginActivity() {
        return SmsLoginDialogActivity.class;
    }

    @Override
    public Class getMobQuickFullScreenLoginActivity() {
        return QuickLoginForMobFullPageActivity.class;
    }

    @Override
    public Class getMobQuickHalfScreenLoginActivity() {
        return QuickLoginForMobHalfPageActivity.class;
    }

}
