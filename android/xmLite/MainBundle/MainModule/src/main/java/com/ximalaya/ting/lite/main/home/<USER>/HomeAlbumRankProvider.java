package com.ximalaya.ting.lite.main.home.adapter;

import android.graphics.Color;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.view.other.NoScrollViewPager;
import com.ximalaya.ting.android.host.view.other.NoScrollViewPagerV2;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.album.fragment.NewAggregateRankFragment;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.home.fragment.HomeItemAlbumRankFragment;
import com.ximalaya.ting.lite.main.home.manager.HomeAlbumRankFloorTypeManager;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeAlbumRankFloorViewModel;
import com.ximalaya.ting.lite.main.model.album.HomeAlbumRankItem;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankArgsModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qinhuifeng on 2021/1/20
 *
 * <AUTHOR>
 */
public class HomeAlbumRankProvider implements IMulitViewTypeViewAndData<HomeAlbumRankProvider.Holder, HomeAlbumRankFloorViewModel> {

    //唯一id，根据唯一id，选择设置viewpager在页面中的唯一id
    //fragment中的viewpager使用多个必须要设置不同的id
    private BaseFragment2 baseFragment2;
    private HomeRecommedExtraDataProvider extraDataProvider;
    private int mFloorViewType = 0;
    private boolean mIsRecommendChannel;

    public HomeAlbumRankProvider(BaseFragment2 baseFragment2, HomeRecommedExtraDataProvider extraDataProvider, int floorViewType) {
        this.baseFragment2 = baseFragment2;
        this.extraDataProvider = extraDataProvider;
        this.mFloorViewType = floorViewType;
        mIsRecommendChannel = extraDataProvider.getFrom() == BundleValueConstantsInMain.FROM_NEW_HOME_RECOMMEND;
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_album_rank_floor, null);
    }

    @Override
    public Holder buildHolder(View convertView) {
        return new Holder(convertView);
    }

    @Override
    public void bindViewDatas(Holder holder, ItemModel<HomeAlbumRankFloorViewModel> t, View convertView, int position) {
        if (holder == null || t == null || t.object == null) {
            return;
        }
        HomeAlbumRankFloorViewModel albumRankFloorViewModel = t.object;
        if (albumRankFloorViewModel.homeAlbumRankItemList == null || albumRankFloorViewModel.homeAlbumRankItemList.size() == 0) {
            return;
        }
        String rankTitle = !TextUtils.isEmpty(albumRankFloorViewModel.title) ? albumRankFloorViewModel.title : "排行榜";
        holder.initViewPage(albumRankFloorViewModel.homeAlbumRankItemList, albumRankFloorViewModel.rankNeedRequestNumber, rankTitle);
        holder.tvMoreRankBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //不默认选中
                if (!TextUtils.isEmpty(albumRankFloorViewModel.moreClickUrl)) {
                    ToolUtil.clickUrlAction(baseFragment2, albumRankFloorViewModel.moreClickUrl, v);
                } else {
                    AggregateRankArgsModel model = new AggregateRankArgsModel();
                    //selectRankClusterId和selectRankingListId配置一个即可选中对应的tab
                    //跳转后，优先使用selectRankingListId进行匹配，匹配不到，使用selectRankClusterId进行匹配
                    model.selectRankingListId = -1;
                    NewAggregateRankFragment aggregateRankFragment = NewAggregateRankFragment.newInstance(model);
                    baseFragment2.startFragment(aggregateRankFragment);
                }
            }
        });
    }

    public class Holder extends HolderAdapter.BaseViewHolder {

        private HorizontalScrollView mHsTabLayout;
        private NoScrollViewPagerV2 viewPager;
        private TextView mTitle;
        private AlbumRankTabViewPageAdapter mViewPageAdapter;
        private List<HomeItemAlbumRankFragment> mFragmentList = new ArrayList<>();
        private LinearLayout llTabContainer;
        private TextView tvMoreRankBtn;
        private int dp4 = 0;

        public Holder(View convertView) {
            mHsTabLayout = convertView.findViewById(R.id.main_hs_tab);
            mTitle = convertView.findViewById(R.id.main_title_rank_item);
            viewPager = convertView.findViewById(R.id.main_view_page_rank);


            dp4 = BaseUtil.dp2px(baseFragment2.getContext(), 4);

            //唯一id，根据唯一id，选择设置viewpager在页面中的唯一id
            //fragment中的viewpager使用多个必须要设置不同的id
            int onlyViewPagerIdInFragment = HomeAlbumRankFloorTypeManager.getViewPagerOnlyIdInFragment(mFloorViewType);
            if (onlyViewPagerIdInFragment > 0) {
                viewPager.setId(onlyViewPagerIdInFragment);
            }

            viewPager.setNoScroll(true);
            //设置2个缓存
            viewPager.setOffscreenPageLimit(2);
            llTabContainer = convertView.findViewById(R.id.main_ll_tab_container);
            tvMoreRankBtn = convertView.findViewById(R.id.main_btn_more_rank_item);
        }

        /**
         * 初始化方法只允许调用1次，在listview bindViewDatas变动viewPage，特别是setAdapter和viewPager.setCurrentItem(defTab);
         */
        public void initViewPage(List<HomeAlbumRankItem> albumRankItems, int rankNeedRequestNumber, String rankTitle) {
            if (albumRankItems == null || albumRankItems.size() == 0) {
                return;
            }
            if (viewPager.getAdapter() != null) {
                return;
            }

            //设置标题
            mTitle.setText(rankTitle);

            //没有设置过adapter或者数量发生了变化，重新创建页面+Adapter
            //初始化操作
            mFragmentList.clear();
            int defTab = 0;
            //添加tab
            llTabContainer.removeAllViews();

            for (int i = 0; i < albumRankItems.size(); i++) {
                HomeAlbumRankItem item = albumRankItems.get(i);

                //fragment在ViewPager中会重建，参数setArguments传
                HomeItemAlbumRankFragment homeItemAlbumRankFragment = HomeItemAlbumRankFragment.newInstance(item.rankingListId, rankNeedRequestNumber, mIsRecommendChannel);

                //创建tab
                TextView tabText = (TextView) LayoutInflater.from(baseFragment2.getContext()).inflate(R.layout.main_item_album_rank_floor_tab_item, null);
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                layoutParams.leftMargin = dp4;
                layoutParams.rightMargin = dp4;
                tabText.setLayoutParams(layoutParams);
                tabText.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View clickView) {
                        if (llTabContainer == null) {
                            return;
                        }
                        int childCount = llTabContainer.getChildCount();
                        if (childCount == 0) {
                            return;
                        }
                        for (int j = 0; j < childCount; j++) {
                            View childAt = llTabContainer.getChildAt(j);
                            if (!(childAt instanceof TextView)) {
                                continue;
                            }
                            TextView textView = (TextView) childAt;
                            if (textView == clickView) {
                                //是当前tab
                                viewPager.setCurrentItem(j);
                                textView.setBackgroundResource(R.drawable.main_bg_home_rank_item_tab_bg);
                                textView.setTextColor(Color.parseColor("#e83f46"));
                                textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);

                                String familyName = "sans-serif-light";
                                Typeface boldTypeface = Typeface.create(familyName, Typeface.BOLD);
                                textView.setTypeface(boldTypeface);
                            } else {
                                textView.setBackgroundColor(Color.parseColor("#ffffff"));
                                textView.setTextColor(Color.parseColor("#999999"));
                                textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);

                                String familyName = "sans-serif-light";
                                Typeface boldTypeface = Typeface.create(familyName, Typeface.BOLD);
                                textView.setTypeface(boldTypeface);
                            }
                        }
                    }
                });

                //设置默认背景和颜色
                tabText.setBackgroundColor(Color.parseColor("#ffffff"));
                tabText.setTextColor(Color.parseColor("#999999"));
                tabText.setText(item.rankingListName);
                tabText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
                llTabContainer.addView(tabText);

                //只有1个榜单的时候，隐藏tab空间
                if (albumRankItems.size() == 1) {
                    mHsTabLayout.setVisibility(View.GONE);
                } else {
                    mHsTabLayout.setVisibility(View.VISIBLE);
                }
                if (item.isDefault) {
                    defTab = i;
                    homeItemAlbumRankFragment.setDataList(item.albumList, item.moduleId, item.rankingListId);
                    tabText.setBackgroundResource(R.drawable.main_bg_home_rank_item_tab_bg);
                    tabText.setTextColor(Color.parseColor("#e83f46"));
                    tabText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);

                    String familyName = "sans-serif-light";
                    Typeface boldTypeface = Typeface.create(familyName, Typeface.BOLD);
                    tabText.setTypeface(boldTypeface);
                }
                mFragmentList.add(homeItemAlbumRankFragment);
            }
            mViewPageAdapter = new AlbumRankTabViewPageAdapter(baseFragment2.getChildFragmentManager(), mFragmentList, null);
            viewPager.setAdapter(mViewPageAdapter);
            viewPager.setCurrentItem(defTab);
        }
    }
}
