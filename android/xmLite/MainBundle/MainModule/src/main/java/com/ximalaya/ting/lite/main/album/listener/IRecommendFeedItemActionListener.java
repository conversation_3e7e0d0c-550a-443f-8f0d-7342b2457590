package com.ximalaya.ting.lite.main.album.listener;

import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;

/**
 * Created by <PERSON><PERSON>u on 2018/12/11.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public interface IRecommendFeedItemActionListener {
    enum FeedItemType {
        ALBUM,
        TRACK;
    }

    enum ActionType {
        CLICK,
        UNINTERESTED;
    }

    /**
     *
     * @param itemType item类型
     * @param itemContentId item对应数据的id
     * @param actionType 操作类型
     * @param categoryId item对应数据所属分类id
     * @param itemData 方便匹配找到item数据所在位置
     */
    public void onItemAction(FeedItemType itemType, long itemContentId, ActionType actionType, long categoryId
            , RecommendItemNew itemData, ItemModel itemModel);
}
