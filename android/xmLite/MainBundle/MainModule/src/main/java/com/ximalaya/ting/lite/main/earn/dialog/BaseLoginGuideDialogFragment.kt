package com.ximalaya.ting.lite.main.earn.dialog

import android.app.Dialog
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.*
import androidx.fragment.app.FragmentManager
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment

/**
 * 登录引导弹窗
 */
abstract class BaseLoginGuideDialogFragment : BaseDialogFragment<BaseLoginGuideDialogFragment>(){

    var mMaskIsShow = false //解决fragment重复添加crash问题
    var onDialogCloseListener: OnDialogCloseListener ?= null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view: View = inflater.inflate(getLayoutRes(), container, false)
        initUI(view)
        return view
    }

    abstract fun getLayoutRes():Int

    abstract fun initUI(view: View)

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        //去掉标题
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCanceledOnTouchOutside(false)
        val window = dialog.window
        if (window != null) {
            //dialog背景设置透明，解决shape不生效问题
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window.decorView.setPadding(0, 0, 0, 0) //消除边距
            val lp = window.attributes
            lp.width = WindowManager.LayoutParams.MATCH_PARENT //设置宽度充满屏幕
            lp.height = WindowManager.LayoutParams.MATCH_PARENT
            window.attributes = lp
        }
        return dialog
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        mMaskIsShow = false
    }

    override fun isShowing(): Boolean {
        return mMaskIsShow
    }

    override fun show(manager: FragmentManager, tag: String?) {
        if (mMaskIsShow) {
            return
        }
        mMaskIsShow = true
        super.show(manager, tag)
    }
}