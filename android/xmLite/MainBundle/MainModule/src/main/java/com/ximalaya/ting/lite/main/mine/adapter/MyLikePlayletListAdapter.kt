package com.ximalaya.ting.lite.main.mine.adapter

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.xiaomi.push.ho
import com.ximalaya.ting.android.framework.adapter.HolderAdapter
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.main.R

/**
 * Created by zk on 2022/1/27
 * 我的-喜欢-短剧 Adapter
 */
class MyLikePlayletListAdapter(context: Context?, listData: MutableList<TrackM>?) : HolderAdapter<TrackM>(context, listData) {

    private lateinit var mListener: OnItemClickListener

    public fun setOnItemClickListener(listener: OnItemClickListener) {
        mListener = listener
    }

    override fun getConvertViewId(): Int {

        return R.layout.main_item_like_playlet
    }

    override fun buildHolder(convertView: View): BaseViewHolder {

        return ViewHolder(convertView)
    }

    override fun bindViewDatas(viewHolder: BaseViewHolder, item: TrackM, position: Int) {

        val holder = viewHolder as ViewHolder
        holder.tvTitle.text = item.trackTitle
        holder.tvTime.text = TimeHelper.toTime(item.duration.toDouble(), 0)
//        holder.tvLikeNum.text = "206万"
        ImageManager.from(context).displayImage(holder.ivCover, item.coverUrlMiddle, R.drawable.main_bg_skits_default)

        holder.clParent.setOnClickListener {
            mListener.onItemClick(position)
        }
    }

    override fun onClick(view: View?, t: TrackM?, position: Int, holder: BaseViewHolder?) {

    }

    class ViewHolder(view: View) : BaseViewHolder() {

        var clParent: ConstraintLayout = view.findViewById(R.id.main_cl_parent)
        var ivCover: ImageView = view.findViewById(R.id.main_iv_playlet_cover)
        var tvTitle: TextView = view.findViewById(R.id.main_tv_playlet_title)
        var tvTime: TextView = view.findViewById(R.id.main_tv_playlet_time)
        var tvLikeNum: TextView = view.findViewById(R.id.main_tv_like_num)

    }

    interface OnItemClickListener {

        fun onItemClick(position: Int)
    }
}