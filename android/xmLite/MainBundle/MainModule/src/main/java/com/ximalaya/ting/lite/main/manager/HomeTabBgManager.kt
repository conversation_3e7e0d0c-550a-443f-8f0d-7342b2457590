package com.ximalaya.ting.lite.main.manager

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.view.XmLottieAnimationView
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.NormalModeActivity
import com.ximalaya.ting.android.host.activity.manager.AppModeManager
import com.ximalaya.ting.android.host.view.search.SearchTextSwitcher
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.model.newhome.LiteTabModel
import com.ximalaya.ting.lite.main.tab.HomeFragment
import com.ximalaya.ting.lite.main.view.LitePagerSlidingTabStrip

object HomeTabBgManager {

    @JvmStatic
    fun checkHomeTabBg(
        homeFragment: HomeFragment,
        mIvAtmosphereBg: ImageView,
        mTabs: LitePagerSlidingTabStrip?,
        mTvAllCategory: TextView?,
        mLlSearchLayout: LinearLayout?,
        ivSearch: ImageView?,
        tvSearch: SearchTextSwitcher?,
        mAllCategoryLottie: XmLottieAnimationView?,
        changeNormalTabBg: () -> Int,
        callback: HomeFreeModelGuideManager.IGetTabCallBack
    ) {
        // 短剧界面隐藏免费畅听
        val tab = callback.getCurTab() ?: return

        val isNormalModel = tab.pageType != LiteTabModel.TYPE_CSJ_DUANJU_FEED
        val bgDrawable = mLlSearchLayout?.background

        //tab底部的线条
        val topTobBottomLine: View? = homeFragment.findViewById(R.id.view_horizontal_split)
        val bgMask: View? = homeFragment.findViewById(R.id.view_short_play_mask_bg)

        if (isNormalModel) {
            // 背景图片
            mIvAtmosphereBg.visibility = View.VISIBLE
            changeNormalTabBg()
            if (bgDrawable is GradientDrawable) {
                bgDrawable.setColor(Color.parseColor("#f7f7f7"))
                mLlSearchLayout.background = bgDrawable
            }
            ivSearch?.clearColorFilter()
            tvSearch?.setTextColor(Color.parseColor("#666666"))
            mAllCategoryLottie?.progress = 1f
            mAllCategoryLottie?.setAnimation("lottie/all_category/data.json")
            mAllCategoryLottie?.imageAssetsFolder = "lottie/all_category/images"

            topTobBottomLine?.setBackgroundColor(
                ContextCompat.getColor(
                    BaseApplication.getMyApplicationContext(),
                    R.color.main_color_e8e8e8
                )
            )
            bgMask?.visibility = View.GONE
        } else {
            mIvAtmosphereBg.visibility = View.GONE
            mTabs?.textColor = Color.parseColor("#ffffff")
            mTabs?.setDeactivateTextColor(Color.parseColor("#ffffff"))
            mTabs?.indicatorColor = Color.parseColor("#ffffff")
            mTvAllCategory?.setTextColor(Color.parseColor("#ffffff"))

            if (bgDrawable is GradientDrawable) {
                bgDrawable.setColor(Color.parseColor("#33ffffff"))
                mLlSearchLayout.background = bgDrawable
            }

            ivSearch?.setColorFilter(Color.parseColor("#ffffff"))
            tvSearch?.setTextColor(Color.parseColor("#ffffff"))

            mAllCategoryLottie?.progress = 1f
            mAllCategoryLottie?.setAnimation("lottie/all_dark_category/data.json")
            mAllCategoryLottie?.imageAssetsFolder = "lottie/all_dark_category/images"

            topTobBottomLine?.setBackgroundColor(
                ContextCompat.getColor(
                    BaseApplication.getMyApplicationContext(),
                    R.color.host_transparent
                )
            )
            bgMask?.visibility = View.VISIBLE
        }

        checkBottomTabBg(homeFragment, callback)

    }

    @JvmStatic
    fun checkBottomTabBg(
        homeFragment: HomeFragment,
        callback: HomeFreeModelGuideManager.IGetTabCallBack
    ) {
        // 短剧界面隐藏免费畅听
        val tab = callback.getCurTab() ?: return

        val isNormalModel = tab.pageType != LiteTabModel.TYPE_CSJ_DUANJU_FEED
        val activity = BaseApplication.getMainActivity()
        var normalModeActivity: NormalModeActivity?=null

        val tabRadioGroup = if (activity is MainActivity) {
            normalModeActivity = activity.normalModeActivity
            activity.tabRadioGroup
        } else {
            null
        }

        if (isNormalModel || !homeFragment.isRealVisable) {
            normalModeActivity?.setPlayProgressBarRoundColor(homeFragment.resources.getColor(R.color.host_color_e8e8e8))

            // 底部tab
            if (tabRadioGroup != null && AppModeManager.isAppModeForNormal()) {
                val resources = tabRadioGroup.resources
                tabRadioGroup.setBackgroundColor(Color.TRANSPARENT)
                val tabHome = tabRadioGroup.findViewById<RadioButton?>(R.id.tab_home)
                val tabListen = tabRadioGroup.findViewById<RadioButton?>(R.id.tab_i_listen)
                val tabWelfare = tabRadioGroup.findViewById<RadioButton?>(R.id.tab_welfare)
                val tabMine = tabRadioGroup.findViewById<RadioButton?>(R.id.tab_mine)

                val textColor =
                    resources.getColorStateList(R.color.host_theme_bottom_tab_text_selector)

                val homeDrawable = ResourcesCompat.getDrawable(
                    resources, R.drawable.host_theme_tab1_selector, null
                )
                homeDrawable?.apply {
                    setBounds(0, 0, minimumWidth, minimumHeight)
                }
                tabHome?.setCompoundDrawables(null, homeDrawable, null, null)
                tabHome?.setTextColor(textColor)

                val listenDrawable = ResourcesCompat.getDrawable(
                    resources, R.drawable.host_theme_tab2_selector, null
                )
                listenDrawable?.apply {
                    setBounds(0, 0, minimumWidth, minimumHeight)
                }
                tabListen?.setCompoundDrawables(null, listenDrawable, null, null)
                tabListen?.setTextColor(textColor)

                val welfareDrawable = ResourcesCompat.getDrawable(
                    resources, R.drawable.host_theme_tab3_selector, null
                )
                welfareDrawable?.apply {
                    setBounds(0, 0, minimumWidth, minimumHeight)
                }
                tabWelfare?.setCompoundDrawables(null, welfareDrawable, null, null)
                tabWelfare?.setTextColor(textColor)

                val mineDrawable = ResourcesCompat.getDrawable(
                    resources, R.drawable.host_theme_tab4_selector, null
                )
                mineDrawable?.apply {
                    setBounds(0, 0, minimumWidth, minimumHeight)
                }
                tabMine?.setCompoundDrawables(null, mineDrawable, null, null)
                tabMine?.setTextColor(textColor)
            }

        } else {
            normalModeActivity?.setPlayProgressBarRoundColor(homeFragment.resources.getColor(R.color.host_color_666666))

            // 底部tab
            if (tabRadioGroup != null && AppModeManager.isAppModeForNormal()) {
                val resources = tabRadioGroup.resources
                tabRadioGroup.setBackgroundColor(Color.parseColor("#000000"))
                val tabHome = tabRadioGroup.findViewById<RadioButton?>(R.id.tab_home)
                val tabListen = tabRadioGroup.findViewById<RadioButton?>(R.id.tab_i_listen)
                val tabWelfare = tabRadioGroup.findViewById<RadioButton?>(R.id.tab_welfare)
                val tabMine = tabRadioGroup.findViewById<RadioButton?>(R.id.tab_mine)

                val textColor =
                    resources.getColorStateList(R.color.host_theme_bottom_tab_text_short_draw_selector)

                val homeDrawable = ResourcesCompat.getDrawable(
                    resources, R.drawable.host_theme_tab1_short_draw_selector, null
                )
                homeDrawable?.apply {
                    setBounds(0, 0, minimumWidth, minimumHeight)
                }
                tabHome?.setCompoundDrawables(null, homeDrawable, null, null)
                tabHome?.setTextColor(textColor)

                val listenDrawable = ResourcesCompat.getDrawable(
                    resources, R.drawable.host_theme_tab2_short_draw_selector, null
                )
                listenDrawable?.apply {
                    setBounds(0, 0, minimumWidth, minimumHeight)
                }
                tabListen?.setCompoundDrawables(null, listenDrawable, null, null)
                tabListen?.setTextColor(textColor)

                val welfareDrawable = ResourcesCompat.getDrawable(
                    resources, R.drawable.host_theme_tab3_short_draw_selector, null
                )
                welfareDrawable?.apply {
                    setBounds(0, 0, minimumWidth, minimumHeight)
                }
                tabWelfare?.setCompoundDrawables(null, welfareDrawable, null, null)
                tabWelfare?.setTextColor(textColor)

                val mineDrawable = ResourcesCompat.getDrawable(
                    resources, R.drawable.host_theme_tab4_short_draw_selector, null
                )
                mineDrawable?.apply {
                    setBounds(0, 0, minimumWidth, minimumHeight)
                }
                tabMine?.setCompoundDrawables(null, mineDrawable, null, null)
                tabMine?.setTextColor(textColor)
            }
        }

    }
}