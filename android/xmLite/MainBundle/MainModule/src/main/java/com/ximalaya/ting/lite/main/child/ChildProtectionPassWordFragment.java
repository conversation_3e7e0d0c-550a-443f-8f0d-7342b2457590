package com.ximalaya.ting.lite.main.child;


import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.sina.util.dnscache.DNSCache;
import com.sina.util.dnscache.constants.PreferenceConstantsInDNSCache;
import com.xiaomi.mipush.sdk.MiPushClient;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.manager.AppModeManager;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.manager.AppModeGlobalChangeManager;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

/**
 * Created by qinhuifeng on 2018/11/08.
 * <p>
 * 未成年人保护模式--输入/设置 密码
 *
 * <AUTHOR>
 */
public class ChildProtectionPassWordFragment extends BaseFragment2 {
    public static final String TAG = "ChildProtectionPassWordFragment";

    private static final int PWD_LENGTH = 4;

    private TextView mTvInfo;
    private TextView[] mTvNumbers;
    private View[] mViewNumberBottomLines;
    private EditText mEtPwd;
    private InputMethodManager mInputManager;

    private String mPwdRecord = "";
    //增加密码展示方式  true:密文展示  false:明文展示
    private boolean mIsShowPwd = true;

    public static ChildProtectionPassWordFragment newInstanceForOpen() {
        Bundle args = new Bundle();
        ChildProtectionPassWordFragment fragment = new ChildProtectionPassWordFragment();
        fragment.setArguments(args);
        return fragment;
    }

    public static ChildProtectionPassWordFragment newInstanceForClose() {
        Bundle args = new Bundle();
        ChildProtectionPassWordFragment fragment = new ChildProtectionPassWordFragment();
        fragment.setArguments(args);
        return fragment;
    }


    public ChildProtectionPassWordFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_FRAMELAYOUT, null);
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    private void parseBundle() {
        Bundle bundle = getArguments();
        if (bundle == null) {
            return;
        }
    }


    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("青少年模式");
        parseBundle();
        mTvInfo = findViewById(R.id.main_tv_info);
        mEtPwd = findViewById(R.id.main_et_pwd);
        initNumberText();

        if (ChildProtectManager.isChildProtectOpen(getContext())) {
            //当前是开启状态，需要去关闭
            mTvInfo.setText("输入密码");
        } else {
            mTvInfo.setText("设置密码");
        }
        mEtPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String string = mEtPwd.getText().toString();
                updateCodeContent(string);
            }
        });


        if (getActivity() != null) {
            mInputManager = SystemServiceManager.getInputMethodManager(getActivity());
        }

        mEtPwd.setFocusable(true);
        mEtPwd.setFocusableInTouchMode(true);
        mEtPwd.requestFocus();

        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (mInputManager != null) {
                    mInputManager.showSoftInput(mEtPwd, 0);
                }
            }
        });
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 100048;
        super.onMyResume();
    }

    private void initNumberText() {
        mTvNumbers = new TextView[PWD_LENGTH];
        mTvNumbers[0] = findViewById(R.id.main_tv_number_1);
        mTvNumbers[1] = findViewById(R.id.main_tv_number_2);
        mTvNumbers[2] = findViewById(R.id.main_tv_number_3);
        mTvNumbers[3] = findViewById(R.id.main_tv_number_4);
        mEtPwd.setText("");

        //底部线条
        mViewNumberBottomLines = new View[PWD_LENGTH];
        mViewNumberBottomLines[0] = findViewById(R.id.main_view_number_bottom_line_1);
        mViewNumberBottomLines[1] = findViewById(R.id.main_view_number_bottom_line_2);
        mViewNumberBottomLines[2] = findViewById(R.id.main_view_number_bottom_line_3);
        mViewNumberBottomLines[3] = findViewById(R.id.main_view_number_bottom_line_4);
        //初始化选中第一个
        mViewNumberBottomLines[0].setBackgroundColor(ContextCompat.getColor(mContext, R.color.host_color_FF6110));
    }

    /**
     * 拆分显示密码
     */
    private void updateCodeContent(String s) {
        if (s == null) {
            return;
        }
        char[] charArray = s.toCharArray();
        for (int i = 0; i < mTvNumbers.length; i++) {
            if (i > charArray.length - 1) {
                mTvNumbers[i].setText("");
            } else {
                //注意不可以去掉强转的 ""
                //只setText(charArray[i])实际调用的是setText(@StringRes int resid)方法
                if (mIsShowPwd) {
                    mTvNumbers[i].setText("*");
                } else {
                    mTvNumbers[i].setText(charArray[i] + "");
                }
            }

            //设置底部线条颜色
            if (charArray.length == i) {
                mViewNumberBottomLines[i].setBackgroundColor(ContextCompat.getColor(mContext, R.color.host_color_FF6110));
            } else {
                mViewNumberBottomLines[i].setBackgroundColor(Color.parseColor("#D2D2D2"));
            }
        }
        if (s.length() == PWD_LENGTH) {
            //触发完成操作
            inputFinish(s);
        }
    }

    /**
     * 密码输入完成校验
     *
     * @param s
     */
    private void inputFinish(String s) {
        boolean childProtectOpen;
        childProtectOpen = ChildProtectManager.isChildProtectOpen(getContext());
        if (childProtectOpen) {
            //目前打开状态，去关闭
            closeChildProtect(s);
        } else {
            //目前是关闭状态需要去打开
            if (TextUtils.isEmpty(mPwdRecord)) {
                mPwdRecord = s;
                //首次输入清除数据
                //延时200毫秒，让用户可以看到最后输入的一个数字
                mEtPwd.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        mEtPwd.setText("");
                        mTvInfo.setText("再次确认");
                    }
                }, 200);
            } else {
                if (mPwdRecord.equals(s)) {
                    //两次输入一致，可以进行开启操作
                    openChildProtect(mPwdRecord);
                } else {
                    //两次密码输入的不一致，请重新输入
                    CustomToast.showFailToast("密码不一致");
                    //两次密码输入不一致，关闭当前页面
                    finishFragment();
                }
            }
        }
    }


    /**
     * 请求服务端，关闭儿童保护模式
     */
    private void closeChildProtect(String pwd) {
        boolean isCloseSuccess = ChildProtectManager.closeChildProtectStatus(pwd);
        if (isCloseSuccess) {
            //输入密码正确，跳转到首页，并弹出toast提示：青少年模式已关闭
            finishFragment();
            CustomToast.showSuccessToast("青少年模式已关闭");
            MiPushClient.resumePush(mContext.getApplicationContext(), null);

            if (UnlockListenTimeManagerNew.INSTANCE.getUnlockConfigModel() == null) {
                UnlockListenTimeManagerNew.INSTANCE.init();
            }

            if (mActivity instanceof MainActivity) {
                Bundle bundle = new Bundle();
                bundle.putBoolean(TabFragmentManager.TAB_SHALL_REPLACE, true);
                ((MainActivity) mActivity).switchHomeTab(bundle);
            }
        } else {
            //输入密码错误，弹出toast提示：密码错误，并自动返回到青少年模式设置首页
            //关闭失败，密码不一致
            finishFragment();
            CustomToast.showSuccessToast("密码错误");
        }
    }

    /**
     * 请求服务端，打开儿童保护模式
     */
    private void openChildProtect(String pwd) {
        ChildProtectManager.openChildProtectStatus(pwd);
        CustomToast.showSuccessToast("青少年模式已开启");
        if (mActivity instanceof MainActivity) {
            final boolean isPlaying = XmPlayerManager.getInstance(mContext).isPlaying();
            if (isPlaying) {
                XmPlayerManager.getInstance(mContext).pause();
            }

            XmPlayerManager.getInstance(mActivity).clearCurTrackCache();
            //停止播放
            XmPlayerManager.getInstance(mActivity).stop();
            //重置播放器
            XmPlayerManager.getInstance(mActivity).resetPlayer();
            //播放列表
            XmPlayerManager.getInstance(mActivity).resetPlayList();

            HandlerManager.postOnUIThreadDelay(() -> {
                //清除当前播放的声音
                XmPlayerManager.getInstance(mActivity).clearCurTrackCache();
                //停止播放
                XmPlayerManager.getInstance(mActivity).stop();
                //重置播放器
                XmPlayerManager.getInstance(mActivity).resetPlayer();
                //播放列表
                XmPlayerManager.getInstance(mActivity).resetPlayList();
                XmPlayerManager.getInstance(mActivity).clearPlayList();
                XmPlayerManager.getInstance(mActivity).clearPlayCache();
                XmPlayerManager.getInstance(mActivity).clearAllLocalHistory();
            }, 200);

            if (AppModeManager.isAppModeForTruckFriend()) {
                HandlerManager.postOnUIThread(() -> {
                    AppModeManager.selectAppMode(AppModeGlobalChangeManager.MODE_NORMAL_DEF);
                    mActivity.recreate();
                });
            } else {
                Bundle bundle = new Bundle();
                bundle.putBoolean(TabFragmentManager.TAB_SHALL_REPLACE, true);
                ((MainActivity) mActivity).switchHomeTab(bundle);
            }
        }

        MiPushClient.pausePush(mContext.getApplicationContext(), null);

//        finishFragment();
        //青少年模式开启，关闭正在播放的直播
        try {
            //把音频直播给清掉
            PlayableModel currSound = XmPlayerManager.getInstance(mActivity).getCurrSound();
            if (PlayTools.isLiveMode(currSound)) {
                XmPlayerManager.getInstance(mActivity).stop();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (getActivity() instanceof MainActivity) {
            if (mInputManager != null && getView() != null && getView().getWindowToken() != null) {
                mInputManager.hideSoftInputFromWindow(getView().getWindowToken(), 0);
            }
        } else {
            finishFragment();
        }
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_child_protection_input_password;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public boolean isShowTruckFloatPlayBar() {
        return false;
    }

    @Override
    public boolean onBackPressed() {
        if (mInputManager != null && getView() != null && getView().getWindowToken() != null) {
            mInputManager.hideSoftInputFromWindow(getView().getWindowToken(), 0);
        }
        return super.onBackPressed();
    }
}
