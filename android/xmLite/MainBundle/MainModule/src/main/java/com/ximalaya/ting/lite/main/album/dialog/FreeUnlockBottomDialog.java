package com.ximalaya.ting.lite.main.album.dialog;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.business.unlock.manager.VipTrackUnLockPaidManager;
import com.ximalaya.ting.android.host.business.unlock.model.AlbumPaidUnLockHintInfo;
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.model.UnLockRelationModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.ShareStringCodeUtils;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBackNew;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.album.fragment.LiteAlbumFragmentNewList;
import com.ximalaya.ting.lite.main.utils.ShareUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @des:免费听书
 */
public class FreeUnlockBottomDialog extends XmBaseDialog<FreeUnlockBottomDialog> implements View.OnClickListener, IDataCallBackNew {
    private final Activity mActivity;
    private TextView tvLookShortVideo;
    private TextView tvOpenVipMember;
    private AlbumPaidUnLockHintInfo mUnLockHintInfo = null;
    private AlbumM mAlbumM;
    private long mAlbumId;

    private TextView mTitle;
    private TextView mTvFreeUnlockHint;
    private List<Track> mTrackList = null;
    private LiteAlbumFragmentNewList albumFragmentNewList;
    // 外部解锁回调
    private UnlockListenTimeManagerNew.IVideoUnlockCallBack mIVideoUnlockCallBack;
    private JSONObject freeUnlockBottomVipJson;

    public FreeUnlockBottomDialog(@NonNull Activity context, AlbumM mAlbum) {
        super(context, R.style.host_share_dialog);
        this.mActivity = context;
        this.mAlbumM = mAlbum;
    }

    public void setUnlockHintInfo(List<Track> trackList, AlbumPaidUnLockHintInfo info) {
        this.mUnLockHintInfo = info;
        this.mTrackList = trackList;
    }

    public void setCurrentFragment(LiteAlbumFragmentNewList fragmentNewList) {
        this.albumFragmentNewList = fragmentNewList;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getWindow() != null) {
            View decorView = getWindow().getDecorView();
            //宽度铺满屏幕
            if (decorView != null) {
                decorView.setPadding(0, 0, 0, 0);
            }
            getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            WindowManager.LayoutParams lp = getWindow().getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            lp.gravity = Gravity.BOTTOM;
            lp.horizontalMargin = 0;
            getWindow().setAttributes(lp);
            getWindow().setWindowAnimations(R.style.host_popup_window_from_bottom_animation);
        }
        setCanceledOnTouchOutside(true);
        setCancelable(true);
        setContentView(R.layout.host_fra_dialog_free_unlock_album_bottom);
        initUI();
        mAlbumId = mAlbumM != null ? mAlbumM.getId() : 0;
        // 专辑页-新-免费听书弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(46827)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put("albumId", String.valueOf(mAlbumId))
                .put("currPage", "albumPage")
                .createTrace();
    }

    private void initUI() {
        //标题
        mTitle = findViewById(R.id.host_tv_unlock_title);
        //提示内容
        mTvFreeUnlockHint = findViewById(R.id.host_tv_free_unlock_hint);
        //第一个资源位按钮
        ViewGroup mLayoutClose = findViewById(R.id.host_layout_close);
        tvOpenVipMember = findViewById(R.id.host_rl_tv_open_vip_member);
        tvLookShortVideo = findViewById(R.id.tv_look_short_video_ablum);
        mLayoutClose.setOnClickListener(this);
        tvLookShortVideo.setOnClickListener(this);
        tvOpenVipMember.setOnClickListener(this);
        if (mIVideoUnlockCallBack != null || mUnLockHintInfo != null && mUnLockHintInfo.isCanUnlock()) {
            tvLookShortVideo.setTag(true);
            tvLookShortVideo.setTextColor(Color.parseColor("#FF6110"));
            tvLookShortVideo.setBackgroundResource(R.drawable.host_round_bg_radius_0fff6110_dp48);
        } else {
            tvLookShortVideo.setTag(false);
            tvLookShortVideo.setTextColor(Color.parseColor("#666666"));
            tvLookShortVideo.setBackgroundResource(R.drawable.host_round_bg_radius_eeeeee_dp48);
        }
        freeUnlockBottomVipJson = getVipConfigureJsonData();
        mTitle.setText(freeUnlockBottomVipJson.optString("title", "本集为VIP专享，以下方式可收听"));

        customShortVideoUnLockSound();

        if (mIVideoUnlockCallBack != null || mUnLockHintInfo != null) {
            tvLookShortVideo.setVisibility(View.VISIBLE);
        } else {
            tvLookShortVideo.setVisibility(View.GONE);
        }
    }
    //用于配置解锁半浮层的按钮文案
    private JSONObject getVipConfigureJsonData() {
        try {
            String freeUnlockBottomVip = ConfigureCenter.getInstance().getString(CConstants.Group_Base.GROUP_NAME, CConstants.Group_Base.ITEM_FREEUNLOCKBOTTOM_VIP);
            return new JSONObject(freeUnlockBottomVip);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new JSONObject();
    }

    private void customShortVideoUnLockSound() {
        mTvFreeUnlockHint.setVisibility(View.GONE);
        if (tvOpenVipMember != null) {
            if (isShowOpenVipMember()) {
                tvOpenVipMember.setVisibility(View.VISIBLE);
            } else {
                boolean isOpenSwitch = getVipContinuousMonthlySubscriptionSwitchStatus();
                tvOpenVipMember.setVisibility(isOpenSwitch ? View.VISIBLE : View.GONE);
            }
            tvOpenVipMember.setText(freeUnlockBottomVipJson.optString("VIPbutton_name", "开通会员·18元连续包月"));
        }
        String videoBtnName = freeUnlockBottomVipJson.optString("videobutton_name", "看30s广告·免费收听%集");
        if (albumFragmentNewList != null && CollectionUtil.isNullOrEmpty(mTrackList)) {
            albumFragmentNewList.findFirstPaidTrackPosition(new IDataCallBack<UnLockRelationModel>() {
                @Override
                public void onSuccess(@Nullable UnLockRelationModel object) {
                    if (object != null && object.getTrackNumber() > 0) {
                        mTrackList = new ArrayList<>();
                        mTrackList.add(object.getTrack());
                    }
                    int count = mTrackList != null ? mTrackList.size() : 0;
                    if (count != 0) {
                        tvLookShortVideo.setText(videoBtnName.replace("%", count + ""));
                        tvLookShortVideo.setVisibility(View.VISIBLE);
                    } else {
                        tvLookShortVideo.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onError(int code, String message) {

                }
            });
        } else {
            int count = mTrackList != null ? mTrackList.size() : 0;
            if (count != 0) {
                tvLookShortVideo.setText(videoBtnName.replace("%", count + ""));
                tvLookShortVideo.setVisibility(View.VISIBLE);
            } else {
                tvLookShortVideo.setVisibility(View.GONE);
            }
        }
    }

    private boolean getVipContinuousMonthlySubscriptionSwitchStatus() {
        JSONObject json = ConfigureCenter.getInstance().getJson(CConstants.Group_Base.GROUP_NAME, CConstants.Group_Base.ITEM_UNLOCK_CHAPTER_CONFIG);
        if (json != null) {
            return json.optBoolean("VIPbutton", false);
        }
        return false;
    }

    private boolean isShowOpenVipMember() {
        int size = mTrackList != null ? mTrackList.size() : 0;
        for (int i = 0; i < size; i++) {
            if (isTrackLock(mTrackList.get(i))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断该声音是不是锁定状态
     */
    public boolean isTrackLock(Track track) {
        if (track == null || track.getAlbum() == null || UserInfoMannage.isVipUser()) return false;
        return !track.canPlayTrackForMainProcess() || track.isAudition();
    }

    @Override
    public void onClick(View view) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        if (view.getId() == R.id.host_layout_close) {
            dismiss();
            // 专辑页-新-免费听书弹窗-关闭按钮  弹框控件点击
            new XMTraceApi.Trace()
                    .setMetaId(46832)
                    .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                    .put("albumId", String.valueOf(mAlbumId))
                    .put("currPage", "albumPage")
                    .createTrace();
            return;
        }
        if (view.getId() == R.id.host_rl_tv_open_vip_member) {
            String freeUnlockBottomVipUrl = freeUnlockBottomVipJson.optString("VIPbutton_url");
            if (!TextUtils.isEmpty(freeUnlockBottomVipUrl) && BaseApplication.getMainActivity() instanceof MainActivity) {
                ToolUtil.clickUrlAction((MainActivity) BaseApplication.getMainActivity(), freeUnlockBottomVipUrl, view);
            }
            // 专辑页-新-免费听书弹窗-开通会员  弹框控件点击
            new XMTraceApi.Trace()
                    .setMetaId(49314)
                    .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                    .put("currPage", "albumPage")
                    .createTrace();
            dismiss();
            return;
        }
        if (view.getId() == R.id.tv_look_short_video_ablum) {
            try {
                // 专辑页-新-免费听书弹窗-看视频按钮  弹框控件点击
                new XMTraceApi.Trace()
                        .setMetaId(46831)
                        .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                        .put("albumId", String.valueOf(mAlbumId))
                        .put("currPage", "albumPage")
                        .createTrace();

                // 非空代表外部解锁
                if (mIVideoUnlockCallBack != null) {
                    mIVideoUnlockCallBack.onVideoUnlock();
                    return;
                }

                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mActivity);
                    return;
                }
                boolean isCanUnLock = (boolean) view.getTag();
                if (!isCanUnLock) {
                    if (mUnLockHintInfo.isUnlockAlbumLimit()) {
                        CustomToast.showFailToast("该专辑下今日无可解锁的集数，明日再来或者试试其它专辑哦~");
                        return;
                    }
                    CustomToast.showFailToast("今日看视频次数用完，明日可继续解锁");
                    return;
                }
                if (CollectionUtil.isNullOrEmpty(mTrackList) || mUnLockHintInfo == null) return;
                VipTrackUnLockPaidManager.startVipPaidTrackUnlock(mUnLockHintInfo, mActivity, mTrackList, VipTrackUnLockPaidManager.START_VIP_PAID_TRACK_UNLOCK_FROM_PLAY_VIP_BAR, null);
                dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void setIVideoUnlockCallBack(UnlockListenTimeManagerNew.IVideoUnlockCallBack mIVideoUnlockCallBack) {
        this.mIVideoUnlockCallBack = mIVideoUnlockCallBack;
    }

}
