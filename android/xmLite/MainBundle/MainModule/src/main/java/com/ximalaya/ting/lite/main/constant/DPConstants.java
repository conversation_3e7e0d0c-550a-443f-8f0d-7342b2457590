package com.ximalaya.ting.lite.main.constant;

import android.content.Context;

import com.ximalaya.ting.android.framework.util.BaseUtil;

/**
 * <AUTHOR> on 17/7/21.
 */

public class DPConstants {
    private static DPConstants mInstance;

    public final int DP_1;
    public final int DP_2;
    public final int DP_4;
    public final int DP_5;
    public final int DP_6;
    public final int DP_7;
    public final int DP_8;
    public final int DP_3;
    public final int DP_10;
    public final int DP_13;
    public final int DP_15;
    public final int DP_16;
    public final int DP_17;
    public final int DP_20;

    private DPConstants(Context context) {
        DP_1 = BaseUtil.dp2px(context, 1);
        DP_3 = DP_1 * 3;
        DP_2 = DP_1 * 2;
        DP_4 = DP_1 * 4;
        DP_5 = DP_1 * 5;
        DP_6 = DP_1 * 6;
        DP_7 = DP_1 * 7;
        DP_8 = DP_1 * 8;
        DP_10 = DP_1 * 10;
        DP_13 = DP_1 * 13;
        DP_15 = DP_1 * 15;
        DP_16 = DP_1 * 16;
        DP_17 = DP_1 * 17;
        DP_20 = DP_1 * 20;
    }

    public static DPConstants getInstance(Context context) {
        if (mInstance == null) {
            synchronized (DPConstants.class) {
                if (mInstance == null) {
                    mInstance = new DPConstants(context);
                }
            }
        }

        return mInstance;
    }

}
