package com.ximalaya.ting.lite.main.login.qucikmob;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;


import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.login.LoginPageTraceManager;
import com.ximalaya.ting.android.host.manager.login.QuickLoginLogger;
import com.ximalaya.ting.android.host.manager.login.mobquick.IMobLoginCallBack;
import com.ximalaya.ting.android.host.manager.login.mobquick.MobLoginParams;
import com.ximalaya.ting.android.host.manager.login.mobquick.MobQuickManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.util.common.TextFontManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.login.BaseLoginFragment;
import com.ximalaya.ting.lite.main.login.LoginArgeementView;

/**
 * Created by nali on 2018/5/3.
 * 短信登录fragment
 *
 * <AUTHOR>
 * <p>
 * 拷贝主app：SmsLoginFragment
 */
public class QuickLoginForHalfPageFragment extends BaseLoginFragment {
    private TextView mTitle;
    private TextView mTvPhoneNumber;
    private TextView mTvOperator;
    private TextView mTvLoginBtn;
    private ViewGroup mLayoutOtherLogin;
    private MobLoginParams mMobLoginParams;
    private ImageView mIvClose;
    private LoginArgeementView mLoginAgreementView;

    @Override
    public View onCreateView(final LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.main_sms_login_layout_for_mob_half, null);
        mMobLoginParams = LoginBundleParamsManager.getQuickMobParams(getArguments());
        //参数传递错误
        if (mMobLoginParams == null) {
            UserInfoMannage.loginBySelfPage(mContext, LoginBundleParamsManager.getLoginBy(getArguments()), getArguments());
            finishActivity();
            return view;
        }
        initView(view);

        LoginPageTraceManager.traceLoginPageShow(true, false);
        return view;
    }

    private void initView(View rootView) {
        mTitle = rootView.findViewById(R.id.main_sms_login_title);
        mTvPhoneNumber = rootView.findViewById(R.id.main_mob_tv_phone_number);
        mTvOperator = rootView.findViewById(R.id.main_mob_tv_operator);
        mTvLoginBtn = rootView.findViewById(R.id.main_mob_tv_quick_login);
        mLayoutOtherLogin = rootView.findViewById(R.id.main_mob_quick_other_login);
        mIvClose = rootView.findViewById(R.id.main_iv_close);
        mLoginAgreementView = rootView.findViewById(R.id.main_view_login_agreement_view);
        //设置标题
        mTitle.setText(LoginBundleParamsManager.getLoginTitle(getArguments()));
        //设置din字体
        TextFontManager.setFontForDIN_Alternate_Bold(mTvPhoneNumber);
        //设置手机号码
        mTvPhoneNumber.setText(mMobLoginParams.number);
        //设置运营商认证信息
        if (!TextUtils.isEmpty(mMobLoginParams.protocolName) && mMobLoginParams.protocolName.contains("联通")) {
            mTvOperator.setText("中国联通提供认证服务");
        } else if (!TextUtils.isEmpty(mMobLoginParams.protocolName) && mMobLoginParams.protocolName.contains("移动")) {
            mTvOperator.setText("中国移动提供认证服务");
        } else if (!TextUtils.isEmpty(mMobLoginParams.protocolName) && mMobLoginParams.protocolName.contains("电信")) {
            mTvOperator.setText("中国电信提供认证服务");
        }
        //去登录点击
        mTvLoginBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                //弹出选中提醒
                if (!mLoginAgreementView.checkAgreementSelectedAndShowHint()) {
                    return;
                }
                QuickLoginLogger.log("全屏登录界面=====开始登录===");

                //登录点击埋点，在校验之后
                LoginPageTraceManager.traceLoginBtnClick(true, false);
                //进行一键登录
                //展示loading
                showLoginProgress(mActivity);
                MobQuickManager.gotoMobQuickLogin(getArguments(),new IMobLoginCallBack() {
                    @Override
                    public void onMobLoginSuccess(LoginInfoModelNew loginInfoModelNew) {
                        //隐藏loading
                        dismissLoginProgress();
                        //登录成功了，关闭当前页面
                        finishActivity();

                        QuickLoginLogger.log("全屏登录界面=====开始登录=success=");
                        if (loginInfoModelNew != null) {
                            QuickLoginLogger.log("全屏登录界面=====开始登录=success=getUid=" + loginInfoModelNew.getUid());
                            QuickLoginLogger.log("全屏登录界面=====开始登录=success=getNickname=" + loginInfoModelNew.getNickname());
                        }
                    }

                    @Override
                    public void onMobLoginFailure(int code, String message) {
                        //隐藏loading
                        dismissLoginProgress();
                        //toast提示
                        CustomToast.showFailToast("登录失败，已自动切换为验证码登录");
                        //切换到短信登录
                        UserInfoMannage.loginBySelfPage(mContext, LoginBundleParamsManager.getLoginBy(getArguments()), getArguments());
                        //关闭当前页面
                        finishActivity();
                        QuickLoginLogger.log("全屏登录界面=====开始登录=error=" + code + " msg=" + message);
                    }
                });
            }
        });
        //切换到其他方式登录
        mLayoutOtherLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                UserInfoMannage.loginBySelfPage(mContext, LoginBundleParamsManager.getLoginBy(getArguments()), getArguments());
                //关闭当前页面
                finishActivity();
                //埋点
                LoginPageTraceManager.traceLoginSelectOtherLoginClick(false);
            }
        });

        mIvClose.setAlpha(0.4f);
        mIvClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finishActivity();
            }
        });
        //配置需要在initUi之前进行配置
        mLoginAgreementView.setQuickProtocolConfig(mMobLoginParams.protocolName, mMobLoginParams.protocolUrl);
        //初始化登录协议UI
        mLoginAgreementView.initUi(getActivity(), getArguments());
    }

    public void finishActivity() {
        if (mActivity != null) {
            mActivity.finish();
        } else {
            finishFragment();
        }
    }

    @Override
    protected String getPageLogicName() {
        return "QuickLoginForHalfPageFragment";
    }
}
