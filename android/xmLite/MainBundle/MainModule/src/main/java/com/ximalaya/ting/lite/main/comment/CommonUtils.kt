package com.ximalaya.ting.lite.main.comment

import com.google.gson.Gson
import com.google.gson.internal.LinkedTreeMap
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.lite.main.model.PlayRecordModel

object CommonUtils {

    fun nameIsNullReplaceUid(sourceStr: String?, uid: Long): String {
        if (TextUtils.isEmpty(sourceStr) || "null" == sourceStr) {
            if (uid == 0L) {
                return ""
            }
            return "听友${uid}"
        }
        return sourceStr.toString()
    }

    /**
     * @param strParam
     */
    fun mapStringToMap(strParam: String): HashMap<String, String> {
        var map: HashMap<String, String> = HashMap()
        if (TextUtils.isEmpty(strParam) || "{}" == strParam) return map
        var tempHashMap: HashMap<String, LinkedTreeMap<String, Double>> = HashMap()
        try {
            tempHashMap = Gson().fromJson<HashMap<String, LinkedTreeMap<String, Double>>>(
                strParam,
                tempHashMap.javaClass
            )
            tempHashMap.keys.forEachIndexed { _, key ->
                tempHashMap[key]?.apply {
                    val isPlayFinish = this["isPlayFinish"]?.toInt() ?: 0
                    val playStartTime = this["playStartTime"]?.toLong() ?: 0L
                    map[key] = Gson().toJson(PlayRecordModel(isPlayFinish, playStartTime))
                }
            }
        } catch (e: Exception) {
        }
        return map
    }
}