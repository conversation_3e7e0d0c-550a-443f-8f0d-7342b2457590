package com.ximalaya.ting.lite.main.home.manager;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendAdapter;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendNormalTitleProvider;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;
import com.ximalaya.ting.lite.main.model.album.RecommendTrackItem;

import java.util.ArrayList;
import java.util.List;

/**
 * 2
 * Created by qinhuifeng on 2021/1/27
 *
 * <AUTHOR>
 */
public class HomeFeedTrackStatusManager implements IXmPlayerStatusListener {

    public long mLastPlayTrackId = 0;
    private boolean mHasRegister = false;
    private List<Long> mPagerTrackIdList = new ArrayList<>();
    private HomeRecommendAdapter mAdapter;

    public HomeFeedTrackStatusManager(HomeRecommendAdapter adapter) {
        mAdapter = adapter;
    }

    public void register() {
        if (mHasRegister) {
            return;
        }
        mHasRegister = true;
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).addPlayerStatusListener(this);
    }

    private void unRegister() {
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).removePlayerStatusListener(this);
        mHasRegister = false;
    }

    /************生命周期方法*********/
    public void onDestroy() {
        unRegister();
    }

    /************生命周期方法*********/

    @Override
    public void onPlayStart() {
        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                PlayableModel currSound = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getCurrSound();
                if (currSound == null) {
                    return;
                }
                if (!(currSound instanceof Track)) {
                    return;
                }
                if (mPagerTrackIdList.size() == 0) {
                    loadFeedTrackIds();
                }
                long currentPlayTrackId = currSound.getDataId();
                //多线程处理，可能在contains的时候，数据源已经发生变化，造成ArrayIndexOutOfBoundsException崩溃
                //处理bugly bug
                try {
                    if (mPagerTrackIdList.contains(currentPlayTrackId)) {
                        mLastPlayTrackId = currentPlayTrackId;
                    }
                } catch (Exception ignored) {

                }
            }
        });
    }

    private void loadFeedTrackIds() {
        if (mAdapter == null) {
            return;
        }
        List<ItemModel> listData = mAdapter.getListData();
        if (listData == null || listData.size() == 0) {
            return;
        }
        List<Long> trackIdList = new ArrayList<>();
        for (int i = 0; i < listData.size(); i++) {
            ItemModel itemModel = listData.get(i);
            if (itemModel == null) {
                continue;
            }
            if (itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_TRACK_STYLE_V2 || itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_TRACK) {
                if (itemModel.getObject() instanceof RecommendItemNew) {
                    RecommendItemNew trackItem = (RecommendItemNew) itemModel.getObject();
                    if (trackItem.getItem() instanceof RecommendTrackItem) {
                        trackIdList.add(((RecommendTrackItem) trackItem.getItem()).getDataId());
                    }
                }
                if (trackIdList.size() >= HomeRecommendNormalTitleProvider.MAX_PLAY_ALL_LOAD_FEED_NUMBER) {
                    break;
                }
            }
        }
        mPagerTrackIdList.clear();
        mPagerTrackIdList.addAll(trackIdList);
    }

    @Override
    public void onPlayPause() {

    }

    @Override
    public void onPlayStop() {

    }

    @Override
    public void onSoundPlayComplete() {

    }

    @Override
    public void onSoundPrepared() {

    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {

    }

    @Override
    public void onBufferingStart() {

    }

    @Override
    public void onBufferingStop() {

    }

    @Override
    public void onBufferProgress(int percent) {

    }

    @Override
    public void onPlayProgress(int currPos, int duration) {

    }

    @Override
    public boolean onError(XmPlayerException exception) {
        return false;
    }

    public long getLastPlayTrackId() {
        return mLastPlayTrackId;
    }

    public void reset() {
        mLastPlayTrackId = -1;
        if (mPagerTrackIdList != null) {
            mPagerTrackIdList.clear();
        }
    }
}
