package com.ximalaya.ting.lite.main.base.album;

import android.content.Context;
import android.graphics.Color;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.utils.ViewFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xmly on 10/08/2018.
 *
 * <AUTHOR>
 */
public class AlbumAdapter extends BaseAlbumAdapter {

    private Context mContext;
    private boolean mIsReport;
    private boolean mIsHotWord;

    public AlbumAdapter(Context context, List<Album> listData) {
        super(context, listData);
        mContext = context;
    }

    public AlbumAdapter(Context context, List<Album> listData, boolean isReport, boolean isHotWord) {
        super(context, listData);
        mContext = context;
        mIsReport = isReport;
        mIsHotWord = isHotWord;
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_album_home_lite;
    }

    /**
     * 建立视图Holder
     */
    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void onClick(View view, final Album t, final int position, BaseViewHolder holder) {

    }

    @Override
    protected void hideAllViews(BaseAlbumAdapter.ViewHolder viewHolder) {
        super.hideAllViews(viewHolder);
        ViewHolder holder = (ViewHolder) viewHolder;
        holder.offSale.setVisibility(View.INVISIBLE);
    }

    @Override
    public void bindViewDatas(final BaseViewHolder holder, final Album o, final int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        hideAllViews((ViewHolder) holder);
        if (!(o instanceof AlbumM)) {
            return;
        }
        final AlbumM albumM = (AlbumM) o;

        if (mIsReport) {
            if (mIsHotWord) {
                // 分类页-热词内页-专辑item  控件曝光
                new XMTraceApi.Trace()
                        .setMetaId(45534)
                        .setServiceId("slipPage")
                        .put("albumId", String.valueOf(albumM.getId()))
                        .put("currPage", "Hot word inner page")
                        .put("exploreType", "Hot word inner page")
                        .createTrace();
            } else {
                // 首页内容池-查看更多内页-专辑item  控件曝光
                new XMTraceApi.Trace()
                        .setMetaId(45540)
                        .setServiceId("slipPage")
                        .put("albumId", String.valueOf(albumM.getId()))
                        .put("currPage", "See more inside pages")
                        .put("exploreType", "See more inside pages")
                        .createTrace();
            }
        }

        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, albumM);
        //添加专辑Item的ContentDescription
        if (viewHolder.root != null) {
            if (!TextUtils.isEmpty(albumM.getAlbumTitle())) {
                viewHolder.root.setContentDescription(albumM.getAlbumTitle());
            } else {
                viewHolder.root.setContentDescription("");
            }
        }
        if (AlbumTagUtil.getAlbumCoverTag((AlbumM) o) != -1) {
            viewHolder.ivTag.setImageResource(AlbumTagUtil.getAlbumCoverTag((AlbumM) o));
            viewHolder.ivTag.setVisibility(View.VISIBLE);
        } else {
            viewHolder.ivTag.setVisibility(View.INVISIBLE);
        }

        ImageManager.from(context).displayImage(viewHolder.cover, o.getLargeCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145, com.ximalaya.ting.android.host.R.drawable.host_default_album_145);
        int textSize = (int) viewHolder.title.getTextSize();
        Spanned richTitle = getRichTitle(o, context, textSize);
        viewHolder.title.setText(richTitle);
        String subTitle = getSubTitle(o, mTypeFrom);
        if (!TextUtils.isEmpty(subTitle)) {
            viewHolder.subtitle.setText(Html.fromHtml(subTitle));
        } else {
            viewHolder.subtitle.setText("");
        }
        String playCountStr = StringUtil.getFriendlyNumStr(albumM.getPlayCount());
        int drawable = R.drawable.main_ic_common_play_count;
        addAlbumInfo(context, viewHolder.layoutAlbumInfo, drawable, playCountStr, Color.parseColor("#999999"), false, false);
        addAlbumInfo(context, viewHolder.layoutAlbumInfo, R.drawable.main_ic_common_track_count, StringUtil.getFriendlyNumStr(albumM.getIncludeTrackCount()) + " 集", Color.parseColor("#999999"));
    }

    public static void addCategoryTag(String categoryTag, LinearLayout albumInfoContainer, Context context) {
        if (TextUtils.isEmpty(categoryTag)) {
            return;
        }
        if (context == null) {
            return;
        }
        if (albumInfoContainer == null) {
            return;
        }
        TextView tvCategoryTag = ViewFactory.getCategoryTag(context, categoryTag);
        albumInfoContainer.addView(tvCategoryTag);
    }

    /**
     * 根据不同的页面返回所需要的副标题
     *
     * @param album
     * @param pageType
     * @return 返回null表示没有文本副标题
     */
    public static String getSubTitle(Album album, int pageType) {
        String subTitle = "";
        if (album instanceof AlbumM) {
            AlbumM albumM = (AlbumM) album;
            subTitle = albumM.getSubTitle();
            if (TextUtils.isEmpty(subTitle)) {
                subTitle = albumM.getAlbumIntro();
            }
        }
        return subTitle;
    }

    @Nullable
    public static Spanned getRichTitle(Album data, Context context, int maxHeight) {
        if (!(data instanceof AlbumM)) {
            return null;
        }
        if (context == null) {
            return null;
        }
        AlbumM album = (AlbumM) data;
        boolean isCompleteTag = album.getSerialState() == 2 || album.isCompleted() || (album.getAttentionModel() != null && album.getAttentionModel().getSerialState() == 2);
        List<Integer> resList = new ArrayList<>();
        if (isCompleteTag) {
            // 完本标签
            resList.add(R.drawable.main_tag_complete_top_new);
        }
        String intro;
        if (resList.size() > 0) {
            intro = " " + data.getAlbumTitle();
        } else {
            intro = data.getAlbumTitle();
        }
        Spanned titleWithTag = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(context, intro, resList, resList.size(), maxHeight);
        return titleWithTag;
    }

    public static class ViewHolder extends BaseAlbumAdapter.ViewHolder {
        private ImageView offSale; //下架icon
        private ImageView ivTag;

        public ViewHolder(View convertView) {
            super(convertView);
            cover = convertView.findViewById(R.id.main_iv_album_cover);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
            border = convertView.findViewById(R.id.main_album_border);
            title = convertView.findViewById(R.id.main_tv_album_title);
            subtitle = convertView.findViewById(R.id.main_tv_album_subtitle);
            layoutAlbumInfo = convertView.findViewById(R.id.main_layout_album_info);
            offSale = convertView.findViewById(R.id.main_iv_off_sale);
        }
    }
}
