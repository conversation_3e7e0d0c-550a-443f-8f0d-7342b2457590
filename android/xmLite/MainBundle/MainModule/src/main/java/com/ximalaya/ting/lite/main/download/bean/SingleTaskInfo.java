package com.ximalaya.ting.lite.main.download.bean;

import java.io.Serializable;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   : 单线程信息
 */
public class SingleTaskInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    public int taskId;
    /**
     * 起始位置
     */
    public long beginPos;
    /**
     * 结束位置
     */
    public long endPos;
    /**
     * 已完成量
     */
    public long haveDoneSize;

    @Override
    public String toString() {
        return "SingleTaskInfo [taskId=" + taskId + ", beginPos=" + beginPos + ", endPos=" + endPos + ", haveDoneSize=" + haveDoneSize
                + "]";
    }

}
