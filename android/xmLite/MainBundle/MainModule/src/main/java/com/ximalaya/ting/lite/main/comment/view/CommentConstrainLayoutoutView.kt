package com.ximalaya.ting.lite.main.comment.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ximalaya.ting.android.main.R

/**
 *  @Author: <PERSON><PERSON><PERSON>
 *  @Mail: <EMAIL>
 *  @CreateTime: 1/5/22
 *
 *  @Description: 评论输入控件，显示默认提示语&用户已输入的评论
 */
class CommentConstrainLayoutoutView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
): ConstraintLayout(context, attrs, defStyleAttr) {

    override fun onTouchEvent(event: MotionEvent?): <PERSON><PERSON>an {
        return super.onTouchEvent(event)
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): Bo<PERSON>an {
        return true
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): <PERSON><PERSON>an {
        return true
    }
}