package com.ximalaya.ting.lite.main.home.listener;

import android.os.Bundle;
import android.view.View;

import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.album.fragment.AggregateRankFragment;
import com.ximalaya.ting.lite.main.home.fragment.CategoryMetadataFragment;
import com.ximalaya.ting.lite.main.home.fragment.HomeCategoryDetailFragment;
import com.ximalaya.ting.lite.main.home.fragment.HomeGuessYouLikeListFragment;
import com.ximalaya.ting.lite.main.home.fragment.KeywordMetadataFragment;
import com.ximalaya.ting.lite.main.home.fragment.NewContentPoolListFragment;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeGuessYouLikeViewModel;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.lite.main.model.album.MainAlbumOtherData;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankArgsModel;
import com.ximalaya.ting.lite.main.newuser.NewUserGuideFragment;

/**
 * Created by qinhuifeng on 2019-07-17
 * <p>
 * 处理标题更多的点击事件，从Fragment内部类中拆分出来
 *
 * <AUTHOR>
 */
public class NormalTitleClickListener implements View.OnClickListener {
    private MainAlbumMList titleBean;
    private BaseFragment2 mBaseFragment;

    private int from = -1;

    public NormalTitleClickListener(MainAlbumMList titleBean, BaseFragment2 baseFragment) {
        this.titleBean = titleBean;
        this.mBaseFragment = baseFragment;
    }

    public void setFrom(int from) {
        this.from = from;
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        if (titleBean == null) {
            return;
        }
        if (mBaseFragment == null) {
            return;
        }
        int moduleType = titleBean.getModuleType();
        Logger.d("首页_更多点击", "首页更多点击--楼层--" + moduleType);
        switch (moduleType) {
            case MainAlbumMList.MODULE_CALCDIMENSION:
                Bundle argumentCalcdimension = CategoryMetadataFragment.createArgumentFromSinglePage(titleBean.getCategoryId(), from);
                CategoryMetadataFragment fragmentCalcdimension = new CategoryMetadataFragment();
                fragmentCalcdimension.setArguments(argumentCalcdimension);
                mBaseFragment.startFragment(fragmentCalcdimension);
                break;
            case MainAlbumMList.MODULE_TAG:
                int categoryId = titleBean.getCategoryId();
                int keywordId = titleBean.getKeywordId();
                //展示的是title字段
                String keyName = titleBean.getTitle();
                Bundle argumentForType5 = KeywordMetadataFragment.createArgumentFromSinglePage(categoryId, keywordId, keyName, from);
                KeywordMetadataFragment fragmentForType5 = new KeywordMetadataFragment();
                fragmentForType5.setArguments(argumentForType5);
                mBaseFragment.startFragment(fragmentForType5);
                break;
            case MainAlbumMList.MODULE_CONTENT_POOL:
                if (MainAlbumMList.ITEM_DIRECTION_NEW_LISTENER.equals(titleBean.getCardClass())) {
                    NewUserGuideFragment fragment = NewUserGuideFragment.newInstance();
                    mBaseFragment.startFragment(fragment);
                    return;
                }
                int poolId = 0;
                MainAlbumOtherData mainAlbumOtherData = titleBean.getMainAlbumOtherData();
                if (mainAlbumOtherData != null) {
                    poolId = mainAlbumOtherData.poolId;
                }
                NewContentPoolListFragment contentPoolListFragment = NewContentPoolListFragment.newInstance(titleBean.getTitle(), poolId, from);
                mBaseFragment.startFragment(contentPoolListFragment);
                break;
            case MainAlbumMList.MODULE_PERSONAL_RECOMMEND:
            case MainAlbumMList.MODULE_VIP_ALBUM_CARD:
            case MainAlbumMList.MODULE_INTEREST_RECOMMEND:
                //当前是猜你喜欢类型，更多要跳转猜你喜欢
                HomeGuessYouLikeViewModel viewModel = new HomeGuessYouLikeViewModel();
                viewModel.title = titleBean.getTitle();
                viewModel.keywordId = titleBean.getKeywordId();
                viewModel.categoryId = titleBean.getCategoryId();
                viewModel.moduleType = titleBean.getModuleType();
                viewModel.personalRecSubType = titleBean.getPersonalRecSubType();
                viewModel.moduleId = titleBean.getModuleId() + "";
                viewModel.channelId = titleBean.getChannelId();
                if (moduleType == MainAlbumMList.MODULE_INTEREST_RECOMMEND) {
                    viewModel.title = "";
                    viewModel.subTitle = titleBean.getSubtitle();
                    viewModel.interestId = titleBean.interestId;
                }
                Bundle argumentGuessYouLike = HomeGuessYouLikeListFragment.createArgumentFromSinglePage(viewModel, from);
                HomeGuessYouLikeListFragment fragmentGuessYouLike = new HomeGuessYouLikeListFragment();
                fragmentGuessYouLike.setArguments(argumentGuessYouLike);
                mBaseFragment.startFragment(fragmentGuessYouLike);
                break;
            case MainAlbumMList.MODULE_GUESSLIKE:
            case MainAlbumMList.MODULE_KEYWORD:
            case MainAlbumMList.MODULE_METADATA:
            case MainAlbumMList.MODULE_RECOMMEND_YOU:
                Bundle argumentFromSinglePage = HomeCategoryDetailFragment.createArgumentFromSinglePage(titleBean.getCategoryId(), titleBean.getKeywordId(), titleBean.getTitle(), from);
                HomeCategoryDetailFragment fragment = new HomeCategoryDetailFragment();
                fragment.setArguments(argumentFromSinglePage);
                mBaseFragment.startFragment(fragment);
                break;
            case MainAlbumMList.MODULE_CATEGORY_RANK:
                AggregateRankFragment aggregateRankFragment = new AggregateRankFragment();
                AggregateRankArgsModel aggregateRankArgsModel = new AggregateRankArgsModel();
                aggregateRankArgsModel.selectRankClusterId = titleBean.getRankClusterId();
                Bundle bundle = AggregateRankFragment.newArgument(aggregateRankArgsModel);
                aggregateRankFragment.setArguments(bundle);
                mBaseFragment.startFragment(aggregateRankFragment);
                break;
            default:
                break;
        }
    }
}