package com.ximalaya.ting.lite.main.download;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import java.util.List;

/**
 * Created by xmly on 10/08/2018.
 *
 * <AUTHOR>
 */
public class SoundSortAdapter extends HolderAdapter<Track> {
    private boolean mShowSubtitle;

    public SoundSortAdapter(Context context, List<Track> listData, boolean showSubtitle) {
        super(context, listData);

        mShowSubtitle = showSubtitle;
    }

    @Override
    public void onClick(View view, Track t, int position, BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        if (mShowSubtitle) {
            return R.layout.main_item_sort_sounds;
        } else {
            return R.layout.main_item_sort_sounds_without_subtitle;
        }
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        ViewHolder vh = new ViewHolder(convertView);
        return vh;
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, Track t, int position) {
        ViewHolder vh = (ViewHolder) holder;

        String coverUrl = TextUtils.isEmpty(t.getCoverUrlMiddle()) ? t
                .getCoverUrlSmall() : t.getCoverUrlMiddle();
        ImageManager.from(context).displayImage(vh.ivCover, coverUrl,
                com.ximalaya.ting.android.host.R.drawable.host_default_album_145);

        vh.tvTitle.setText(t.getTrackTitle());
        vh.tvTotalTime.setText(StringUtil.toTime(t.getDuration()));
        String fileSize = StringUtil.toMBFormatString(t.getDownloadSize()) + "M";
        vh.tvFileSize.setText(fileSize);

        if (mShowSubtitle && t.getAlbum() != null) {
            vh.tvSubtitle.setText(t.getAlbum().getAlbumTitle());
        }
    }

    private class ViewHolder extends BaseViewHolder {
        TextView tvTitle;
        TextView tvSubtitle;
        TextView tvTotalTime;
        TextView tvFileSize;
        ImageView ivCover;

        ViewHolder(View convertView) {
            tvTitle = (TextView) convertView.findViewById(R.id.main_sort_sound_title);
            tvSubtitle = (TextView) convertView.findViewById(R.id.main_tv_subtitle);
            tvTotalTime = (TextView) convertView.findViewById(R.id.main_tv_total_time);
            tvFileSize = (TextView) convertView.findViewById(R.id.main_tv_file_size);
            ivCover = (ImageView) convertView.findViewById(R.id.main_iv_cover);
        }
    }
}
