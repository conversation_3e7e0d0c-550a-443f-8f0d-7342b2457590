package com.ximalaya.ting.lite.main.home.view;

import android.app.Activity;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.host.model.album.DislikeReason;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2017/9/28.
 */

public class DislikeFeedbackWindowNew extends PopupWindow implements View.OnClickListener, View.OnKeyListener {
    private List<TextView> mTvDislikeReasons = new ArrayList<>();
    private @NonNull
    List<DislikeReason> mDislikeReasons;
    private LayoutInflater mLayoutInflater;
    private LinearLayout mLlContainer;
    private Context mContext;
    private ImageView mIvTopTail;
    private ImageView mIvBottomTail;
    public int mTopTailCorrectPadding;  //顶部小尾巴距离目标view底部的距离
    public int mBottomTailCorrectPadding;  //底部小尾巴距离目标view顶部的距离

    public void setFragment(BaseFragment2 fragment) {
        this.fragment = fragment;
    }

    private BaseFragment2 fragment;

    public DislikeFeedbackWindowNew(Context context, @NonNull List<DislikeReason> dislikeReasons) {
        super(context);

        mContext = context;
        mDislikeReasons = dislikeReasons;

        mLayoutInflater = LayoutInflater.from(context);
        View contentView = mLayoutInflater.inflate(R.layout.main_view_dislike_feedback_new, null);
        contentView.findViewById(R.id.main_tv_dislike_reason_1).setSelected(true);

        AutoTraceHelper.bindData(contentView, AutoTraceHelper.MODULE_DEFAULT, "");
        mIvTopTail = contentView.findViewById(R.id.main_iv_top_tail);
        mIvBottomTail = contentView.findViewById(R.id.main_iv_bottom_tail);
        contentView.setOnClickListener(this);
        contentView.setFocusable(true);
        contentView.setId(R.id.main_content);
        contentView.setFocusableInTouchMode(true);
        contentView.setOnKeyListener(this);
        mLlContainer = contentView.findViewById(R.id.main_ll_container);
        TextView tvDislikeReason1 = contentView.findViewById(R.id.main_tv_dislike_reason_1);
        tvDislikeReason1.setOnClickListener(this);
        tvDislikeReason1.setSelected(true);
        AutoTraceHelper.bindData(tvDislikeReason1, AutoTraceHelper.MODULE_DEFAULT, "");
        mTvDislikeReasons.add(tvDislikeReason1);

        if (dislikeReasons.size() > 0) {
            tvDislikeReason1.setText(dislikeReasons.get(0).name);
            tvDislikeReason1.setTag(dislikeReasons.get(0));
        }

        inflateDislikeReason();

        setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        setBackgroundDrawable(null);
        setTouchable(true);
        setFocusable(true);
        setClippingEnabled(false);
        setContentView(contentView);
    }

    private void inflateDislikeReason() {


        for (int i = 1; i < mDislikeReasons.size(); i += 2) {
            LinearLayout linearLayout =
                    (LinearLayout) mLayoutInflater.inflate(R.layout.main_view_dislike_feedback_row_item_new,
                            mLlContainer, false);
            mLlContainer.addView(linearLayout);

            TextView tvReason1 = linearLayout.findViewById(R.id.main_tv_dislike_reason_1);
            tvReason1.setOnClickListener(this);
            AutoTraceHelper.bindData(tvReason1, AutoTraceHelper.MODULE_DEFAULT, "");

            mTvDislikeReasons.add(tvReason1);

            TextView tvReason2 = linearLayout.findViewById(R.id.main_tv_dislike_reason_2);
            tvReason2.setOnClickListener(this);
            AutoTraceHelper.bindData(tvReason2, AutoTraceHelper.MODULE_DEFAULT, "");

            mTvDislikeReasons.add(tvReason2);

            tvReason1.setText(mDislikeReasons.get(i).name);
            tvReason1.setTag(mDislikeReasons.get(i));

            if (i + 1 < mDislikeReasons.size()) {
                tvReason2.setText(mDislikeReasons.get(i + 1).name);
                tvReason2.setTag(mDislikeReasons.get(i + 1));
            } else {
                tvReason2.setVisibility(View.INVISIBLE);
            }
        }
    }

    @Override
    public void onClick(View v) {
        if (OneClickHelper.getInstance().onClick(v)) {
            if (v.getId() == R.id.main_tv_dislike_reason_1 || v.getId() == R.id.main_tv_dislike_reason_2) {
                for (TextView textView : mTvDislikeReasons) {
                    if (v == textView) {
                        textView.setSelected(true);
                        mUserSelectedDislikeReason = (DislikeReason) textView.getTag();
                    } else {
                        textView.setSelected(false);
                    }
                }
                dismiss();
            } else if (v.getId() == R.id.main_content) {
                dismiss();
            }
        }
    }

    public void customShowAtLocation(Activity activity, View anchor) {
        int[] position = new int[2];
        anchor.getLocationOnScreen(position);
        int targetX = position[0] + anchor.getMeasuredWidth() / 2;

        int leftPadding;
        if (targetX > BaseUtil.dp2px(mContext, 310)) {
            leftPadding = targetX - BaseUtil.dp2px(mContext, 310);
        } else {
            leftPadding = 0;
        }

        //模拟测量，获取content view需要的高度
        int width = View.MeasureSpec.makeMeasureSpec(0,
                View.MeasureSpec.UNSPECIFIED);
        int height = View.MeasureSpec.makeMeasureSpec(0,
                View.MeasureSpec.UNSPECIFIED);
        getContentView().measure(width, height);
        int neededHeight = getContentView().getMeasuredHeight();

        int statusBarHeight = getStatusBarHeight(activity);
        int screenHeight = activity.getResources().getDisplayMetrics().heightPixels;
        int topPadding;
        if (position[1] + anchor.getMeasuredHeight() + statusBarHeight + neededHeight > screenHeight) {
            topPadding = position[1] - statusBarHeight - neededHeight - mBottomTailCorrectPadding;
            mIvTopTail.setVisibility(View.INVISIBLE);
        } else {
            topPadding = position[1] + anchor.getMeasuredHeight() - statusBarHeight + mTopTailCorrectPadding;
            mIvBottomTail.setVisibility(View.INVISIBLE);
        }

        getContentView().setPadding(leftPadding, topPadding, 0, 0);

        getContentView().addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {  //fix window position incorrect problem in some devices
                View contentView = getContentView();
                contentView.removeOnAttachStateChangeListener(this);
                int[] position = new int[2];
                contentView.getLocationOnScreen(position);
                if (position[1] == 0) {
                    int topPadding = contentView.getPaddingTop() + BaseUtil.getStatusBarHeight(mContext);
                    contentView.setPadding(contentView.getPaddingLeft(),
                            topPadding,
                            contentView.getPaddingRight(),
                            contentView.getPaddingBottom());
                }
            }

            @Override
            public void onViewDetachedFromWindow(View v) {

            }
        });

        showAtLocation(anchor, Gravity.NO_GRAVITY, 0, 0);
    }

    private int getStatusBarHeight(Activity activity) {
        return BaseUtil.getStatusBarHeight(activity);
    }


    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            dismiss();
            return true;
        }
        return false;
    }

    private DislikeReason mUserSelectedDislikeReason;

    public @Nullable
    DislikeReason getSelectedDislikeReason() {
        return mUserSelectedDislikeReason;
    }

}
