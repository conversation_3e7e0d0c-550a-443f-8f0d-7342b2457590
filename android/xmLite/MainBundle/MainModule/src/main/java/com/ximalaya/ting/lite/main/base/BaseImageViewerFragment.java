package com.ximalaya.ting.lite.main.base;

import android.app.Activity;

import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.view.ImageViewer;
import com.ximalaya.ting.android.host.view.other.RichWebView;

import java.util.List;

/**
 * <AUTHOR> on 2017/9/27.
 */

public abstract class BaseImageViewerFragment extends BaseFragment2 implements RichWebView.IOnImageClickListener {
    private ImageViewer mImageViewer;

    public BaseImageViewerFragment() {
        super();
    }

    public BaseImageViewerFragment(boolean canSiled, SlideView.IOnFinishListener onFinishListener) {
        super(canSiled, onFinishListener);
    }

    public BaseImageViewerFragment(boolean canSiled, int slideViewContentViewLayoutType, SlideView.IOnFinishListener onFinishListener) {
        super(canSiled, slideViewContentViewLayoutType, onFinishListener);
    }


    @Override
    public final void onClick(List<ImageViewer.ImageUrl> list, int i) {
        Activity activity = getActivity();
        if(activity == null){
            return;
        }

        if (mImageViewer == null) {
            mImageViewer = new ImageViewer(activity);
        }
        mImageViewer.setImageUrls(list);
        mImageViewer.show(i, getView());
    }
}
