package com.ximalaya.ting.lite.main.download.bean;

import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.download.engine.SingleTaskDownloadEngine;
import com.ximalaya.ting.lite.main.download.inter.DownloadListener;
import com.ximalaya.ting.lite.main.download.inter.IDownloadEngine;
import com.ximalaya.ting.lite.main.download.inter.IEngineObserver;
import com.ximalaya.ting.lite.main.download.utils.TaskCode;
import com.ximalaya.ting.lite.main.download.utils.TaskUtil;

/**
 * <AUTHOR> feiwen
 * date   : 2019-06-26
 * desc   :
 * 核心的下载功能，主要有两点功能：
 * 1.选择适合的下载引擎
 * 2.下载引擎的观察者
 */
public class CommonTask extends Task<TaskInfo, TaskState> {

    private static final String tag = CommonTask.class.getSimpleName();
    private static final long FLASH_INTERVAL = 1000;
    private long pre_flash_time = 0;
    private TaskInfo mInfo;
    private TaskState mState;
    private boolean firstDownload = true;
    private DownloadListener mDownloadListener;
    /**
     * 标志位，标识是否取消下载操作，如果正在下载的话只会取消正在下载的那个时候的操作
     */
    private boolean requestCancel = false;
    /**
     * 标志位，表示是否为删除操作，如果为删除操作，将不发出取消操作的通知
     */
    private boolean requestDelete = false;

    private IDownloadEngine mEngine = SingleTaskDownloadEngine.getInstance();

    private IEngineObserver engineObserver = new IEngineObserver() {

        @Override
        public void onStartConnectServer() {
            postDownloadStart();
        }

        @Override
        public void onDownloading(long total, long curr, double speed) {
            postDownloading(total, curr, speed);
        }

        @Override
        public void onError(int errorCode) {
            postError(errorCode);
        }

        @Override
        public void onPaused() {
            postPause();
        }

        @Override
        public void onSuccess() {
            postDone();
        }

        @Override
        public void updateTaskInfo() {
        }
    };

    public CommonTask(TaskInfo info, TaskState state, DownloadListener downloadTaskCallback) {
        mInfo = info;
        mState = state;
        mDownloadListener = downloadTaskCallback;
    }


    private void resetData() {
        requestCancel = false;
        requestDelete = false;
    }

    /**
     * 停止任务
     */
    @Override
    public int stop() {
        if (mEngine != null && mEngine.busy()) {
            Logger.d(tag, "请求停止下载：" + mInfo.toString());
            mEngine.stop();
        } else {
            Logger.d(tag, "任务未开始下载");
            requestCancel = true;
            postPause();
        }

        return TaskCode.SUCCESS;
    }

    @Override
    public void run() {
        start();
    }

    public void start() {
        try {
            startDownload();
        } catch (Exception e) {
            if (!requestCancel) {
                e.printStackTrace();
                Logger.e(tag, e.toString());
                mState.setErrorCode(TaskUtil.getErrorCode(e));
                mState.updateState(TaskState.STATE_ERROR, this);
            }
        } finally {
            requestCancel = false;
        }
    }

    private void startDownload() throws Exception {
        updateState(TaskState.STATE_CONNECTING);
        mInfo.setStartTime(System.currentTimeMillis());
        if (mInfo.existDoneFile()) {
            postDone();
            return;
        }
        // 先恢复初始化
        mEngine.reset();
        mEngine.setData(mInfo, engineObserver);
        // 启动下载
        mEngine.start();
    }

    @Override
    public int delete(boolean deleteFile) {
        requestDelete = true;
        // 停止未完成的任务
        if (mEngine != null && mEngine.busy()) {
            Logger.d(tag, "请求停止下载：" + mInfo.toString());
            mEngine.stop();
        }
        final TaskInfo info = mInfo;
        // 清楚相关数据
        TaskUtil.clearTaskAllFile(info, deleteFile);
        if (mEngine != null) {
            mEngine.delete(deleteFile);
        }
        // 通知完成
        mState.updateState(TaskState.STATE_DELETE, this);
        return TaskCode.SUCCESS;
    }

    private void postDownloadStart() {
        updateState(TaskState.STATE_CONNECTING);
        if(mDownloadListener != null) {
            mDownloadListener.onTaskStart(mInfo);
        }
    }

    /**
     * 提交下载中操作
     */
    private void postDownloading(long total, long curr, double speed) {
        mState.setSpeed(speed);
        mInfo.setCurrSize(curr);
        mState.setCurrSize(curr);
        if (firstDownload) {
            mInfo.setSize(total);
            mState.setTotalSize(total);
            firstDownload = false;
        }
        updateProgress(curr);
        if(mDownloadListener != null) {
            mDownloadListener.onTaskProgress(mInfo, (int) (curr * 100 / total));
        }
    }

    /**
     * 提交已完成操作
     */
    private void postDone() {
        mInfo.setDownloadState(TaskInfo.DOWN_STATE_DONE);
        mInfo.setFinishTime(System.currentTimeMillis());
        updateState(TaskState.STATE_DONE);
        Logger.d(tag, "CommonTask 完成下载任务：" + mInfo.getFileName() + "， 文件大小 = " + mInfo.getSize() + "， 耗时 = " + (mInfo.getFinishTime() - mInfo.getStartTime()));
        if(mDownloadListener != null) {
            mDownloadListener.onTaskSuccess(mInfo);
        }
    }

    /**
     * 提交错误
     */
    private void postError(int code) {
        mState.setErrorCode(code);
        updateState(TaskState.STATE_ERROR);
        if(mDownloadListener != null) {
            mDownloadListener.onTaskFailed(mInfo);
        }
    }

    /**
     * 提交暂停操作
     */
    private void postPause() {
        // 删除操作会先暂停，避免消息混淆
        if (!this.requestDelete) {
            updateState(TaskState.STATE_PAUSE);
        }
    }

    /**
     * 更新下载中的任务信息
     */
    private void updateProgress(long value) {
        boolean notify = notify(System.currentTimeMillis());
        boolean done = mInfo.getCurrSize() == mInfo.getSize();
        if (notify || done) {
            updateState(TaskState.STATE_DOWNLOADING);
        }
    }

    private boolean notify(long currt) {
        if (pre_flash_time == 0) {
            pre_flash_time = currt;
            return true;
        }
        long interval = currt - pre_flash_time;
        boolean ret = interval > FLASH_INTERVAL;
        if (ret) {
            pre_flash_time = currt;
        }
        return ret;
    }

    /**
     * 更新状态
     */
    private void updateState(int state) {
        mState.updateState(state, this);
    }

    @Override
    public void reset() {
        mState.setState(TaskState.STATE_PENDING);
        mState.setErrorCode(TaskCode.SUCCESS);
        mInfo.setDownloadState(TaskInfo.DOWN_STATE_INVALID);
    }

    @Override
    public void release() {
        mDownloadListener = null;
    }

    @Override
    public TaskInfo getInfo() {
        return this.mInfo;
    }

    @Override
    public TaskState getState() {
        return this.mState;
    }

    @Override
    public String toString() {
        return mInfo != null ? mInfo.toString() : super.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (o == null) {
            return false;
        }
        Task task = (Task) o;
        return mInfo.equals(task.getInfo());
    }


}
