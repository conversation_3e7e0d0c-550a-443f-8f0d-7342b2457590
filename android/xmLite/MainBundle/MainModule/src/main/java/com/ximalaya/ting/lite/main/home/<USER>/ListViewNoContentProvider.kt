package com.ximalaya.ting.lite.main.home.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.model.ListViewNoContentModel
import com.ximalaya.ting.lite.main.vip.listener.ListViewNoContentRefreshListener
import kotlinx.android.synthetic.main.main_item_list_view_no_content.view.*

/**
 * Created by duming<PERSON> on 2020/5/13
 *
 * Desc: ListView中的楼层，显示没用内容的空界面
 */

class ListViewNoContentProvider @JvmOverloads constructor(
        val fragment: BaseFragment2,
        val refreshListenerListView: ListViewNoContentRefreshListener? = null
) : IMulitViewTypeViewAndData<ListViewNoContentProvider.Holder, ListViewNoContentModel> {

    private val TAG: String? = "ListViewNoConentProvide"

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_list_view_no_content, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView)
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<ListViewNoContentModel>, convertView: View, position: Int) {
        Logger.d(TAG, "bindViewDatas")
        val model = t.getObject()

        with(holder.rootView) {

            //这个高度是VipTabFragment顶部的变色高度+悬浮的vip热词单行高度
            val height = BaseUtil.getScreenHeight(context) - BaseUtil.dp2px(context, 163f)
            val layoutParams = llNoContent.layoutParams as FrameLayout.LayoutParams
            layoutParams.height = height

            model.resId?.let {
                ivNoContent.background = resources.getDrawable(it)
            }
            model?.title?.let {
                tvNoContentTitle.text = it
            }
            model?.refreshText?.let {
                tvRefreshContent.text = it
            }

            if (model.showRefreshButton) {
                tvRefreshContent.visibility = View.VISIBLE
                tvRefreshContent.setOnClickListener {
                    Logger.d(TAG, "No content request refresh")
                    refreshListenerListView?.onRefresh()
                }
            } else {
                tvRefreshContent.setOnClickListener(null)
                tvRefreshContent.visibility = View.GONE
            }
        }
    }

    class Holder(var rootView: View) : BaseViewHolder()

}