package com.ximalaya.ting.lite.main.home.adapter

import androidx.core.content.ContextCompat
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.model.album.TitleModule
import kotlinx.android.synthetic.main.main_view_list_header_v2.view.*

/**
 * Created by du<PERSON><PERSON> on 2020/7/3
 *
 * Desc:
 */
class HomeRecommendNormalTitleProviderV2 : IMulitViewTypeViewAndData<HomeRecommendNormalTitleProviderV2.NormalTitleHolder, TitleModule> {

    private val TAG: String = "HomeRecommendNormalTitl"

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_view_list_header_v2, parent, false)
    }

    override fun bindViewDatas(holder: NormalTitleHolder, t: ItemModel<TitleModule>, convertView: View, position: Int) {
        val titleModule = t.getObject()
        if (titleModule != null && titleModule.titleBean != null) {
            with(holder.view) {
                val titleBean = titleModule.titleBean
                mainBtnMore.setOnClickListener(titleModule.moreClickListener)
                AutoTraceHelper.bindData(mainBtnMore, titleBean.moduleType.toString(), titleBean)
                mainTvTitle.text = titleBean.title
                mainBtnMore.visibility = if (titleBean.isHasMore) View.VISIBLE else View.GONE

                val subTitle = titleBean.subtitle
                if (subTitle != null && subTitle.isNotEmpty()) {
                    mainTvSubtitle.visibility = View.VISIBLE
                    val length = subTitle.length
                    val finalSubTitle = context.getString(R.string.main_recommend_base_your_interest, subTitle)
                    val startIndex = finalSubTitle.indexOf(subTitle)
                    val spanString = SpannableString(finalSubTitle)
                    //设置字体颜色
                    val colorSpan = ForegroundColorSpan(ContextCompat.getColor(context, R.color.main_color_ae8559))
                    spanString.setSpan(colorSpan, startIndex, startIndex + length, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
                    mainTvSubtitle.text = spanString
                } else {
                    mainTvSubtitle.text = ""
                    mainTvSubtitle.visibility = View.GONE
                }
            }
        }
    }

    override fun buildHolder(convertView: View): NormalTitleHolder {
        return NormalTitleHolder(convertView)
    }

    class NormalTitleHolder(val view: View) : BaseViewHolder()
}