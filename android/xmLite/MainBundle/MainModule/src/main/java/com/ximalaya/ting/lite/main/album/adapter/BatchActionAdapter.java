package com.ximalaya.ting.lite.main.album.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.album.fragment.BatchActionFragment;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量下载或购买适配器
 *
 * <AUTHOR>
 */
public class BatchActionAdapter extends HolderAdapter<Track> {

    private final int type;

    public BatchActionAdapter(Context context, List<Track> listData, int type) {
        super(context, listData);
        this.type = type;
    }

    @Override
    public void onClick(View view, Track t, int position, BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_album_download_batch;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        ViewHolder vhHolder = new ViewHolder();
        vhHolder.tvSize = (TextView) convertView.findViewById(R.id.main_size);
        vhHolder.tvTitle = (TextView) convertView.findViewById(R.id.main_item_album_down_title);
        vhHolder.cbTrack = (CheckBox) convertView.findViewById(R.id.main_checkbox);
        return vhHolder;
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, Track t, int position) {
        ViewHolder vh = (ViewHolder) holder;
        boolean isChecked = t.getExtra();
        vh.tvTitle.setText(t.getTrackTitle());
        vh.cbTrack.setChecked(isChecked);
        if (type == BatchActionFragment.ACTION_DOWNLOAD) {
            bindDownloadData(vh, t);
        }
    }

    private void bindDownloadData(ViewHolder vh, Track t) {
        vh.tvSize.setText(StringUtil.getFriendlyFileSize(t.getDownloadSize()));
        if (RouteServiceUtil.getDownloadService().isAddToDownload(t)) {
            vh.tvTitle.setTextColor(Color.parseColor("#b2bac8"));
            vh.cbTrack.setButtonDrawable(R.drawable.host_checkbox_disable);
        } else {
            vh.tvTitle.setTextColor(Color.parseColor("#394257"));
            vh.cbTrack.setButtonDrawable(R.drawable.host_checkbox_bg);
        }
    }

    public void checkAll() {
        if (getCount() <= 0) {
            return;
        }
        for (Track track : listData) {
            if (type == BatchActionFragment.ACTION_DOWNLOAD) {
                if (!RouteServiceUtil.getDownloadService().isAddToDownload(track)) {
                    track.setExtra(true);
                }
            }
        }

        notifyDataSetChanged();
    }

    public void uncheckAll() {
        if (getCount() <= 0) {
            return;
        }
        for (Track track : listData) {
            if (type == BatchActionFragment.ACTION_DOWNLOAD) {
                if (!RouteServiceUtil.getDownloadService().isAddToDownload(track)) {
                    track.setExtra(false);
                }
            }
        }

        notifyDataSetChanged();
    }

    /**
     * 判斷是否有一個被選中
     *
     * @return
     */
    public boolean isOneChecked() {
        if (getCount() > 0) {
            for (Track track : listData) {
                if (!RouteServiceUtil.getDownloadService().isAddToDownload(track)
                        && track.getExtra()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断列表是否全选
     *
     * @return
     */
    public boolean isAllChecked() {
        for (Track track : listData) {
            if (type == BatchActionFragment.ACTION_DOWNLOAD) {
                if (!RouteServiceUtil.getDownloadService().isAddToDownload(track) && !track.getExtra()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取待下载的列表
     *
     * @return
     */
    public List<Track> getCheckedTracks() {
        List<Track> checkedList = null;
        if (getCount() > 0) {
            checkedList = new ArrayList<>();
            for (Track track : listData) {
                if (track.getExtra()) {
                    checkedList.add(track instanceof TrackM ? TrackM
                            .convertToDownloadTrack(track) : track);
                }
            }
        }
        return checkedList;
    }

    private class ViewHolder extends BaseViewHolder {
        TextView tvTitle;
        TextView tvSize;
        CheckBox cbTrack;
    }

}
