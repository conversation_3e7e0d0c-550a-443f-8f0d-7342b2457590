package com.ximalaya.ting.lite.main.earn;

import android.os.Bundle;

import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.listenertask.callback.JssdkFuliRewardCallback;
import com.ximalaya.ting.android.host.model.ad.AdWrapper;
import com.ximalaya.ting.android.host.model.earn.FuliBallDialogDataModel;
import com.ximalaya.ting.android.host.model.earn.FuliBallType;
import com.ximalaya.ting.android.host.util.constant.AdConstants;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.earn.dialog.FuliCoinBallDialogFragment;

/**
 * Created by qinhuifeng on 2020/7/21
 *
 * <AUTHOR>
 */
public class FullCoinBallDialogManager {

    public static int V1 = 1;
    public static int V2 = 2;

    public static BaseDialogFragment newFuliCoinBallDialogFragment(FuliBallDialogDataModel dialogDataModel, AdWrapper ttFeedAd, JssdkFuliRewardCallback callback) {
        //使用默认老的样式
        Bundle argument = FuliCoinBallDialogFragment.newArgument(dialogDataModel);
        FuliCoinBallDialogFragment fragment = new FuliCoinBallDialogFragment();
        fragment.updateAdFeedAdBottom(ttFeedAd);
        fragment.setJssdkFuliRewardCallback(callback);
        fragment.setArguments(argument);
        return fragment;
    }

    /**
     * 曝光埋点
     */
    public static void trackShow(int version, FuliBallDialogDataModel mDataModel, String adCode) {
        if (mDataModel == null) {
            return;
        }
        //增加其他新的埋点注意增加该参数
        String popUi = "1";
        //新样式埋点是2
        if (version == V2) {
            popUi = "2";
        }
        if (mDataModel.awardType == FuliBallType.AWARD_TYPE_DOUBLE) {
            //翻倍弹框
            switch (mDataModel.ballType) {
                case FuliBallType.BALL_TYPE_LISTEN:
                    //收听奖励气泡，不可翻倍

                    break;
                case FuliBallType.BALL_TYPE_LUCK:
                    //幸运奖励气泡，翻倍弹框
                    new XMTraceApi.Trace()
                            .setMetaId(10034)
                            .setServiceId("dialogView")
                            .put("coincount", mDataModel.amount + "")
                            .put("adId", adCode)
                            .put("dialogType", "doubleAward")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_COIN:
                    //收听阶段金币奖励，不可翻倍

                    break;
                case FuliBallType.BALL_TYPE_SGIN:
                    //签到翻倍奖励
                    new XMTraceApi.Trace()
                            .setMetaId(10135)
                            .setServiceId("dialogView")
                            .put("coincount", mDataModel.amount + "")
                            .put("adId", adCode)
                            .put("dialogType", "doubleSignAward")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_HOME_VIDEO:
                    new XMTraceApi.Trace()
                            .setMetaId(12462)
                            .setServiceId("dialogView")
                            .put("coinCount", "" + mDataModel.amount)
                            .put("adId", adCode)
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                default:
                    break;
            }
        } else {
            //领取弹框
            switch (mDataModel.ballType) {
                case FuliBallType.BALL_TYPE_LISTEN:
                    //收听奖励气泡
                    //是否是超限后观看视频弹框
                    boolean isPlayVideoFinish = false;
                    if (mDataModel.fulliCoinRewardReqModel != null && mDataModel.fulliCoinRewardReqModel.ballAward != null) {
                        isPlayVideoFinish = mDataModel.fulliCoinRewardReqModel.ballAward.isAdListeLimitFinish;
                    }
                    new XMTraceApi.Trace()
                            .setMetaId(10037)
                            .setServiceId("dialogView")
                            .put("coincount", mDataModel.amount + "")
                            .put("adId", adCode)
                            .put("dialogType", "listenAward")
                            .put("dialogSeq", isPlayVideoFinish ? "2" : "1")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_LUCK:
                    //幸运奖励气泡
                    new XMTraceApi.Trace()
                            .setMetaId(10030)
                            .setServiceId("dialogView")
                            .put("coincount", mDataModel.amount + "")
                            .put("adId", adCode)
                            .put("dialogType", "LuckAward")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_BAOXIANG:
                    //开宝箱奖励
                    new XMTraceApi.Trace()
                            .setMetaId(12407)
                            .setServiceId("dialogView")
                            .put("dialogTitle", "金币奖励")
                            .put("coinCount", mDataModel.amount + "")
                            .put("adId", adCode)
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_COIN:
                    //收听阶段金币奖励
                    new XMTraceApi.Trace()
                            .setMetaId(10044)
                            .setServiceId("dialogView")
                            .put("coincount", mDataModel.amount + "")
                            .put("adId", adCode)
                            .put("dialogType", "getAward")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_COMMON_DIALOG:
                    //福利页通用弹框
                    new XMTraceApi.Trace()
                            .setMetaId(10739)
                            .setServiceId("dialogView")
                            .put("coincount", mDataModel.amount + "")
                            .put("adId", adCode)
                            .put("dialogType", "commonAdPopup")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_HOME_HOUR:
                    //首页整点奖励弹框
                    new XMTraceApi.Trace()
                            .setMetaId(11698)
                            .setServiceId("dialogView")
                            .put("abTest", "testA")
                            .put("adId", adCode)
                            .put("coincount", mDataModel.amount + "")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_HOME_VIDEO:
                    new XMTraceApi.Trace()
                            .setMetaId(12461)
                            .setServiceId("dialogView")
                            .put("coinCount", "" + mDataModel.amount)
                            .put("adId", adCode)
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_SUPPER_COMMON:
                    new XMTraceApi.Trace()
                            .setMetaId(28289)
                            .setServiceId("slipPage")
                            .put("positionName", mDataModel.adPositionName)
                            .put("coinCount", "" + mDataModel.amount)
                            .put("popUi", popUi)
                            .put("taskId", AdConstants.NEW_COIN_ACT_TASK_ID)
                            .createTrace();
                    break;
                default:
                    new XMTraceApi.Trace()
                            .setMetaId(12930)
                            .setServiceId("dialogView")
                            .put("positonName", mDataModel.adPositionName)
                            .put("coinCount", mDataModel.amount + "")
                            .put("slotId", mDataModel.adCSJCode)
                            .put("popUi", popUi)
                            .createTrace();
                    break;
            }
        }
    }

    /**
     * 点击翻倍更新埋点
     */
    public static void trackClickMore(int version, FuliBallDialogDataModel mDataModel, String adCode) {
        if (mDataModel == null) {
            return;
        }
        //增加其他新的埋点注意增加该参数
        String popUi = "1";
        //新样式埋点是2
        if (version == V2) {
            popUi = "2";
        }
        switch (mDataModel.ballType) {
            case FuliBallType.BALL_TYPE_LISTEN:
                //收听奖励气泡
                break;
            case FuliBallType.BALL_TYPE_LUCK:
                //幸运奖励气泡
                new XMTraceApi.Trace()
                        .setMetaId(10031)
                        .setServiceId("dialogClick")
                        .put("adId", adCode)
                        .put("item", "金币翻倍")
                        .put("dialogType", "LuckAward")
                        .put("popUi", popUi)
                        .createTrace();
                break;
            case FuliBallType.BALL_TYPE_COIN:
                //收听阶段金币奖励
                break;
            case FuliBallType.BALL_TYPE_HOME_VIDEO:
                new XMTraceApi.Trace()
                        .setMetaId(12464)
                        .setServiceId("dialogClick")
                        .put("item", "金币翻倍")
                        .put("coinCount", "" + mDataModel.amount)
                        .put("adId", adCode)
                        .put("popUi", popUi)
                        .createTrace();
                break;
            default:
                //通用的翻倍点击埋点
                new XMTraceApi.Trace()
                        .setMetaId(12946)
                        .setServiceId("dialogClick")
                        .put("positonName", mDataModel.adPositionName)
                        .put("coinCount", mDataModel.amount + "")
                        .put("slotId", mDataModel.adCSJCode)
                        .put("item", "翻倍")
                        .put("popUi", popUi)
                        .createTrace();
                break;
        }
    }

    /**
     * 关闭按钮埋点
     */
    public static void trackClickClose(int version, FuliBallDialogDataModel mDataModel, String adCode) {
        if (mDataModel == null) {
            return;
        }
        //增加其他新的埋点注意增加该参数
        String popUi = "1";
        //新样式埋点是2
        if (version == V2) {
            popUi = "2";
        }
        if (mDataModel.awardType == FuliBallType.AWARD_TYPE_DOUBLE) {
            switch (mDataModel.ballType) {
                case FuliBallType.BALL_TYPE_LISTEN:
                    //收听奖励气泡
                    break;
                case FuliBallType.BALL_TYPE_LUCK:
                    //幸运奖励气泡
                    new XMTraceApi.Trace()
                            .setMetaId(10035)
                            .setServiceId("dialogClick")
                            .put("adId", adCode)
                            .put("item", "关闭")
                            .put("dialogType", "doubleAward")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_COIN:
                    //收听阶段金币奖励
                    break;
                case FuliBallType.BALL_TYPE_SGIN:
                    //签到翻倍奖励
                    new XMTraceApi.Trace()
                            .setMetaId(10136)
                            .setServiceId("dialogClick")
                            .put("adId", adCode)
                            .put("item", "关闭")
                            .put("dialogType", "doubleSignAward")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_HOME_VIDEO:
                    new XMTraceApi.Trace()
                            .setMetaId(12465)
                            .setServiceId("dialogClick")
                            .put("item", "关闭")
                            .put("coinCount", "" + (mDataModel != null ? mDataModel.amount : 0))
                            .put("adId", adCode)
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                default:
                    break;
            }
        } else {
            switch (mDataModel.ballType) {
                case FuliBallType.BALL_TYPE_LISTEN:
                    //收听奖励气泡
                    //是否是超限后观看视频弹框
                    boolean isPlayVideoFinish = false;
                    if (mDataModel.fulliCoinRewardReqModel != null && mDataModel.fulliCoinRewardReqModel.ballAward != null) {
                        isPlayVideoFinish = mDataModel.fulliCoinRewardReqModel.ballAward.isAdListeLimitFinish;
                    }
                    new XMTraceApi.Trace()
                            .setMetaId(10038)
                            .setServiceId("dialogClick")
                            .put("adId", adCode)
                            .put("item", "关闭")
                            .put("dialogType", "listenAward")
                            .put("dialogSeq", isPlayVideoFinish ? "2" : "1")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_LUCK:
                    //幸运奖励气泡
                    new XMTraceApi.Trace()
                            .setMetaId(10032)
                            .setServiceId("dialogClick")
                            .put("adId", adCode)
                            .put("item", "关闭")
                            .put("dialogType", "LuckAward")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_BAOXIANG:
                    //开宝箱奖励
                    new XMTraceApi.Trace()
                            .setMetaId(12408)
                            .setServiceId("dialogClick")
                            .put("dialogTitle", "金币奖励")
                            .put("item", "关闭")
                            .put("adId", adCode)
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_COIN:
                    //收听阶段金币奖励
                    new XMTraceApi.Trace()
                            .setMetaId(10045)
                            .setServiceId("dialogClick")
                            .put("adId", adCode)
                            .put("item", "关闭")
                            .put("dialogType", "getAward")
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_COMMON_DIALOG:
                    //福利页通用弹框
                    new XMTraceApi.Trace()
                            .setMetaId(10740)
                            .setServiceId("dialogClick")
                            .put("adId", adCode)
                            .put("item", "关闭")
                            .put("dialogType", "commonAdPopup")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_HOME_HOUR:
                    //首页整点奖励弹框
                    new XMTraceApi.Trace()
                            .setMetaId(11700)
                            .setServiceId("dialogClick")
                            .put("item", "close")
                            .put("abTest", "testA")
                            .put("adId", adCode)
                            .put("coincount", "" + mDataModel.amount)
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                case FuliBallType.BALL_TYPE_HOME_VIDEO:
                    new XMTraceApi.Trace()
                            .setMetaId(12463)
                            .setServiceId("dialogClick")
                            .put("item", "关闭")
                            .put("coinCount", "" + mDataModel.amount)
                            .put("adId", adCode)
                            .put("popUi", popUi)
                            .createTrace();
                    break;
                default:
                    //进行通用奖励类型的埋点
                    new XMTraceApi.Trace()
                            .setMetaId(12945)
                            .setServiceId("dialogClick")
                            .put("positonName", mDataModel.adPositionName)
                            .put("coinCount", mDataModel.amount + "")
                            .put("slotId", mDataModel.adCSJCode)
                            .put("item", "关闭")
                            .put("popUi", popUi)
                            .createTrace();
                    break;
            }
        }
    }
}
