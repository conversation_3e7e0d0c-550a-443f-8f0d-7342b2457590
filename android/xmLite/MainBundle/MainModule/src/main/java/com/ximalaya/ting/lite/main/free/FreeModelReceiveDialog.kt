package com.ximalaya.ting.lite.main.free

import android.graphics.Color
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.adsdk.callback.ISimpleReawardPlayComplete
import com.ximalaya.ting.android.host.adsdk.manager.ThirdReawardAdShowManger
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.listenertask.ListenEarnCoinDialogManager
import com.ximalaya.ting.android.host.listenertask.callback.JssdkFuliRewardCallback
import com.ximalaya.ting.android.host.listenertask.callback.OnLoadOneSelfAdLoadListener
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew
import com.ximalaya.ting.android.host.manager.UnlockTimeTrackManager
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.model.ad.AdWrapper
import com.ximalaya.ting.android.host.model.ad.LoadReawardParams
import com.ximalaya.ting.android.host.model.earn.FuliBallDialogDataModel
import com.ximalaya.ting.android.host.model.earn.FuliBallType
import com.ximalaya.ting.android.host.model.earn.RewardCoinObtainRsp
import com.ximalaya.ting.android.host.util.common.SpannableStringUtils
import com.ximalaya.ting.android.host.util.constant.AdConstants
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.manager.HomeFreeModelGuideManager
import com.ximalaya.ting.lite.main.model.RewardVideoExchangeModel
import com.ximalaya.ting.lite.main.playnew.dialog.UnlockListenTimeDialog


class FreeModelReceiveDialog : BaseFullScreenDialogFragment() {

    private var tvTime: TextView? = null
    private var tvConfirm: TextView? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.main_dia_free_model_receive_layout, container, false)
        initUi(view)
        return view
    }

    private fun initUi(rootView: View?) {
        rootView?.findViewById<View?>(R.id.main_iv_close)?.setOnClickListener {
            dismiss()
            // 首次领取畅听时长成功弹窗-点击关闭  弹框控件点击
            XMTraceApi.Trace()
                .setMetaId(51198)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("currPage", "homePageV2")
                .createTrace()
        }

        tvTime = rootView?.findViewById(R.id.main_tv_time)
        tvConfirm = rootView?.findViewById(R.id.main_tv_confirm)

        showTimeUi()

        tvConfirm?.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            //去看视频
            val loadRewardParams = LoadReawardParams()
            val positionName = "sub_freetime_inspire_video"

            val startTime = SystemClock.elapsedRealtime()
            ThirdReawardAdShowManger.loadRewardVideoAndShow(BaseApplication.getTopActivity(), object : ISimpleReawardPlayComplete {

                override fun onAdLoadError() {
                    CustomToast.showFailToast("激励视频数据异常")
                }

                override fun onAdPlayComplete() {
                    UnlockTimeTrackManager.trackClick(
                        "弹窗激励视频兑换",
                        SystemClock.elapsedRealtime() - startTime,
                        "激励视频观看成功"
                    )
                    HomeFreeModelGuideManager.performRewardVideoExchangeListenTime(true, object : UnlockListenTimeDialog.IRequestCallBack<RewardVideoExchangeModel> {
                        override fun onResult(result: RewardVideoExchangeModel) {
                            dismiss()
                            val duration = (UnlockListenTimeManagerNew.unlockConfigModel?.videoExchangeRateListenDuration ?: 0) / 60
                            if (UnlockListenTimeManagerNew.getAvailableListeningTime() >= 14400) {
                                ToastManager.showToast("已领取${duration}分钟免费时长")
                            } else{
                                showSupperCommonWithAdDialog(duration)
                            }
                        }
                    })
                }
            }, positionName, loadRewardParams)

            // 首次领取畅听时长成功弹窗-点击立即获取  弹框控件点击
            XMTraceApi.Trace()
                .setMetaId(51197)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("currPage", "homePageV2")
                .createTrace()
        }

        // 首次领取畅听时长成功弹窗  弹框展示
        XMTraceApi.Trace()
            .setMetaId(51196)
            .setServiceId("dialogView") // 弹窗展示时上报
            .put("currPage", "homePageV2")
            .createTrace()
    }

    private fun showTimeUi() {
        val config = UnlockListenTimeManagerNew.unlockConfigModel
        if (config != null) {
            val text = formatAvailableListeningTime(config.bufferDailyComplimentaryListenDuration!!)
            var content = SpannableStringUtils.transformForChangeSize(text, 2f, true, "\\d+")
            content = SpannableStringUtils.transformForAddColor(content, Color.parseColor("#FF6110"), "\\d+")
            tvTime?.text = content
        }
    }

    private fun showSupperCommonWithAdDialog(duration : Int){
        val dataModel = FuliBallDialogDataModel(FuliBallType.BALL_TYPE_COMMON_DIALOG, 0)
        dataModel.awardDesc = "恭喜获得" + duration + "分钟免费时长"
        dataModel.adPositionName = AdConstants.XM_AD_NAME_CSJ_SUB_FULI_COMMON_REWARD_POPUP_LARGE
        dataModel.sourceName = FuliBallType.SOURCE_NAME_FREE_LISTEN_DIALOG
        dataModel.awardTime = duration

        val loadRewardParams = LoadReawardParams()
        ListenEarnCoinDialogManager.getInstance()
            .loadFuliDialogButtomAdWithAdx(dataModel.adPositionName,
                loadRewardParams, object : OnLoadOneSelfAdLoadListener {
                    override fun loadAdSuccess(ttFeedAd: AdWrapper) {
                        showCommonAdDialog(dataModel, ttFeedAd)
                    }

                    override fun loadAdError(code: Int, message: String) {
                        ToastManager.showToast("已领取${duration}分钟免费时长")
                    }
                }
            )
    }


    private fun showCommonAdDialog(dataModel: FuliBallDialogDataModel, ttFeedAd: AdWrapper?) {
        val mainActivity = BaseApplication.getMainActivity()
        if (mainActivity is MainActivity) {
            try {
                val fragmentAction = Router.getMainActionRouter().fragmentAction
                val baseDialogFragment: BaseDialogFragment<*> = fragmentAction.newFuliCoinBallDialogFragment(dataModel, ttFeedAd, object :
                    JssdkFuliRewardCallback {
                    override fun onAwardSuccess(
                        successCode: Int,
                        obtainRsp: RewardCoinObtainRsp?
                    ) {
                        HomeFreeModelGuideManager.performRewardVideoExchangeListenTime(true, null)
                    }

                    override fun onError(
                        errorCode: Int,
                        message: String?,
                        obtainRsp: RewardCoinObtainRsp?
                    ) {
                        ToastManager.showToast("已领取${dataModel.awardTime}分钟免费时长")
                    }

                })
                baseDialogFragment.show(mainActivity.supportFragmentManager, "")
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * 格式化可用收听时长
     *
     * @param time 可用收听时长 单位秒
     * @return 格式化后数据  大于4小时：显示“剩余收听时长超过4小时”； 0<X></X><4小时：显示“剩余收听时长还剩H小时M分钟”；X=0分钟，显示“剩余收听时长还剩0分钟”
     */
    private fun formatAvailableListeningTime(time: Long): String {
        if (time <= 0L) {
            return "0分钟"
        }
        // 计算出分钟
        var minutes = time / 60f
        // 计算多余的秒数
        val second = time % 60f
        // 秒数不为空  分钟数+1
        minutes = if (second == 0f) minutes else minutes + 1

        return "${minutes.toInt()}分钟"
    }
}