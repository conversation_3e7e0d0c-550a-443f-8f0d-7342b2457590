package com.ximalaya.ting.lite.main.home.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.onekey.OneKeyRadioSettingFragment;

/**
 * Created by ZhuPeipei on 2020-04-18 00:37.
 *
 * @Description: 一键听电台兴趣设置入口
 */
public class HomeRecommendOneKeyRadioSettingProvider
        implements IMulitViewTypeViewAndData<HomeRecommendOneKeyRadioSettingProvider.Holder, String> {

    private BaseFragment2 mFragment;

    public HomeRecommendOneKeyRadioSettingProvider(BaseFragment2 fragment) {
        this.mFragment = fragment;
    }

    @Override
    public void bindViewDatas(Holder holder, ItemModel<String> t, View convertView, int position) {
        holder.rootView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mFragment == null) {
                    return;
                }
                mFragment.startFragment(OneKeyRadioSettingFragment.newInstance());
            }
        });
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_home_recommend_item_onekey_radio_setting, parent, false);
    }

    @Override
    public Holder buildHolder(View convertView) {
        return new Holder(convertView);
    }

    public static class Holder extends HolderAdapter.BaseViewHolder {
        View rootView;

        private Holder(View convertView) {
            rootView = convertView;
        }
    }
}
