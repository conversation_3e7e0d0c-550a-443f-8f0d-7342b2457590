package com.ximalaya.ting.lite.main.home.view;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.HorizontalScrollViewInSlideView;
import com.ximalaya.ting.android.host.model.category.CategoryMetadata;
import com.ximalaya.ting.android.host.model.category.CategoryMetadataValue;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.constant.DPConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2017/11/29.
 */

public class ChooseMetadataView extends LinearLayout implements View.OnClickListener {
    public static final String CAL_DIMEN_RECENT = "recent";
    public static final String CAL_DIMEN_HOT = "hot";
    public static final String CAL_DIMEN_CLASSIC = "classic";
    public static final String CAL_DIMEN_DEFAULT = CAL_DIMEN_HOT;

    public static final int FROM_CATEGORY = 1;
    public static final int FROM_KEYWORD = 2;
    private int mFrom = FROM_CATEGORY;

    private String mCategoryId;
    private String mKeywordId;

    public void setCategoryId(String categoryId) {
        this.mCategoryId = categoryId;
    }

    public void setFrom(int from) {
        mFrom = from;
    }

    public void setKeywordId(String keywordId) {
        mKeywordId = keywordId;
    }

    private boolean mIsFold = false; //是否为收起状态


    private List<CategoryMetadata> mMetadata;
    private View mSlideView;
    private final List<String> mMetadataDisplayNameList = new ArrayList<>();
    private String mCalDimension = CAL_DIMEN_DEFAULT;
    private String mMetadataHttpRequestParam;
    private List<OnMetadataChangeListener> mMetadataChangeListeners = new ArrayList<>();

    private FrameLayout mFlFoldContainer;
    private TextView mTvFold;
    private String chooseMetaData;
    private OnClickListener mOnFoldClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            UserTracking userTracking = new UserTracking();
            String srcPage = "";
            if (mFrom == FROM_CATEGORY) {
                srcPage = "全部分类页";
            } else {
                srcPage = "hotword";
                userTracking.setSrcPageId(mKeywordId);
            }

            String itemId = "";
            if (mIsFold) {
                itemId = "筛选";
            } else {
                itemId = "收起";
            }

            userTracking.setSrcPage(srcPage).
                    setItem("button").
                    setItemId(itemId).
                    setCategory(mCategoryId).
                    statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_CATEGORY_PAGE_CLICK);

            // 分类页-热词内页-筛选  点击事件
            new XMTraceApi.Trace()
                    .click(45536)
                    .put("currPage", "Hot word inner page")
                    .createTrace();

            toggleFold();
        }
    };

    public ChooseMetadataView(Context context) {
        super(context);
        init();
    }

    public ChooseMetadataView(Context context, String calDimension, String metadata) {
        super(context);
        mCalDimension = calDimension;
        chooseMetaData = metadata;

        init();
    }

    private void setCategoryMetaDataValue(List<CategoryMetadata> object, String chooseMetadata) {
        if (!TextUtils.isEmpty(chooseMetadata)) {
            if (!TextUtils.isEmpty(chooseMetadata)) {
                String[] selectedMetaDataStrArray = chooseMetadata.split(":");
                if (selectedMetaDataStrArray.length >= 2) {
                    int selectedMetadataId = Integer.parseInt(selectedMetaDataStrArray[0]);
                    int selectedMetadataValueId = Integer.parseInt(selectedMetaDataStrArray[1]);
                    for (CategoryMetadata categoryMetadata : object) {
                        if (categoryMetadata.getId() == selectedMetadataId) {
                            if (categoryMetadata.getMetadataValues() != null) {
                                for (CategoryMetadataValue categoryMetadataValue : categoryMetadata.getMetadataValues()) {
                                    if (categoryMetadataValue.getId() == selectedMetadataValueId) {
                                        categoryMetadata.setChosed(false);
                                        categoryMetadataValue.setChosed(true);
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    public ChooseMetadataView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ChooseMetadataView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public void setSlideView(View slideView) {
        mSlideView = slideView;
    }

    private void toggleFold() {
        if (mIsFold) {
            mIsFold = false;
            mTvFold.setText("收起");
            setCategoryMetaDataValue(mMetadata, chooseMetaData);
            doRecursiveInflate(ChooseMetadataView.this, mMetadata);

            if (mShowBottomDivider) {
                addBottomDivider(ChooseMetadataView.this);
            }
        } else {
            mIsFold = true;
            mTvFold.setText("筛选");
            int defaultChildCount = mShowDivider ? 2 : 1;
            if (getChildCount() > defaultChildCount) {
                removeViews(defaultChildCount, getChildCount() - defaultChildCount);
            }
        }
    }

    public void setFold(boolean isFold) {
        if (mIsFold != isFold) {
            toggleFold();
        }
    }

    private boolean mShowFoldButton = true;

    public void showFoldButton(boolean show) {
        mShowFoldButton = show && !ToolUtil.isEmptyCollects(mMetadata);
        if (mShowFoldButton) {
            mTvFold.setVisibility(VISIBLE);
        } else {
            mTvFold.setVisibility(INVISIBLE);
        }

    }


    private void init() {
        setOrientation(LinearLayout.VERTICAL);

        setFocusable(false);
        setFocusableInTouchMode(false);

        mMetadataDisplayNameList.add(getCalDimenDisplayName(mCalDimension));

        mTvFold = new TextView(getContext());
        if (mIsFold) {
            mTvFold.setText("筛选");
        } else {
            mTvFold.setText("收起");
        }
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.RIGHT;
        params.rightMargin = BaseUtil.dp2px(getContext(), 15);
        mTvFold.setLayoutParams(params);
        mTvFold.setTextSize(13);
        ColorStateList stateList = getResources().
                getColorStateList(R.color.main_text_gray_clickable);
        mTvFold.setTextColor(stateList);
        mTvFold.setCompoundDrawablePadding(BaseUtil.dp2px(getContext(), 2));
        Drawable leftDrawable = LocalImageUtil.getDrawable(getContext(), R.drawable.main_ic_category_filter_new);
        mTvFold.setCompoundDrawables(leftDrawable, null, null, null);
        mTvFold.setBackgroundResource(R.drawable.main_bg_corner_ccf3f4f5);
        mTvFold.setPadding(DPConstants.getInstance(getContext()).DP_8,
                DPConstants.getInstance(getContext()).DP_2,
                DPConstants.getInstance(getContext()).DP_8,
                DPConstants.getInstance(getContext()).DP_2);
        mTvFold.setOnClickListener(mOnFoldClickListener);
        AutoTraceHelper.bindData(mTvFold, "");

        mFlFoldContainer = new FrameLayout(getContext());
        LinearLayout.LayoutParams llParams = new LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        mFlFoldContainer.setLayoutParams(llParams);
        mFlFoldContainer.addView(mTvFold);
    }

    public void setMetadata(List<CategoryMetadata> metadata) {
        setMetadata(metadata, -1, -1);
    }

    /**
     * 递归查找出指定标签所在的节点
     */
    private CategoryMetadataValue getSelectMetadataOrMetadataValue(List<CategoryMetadata> metadata, int metadataValueId) {
        CategoryMetadataValue result = null;
        for(CategoryMetadata categoryMetadata : metadata) {
            List<CategoryMetadataValue> metadataValues = categoryMetadata.getMetadataValues();
            if(!ToolUtil.isEmptyCollects(metadataValues)) {
                for(CategoryMetadataValue categoryMetadataValue : metadataValues) {
                    if (categoryMetadataValue.getId() == metadataValueId) {
                        return categoryMetadataValue;
                    } else if(!ToolUtil.isEmptyCollects(categoryMetadataValue.getMetadatas())){
                        result = getSelectMetadataOrMetadataValue(categoryMetadataValue.getMetadatas(), metadataValueId);
                        if(result != null) {
                            return result;
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 由指定节点递归向上回溯父节点，改变状态
     */
    private void requestUpdateMetadataValue(CategoryMetadataValue selectedMetadataValue) {
        updateSelectedMetadata(selectedMetadataValue);
        CategoryMetadata categoryMetadata = selectedMetadataValue.getParentMetadata();
        if(categoryMetadata != null) {
            CategoryMetadataValue categoryMetadataValue = categoryMetadata.getParentMetadataValue();
            if(categoryMetadataValue != null) {
                requestUpdateMetadataValue(categoryMetadataValue);
            }
        }
    }

    public void setMetadata(List<CategoryMetadata> metadata, int metadataId, int metadataValueId) {
        // 跳转过来有指定某一个标签（如专辑页标签跳转）
        if(metadataValueId != -1 && !ToolUtil.isEmptyCollects(metadata)) {
            CategoryMetadataValue selectedMetadataValue = getSelectMetadataOrMetadataValue(metadata, metadataValueId);
            if(null != selectedMetadataValue) {
                requestUpdateMetadataValue(selectedMetadataValue);
            }
        }

        mMetadata = metadata;

        inflateFilterPanel(this, metadata);

        parseMetaParams();

        notifyMetadataChangeListener();

        showFoldButton(true);
    }

    private Map<CategoryMetadata, HorizontalScrollView> mHorizontalScrollViews = new HashMap<>();

    @NonNull
    private HorizontalScrollView getChild(CategoryMetadata categoryMetadata) {
        HorizontalScrollView scrollView = mHorizontalScrollViews.get(categoryMetadata);
        if (scrollView == null) {
            scrollView = new HorizontalScrollViewInSlideView(getContext());
            scrollView.setPadding(BaseUtil.dp2px(getContext(), 15), BaseUtil.dp2px(getContext(), 6), 0, BaseUtil.dp2px(getContext(), 6));

            if (mSlideView != null) {
                ((HorizontalScrollViewInSlideView) scrollView).
                        setDisallowInterceptTouchEventView((ViewGroup) mSlideView);
            }

            scrollView.setTag(categoryMetadata);
            scrollView.requestDisallowInterceptTouchEvent(true);
            scrollView.setHorizontalScrollBarEnabled(false);

            LinearLayout layout = new LinearLayout(getContext());
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layout.setLayoutParams(lp);
            scrollView.addView(layout);

            mHorizontalScrollViews.put(categoryMetadata, scrollView);
        }

        return scrollView;
    }

    private void doRecursiveInflate(ViewGroup viewGroup, List<CategoryMetadata> metadata) {
        if (metadata != null && metadata.size() > 0) {
            for (CategoryMetadata categoryMetadata : metadata) {
                Logger.log("Metadata___" + categoryMetadata.getDisplayName());
                final HorizontalScrollView scrollView = getChild(categoryMetadata);
                viewGroup.addView(scrollView);
                AutoTraceHelper.setLabelForCTWithMultiSameSubView(viewGroup);
                LinearLayout layout = (LinearLayout) scrollView.getChildAt(0);
                AutoTraceHelper.setLabelForCTWithMultiSameSubView(layout);

                int index = 0;
                TextView tv;
                if (layout.getChildAt(index) == null) {
                    tv = getView(categoryMetadata);
                    layout.addView(tv);
                } else {
                    tv = (TextView) layout.getChildAt(index);
                    if (categoryMetadata.isChosed()) {
                        tv.setTextColor(Color.parseColor("#E83F46"));
                        tv.setBackgroundResource(R.drawable.main_bg_category_metadata_item);
                        tv.setOnClickListener(null);
                    } else {
                        tv.setTextColor(Color.parseColor("#999999"));
                        tv.setBackground(null);
                        tv.setOnClickListener(this);
                        Map<String, Integer> bindData = new HashMap<>();
                        bindData.put("rowID", categoryMetadata.getId());
                        AutoTraceHelper.bindData(tv, AutoTraceHelper.MODULE_DEFAULT, bindData);
                    }
                }
                LinearLayout.LayoutParams childPs = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                childPs.leftMargin = BaseUtil.dp2px(getContext(), 2);
                childPs.rightMargin = BaseUtil.dp2px(getContext(), 2);
                for (CategoryMetadataValue value : categoryMetadata.getMetadataValues()) {
                    index++;
                    final TextView view;
                    if (index < layout.getChildCount() && layout.getChildAt(index) != null) {
                        view = (TextView) layout.getChildAt(index);
                        if (value.isChosed()) {
                            view.setTextColor(Color.parseColor("#E83F46"));
                            view.setBackgroundResource(R.drawable.main_bg_category_metadata_item);
                            view.setOnClickListener(null);
                        } else {
                            view.setTextColor(Color.parseColor("#999999"));
                            view.setBackground(null);
                            view.setOnClickListener(this);
                            Map<String, Integer> bindData = new HashMap<>();
                            bindData.put("rowID", categoryMetadata.getId());
                            bindData.put("columnID", value.getId());
                            AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, bindData);
                        }
                    } else {
                        view = getView(value, categoryMetadata);
                        layout.addView(view, childPs);
                    }
                    if (value.isChosed()) {
                        //搜索直达中需要HorizontalScrollView直接跳到被选中的TextView上
//                        scrollToLocation(scrollView, view);
                        scrollView.addOnLayoutChangeListener(new OnLayoutChangeListener() {
                            @Override
                            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                                removeOnLayoutChangeListener(this);
                                scrollToLocation(scrollView, view);
                            }
                        });
                    }
                }

                //添加次级数据
                if (!categoryMetadata.isChosed() && categoryMetadata.getMetadataValues() != null) {
                    for (CategoryMetadataValue value : categoryMetadata
                            .getMetadataValues()) {
                        if (value.isChosed()) {
                            doRecursiveInflate(viewGroup, value.getMetadatas());
                        }
                    }
                }
            }
        }
    }

    private void scrollToLocation(final HorizontalScrollView scrollView, final TextView view) {
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        if (location[0] > BaseUtil.getScreenWidth(getContext())) {
            scrollView.scrollTo(location[0] - view.getWidth() / 2, location[1]);
        }
    }

    private TextView getView(CategoryMetadata data) {
        TextView tv = new TextView(getContext());
        tv.setText(data.getDisplayName());
        tv.setTextSize(13);
        tv.setPadding(DPConstants.getInstance(getContext()).DP_13,
                DPConstants.getInstance(getContext()).DP_4,
                DPConstants.getInstance(getContext()).DP_13,
                DPConstants.getInstance(getContext()).DP_4);
        if (data.isChosed()) {
            tv.setTextColor(Color.parseColor("#E83F46"));
            tv.setBackgroundResource(R.drawable.main_bg_category_metadata_item);
            tv.setOnClickListener(null);
        } else {
            tv.setTextColor(Color.parseColor("#999999"));
            tv.setBackground(null);
            tv.setOnClickListener(this);
            Map<String, Integer> bindData = new HashMap<>();
            bindData.put("rowID", data.getId());
            AutoTraceHelper.bindData(tv, AutoTraceHelper.MODULE_DEFAULT, bindData);
        }
        tv.setTag(data);

        return tv;
    }

    private TextView getView(CategoryMetadataValue data, CategoryMetadata categoryMetadata) {
        TextView tv = new TextView(getContext());
        tv.setText(data.getDisplayName());
        tv.setTextSize(13);
        tv.setPadding(DPConstants.getInstance(getContext()).DP_10,
                DPConstants.getInstance(getContext()).DP_3,
                DPConstants.getInstance(getContext()).DP_10,
                DPConstants.getInstance(getContext()).DP_3);
        if (data.isChosed()) {
            tv.setTextColor(Color.parseColor("#E83F46"));
            tv.setBackgroundResource(R.drawable.main_bg_category_metadata_item);
            tv.setOnClickListener(null);
        } else {
            tv.setTextColor(Color.parseColor("#999999"));
            tv.setBackground(null);
            tv.setOnClickListener(this);
            Map<String, Integer> bindData = new HashMap<>();
            bindData.put("rowID", categoryMetadata.getId());
            bindData.put("columnID", data.getId());
            AutoTraceHelper.bindData(tv, AutoTraceHelper.MODULE_DEFAULT, bindData);
        }
        tv.setTag(data);

        return tv;
    }

    /**
     * @param selectedItem 被选中的model 可能为CategoryMetadata 或 CategoryMetadataValue
     */
    private void updateSelectedMetadata(Object selectedItem) {
        if (selectedItem instanceof CategoryMetadata) {
            CategoryMetadata metadata = (CategoryMetadata) selectedItem;
            metadata.setChosed(true);

            for (CategoryMetadataValue value : metadata.getMetadataValues()) {
                value.setChosed(false);
            }
        } else if (selectedItem instanceof CategoryMetadataValue) {
            CategoryMetadataValue metadataValue = (CategoryMetadataValue) selectedItem;
            metadataValue.getParentMetadata().setChosed(false);

            for (CategoryMetadataValue value : metadataValue.getParentMetadata().getMetadataValues()) {
                if (value.getId() == metadataValue.getId()) {
                    value.setChosed(true);
                } else {
                    value.setChosed(false);
                }
            }
        }
    }

    /**
     * 分类过滤元数据布局初始化
     */
    private void inflateFilterPanel(ViewGroup viewGroup,
                                    List<CategoryMetadata> metadata) {
        viewGroup.removeAllViews();

        addCalDimension(viewGroup);

        if (mShowDivider) {
            addDivider(viewGroup);
        }

        doRecursiveInflate(viewGroup, metadata);

        addBottomDivider(viewGroup);
    }

    private void addDivider(ViewGroup viewGroup) {
        View divider = new View(getContext());
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 1);
        params.bottomMargin = BaseUtil.dp2px(getContext(), 10);
        divider.setLayoutParams(params);
        divider.setBackgroundColor(Color.parseColor("#E8E8E8"));
        viewGroup.addView(divider);
    }

    private View mBottomDivider;
    private boolean mShowBottomDivider = true;
    private boolean mShowDivider = false;

    private void addBottomDivider(ViewGroup viewGroup) {
        mBottomDivider = new View(getContext());
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                AbsListView.LayoutParams.MATCH_PARENT, BaseUtil.dp2px(
                getContext(), 1));
        lp.bottomMargin = BaseUtil.dp2px(getContext(), 10);
        lp.topMargin = BaseUtil.dp2px(getContext(), 10);
        mBottomDivider.setLayoutParams(lp);
        mBottomDivider.setBackgroundColor(Color.parseColor("#f3f4f5"));
        viewGroup.addView(mBottomDivider);
    }

    public void showBottomDivider(boolean show) {
        mShowBottomDivider = show;
        if (mBottomDivider == null) {
            return;
        }
        if (show) {
            mBottomDivider.setVisibility(VISIBLE);
        } else {
            mBottomDivider.setVisibility(GONE);
        }
    }

    private void addCalDimension(ViewGroup viewGroup) {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                LayoutParams.WRAP_CONTENT);
        params.leftMargin = BaseUtil.dp2px(getContext(), 15);
        params.topMargin = BaseUtil.dp2px(getContext(), 15);
        params.bottomMargin = BaseUtil.dp2px(getContext(), 6);
        LinearLayout layout = new LinearLayout(getContext());
        layout.setGravity(Gravity.CENTER_VERTICAL);

        layout.setLayoutParams(params);
        String[] calDimensions = {CAL_DIMEN_HOT,
                CAL_DIMEN_CLASSIC,
                CAL_DIMEN_RECENT};
        for (final String str : calDimensions) {
            final String displayName = getCalDimenDisplayName(str);
            TextView subTv = new TextView(getContext());
            subTv.setText(displayName);
            subTv.setTextSize(13);
            subTv.setPadding(DPConstants.getInstance(getContext()).DP_13,
                    DPConstants.getInstance(getContext()).DP_4,
                    DPConstants.getInstance(getContext()).DP_13,
                    DPConstants.getInstance(getContext()).DP_4);

            if (mMetadataDisplayNameList.contains(displayName)) {
                subTv.setTextColor(Color.parseColor("#E83F46"));
                subTv.setBackgroundResource(R.drawable.main_bg_category_metadata_item);
            } else {
                subTv.setTextColor(Color.parseColor("#999999"));
                subTv.setBackground(null);
                subTv.setOnClickListener(new OnClickListener() {

                    @Override
                    public void onClick(View v) {
                        UserTracking userTracking = new UserTracking();
                        String srcPage = "";
                        if (mFrom == FROM_KEYWORD) {
                            srcPage = "hotword";
                            userTracking.setSrcPageId(mKeywordId);
                        } else {
                            srcPage = "全部分类页";
                        }


                        userTracking.setSrcPage(srcPage).
                                setSrcModule("排序").
                                setItem("button").
                                setItemId(displayName).
                                setCategory(mCategoryId).
                                statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_CATEGORY_PAGE_CLICK);

                        if (!NetworkUtils.isNetworkAvaliable(getContext().getApplicationContext())) {
                            CustomToast.showFailToast(R.string.main_network_exeption_toast);
                            return;
                        }

                        mMetadataDisplayNameList.remove(getCalDimenDisplayName(mCalDimension));
                        mMetadataDisplayNameList.add(0, displayName);

                        removeViewAt(0);
                        addCalDimension(ChooseMetadataView.this);

                        mCalDimension = str;

                        notifyMetadataChangeListener();
                    }
                });
                AutoTraceHelper.setLabelForCTWithMultiSameSubView(layout);
                AutoTraceHelper.bindData(subTv, AutoTraceHelper.MODULE_DEFAULT, "");
            }

            layout.addView(subTv);
        }

        if (mFlFoldContainer.getParent() != null) {
            ((ViewGroup) mFlFoldContainer.getParent()).removeView(mFlFoldContainer);
        }
        layout.addView(mFlFoldContainer);

        viewGroup.addView(layout, 0);
    }

    @Override
    public void onClick(View v) {
        if (!NetworkUtils.isNetworkAvaliable(getContext().getApplicationContext())) {
            CustomToast.showFailToast(R.string.main_network_exeption_toast);
            return;
        }

        TextView tv = (TextView) v;
        tv.setClickable(false);
        Object object = tv.getTag();

        List<CategoryMetadata> metadata = mMetadata;
        updateSelectedMetadata(object);

        inflateFilterPanel(this, metadata);

        parseMetaParams();

        String srcPage = "";
        UserTracking userTracking = new UserTracking();
        if (mFrom == FROM_CATEGORY) {
            srcPage = "全部分类页";
        } else {
            srcPage = "hotword";
            userTracking.setSrcPageId(mKeywordId);
        }
        userTracking.
                setSrcPage(srcPage).
                setSrcModule("类目搜索").
                setCategory(mCategoryId).
                setMetaData(mMetadataHttpRequestParam).
                statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_CATEGORY_PAGE_CLICK);
    }

    private void parseMetaParams() {
        mMetadataDisplayNameList.clear();
        mMetadataDisplayNameList.add(getCalDimenDisplayName(mCalDimension));

        StringBuilder outSb = new StringBuilder();
        recursiveParseMetaParams(mMetadata, outSb, mMetadataDisplayNameList);
        if (outSb.length() > 0) {
            mMetadataHttpRequestParam = outSb.substring(0, outSb.length() - 1);  //去除最后的"," 号
        } else {
            mMetadataHttpRequestParam = "";
        }

        notifyMetadataChangeListener();
    }

    /**
     * 解析已选的标签元数据
     */
    private void recursiveParseMetaParams(List<CategoryMetadata> metadata,
                                          StringBuilder httpRequestParam,
                                          List<String> displayNameList) {
        if (metadata == null || httpRequestParam == null || displayNameList == null) {
            return;
        }

        for (CategoryMetadata categoryMetadata : metadata) {

            for (CategoryMetadataValue value : categoryMetadata
                    .getMetadataValues()) {
                if (value.isChosed() && value.getParentMetadata().getId() != 0) {//只筛选单行子标签的数据
                    httpRequestParam.append(value.getParentMetadata().getId());
                    httpRequestParam.append(":");
                    httpRequestParam.append(value.getId());
                    httpRequestParam.append(",");
                    displayNameList.add(value.getDisplayName());
                }
            }

            //操作次级数据
            if (!categoryMetadata.isChosed() && categoryMetadata.getMetadataValues() != null) {
                for (CategoryMetadataValue value : categoryMetadata.getMetadataValues()) {
                    if (value.isChosed()) {
                        recursiveParseMetaParams(value.getMetadatas(), httpRequestParam, displayNameList);
                    }
                }
            }

        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {  // 限制该view 的高度最高为屏幕的2/3
        int originHeight = MeasureSpec.getSize(heightMeasureSpec);
        int maxHeight = BaseUtil.getScreenHeight(getContext()) / 3 * 2;
        if (maxHeight < originHeight) {
            heightMeasureSpec = MeasureSpec.makeMeasureSpec(maxHeight, MeasureSpec.AT_MOST);
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    public interface OnMetadataChangeListener {
        /**
         * @param calDimension         筛选维度
         * @param metadataRequestParam 用于筛选专辑的元数据请求参数
         * @param hintStr              展示给用户看的，当前已选的筛选元数据
         */
        void onMetadataChange(String calDimension, String metadataRequestParam, String hintStr);
    }

    public void addMetadataChangeListener(OnMetadataChangeListener listener) {
        if (listener != null) {
            mMetadataChangeListeners.add(listener);
        }
    }

    public void removeMetadataChangeListener(OnMetadataChangeListener listener) {
        if (listener != null) {
            mMetadataChangeListeners.remove(listener);
        }
    }

    private void notifyMetadataChangeListener() {
        String hintStr = getHintStr(mMetadataDisplayNameList);
        for (OnMetadataChangeListener listener : mMetadataChangeListeners) {
            listener.onMetadataChange(mCalDimension, mMetadataHttpRequestParam, hintStr);
        }
    }

    private String getHintStr(List<String> displayNames) {
        StringBuilder sb = new StringBuilder();
        for (String name : displayNames) {
            sb.append(name);
            sb.append(" · ");
        }
        String metadata = sb.toString();
        metadata = metadata.substring(0, metadata.length() - 2);
        return metadata;
    }


    private static final Map<String, String> CAL_DIMEN_DISPLAY_NAMES = new HashMap<String, String>() {
        {
            put(CAL_DIMEN_CLASSIC, "播放最多");
            put(CAL_DIMEN_HOT, "综合排序");
            put(CAL_DIMEN_RECENT, "最近更新");
        }
    };

    public static String getCalDimenDisplayName(String calDimen) {
        return CAL_DIMEN_DISPLAY_NAMES.get(calDimen);
    }

}
