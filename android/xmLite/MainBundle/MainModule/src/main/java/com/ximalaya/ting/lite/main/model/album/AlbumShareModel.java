package com.ximalaya.ting.lite.main.model.album;

import com.ximalaya.ting.android.host.model.album.AlbumM;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class AlbumShareModel {
    private String command;
    private Long expireTime;
    private String resourceCoverPage;
    private String resourceName;
    private String resourceShareCount;
    private Long shareRecordId;
    private Long resourceId;
    private int requiredNumber;
    private AlbumM basicAlbum;
    private int status = -1;
    private List<AlbumUserInfo> userInfos;

    public AlbumShareModel(JSONObject json) {
        this.shareRecordId = json.optLong("shareRecordId");
        this.command = json.optString("command");
        this.expireTime = json.optLong("expireTime");
        this.status = json.optInt("status", -1);
        this.resourceId = json.optLong("resourceId");
        this.resourceName = json.optString("resourceName");
        this.resourceCoverPage = json.optString("resourceCoverPage");
        this.basicAlbum = new AlbumM(json.optJSONObject("basicAlbum"));
        JSONArray jsonArray = json.optJSONArray("userInfos");
        if (jsonArray != null) {
            userInfos = new ArrayList<>();
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.optJSONObject(i);
                if (object != null) {
                    this.userInfos.add(new AlbumUserInfo(object));
                }
            }
        }
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public String getResourceCoverPage() {
        return resourceCoverPage;
    }

    public void setResourceCoverPage(String resourceCoverPage) {
        this.resourceCoverPage = resourceCoverPage;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getResourceShareCount() {
        return resourceShareCount;
    }

    public void setResourceShareCount(String resourceShareCount) {
        this.resourceShareCount = resourceShareCount;
    }

    public Long getShareRecordId() {
        return shareRecordId;
    }

    public void setShareRecordId(Long shareRecordId) {
        this.shareRecordId = shareRecordId;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public int getRequiredNumber() {
        return requiredNumber;
    }

    public void setRequiredNumber(int requiredNumber) {
        this.requiredNumber = requiredNumber;
    }

    public AlbumM getBasicAlbum() {
        return basicAlbum;
    }

    public void setBasicAlbum(AlbumM basicAlbum) {
        this.basicAlbum = basicAlbum;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<AlbumUserInfo> getUserInfos() {
        return userInfos;
    }

    public void setUserInfos(List<AlbumUserInfo> userInfos) {
        this.userInfos = userInfos;
    }
}
