package com.ximalaya.ting.lite.main.newuser.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.TagResult;
import com.ximalaya.ting.android.host.model.newuser.QuickListenModel;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.album.fragment.LiteAlbumFragment;
import com.ximalaya.ting.lite.main.utils.OneKeyRadioUtil;

import java.util.List;

/**
 * Created by dumingwei on 2021/4/23
 * <p>
 * Desc: 新人极速听下面的声音列表界面
 */
public class NewListenTrackListAdapter extends HolderAdapter<TrackM> {

    private static final String TAG = "NewListenTrackListAdapt";

    private final QuickListenModel mModel;

    public NewListenTrackListAdapter(Context context, List<TrackM> listData, QuickListenModel model) {
        super(context, listData);
        mModel = model;
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_new_listen_track;
    }


    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, TrackM oneKeyTrack, int position) {
        if (oneKeyTrack == null || !(holder instanceof ViewHolder)) {
            return;
        }
        ViewHolder h = (ViewHolder) holder;
        h.titleTv.setText(OneKeyRadioUtil.getTrackTitle(oneKeyTrack));
        Track track = PlayTools.getCurTrack(context);

        ImageManager.from(context).displayImage(h.ivCover, oneKeyTrack.getCoverUrlMiddle(), R.drawable.host_default_album);
        int playProgress = getPlayProgress(oneKeyTrack);
        if (track != null && track.getDataId() == oneKeyTrack.getDataId()) {
            h.titleTv.setTextColor(context.getResources().getColor(R.color.host_color_e83f46));
        } else {
            if (playProgress > 0) {
                h.titleTv.setTextColor(context.getResources().getColor(R.color.main_color_999999));
            } else {
                h.titleTv.setTextColor(context.getResources().getColor(R.color.main_color_111111));
            }
        }
        if (oneKeyTrack.getAlbum() != null) {
            String albumTitle = oneKeyTrack.getAlbum().getAlbumTitle();
            Logger.i(TAG, "albumTitle = " + albumTitle);
            Logger.i(TAG, "albumTitle = length = " + albumTitle.length());
            h.albumTitleTv.setText(getAlbumTitleLimitSize(albumTitle));
        }
        List<TagResult> tagList = oneKeyTrack.getShowTagList();

        String tagString = getTagString(tagList);

        if (TextUtils.isEmpty(tagString)) {
            h.tvAlbumTags.setText("");
            h.tvAlbumTags.setVisibility(View.INVISIBLE);
        } else {
            h.tvAlbumTags.setText(tagString);
            h.tvAlbumTags.setVisibility(View.VISIBLE);
        }
        h.ivRightArrow.setOnClickListener(v -> {
            toAlbumPage(oneKeyTrack);
            // 极速听-专辑入口  点击事件
            new XMTraceApi.Trace()
                    .click(30790) // 用户点击时上报
                    .put("trackName", oneKeyTrack.getTrackTitle())
                    .put("trackId", String.valueOf(oneKeyTrack.getDataId()))
                    .put("channelName", mModel.getTitle())
                    .put("channelId", String.valueOf(mModel.getPoolId()))
                    .put("currPage", "newUserFastListen")
                    .createTrace();
        });

        AutoTraceHelper.bindData(h.rootView, AutoTraceHelper.MODULE_DEFAULT, oneKeyTrack);
    }

    private String getAlbumTitleLimitSize(String albumTitle) {
        if (TextUtils.isEmpty(albumTitle)) {
            return albumTitle;
        }
        if (albumTitle.length() <= 7) {
            return albumTitle;
        }
        StringBuilder builder = new StringBuilder(albumTitle.substring(0, 7));
        builder.append("...");
        return builder.toString();
    }

    private void toAlbumPage(TrackM trackM) {
        if (trackM == null)
            return;
        SubordinatedAlbum album = trackM.getAlbum();
        if (album == null || album.getAlbumId() <= 0) {
            return;
        }
        LiteAlbumFragment fragment = LiteAlbumFragment.newInstance(album.getAlbumTitle(), album.getAlbumId(),
                AlbumEventManage.FROM_ALBUM_BELONG, ConstantsOpenSdk.PLAY_FROM_TAB_ALBUM);

        Activity topActivity = BaseApplication.getTopActivity();
        if (topActivity instanceof MainActivity) {
            ((MainActivity) topActivity).startFragment(fragment);
        }
    }

    private String getTagString(List<TagResult> tagList) {
        if (CollectionUtil.isNullOrEmpty(tagList)) {
            return null;
        }
        TagResult result = tagList.get(0);
        return result.getTagName();
    }

    private int getPlayProgress(Track track) {
        if (track == null) {
            return 0;
        }
        int playProgress;
        track.setLastPlayedMills(XmPlayerManager.getInstance(context).getHistoryPos(track.getDataId()));

        if (track.getLastPlayedMills() == PlayerConstants.PLAY_COMPLETE) {
            playProgress = 100;
        } else if (track.getDuration() <= 0 || track.getLastPlayedMills() < 0) {
            playProgress = 0;
        } else {
            if (track.getLastPlayedMills() >= track.getDuration() * 1000) {
                playProgress = 100;
            } else {
                playProgress = (int) (track.getLastPlayedMills() / 10.0f / track.getDuration());
                if (playProgress == 0) { // 可能播放进度小于1% 因此统一设置为1%
                    playProgress = 1;
                }
            }
        }
        return playProgress;
    }

    @Override
    public void onClick(View view, TrackM oneKeyTrack, int position, BaseViewHolder holder) {

    }

    class ViewHolder extends BaseViewHolder {

        View rootView;
        TextView titleTv;
        TextView albumTitleTv;
        TextView tvAlbumTags;
        ImageView ivRightArrow;

        ImageView ivCover;

        public ViewHolder(View convertView) {
            rootView = convertView;
            titleTv = convertView.findViewById(R.id.main_tv_track_title);
            albumTitleTv = convertView.findViewById(R.id.main_tv_album_title);
            tvAlbumTags = convertView.findViewById(R.id.main_tv_album_tags);
            ivRightArrow = convertView.findViewById(R.id.main_iv_right_arrow);
            ivCover = convertView.findViewById(R.id.main_iv_cover);
        }
    }
}
