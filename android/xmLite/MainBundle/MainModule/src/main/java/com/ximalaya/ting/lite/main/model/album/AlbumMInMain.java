package com.ximalaya.ting.lite.main.model.album;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.DislikeReason;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.List;

/**
 * <AUTHOR> on 2017/9/29.
 */

public class AlbumMInMain extends AlbumM {
    public List<DislikeReason> dislikeReasons;

    public AlbumMInMain() {

    }

    public AlbumMInMain(String json) {
        super(json);

        try {
            JSONObject jsonObject = new JSONObject(json);
            if (jsonObject.has("dislikeReasons")) {
                Type type = new TypeToken<List<DislikeReason>>() {
                }.getType();
                dislikeReasons = new Gson().fromJson(jsonObject.optString("dislikeReasons"), type);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void parseAlbum(JSONObject album) throws JSONException {
        super.parseAlbum(album);

        if (album.has("dislikeReasons")) {
            Type type = new TypeToken<List<DislikeReason>>() {
            }.getType();
            dislikeReasons = new Gson().fromJson(album.optString("dislikeReasons"), type);
        }

    }
}
