package com.ximalaya.ting.lite.main.history;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.MenuDialog;
import com.ximalaya.ting.android.framework.view.image.FlexibleRoundImageView;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.ICloudyHistory;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.album.fragment.AlbumRecListFragment;
import com.ximalaya.ting.lite.main.base.BaseMainAlbumAdapter;

import java.util.List;

/**
 * <AUTHOR>
 */

public class HistoryAlbumAdapter extends BaseMainAlbumAdapter {
    private final String mTrackingCamp = "训练营";
    private final String mTrackingCampDivider = " | ";
    private final String mTrackingCampNoTaskContent = "专属互动群，立即进群互动";

    public HistoryAlbumAdapter(MainActivity activity, List<Album> listData, boolean isChooseType) {
        super(activity, listData);

        mIsChooseType = isChooseType;
    }

    @Override
    public int getConvertViewIdByPosition(int position) {
        return R.layout.main_item_album_history;
    }

    @Override
    public BaseViewHolder buildHolderByPosition(View convertView, int position) {
        return new HistoryViewHolder(convertView);
    }

    @Override
    public void onClick(View view, final Album album, int position, BaseViewHolder holder) {
        if (view.getId() == R.id.main_iv_album_delete) {
            new DialogBuilder(mFragment.getActivity()).setMessage("确定删除该条播放记录？")
                    .setOkBtn("确定", () -> {
                        if (album instanceof AlbumM) {
                            final HistoryModel historyModel = ((AlbumM) album).getHistoryModel();
                            ICloudyHistory historyManager = RouterServiceManager.getInstance().getService(
                                    ICloudyHistory.class);
                            if (historyManager != null) {
                                historyManager.deletePlayHistory(historyModel);
                            }
                        }
                    }).showConfirm();
        } else if (view.getId() == R.id.main_iv_find_relative) {
            Bundle argumentFromDef = AlbumRecListFragment.createArgumentUseAlbumIdFromDef(album.getId(), AlbumRecListFragment.FROM_PLAY_HISTORY);
            AlbumRecListFragment albumRecListFragment = new AlbumRecListFragment();
            albumRecListFragment.setArguments(argumentFromDef);
            startFragment(albumRecListFragment);
        }
    }

    private Drawable mCompoundDrawable;
    private boolean mIsChooseType = false;
    public BaseFragment mFragment;

    @Override
    public void bindViewDatas(BaseViewHolder h, Album album, int position) {
        HistoryViewHolder holder = (HistoryViewHolder) h;

        if (!(album instanceof AlbumM)) {
            return;
        }
        HistoryModel historyModel = ((AlbumM) album).getHistoryModel();

        String subtitleTitle;
        if (historyModel.isRadio) {
            if (TextUtils.isEmpty(historyModel.getRadio().getProgramName())) {
                subtitleTitle = AppConstants.LIVE_PROGRAM_DEFAULT_TEXT;
            } else {
                subtitleTitle = "上次收听的节目 : " + historyModel.getRadio().getProgramName();
            }
            long endedAt = historyModel.getEndedAt() != 0 ? historyModel.getEndedAt() : historyModel.getUpdateAt();
            holder.lastPlayTime.setText("上次收听时间: " + StringUtil.getFriendlyDataStr2(endedAt));
            holder.lastPlayTime.setCompoundDrawables(null, null, null, null);
            holder.lastPlayPercent.setText("");

            holder.ivFindRelative.setVisibility(View.INVISIBLE);
        } else {
            subtitleTitle = historyModel.getTrack().getTrackTitle();
            holder.lastPlayTime.setText(StringUtil.toTime(historyModel.getTrack().getDuration()));
            if (mCompoundDrawable == null) {
                mCompoundDrawable = LocalImageUtil.getDrawable(context, R.drawable.main_ic_common_play_duration);
            }

            holder.lastPlayTime.setCompoundDrawables(mCompoundDrawable, null, null, null);
            int lastPos = XmPlayerManager.getInstance(context).getHistoryPos(historyModel.getTrack().getDataId());
            if (!mIsChooseType)//听友圈选择专辑不加
                holder.lastPlayPercent.setText(ToolUtil.getPlaySchedule(lastPos, historyModel.getTrack().getDuration()));
            else holder.lastPlayPercent.setText("");

            if (!mIsChooseType) {
                holder.ivFindRelative.setVisibility(View.VISIBLE);
                setClickListener(holder.ivFindRelative, album, position, holder);
                AutoTraceHelper.bindData(holder.ivFindRelative, album);
            } else {
                holder.ivFindRelative.setVisibility(View.INVISIBLE);
            }
        }

        if (((AlbumM) album).isHiddenAlbum(AlbumM.TYPE_HIDDEN_ALBUM_HISTORY)) {
            // 隐藏专辑  标题显示子标题名称
            holder.title.setText(subtitleTitle);
            holder.subtitle.setVisibility(View.INVISIBLE);
            holder.ivFindRelative.setVisibility(View.INVISIBLE);
        } else {
            Spanned richTitle = getRichTitle(album);
            if (!TextUtils.isEmpty(richTitle)) {
                holder.title.setText(richTitle);
            } else {
                holder.title.setText(album.getAlbumTitle());
            }
            holder.subtitle.setVisibility(View.VISIBLE);
            holder.subtitle.setText(subtitleTitle);
            if (!historyModel.isRadio && !mIsChooseType) {
                holder.ivFindRelative.setVisibility(View.VISIBLE);
            } else {
                holder.ivFindRelative.setVisibility(View.INVISIBLE);
            }
        }

        ImageManager.from(context).displayImage(holder.cover, album.getValidCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145);

        //Item增加contentDescription
        StringBuilder builder = new StringBuilder();
        if (!TextUtils.isEmpty(historyModel.getAlbumTitle())) {
            builder.append(historyModel.getAlbumTitle());
        }
        if (!TextUtils.isEmpty(subtitleTitle)) {
            builder.append("            ")//增加空格增目的是增加朗读停顿
                    .append(subtitleTitle);
        }
        holder.root.setContentDescription(builder.toString());

        if (!TextUtils.isEmpty(((AlbumM) album).getTimeTag())) {
            holder.timeTitle.setVisibility(View.VISIBLE);
            holder.timeTitle.setText(((AlbumM) album).getTimeTag());
            if (position > 0) {
                holder.divider.setVisibility(View.GONE);
            }
        } else {
            holder.timeTitle.setVisibility(View.GONE);
            holder.divider.setVisibility(View.GONE);
        }

        if (!mIsChooseType) {
            holder.ivDelete.setVisibility(View.VISIBLE);
            setClickListener(holder.ivDelete, album, position, holder);
            AutoTraceHelper.bindData(holder.ivDelete, album);
        } else {
            holder.ivDelete.setVisibility(View.INVISIBLE);
        }

        if (ChildProtectManager.isChildProtectOpen(context)) {
            holder.ivFindRelative.setVisibility(View.INVISIBLE);
        }

        if (((AlbumM) album).getCampGroupId() != 0) {
            StringBuilder campContemtBuilder = new StringBuilder();
            campContemtBuilder.append(mTrackingCamp).append(mTrackingCampDivider).append(mTrackingCampNoTaskContent);
            SpannableString ss = new SpannableString(campContemtBuilder);
            ForegroundColorSpan fcsBlue = new ForegroundColorSpan(0xFF4990E2);
            ss.setSpan(fcsBlue, campContemtBuilder.indexOf(mTrackingCamp),
                    campContemtBuilder.indexOf(mTrackingCamp) + mTrackingCamp.length(),
                    Spanned.SPAN_INCLUSIVE_INCLUSIVE);
            ForegroundColorSpan fcsGray = new ForegroundColorSpan(0xFFE8E8E8);
            ss.setSpan(fcsGray, campContemtBuilder.indexOf(mTrackingCampDivider),
                    campContemtBuilder.indexOf(mTrackingCampDivider) + mTrackingCampDivider.length(),
                    Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        }

        if (AlbumTagUtil.getAlbumCoverTag((AlbumM) album) != -1) {
            AlbumM albumM = (AlbumM) album;
            albumM.setFromPage(AlbumM.PAGE_HISTORY);
            holder.ivTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable((AlbumM) album, context, AlbumTagUtil.ZOOM_IN_RATIO_78_percent));
            holder.ivTag.setVisibility(View.VISIBLE);
        } else {
            holder.ivTag.setVisibility(View.INVISIBLE);
        }
    }

    private static class HistoryViewHolder extends BaseMainAlbumHolder {
        public TextView subtitle;
        public TextView lastPlayTime;
        public TextView lastPlayPercent;
        ImageView ivFindRelative;
        ImageView ivDelete;
        public TextView timeTitle;
        public View divider;
        FlexibleRoundImageView ivTag;

        public HistoryViewHolder(View convertView) {
            super(convertView);

            subtitle = convertView.findViewById(R.id.main_tv_album_subtitle);
            lastPlayTime = convertView.findViewById(R.id.main_tv_last_play_time);
            lastPlayPercent = convertView.findViewById(R.id.main_tv_last_play_percent);
            ivDelete = convertView.findViewById(R.id.main_iv_album_delete);
            ivFindRelative = convertView.findViewById(R.id.main_iv_find_relative);
            timeTitle = convertView.findViewById(R.id.main_time_title);
            divider = convertView.findViewById(R.id.main_divider);
            ivTag = convertView.findViewById(R.id.main_iv_album_cover_tag);
        }
    }

}
