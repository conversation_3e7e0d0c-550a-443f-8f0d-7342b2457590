package com.ximalaya.ting.lite.main.download;

import android.content.Context;
import android.graphics.Color;
import androidx.collection.ArraySet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.view.text.AdaptiveTextView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Created by xmly on 10/08/2018.
 *
 * <AUTHOR>
 */
public class BatchPagerAdapter extends HolderAdapter<BatchPagerAdapter.PageIndex> {

    private int mPageId = 1;

    public BatchPagerAdapter(Context context, List<PageIndex> listData) {
        super(context, listData);
    }

    /**
     * 注意：选集面板的顺序要根据 当前专辑排序（isRecordDesc）^ 选择的正倒序（isAsc） =true 就正序显示，=false就倒序显示
     *
     * @param pageSize
     * @param trackCounts
     * @param isAsc       是否为升序（orderNo:1/2/3/4/5/...）否则（orderNo:.../5/4/3/2/1）
     * @return
     */
    public static List<PageIndex> computePagerIndex(int pageSize, int trackCounts, boolean isAsc) {
        List<PageIndex> mPagerIndexs = new ArrayList<>();
        if (pageSize == 0)
            pageSize = DTransferConstants.DEFAULT_PAGE_SIZE;
        int group = (int) Math.ceil(trackCounts / (float) pageSize);
        for (int i = 0; i < group; i++) {
            PageIndex pi = new PageIndex();
            pi.setPageIndex(i + 1);
            if (isAsc) {
                pi.setStartIndex(pageSize * i + 1);
                pi.setEndIndex(Math.min(pageSize * (i + 1), trackCounts));
                pi.setPageString(pi.getStartIndex() + "~" + pi.getEndIndex());
            } else {
                pi.setStartIndex(trackCounts - pageSize * i);
                pi.setEndIndex(Math.max(trackCounts - pageSize * (i + 1) + 1, 1));
                pi.setPageString(pi.getStartIndex() + "~" + pi.getEndIndex());
            }
            mPagerIndexs.add(pi);
        }
        return mPagerIndexs;
    }

    public void setPageId(int pageId) {
        this.mPageId = pageId;
    }

    @Override
    public void onClick(View view, PageIndex t, int position,
                        BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        AdaptiveTextView tv;
        if (convertView == null) {
            tv = new AdaptiveTextView(context);
            tv.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
            tv.setHeight(BaseUtil.dp2px(context, 30));
            tv.setTextSize(15);
            tv.setTextColor(Color.parseColor("#f5f8fa"));
            tv.setGravity(Gravity.CENTER);
            tv.setSingleLine();
            tv.setBackgroundResource(R.drawable.main_album_pager_item_bg_rect_gray);
            convertView = tv;
        }
        tv = (AdaptiveTextView) convertView;
        PageIndex pi = listData.get(position);
        tv.setText(pi.pageString);
        tv.setBackgroundResource(pi.pageIndex == mPageId
                ? R.drawable.main_album_pager_item_bg_rect_orange
                : (pi.hasSelectedTrack()
                ? R.drawable.main_batch_down_pager_has_child_selected_bg
                : R.drawable.main_album_pager_item_bg_rect_gray));
        tv.setTextColor(Color.parseColor(pi.pageIndex == mPageId ? "#ffffff" : context.getString(R.string.main_color_black)));
        return convertView;
    }


    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new BaseViewHolder();
    }


    @Override
    public void bindViewDatas(BaseViewHolder holder, PageIndex t, int position) {

    }

    public static class PageIndex {
        private int pageIndex;
        private int startIndex;
        private int endIndex;
        public String pageString;
        private AlbumM pageData;
        private Set<Track> selectedSet;

        public PageIndex() {
            selectedSet = new ArraySet<>(0);
        }

        public int getPageIndex() {
            return pageIndex;
        }

        public void setPageIndex(int pageIndex) {
            this.pageIndex = pageIndex;
        }

        public int getStartIndex() {
            return startIndex;
        }

        public void setStartIndex(int startIndex) {
            this.startIndex = startIndex;
        }

        public int getEndIndex() {
            return endIndex;
        }

        public void setEndIndex(int endIndex) {
            this.endIndex = endIndex;
        }

        public String getPageString() {
            return pageString;
        }

        public void setPageString(String pageString) {
            this.pageString = pageString;
        }

        public void setSelectedTrack(Track track) {
            if (track.getExtra()) {
                selectedSet.add(track);
            } else {
                selectedSet.remove(track);
            }
        }

        public void clearSelectedTrack() {
            if (!ToolUtil.isEmptyCollects(selectedSet)) {
                for (Track t : selectedSet) {
                    t.setExtra(false);
                }
                selectedSet.clear();
            }
        }

        public boolean hasSelectedTrack() {
            return !ToolUtil.isEmptyCollects(selectedSet);
        }

        public Collection<Track> getSelectedTrack() {
            return selectedSet;
        }

        public AlbumM getPageData() {
            return pageData;
        }

        public void setPageData(AlbumM pageData) {
            this.pageData = pageData;
        }

        public void syncChooseStatus() {
            CommonTrackList tl = null;
            List<TrackM> list = null;
            if (pageData != null
                    && (tl = pageData.getCommonTrackList()) != null
                    && !ToolUtil.isEmptyCollects(list = tl.getTracks())) {
                for (TrackM tm : list) {
                    setSelectedTrack(tm);
                }
            }

        }
    }
}
