package com.ximalaya.ting.lite.main.history;

import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.util.Log;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshRecyclerView;
import com.ximalaya.ting.android.host.db.model.BookHistoryInfo;
import com.ximalaya.ting.android.host.db.repository.BookHistoryRecordRepository;
import com.ximalaya.ting.android.host.db.repository.BookHistoryRepository;
import com.ximalaya.ting.android.host.db.utils.BookHistoryUtils;
import com.ximalaya.ting.android.host.db.utils.BookUtils;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.util.ReadUtils;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.book.bean.BookWrapperBean;
import com.ximalaya.ting.lite.main.history.adapter.BookHistoryListAdapter;
import com.ximalaya.ting.lite.main.history.presenter.BookHistoryPresenter;
import com.ximalaya.ting.lite.main.history.presenter.IBookHistoryPresenter;
import com.ximalaya.ting.lite.main.history.view.IBookHistoryView;
import com.ximalaya.ting.lite.main.mylisten.inter.IHistoryCallBack;
import com.ximalaya.ting.lite.main.mylisten.view.AllHistoryFragment;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 读历史记录
 */
public class ReadHistoryFragment extends BaseFragment2 implements IHistoryCallBack {

    private static final String TAG = "ReadHistoryFragment";

    private RefreshRecyclerView mRecyclerView;

    private final List<BookWrapperBean<?>> mBookHistoryList = new ArrayList<>();

    private BookHistoryListAdapter mBookHistoryListAdapter;

    private IBookHistoryPresenter mBookHistoryPresenter;

    private DialogBuilder<?> mRemoveOneBookDialog;

    private long mCurTime;

    private final Handler mHandler = new Handler();

    @Override
    protected String getPageLogicName() {
        return getClass().getSimpleName();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRecyclerView = findViewById(R.id.main_rv_history_list);
        setScrollViewListener(mRecyclerView);
        mRecyclerView.getRefreshableView().setLayoutManager(new GridLayoutManager(getContext(), 3));
        mRecyclerView.setMode(PullToRefreshBase.Mode.PULL_FROM_START);

        mBookHistoryListAdapter = new BookHistoryListAdapter(mContext, mBookHistoryList);
        mRecyclerView.setAdapter(mBookHistoryListAdapter);

        mBookHistoryPresenter = new BookHistoryPresenter(new IBookHistoryView() {
            @Override
            public void setData(@Nullable List<BookHistoryInfo> list) {
                if (canUpdateUi()) {
                    setRecyclerViewData(list);
                }
            }

            @Override
            public void setTotalCount(int totalCount) {

            }

            @Override
            public void loadDataEnd() {
                mRecyclerView.onRefreshComplete(false);
            }
        });

        initListener();

        onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
    }

    @Override
    protected boolean onPrepareNoContentView() {
        setNoContentImageView(R.drawable.host_no_content);
        setNoContentTitle("没有阅读过书籍");
        return false;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_read_history_layout;
    }

    private void initListener() {
        mRecyclerView.setOnRefreshLoadMoreListener(new IRefreshLoadMoreListener() {
            @Override
            public void onRefresh() {
                requestData();
            }

            @Override
            public void onMore() {

            }
        });

        mBookHistoryListAdapter.setOnBookClickListener(new BookHistoryListAdapter.onBookClickListener() {
            @Override
            public void onClickBook(int position) {
                BookWrapperBean<?> wrapperBean = mBookHistoryList.get(position);
                BookHistoryInfo bookInfo = null;

                if (wrapperBean != null && wrapperBean.getData() instanceof BookHistoryInfo) {
                    bookInfo = (BookHistoryInfo) wrapperBean.getData();
                }

                // 书籍下架
                if (bookInfo == null || bookInfo.isOffShelf()) {
                    return;
                }

                mBookHistoryList.remove(position);
                bookInfo.setLastUpdatedTime(BookUtils.INSTANCE.getLastUpdatedTime());
                // 移动到第一本
                mBookHistoryList.add(0, wrapperBean);

                //跳转到阅读器
                ReadUtils.startToReader(bookInfo.getBookId());

                // 延时刷新
                mHandler.postDelayed(() -> mBookHistoryListAdapter.notifyDataSetChanged(), 500);
            }

            @Override
            public void onLongClickBook(int position) {
                BookWrapperBean<?> bookWrapperBean = mBookHistoryList.get(position);

                String bookId = "";
                if (bookWrapperBean != null && bookWrapperBean.getData() instanceof BookHistoryInfo) {
                    BookHistoryInfo bookInfo = (BookHistoryInfo) bookWrapperBean.getData();
                    bookId = String.valueOf(bookInfo.getBookId());
                }
                // 移出阅读历史弹窗  弹框展示
                new XMTraceApi.Trace()
                        .setMetaId(39445)
                        .setServiceId("dialogView")
                        .put("bookId", bookId)
                        .put("currPage", "historyPage")
                        .createTrace();
                // 删除
                String finalBookId = bookId;
                showDelBookDialog("确认将本书移出历史", () -> {
                    // 移出阅读历史弹窗-确认点击  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(39446)
                            .setServiceId("dialogClick")
                            .put("bookId", finalBookId)
                            .put("currPage", "historyPage")
                            .createTrace();

                    BookWrapperBean<?> wrapperBean = mBookHistoryList.remove(position);
                    mBookHistoryListAdapter.notifyDataSetChanged();

                    if (wrapperBean != null && wrapperBean.getData() instanceof BookHistoryInfo) {
                        BookHistoryInfo bookInfo = (BookHistoryInfo) wrapperBean.getData();
                        BookHistoryUtils.INSTANCE.operatingBooks(bookInfo.getBookId(), "",
                                "", bookInfo.getReadChapterId(),
                                "true".equals(bookInfo.getExtensionField1()), BookUtils.TYPE_DEL);
                        mBookHistoryPresenter.syncBookHistoryModifyRecord();
                    }
                    checkCleanAll();
                }, () -> {
                    // 移出阅读历史弹窗-取消点击  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(39447)
                            .setServiceId("dialogClick")
                            .put("bookId", finalBookId)
                            .put("currPage", "historyPage")
                            .createTrace();
                });
            }
        });
    }

    private void showDelBookDialog(String msg, DialogBuilder.DialogCallback callback, DialogBuilder.DialogCallback cancelCallback) {
        if (mRemoveOneBookDialog == null) {
            mRemoveOneBookDialog = new DialogBuilder<>(mActivity);
        }
        mRemoveOneBookDialog.setTitleVisibility(false)
                .setMessage(msg)
                .setOkBtn(callback)
                .setCancelBtn(cancelCallback);

        if (!mRemoveOneBookDialog.isShowing()) {
            mRemoveOneBookDialog.showConfirm();
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        Log.e("ReadHistoryFragment=", "setUserVisibleHint==isVisibleToUser:" + isVisibleToUser);
        // 界面可见时触发
        if (isRealVisable()) {
            FuliLogger.log(TAG, "setUserVisibleHint:" + isRealVisable());
            requestData();
        }
    }

    private void requestData() {
        if (mBookHistoryPresenter != null) {
            FuliLogger.log(TAG, "requestData");
            mBookHistoryPresenter.loadData();
        }
    }

    @Override
    protected void loadData() {
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        Log.e("ReadHistoryFragment=", "onMyResume==");
        // 界面可见时加载数据  防止频繁刷新
        long sysTime = SystemClock.elapsedRealtime();
        if (sysTime - mCurTime > 5000L) {
            FuliLogger.log(TAG, "onMyResume requestData");
            mCurTime = sysTime;
            requestData();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.e("ReadHistoryFragment=", "onResume==");
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.e("ReadHistoryFragment=", "onPause==");
    }


    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        Log.e("ReadHistoryFragment=", "onHiddenChanged==hidden:" + hidden);
    }

    private void setRecyclerViewData(List<BookHistoryInfo> list) {
        // 清除所有数据  因为每次都是从数据库获取全量数据
        mBookHistoryList.clear();

        if (CollectionUtil.isNotEmpty(list)) {
            HashSet<Long> hashSet = new HashSet<>();
            // 数据去重
            for (BookHistoryInfo bookInfo : list) {
                if (bookInfo == null) {
                    continue;
                }
                // 判断是否存在同一本书
                if (hashSet.contains(bookInfo.getBookId())) {
                    FuliLogger.log(TAG, "存在同一本书:" + bookInfo.getBookName() + " uid:" + bookInfo.getUid());
                } else {
                    hashSet.add(bookInfo.getBookId());
                    mBookHistoryList.add(new BookWrapperBean<>(bookInfo));
                }
            }
        }

        mBookHistoryListAdapter.notifyDataSetChanged();
        checkCleanAll();
    }

    @Override
    public void clearAll() {
        if (mBookHistoryList.isEmpty()) {
            return;
        }

        // 清空阅读历史弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(39442)
                .setServiceId("dialogView")
                .put("currPage", "historyPage")
                .createTrace();

        // 删除
        showDelBookDialog("确认清空阅读历史", () -> {

            // 清空阅读历史弹窗-确认点击  弹框控件点击
            new XMTraceApi.Trace()
                    .setMetaId(39443)
                    .setServiceId("dialogClick")
                    .put("currPage", "historyPage")
                    .createTrace();

            if (!UserInfoMannage.hasLogined()) {
                mBookHistoryList.clear();
                mBookHistoryListAdapter.notifyDataSetChanged();
                checkCleanAll();

                // 清除本地记录和本地操作记录  只能删除当前用户的数据
                BookHistoryRepository.INSTANCE.removeAll();
                BookHistoryRecordRepository.INSTANCE.removeAll();
                return;
            }

            onPageLoadingCompleted(LoadCompleteType.LOADING);

            // 接口请求成功后再删除
            mBookHistoryPresenter.clearAllBookHistoryRecord(new IDataCallBack<Boolean>() {
                @Override
                public void onSuccess(@Nullable Boolean object) {
                    mBookHistoryList.clear();
                    mBookHistoryListAdapter.notifyDataSetChanged();
                    checkCleanAll();
                }

                @Override
                public void onError(int code, String message) {
                    if (canUpdateUi()) {
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        CustomToast.showToast(message);
                    }
                }
            });
        }, () -> {
            // 清空阅读历史弹窗-取消点击  弹框控件点击
            new XMTraceApi.Trace()
                    .setMetaId(39444)
                    .setServiceId("dialogClick")
                    .put("currPage", "historyPage")
                    .createTrace();
        });
    }

    private void checkCleanAll() {
        boolean isNoData = mBookHistoryList.isEmpty();
        checkDelViewVisible(isNoData ? View.GONE : View.VISIBLE);

        onPageLoadingCompleted(isNoData ? LoadCompleteType.NOCONTENT : LoadCompleteType.OK);
    }

    public void checkDelViewVisible(int visible) {
        Fragment fragment = getParentFragment();
        if (fragment instanceof AllHistoryFragment) {
            AllHistoryFragment allHistoryFragment = (AllHistoryFragment) fragment;
            allHistoryFragment.checkDelBtnVisible(visible, 2);
        }
    }
}
