package com.ximalaya.ting.lite.main.shortplay

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Point
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.SeekBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.airbnb.lottie.LottieAnimationView
import com.ximalaya.ting.android.downloadservice.base.IDownloadStatus
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constants.LoginByConstants
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener
import com.ximalaya.ting.android.host.manager.ShortPlayHistoryManager
import com.ximalaya.ting.android.host.manager.ShortPlayPlayTimeManager
import com.ximalaya.ting.android.host.manager.ShortPlayTrackManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.duanju.XmDuanjuTimeTraceParamsModel
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumVideoInfoModel
import com.ximalaya.ting.android.host.model.album.VideoBaseInfo
import com.ximalaya.ting.android.host.model.duanju.XmDuanJuItemTransferModel
import com.ximalaya.ting.android.host.util.RouteServiceUtil
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.util.visible
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.player.video.listener.IVideoDataInterceptor
import com.ximalaya.ting.android.player.video.listener.IXmVideoPlayStatusListener
import com.ximalaya.ting.android.player.video.player.IMediaPlayer
import com.ximalaya.ting.android.video.util.VideoViewUtil
import com.ximalaya.ting.android.xmplaysdk.IMediaPlayerControl
import com.ximalaya.ting.android.xmplaysdk.IRenderView
import com.ximalaya.ting.android.xmutil.INetworkChangeListener
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.android.xmutil.NetworkType
import com.ximalaya.ting.lite.main.manager.ShortPlayFreeListenConfigManager
import com.ximalaya.ting.lite.main.manager.ShortPlayFreeListenManager
import com.ximalaya.ting.lite.main.play.manager.TempoManager
import com.ximalaya.ting.lite.main.shortplay.listener.IShortPlayDetailListener
import com.ximalaya.ting.lite.main.shortplay.listener.IVideoContainer
import com.ximalaya.ting.lite.main.shortplay.utils.TrackTitleUtil
import com.ximalaya.ting.lite.main.shortplay.utils.XMScreenUtils
import com.ximalaya.ting.lite.main.shortplay.view.XLandScapeTitle
import com.ximalaya.ting.lite.main.shortplay.view.XLandScapeVideoControl
import com.ximalaya.ting.lite.main.tab.presenter.XVideoPlayListPresenter
import com.ximalaya.ting.lite.main.utils.isExtremelySmallDevice
import com.ximalaya.ting.lite.main.view.ScaleableSeekBar
import com.ximalaya.ting.lite.main.view.ShortPlayExpandableContentTextView
import java.io.File


class VideoImmersiveDetailFragment(
    val totalCount: Int,
    private val displayId: Int,
    private val mVideoPlayListPresenter: XVideoPlayListPresenter
) : BaseFragment2(), IVideoContainer {

    private var mVgVideoPlayerContainer: ViewGroup? = null
    private var mVideoPlayHasInit = false
    private var mCurPlayPath: String? = null
    private var mVideoPlayer: IMediaPlayerControl? = null
    private var mRootView: View? = null
    private var mRootLanScapeView: ConstraintLayout? = null

    private var mIvPlayBtn: ImageView? = null
    private var mSeekBar: ScaleableSeekBar? = null
    private var mTvProgress: TextView? = null
    private var mTvFloatingProgress: TextView? = null

    private var mIsVideoPortrait: Boolean? = null // 视频加载前不知道横竖屏，先赋值为null
    private var mVideoAspect: Int = -1 // 视频加载前不知道横竖屏，先赋值为 -1
    private var mGroupErrorState: Group? = null
    private var mGroupNoNetwork: Group? = null
    private var mGroupCommercialInfo: Group? = null
    private var mCommercialInfoView: ViewGroup? = null

    private var mTvRefreshBtn: TextView? = null

    private var mIsInLandscapeFullscreen = false

    private var mVideoBaseInfo: VideoBaseInfo? = null

    // 全屏上下渐变阴影 非全屏不展示
    private var fullScreenWidgets: Group? = null
    private var topMask: View? = null

    private var likeBtn: LottieAnimationView? = null
    private var likeCountTextView: TextView? = null
    private var collectBtn: LottieAnimationView? = null
    private var collectCountTextView: TextView? = null

    private var coverBackgroundIv: ImageView? = null
    private var commentBtn: ImageView? = null
    private var commentCountTextView: TextView? = null
    private var mIvShareBtn: ImageView? = null
    private var mShareCountText: TextView? = null
    private var mTvAuthorName: TextView? = null

    private var mBottomMaskView: View? = null
    private var mTvVideoTitleSketch: TextView? = null
    private var mTvVideoIntroSketch: ShortPlayExpandableContentTextView? = null

    private var ivLandscape: TextView? = null

    private var playListEntry: View? = null

    // 短剧信息
    private var rootTrackAnchorSketchPart: ViewGroup? = null

    // 屏幕右侧功能
    private var rootFunctionPart: ViewGroup? = null
    private var seekbarContain: ViewGroup? = null
    private var ivFullEntry: ImageView? = null
    private var isLike = false
    private var isCollect = false
    private var tvPlayList: TextView? = null

    private var llTitleView: ViewGroup? = null

    private val mNetworkChangeListener =
        INetworkChangeListener { _, _, netWorkType, _ -> handleNetworkChanged(netWorkType) }
    private var mCommentDialogShowing = false
    private var mNeedPlayNextWhileCommentDialogHide = false

    // 退出后再回来要不要恢复播放
    private var mIsNeedResumePlay = true

    // 因为需要购买解锁而停止
    private var mIsStopByCommercialInfo: Boolean = false

    // 是否可见
    private var isRealVisibleVideo = false

    // 用户手动暂停
    private var pauseByUser = false

    // 回到后台继续播放
    private var isContinuePlay = false

    private var setPlayPathTs = 0L

    private var firstPosition = -1L

    private var style = ""
    private var type = ""

    companion object {
        private const val TAG = "VideoDetailFragment"
        const val BUNDLE_KEY_DATA = "data"
        const val BUNDLE_KEY_DATA_TRACK_ID = "video_track_id"
        const val BUNDLE_KEY_DATA_POSITION = "video_track_position"
        const val BUNDLE_KEY_DATA_STYLE = "video_track_style"
        const val BUNDLE_KEY_DATA_TYPE = "video_track_type"

        private var sNeedShowMobileToast = true

        //0 流程  1 高清   2 超清
        private var videoQuality: Int = -1
        private var videoQualityChangedByUser = false
    }

    private var mVideoInfo: AlbumVideoInfoModel.AlbumVideoInfo? = null
    private var currentTrackId: Long? = null

    private var isFromDuanju = true
    private var isFullScreen = false

    private var mSeekBarWindowBar: SeekBar? = null
    private var mSeekBarWindow: ViewGroup? = null
    private var mSeekBarWindowCurPosition: TextView? = null
    private var mSeekBarWindowTotalPosition: TextView? = null
    private var rootVideo: ViewGroup? = null

    //长按3倍速播放--start
    //是否长按加速中
    private var mRootViewLongPressAccelerateing=false;
    private var mRootViewLongPressAccelerateBeforeSpeedValue=1.0f;
    private var mPlayLongPressSeedTips:TextView?=null;
    private var mPlayLongPressActionDown:Point= Point();
    //长按3倍速播放--end

    var playListenerListener: IShortPlayDetailListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parseArguments()
    }

    override fun getPageLogicName() = "VideoImmersiveDetailFragment"

    @SuppressLint("ClickableViewAccessibility")
    override fun initUi(savedInstanceState: Bundle?) {
        printAllLog("initUi")
        mRootView = view
        initPlayer()
        coverBackgroundIv = findViewById(R.id.main_iv_cover)

        if (mVideoInfo?.isAuthorized == false) {
            setBackgroundForCover()
        }
        ivFullEntry = findViewById(R.id.main_iv_video_full_entry)
        ivFullEntry?.setOnClickListener {

        }
        mRootView?.setOnClickListener(mOnClickListener)


        //处理长按加速逻辑---start
        mPlayLongPressSeedTips=findViewById(R.id.main_play_long_press_seed_tips)
        mRootView?.setOnLongClickListener {
            //当前没有处于播放中，则不触发长安事件
            if (mVideoPlayer!=null && !mVideoPlayer!!.isPlaying){
                return@setOnLongClickListener true;
            }
            Log.e("qinhuifeng889900=", "触发长按事件=上次的点击位置:$mPlayLongPressActionDown")

            var screenWidth=BaseUtil.getScreenWidth(mContext)
            Log.e("qinhuifeng889900=", "屏幕宽度:$screenWidth")

            //中间区域不key点击
            if (mPlayLongPressActionDown.x>screenWidth*0.3 && mPlayLongPressActionDown.x<screenWidth*0.6){
                Log.e("qinhuifeng889900=", "中间区域不触发长按加速:$screenWidth")
                return@setOnLongClickListener true;
            }
            mRootViewLongPressAccelerateing = true;
            mRootViewLongPressAccelerateBeforeSpeedValue = getSpeed();
            Log.e("qinhuifeng889900=", "长按了==加速=old速度=$mRootViewLongPressAccelerateBeforeSpeedValue")
            setSpeed(3.0f)
            if (mPlayLongPressSeedTips?.layoutParams is MarginLayoutParams){
                var topMargin=if (isLandScape()){
                    50f;
                }else{
                    100f
                }
                (mPlayLongPressSeedTips!!.layoutParams as MarginLayoutParams).topMargin=BaseUtil.dp2px(mContext,topMargin);
                mPlayLongPressSeedTips!!.layoutParams=mPlayLongPressSeedTips!!.layoutParams;
            }
            mPlayLongPressSeedTips?.visibility=View.VISIBLE
            //返回true，禁用长按触发点击事件
            return@setOnLongClickListener true;
        };
        mRootView?.setOnTouchListener { v, event ->
            if (event?.action == MotionEvent.ACTION_UP || event?.action == MotionEvent.ACTION_CANCEL) {
                if (mRootViewLongPressAccelerateing) {
                    Log.e("qinhuifeng889900=", "释放了==恢复=$mRootViewLongPressAccelerateBeforeSpeedValue")
                    setSpeed(mRootViewLongPressAccelerateBeforeSpeedValue);
                }
                mRootViewLongPressAccelerateing = false;
                mPlayLongPressSeedTips?.visibility=View.GONE
            }else if (event?.action == MotionEvent.ACTION_DOWN){
                //记录点击的位置
                mPlayLongPressActionDown.x= event.rawX.toInt()
                mPlayLongPressActionDown.y=event.rawY.toInt()
                Log.e("qinhuifeng889900=", "ACTION_DOWN位置=$mPlayLongPressActionDown")
            }
            false;
        };
        //处理长按加速逻辑---end



        mRootLanScapeView = findViewById(R.id.main_landscape_root_view)
        seekbarContain = findViewById(R.id.main_seek_bar_container)
        rootVideo = findViewById(R.id.main_video_root)
        mSeekBarWindow = findViewById<RelativeLayout>(R.id.main_layout_short_video_seek_window)
        mSeekBarWindowCurPosition =
            findViewById(R.id.main_short_video_seek_window_current_position)
        mSeekBarWindowTotalPosition =
            findViewById(R.id.main_short_video_seek_window_total_position)
        mSeekBarWindowBar = findViewById(R.id.main_short_video_seek_window_seekbar)
        mSeekBarWindowBar?.max = 1000
        rootFunctionPart = findViewById(R.id.main_play_video_functions_immersive)
        rootTrackAnchorSketchPart = findViewById(R.id.main_play_sketch_video_immersive_track_info)
        mIvPlayBtn = findViewById(R.id.main_iv_play_btn)
        mTvProgress = findViewById(R.id.main_tv_progress)
        mTvFloatingProgress = findViewById(R.id.main_tv_progress_floating)
        mTvVideoIntroSketch = findViewById(R.id.main_video_album_intro_sketch)
        mTvVideoTitleSketch = findViewById(R.id.main_video_album_title_sketch)
        mBottomMaskView = findViewById(R.id.main_video_bottom_mask)
        mTvVideoTitleSketch?.setOnClickListener(mOnClickListener)
        ivLandscape = findViewById(R.id.main_play_video_landscape_btn)
        if (BaseUtil.isFoldScreen(context)) {
            ViewStatusUtil.setVisible(View.GONE, ivLandscape)
        }
        ivLandscape?.setOnClickListener {
            enterLandScape()
            ShortPlayTrackManager.trackClickAlbumTitle(mVideoInfo, "横屏",getXmDuanJuItemTransferModel())
        }
        mShareCountText = findViewById(R.id.main_iv_play_video_share_text)
        likeBtn = findViewById(R.id.main_iv_play_video_like)
        likeBtn?.setOnClickListener(mOnClickListener)
        likeCountTextView = findViewById(R.id.main_iv_play_video_like_text)
        likeCountTextView?.setOnClickListener(mOnClickListener)
        mIvShareBtn = findViewById(R.id.main_iv_play_video_share)
        mIvShareBtn?.setOnClickListener(mOnClickListener)
        mShareCountText?.setOnClickListener(mOnClickListener)
        collectBtn = findViewById(R.id.main_iv_play_video_fav)
        collectCountTextView = findViewById(R.id.main_iv_play_video_fav_text)
        collectBtn?.setOnClickListener(mOnClickListener)
        collectCountTextView?.setOnClickListener(mOnClickListener)
        commentBtn = findViewById(R.id.main_iv_play_video_comment)
        commentCountTextView = findViewById(R.id.main_iv_play_video_comment_text)
        commentBtn?.setOnClickListener(mOnClickListener)
        commentCountTextView?.setOnClickListener(mOnClickListener)
        mTvAuthorName = findViewById(R.id.main_tv_anchor_name)
        mTvAuthorName?.setOnClickListener(mOnClickListener)
        tvPlayList = findViewById(R.id.main_iv_play_list_text)

        mGroupErrorState = findViewById(R.id.main_group_error)
        mGroupNoNetwork = findViewById(R.id.main_group_no_network)
        mGroupCommercialInfo = findViewById(R.id.main_group_commercial_info)
        mGroupCommercialInfo = findViewById(R.id.main_group_commercial_info)
        mCommercialInfoView = findViewById(R.id.main_rl_commercial_info)
        mTvRefreshBtn = findViewById(R.id.main_tv_refresh_btn)
        mTvRefreshBtn?.setOnClickListener(mOnClickListener)

        initSeekBar()
        fullScreenWidgets = findViewById(R.id.main_group_fullscreen_widgets)
        topMask = findViewById(R.id.main_video_top_mask)
        playListEntry = findViewById(R.id.main_video_play_list_group)
        playListEntry?.setOnClickListener {
            ShortPlayTrackManager.trackClickAlbumTitle(mVideoInfo, "点击竖版播放列表",getXmDuanJuItemTransferModel())
            showPlayList()
        }
        llTitleView = findViewById(R.id.main_ll_title_view)
        findViewById<View?>(R.id.main_iv_back)?.setOnClickListener {
            playListenerListener?.onBack()
        }
        findViewById<View?>(R.id.main_iv_more)?.setOnClickListener {
            ShortPlayTrackManager.trackClickAlbumTitle(mVideoInfo, "点击更多菜单",getXmDuanJuItemTransferModel())
            TempoManager.getInstance().addListener(mTempoListener)
            val mVideoPlayMoreDlgFragment = VideoPlayMoreDlgFragment(
                mVideoInfo?.id, mVideoBaseInfo,
                downloadCallback = { },
                showAllDownload = { },
                report = { },
                copyRight = { },
                isFromDownload = false,
                fragment = this@VideoImmersiveDetailFragment
            )
            mVideoPlayMoreDlgFragment.show(childFragmentManager, "VideoPlayMoreDlgFragment")
        }

        UserInfoMannage.getInstance().addLoginStatusChangeListener(loginStateChangedListener)

        // 用于预加载
        loadVideoInfo()
        bindDataToView()
        collectBtn?.setAnimation("lottie/video/main_video_collect_new.json")
        likeBtn?.setAnimation("lottie/video/main_video_like_new.json")
    }

    override fun loadData() {

    }

    override fun getContainerLayoutId() = R.layout.main_video_xplay_detail_immersive

    private fun enterLandScape() {
        XMScreenUtils.enterLandScape(activity)
        showLandScapeWidgets()
    }

    private var currentProgress: Int = 0
    private var currentDuration: Int = 0
    private var landscapeTitle: XLandScapeTitle? = null
    private var landscapeControlBar: XLandScapeVideoControl? = null

    private var mRemoveLandScapeWidgetsRun = Runnable {
        removeLandScapeWidgets()
    }

    private fun showLandScapeWidgets() {
        HandlerManager.removeCallbacks(mRemoveLandScapeWidgetsRun)
        ensureLandscapeTitle()
        ensureLandscapeControlBar()
        HandlerManager.postOnUIThreadDelay(mRemoveLandScapeWidgetsRun, 5000)
    }

    private fun removeLandScapeWidgets() {
        HandlerManager.removeCallbacks(mRemoveLandScapeWidgetsRun)
        removeLandscapeTitle()
        removeLandscapeControlBar()
    }

    private fun ensureLandscapeTitle() {
        if (landscapeTitle != null) return
        landscapeTitle = XLandScapeTitle(requireActivity()).apply {
            backIcon.setOnClickListener {
                XMScreenUtils.exitLandScape(activity)
            }
            btnShare.visibility = View.INVISIBLE
            title.text = TrackTitleUtil.getTrackTitle(mVideoInfo)
            mRootLanScapeView?.addView(this,
                ConstraintLayout.LayoutParams(
                    ConstraintLayout.LayoutParams.MATCH_PARENT,
                    ConstraintLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    topMargin = 25.dp
                }
            )
        }
    }

    private var mTempoListener = TempoManager.TempoListener { tempo, _ ->
        setSpeed(tempo)
        landscapeControlBar?.speed?.text = TempoManager.getInstance().currentTempoText1
    }

    private fun ensureLandscapeControlBar() {
        if (landscapeControlBar != null) return
        landscapeControlBar = XLandScapeVideoControl(requireActivity()).apply {
            id = R.id.main_xvideo_landscape_control_bar
            videoContainer = this@VideoImmersiveDetailFragment
            videoInfo = mVideoInfo
            onPlayProgress(currentProgress, currentDuration)
            playBtn.isSelected = isPlaying()
            playBtn.setOnClickListener {
                ShortPlayTrackManager.trackClickAlbumTitle(mVideoInfo, "点击横版播放暂停",getXmDuanJuItemTransferModel())
                playOrPause()
            }
            updateNextBtn(playListenerListener?.hasNext(currentTrackId) ?: false)
            nextBtn.setOnClickListener {
                ShortPlayTrackManager.trackClickAlbumTitle(mVideoInfo, "点击横版下一首",getXmDuanJuItemTransferModel())
                playListenerListener?.autoPlayNext()
            }
            speed.text = TempoManager.getInstance().currentTempoText1
            speed.setOnClickListener {
                ShortPlayTrackManager.trackClickAlbumTitle(mVideoInfo, "点击倍速",getXmDuanJuItemTransferModel())
                removeLandScapeWidgets()
                TempoManager.getInstance().addListener(mTempoListener)
                XVideoSpeedLandDialogFragment().show(
                    childFragmentManager, "XVideoSpeedLandDialogFragment"
                )
            }
            ViewStatusUtil.setVisible(View.GONE, qulity, danmu)
            selectEp.setOnClickListener {
                ShortPlayTrackManager.trackClickAlbumTitle(mVideoInfo, "点击横版播放列表",getXmDuanJuItemTransferModel())
                removeLandScapeWidgets()
                XVideoPlayListLandDialogFragmentNew(
                    mVideoInfo, mVideoPlayListPresenter,
                        getXmDuanJuItemTransferModel(),
                        currentTrackId ?: 0,
                    object : XVideoPlayListLandDialogFragmentNew.Callback {
                        override fun clickVideoItem(video: AlbumVideoInfoModel.AlbumVideoInfo) {
                            playListenerListener?.onShortPlayClick(video.trackId)
                            video.apply {
                                ShortPlayTrackManager.xmTraceClickDramaGallery(video,getXmDuanJuItemTransferModel())
                            }
                        }
                    }).show(
                    childFragmentManager, "XVideoPlayListLandDialogFragment"
                )
            }
            playingTime.text = "00:00"
            endTime.text = "00:00"

            mRootLanScapeView?.addView(this,
                ConstraintLayout.LayoutParams(
                    ConstraintLayout.LayoutParams.MATCH_PARENT,
                    ConstraintLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    bottomMargin = 17.dp
                }
            )
            ensureSeekBarMaxAccuracy(100)
            enableSeekBar(true)
        }
    }

    private fun removeLandscapeControlBar() {
        val landscapeControlBar = landscapeControlBar ?: return
        mRootLanScapeView?.removeView(landscapeControlBar)
        this.landscapeControlBar = null
    }

    private fun removeLandscapeTitle() {
        val landscapeTitle = landscapeTitle ?: return
        mRootLanScapeView?.removeView(landscapeTitle)
        this.landscapeTitle = null
    }

    private fun checkLandScapeWidgets() {
        if (isLandScape()) {
            showLandScapeWidgets()
        }
    }

    private fun toggleLandScapeWidgets() {
        if (isLandScape()) {
            if (landscapeTitle?.isShown == true) {
                removeLandScapeWidgets()
            } else {
                showLandScapeWidgets()
            }
        }
    }

    // 未解锁短剧封面图
    private fun setBackgroundForCover() {
        val drawable = coverBackgroundIv?.drawable
        if (drawable != null) {
            return
        }
        var cover = mVideoInfo?.albumInfo?.validCover
        if (TextUtils.isEmpty(cover)) {
            cover = mVideoInfo?.videoCover
            if (TextUtils.isEmpty(cover)) {
                cover = mVideoInfo?.trackInfo?.validCover
            }
        }
        ShortPlayFreeListenManager.setCoverForImageView(cover, coverBackgroundIv)
    }

    private var mSketchVideoPlayListDialog: XSketchVideoPlayListDialogFragment? = null

    private fun showPlayList() {
        if (mVideoInfo == null) {
            return
        }

        if (mVideoInfo?.albumInfo?.albumType != 32) {
            XVideoPlayListDialogFragmentNew(mVideoInfo,
                mVideoPlayListPresenter,
                    getXmDuanJuItemTransferModel(),
                currentTrackId ?: 0,
                object : XVideoPlayListDialogFragmentNew.Callback {
                    override fun clickVideoItem(video: AlbumVideoInfoModel.AlbumVideoInfo) {
                        playListenerListener?.onShortPlayClick(video.trackId)
                        video.apply {
                            ShortPlayTrackManager.xmTraceClickDramaGallery(video,getXmDuanJuItemTransferModel())
                        }
                    }
                }).show(
                childFragmentManager, "XVideoPlayListDialogFragmentNew"
            )
            return
        }

        val djDataProvider = object : XSketchVideoPlayListDialogFragment.IDataProvider {
            override fun getCurVideoId(): Long {
                return mVideoInfo?.id ?: 0
            }

            override fun getCurAlbumId(): Long {
                return mVideoInfo?.albumId ?: 0
            }

            override fun getVideoInfo(): AlbumVideoInfoModel.AlbumVideoInfo? {
                return mVideoInfo
            }

            override fun dismiss() {
                mSketchVideoPlayListDialog?.dismiss()
            }

            override fun getCurVideoDisplayId(): Int {
                return mVideoInfo?.displayId ?: 0
            }

            override fun getStyle(): String {
                return style
            }

            override fun getType(): String {
                return type
            }

            override fun getCurXmDuanJuItemTransferModel(): XmDuanJuItemTransferModel? {
                return getXmDuanJuItemTransferModel()
            }

            override fun notifySketchVideoClick(videoId: Long, pageIndex: Int, requestKey: String) {
                playListenerListener?.onShortPlayClick(videoId, pageIndex, requestKey)
            }
        }

        mSketchVideoPlayListDialog =
            XSketchVideoPlayListDialogFragment(mVideoPlayListPresenter, djDataProvider)
        mSketchVideoPlayListDialog?.show(childFragmentManager, null)
    }

    //清除视频播放信息缓存
    private fun clearExistVideoInfo() {
        mVideoBaseInfo = null
        mCurPlayPath = null
        mVideoPlayer?.release(false)
        mVideoPlayer?.clearPlayerPath()
    }

    private fun updateWidgetsVisibility() {
        if (isFromDuanju) {
            ViewStatusUtil.setVisible(View.GONE, ivLandscape)
            ViewStatusUtil.setVisible(View.VISIBLE, rootTrackAnchorSketchPart, llTitleView)
            ViewStatusUtil.setVisible(View.VISIBLE, fullScreenWidgets)
            showHideImmersiveControlBar(true)
            return
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, rootTrackAnchorSketchPart, llTitleView)
            showHideImmersiveControlBar(!isFullScreen)
        }

        if (isLandScape()) {
            ViewStatusUtil.setVisible(View.VISIBLE, topMask)
            ViewStatusUtil.setVisible(View.GONE, ivLandscape, llTitleView)
            showHideImmersiveControlBar(false)
            ViewStatusUtil.setVisible(View.GONE, rootTrackAnchorSketchPart)
            return
        }

        removeLandScapeWidgets()

        if (mIsVideoPortrait == true) {
            ViewStatusUtil.setVisible(View.VISIBLE, fullScreenWidgets)
        } else {
            ViewStatusUtil.setVisible(View.GONE, fullScreenWidgets)
        }

        if (isFullScreen) {
            ViewStatusUtil.setVisible(View.GONE, ivLandscape, rootTrackAnchorSketchPart)
        } else {
            if (mIsVideoPortrait == false && !isExtremelySmallDevice()
                && !BaseUtil.isFoldScreen(context)
            ) {
                ViewStatusUtil.setVisible(View.VISIBLE, ivLandscape)
            } else {
                ViewStatusUtil.setVisible(View.GONE, ivLandscape)
            }
        }
    }

    private fun updateQuality() {
//        videoContainer?.updateQuality(videoQuality)
    }

    private val loginStateChangedListener = object : ILoginStatusChangeListener {
        override fun onLogout(olderUser: LoginInfoModelNew?) {
//            mVideoBaseInfo = null
//            loadVideoInfo()
        }

        override fun onLogin(model: LoginInfoModelNew?) {
//            mVideoBaseInfo = null
//            loadVideoInfo()
        }

        override fun onUserChange(oldModel: LoginInfoModelNew?, newModel: LoginInfoModelNew?) {
//            mVideoBaseInfo = null
//            loadVideoInfo()
        }
    }

    private fun getCustomString(): String {
        val videoUrl = downloadPath(mVideoBaseInfo?.trackInfo?.trackId ?: -1)
            ?: mVideoBaseInfo?.playInfo?.decodeUrl
        return "dqqTitle:${mVideoInfo?.title} isRealVisible = ${isRealVisible()} trackId:${mVideoInfo?.trackId}  videoUrl:${videoUrl}  mCurPlayPath:$mCurPlayPath"
    }

    private fun printVisibleLog(msg: String) {
        if (isRealVisible() && ConstantsOpenSdk.isDebug) {
            Log.d("dqq1", "$msg :${getCustomString()}")
        }
    }

    private fun printAllLog(msg: String) {
        if (ConstantsOpenSdk.isDebug) {
            Log.d("dqq1", "$msg :${getCustomString()}")
        }
    }

    private fun initPlayer() {
        try {
            val context = activity?.applicationContext ?: return
            mVideoPlayer = VideoViewUtil.createVideoView(context)
            setSpeed(TempoManager.getInstance().currentTempo)

            mVideoPlayer?.setDataInterceptor(object : IVideoDataInterceptor {
                override fun isFree(): Boolean {
                    val isPaid = mVideoInfo?.isPaid == true
                    return !isPaid
                }

                override fun getCurTrackId(): Long {
                    return mVideoInfo?.trackId ?: 0L
                }
            })

            val videoView = mVideoPlayer?.view
            if (videoView != null) {
                videoView.clearFocus()
                mVgVideoPlayerContainer =
                    findViewById(R.id.main_vg_video_player_container)
                mVgVideoPlayerContainer?.addView(videoView)
                mVideoPlayHasInit = true
                mVideoPlayer?.let {
                    it.addXmVideoStatusListener(mVideoPlayerStatusListener)
                    it.setOnPreparedListener(IMediaPlayer.OnPreparedListener {
                        if (setPlayPathTs > 0) {
                            val diffTime = System.currentTimeMillis() - setPlayPathTs
                            printVisibleLog("setOnPreparedListener diffTime:$diffTime")
                            setPlayPathTs = 0
                        }
                    })
                    it.setOnResolutionChangeListener(
                        IMediaPlayer.OnResolutionChangeListener { newWidth, newHeight ->
                            printVisibleLog("OnResolutionChangeListener newWidth:$newWidth newHeight:$newHeight")
                            handleOnResolutionChanged(newWidth, newHeight)
                            if (videoQuality != it.resolution && videoQuality >= 0
                                && it.videoPath?.startsWith("http") == true
                            ) {
                                it.setDefaultResolution(videoQuality)
                            }

                            updateQuality()
                        }
                    )
                    it.setOnErrorListener { _, what, extra ->
                        printVisibleLog("OnErrorListener.onError what:$what extra:$extra")
                        handleVideoError()
                        true
                    }
                    it.setPlayErrorCallback {
                        printVisibleLog("PlayErrorCallback 403")
                        loadVideoInfo(true)
                        true
                    }
                    layoutInflater.inflate(
                        R.layout.main_view_video_loading, null, false
                    )?.apply {
                        it.setLoadingView(this)
                    }
                    startPlayVideo()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun seekTo(position: Long) {
        mVideoPlayer?.seekTo(position)
        printAllLog("seekTo3 position:${position}")
    }

    override fun showFloatingProgress() {
        HandlerManager.removeCallbacks(mRemoveLandScapeWidgetsRun)
        mSeekBarWindow?.visibility = View.VISIBLE
    }

    override fun hideFloatingProgress() {
        checkLandScapeWidgets()
        mSeekBarWindow?.visibility = View.GONE
    }

    override fun updateFloatingProgress(progress: Int, duration: Int) {
        showProgressView(progress, duration)
    }

    private fun showProgressView(progress: Int, max: Int) {
        if (mSeekBarWindow?.visibility == View.VISIBLE) {
            val progressText = TimeHelper.toTime(progress / 1000.0)
            val durationText = TimeHelper.toTime(max / 1000.0)

            mSeekBarWindowCurPosition?.text = progressText
            mSeekBarWindowTotalPosition?.text = durationText

            mSeekBarWindowBar?.progress = progress
        }
    }

    private fun initSeekBar() {
        mSeekBar = findViewById(R.id.main_seek_bar)
        val seekBar = mSeekBar ?: return
        (seekBar.parent as View).setOnTouchListener { v, event ->
            seekBar.onTouchEvent(event)
            true
        }

        mSeekBar?.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {


            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                showProgressView(progress, seekBar?.max ?: 0)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                mSeekBarWindow?.visibility = View.VISIBLE
                showProgressView(seekBar?.progress ?: 0, seekBar?.max ?: 0)
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                mSeekBarWindow?.visibility = View.GONE
                seekBar?.progress?.toLong()?.let {
                    seekTo(it)
                }
            }
        })
    }

    private fun parseArguments() {
        currentTrackId = arguments?.getLong(BUNDLE_KEY_DATA_TRACK_ID)
        mVideoInfo = arguments?.getParcelable(BUNDLE_KEY_DATA)
        firstPosition = arguments?.getLong(BUNDLE_KEY_DATA_POSITION, -1) ?: -1
        style = arguments?.getString(BUNDLE_KEY_DATA_STYLE, "") ?: ""
        type = arguments?.getString(BUNDLE_KEY_DATA_TYPE, "") ?: ""
        mVideoInfo?.style = style
        mVideoInfo?.type = type
    }

    private fun downloadPath(trackId: Long): String? {
        val task = RouteServiceUtil.getDownloadService().queryVideoTaskByTrackId(trackId)
        if (task?.downloadStatus == IDownloadStatus.DOWNLOAD_FINISH) {
            return task.downloadedFileSavePath?.takeIf {
                kotlin.runCatching { File(it).exists() }.getOrNull() == true
            }
        }
        return null
    }

    private fun updateDefaultRes(baseInfo: VideoBaseInfo) {
        if (!videoQualityChangedByUser) {
            videoQuality = kotlin.runCatching {
                baseInfo.videoResolutions?.mapNotNull { it?.videoQualityLevel }?.max()
            }.getOrNull() ?: 1
            mVideoPlayer?.setDefaultResolution(videoQuality)
        }
    }

    private fun loadVideoInfo(forceLoad: Boolean = false) {
        printAllLog("loadVideoInfo start")

        val trackId = currentTrackId ?: return
        if (!forceLoad && mVideoBaseInfo != null) return
        val startTs = System.currentTimeMillis()

        CommonRequestM.getVideoBaseInfoNew(trackId, 1, object : IDataCallBack<VideoBaseInfo> {
            override fun onSuccess(data: VideoBaseInfo?) {
                if (data == null) return
                mVideoBaseInfo = data
                isFromDuanju = data.albumInfo?.isShortPlay ?: false

                updateVideoViewAspect()

                data.trackInfo?.apply {
                    mVideoInfo?.trackInfo = data.trackInfo
                }
                data.albumInfo?.apply {
                    mVideoInfo?.albumInfo = data.albumInfo
                }
                bindCommonInfo()

                val videoInfoTs = System.currentTimeMillis() - startTs
                updateDefaultRes(data)

                val videoUrl = downloadPath(trackId) ?: data.playInfo?.decodeUrl
                printAllLog("loadVideoInfo end 耗时:$videoInfoTs")

                if (videoUrl != null && videoUrl != mCurPlayPath) {
                    mCurPlayPath = videoUrl
                    mVideoInfo?.extraInfo = data.extraInfo
                    startPlayVideo()

//                    if (ShortDramaManager.isShortDrama(mSoundInfo?.trackInfo2TrackM())) {
//                        //有权限，关闭短剧购买弹窗
//                        Log.d(TAG, "有权限，关闭短剧购买弹窗 >>> ")
//                        ShortDramaManager.dismissBuyDialog();
//                    }
                }

                if (videoUrl == null) {
                    pauseByUser = true
                    mVideoPlayer?.pause()
                    updateCommercialView(mVideoInfo)

//                    if (mVideoInfo?.trackInfo?.isPayTrack == true && mVideoInfo?.isAuthorized == false) {
//                        Log.d(TAG, "onComplete updateCommercialView")
//                        checkAutoUnlockAndUpdateCommercialView(mVideoInfo)
//                    }
                }
            }

            override fun onError(code: Int, message: String) {
                printAllLog("loadVideoInfo error code:$code message:$message")
                if (!TextUtils.isEmpty(message)) {
                    CustomToast.showFailToast(message)
                } else {
                    CustomToast.showFailToast("获取视频数据异常")
                }
                if (!NetworkType.isConnectTONetWork(context)) {
                    showOrHideNoNetworkView(true)
                }
            }
        })
    }

    private fun showOrHideCommercialAreaView(show: Boolean, reCreateView: Boolean = false) {
        val realShow = show && mIsStopByCommercialInfo
//        if (ConstantsOpenSdk.isDebug) {
//            Logger.e(
//                TAG,
//                "showOrHideCommercialAreaView $realShow >>> ${mVideoInfo?.title}" + Log.getStackTraceString(
//                    Throwable()
//                )
//            )
//        }
        if (realShow) {
            showOrHidePlayBtnView(false)
//            if (reCreateView) {
//                mVideoPlayCommercialManager.doOnPlayPageControlFullScreenStatusChange(
//                    mSoundInfo,
//                    playContainer as BaseFragment2,
//                    R.id.main_rl_commercial_info
//                )
//            }
        }
        mGroupCommercialInfo?.visible(if (realShow) View.VISIBLE else View.GONE)
    }

    private fun checkAutoUnlockAndUpdateCommercialView(data: AlbumVideoInfoModel.AlbumVideoInfo?) {
        if (!isRealVisible()) {
            //不可见的时候，不去解锁
            updateCommercialView(mVideoInfo)
            return
        }

//        if (ShortDramaManager.isShortDrama(data?.trackInfo2TrackM())
//            && data?.trackInfo2TrackM()?.isPayTrack == true
//            && mSoundInfo?.trackInfo2TrackM()?.isAuthorized == false
//            && ShortPlayFreeListenConfigManager.isCompatAutoUnlock()
//        ) {
//            //没权限，先去看能否解锁
//            Logger.e("ShortDramaManager", "没权限，先去看能否解锁 >>> ")
//            ShortDramaManager.autoUnlock(data!!.trackInfo2TrackM()) { state ->
//                if (state == ShortDramaState.UNLOCK_FAILED) {
//                    Logger.e("ShortDramaManager", "解锁失败，展示入口 >>> ")
//                    updateCommercialView(data)
//                }
//            }
//
//        } else {
//            updateCommercialView(data)
//        }
    }

    /**
     * 显示商业化购买信息
     */
    private fun updateCommercialView(mVideoInfo: AlbumVideoInfoModel.AlbumVideoInfo?): Boolean {
        if (mVideoInfo?.albumInfo == null || mVideoInfo.trackInfo == null) {
            mIsStopByCommercialInfo = false
            return false
        }
        var needShow = false

        if (ShortPlayFreeListenManager.supportFreeListen(mVideoInfo)) {
            //短剧的样式不需要请求接口，直接处理
            needShow = true
            ViewStatusUtil.setVisible(View.VISIBLE, coverBackgroundIv)
            setBackgroundForCover()
            ShortPlayFreeListenManager.showCommercialView(
                mVideoInfo,
                mVideoInfo.albumInfo?.wrapCover,
                mCommercialInfoView
            )
        }

        if (needShow) {
            showOrHidePlayBtnView(false)
            mIsStopByCommercialInfo = true
            showOrHideCommercialAreaView(true)
        } else {
            mIsStopByCommercialInfo = false
        }
        return needShow
    }

    private fun playOrPause() {
        if (mVideoPlayer?.isPlaying == true) {
            pauseByUser = true
            mVideoPlayer?.pause()
        } else {
            mVideoPlayer?.start()
        }
    }

    private fun isRealVisible(): Boolean {
        return playListenerListener?.isRealShow(currentTrackId) == true && isRealVisibleVideo
    }

    override fun onResume() {
        super.onResume()
        printAllLog("onResume")

        if (playListenerListener?.isRealShow(currentTrackId) == true) {
            playListenerListener?.updateShortPlayFragment(this)
            // 请求焦点 不然横屏不准确
            rootVideo?.requestFocus()

            mVideoInfo?.apply {
                ShortPlayTrackManager.xmTraceShowShortPlayDetailItem(this,getXmDuanJuItemTransferModel())
                ShortPlayTrackManager.xmTraceOperatorShortPlayDetailItem(this,getXmDuanJuItemTransferModel())
            }
        }

        if (isRealVisibleVideo) return
        isRealVisibleVideo = true

        HandlerManager.postOnBackgroundThread {
            BaseApplication.mAppInstance?.also {
                XmPlayerManager.getInstance(it).pause()
            }
        }
        syncAudioProgressAndStartPlayIfNeeded(true, false)
        updateWidgetsVisibility()
        isContinuePlay = false
    }

    override fun onPause() {
        super.onPause()
        printAllLog("onPause")

        if (isRealVisible()) {
            ShortPlayPlayTimeManager.xmShortPlayPagePause(this)
            // 保存播放进度
            if (isPlaying()) {
                ShortPlayHistoryManager.saveShortPlayPositionToXmHistory(
                    mVideoInfo,
                    getCurPosition()
                )
            }
        }

        isRealVisibleVideo = false
        isContinuePlay = false
        if (!canVideoContinuePlayOnPause()) {
            printAllLog("videoPlay 执行暂停")
            mVideoPlayer?.pause()
        } else {
            isContinuePlay = true
        }
        onPauseOrTabDeactivate()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        TempoManager.getInstance().removeListener(mTempoListener)
//        LikeTrackManage.removeListener(this)
//        TrackCollectManager.getInstance().removeListener(this)
//        TrackCommentBehaviorFactory.newInstance().removeManager(mCommentChangeListener)
        LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext())
            .unregisterReceiver(mPayListener)

        mVideoPlayer?.removeXmVideoStatusListener(mVideoPlayerStatusListener)
        mVideoPlayer?.release(true)
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(loginStateChangedListener)

        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.Q) {
            ToolUtil.fixInputMethodManagerLeak(context)
        }
        rootVideo?.removeAllViews()
        (rootVideo?.parent as? ViewGroup)?.removeView(rootVideo)
    }

    override fun onDestroy() {
        super.onDestroy()
        clearExistVideoInfo()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        updateVideoViewAspect()
        onConfigurationChangedByUser(newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE)
    }

    private fun onPauseOrTabDeactivate() {
        mIsNeedResumePlay = mVideoPlayer?.isPlaying ?: true

        NetworkType.removeNetworkChangeListener(mNetworkChangeListener)
    }

    private fun syncAudioProgressAndStartPlayIfNeeded(
        forcePlay: Boolean,
        needShowToast: Boolean = false
    ) {
        if (!isRealVisible()) return
        if (forcePlay || mIsNeedResumePlay) {
            realStartPlay()
        }
    }

    fun setSpeed(speed: Float) {
        printAllLog("videoPlay 设置倍速:$speed")
        mVideoPlayer?.speed = speed
    }
    fun getSpeed():Float {
        printAllLog("videoPlay 获取倍速:")
        if (mVideoPlayer==null){
            return 1.0f;
        }
        return mVideoPlayer!!.speed;
    }

    private fun realStartPlay() {
        printAllLog("realStartPlay")
        if (mVideoPlayer?.videoPath.isNullOrEmpty()) {
            startPlayVideo()
            return
        }
        if (!isRealVisible()) return
        if (!mVideoPlayer?.videoPath.isNullOrEmpty() && mVideoPlayer?.isPlaying != true) {
            setSpeed(TempoManager.getInstance().currentTempo)
            printAllLog("触发开始播放")
            mVideoPlayer?.start()
            HandlerManager.postOnBackgroundThread {
                XmPlayerManager.getInstance(BaseApplication.mAppInstance).pause()
            }
            if (NetworkType.isConnectMOBILE(context)) {
                showMobilePlayToast()
            }

        }
    }

    private fun getStartPosition(): Long {
        var position = 0L
        if (firstPosition >= 0) {
            position = firstPosition
            // 只使用一次
            firstPosition = -1
        } else {
            val videoId = mVideoInfo?.trackId
            if (videoId != null) {
                val history = XmPlayerManager.getInstance(context).getHistoryPos(videoId)
                if (history > 0) {
                    position = history.toLong()
                }
            }
        }
        return position
    }

    private fun startPlayVideo() {
        printAllLog("startPlayVideo hasInit = $mVideoPlayHasInit")

        val path = mCurPlayPath ?: return
        if (!mVideoPlayHasInit) return
        postOnUiThread {
            mVideoPlayer?.let {
                if (it.videoPath.isNullOrEmpty()) {
                    it.release(true)
                    setPlayPathTs = System.currentTimeMillis()
                    val position = getStartPosition()
                    printAllLog("设置播放地址和进度:$position")
                    it.setVideoStartPosition(position)
                    it.videoPath = path
                    it.setContentId(currentTrackId ?: 0L)
                    mSeekBar?.isCanSeek = true
                }

                realStartPlay()
            }
        }
    }

    private fun updatePlayingStatus(targetStatusPlaying: Boolean? = null) {
        val isPlaying = (targetStatusPlaying ?: mVideoPlayer?.isPlaying) ?: false
        mIvPlayBtn?.visible(
            if (isPlaying ||
                mGroupErrorState?.visibility == View.VISIBLE ||
                mGroupNoNetwork?.visibility == View.VISIBLE ||
                mGroupCommercialInfo?.visibility == View.VISIBLE
            ) {
                View.INVISIBLE
            } else {
                View.VISIBLE
            }
        )
    }

    private fun updateFloatingTimeUi(curPositionN: Int?, durationN: Int?) {
        val duration = durationN ?: 0
        val curPosition = (curPositionN ?: 0).coerceAtLeast(0).coerceAtMost(duration)
        val progressText = TimeHelper.toTime(curPosition / 1000.0)
        val durationText = TimeHelper.toTime(duration / 1000.0)
        val floatingProgress = SpannableString("%s / %s".format(progressText, durationText))
        val index: Int = floatingProgress.indexOf("/")
        if (index > 0) {
            floatingProgress.setSpan(
                ForegroundColorSpan(0xb3ffffff.toInt()), index,
                floatingProgress.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        mTvFloatingProgress?.text = floatingProgress
    }

    private fun handleOnResolutionChanged(newWidth: Int, newHeight: Int) {
        val realWidth = mVideoPlayer?.mediaPlayer?.videoWidth ?: newWidth
        val realHeight = mVideoPlayer?.mediaPlayer?.videoHeight ?: newHeight
        val isVideoPortrait = realHeight > realWidth
        if (isVideoPortrait != mIsVideoPortrait) {
            mIsVideoPortrait = isVideoPortrait
            updateVideoViewAspect()

            kotlin.runCatching {
                updateWidgetsVisibility()
            }
        }
        //updateAlbumBgColor()
        relayoutLandscapeLocation(mVideoPlayer?.view, realHeight, realWidth, isVideoPortrait)
    }

    private fun isFoldScreenWithExpand(): Boolean {
        return kotlin.runCatching {
            BaseUtil.isCollapsibleScreenOnLandscapeMode(context)
                || BaseUtil.isCollapsibleScreenOnPortraitExpandMode(context)
        }.getOrNull() ?: false
    }

    private fun updateVideoViewAspect() {
        if (mVideoPlayer == null) return

        if (isFromDuanju) {
            if (isFoldScreenWithExpand()) {
                mVideoPlayer?.setAspectRatio(IRenderView.AR_ASPECT_FIT_PARENT)
            } else {
                mVideoPlayer?.setAspectRatio(IRenderView.AR_ASPECT_FILL_PARENT)
            }
            return
        }

        mIsVideoPortrait ?: return
        val aspect = IRenderView.AR_ASPECT_FIT_PARENT

        mVideoPlayer?.apply {
            if (mVideoAspect != aspect) {
                mVideoAspect = aspect
                setAspectRatio(aspect)
            }
        }
    }

    private fun relayoutLandscapeLocation(
        mediaPlayer: View?,
        height: Int,
        width: Int,
        isVideoPortrait: Boolean
    ) {
        if (width <= 0) {
            return
        }
        mediaPlayer?.post {
            kotlin.runCatching {
                val realHeight = if (isLandScape()) {
                    BaseUtil.getScreenHeight(context) * height / width
                } else {
                    BaseUtil.getScreenWidth(context) * height / width
                }
                ivLandscape?.translationY = (realHeight / 2).toFloat() + 24.dp
            }
        }
    }

    private fun onConfigurationChangedByUser(isLandscape: Boolean) {
        mIsInLandscapeFullscreen = isLandscape

        if (mIsInLandscapeFullscreen) {
            topMask?.visibility = View.INVISIBLE
        } else {
            topMask?.visibility = View.VISIBLE
        }
        updateWidgetsVisibility()
    }

    fun isPlaying(): Boolean {
        return mVideoPlayer?.isPlaying ?: false
    }

    private fun handleNetworkChanged(networkType: NetworkType.NetWorkType) {
        // 网络状态发生改变了，下次再用流量播放时要toast提示
        sNeedShowMobileToast = true
        if (networkType.isMobile() && mVideoPlayer?.isPlaying == true) {
            showMobilePlayToast()
        }
    }

    private fun showMobilePlayToast() {
        if (sNeedShowMobileToast) {
            CustomToast.showToast("当前为非wifi环境，请注意流量消耗")
            sNeedShowMobileToast = false
        }
    }

    private fun showOrHideErrorView(show: Boolean) {
        mGroupErrorState?.visible(if (show) View.VISIBLE else View.INVISIBLE)
    }

    private fun showOrHideNoNetworkView(show: Boolean) {
        mGroupNoNetwork?.visible(if (show) View.VISIBLE else View.INVISIBLE)
        if (show) {
            showOrHidePlayBtnView(false)
        }
    }

    private fun showOrHidePlayBtnView(show: Boolean) {
        mIvPlayBtn?.visible(if (show) View.VISIBLE else View.INVISIBLE)
    }

    private fun refresh() {
        if (mCurPlayPath.isNullOrEmpty()) {
            loadVideoInfo()
        } else {
            mVideoPlayer?.let {
                it.videoPath = mCurPlayPath
                realStartPlay()
            }
        }
    }

    private fun handleVideoError() {
        if (!NetworkType.isConnectTONetWork(context)) {
            showOrHideNoNetworkView(true)
        } else {
            showOrHideErrorView(true)
        }
        updatePlayingStatus()
    }


    private fun handlePlayClick() {
        mVideoPlayer?.apply {
            if (!isPlaying) {
                realStartPlay()
            } else {
                pauseByUser = true
                pause()
            }
        }
    }

    private fun getCurPosition(): Long {
        return mVideoPlayer?.currentPosition?.toLong() ?: 0
    }

    private val mVideoPlayerStatusListener = object : IXmVideoPlayStatusListener {
        override fun onStart(videoSourceUrl: String?) {
            pauseByUser = false
            if (!isRealVisible() && !isContinuePlay) {
                printAllLog("onStart 执行暂停")
                mVideoPlayer?.pause()
            }
            ViewStatusUtil.setVisible(View.GONE, coverBackgroundIv)
            updatePlayingStatus(true)
            showOrHideErrorView(false)
            showOrHideNoNetworkView(false)
            checkAutoUnlockAndUpdateCommercialView(null)
            showOrHideCommercialAreaView(false)
            landscapeControlBar?.onPlayStart()
            HandlerManager.postOnBackgroundThread {
                XmPlayerManager.getInstance(BaseApplication.mAppInstance).pause()
            }

            ShortPlayHistoryManager.saveShortPlayHistory(
                mVideoInfo,
                true,
                getCurPosition(),
                style,
                type
            )
            ShortPlayHistoryManager.saveShortPlayTrackToXmHistory(mVideoInfo)
            if (isRealVisible()) {
                var xmInfoModel= XmDuanjuTimeTraceParamsModel();
                xmInfoModel.videoInfo=mVideoInfo;
                xmInfoModel.transferModel=getXmDuanJuItemTransferModel();
                ShortPlayPlayTimeManager.xmShortPlayPageShow(this@VideoImmersiveDetailFragment, xmInfoModel)
            }
            printAllLog("player onStart")
        }

        override fun onPause(videoSourceUrl: String?, playedTime: Long, duration: Long) {
            updatePlayingStatus(false)
            landscapeControlBar?.onPause()
            printAllLog("player onPause")
        }

        override fun onStop(videoSourceUrl: String?, playedTime: Long, duration: Long) {
            updatePlayingStatus()
            landscapeControlBar?.onPause()
            printAllLog("player onStop")
        }

        override fun onComplete(videoSourceUrl: String?, duration: Long) {
            if (!isRealVisible() && !isContinuePlay) {
                return
            }
            landscapeControlBar?.onPause()
            updatePlayingStatus()
            if (mCommentDialogShowing) {
                mNeedPlayNextWhileCommentDialogHide = true
            } else {
                playListenerListener?.autoPlayNext()
            }

            ShortPlayHistoryManager.saveShortPlayHistory(mVideoInfo, true, 0, style, type)
            printAllLog("player onComplete")
        }

        override fun onError(videoSourceUrl: String?, playedTime: Long, duration: Long) {
            handleVideoError()

            ShortPlayHistoryManager.saveShortPlayHistory(
                mVideoInfo,
                true,
                getCurPosition(),
                style,
                type
            )
            printAllLog("onError playedTime:$playedTime duration:$duration")
        }

        override fun onProgress(videoSourceUrl: String?, curPosition: Long, duration: Long) {
            currentProgress = curPosition.toInt()
            currentDuration = duration.toInt()
            landscapeControlBar?.onPlayProgress(curPosition.toInt(), duration.toInt())

            // 视频播放器现在有点问题，如果不在播放中，调暂停，不会暂停，但是状态会变成暂停，播放进度回调等也会移除，导致无法再暂停
            // 离开页面的那次暂停可能还没有到播放中状态，这里确保一下能暂停
            if (!isRealVisible() && mVideoPlayer?.isPlaying == true) {
                if (!isContinuePlay || !isTopActivityIsMain()) {
                    mVideoPlayer?.pause()
                }
            }
            mSeekBar?.let {
                it.isCanSeek = true
                it.max = if (duration > 0) duration.toInt() else 100
                it.progress = curPosition.toInt()
            }
            mSeekBarWindowBar?.let {
                it.max = duration.toInt()
            }

            ShortPlayHistoryManager.saveShortPlayHistory(
                mVideoInfo,
                false,
                curPosition,
                style,
                type
            )
        }

        override fun onRenderingStart(videoSourceUrl: String?, renderingSpentMilliSec: Long) {
            mSeekBar?.isCanSeek = true
            if (!isRealVisible() && !isContinuePlay) {
                return
            }

            printAllLog("onRenderingStart renderingSpentMilliSec:$renderingSpentMilliSec")
        }

        override fun onBlockingStart(videoSourceUrl: String?) {
            printAllLog("onBlockingStart")

        }

        override fun onBlockingEnd(videoSourceUrl: String?) {
            printAllLog("onBlockingEnd")

            if (isRealVisible()) {
                updatePlayingStatus()
            }
        }
    }

    private val mOnClickListener = View.OnClickListener { view ->
        var clickHandled = true
        when (view) {
            // 不限制点击时间间隔的放这里
            mRootView -> {
                if (!isLandScape() || mIvPlayBtn?.visibility == View.VISIBLE) {
                    handlePlayClick()
                }
                toggleLandScapeWidgets()
            }

            else -> clickHandled = false
        }
        if (!clickHandled && OneClickHelper.getInstance().onClick(view)) {
            when (view) {
//移除点击跳转
//                mTvVideoTitleSketch -> {
//                    val url = "iting://open?msg_type=13&album_id=" + mVideoInfo?.albumId
//                    ITingHandler().handleITing(activity, Uri.parse(url))
//                    ShortPlayTrackManager.trackClickAlbumTitle(mVideoInfo, "点击专辑标题")
//                }

                mIvShareBtn, mShareCountText -> {

                }

                mTvRefreshBtn -> refresh()
                likeBtn, likeCountTextView -> {
                    doLike()
                }

                collectBtn, collectCountTextView -> {
                    doCollect()
                }

                commentBtn, commentCountTextView -> {
//                    playContainer?.showCommentPanel(false, true)
                }
            }
        }
    }

    /**
     * 白名单内，在播放页按home回后台 或者 闭屏，则不停止视频播放
     */
    private fun canVideoContinuePlayOnPause(): Boolean {
        return false
    }

    private fun isTopActivityIsMain(): Boolean = BaseApplication.getTopActivity() is MainActivity

    fun isStopByCommercialInfo(): Boolean {
        return mIsStopByCommercialInfo
    }


    private fun doCollect() {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(activity, LoginByConstants.LOGIN_BY_LIKE)
            return
        }
        val activity = BaseApplication.getTopActivity() ?: return
        if (collectBtn?.isAnimating == true) return
        val isCollected = isCollect
        traceLikeOrCollect(isCollected, false)

        if (!isCollected) {
            collectBtn?.playAnimation()
        } else {
            collectBtn?.progress = 0f
        }

        mVideoInfo?.trackId?.let {
//            TrackCollectManager.getInstance().requestCollectOrUnCollect(
//                isCollected,
//                it,
//                object : IDataCallBack<Boolean?> {
//                    override fun onSuccess(`object`: Boolean?) {
//                        if (`object` == null || !`object`) {
//                            return
//                        }
//                    }
//
//                    override fun onError(code: Int, message: String) {
//                        if (collectBtn != null) {
//                            collectBtn?.cancelAnimation()
//                            collectBtn?.progress = if (isCollected) 1f else 0.toFloat()
//                        }
//                        CustomToast.showFailToast(message)
//                    }
//                })
        }
    }

    private fun updateLikeState(isLikeNow: Boolean) {
        if (isLikeNow && likeBtn?.isAnimating == true) return
        likeBtn?.cancelAnimation()
        likeBtn?.progress = if (isLikeNow) 1f else 0f
    }

    private fun updateCollectState(isCollect: Boolean) {
        if (collectBtn?.isAnimating == true && isCollect) return
        collectBtn?.cancelAnimation()
        collectBtn?.progress = if (isCollect) 1f else 0.toFloat()
    }


    private fun update(info: AlbumVideoInfoModel.AlbumVideoInfo?) {
        val track = info?.trackInfo
        isLike = track?.isLike == true
        if (likeBtn?.isAnimating == true) {
            likeBtn?.cancelAnimation()
        }
        likeBtn?.progress = if (isLike) 1f else 0f
//        isCollect = PlayCommentUtil.isTrackCollected(info)
        if (collectBtn?.isAnimating == true) {
            collectBtn?.cancelAnimation()
        }
        collectBtn?.progress = if (isCollect) 1f else 0.toFloat()
    }


    private fun doLike() {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(activity, LoginByConstants.LOGIN_BY_LIKE)
            return
        }
        val activity = BaseApplication.getTopActivity() ?: return
        if (likeBtn?.isAnimating == true) return
        mVideoInfo?.trackInfo?.isLike = isLike
        if (!isLike) {
            likeBtn?.playAnimation()
        } else {
            likeBtn?.progress = 0f
        }
        traceLikeOrCollect(isLike, true)
//        LikeTrackManage.toLikeOrUnLike(
//            track,
//            true,
//            null,
//            activity,
//            object : IDataCallBack<Boolean?> {
//                override fun onSuccess(`object`: Boolean?) {
//                    if (`object` == null || !`object`) {
//                        return
//                    }
//                }
//
//                override fun onError(code: Int, message: String) {
//                }
//            })

    }

    private fun traceLikeOrCollect(isCancel: Boolean, isLike: Boolean) {
    }

    private fun updateShareState() {
        mVideoInfo?.let {
            val shares = it.trackInfo?.shares ?: 0
            mShareCountText?.setText(
                if (shares <= 0) "分享" else StringUtil.getFriendlyNumStr(shares)
            )
        }

    }

    private fun bindLikeCollectCount() {
        mVideoInfo?.let {
            likeCountTextView?.text =
                if (it.trackInfo?.likes ?: 0 <= 0) "喜欢" else StringUtil.getFriendlyNumStr(
                    it.trackInfo?.likes ?: 0
                )

//            collectCountTextView?.text = if (it.otherInfo?.collectNum ?: 0 <= 0) "收藏"
//            else StringUtil.getFriendlyNumStr(it.otherInfo?.collectNum ?: 0)
//
//            commentCountTextView?.text =
//                if (it.trackInfo?.comments ?: 0 <= 0) "评论" else StringUtil.getFriendCommentCountNew(
//                    it.trackInfo?.comments ?: 0
//                )
        }

    }

    private fun bindCommonInfo() {
        mVideoInfo?.let {
            // 从首页短剧进入
            mTvVideoTitleSketch?.text = it.albumInfo?.title ?: ""

            val intro = TrackTitleUtil.getTrackTitle(mVideoInfo)

            mTvVideoIntroSketch?.setLimitedLineAlignRight(true)
            mTvVideoIntroSketch?.setContent(intro)

            ShortPlayFreeListenManager.saveTrackInfo(it.albumInfo?.albumId, it.albumInfo?.intro)

            tvPlayList?.text = "选集·共${totalCount}集"
        }
    }

    @SuppressLint("SetTextI18n")
    private fun bindDataToView() {
        mVideoInfo?.let {
            bindCommonInfo()
            update(it)
            bindLikeCollectCount()
            updateShareState()
            traceAlbumExpose()
        }
    }

    private fun traceAlbumExpose() {
    }

    private fun isLandScape(): Boolean {
        return XMScreenUtils.isLandScape(activity)
    }

    private fun showHideImmersiveControlBar(toShow: Boolean) {
        if (isLandScape() || !toShow) {
            ViewStatusUtil.setVisible(
                View.GONE,
                seekbarContain,
                mSeekBar,
                rootFunctionPart,
                ivFullEntry,
                playListEntry
            )
        } else {
            ViewStatusUtil.setVisible(
                View.VISIBLE,
                seekbarContain,
                mSeekBar,
                rootFunctionPart,
                ivFullEntry,
                playListEntry
            )
        }

        // 本次强制隐藏方法区
        ViewStatusUtil.setVisible(View.GONE, rootFunctionPart, ivFullEntry)
    }

    fun getCurTrackId(): Long {
        return mVideoInfo?.trackId ?: 0L
    }

    private fun toAlbumPage() {
        val albumInfo = mVideoInfo?.albumInfo ?: return
        val option = AlbumEventManage.AlbumFragmentOption()
        AlbumEventManage.startMatchAlbumFragment(
            albumInfo.albumId, AlbumEventManage.FROM_ALBUM_BELONG,
            ConstantsOpenSdk.PLAY_FROM_TAB_ALBUM, null, null, -1, activity, option
        )

    }

    fun onTrackCollectChanged(collectNow: Boolean, trackId: Long) {
        if (trackId == currentTrackId) {
            isCollect = collectNow
            updateCollectState(collectNow)
            postOnUiThread { bindLikeCollectCount() }
        }
    }

    fun onTrackLikeChanged(isLikeNow: Boolean, trackId: Long) {
        if (trackId == currentTrackId) {
            isLike = isLikeNow
            updateLikeState(isLikeNow)
            postOnUiThread { bindLikeCollectCount() }
        }
    }


    fun onShare() {
        mVideoInfo?.trackInfo?.also { it.shares += 1 }
        updateShareState()
    }

    private val mPayListener = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            intent ?: return

//            if (ShortPlayFreeListenTimeManager.ACTION_UNLOCK_SUCCEED == intent.action) {
//                //激励视频解锁成功
//                Logger.d(
//                    TAG,
//                    "mPayListener onReceive >>>> isRealVisable: $isRealVisable"
//                )
//                refreshSoundInfoAndVideoInfo(true)
//                return
//            }
//
//            if (ShortDramaManager.ACTION_SHORT_DRAMA_BUY_SUCCESS == intent.action) {
//                //购买短剧
//
//                val trackId = intent.getLongExtra(ShortDramaManager.ARGS_SHORT_DRAMA_TRACK_ID, -1)
//
//                Logger.d(
//                    "ShortDramaManager",
//                    "mPayListener onReceive, trackId: $trackId, currentTrackId: $currentTrackId"
//                )
//
//                if (currentTrackId == null || trackId == currentTrackId) {
//                    refreshSoundInfoAndVideoInfo(false)
//                }
//                return
//            }
//
//            var trackId = intent.getLongExtra(SingleAlbumPayBroadcastManager.ARGS_TRACK_ID, -1L)
//            if (trackId == -1L) {
//                trackId = intent.getLongExtra(BundleKeyConstants.KEY_TRACK_ID, -1)
//            }
//
//            val currentTrackId = mSoundInfo?.trackInfo2TrackM()?.dataId
//            Logger.e(
//                "XVideoImmerDetail",
//                "videoFragmentReceive, trackId:$trackId, current:$currentTrackId "
//            )
//
//            if (currentTrackId == null || trackId == currentTrackId) {
//                loadVideoInfo(true)
//            }
        }
    }

    private fun refreshSoundInfoAndVideoInfo(refreshTabsInfo: Boolean) {
//        if (mSoundInfo?.trackInfo2TrackM()?.isPayTrack == true && mVideoInfo?.isAuthorized == false) {
//            mVideoInfo?.isAuthorized = true
//            Logger.e(TAG, "解锁成功，手动修改 track 权限为 true")
//        }

        mVideoBaseInfo = null
        loadVideoInfo(true)
    }

    private fun toAnchorPage() {

    }


    //如果是支持激励视频的短剧，通知播放信息变化
    private fun notifyShortPlayFreeListenVideoChanged() {
        if (mVideoInfo == null) {
            Logger.w("", "notifyShortPlayFreeListenVideoChanged return, mSoundInfo is null ")
            return
        }
        if (!ShortPlayFreeListenConfigManager.isFreeListenOpen()) {
            Logger.w(
                "",
                "notifyShortPlayFreeListenVideoChanged return s2, isFreeListenOpen return false "
            )
            return
        }

        val supportFreeListen = ShortPlayFreeListenManager.supportFreeListen(mVideoInfo)
        Logger.w(
            "",
            "notifyShortPlayFreeListenVideoChanged supportFreeListen: %$supportFreeListen "
        )
        if (!supportFreeListen) {
            return;
        }

        var needCountDown = true
//        if (mSoundInfo?.trackInfo?.isPaid == true && mSoundInfo?.trackInfo?.isFree == false && mSoundInfo?.trackInfo?.isAuthorized == true
//            && !FreeListenV2Util.FREE_LISTEN_V2_PREMISSION_SOURCE.equals(mSoundInfo?.trackInfo?.permissionSource)
//        ) {
//            needCountDown = false
//        }
//
//        val intent = Intent()
//        intent.action = ShortPlayFreeListenTimeManager.ACTION_PLAY_VIDEO_INFO_CHANGED
//        intent.putExtra(
//            ShortPlayFreeListenTimeManager.PARAMS_TRACK_ID,
//            mSoundInfo?.trackInfo?.trackId ?: -1
//        )
//        intent.putExtra(
//            ShortPlayFreeListenTimeManager.PARAMS_ALBUM_ID,
//            mSoundInfo?.trackInfo?.albumId ?: -1
//        )
//        intent.putExtra(ShortPlayFreeListenTimeManager.PARAMS_NEED_COUNT_DOWN, needCountDown)
//        LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext())
//            .sendBroadcast(intent)

    }

    private fun NetworkType.NetWorkType.isMobile(): Boolean {
        return this != NetworkType.NetWorkType.NETWORKTYPE_INVALID && this != NetworkType.NetWorkType.NETWORKTYPE_WIFI
    }

    fun getXmDuanJuItemTransferModel(): XmDuanJuItemTransferModel?{
        if (parentFragment is XmShortPlayDetailFragment){
            return (parentFragment as XmShortPlayDetailFragment).getXmDuanJuItemTransferModel()
        }
        return null;
    }
}