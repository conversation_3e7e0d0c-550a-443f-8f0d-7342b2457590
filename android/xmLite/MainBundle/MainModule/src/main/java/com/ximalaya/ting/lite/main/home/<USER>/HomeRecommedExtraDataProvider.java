package com.ximalaya.ting.lite.main.home.adapter;

import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.manager.HomeRecommendAdapterAddFloorManager;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel;

import java.util.List;

/**
 * Created by qinhuifeng on 2019-07-17
 *
 * <AUTHOR>
 */
public interface HomeRecommedExtraDataProvider {

    HomeRecommendExtraViewModel getHomeRecommendExtraViewModel();

    int getCategoryId();

    List<ItemModel> getListData();

    void removeItem(int position);

    void notifyDataSetChanged();

    int getItemViewType(int position);

    int getFrom();
}
