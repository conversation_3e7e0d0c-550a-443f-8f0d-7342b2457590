package com.ximalaya.ting.lite.main.download.bean;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   : 多任务信息
 */
public class MultiTaskInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    public long totalSize = 0;
    public long blockSize = 0;
    public ArrayList<SingleTaskInfo> list = new ArrayList<>();

    @Override
    public String toString() {
        StringBuffer buffer = new StringBuffer();
        buffer.append("MultiTaskInfo [size=" + totalSize + ", blockSize=" + blockSize + "]\n");
        for (SingleTaskInfo info : list) {
            buffer.append("info:" + info.beginPos + "_" + info.endPos + ":" + info.haveDoneSize);
            buffer.append("\n");
        }
        return buffer.toString();
    }

    public void reset() {
        list.clear();
        totalSize = 0;
        blockSize = 0;
    }
}
