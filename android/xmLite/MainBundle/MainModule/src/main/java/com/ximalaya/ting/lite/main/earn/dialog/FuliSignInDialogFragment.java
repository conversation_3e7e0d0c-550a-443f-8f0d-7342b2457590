package com.ximalaya.ting.lite.main.earn.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.cardview.widget.CardView;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.qq.e.ads.nativ.widget.NativeAdContainer;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.adsdk.platform.common.modelproxy.AbstractThirdAd;
import com.ximalaya.ting.android.host.adsdk.platform.gdt.view.GdtMediaViewContainer;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.listenertask.ListenEarnCoinDialogManager;
import com.ximalaya.ting.android.host.listenertask.SignInConfigManager;
import com.ximalaya.ting.android.host.listenertask.callback.JssdkFuliRewardCallback;
import com.ximalaya.ting.android.host.model.ad.AdWrapper;
import com.ximalaya.ting.android.host.model.earn.SignInConfigModel;
import com.ximalaya.ting.android.host.model.earn.SignInDialogDataModel;
import com.ximalaya.ting.android.host.util.CountDownTimerFix;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.host.adsdk.provider.CommonNativeDaTuAdProvider;
import com.ximalaya.ting.android.host.adsdk.provider.viewmodel.SimpleDaTuViewModel;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 福利页面签到弹框
 *
 * <AUTHOR>
 */
public class FuliSignInDialogFragment extends BaseDialogFragment {

    private static final String ARGUMENT_KEY_DIALOG_DATA_MODEL = "sign_in_dialog_data_model";
    private boolean mMaskIsShow = false; //解决fragment重复添加crash问题
    private TextView mTvCoinNumber;
    private TextView mTvEarnListenTime;
    private TextView mTvMyBalanceExchange;
    private LinearLayout mLayoytMyCoinBalance;
    //加倍
    private TextView mTvEarnMore;
    //倒计时
    private TextView mCloseCountdownTime;
    private View mViewClose;

    private CountDownTimerFix mTimer;

    private ViewGroup mAdLayoutContent;
    private TextView mAdTvTitle;
    private ImageView mAdIvImage;
    //穿山甲视频广告容器
    private CardView mCsjAdVideoLayout;
    //广点通视频广告
    private GdtMediaViewContainer mGdtAdVideoLayout;
    //广点通广告必须设置使用的布局
    private NativeAdContainer mNativeAdContainer;

    private AdWrapper mAdWrapper = null;
    private JssdkFuliRewardCallback mJssdkFuliRewardCallback;
    //签到数据载体
    private SignInDialogDataModel mDataModel;

    private ImageView mAdTag;

    private CommonNativeDaTuAdProvider mDatuAdProvider;

    public static Bundle newArgument(SignInDialogDataModel dialogDataModel) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ARGUMENT_KEY_DIALOG_DATA_MODEL, dialogDataModel);
        return bundle;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mDatuAdProvider = new CommonNativeDaTuAdProvider(getActivity());
        Bundle arguments = getArguments();
        if (arguments != null) {
            mDataModel = arguments.getParcelable(ARGUMENT_KEY_DIALOG_DATA_MODEL);
        }
        if (mDataModel == null) {
            mDataModel = new SignInDialogDataModel();
        }
        View inflate = inflater.inflate(R.layout.main_fra_dialog_fuli_sign_in, container, false);
        mViewClose = inflate.findViewById(R.id.main_iv_close);

        mTvCoinNumber = inflate.findViewById(R.id.main_tv_coin_number);
        mCloseCountdownTime = inflate.findViewById(R.id.main_tv_close_countdown_time);
        mTvEarnListenTime = inflate.findViewById(R.id.main_tv_earn_listen_time);
        mTvMyBalanceExchange = inflate.findViewById(R.id.main_tv_my_balance_exchange);
        mLayoytMyCoinBalance = inflate.findViewById(R.id.main_ll_my_coin_balance);
        mTvEarnMore = inflate.findViewById(R.id.main_tv_coin_earn_more);
        mAdTag = inflate.findViewById(R.id.main_iv_ad_tag);

        mAdLayoutContent = inflate.findViewById(R.id.main_ad_listen_earn_dialog_bottom_content);
        mAdTvTitle = inflate.findViewById(R.id.main_ad_title);
        mAdIvImage = inflate.findViewById(R.id.main_ad_image);
        mCsjAdVideoLayout = inflate.findViewById(R.id.main_ad_video_layout);
        mGdtAdVideoLayout = inflate.findViewById(R.id.main_ad_gdt_video_layout);
        mNativeAdContainer = inflate.findViewById(R.id.main_ad_native_container);

        SignInConfigModel model = SignInConfigManager.getSignInConfigModel();
        mTvEarnMore.setText(model.text);

        //有广告数据，展示广告布局
        if (mAdWrapper != null && mAdWrapper.hasThirdAd()) {
            mAdLayoutContent.setVisibility(View.VISIBLE);
        } else {
            mAdLayoutContent.setVisibility(View.GONE);
            //没有广告增加底部间距
            if (mLayoytMyCoinBalance.getLayoutParams() instanceof RelativeLayout.LayoutParams) {
                RelativeLayout.LayoutParams myBalanceParams = (RelativeLayout.LayoutParams) mLayoytMyCoinBalance.getLayoutParams();
                myBalanceParams.bottomMargin = BaseUtil.dp2px(getActivity(), 12);
                mLayoytMyCoinBalance.setLayoutParams(myBalanceParams);
            }
        }

        if (TextUtils.isEmpty(mDataModel.signInDay)) {
            mDataModel.signInDay = "1";
        }
        String signDayText = mDataModel.signInDay;
        String amountText = mDataModel.amount + "金币";
        String signInfo = "已签到" + signDayText + "天，获得" + amountText;
        int dayIndex = signInfo.indexOf(signDayText);
        int amountIndex = signInfo.lastIndexOf(amountText);
        SpannableString spannableSignInfo = new SpannableString(signInfo);
        spannableSignInfo.setSpan(new ForegroundColorSpan(Color.parseColor("#E83F46")), dayIndex, dayIndex + signDayText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableSignInfo.setSpan(new ForegroundColorSpan(Color.parseColor("#E83F46")), amountIndex, amountIndex + amountText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        mTvCoinNumber.setText(spannableSignInfo);

        String myCoinBalance = mDataModel.myCoinBalance + "";

        String balanceTransform = StringUtil.balanceTransform(myCoinBalance);

        BigDecimal decimal = new BigDecimal(myCoinBalance);
        BigDecimal divide = decimal.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
        String myCoinExchangeInfo = balanceTransform + "≈" + divide.toString() + "元";

        SpannableString spannableExchangeInfo = new SpannableString(myCoinExchangeInfo);
        spannableExchangeInfo.setSpan(new ForegroundColorSpan(Color.parseColor("#666666")), 0, balanceTransform.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        mTvMyBalanceExchange.setText(spannableExchangeInfo);

        mViewClose.setAlpha(0.4f);
        mViewClose.setVisibility(View.GONE);
        mCloseCountdownTime.setVisibility(View.VISIBLE);
        mViewClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismissAllowingStateLoss();
                new XMTraceApi.Trace()
                        .setMetaId(10154)
                        .setServiceId("dialogClick")
                        .put("adId", getTrackAdId())
                        .put("item", "关闭")
                        .put("dialogType", "signAward")
                        .createTrace();
            }
        });
        mTvEarnMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!OneClickHelper.getInstance().onClick(view)) {
                    return;
                }
                ListenEarnCoinDialogManager.getInstance().loadVideoAdForSignInMultipleReward(mJssdkFuliRewardCallback);
                dismissAllowingStateLoss();

                new XMTraceApi.Trace()
                        .setMetaId(10160)
                        .setServiceId("dialogClick")
                        .put("adId", getTrackAdId())
                        .put("item", "金币翻倍")
                        .put("dialogType", "signAward")
                        .createTrace();
            }
        });
        AutoTraceHelper.bindData(mViewClose, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(mTvEarnMore, AutoTraceHelper.MODULE_DEFAULT, "");

        boolean isShowAdSuccess = updateAdUi();
        if (isShowAdSuccess) {
            //有广告展示的时候才启动倒计时
            startTimer();
        } else {
            mCloseCountdownTime.setVisibility(View.GONE);
            mViewClose.setVisibility(View.VISIBLE);
        }

        new XMTraceApi.Trace()
                .setMetaId(10152)
                .setServiceId("dialogView")
                .put("coincount", mDataModel.amount + "")
                .put("adId", getTrackAdId())
                .put("dialogType", "signAward")
                .createTrace();

        return inflate;
    }

    /**
     * 获取广告id
     */
    private String getTrackAdId() {
        //无广告展示则上报null
        if (mAdWrapper == null || mAdWrapper.getThirdAd() == null) {
            return "null";
        }
        return "";
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        //去掉标题
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialogInterface, int keyCode, KeyEvent keyEvent) {
                //运行关闭了，不再拦截返回按键
                if (mViewClose != null && mViewClose.getVisibility() == View.VISIBLE) {
                    return false;
                }
                //拦截返回按键，禁止返回关闭弹框
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    return true;
                }
                return false;
            }
        });
        Window window = dialog.getWindow();
        if (window != null) {
            //dialog背景设置透明，解决shape不生效问题
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setPadding(0, 0, 0, 0); //消除边距
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;   //设置宽度充满屏幕
            lp.height = WindowManager.LayoutParams.MATCH_PARENT;
            window.setAttributes(lp);
        }
        return dialog;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mMaskIsShow = false;
    }

    /**
     * 刷新广告ui
     */
    private boolean updateAdUi() {
        if (mAdWrapper == null || mAdWrapper.getThirdAd() == null) {
            mAdLayoutContent.setVisibility(View.GONE);
            return false;
        }
        AbstractThirdAd thirdAd = mAdWrapper.getThirdAd();
        if (thirdAd == null || thirdAd.getAdData() == null) {
            mAdLayoutContent.setVisibility(View.GONE);
            return false;
        }
        mAdLayoutContent.setOnClickListener(null);
        //处理自渲染通用操作
        //图片的宽度
        int imageWith = BaseUtil.dp2px(getActivity(), 280);
        //设置点击的view
        List<View> clickList = new ArrayList<>();
        clickList.add(mAdLayoutContent);
        //创建广告绑定相关View
        SimpleDaTuViewModel simpleDaTuViewModel = new SimpleDaTuViewModel(imageWith, clickList, mAdIvImage);
        //设置通用标题
        simpleDaTuViewModel.descView = mAdTvTitle;
        //设置穿山甲,百度等广告根布局
        simpleDaTuViewModel.adContentRootView = mAdLayoutContent;
        simpleDaTuViewModel.adTagView = mAdTag;
        simpleDaTuViewModel.csjVideoLayout = mCsjAdVideoLayout;


        //设置广点通视频广告
        simpleDaTuViewModel.gdtNativeAdContainer = mNativeAdContainer;
        simpleDaTuViewModel.gdtVideoContainer = mGdtAdVideoLayout;

        //绑定广告相关事件，埋点等
        boolean isBindSuccess = mDatuAdProvider.bindViewDatas(thirdAd, simpleDaTuViewModel, thirdAd.getPositionName());
        //绑定失败
        if (!isBindSuccess) {
            mAdLayoutContent.setVisibility(View.GONE);
            return false;
        }
        //绑定成功
        //展示广告界面
        mAdLayoutContent.setVisibility(View.VISIBLE);
        return true;
    }

    public boolean isShowing() {
        return mMaskIsShow;
    }

    @Override
    public int show(FragmentTransaction transaction, String tag) {
        if (mMaskIsShow) {
            return 0;
        }
        mMaskIsShow = true;
        return super.show(transaction, tag);
    }

    private void startTimer() {
        if (mTimer == null) {
            mTimer = new CountDownTimerFix(4000, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    long time = millisUntilFinished / 1000;
                    mCloseCountdownTime.setText(time + "");
                }

                @Override
                public void onFinish() {
                    mCloseCountdownTime.setVisibility(View.GONE);
                    mViewClose.setVisibility(View.VISIBLE);
                }
            };
        }
        stopTimer();
        mTimer.start();
    }

    private void stopTimer() {
        if (mTimer != null) {
            mTimer.cancel();
        }
    }


    @Override
    public void onResume() {
        super.onResume();

        mDatuAdProvider.onMyResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        stopTimer();

        mDatuAdProvider.onDestroy();
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        if (mMaskIsShow) {
            return;
        }
        mMaskIsShow = true;
        super.show(manager, tag);
    }

    public void setJssdkFuliRewardCallback(JssdkFuliRewardCallback jssdkFuliRewardCallback) {
        this.mJssdkFuliRewardCallback = jssdkFuliRewardCallback;
    }

    /**
     * 设置底部的广告数据
     *
     * @param mAdFeedAdBottom
     */
    public void updateAdFeedAdBottom(AdWrapper mAdFeedAdBottom) {
        this.mAdWrapper = mAdFeedAdBottom;
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }

}
