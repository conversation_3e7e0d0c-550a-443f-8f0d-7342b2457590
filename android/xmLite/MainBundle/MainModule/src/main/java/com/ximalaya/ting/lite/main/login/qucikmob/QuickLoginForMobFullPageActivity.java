/**
 * MySpaceActivity.java
 * com.ximalaya.ting.android.host.activity
 * <p/>
 * Function： TODO
 * <p/>
 * ver     date      		author
 * ──────────────────────────────────
 * 2015-11-25 		jack.qin
 * <p/
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.lite.main.login.qucikmob;

import android.content.Intent;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.loginservice.verify.VerifyManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

import javax.annotation.Nullable;


/**
 * <AUTHOR> on 2017/6/29.
 */
public class QuickLoginForMobFullPageActivity extends BaseFragmentActivity2 {

    @Nullable
    private QuickLoginForMobFullPageFragment mLoginFragment;

    @Override
    protected void onCreate(Bundle savedState) {
        super.onCreate(savedState);
        VerifyManager.preloadGTCaptchClient(this);
        Intent intent = getIntent();
        Bundle loginParams = new Bundle();
        if (intent != null && intent.getExtras() != null) {
            loginParams.putAll(intent.getExtras());
        }
        mLoginFragment = QuickLoginForMobFullPageFragment.newInstance(loginParams);
        addFragment(android.R.id.content, mLoginFragment);
    }

    public void startFragment(Fragment fragment) {
        if (isFinishing())
            return;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            if (isDestroyed())
                return;
        }
        if (fragment == null) return;
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(android.R.id.content, fragment);
        ft.addToBackStack(null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onResume() {
        super.onResume();
        //如果当前登陆状态,自己关掉,防止登录页在最上面覆盖现场
        if (UserInfoMannage.hasLogined()) { // 授权登录需要入 Task 中
            finish();
            return;
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        //fix java.lang.IllegalStateException: Can not perform this action after onSaveInstanceState
        invokeFragmentManagerNoteStateNotSaved();
    }

    //fix java.lang.IllegalStateException: Can not perform this action after onSaveInstanceState
    @Nullable
    private Method noteStateNotSavedMethod;
    @Nullable
    private Object fragmentMgr;
    private String[] activityClassName = {"Activity", "FragmentActivity"};

    private void invokeFragmentManagerNoteStateNotSaved() {
        //java.lang.IllegalStateException: Can not perform this action after onSaveInstanceState
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.HONEYCOMB) {
            return;
        }
        try {
            if (noteStateNotSavedMethod != null && fragmentMgr != null) {
                noteStateNotSavedMethod.invoke(fragmentMgr);
                return;
            }
            Class cls = getClass();
            do {
                cls = cls.getSuperclass();
                if (cls == null) {
                    break;
                }
            } while (!(activityClassName[0].equals(cls.getSimpleName())
                    || activityClassName[1].equals(cls.getSimpleName())));

            Field fragmentMgrField = prepareField(cls, "mFragments");
            if (fragmentMgrField != null) {
                fragmentMgr = fragmentMgrField.get(this);
                noteStateNotSavedMethod = getDeclaredMethod(fragmentMgr, "noteStateNotSaved");
                if (noteStateNotSavedMethod != null) {
                    noteStateNotSavedMethod.invoke(fragmentMgr);
                }
            }

        } catch (Exception ex) {
            Logger.e(ex);
            ex.printStackTrace();
        }
    }

    private Field prepareField(Class<?> c, String fieldName) throws NoSuchFieldException {
        while (c != null) {
            try {
                Field f = c.getDeclaredField(fieldName);
                f.setAccessible(true);
                return f;
            } finally {
                c = c.getSuperclass();
            }
        }
        throw new NoSuchFieldException();
    }

    @Nullable
    private Method getDeclaredMethod(Object object, String methodName, Class<?>... parameterTypes) {
        Method method = null;
        for (Class<?> clazz = object.getClass(); clazz != Object.class; clazz = clazz.getSuperclass()) {
            if (clazz == null) {
                return method;
            }
            try {
                method = clazz.getDeclaredMethod(methodName, parameterTypes);
                return method;
            } catch (Exception e) {
            }
        }
        return null;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        try {
            VerifyManager.onConfigurationChanged(newConfig);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        VerifyManager.destroyGTCaptchClient();
    }
}
