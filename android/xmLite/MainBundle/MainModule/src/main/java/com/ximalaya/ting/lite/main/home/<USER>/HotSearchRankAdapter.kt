package com.ximalaya.ting.lite.main.home.adapter

import android.annotation.SuppressLint
import android.util.SparseIntArray
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter
import kotlinx.android.synthetic.main.main_item_hot_search_rank_adapter_item.view.*

/**
 * Created by dumingwei on 2020/6/17
 *
 * Desc:
 */
class HotSearchRankAdapter(
        val mFragment: BaseFragment2
) : AbRecyclerViewAdapter<RecyclerView.ViewHolder>() {

    var mDataList: List<String>? = null
    private val mRankingPositionBg: SparseIntArray = SparseIntArray()
    private val halfWidth: Int

    init {
        // 前三分别使用的图片
        mRankingPositionBg.put(0, R.drawable.host_ic_hot_search_no_one)
        mRankingPositionBg.put(1, R.drawable.host_ic_hot_search_no_two)
        mRankingPositionBg.put(2, R.drawable.host_ic_hot_search_no_three)
        halfWidth = (BaseUtil.getScreenWidth(mFragment.context) - BaseUtil.dp2px(mFragment.context, 32f)) / 2
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = inflater.inflate(R.layout.main_item_hot_search_rank_adapter_item, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount(): Int {
        return mDataList?.size ?: 0
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is ViewHolder) {
            val searchWorld = mDataList?.get(position)
            val layoutParams = holder.itemView.layoutParams as RecyclerView.LayoutParams
            layoutParams.width = halfWidth
            holder.itemView.layoutParams = layoutParams

            with(holder.itemView) {
                searchWorld?.let {
                    if (position < 3) {
                        mainTvRankNumber.text = ""
                    } else {
                        mainTvRankNumber.text = "${position + 1}"
                    }
                    //SparseArray找不到，返回0，最终会将背景清除
                    mainTvRankNumber.setBackgroundResource(mRankingPositionBg.get(position))
                    mainTvRankTitle.text = searchWorld
                    //竖直的分割线居中，分割线右边的item显示占位的space
                    if (position >= 5) {
                        mainSpaceOfRight.visibility = View.VISIBLE
                    } else {
                        mainSpaceOfRight.visibility = View.GONE
                    }

                    holder.itemView.setOnClickListener {
                        handleItemClick(searchWorld)
                    }
                }
            }
        }
    }

    private fun handleItemClick(searchWorld: String) {
        val fragmentAction = SearchActionRouter.getInstance().fragmentAction
        fragmentAction?.let {
            val fragment = fragmentAction.newSearchFragmentByWordAndSearchNow(searchWorld)
            mFragment.startFragment(fragment)
        }
    }

    override fun getItem(position: Int): Any? {
        if (CollectionUtil.isNotEmpty(mDataList)) {
            return mDataList?.get(position)
        }
        return null
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

}