package com.ximalaya.ting.lite.main.base;

import androidx.annotation.ColorRes;
import androidx.annotation.Nullable;

import android.view.View;

import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.listener.IGotoTop;

/**
 * <AUTHOR> on 2017/11/30.
 */

public abstract class GotoTopFragment extends BaseFragmentInMain {

    public GotoTopFragment() {

    }


    public GotoTopFragment(boolean canSlide, SlideView.IOnFinishListener onFinishListener) {
        super(canSlide, onFinishListener);
    }

    public GotoTopFragment(boolean canSiled, int slideViewContentViewLayoutType, @Nullable SlideView.IOnFinishListener onFinishListener) {
        super(canSiled, slideViewContentViewLayoutType, onFinishListener);
    }


    public GotoTopFragment(boolean canSlided, @SlideView.SlideViewContentViewLayoutType int contentViewType, @Nullable SlideView.IOnFinishListener onFinishListener, @ColorRes int contentViewBackgroundColor) {
        super(canSlided, contentViewType, onFinishListener, contentViewBackgroundColor);
    }

    public GotoTopFragment(boolean canSlided, int contentViewType, @Nullable SlideView.IOnFinishListener onFinishListener, boolean fullSlide) {
        super(canSlided, contentViewType, onFinishListener, fullSlide);
    }

    @Override
    public void onResume() {
        super.onResume();

        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(mTopBtnListener);
        }
    }

    @Override
    public void onPause() {
        super.onPause();

        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
    }

    public abstract void gotoTop();

    private IGotoTop.IGotoTopBtnClickListener mTopBtnListener = new IGotoTop.IGotoTopBtnClickListener() {
        @Override
        public void onClick(View v) {
            if (isRealVisable()) {
                gotoTop();
            }
        }
    };
}
