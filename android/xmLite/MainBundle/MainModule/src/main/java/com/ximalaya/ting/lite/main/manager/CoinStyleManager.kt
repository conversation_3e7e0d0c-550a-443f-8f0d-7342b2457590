package com.ximalaya.ting.lite.main.manager

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.support.rastermill.FrameSequenceDrawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.google.gson.Gson
import com.ximalaya.ting.android.adsdk.external.feedad.ILiteFeedAd
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.GifHelper
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.safe.SafeJiaMiRequestM
import com.ximalaya.ting.android.host.manager.safe.SafeJiaMiUrlConstants
import com.ximalaya.ting.android.host.util.ContextUtils
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.lite.main.model.CoinStyleModel
import com.ximalaya.ting.lite.main.request.LiteUrlConstants
import org.json.JSONObject
import java.lang.ref.SoftReference

object CoinStyleManager : Application.ActivityLifecycleCallbacks {

    private const val TAG = "CoinStyleManager"

    // 最大次数key
    private const val KEY_MAX_COUNT_DATE = "coin_style_max_count_date"

    // 显示金币界面的数据 hashcode 金币数
    private val mADDataMap = LinkedHashMap<String, Int>()

    private val mHandler = Handler(Looper.getMainLooper())

    private var mHiddenViewRun: HiddenViewRun? = null

    private var isCheckResult = false

    fun init(app: Application?) {
        isCheckResult = false
        app?.registerActivityLifecycleCallbacks(this)
    }

    fun destroy(app: Application?) {
        app?.unregisterActivityLifecycleCallbacks(this)
        mHiddenViewRun?.run()
        mHiddenViewRun = null
    }

    fun isCoinStyle(ad: ILiteFeedAd?): Boolean {
        if (ad == null) {
            return false
        }

        if (ad.goldCoinsNum <= 0 || TextUtils.isEmpty(ad.goldCoinsNumEncrypt)) {
            return false
        }

        if (!UserInfoMannage.hasLogined()) {
            FuliLogger.log(TAG, "未登录不处理")
            return false
        }

        val date = MMKVUtil.getInstance().getString(KEY_MAX_COUNT_DATE + UserInfoMannage.getUid())
        if (DateTimeUtil.getCurrentDate4yyyyMMdd().equals(date)) {
            FuliLogger.log(TAG, "当天已达最大领取次数不显示")
            return false
        }

        return true
    }

    private fun isCoinStyle(adverts: Advertis?): Boolean {
        if (adverts == null) {
            return false
        }

        if (adverts.goldCoinsNum <= 0 || TextUtils.isEmpty(adverts.goldCoinsNumEncrypt)) {
            return false
        }

        if (!UserInfoMannage.hasLogined()) {
            FuliLogger.log(TAG, "未登录不处理")
            return false
        }

        val date = MMKVUtil.getInstance().getString(KEY_MAX_COUNT_DATE + UserInfoMannage.getUid())
        if (DateTimeUtil.getCurrentDate4yyyyMMdd().equals(date)) {
            FuliLogger.log(TAG, "当天已达最大领取次数不显示")
            return false
        }

        return true
    }


    fun showCoinStyle(parentView: ViewGroup?, adverts: Advertis?): Boolean {
        if (parentView == null || adverts == null) {
            return false
        }

        if (!isCoinStyle(adverts)) {
            return false
        }

        return doShowCoinView(parentView, adverts.goldCoinsNum)

    }

    fun showCoinStyle(parentView: ViewGroup?, ad: ILiteFeedAd?): Boolean {
        if (parentView == null || ad == null) {
            return false
        }

        if (!isCoinStyle(ad)) {
            return false
        }

        return doShowCoinView(parentView, ad.goldCoinsNum)

    }

    @SuppressLint("SetTextI18n")
    fun doShowCoinView(parentView: ViewGroup, coinNum: Int): Boolean {
        if (!ContextUtils.checkContext(parentView.context)) {
            return false
        }
        val coinView = LayoutInflater.from(parentView.context)
            .inflate(R.layout.main_lite_coin_style_layout, parentView, false)
        val tvCoin = coinView.findViewById<TextView>(R.id.main_coin_style_tv_coin_num)
        val mIvCoin: ImageView? = coinView.findViewById(R.id.main_coin_style_iv_coin)
        tvCoin?.text = "+${coinNum}金币"
        try {
            parentView.removeAllViews()
            parentView.addView(coinView)
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }

        mIvCoin?.apply {
            GifHelper.fromRawResource(
                resources,
                R.drawable.main_ic_coin_style_coin_animal
            ) { drawable ->
                if (ContextUtils.checkContext(parentView.context)) {
                    if (drawable == null) {
                        ImageManager.from(parentView.context).displayImage(
                            mIvCoin,
                            "",
                            R.drawable.main_ic_coin_style_coin_animal,
                            -1
                        )
                    } else {
                        drawable.setScaleType(mIvCoin.scaleType)
                        drawable.setLoopBehavior(FrameSequenceDrawable.LOOP_FINITE)
                        drawable.setOnFinishedListener {
                            mIvCoin.setImageResource(R.drawable.main_ic_coin_style_coin)
                        }
                        mIvCoin.setImageDrawable(drawable)
                        drawable.start()
                    }
                }
            }
        }
        return true
    }

    fun clickCoinStyle(ad: ILiteFeedAd?) {

        if (ad == null) return

        if (!isCoinStyle(ad)) {
            return
        }

        val activity = BaseApplication.getTopActivity()
        if (!ContextUtils.checkActivity(activity)) {
            return
        }

        // 记录当前广告  当前界面
        val key = activity.hashCode().toString()
        mADDataMap[key] = 0

        // code -1015：次数达到上限  -1012 ：加密信息错误
        reportActiveCoin(
            ad.goldCoinsNum,
            ad.goldCoinsNumEncrypt,
            object : IDataCallBack<CoinStyleModel?> {
                override fun onSuccess(model: CoinStyleModel?) {
                    FuliLogger.log(TAG, "onSuccess model:$model")
                    performActiveCoinResult(model, key)
                }

                override fun onError(code: Int, message: String?) {
                    FuliLogger.log(TAG, "onError code:$code message:$message")
                    performActiveCoinResult(null, key)
                }
            })
    }

    fun clickCoinStyle(adverts: Advertis?) {
        if (!isCoinStyle(adverts)) {
            return
        }

        val activity = BaseApplication.getTopActivity()
        if (!ContextUtils.checkActivity(activity)) {
            return
        }

        // 记录当前广告  当前界面
        val key = activity.hashCode().toString()
        mADDataMap[key] = 0

        // code -1015：次数达到上限  -1012 ：加密信息错误
        reportActiveCoin(
            adverts!!.goldCoinsNum,
            adverts.goldCoinsNumEncrypt,
            object : IDataCallBack<CoinStyleModel?> {
                override fun onSuccess(model: CoinStyleModel?) {
                    FuliLogger.log(TAG, "onSuccess model:$model")
                    performActiveCoinResult(model, key)
                }

                override fun onError(code: Int, message: String?) {
                    FuliLogger.log(TAG, "onError code:$code message:$message")
                    performActiveCoinResult(null, key)
                }
            })
    }

    private fun performActiveCoinResult(model: CoinStyleModel?, key: String) {
        if (model == null) {
            mADDataMap.remove(key)
            return
        }
        when (model.code) {
            0 -> {
                isCheckResult = true
                mADDataMap[key] = model.score

                // 没有领取次数了
                if (model.availableTimes <= 0) {
                    MMKVUtil.getInstance().saveString(
                        KEY_MAX_COUNT_DATE + UserInfoMannage.getUid(),
                        DateTimeUtil.getCurrentDate4yyyyMMdd()
                    )
                }
            }
            -1015 -> {
                mADDataMap.remove(key)
                // 领取次数超过了
                MMKVUtil.getInstance().saveString(
                    KEY_MAX_COUNT_DATE + UserInfoMannage.getUid(),
                    DateTimeUtil.getCurrentDate4yyyyMMdd()
                )
            }
            -1012 -> {
                mADDataMap.remove(key)
                FuliLogger.log(TAG, "验签失败:$model")
            }
            else -> {
                mADDataMap.remove(key)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun checkShowCoinResult(activity: Activity) {
        // 没有请求结果不检查 防止每个界面变化都要循环
        if (!isCheckResult) {
            FuliLogger.log(TAG, "checkShowCoinStyle isCheckResult:$isCheckResult")
            return
        }

        if (!ContextUtils.checkActivity(activity)) {
            return
        }

        val key = findAdvertKey(activity.hashCode().toString())
        FuliLogger.log(TAG, "checkShowCoinStyle key:$key")
        if (key == null) {
            return
        }
        val coinNum = mADDataMap.remove(key)
        FuliLogger.log(TAG, "checkShowCoinStyle result:$coinNum")
        if (coinNum != null && coinNum > 0) {
            val decorView = activity.window.decorView as ViewGroup
            //显示弹窗
            val coinLayout = LayoutInflater.from(activity)
                .inflate(R.layout.main_lite_coin_style_result_layout, decorView, false)

            val mTvCoinNum: TextView? =
                coinLayout.findViewById(R.id.main_coin_style_tv_coin_content)

            activity.addContentView(coinLayout, coinLayout.layoutParams)
            mTvCoinNum?.text = "+${coinNum}"

            mHiddenViewRun?.apply {
                run()
                mHandler.removeCallbacks(this)
            }
            mHiddenViewRun = HiddenViewRun(activity, coinLayout)
            mHiddenViewRun?.apply {
                mHandler.postDelayed(this, 2500)
            }
        }

        // 重置
        if (mADDataMap.isEmpty()) {
            isCheckResult = false
        }
    }

    /**
     * 获取广告key
     */
    private fun findAdvertKey(data: String): String? {
        val iterator = mADDataMap.iterator()
        while (iterator.hasNext()) {
            val item = iterator.next()
            // 同一个界面可能同时显示多个广告  匹配获取到了金币的值
            if (item.key == data && item.value > 0) {
                return item.key
            }
        }
        return null
    }

    /**
     * 删除缓存的数据
     */
    private fun removeSaveAdvertData(removeActivity: Activity) {
        mHiddenViewRun?.apply {
            val result = activityRef.get()
            // 已经被销毁或者 当前界面开始销毁
            if (result == null || result == removeActivity) {
                run()
                mHandler.removeCallbacks(this)
                mHiddenViewRun = null
            }
        }

        val iterator = mADDataMap.iterator()
        val hashCode = removeActivity.hashCode().toString()
        while (iterator.hasNext()) {
            val item = iterator.next()
            if (item.key == hashCode) {
                iterator.remove()
            }
        }
    }

    private class HiddenViewRun(activity: Activity?, curView: View?) : Runnable {

        var activityRef = SoftReference(activity)
        var viewRef = SoftReference(curView)

        override fun run() {
            val curView = viewRef.get()
            if (curView != null) {
                try {
                    val parent = curView.parent
                    if (parent is ViewGroup) {
                        parent.removeView(curView)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }


    private fun reportActiveCoin(
        score: Int,
        signature: String?,
        callback: IDataCallBack<CoinStyleModel?>
    ) {
        val map = mutableMapOf<String, String>()
        map["score"] = score.toString()
        map["signature"] = signature ?: ""
        map["timestamp"] = System.currentTimeMillis().toString()


        val iRequestCallBack = CommonRequestM.IRequestCallBack { content: String? ->
            try {
                val json = JSONObject(content)
                val code = json.optInt("code", -1)
                var scoreData = 0
                var availableTimes = 0
                if (code == 0) {
                    val data = json.optJSONObject("data")
                    scoreData = data?.optInt("score") ?: 0
                    availableTimes = data?.optInt("availableTimes") ?: 0
                }
                val model = CoinStyleModel(code, scoreData, availableTimes)
                model
            } catch (e: Throwable) {
                null
            }
        }

//        CommonRequestM.basePostRequestParmasToJson(
//            LiteUrlConstants.activeCoin(),
//            map,
//            callback, iRequestCallBack
//        )
        //切换为安全结果
        SafeJiaMiRequestM.postSafeJiaMiRequest(
            SafeJiaMiUrlConstants.activeCoin(),
            SafeJiaMiRequestM.SafeBusinessScene_receive_coins,
            Gson().toJson(map),
            callback,
            iRequestCallBack
        )
    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
    }

    override fun onActivityStarted(activity: Activity) {
    }

    override fun onActivityResumed(activity: Activity) {
        try {
            checkShowCoinResult(activity)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onActivityPaused(activity: Activity) {
    }

    override fun onActivityStopped(activity: Activity) {
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }

    override fun onActivityDestroyed(activity: Activity) {
        removeSaveAdvertData(activity)
    }
}