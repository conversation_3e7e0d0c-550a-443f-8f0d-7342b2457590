package com.ximalaya.ting.lite.main.album.dialog

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.FileUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.view.ImageViewer
import com.ximalaya.ting.android.host.view.other.LocalTemplateWebView
import com.ximalaya.ting.android.host.view.other.RichWebView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.util.AsyncGson
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.model.album.AlbumIntroDetailTemplateModel


/**
 * Created by dumingwei on 2021/5/22
 *
 * Desc:完整简介弹窗
 */
class LiteFullIntroDialog : BaseFullScreenDialogFragment(), View.OnClickListener, RichWebView.IOnImageClickListener {


    companion object {

        const val DEFAULT_ALBUM_COVER_URL = "https://fdfs.xmcdn.com/group45/M00/63/69/wKgKlFuOScHhcPyaAAAnJk4mQ3M951.png"

        @JvmStatic
        fun newInstance(data: AlbumM): LiteFullIntroDialog {
            val args = Bundle()
            args.putParcelable(BundleKeyConstants.KEY_ALBUM, data)
            val fragment = LiteFullIntroDialog()
            fragment.arguments = args
            return fragment
        }
    }

    private lateinit var rlRootContainer: RelativeLayout

    private var mImageViewer: ImageViewer? = null

    private var albumM: AlbumM? = null

    private var mRichContent: LocalTemplateWebView? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        albumM = arguments?.getParcelable(BundleKeyConstants.KEY_ALBUM)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val inflaterView = inflater.inflate(R.layout.main_dialog_album_full_intro, container, false)
        val clContainer = inflaterView.findViewById<ConstraintLayout>(R.id.main_cl_container)

        val lp = clContainer.layoutParams as RelativeLayout.LayoutParams
        val screenHeight = BaseUtil.getScreenHeight(context)
        lp.height = (screenHeight / 4f * 3f).toInt()

        clContainer.layoutParams = lp

        rlRootContainer = inflaterView.findViewById<RelativeLayout>(R.id.main_rl_root_container)

        rlRootContainer.setOnClickListener(this)

        val tvClose = inflaterView.findViewById<TextView>(R.id.main_tv_close)
        tvClose.setOnClickListener(this)
        AutoTraceHelper.bindData(tvClose, AutoTraceHelper.MODULE_DEFAULT, "")

        mRichContent = inflaterView.findViewById(R.id.main_webview_content)
        mRichContent?.visibility = View.INVISIBLE
        mRichContent?.setOnImageClickListener(this)
        //极速版禁止专辑详情跳转
        mRichContent?.setURLClickListener { true }
        // 支持上下文复制
        mRichContent?.enableSelectCopy()

        return inflaterView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        traceShow()
        setDataForView()
    }

    private fun traceShow() {
        XMTraceApi.Trace()
                .setMetaId(32267)
                .setServiceId("dialogView")
                .createTrace()
    }

    override fun isShowFromBottomEnable() = true

    override fun onClick(view: View) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return
        }
        when (view.id) {
            R.id.main_tv_close -> dismiss()
        }
        when (view.id) {
            R.id.main_rl_root_container -> dismiss()
        }
    }

    override fun onClick(list: MutableList<ImageViewer.ImageUrl>?, index: Int) {
        val activity = activity ?: return

        if (mImageViewer == null) {
            mImageViewer = ImageViewer(activity)
        }
        mImageViewer?.setImageUrls(list)
        mImageViewer?.show(index, view)
    }

    private fun setDataForView() {
        albumM?.let {
            setUpRichTextModule(it)

        }
    }

    private fun setUpRichTextModule(album: AlbumM?) {
        if (album != null) {
            val templateModel = AlbumIntroDetailTemplateModel()
            if (!TextUtils.isEmpty(album.validCover)) {
                templateModel.setCover(album.validCover)
            } else {
                templateModel.setCover(DEFAULT_ALBUM_COVER_URL)
            }
            templateModel.setIntro(album.introRich)
            templateModel.setOutline(album.outline)
            templateModel.setSalePoint(album.salePointPopup)
            templateModel.setTitle(album.albumTitle)
            AsyncGson<String>().toJson(templateModel, object : AsyncGson.IResult<String> {
                override fun postResult(result: String) {
                    var richTemplate = FileUtil.readAssetFileData(context, "albumDetailTemplate/index.html")
                    if (richTemplate.contains("var tplData")) {
                        richTemplate = richTemplate.replace("var tplData =", "var tplData = $result")
                    }
                    if (canUpdateUi()) {
                        mRichContent?.setData(richTemplate)
                        mRichContent?.visibility = View.VISIBLE
                    }
                }

                override fun postException(e: Exception) {}
            })
        }
    }

}