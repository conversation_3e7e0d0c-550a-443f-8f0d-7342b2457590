package com.ximalaya.ting.lite.main.home.view

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.constant.DPConstants
import kotlinx.android.synthetic.main.main_first_level_meta_data_view.view.*

/**
 * Created by du<PERSON><PERSON> on 2021/1/12
 *
 * Desc: 新的筛选View
 */
class FirstLevelMetaDataView @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    private var selectedColor = 0
    private var unSelectedColor = 0

    private var dp2 = 0
    private var dp6 = 0
    private var dp10 = 0

    init {
        View.inflate(context, R.layout.main_first_level_meta_data_view, this)
        selectedColor = resources.getColor(R.color.main_color_e83f46)
        unSelectedColor = resources.getColor(R.color.main_color_999999)

        dp2 = DPConstants.getInstance(getContext()).DP_2
        dp6 = DPConstants.getInstance(getContext()).DP_6
        dp10 = DPConstants.getInstance(getContext()).DP_10
    }

    /**
     * @param chosen 是否被选中
     * @param showArrow 是否显示向上的箭头，如果文字有全部，则不显示箭头
     */
    @JvmOverloads
    fun setChosen(chosen: Boolean, showArrow: Boolean = true) {
        if (chosen) {
            setTextViewBold(main_tv_display_name, true)
            main_tv_display_name.setTextColor(selectedColor)
            main_tv_display_name.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12)
            main_tv_display_name.setPadding(dp10, dp2, dp10, dp2)
            if (showArrow) {
                main_iv_up_arrow.visibility = View.VISIBLE
            } else {
                main_iv_up_arrow.visibility = View.GONE
            }
        } else {
            setTextViewBold(main_tv_display_name, false)
            main_tv_display_name.setTextColor(unSelectedColor)
            main_tv_display_name.background = null
            main_tv_display_name.setPadding(dp6, dp2, dp6, dp2)
            main_iv_up_arrow.visibility = View.GONE
        }
    }

    fun setText(text: String?) {
        main_tv_display_name.text = text
    }

    fun setTextViewBold(textView: TextView?, isBold: Boolean) {
        if (textView != null) {
            if (isBold) {
                val familyName = "sans-serif-light"
                val boldTypeface = Typeface.create(familyName, Typeface.BOLD)
                textView.typeface = boldTypeface
            } else {
                textView.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            }
        }
    }

}