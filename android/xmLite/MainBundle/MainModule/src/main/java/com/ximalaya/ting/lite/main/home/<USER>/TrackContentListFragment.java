package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.lite.main.base.FreeTrackAdapter;
import com.ximalaya.ting.lite.main.request.HttpParamsConstantsInMain;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.request.LiteUrlConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 声音免费畅听列表
 *
 * <AUTHOR>
 */
public class TrackContentListFragment extends BaseFragment2 implements AdapterView.OnItemClickListener, IRefreshLoadMoreListener {

    private RefreshLoadMoreListView mListView;
    private int mPageId = 1;
    private boolean mIsLoading = false;
    private FreeTrackAdapter mAdapter;
    private ImageView vNoContent;
    private List<TrackM> albumList = new ArrayList<>();
    private HashMap<String, TrackM> trackMHashMap = new HashMap<>();

    private String title;
    private int poolId;//内容池id

    public static TrackContentListFragment newInstance(String title, int poolId) {
        Bundle args = new Bundle();
        args.putString(BundleKeyConstants.KEY_TITLE, title);
        args.putInt(BundleKeyConstants.CONTENT_POOL_ID, poolId);
        TrackContentListFragment fragment = new TrackContentListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_free_track_list;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            title = arguments.getString(BundleKeyConstants.KEY_TITLE);
            poolId = arguments.getInt(BundleKeyConstants.CONTENT_POOL_ID);
        }
        //需要标题栏，设置为可滑动返回
        setCanSlided(true);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mListView = findViewById(R.id.main_listview);
        mAdapter = new FreeTrackAdapter(mContext, albumList, this);
        initFooterView();
        mListView.setAdapter(mAdapter);
        mListView.setOnRefreshLoadMoreListener(this);
        mListView.setOnItemClickListener(this);
        setTitle(title);
        refresh();
    }

    private void initFooterView() {
        LinearLayout ll = new LinearLayout(getActivity());
        ll.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        ll.setGravity(Gravity.CENTER);
        vNoContent = new ImageView(getActivity());
        vNoContent.setPadding(0, BaseUtil.dp2px(mContext, 30), 0, 0);
        vNoContent.setImageResource(R.drawable.main_bg_meta_nocontent);
        ll.addView(vNoContent);
        vNoContent.setVisibility(View.GONE);
        mListView.getRefreshableView().addFooterView(ll);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        List<TrackM> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (index < 0 || index >= listData.size()) {
            return;
        }
    }

    @Override
    protected void loadData() {
        if (mIsLoading) {
            return;
        }
        if (canUpdateUi() && mAdapter != null) {
            onPageLoadingCompleted(BaseFragment.LoadCompleteType.LOADING);
        }
        mIsLoading = true;

        loadGuessLikeListRefresh();
    }

    private void refresh() {
        mPageId = 1;
        if (mListView != null) {
            mListView.setFooterViewVisible(View.VISIBLE);
        }
        loadData();
    }

    @Override
    public void onRefresh() {
        refresh();
    }

    @Override
    public void onMore() {
        mPageId++;
        loadData();
    }

    private void loadGuessLikeListRefresh() {
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstantsInMain.PARAM_UID, String.valueOf(UserInfoMannage.getUid()));
        params.put("poolId", String.valueOf(poolId));
        params.put(HttpParamsConstants.PARAM_PAGE_ID, String.valueOf(mPageId));
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, "20");
        IDataCallBack<List<TrackM>> callBack = new IDataCallBack<List<TrackM>>() {
            @Override
            public void onSuccess(final List<TrackM> list) {
                mIsLoading = false;
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        try {
                            if (!canUpdateUi()) {
                                return;
                            }
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            boolean isNoContent = CollectionUtil.isNullOrEmpty(list);
                            if (isNoContent) {
                                mListView.setHasMoreNoFooterView(false);
                                List<TrackM> listData = mAdapter.getListData();
                                if (CollectionUtil.isNullOrEmpty(listData)) {
                                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                                }
                                return;
                            }
                            //加载数据
                            onLoadSuccess(list);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                onLoadFailure(message);
            }
        };
        LiteCommonRequest.getTrackContentPoolData(LiteUrlConstants.newListenContentPollUrl(), params, callBack);
    }

    /**
     * 数据加载成功
     */
    private void onLoadSuccess(final List<TrackM> list) {
        List<TrackM> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (mPageId == 1) {
            listData.clear();
            trackMHashMap.clear();
        }
        listData.addAll(removeSameTrack(list));
        mListView.onRefreshComplete(true);
    }

    /**
     * 去掉重复数据
     *
     * @param list
     * @return
     */
    private List<TrackM> removeSameTrack(List<TrackM> list) {
        List<TrackM> trackMList = new ArrayList<>();
        for (TrackM trackM : list) {
            String dataId = String.valueOf(trackM.getDataId());
            if (trackMHashMap.get(dataId) == null) {
                trackMList.add(trackM);
                trackMHashMap.put(dataId, trackM);
            }
        }
        return trackMList;
    }

    /**
     * 数据加载失败
     */
    private void onLoadFailure(String message) {
        mIsLoading = false;
        if (!canUpdateUi()) {
            return;
        }
        if (mPageId == 1) {
            mAdapter.clear();
            mListView.onRefreshComplete(true);
            mListView.setHasMoreNoFooterView(false);
            onPageLoadingCompleted(BaseFragment.LoadCompleteType.NETWOEKERROR);
        } else {
            CustomToast.showFailToast(message);
            mListView.onRefreshComplete(true);
        }
    }

    @Override
    protected void loadDataError() {
        if (mListView != null) {
            mListView.setMode(Mode.DISABLED);
            mListView.setHasMoreNoFooterView(false);
        }
    }

    @Override
    protected void loadDataOk() {
        if (mListView != null) {
            mListView.setMode(Mode.PULL_FROM_START);
        }
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }


    @Override
    protected String getPageLogicName() {
        return "TrackContentListFragment";
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (trackMHashMap != null) {
            trackMHashMap.clear();
        }
        XmPlayerManager.getInstance(mActivity).removePlayerStatusListener(mAdapter);
    }
}