package com.ximalaya.ting.lite.main.model.album;

import android.content.Context;
import androidx.collection.ArrayMap;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2017/8/16.
 */

public class Channel {
    public static final String CHANNEL_ONE_KEY = "ONE_KEY";
    public static final String CHANNEL_DAILY_REC = "DT";
    public static final String CHANNEL_TOU_TIAO = "TOUTIAO";


    public long channelId;    //频道Id
    public String channelName;  //频道名字
    public float ratio;   //收听比率阈值
    public String cover;    //频道封面
    public String bigCover; // 老封面(原图封面)
    public int duration; //收听时长
    public String recSrc;         //推荐跟踪字段
    public String recTrack;        //推荐跟踪字段
    public int curPlayIndex;
    public List<Track> tracks = new ArrayList<>();
    public String channelCoverPlay; //极速版一键听页面渠道背景图

    @SerializedName("channelProperty")
    public String channelProperty;//ONE_KEY
    @SerializedName("jumpUrl")
    public String jumpUrl;
    @SerializedName("playUrl")
    public String playUrl;
    @SerializedName("slogan")
    public String slogan;
    @SerializedName("playParam")
    public PlayParam playParam;
    @SerializedName("createdAt")
    public long createdAt;
    @SerializedName("subscribe")
    public boolean subscribe;

    // 额外数据
    public int section;
    public String categoryName;
    public boolean isFirstIncategory;

    public Channel() {}

    public Channel(long channelId) {
        this.channelId = channelId;
    }

    public static Channel create(Channel channel) {
        Channel newChannel = new Channel();
        newChannel.channelId = channel.channelId;
        newChannel.channelName = channel.channelName;
        newChannel.ratio = channel.ratio;
        newChannel.cover = channel.cover;
        newChannel.bigCover = channel.bigCover;
        newChannel.duration = channel.duration;
        newChannel.recSrc = channel.recSrc;
        newChannel.recTrack = channel.recTrack;
        newChannel.curPlayIndex = channel.curPlayIndex;
        newChannel.tracks = channel.tracks;
        newChannel.channelProperty = channel.channelProperty;
        newChannel.jumpUrl = channel.jumpUrl;
        newChannel.playUrl = channel.playUrl;
        newChannel.slogan = channel.slogan;
        newChannel.playParam = channel.playParam;
        newChannel.createdAt = channel.createdAt;
        newChannel.subscribe = channel.subscribe;
        newChannel.section = channel.section;
        newChannel.categoryName = channel.categoryName;
        newChannel.isFirstIncategory = channel.isFirstIncategory;
        return newChannel;
    }

    public static boolean isPlayInChannel(Context mContext, Channel channel) {
        Track curTrack = PlayTools.getCurTrack(mContext);
        if (curTrack == null || channel == null)
            return false;

        return curTrack.getChannelId() == channel.channelId;
    }


    public static class PlayParam{
        @SerializedName("pageSize")
        public int pageSize;
        @SerializedName("albumId")
        public int albumId;
        @SerializedName("pageId")
        public int pageId;
        @SerializedName("isWrap")
        public boolean isWrap;
        @SerializedName("tabid")
        public int tabid;

        public Map<String, String> createParamsMap() {
            Map<String, String> params = new ArrayMap<>(4);
            params.put("pageSize", pageSize + "");
            params.put("albumId", albumId + "");
            params.put("pageId", pageId + "");
            params.put("isWrap", isWrap + "");
            params.put("tabId",tabid + "");
            return params;
        }
    }


}
