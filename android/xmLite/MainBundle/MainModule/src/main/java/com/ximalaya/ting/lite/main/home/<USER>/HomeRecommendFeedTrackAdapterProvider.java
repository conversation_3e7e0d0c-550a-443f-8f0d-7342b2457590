package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.LeadingMarginSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTrackCookie;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.shareservice.base.IShareDstType;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.view.DislikeFeedbackWindowNew;
import com.ximalaya.ting.android.host.model.album.DislikeReason;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;
import com.ximalaya.ting.lite.main.model.album.RecommendTrackItem;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.utils.ShareUtilsInMain;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created by WolfXu on 2018/5/29.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class HomeRecommendFeedTrackAdapterProvider implements IMulitViewTypeViewAndData {

    private BaseFragment2 mFragment;
    private Activity mActivity;
    private HomeRecommedExtraDataProvider dataProvider;
    private IRecommendFeedItemActionListener recommendFeedItemActionListener;

    public HomeRecommendFeedTrackAdapterProvider(BaseFragment2 fragment, HomeRecommedExtraDataProvider listDataProvider,
                                                 IRecommendFeedItemActionListener recommendFeedItemActionListener) {
        mFragment = fragment;
        dataProvider = listDataProvider;
        mActivity = fragment.getActivity();
        this.recommendFeedItemActionListener = recommendFeedItemActionListener;
    }

    @Override
    public void bindViewDatas(HolderAdapter.BaseViewHolder viewHolder, final ItemModel t, View convertView, final int position) {
        if (viewHolder == null || t == null) {
            return;
        }
        if (!(t.object instanceof RecommendItemNew)) {
            return;
        }
        final RecommendItemNew recommendItem = (RecommendItemNew) t.object;
        if (!(recommendItem.getItem() instanceof RecommendTrackItem)) {
            return;
        }
        final RecommendTrackItem track = (RecommendTrackItem) recommendItem.getItem();
        final TrackViewHolder holder = (TrackViewHolder) viewHolder;
        ImageManager.from(mActivity).displayImage(mFragment, holder.ivTrackCover, track.getValidCover(), R.drawable.host_default_album_145);
        // 根据状态设置标题颜色和前面的图标
        AnimationUtil.stopAnimation(holder.ivPlayBtn);
        holder.ivPlayBtn.setImageResource(R.drawable.main_btn_feed_stream_track_play);
        if (isCurrentTrack(track.getDataId())) {
            holder.tvTitle.setTextColor(Color.parseColor("#f86442"));
            if (XmPlayerManager.getInstance(mActivity).isPlaying()) {
                holder.ivPlayBtn.setImageResource(R.drawable.main_btn_feed_stream_track_pause);
            } else if (XmPlayerManager.getInstance(mActivity).isBuffering()) {
                holder.ivPlayBtn.setImageResource(R.drawable.main_img_feed_stream_track_loading);
                AnimationUtil.rotateView(mActivity, holder.ivPlayBtn);
            }
            holder.llTrackInfo.setVisibility(View.GONE);
            holder.llShare.setVisibility(View.VISIBLE);
        } else {
            if (track.isClicked()) {
                holder.tvTitle.setTextColor(Color.parseColor("#999999"));
            } else {
                holder.tvTitle.setTextColor(ContextCompat.getColor(mActivity, R.color.main_color_black));
            }
            holder.llTrackInfo.setVisibility(View.VISIBLE);
            holder.llShare.setVisibility(View.GONE);
        }

        // 播放中，显示音波动效
        if (isCurrentTrackPlaying(track.getDataId())) {
            holder.ivPlayingIconBeforeTitle.setVisibility(View.VISIBLE);
            ((AnimationDrawable) holder.ivPlayingIconBeforeTitle.getDrawable()).start();
            // 标题首行缩进一些，留位置给前面的图标
            SpannableString spannableString = new SpannableString(track.getTrackTitle());
            LeadingMarginSpan.Standard leadingMarginSpan = new LeadingMarginSpan.Standard(BaseUtil.dp2px(mActivity, 16), 0);
            spannableString.setSpan(leadingMarginSpan, 0, spannableString.length(), SpannableString.SPAN_INCLUSIVE_INCLUSIVE);
            holder.tvTitle.setText(spannableString);
        } else {
            AnimationUtil.stopAnimation(holder.ivPlayingIconBeforeTitle);
            holder.ivPlayingIconBeforeTitle.setVisibility(View.INVISIBLE);
            holder.tvTitle.setText(track.getTrackTitle());
        }

        if (!TextUtils.isEmpty(track.getTrackIntro())) {
            holder.tvTrackIntro.setText(track.getTrackIntro());
            holder.tvTrackIntro.setVisibility(View.VISIBLE);
        } else {
            holder.tvTrackIntro.setVisibility(View.GONE);
        }
        //20为不感兴趣的大小
        int otherContentWidth = 215 + 20;
        // 专辑名称的最大宽度 = 屏幕宽度 - 其他内容的大概宽度 - 左右margin之和
        final int maxWidth = BaseUtil.getScreenWidth(mActivity) - BaseUtil.dp2px(mActivity, otherContentWidth) - 2 * mActivity.getResources().getDimensionPixelSize(R.dimen.main_find_recommend_stream_item_margin);
        holder.tvAlbumTitle.setMaxWidth(maxWidth);
        if (track.getAlbum() != null && !TextUtils.isEmpty(track.getAlbum().getAlbumTitle())) {
            holder.tvAlbumTitle.setText(track.getAlbum().getAlbumTitle());
            holder.tvAlbumTitle.setVisibility(View.VISIBLE);
        } else {
            holder.tvAlbumTitle.setVisibility(View.GONE);
        }

        if (track.getPlayCount() > 0) {
            holder.tvPlayCount.setText(StringUtil.getFriendlyNumStr(track.getPlayCount()) + "播放");
            int drawable = R.drawable.main_ic_recommend_stream_listen;
            holder.tvPlayCount.setCompoundDrawables(LocalImageUtil.getDrawable(mActivity, drawable), null, null, null);
            holder.tvPlayCount.setVisibility(View.VISIBLE);
        } else {
            holder.tvPlayCount.setVisibility(View.GONE);
        }

        holder.ivTrackCover.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean isCurrentTrackPlaying = isCurrentTrackPlaying(track.getDataId());
                if (isCurrentTrackPlaying) {
                    XmPlayerManager.getInstance(mActivity).pause();
                    dataProvider.notifyDataSetChanged();
                } else {
                    play(track, v, position);
                    dataProvider.notifyDataSetChanged();
                    notifyItemAction(track, IRecommendFeedItemActionListener.ActionType.CLICK, recommendItem, t);
                }
                statPlayBtnClick(isCurrentTrackPlaying, track, t, recommendItem, position);
            }
        });
        AutoTraceHelper.bindData(holder.ivTrackCover, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, recommendItem));
        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                track.setClicked(true);
                //没有配置或为true，均打开播放页
                if (ConfigureCenter.getInstance().getBool("toc", "soundbarclick", true)) {
                    play(track, v, position);
                    mFragment.showPlayFragment(v, PlayerManager.PLAY_TAG);
                    dataProvider.notifyDataSetChanged();
                    notifyItemAction(track, IRecommendFeedItemActionListener.ActionType.CLICK, recommendItem, t);
                } else {
                    if (isCurrentTrackPlaying(track.getDataId())) {
                        XmPlayerManager.getInstance(mActivity).pause();
                        dataProvider.notifyDataSetChanged();
                    } else {
                        play(track, v, position);
                        dataProvider.notifyDataSetChanged();
                        notifyItemAction(track, IRecommendFeedItemActionListener.ActionType.CLICK, recommendItem, t);
                    }
                }
                statItemClick(track, position, t, recommendItem);
            }
        });
        holder.ivDislike.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                handleDislike(track, v, position, recommendItem, t);
            }
        });
        AutoTraceHelper.bindData(convertView, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, recommendItem));
        AutoTraceHelper.bindData(holder.ivDislike, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, recommendItem));
        holder.ivShareToMoments.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                shareToMoment(track);
            }
        });
        AutoTraceHelper.bindData(holder.ivShareToMoments, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, recommendItem));
        holder.ivShareToWeChat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                shareToWeChat(track);
            }
        });
        AutoTraceHelper.bindData(holder.ivShareToWeChat, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, recommendItem));
    }

    private void handleDislike(final RecommendTrackItem recommendAlbumItem, View anchorView, final int position,
                               final RecommendItemNew recommendItem, final ItemModel itemModel) {
        if (recommendAlbumItem == null) {
            return;
        }
        if (!ToolUtil.isEmptyCollects(recommendAlbumItem.getDislikeReasons())) {
            showDislikeFeedbackWindow(recommendAlbumItem, anchorView, new IDataCallBack<JSONObject>() {
                @Override
                public void onSuccess(@Nullable JSONObject object) {
                    //异步操作，需要判断
                    if (mFragment == null) {
                        return;
                    }
                    if (!mFragment.canUpdateUi()) {
                        return;
                    }
                    CustomToast.showSuccessToast("将减少类似推荐");
                    if (dataProvider != null) {
                        dataProvider.removeItem(position);
                    }
                    notifyItemAction(recommendAlbumItem, IRecommendFeedItemActionListener.ActionType.UNINTERESTED, recommendItem, itemModel);
                }

                @Override
                public void onError(int code, String message) {
                    //异步操作，需要判断
                    if (mFragment == null) {
                        return;
                    }
                    if (!mFragment.canUpdateUi()) {
                        return;
                    }
                    CustomToast.showFailToast("操作失败");
                }
            });
        } else {
            if (dataProvider != null) {
                dataProvider.removeItem(position);
            }
        }
    }

    private void notifyItemAction(RecommendTrackItem track, IRecommendFeedItemActionListener.ActionType actionType
            , RecommendItemNew itemData, ItemModel itemModel) {
        if (recommendFeedItemActionListener != null && track != null) {
            recommendFeedItemActionListener.onItemAction(IRecommendFeedItemActionListener.FeedItemType.TRACK, track.getDataId()
                    , actionType, track.getCategoryId(), itemData, itemModel);
        }
    }

    private void showDislikeFeedbackWindow(final RecommendTrackItem recommendTrackItem, View anchorView, final IDataCallBack<JSONObject> dislikeCallback) {
        if (recommendTrackItem != null && recommendTrackItem.getDislikeReasons() != null) {
            Activity activity = BaseApplication.getTopActivity();
            if (activity == null) {
                return;
            }
            final DislikeFeedbackWindowNew window = new DislikeFeedbackWindowNew(activity, recommendTrackItem.getDislikeReasons());
            if (mFragment != null) {
                window.setFragment(mFragment);
            }
            window.setOnDismissListener(new PopupWindow.OnDismissListener() {
                @Override
                public void onDismiss() {
                    DislikeReason reason = window.getSelectedDislikeReason();
                    if (reason == null) {
                        return;
                    }
                    Map<String, String> params = new HashMap<>();
                    params.put(HttpParamsConstants.PARAM_TRACK_ID, String.valueOf(recommendTrackItem.getDataId()));
                    params.put(HttpParamsConstants.PARAM_LEVEL, "track");
                    if (recommendTrackItem.getAlbum() != null) {
                        params.put(HttpParamsConstants.PARAM_ALBUM_ID, String.valueOf(recommendTrackItem.getAlbum().getAlbumId()));
                    }
                    params.put(HttpParamsConstants.PARAM_SOURCE, "discoveryFeed");
                    params.put("name", reason.name);
                    params.put("value", reason.value);
                    LiteCommonRequest.dislike(params, dislikeCallback);
                }
            });
            window.customShowAtLocation(activity, anchorView);
        }
    }

    protected void statItemClick(RecommendTrackItem track, int position, ItemModel t, RecommendItemNew recommendItem) {
        UserTrackCookie.getInstance().setXmContent("flow", "homepage", "track", "");
        if (track.getRecInfo() != null) {
            UserTrackCookie.getInstance().setXmRecContent(track.getRecInfo().getRecTrack(), track.getRecInfo().getRecSrc());
        }
    }

    protected void statPlayBtnClick(boolean isCurrentTrackPlaying, RecommendTrackItem track, ItemModel t, RecommendItemNew recommendItem, int position) {
        UserTracking userTracking = new UserTracking();
        if (isCurrentTrackPlaying) {
            userTracking.setItemId("pause");
        } else {
            userTracking.setItemId("play");
            UserTrackCookie.getInstance().setXmContent("flow", "homepage", "track", "");
            if (track.getRecInfo() != null) {
                UserTrackCookie.getInstance().setXmRecContent(track.getRecInfo().getRecTrack(), track.getRecInfo().getRecSrc());
            }
        }
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_recommend_track, parent, false);
    }

    @Override
    public HolderAdapter.BaseViewHolder buildHolder(View convertView) {
        return new TrackViewHolder(convertView);
    }

    private void shareToWeChat(Track track) {
        ShareUtilsInMain.shareTrackWithoutXdcs(mActivity, track, IShareDstType.SHARE_TYPE_WX_FRIEND, ICustomShareContentType.SHARE_TYPE_TRACK);
    }

    private void shareToMoment(Track track) {
        ShareUtilsInMain.shareTrackWithoutXdcs(mActivity, track, IShareDstType.SHARE_TYPE_WX_CIRCLE, ICustomShareContentType.SHARE_TYPE_TRACK);
    }

    public void play(Track track, View view, int position) {
        if (track == null) {
            return;
        }
        if (isCurrentTrackPlaying(track.getDataId())) {
        } else if (isCurrentTrack(track.getDataId())) {
            XmPlayerManager.getInstance(mActivity).play();
        } else {
            if (dataProvider != null) {
                List<ItemModel> itemList = dataProvider.getListData();
                List<Track> trackItemList = new ArrayList<>();
                for (ItemModel item : itemList) {
                    if (item != null && item.getObject() instanceof RecommendItemNew && ((RecommendItemNew) item.getObject()).getItem() instanceof RecommendTrackItem) {
                        trackItemList.add((Track) ((RecommendItemNew) item.getObject()).getItem());
                    }
                }
                List<Track> listToPlay = trackItemList;
                if (trackItemList.size() >= 200) {  //列表长度小于200直接播放，否则要对播放列表进行处理
                    //截取包含指定track在内的199条
                    int index = trackItemList.indexOf(track);
                    if ((index - 99) < 0) {
                        listToPlay = trackItemList.subList(0, 198);
                    } else if ((index + 99) >= trackItemList.size()) {
                        listToPlay = trackItemList.subList(trackItemList.size() - 199, trackItemList.size() - 1);
                    } else {
                        listToPlay = trackItemList.subList(index - 99, index + 99);
                    }
                }
                int index = listToPlay.indexOf(track);
                PlayTools.playList(mActivity, listToPlay, index, false, view);
            }
        }
    }


    private boolean isCurrentTrack(long trackId) {
        PlayableModel curModel = XmPlayerManager.getInstance(mActivity).getCurrSound();
        if (curModel == null) {
            return false;
        }
        if (curModel.getDataId() <= 0) {
            return false;
        }
        return curModel.getDataId() == trackId;
    }

    private boolean isCurrentTrackPlaying(long trackId) {
        return isCurrentTrack(trackId) && XmPlayerManager.getInstance(mActivity).isPlaying();
    }

    private static class TrackViewHolder extends HolderAdapter.BaseViewHolder {
        View rootView;
        ImageView ivDislike;
        TextView tvTitle;
        TextView tvAlbumTitle;
        TextView tvPlayCount;
        TextView tvTrackIntro;
        LinearLayout llTrackInfo;
        LinearLayout llShare;
        ImageView ivShareToMoments;
        ImageView ivShareToWeChat;
        ImageView ivTrackCover;
        ImageView ivPlayBtn;
        ImageView ivPlayingIconBeforeTitle;

        TrackViewHolder(View convertView) {
            rootView = convertView;
            ivDislike = convertView.findViewById(R.id.main_iv_track_dislike);
            tvTitle = convertView.findViewById(R.id.main_tv_title);
            tvAlbumTitle = convertView.findViewById(R.id.main_tv_album_title);
            tvPlayCount = convertView.findViewById(R.id.main_tv_play_count);
            tvTrackIntro = convertView.findViewById(R.id.main_tv_track_intro);
            llTrackInfo = convertView.findViewById(R.id.main_ll_track_info);
            llShare = convertView.findViewById(R.id.main_ll_share);
            ivShareToMoments = convertView.findViewById(R.id.main_iv_share_to_moments);
            ivShareToWeChat = convertView.findViewById(R.id.main_iv_share_to_wechat);
            ivTrackCover = convertView.findViewById(R.id.main_iv_track_cover);
            ivPlayBtn = convertView.findViewById(R.id.main_iv_play_btn);
            ivPlayingIconBeforeTitle = convertView.findViewById(R.id.main_iv_playing_icon_before_title);
        }
    }
}
