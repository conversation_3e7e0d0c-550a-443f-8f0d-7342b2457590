package com.ximalaya.ting.lite.main.home.fragment;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.download.DownloadXmlyFullManager;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter;
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder;
import com.ximalaya.ting.lite.main.home.HomeTanghuluPresenter;
import com.ximalaya.ting.lite.main.home.viewmodel.TanghuluClickViewModel;
import com.ximalaya.ting.lite.main.model.album.RecommendDiscoveryM;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qinhuifeng on 2019-07-16
 * <p>
 * 首页糖葫芦点击全部--分类列表跳转进来
 *
 * <AUTHOR>
 */
public class HomeCalabashTypeListFragment extends BaseFragment2 implements View.OnClickListener {

    private static final String KEY_CALABASH_TYPE_LIST = "key_calabash_type_list";
    private static final int SPAN_COUNT = 4;

    private RelativeLayout mRlDownInfo;
    private TextView mTvDownloadXmfull;
    private RecyclerView mRvList;
    private List<RecommendDiscoveryM> mDataList = new ArrayList<>();
    private MultiRecyclerAdapter<RecommendDiscoveryM, SuperRecyclerHolder> mAdapter;

    private HomeTanghuluPresenter mHomeTanghuluPresenter = new HomeTanghuluPresenter(this);

    public HomeCalabashTypeListFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_LINEARLAYOUT, null);
    }

    @Override
    protected String getPageLogicName() {
        return "HomeCalabashTypeListFragment";
    }

    public static HomeCalabashTypeListFragment newInstance(ArrayList<RecommendDiscoveryM> list) {
        HomeCalabashTypeListFragment fragment = new HomeCalabashTypeListFragment();
        Bundle args = new Bundle();
        args.putParcelableArrayList(KEY_CALABASH_TYPE_LIST, list);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            List<RecommendDiscoveryM> list = arguments.getParcelableArrayList(KEY_CALABASH_TYPE_LIST);
            if (list != null) {
                mDataList.addAll(list);
            }
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("节目分类");

        mRvList = findViewById(R.id.main_rv);

        mAdapter = new MultiRecyclerAdapter<RecommendDiscoveryM, SuperRecyclerHolder>(mActivity, mDataList) {
            @Override
            public SuperRecyclerHolder createMultiViewHolder(Context mCtx, @NonNull View itemView, int viewType) {
                return SuperRecyclerHolder.createViewHolder(mCtx, itemView);
            }

            @Override
            public void onBindMultiViewHolder(SuperRecyclerHolder holder, RecommendDiscoveryM recommendDiscoveryM, int viewType, int position) {
                if (recommendDiscoveryM == null) {
                    return;
                }
                ImageView ivIcon = (ImageView) holder.getViewById(R.id.main_iv_icon);
                ImageManager.from(mContext).displayImage(ivIcon, recommendDiscoveryM.getCoverPath(), -1, R.drawable.main_icon_home_tanghulu_error_def);
                holder.setText(R.id.main_tv_title, recommendDiscoveryM.getTitle());
                holder.setOnItemClickListenner(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //正常条目被点击了
                        TanghuluClickViewModel clickViewModel = new TanghuluClickViewModel();
                        clickViewModel.recommendDiscoveryM = recommendDiscoveryM;
                        mHomeTanghuluPresenter.dealWithTanghuluClick(v, clickViewModel);
                    }
                });
            }

            @Override
            public int getMultiItemViewType(RecommendDiscoveryM model, int position) {
                return 0;
            }

            @Override
            public int getMultiItemLayoutId(int viewType) {
                return R.layout.main_item_home_calabash_type_list_rv_item;
            }
        };
        mRvList.setNestedScrollingEnabled(false);
        mRvList.setLayoutManager(new GridLayoutManager(mActivity, SPAN_COUNT));
        mRvList.setAdapter(mAdapter);
    }

    @Override
    protected void loadData() {

    }

    @Override
    public void onMyResume() {
        super.onMyResume();
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_calabash_type_list;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_tv_download_xmfull) {
            //下载喜马拉雅完整版
            dealWithDownXmlyFullClick(v);
            return;
        }
    }

    /**
     * 下载喜马拉雅完整版
     */
    private void dealWithDownXmlyFullClick(View v) {
        try {
            Uri uri = Uri.parse("market://details?id=com.ximalaya.ting.android");
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mActivity.startActivity(intent);
        } catch (Throwable e) {
            e.printStackTrace();
            CustomToast.showFailToast("应用市场打开失败");
        }
    }


    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    protected boolean isShowCoinGuide() {
        return false;
    }
}
