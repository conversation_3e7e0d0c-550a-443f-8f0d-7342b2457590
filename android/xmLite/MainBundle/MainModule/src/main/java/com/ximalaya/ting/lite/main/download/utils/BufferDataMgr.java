package com.ximalaya.ting.lite.main.download.utils;


import com.ximalaya.ting.lite.main.download.bean.BufferData;

import java.util.LinkedList;
import java.util.Queue;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   :
 */
public class BufferDataMgr {
    private static final int MAX_BYTE_ARRAY_SIZE = 50;
    private Queue<BufferData> mByteArrayGetList = null;
    private Queue<BufferData> mByteArrayRecycleList;
    private int mCurDataCount = MAX_BYTE_ARRAY_SIZE;
    private boolean mAllocateMemory = false;

    public BufferDataMgr() {
        mByteArrayGetList = new LinkedList<>();
        mByteArrayRecycleList = null;
        mByteArrayRecycleList = new LinkedList<>();
    }

    public void initBufferSize(int maxSize) {
        if (mAllocateMemory) {
            return;
        }
        for (int i = 0; i < maxSize; i++) {
            BufferData byteArray = new BufferData();
            mByteArrayGetList.offer(byteArray);
        }
        mAllocateMemory = true;
    }

    public void recycleBuffer() {
        mByteArrayGetList.clear();
        mByteArrayRecycleList.clear();
        mAllocateMemory = false;
        mCurDataCount = MAX_BYTE_ARRAY_SIZE;
    }

    public BufferData getBuffer() {
        BufferData byteArray = null;
        if (mByteArrayGetList.size() == 0 && mByteArrayRecycleList.size() != 0) {
            synchronized (mByteArrayRecycleList) {
                mByteArrayGetList.addAll(mByteArrayRecycleList);
                mByteArrayRecycleList.clear();
            }
        }

        synchronized (mByteArrayGetList) {
            byteArray = mByteArrayGetList.poll();
        }

        if (byteArray == null) {
            if (mCurDataCount >= 2 * MAX_BYTE_ARRAY_SIZE) {
                try {
                    Thread.sleep(200);
                } catch (InterruptedException e) {
                }
                return getBuffer();
            }
            byteArray = new BufferData();
            mCurDataCount++;
        }
        return byteArray;
    }

    public void recycle(BufferData buffer) {
        synchronized (mByteArrayRecycleList) {
            mByteArrayRecycleList.offer(buffer);
        }
    }
}
