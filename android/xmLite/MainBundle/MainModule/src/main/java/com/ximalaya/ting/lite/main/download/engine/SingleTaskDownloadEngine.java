package com.ximalaya.ting.lite.main.download.engine;

import android.text.TextUtils;

import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.download.bean.SingleTaskInfo;
import com.ximalaya.ting.lite.main.download.bean.SingleThreadTask;
import com.ximalaya.ting.lite.main.download.callback.CountDownCallback;
import com.ximalaya.ting.lite.main.download.callback.SingleTaskCallback;
import com.ximalaya.ting.lite.main.download.utils.DownloadConst;
import com.ximalaya.ting.lite.main.download.utils.TaskCode;
import com.ximalaya.ting.lite.main.download.utils.TaskUtil;

import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   :
 */
public class SingleTaskDownloadEngine extends BaseTaskDownloadEngine {


    private SingleTaskDownloadEngine(int threadNum) {
        super(threadNum);
    }

    public static SingleTaskDownloadEngine getInstance() {
        return Holder.sInstance;
    }

    /**
     * 内部调用方法，开始下载
     */
    void innerStart() throws Exception {
        reset();
        this.mObserver.onStartConnectServer();
        // 1. 初始化配置数据及文件
        File infoFile = new File(mSaveDir, mFileName + DownloadConst.MD_CONFIG_SUFFIX);
        File tempFile = new File(mSaveDir, mFileName + DownloadConst.MD_DATA_SUFFIX);
        if (TaskUtil.exist(infoFile)) {
            Logger.d(mTag, "读取配置");
            multiTaskInfo = TaskUtil.readMultiConfigInfo(infoFile);
            if (needDeleteFile()) {
                // 配置文件无法读取或信息错误的时候，临时文件一并删除
                infoFile.delete();
                tempFile.delete();
            }
        }
        Logger.d(mTag, "获取网络大小");

        // 配置multiTaskInfo
        long size;
        // 获取大小的过程是一个网络请求的过程
        try {
            size = fetchFileSize(mDownloadUrl);
        } catch (Exception ex) {
            if (mRequestCancel) {
                return;
            }
            mObserver.onError(TaskCode.ERROR);
            return;
        }
        if (size <= 0) {
            // 不支持断线续传
            mObserver.onError(TaskCode.ERROR_CANNOT_FETCH_DOWNLOAD_SIZE);
            return;
        }
        Logger.d(mTag, "初始化配置数据");
        multiTaskInfo = dispatchTask(size, size);
        if (TaskUtil.exist(infoFile) || TaskUtil.createFile(infoFile)) {
            TaskUtil.writeConfigInfo(infoFile, multiTaskInfo);
        } else {
            mObserver.onError(TaskCode.ERROR);
            return;
        }
        Logger.d(mTag, multiTaskInfo.toString());

        if (mRequestCancel) {
            return;
        }
        // 2.创建临时文件
        final long totalSize = multiTaskInfo.totalSize;
        final ArrayList<SingleTaskInfo> list = multiTaskInfo.list;
        if (totalSize <= 0 || ToolUtil.isEmptyCollects(list)) {
            if (TaskUtil.exist(infoFile)) {
                infoFile.delete();
            }
            mObserver.onError(TaskCode.ERROR_CREATE_TASK_FAILED);
            return;
        }
        final long doneSize = calculateDoneSize(list);
        // 如果文件不存在，创建
        if (!TaskUtil.exist(tempFile)) {
            long space = 0;
            boolean error = false;
            try {
                space = TaskUtil.getAvailableSpace(tempFile.getParent());
            } catch (Exception ex) {
                error = true;
            }
            // getAvailableSpace会出现异常，如果出现异常，默认是空间充足
            if (!error && !TaskUtil.checkAvailableSpace(totalSize, doneSize, space)) {
                mObserver.onError(TaskCode.ERROR_INSUFFICIENT_DISK_SPACE);
                return;
            }
            TaskUtil.createEmptyFile(tempFile, 0);
        }

        // 3. 创建单个下载任务
        mStatistics.init(doneSize, totalSize);
        CountDownCallback callback = new CountDownCallback();
        SingleTaskInfo info = list.get(0);
        SingleThreadTask task = new SingleThreadTask(info, mDownloadUrl, memoryBufferQueue, mTagName, memoryBufferMgr,
                mDownloadConnectionType, mConfig, mPreConnection);
        task.setCallback(callback);
        Logger.d(mTag, MessageFormat.format("共{0}，已完成{1}", totalSize, doneSize));
        // 线程倒数
        mCountDown = new CountDownLatch(1);
        callback.setCountDownLatch(mCountDown);
        // 开启计算统计数据
        mStatistics.start();

        // 4. 创建读数据到文件的线程
        mWriteTask = new FileWriteTask(tempFile.getAbsolutePath(), memoryBufferQueue, multiTaskInfo, infoFile, memoryBufferMgr);
        mWriteTask.setCallback(new SingleTaskCallback(mObserver, mStatistics));
        sThreadFactory.newThread(mWriteTask).start();

        // 5. 开启读数据线程
        mDefaultExecutor.add(task);
        try {
            Logger.d(mTag, "wait..");
            mCountDown.await();
            Logger.d(mTag, "continue..");
        } catch (InterruptedException e) {
            Logger.e(mTag, "Read net stream countDown await throws Exception: " + e.toString());
        }
        if (mRequestCancel) {
            return;
        }

        // 6. 写完所有内存数据
        mCountDown = new CountDownLatch(1);
        mWriteTask.flush(mCountDown);
        // 等待内存中的数据写完
        try {
            mCountDown.await();
        } catch (InterruptedException e) {
            Logger.e(mTag, "writeTask flush countDown await throws Exception: " + e.toString());
        }

        boolean error = false;
        boolean needDeleteInfoFile = false;

        // 有任务下载失败，说明需要删除临时文件，重新设置配置文件
        if (task.isError()) {
            error = true;
            needDeleteInfoFile = task.getErrorCode() == TaskCode.ERROR_TRANSPORT_BREAK;
        }

        Logger.d(mTag, MessageFormat.format("任务结束，共{0}，已完成{1}", mStatistics.total, mStatistics.done));

        if (error && needDeleteInfoFile) {
            infoFile.delete();
            tempFile.delete();
        }
        // 7. 任务结束处理
        if (!mRequestCancel && !error) {
            infoFile.delete();
            long done = calculateDoneSize(list);
            if (totalSize != done) {
                Logger.e(mTag, MessageFormat.format("网络读写错误：{0}\n文件大小:{1}\n下载后文件大小：{2}", mDownloadUrl, totalSize, done));
                mObserver.onError(TaskCode.ERROR_TRANSPORT_BREAK);
                return;
            }

            // 8. 重命名为指定后缀的文件
            File doneFile = new File(mSaveDir, mFileName);
            tempFile.renameTo(doneFile);
            Logger.d(mTag, "通知成功");
            mObserver.onSuccess();
        } else {
            if (mRetryCount-- > 0) {
                innerStart();
            } else {
                mObserver.onError(TaskCode.ERROR_TRANSPORT_BREAK);
            }
        }
    }

    private long fetchFileSize(String url) throws IOException {
        long size = -1;
        mPreConnection = getDownloadConnection(url).execute();
        int responseCode = mPreConnection.getResponseCode();
        String lenStr = "";
        if (responseCode == HttpURLConnection.HTTP_PARTIAL) {
            lenStr = mPreConnection.getResponseHeaderField("Content-Range");
            if (!TextUtils.isEmpty(lenStr)) {
                size = Long.valueOf(lenStr.substring(lenStr.lastIndexOf("/") + 1));
            }
        } else {
            lenStr = mPreConnection.getResponseHeaderField("Content-Length");
            if (TextUtils.isEmpty(lenStr)) {
                lenStr = mPreConnection.getResponseHeaderField2("Content-Length");
            }
            if (!TextUtils.isEmpty(lenStr)) {
                size = Long.parseLong(lenStr);
            }
        }
        return size;
    }

    /**
     * 保证multiTaskInfo正确可读且已下载任务长度不大于总任务长度
     */
    private boolean needDeleteFile() {
        if (multiTaskInfo == null) {
            return true;
        }
        boolean result = true;
        ArrayList<SingleTaskInfo> list = multiTaskInfo.list;
        if (!ToolUtil.isEmptyCollects(list) && list.size() == 1) {
            SingleTaskInfo info = list.get(0);
            result = info.haveDoneSize >= (info.endPos - info.beginPos);
        }
        if (result) {
            multiTaskInfo = null;
        }
        return result;
    }

    private static class Holder {
        static SingleTaskDownloadEngine sInstance = new SingleTaskDownloadEngine(1);
    }

}
