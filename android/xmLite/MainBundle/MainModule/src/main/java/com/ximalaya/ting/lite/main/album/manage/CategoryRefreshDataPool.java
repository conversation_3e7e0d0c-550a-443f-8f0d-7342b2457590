package com.ximalaya.ting.lite.main.album.manage;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.host.manager.customize.CustomizeManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.user.InterestCardModel;
import com.ximalaya.ting.android.host.util.RequestParamsUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.lite.main.model.album.RecommendRefreshModel;
import com.ximalaya.ting.lite.main.request.HttpParamsConstantsInMain;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> on 2018/3/16.
 */

public class CategoryRefreshDataPool {
    @NonNull
    private Context mContext;

    @NonNull
    private MainAlbumMList mAlbumMList;

    private boolean mIsLoading = false;
    private int mCurrentLoopCount = 1;

    //当前循环的页码,猜你喜欢使用
    private int mCurrentPage = 1;

    //猜你喜欢首次创建
    private boolean mIsFirstGuessYouLikeRefresh = true;

    public CategoryRefreshDataPool(@NonNull Context context, @NonNull MainAlbumMList albumMList) {
        mContext = context;
        mAlbumMList = albumMList;
    }

    public void refreshGuessYouLikeLiteNew(int from, @NonNull final IDataCallBack<List<AlbumM>> callBack) {
        if (mIsLoading) {
            callBack.onError(0, "");
            return;
        }
        if (mAlbumMList == null) {
            return;
        }
        if (mAlbumMList.getLoopCount() == 0 || mAlbumMList.getList() == null || mAlbumMList.getList().size() == 0) {
            mIsLoading = false;
            callBack.onSuccess(mAlbumMList.getList());
            return;
        }
        mIsLoading = true;
        //超过循环次数，重置为第一页
        if (mCurrentPage > mAlbumMList.getLoopCount()) {
            mCurrentPage = 1;
            //第一页清除之前的数据，展示当前的数据
            mExcludedAlbumIds.clear();
        }
        //首次创建的时候需要过滤当前展示的item
        if (mCurrentPage == 1 && mIsFirstGuessYouLikeRefresh) {
            mIsFirstGuessYouLikeRefresh = false;
            for (AlbumM albumM : mAlbumMList.getList()) {
                if (albumM != null) {
                    mExcludedAlbumIds.add(albumM.getId());
                }
            }
        }
        int itemCount = mAlbumMList.getList().size();
        Map<String, String> p = new HashMap<>();
        p.put(HttpParamsConstantsInMain.PARAM_USE_RECOMMEND_MODEL, "false");
        String excludedAlbumIds = getRefreshExcludedAlbumIds();
        p.put("excludedAlbumIds", excludedAlbumIds);
        p.put("pageId", mCurrentPage + "");
        p.put("pageSize", String.valueOf(itemCount));
        p.put("moduleId", mAlbumMList.getModuleId() + "");
        p.put("channelId", mAlbumMList.getChannelId() + "");
        if (from == BundleValueConstantsInMain.FROM_VIP_PAGE) {
            p.put("vipPage", "1");
        }
        int categoryId = mAlbumMList.getCategoryId();
        //如果是首页没有categoryId，传入-1
        //兼容老的逻辑，默认值的时候必须传-1，兼容老的接口
        if (mAlbumMList.getCategoryId() == 0) {
            categoryId = -1;
        }
        p.put("categoryId", categoryId + "");
        p = RequestParamsUtil.addVipShowParam(p);
        //本地如果记录了兴趣卡片
        InterestCardModel interestCardModel = CustomizeManager.getInstance().getInterestCardModel();
        if (interestCardModel != null) {
            p.put("ageRange", interestCardModel.ageRange);
            p.put("gender", interestCardModel.gender + "");
        }
        LiteCommonRequest.getGuessYouLikeLite(p, new IDataCallBack<RecommendRefreshModel<AlbumM>>() {
            @Override
            public void onSuccess(@Nullable RecommendRefreshModel<AlbumM> object) {
                if (object == null || object.getList() == null) {
                    callBack.onError(0, "");
                } else {
                    for (AlbumM albumM : object.getList()) {
                        if (albumM != null) {
                            mExcludedAlbumIds.add(albumM.getId());
                        }
                    }
                    callBack.onSuccess(object.getList());
                    mCurrentPage++;
                }
                mIsLoading = false;
            }

            @Override
            public void onError(int code, String message) {
                callBack.onError(code, message);
                mIsLoading = false;
            }
        });
    }

    public void refresh(@NonNull final IDataCallBack<List<AlbumM>> callBack) {
        if (mIsLoading) {
            callBack.onError(0, "");
            return;
        }
        if (mAlbumMList == null || mAlbumMList.getList() == null) {
            return;
        }
        mIsLoading = true;
        if (mCurrentLoopCount == 0) {
            callBack.onSuccess(mAlbumMList.getList());
            mExcludedAlbumIds.clear();
            mCurrentLoopCount = ++mCurrentLoopCount % mAlbumMList.getLoopCount();
            mIsLoading = false;
            return;
        }
        //根据mCurrentLoopCount == 0的逻辑，此处需要增加排除mAlbumMList.getList()专辑
        if (mCurrentLoopCount == 1) {
            for (AlbumM albumM : mAlbumMList.getList()) {
                if (albumM != null) {
                    mExcludedAlbumIds.add(albumM.getId());
                }
            }
        }
        int itemCount = mAlbumMList.getList().size();
        String excludedAlbumIds = getRefreshExcludedAlbumIds();
        //热词
        Map<String, String> p = new HashMap<>();
        p.put(HttpParamsConstantsInMain.PARAM_USE_RECOMMEND_MODEL, "false");
        p.put("categoryId", mAlbumMList.getCategoryId() + "");
        p.put("keywordId", mAlbumMList.getKeywordId() + "");
        p.put("pageId", 1 + "");
        p.put("pageSize", String.valueOf(itemCount));
        p.put("excludedAlbumIds", excludedAlbumIds);
        p.put("excludedOffset", String.valueOf(mCurrentLoopCount * itemCount));
        p.put("moduleType", String.valueOf(mAlbumMList.getModuleType()));
        p.put("personalRecSubType", mAlbumMList.getPersonalRecSubType());
        p = RequestParamsUtil.addVipShowParam(p);
        LiteCommonRequest.getDiscoveryKeywordRefresh(p, new IDataCallBack<RecommendRefreshModel<AlbumM>>() {
            @Override
            public void onSuccess(@Nullable RecommendRefreshModel<AlbumM> object) {
                if (object == null || object.getList() == null) {
                    callBack.onError(0, "");
                } else {
                    for (AlbumM albumM : object.getList()) {
                        if (albumM != null) {
                            mExcludedAlbumIds.add(albumM.getId());
                        }
                    }
                    callBack.onSuccess(object.getList());
                    mCurrentLoopCount = ++mCurrentLoopCount % mAlbumMList.getLoopCount();
                }
                mIsLoading = false;
            }

            @Override
            public void onError(int code, String message) {
                callBack.onError(code, message);
                mIsLoading = false;
            }
        });
    }

    //换一换排重
    private Set<Long> mExcludedAlbumIds = new HashSet<>();  //排重的专辑id

    private String getRefreshExcludedAlbumIds() {
        StringBuilder sb = new StringBuilder();
        for (long i : mExcludedAlbumIds) {
            sb.append(i).append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1); //去除最后一个 ","
        }
        return sb.toString();
    }
}
