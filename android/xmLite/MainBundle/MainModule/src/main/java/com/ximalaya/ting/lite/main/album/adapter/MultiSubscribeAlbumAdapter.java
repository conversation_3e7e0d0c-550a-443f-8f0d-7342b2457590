package com.ximalaya.ting.lite.main.album.adapter;

import android.content.Context;
import android.graphics.Color;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.album.fragment.AlbumRecListFragment;

import java.util.List;


/**
 * Created by <PERSON> on 2017/7/6.
 *
 * <AUTHOR>
 */

public class MultiSubscribeAlbumAdapter extends BaseAlbumAdapter {

    private int color999;

    private boolean showItemDivider = true;

    private int mFrom = 0;

    //可选择参数，不是所有来源都会有
    long mCurrentAlbumAnchorUid = 0;

    public MultiSubscribeAlbumAdapter(Context context, List<Album> listData, int from) {
        super(context, listData);
        color999 = Color.parseColor("#999999");
        mFrom = from;
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_album_woting;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    protected void hideAllViews(BaseAlbumAdapter.ViewHolder viewHolder) {
        super.hideAllViews(viewHolder);
        ((ViewHolder) viewHolder).offSale.setVisibility(View.INVISIBLE);
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, Album o, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        hideAllViews(viewHolder);

        if (!(o instanceof AlbumM)) {
            return;
        }
        AlbumM albumM = (AlbumM) o;

        // 相似推荐页-专辑item  控件曝光
        new XMTraceApi.Trace()
                .setMetaId(45530)
                .setServiceId("slipPage")
                .put("albumId", String.valueOf(albumM.getId()))
                .put("currPage", "SimilarlyRecommended")
                .put("exploreType", "SimilarlyRecommended")
                .createTrace();

        ImageManager.from(context).displayImage(viewHolder.cover, o.getValidCover(), R.drawable.host_default_album_145);
        CharSequence mainTitle = getRichTitle(o);
        viewHolder.title.setText(!TextUtils.isEmpty(mainTitle) ? mainTitle : o.getAlbumTitle());

        //是否展示下架标签,服务端规定 2 表示下架
        viewHolder.offSale.setVisibility(albumM.getStatus() == 2 ? View.VISIBLE : View.GONE);

        //设置副标题
        String subTitle = albumM.getIntro();
        if (!TextUtils.isEmpty(subTitle)) {
            viewHolder.subtitle.setText(subTitle);
            viewHolder.subtitle.setVisibility(View.VISIBLE);
        } else {
            viewHolder.subtitle.setVisibility(View.GONE);
        }

        int drawable = R.drawable.main_ic_common_play_count;
        addAlbumInfo(context, viewHolder.layoutAlbumInfo, drawable, StringUtil.getFriendlyNumStr(albumM.getPlayCount()), color999, false, false);
        addAlbumInfo(context, viewHolder.layoutAlbumInfo, R.drawable.main_ic_common_track_count, StringUtil.getFriendlyNumStr(albumM.getIncludeTrackCount()) + " 集", color999);
        boolean notEndItem = (getCount() != 0 && (position != getCount() - 1));
        viewHolder.border.setVisibility((showItemDivider && notEndItem) ? View.VISIBLE : View.INVISIBLE);

        if (AlbumTagUtil.getAlbumCoverTag(albumM) != -1) {
            viewHolder.ivTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable(albumM, context, AlbumTagUtil.ZOOM_IN_RATIO_78_percent));
            viewHolder.ivTag.setVisibility(View.VISIBLE);
        } else {
            viewHolder.ivTag.setVisibility(View.INVISIBLE);
        }

        //处理相似专辑,只有播放页来源才进行展示
        if (mFrom == AlbumRecListFragment.FROM_PLAY_PAGE_RECOMMEND && isAlbumAnchorSame(albumM)) {
            viewHolder.tvTagSameAnchor.setVisibility(View.VISIBLE);
        } else {
            viewHolder.tvTagSameAnchor.setVisibility(View.GONE);
        }
    }


    private boolean isAlbumAnchorSame(AlbumM albumM) {
        if (albumM == null) {
            return false;
        }
        return mCurrentAlbumAnchorUid > 0 && albumM.getUid() == mCurrentAlbumAnchorUid;
    }

    @Override
    public void onClick(View view, Album album, final int position, BaseViewHolder holder) {

    }

    private Spanned getRichTitle(Album data) {
        if (!(data instanceof AlbumM))
            return null;
        AlbumM album = (AlbumM) data;
        StringBuffer result = new StringBuffer();
        boolean isCompleteTag = album.getSerialState() == 2
                || album.isCompleted()
                || (album.getAttentionModel() != null
                && album.getAttentionModel().getSerialState() == 2);
        if (isCompleteTag) {
            // 完本标签
            result.append("<img src=\"" + R.drawable.main_tag_complete_top_new_xiangsi + "\">  ");
        }
        if (TextUtils.isEmpty(result))
            return null;
        result.append(album.getAlbumTitle());
        return Html.fromHtml(result.toString(), ToolUtil.getImageGetter(context), null);
    }


    public void setCurrentAlbumAnchorUid(long mCurrentAlbumAnchorUid) {
        this.mCurrentAlbumAnchorUid = mCurrentAlbumAnchorUid;
    }

    /**
     * 根据不同的页面返回所需要的副标题
     */
    private String getSubTitle(Album album, int pageType) {
        String subTitle = "";
        if (album instanceof AlbumM) {
            AlbumM albumM = (AlbumM) album;
            if (!TextUtils.isEmpty(subTitle)) {
                subTitle = albumM.getSubTitle();
            }
        }
        return subTitle;
    }

    public static class ViewHolder extends BaseAlbumAdapter.ViewHolder {
        private ImageView offSale; //下架icon
        private ImageView ivTag;
        private TextView tvTagSameAnchor;

        public ViewHolder(View convertView) {
            super(convertView);
            cover = (ImageView) convertView.findViewById(R.id.main_iv_album_cover);
            border = convertView.findViewById(R.id.main_album_border);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
            title = (TextView) convertView.findViewById(R.id.main_tv_album_title);
            subtitle = (TextView) convertView.findViewById(R.id.main_tv_album_subtitle);
            layoutAlbumInfo = (LinearLayout) convertView.findViewById(R.id.main_layout_album_info);
            offSale = (ImageView) convertView.findViewById(R.id.main_iv_off_sale);
            tvTagSameAnchor = convertView.findViewById(R.id.main_tag_same_anchor);
        }
    }
}
