package com.ximalaya.ting.lite.main.earn.dialog;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.LinearInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.qq.e.ads.nativ.widget.NativeAdContainer;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.adsdk.platform.common.modelproxy.AbstractThirdAd;
import com.ximalaya.ting.android.host.adsdk.platform.gdt.view.GdtMediaViewContainer;
import com.ximalaya.ting.android.host.adsdk.provider.CommonNativeDaTuAdProvider;
import com.ximalaya.ting.android.host.adsdk.provider.callback.OnCommonDaTuAdBindCallBack;
import com.ximalaya.ting.android.host.adsdk.provider.viewmodel.SimpleDaTuViewModel;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.listenertask.ListenEarnCoinDialogManager;
import com.ximalaya.ting.android.host.listenertask.callback.JssdkFuliRewardCallback;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.earn.FuliCoinBallConfigCenterManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.ad.AdWrapper;
import com.ximalaya.ting.android.host.model.earn.FuliBallDialogDataModel;
import com.ximalaya.ting.android.host.model.earn.FuliBallType;
import com.ximalaya.ting.android.host.model.exchange.ExchangeReceiveModel;
import com.ximalaya.ting.android.host.util.CountDownTimerFix;
import com.ximalaya.ting.android.host.util.constant.AdConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.earn.FullCoinBallDialogManager;
import com.ximalaya.ting.lite.main.earn.InspireAdRewardCoinManager;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 已登录领取金币的弹框
 *
 * <AUTHOR>
 */
public class FuliCoinBallDialogFragment extends BaseDialogFragment implements View.OnClickListener {

    private static final String ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL = "listen_earn_dialog_data_model";
    private static String TAG = "inspireAd";
    private boolean mMaskIsShow = false; //解决fragment重复添加crash问题

    private ImageView ivTopBgLight;
    //金光旋转的动画
    private ObjectAnimator ivTopBgLightAnimator;

    private TextView mTvCoinNumber;
    private TextView mTvEarnListenTime;
    //加倍
    private ViewGroup mLayoutEarnMore, mMyBalanceExchangeLayout;
    //倒计时
    private TextView mCloseCountdownTime;
    private View mViewClose;
    private TextView mTvMyBalanceExchange;
    private CountDownTimerFix mTimer;

    //自动关闭的倒计时
    private CountDownTimerFix mAutoCloseTimer;


    private ViewGroup mAdLayoutContent, mInspireAdContainer;
    private TextView mAdTvTitle, mInspireAdTvTitle, mInspireAdTvSubtitle, mInspireAdBtn;
    private ImageView mAdIvImage;
    private CardView mAdLayoutCard;

    //穿山甲视频广告容器
    private CardView mCsjAdVideoLayout;
    //广点通视频广告布局
    private GdtMediaViewContainer mGdtAdVideoLayout;
    //广点通广告必须设置使用的布局
    private NativeAdContainer mNativeAdContainer;

    //广告包装类
    private AdWrapper mAdWrapper = null;

    //奖励数据载体
    private FuliBallDialogDataModel mDataModel;

    //呼吸动效，服务端配置是4，使用呼吸动效
    private AnimatorSet mBreatheAnimatorSet = null;

    private JssdkFuliRewardCallback mJssdkFuliRewardCallback;

    private ImageView mAdTag, mInspireAdTag, mInspireAdIcon, mCoinIconIv;

    private CommonNativeDaTuAdProvider mDatuAdProvider;

    //Vip免广告
    private LinearLayout mLlVipRemoveAd;
    private boolean isJumping; // 点击后发生跳转
    private boolean canGetReward;
    private CountDownTimerFix jumpTimer; // 跳转计时器


    public static Bundle newArgument(FuliBallDialogDataModel dialogDataModel) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL, dialogDataModel);
        return bundle;
    }

    @SuppressLint("DefaultLocale")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        mDatuAdProvider = new CommonNativeDaTuAdProvider(getActivity());

        Bundle arguments = getArguments();
        if (arguments != null) {
            mDataModel = arguments.getParcelable(ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL);
        }
        if (mDataModel == null) {
            mDataModel = new FuliBallDialogDataModel(FuliBallType.BALL_TYPE_DEF, FuliBallType.AWARD_TYPE_GET);
        }
        View inflate = inflater.inflate(R.layout.main_fra_dialog_fuli_coin_ball, container, false);

        ivTopBgLight = inflate.findViewById(R.id.main_iv_top_bg_light);

        mViewClose = inflate.findViewById(R.id.main_iv_close);


        mLlVipRemoveAd = inflate.findViewById(R.id.main_ll_vip_remove_ad);
        mCoinIconIv = inflate.findViewById(R.id.main_iv_coin_icon);

        mTvCoinNumber = inflate.findViewById(R.id.main_tv_coin_number);
        mCloseCountdownTime = inflate.findViewById(R.id.main_tv_close_countdown_time);
        mTvEarnListenTime = inflate.findViewById(R.id.main_tv_earn_listen_time);
        mLayoutEarnMore = inflate.findViewById(R.id.main_layout_coin_earn_more);
        mTvMyBalanceExchange = inflate.findViewById(R.id.main_tv_my_balance_exchange);
        mMyBalanceExchangeLayout = inflate.findViewById(R.id.main_ll_my_coin_balance);

        mAdLayoutContent = inflate.findViewById(R.id.main_ad_listen_earn_dialog_bottom_content);
        mAdTvTitle = inflate.findViewById(R.id.main_ad_title);
        mAdIvImage = inflate.findViewById(R.id.main_ad_image);
        mCsjAdVideoLayout = inflate.findViewById(R.id.main_ad_video_layout);
        mGdtAdVideoLayout = inflate.findViewById(R.id.main_ad_gdt_video_layout);
        mAdLayoutCard = inflate.findViewById(R.id.main_layout_ad_card);
        mNativeAdContainer = inflate.findViewById(R.id.main_ad_native_container);

        mInspireAdContainer = inflate.findViewById(R.id.main_ad_inspire_content);
        mInspireAdTvTitle = inflate.findViewById(R.id.main_inspire_ad_title);
        mInspireAdTvSubtitle = inflate.findViewById(R.id.main_inspire_ad_sub_title);
        mInspireAdTag = inflate.findViewById(R.id.main_inspire_ad_tag);
        mInspireAdIcon = inflate.findViewById(R.id.main_inspire_ad_image);
        mInspireAdBtn = inflate.findViewById(R.id.main_inspire_ad_btn);

        mAdTag = inflate.findViewById(R.id.main_iv_ad_tag);

        mViewClose.setVisibility(View.GONE);
        mCloseCountdownTime.setVisibility(View.VISIBLE);
        mViewClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                FullCoinBallDialogManager.trackClickClose(FullCoinBallDialogManager.V1, mDataModel, getTrackAdId());
                dismissAllowingStateLoss();
            }
        });
        AutoTraceHelper.bindData(mViewClose, AutoTraceHelper.MODULE_DEFAULT, "");

        //优先使用服务返回的标题
        if (!TextUtils.isEmpty(mDataModel.awardDesc)) {
            mTvCoinNumber.setText(mDataModel.awardDesc);
        } else {
            String amountInfo =
                    (mDataModel.awardType == FuliBallType.AWARD_TYPE_DOUBLE ? "恭喜再获得" : "恭喜获得")
                            + mDataModel.amount + "金币";
            mTvCoinNumber.setText(amountInfo);
        }

        String totalCoinBalance = mDataModel.myCoinBalance + "";

        if (!TextUtils.isEmpty(totalCoinBalance)) {
            String balanceTransform = StringUtil.balanceTransform(totalCoinBalance);
            BigDecimal decimal = new BigDecimal(totalCoinBalance);
            BigDecimal divide = decimal.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
            String myCoinExchangeInfo = balanceTransform + " ≈ " + divide.toString() + "元";

            SpannableString spannableExchangeInfo = new SpannableString(myCoinExchangeInfo);
            spannableExchangeInfo.setSpan(new ForegroundColorSpan(Color.parseColor("#333333")), 0, balanceTransform.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            mTvMyBalanceExchange.setText(spannableExchangeInfo);
        }

        if (mDataModel.awardType == FuliBallType.AWARD_TYPE_DOUBLE) {
            //处理翻倍奖励，不同的ui
        } else {
            //奖励类型：领取金币弹框，只有领取金币的时候可以进行翻倍等操作
            //处理不同类型的气泡对应的ui
            switch (mDataModel.ballType) {
                case FuliBallType.BALL_TYPE_LISTEN:
                    //收听时长气泡，展示兑换时间，不能进行翻倍
                    mTvEarnListenTime.setVisibility(View.VISIBLE);
                    mTvEarnListenTime.setText(mDataModel.awardSubTile);
                    break;
                case FuliBallType.BALL_TYPE_LUCK:
                case FuliBallType.BALL_TYPE_HOME_VIDEO:
                    //只有幸运气泡和首页视频激励，可以进行翻倍
                    mLayoutEarnMore.setVisibility(View.VISIBLE);
                    mLayoutEarnMore.setOnClickListener(this);
                    AutoTraceHelper.bindData(mLayoutEarnMore, AutoTraceHelper.MODULE_DEFAULT, "");
                    break;
                case FuliBallType.BALL_TYPE_SUPPER_COMMON:
                case FuliBallType.BALL_TYPE_LISTEN_V2_NORMAL:
                    //在此处增加可以翻倍的活动来源，需要翻倍的增加新的case:
                    if (!TextUtils.isEmpty(mDataModel.awardReceiveId)) {
                        mLayoutEarnMore.setVisibility(View.VISIBLE);
                        mLayoutEarnMore.setOnClickListener(this);
                        AutoTraceHelper.bindData(mLayoutEarnMore, AutoTraceHelper.MODULE_DEFAULT, "");
                    }
                    break;
                default:
                    //防止活动混乱，尽量不要在default中进行统一处理
                    break;
            }
        }

        //Vip用户隐藏广告布局
        if (UserInfoMannage.isVipUser()) {
            mLlVipRemoveAd.setVisibility(View.VISIBLE);
            mCloseCountdownTime.setVisibility(View.GONE);
            mAdLayoutContent.setVisibility(View.GONE);
            mInspireAdContainer.setVisibility(View.GONE);
            mViewClose.setVisibility(View.VISIBLE);
        } else {
            //有广告数据，展示广告布局
            if (mAdWrapper != null && mAdWrapper.hasThirdAd()) {
                if (AdManager.isInspireAd(mAdWrapper.getThirdAd())){
                    mInspireAdContainer.setVisibility(View.VISIBLE);
                    if (mAdWrapper.getThirdAd().getAdvertis() != null) {
                        String btnTip = String.format("点击浏览%d秒，再得%d金币",
                                mAdWrapper.getThirdAd().getAdvertis().getTipStayTime(),
                                mAdWrapper.getThirdAd().getAdvertis().getLiteInspireValueCount());
                        if (fromFreeListen()){
                            btnTip = String.format("点击浏览%d秒，再得%d分钟",
                                mAdWrapper.getThirdAd().getAdvertis().getTipStayTime(), mDataModel.awardTime);
                        }
                        mInspireAdBtn.setText(btnTip);
                    }
                } else {
                    mAdLayoutContent.setVisibility(View.VISIBLE);
                }
                if (fromFreeListen()) {
                    mCoinIconIv.setVisibility(View.GONE);
                    ivTopBgLight.setVisibility(View.GONE);
                    mMyBalanceExchangeLayout.setVisibility(View.GONE);
                }
            } else {
                mAdLayoutContent.setVisibility(View.GONE);
                mInspireAdContainer.setVisibility(View.GONE);
            }

            boolean isShowAdSuccess = updateAdUi();
            if (isShowAdSuccess) {
                //有广告展示的时候才启动倒计时
                //配置中心获取的是秒
                int countTime = ConfigureCenter.getInstance().getInt("ximalaya_lite_ad", "UniversalPopupShutdownTime", 3);
                //小于等于0，关闭按钮直接展示，无倒计时
                if (countTime <= 0) {
                    mCloseCountdownTime.setVisibility(View.GONE);
                    mViewClose.setVisibility(View.VISIBLE);
                    return inflate;
                }
                countTime *= 1000;
                //加一个保护措施，如果大于等于10秒了，重置为4秒，避免配置中心配置错误
                if (countTime >= 10000) {
                    countTime = 3000;
                }
                startTimer(countTime);
            } else {
                mCloseCountdownTime.setVisibility(View.GONE);
                mViewClose.setVisibility(View.VISIBLE);
            }
            FullCoinBallDialogManager.trackShow(FullCoinBallDialogManager.V1, mDataModel, getTrackAdId());
        }
        return inflate;
    }

    private boolean fromFreeListen(){
        if (mDataModel == null) return false;
        return FuliBallType.SOURCE_NAME_FREE_LISTEN_PAGE.equals(mDataModel.sourceName) ||
                FuliBallType.SOURCE_NAME_FREE_LISTEN_DIALOG.equals(mDataModel.sourceName);
    }

    /**
     * 获取广告id
     * <p>
     * 获取dspPositionId
     */
    private String getTrackAdId() {
        //有广告展示，上报穿山甲广告id
        if (mDataModel == null) {
            return "";
        }
        if (mAdWrapper == null) {
            return "";
        }
        AbstractThirdAd thirdAd = mAdWrapper.getThirdAd();
        if (thirdAd == null) {
            return "";
        }
        //本地广告，走本地的逻辑
        if (thirdAd.isLocalAdNoAdx()) {
            return mDataModel.adCSJCode;
        }
        //adx广告，走adx逻辑
        return thirdAd.getDspPositionId();
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        //去掉标题
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialogInterface, int keyCode, KeyEvent keyEvent) {
                //运行关闭了，不再拦截返回按键
                if (mViewClose != null && mViewClose.getVisibility() == View.VISIBLE) {
                    return false;
                }
                //拦截返回按键，禁止返回关闭弹框
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    return true;
                }
                return false;
            }
        });
        Window window = dialog.getWindow();
        if (window != null) {
            //dialog背景设置透明，解决shape不生效问题
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setPadding(0, 0, 0, 0); //消除边距
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;   //设置宽度充满屏幕
            lp.height = WindowManager.LayoutParams.MATCH_PARENT;
            window.setAttributes(lp);
        }
        return dialog;
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mMaskIsShow = false;
        stopTopLightAnim();
    }

    @Override
    public void onResume() {
        super.onResume();
        FuliLogger.log("领金币弹框===onResume===");

        if (mBreatheAnimatorSet != null && mAdLayoutContent.getVisibility() == View.VISIBLE) {
            //statBreatheAnimator();
            FuliLogger.log("呼吸动画==onResume");
        }
        mDatuAdProvider.onMyResume();
        handleInspireAdResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        //停止呼吸动效
        stopBreatheAnimator();
    }

    /**
     * 刷新广告ui
     */
    private boolean updateAdUi() {
        if (mAdWrapper == null) {
            mAdLayoutContent.setVisibility(View.GONE);
            mInspireAdContainer.setVisibility(View.GONE);
            return false;
        }
        AbstractThirdAd thirdAd = mAdWrapper.getThirdAd();
        if (thirdAd == null || thirdAd.getAdData() == null) {
            mAdLayoutContent.setVisibility(View.GONE);
            mInspireAdContainer.setVisibility(View.GONE);
            return false;
        }

        if (AdManager.isInspireAd(thirdAd)){
            return updateInspireAdUI(thirdAd);
        }

        mAdLayoutContent.setOnClickListener(null);

        //处理自渲染通用操作
        //图片的宽度
        int imageWith = BaseUtil.dp2px(getActivity(), 320);
        //设置点击的view
        List<View> clickList = new ArrayList<>();
        clickList.add(mAdLayoutContent);
        //创建广告绑定相关View
        SimpleDaTuViewModel simpleDaTuViewModel = new SimpleDaTuViewModel(imageWith, clickList, mAdIvImage);
        //设置通用标题
        simpleDaTuViewModel.descView = mAdTvTitle;
        //设置穿山甲,百度等广告根布局
        simpleDaTuViewModel.adContentRootView = mAdLayoutContent;
        simpleDaTuViewModel.adTagView = mAdTag;
        simpleDaTuViewModel.csjVideoLayout = mCsjAdVideoLayout;

        //设置广点通包装布局
        simpleDaTuViewModel.gdtNativeAdContainer = mNativeAdContainer;
        simpleDaTuViewModel.gdtVideoContainer = mGdtAdVideoLayout;

        //设置广点通tag位置
        FrameLayout.LayoutParams gdtTagParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        gdtTagParams.gravity = Gravity.RIGHT | Gravity.BOTTOM;
        //title布局高度是24+title布局的topMargin 12 + 再加6
        gdtTagParams.bottomMargin = BaseUtil.dp2px(getActivity(), 42);
        int screenWidth = BaseUtil.getScreenWidth(getActivity());
        //gdtTagParams.rightMargin = (screenWidth - BaseUtil.dp2px(getActivity(), 320)) / 2 + BaseUtil.dp2px(getActivity(), 6);
        gdtTagParams.rightMargin = (BaseUtil.dp2px(getActivity(), 320) - BaseUtil.dp2px(getActivity(), 288)) / 2 + BaseUtil.dp2px(getActivity(), 6);
        simpleDaTuViewModel.gtdAdTagLayoutParams = gdtTagParams;

        //绑定广告相关事件，埋点等
        boolean isBindSuccess = mDatuAdProvider.bindViewDatas(thirdAd, simpleDaTuViewModel, thirdAd.getPositionName());
        //绑定失败
        if (!isBindSuccess) {
            mAdLayoutContent.setVisibility(View.GONE);
            return false;
        }

        //绑定成功

        //展示广告界面
        mAdLayoutContent.setVisibility(View.VISIBLE);

        boolean isCsjVideoAd = mCsjAdVideoLayout.getVisibility() == View.VISIBLE || mGdtAdVideoLayout.getVisibility() == View.VISIBLE;
        //加载lottie动画，走配置中心
        String lottieAssetName = FuliCoinBallConfigCenterManager.getLottieAssetName(isCsjVideoAd);
        if (!TextUtils.isEmpty(lottieAssetName)) {
            //这种情况下没有lottie动画了
        } else {
            //不是要lottie动画，代码实现呼吸效果
            //启动呼吸动画
            //statBreatheAnimator();
        }
        return true;
    }

    private boolean updateInspireAdUI(AbstractThirdAd thirdAd) {
        if (thirdAd == null){
            return false;
        }

        mInspireAdContainer.setOnClickListener(null);

        //处理自渲染通用操作
        //图片的宽度
        int imageWith = BaseUtil.dp2px(getActivity(), 320);
        //设置点击的view
        List<View> clickList = new ArrayList<>();
        clickList.add(mInspireAdContainer);
        //创建广告绑定相关View
        SimpleDaTuViewModel simpleDaTuViewModel = new SimpleDaTuViewModel(imageWith, clickList, null);
        //设置通用标题
        simpleDaTuViewModel.titleView = mInspireAdTvTitle;
        simpleDaTuViewModel.descView = mInspireAdTvSubtitle;
        //设置穿山甲,百度等广告根布局
        simpleDaTuViewModel.adContentRootView = mInspireAdContainer;
        simpleDaTuViewModel.adTagView = mInspireAdTag;
        simpleDaTuViewModel.adIconView = mInspireAdIcon;

        //设置广点通包装布局
        simpleDaTuViewModel.gdtNativeAdContainer = mNativeAdContainer;

        //设置广点通tag位置
        FrameLayout.LayoutParams gdtTagParams = new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,BaseUtil.dp2px(getActivity(), 12));
        gdtTagParams.gravity = Gravity.RIGHT | Gravity.TOP;
        gdtTagParams.topMargin = BaseUtil.dp2px(getActivity(), 4);
        gdtTagParams.rightMargin = BaseUtil.dp2px(getActivity(), 20);
        simpleDaTuViewModel.gtdAdTagLayoutParams = gdtTagParams;
        //设置tag的样式
        simpleDaTuViewModel.tagStyle = 2;

        //绑定广告相关事件，埋点等
        boolean isBindSuccess = mDatuAdProvider.bindViewDatas(thirdAd, simpleDaTuViewModel, thirdAd.getPositionName(), new OnCommonDaTuAdBindCallBack() {
            @Override
            public void onAdViewClick() {
                clickInspireAd(thirdAd.getAdvertis());
            }
        });
        //绑定失败
        if (!isBindSuccess) {
            mInspireAdContainer.setVisibility(View.GONE);
            return false;
        }

        //展示广告界面
        mInspireAdContainer.setVisibility(View.VISIBLE);
        return true;
    }

    private void clickInspireAd(Advertis advertis) {
        if (advertis == null) {
            return;
        }
        int tipsStayTime = advertis.getTipStayTime();
        int actualStayTime = advertis.getActualStayTime();
        boolean isNeedJumpStay = tipsStayTime > 0 && actualStayTime > 0;
        isJumping = true;
        if (!isNeedJumpStay) {
            log("clickInspireAd no needStay getReward");
            canGetReward = true;
        } else {
            canGetReward = false;
            log("clickInspireAd needStay startCountDown");
            startJumpCountdown(actualStayTime);
        }
    }

    private void startJumpCountdown(int actualStayTime) {
        jumpTimer = new CountDownTimerFix((long) actualStayTime * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                log("onTick = " + millisUntilFinished);
            }

            @Override
            public void onFinish() {
                log("finish");
                canGetReward = true;
            }
        };
        jumpTimer.start();
    }

    private void cancelJumpCountdown() {
        if (jumpTimer != null) {
            jumpTimer.cancel();
            jumpTimer = null;
        }
    }

    public void handleInspireAdResume() {
        log("onRecommendFragmentResume isJumping =" + isJumping);
        if (isJumping) {
            cancelJumpCountdown();
            log("onPlayFragmentResume canGetReward =" + canGetReward);
            if (canGetReward) {
                if (fromFreeListen()){
                    if (mJssdkFuliRewardCallback != null) {
                        mJssdkFuliRewardCallback.onAwardSuccess(JssdkFuliRewardCallback.CODE_SUCCESS_DEF, null);
                    }
                } else {
                    doRewardCoin();
                }
                dismiss();
            } else {
                CustomToast.showSuccessToast("未完成任务，暂未获取奖励");
            }
            isJumping = false;
        }
    }

    private void doRewardCoin() {
        CommonRequestM.getExchangeComplete(229, new IDataCallBack<ExchangeReceiveModel>() {
            @Override
            public void onSuccess(@Nullable ExchangeReceiveModel object) {
                if (object != null && object.getResult()) {
                    CustomToast.showSuccessToast(String.format("恭喜获得%d金币奖励", object.getScore()));
                    InspireAdRewardCoinManager.recordReward();
                } else {
                    CustomToast.showSuccessToast("网络异常，领取失败");
                }
            }

            @Override
            public void onError(int code, String message) {
                CustomToast.showSuccessToast("网络异常，领取失败");
            }
        });
    }

    private void log(String log) {
        Logger.i(TAG, log);
    }

    public boolean isShowing() {
        return mMaskIsShow;
    }

    @Override
    public int show(FragmentTransaction transaction, String tag) {
        if (mMaskIsShow) {
            return 0;
        }
        mMaskIsShow = true;
        return super.show(transaction, tag);
    }

    private void startTimer(long millisInFuture) {
        if (mTimer == null) {
            mTimer = new CountDownTimerFix(millisInFuture, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    long time = millisUntilFinished / 1000;
                    mCloseCountdownTime.setText(time + "");
                }

                @Override
                public void onFinish() {
                    mCloseCountdownTime.setVisibility(View.GONE);
                    mViewClose.setVisibility(View.VISIBLE);
                    //开启自动关闭
                    int autoCloseTime = ConfigureCenter.getInstance().getInt("ximalaya_lite_ad", "UniversalPopupAutomaticallyShutdownTime", 0);
                    if (autoCloseTime != 0) {
                        startAutoCloseTimer(autoCloseTime * 1000);
                    }
                }
            };
        }
        stopTimer();
        mTimer.start();
    }

    private void stopTimer() {
        if (mTimer != null) {
            mTimer.cancel();

        }
    }


    private void startAutoCloseTimer(long millisInFuture) {
        if (mAutoCloseTimer == null) {
            mAutoCloseTimer = new CountDownTimerFix(millisInFuture, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    //do nothing
                }

                @Override
                public void onFinish() {
                    dismissAllowingStateLoss();
                    // 通用广告弹窗自动关闭  其他事件
                    new XMTraceApi.Trace()
                            .setMetaId(34501)
                            .setServiceId("others")
                            .put("pageTitle", "通用广告弹窗")
                            .createTrace();
                }
            };
        }
        stopAutoCloseTimer();
        mAutoCloseTimer.start();
    }

    private void stopAutoCloseTimer() {
        if (mAutoCloseTimer != null) {
            mAutoCloseTimer.cancel();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        stopTimer();
        stopAutoCloseTimer();
        //停止呼吸动效
        stopBreatheAnimator();
        if (mDatuAdProvider != null) {
            mDatuAdProvider.onDestroy();
        }
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        if (mMaskIsShow) {
            return;
        }
        mMaskIsShow = true;
        super.show(manager, tag);
    }

    /**
     * 设置底部的广告数据
     */
    public void updateAdFeedAdBottom(AdWrapper mAdFeedAdBottom) {
        this.mAdWrapper = mAdFeedAdBottom;
    }

    public void setJssdkFuliRewardCallback(JssdkFuliRewardCallback jssdkFuliRewardCallback) {
        this.mJssdkFuliRewardCallback = jssdkFuliRewardCallback;
    }

    @Override
    public void onClick(View view) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int id = view.getId();
        //翻倍按钮被点击了
        if (id == R.id.main_layout_coin_earn_more) {
            FullCoinBallDialogManager.trackClickMore(FullCoinBallDialogManager.V1, mDataModel, getTrackAdId());
            //只有幸运气泡可以翻倍操作，加载幸运气泡的广告
            switch (mDataModel.ballType) {
                case FuliBallType.BALL_TYPE_LUCK:
                    //幸运气泡翻倍，可以进行翻倍
                    ListenEarnCoinDialogManager.getInstance().loadVideoAdReward(mDataModel.fulliCoinRewardReqModel, mDataModel.awardReceiveId, mJssdkFuliRewardCallback, AdConstants.XM_AD_NAME_CSJ_DOUBLE_GOLD_REWARD);
                    break;
                case FuliBallType.BALL_TYPE_HOME_VIDEO:
                    //首页视频激励，可以进行翻倍
                    ListenEarnCoinDialogManager.getInstance().loadVideoAdReward(mDataModel.fulliCoinRewardReqModel, mDataModel.awardReceiveId, mJssdkFuliRewardCallback, AdConstants.XM_AD_NAME_XXL_VIDEO);
                    break;
                case FuliBallType.BALL_TYPE_SUPPER_COMMON:
                    //通用奖励弹框，翻倍按钮被点击
                    if (mJssdkFuliRewardCallback != null) {
                        mJssdkFuliRewardCallback.onAwardSuccess(JssdkFuliRewardCallback.CODE_SUCCRSS_DIALOG_MULTI_BTN_CLICK, null);
                    }
                    break;
                case FuliBallType.BALL_TYPE_LISTEN_V2_NORMAL:
                    //通用的翻倍操作,新增加的翻倍都可以在尺寸增加新的case
                    if (!TextUtils.isEmpty(mDataModel.awardReceiveId)) {
                        //翻倍按钮被点击，默认加激励视频
                        ListenEarnCoinDialogManager.getInstance().showVideoAdWithCoinMultipleReward(mDataModel);
                    }
                    break;
                default:
                    //防止活动混乱，尽量不要在default中进行统一处理
                    break;
            }
            dismissAllowingStateLoss();
            return;
        }
    }

    /**
     * 启动呼吸动效
     */
    private void statBreatheAnimator() {
        if (mAdLayoutCard == null) {
            return;
        }
        //停止呼吸动效
        stopBreatheAnimator();
        if (mBreatheAnimatorSet == null) {
            mBreatheAnimatorSet = new AnimatorSet();
            //高度缩小动画
            ObjectAnimator changeSmallY = ObjectAnimator.ofFloat(mAdLayoutCard, "scaleY", 1.0f, 0.96f);
            changeSmallY.setDuration(400);
            //宽度缩小动画
            ObjectAnimator changeSmallX = ObjectAnimator.ofFloat(mAdLayoutCard, "scaleX", 1.0f, 0.96f);
            changeSmallX.setDuration(400);

            //高度变大动画
            ObjectAnimator changeBigY = ObjectAnimator.ofFloat(mAdLayoutCard, "scaleY", 0.96f, 1.0f);
            changeBigY.setDuration(400);
            //宽度变大动画
            ObjectAnimator changeBigX = ObjectAnimator.ofFloat(mAdLayoutCard, "scaleX", 0.96f, 1.0f);
            changeBigX.setDuration(400);

            //changeSmallX和changeSmallY一起播放
            mBreatheAnimatorSet.play(changeSmallY).with(changeSmallX);
            //changeSmallX在changeBigY后执行
            mBreatheAnimatorSet.play(changeBigY).after(changeSmallX);
            //changeBigY在600毫秒后执行
            mBreatheAnimatorSet.play(changeBigY).after(600);
            //changeBigX跟随changeBigY一起执行
            mBreatheAnimatorSet.play(changeBigY).with(changeBigX);
            mBreatheAnimatorSet.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    FuliLogger.log("呼吸动画==onAnimationStart");
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    FuliLogger.log("呼吸动画==onAnimationEnd");
                    //弹框已经弹出，并且没有被销毁，可以展示
                    if (!isShowing() || !isAddFix() || isDetached() || !isResumed() || isDestory()) {
                        return;
                    }
                    HandlerManager.postOnUIThreadDelay(new Runnable() {
                        @Override
                        public void run() {
                            //弹框已经弹出，并且没有被销毁，可以展示
                            //不可见不用弹出延时
                            if (!isShowing() || !isAddFix() || isDetached() || !isResumed() || isDestory()) {
                                FuliLogger.log("呼吸动画==禁止延时");
                                return;
                            }
                            FuliLogger.log("呼吸动画==postOnUIThreadDelay1000");
                            //statBreatheAnimator();
                        }
                    }, 1000);
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    FuliLogger.log("呼吸动画==onAnimationCancel");
                }

                @Override
                public void onAnimationRepeat(Animator animation) {
                    FuliLogger.log("呼吸动画==onAnimationRepeat");
                }
            });
        }
        mBreatheAnimatorSet.start();
    }

    /**
     * 停止呼吸动效
     */
    private void stopBreatheAnimator() {
        if (mBreatheAnimatorSet != null) {
            mBreatheAnimatorSet.cancel();
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (!fromFreeListen()) {
            startTopLightAnim();
        }
    }

    private void startTopLightAnim() {
        if (ivTopBgLightAnimator == null) {
            ivTopBgLightAnimator = ObjectAnimator.ofFloat(ivTopBgLight, "rotation", 0f, 360f);
            ivTopBgLightAnimator.setDuration(3600);
            ivTopBgLightAnimator.setInterpolator(new LinearInterpolator());
            ivTopBgLightAnimator.setRepeatMode(ValueAnimator.RESTART);
            ivTopBgLightAnimator.setRepeatCount(ValueAnimator.INFINITE);
        }
        ivTopBgLightAnimator.start();
    }

    private void stopTopLightAnim() {
        if (ivTopBgLightAnimator != null) {
            ivTopBgLightAnimator.cancel();
        }
    }

}