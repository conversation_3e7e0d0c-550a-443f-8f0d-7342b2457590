package com.ximalaya.ting.lite.main.download.utils;

import android.os.StatFs;

import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.download.bean.MultiTaskInfo;
import com.ximalaya.ting.lite.main.download.bean.SingleTaskInfo;
import com.ximalaya.ting.lite.main.download.bean.TaskInfo;

import org.apache.http.conn.ConnectTimeoutException;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.net.UnknownServiceException;

/**
 * <AUTHOR> feiwen
 * date   :
 * desc   :
 */
public class TaskUtil {

    /**
     * 根据异常获取错误代码
     */
    public static int getErrorCode(Exception e) {
        int code = TaskCode.ERROR;
        // 重新具体化修改
        if (e instanceof SocketTimeoutException) {
            code = TaskCode.ERROR_SOCKET_TIMEOUT_EXCEPTION;
        } else if (e instanceof ConnectTimeoutException) {
            code = TaskCode.ERROR_CONNECT_TIMEOUT_EXCEPTION;
        } else if (e instanceof ConnectException) {
            code = TaskCode.ERROR_CONNECT_EXCEPTION;
        } else if (e instanceof UnknownHostException) {
            code = TaskCode.ERROR_UNKNOWN_HOST_EXCEPTION;
        } else if (e instanceof UnknownServiceException) {
            code = TaskCode.ERROR_UNKNOWN_SERVICE_EXCEPTION;
        } else if (e instanceof SocketException) {
            code = TaskCode.ERROR_NET;
        }

        return code;
    }

    /**
     * 删除任务相关的文件
     */
    public static void clearTaskAllFile(TaskInfo i, boolean deleteTempFile) {
        TaskInfo info = i;
        if (deleteTempFile) {
            // 清除临时文件
            File temp = new File(info.getDirPath(), info.getFileName() + DownloadConst.MD_DATA_SUFFIX);
            if (temp.exists()) {
                temp.delete();
            }
            // 清除信息文件(多线程下载的信息)
            File infoFile = new File(info.getDirPath(), info.getFileName() + DownloadConst.MD_CONFIG_SUFFIX);
            if (infoFile.exists()) {
                infoFile.delete();
            }
        }
    }

    /**
     * 向指定的文件中写入配置信息
     */
    public static void writeConfigInfo(File file, Object infos) {
        ObjectOutputStream out = null;
        try {
            out = new ObjectOutputStream(new FileOutputStream(file));
            out.writeObject(infos);
        } catch (Exception e) {
            Logger.e("Multi", e.toString());
        } finally {
            // 关闭流
            if (out != null) {
                try {
                    out.flush();
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 从指定的路径中读取任务配置信息
     *
     * @return 任务信息
     */
    public static MultiTaskInfo readMultiConfigInfo(File file) {
        ObjectInputStream out = null;
        MultiTaskInfo info = null;
        try {
            out = new ObjectInputStream(new FileInputStream(file));
            info = (MultiTaskInfo) out.readObject();
        } catch (Exception e) {
            Logger.e("Multi", e.toString());
        } finally {
            // 关闭流
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return info;
    }

    public static SingleTaskInfo readSingleConfigInfo(File file) {
        ObjectInputStream out = null;
        SingleTaskInfo info = null;
        try {
            out = new ObjectInputStream(new FileInputStream(file));
            info = (SingleTaskInfo) out.readObject();
        } catch (Exception e) {
            Logger.e("Multi", e.toString());
        } finally {
            // 关闭流
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return info;
    }

    public static boolean checkAvailableSpace(long size, long currSize, long leftSpace) {
        final long limit = 50 * 1024 * 1024;
        final long left = size - currSize;
        if (size > limit) {
            return leftSpace > (left + limit);
        } else {
            return leftSpace > left * 2;
        }
    }

    /**
     * create a file. Return true if succeed, return false if failed.
     *
     */
    public static boolean createFile(File file) {
        File parentFile = file.getParentFile();
        if (parentFile != null && !parentFile.exists()) {
            if (parentFile.mkdirs()) {
                try {
                    return file.createNewFile();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else {
                return false;
            }
        } else {
            try {
                return file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    public static boolean exist(File file) {
        if (file != null && file.exists()) {
            return true;
        }
        return false;
    }

    /**
     * 创建指定大小的文件
     *
     */
    public static void createEmptyFile(File file, long size) throws IOException {
        RandomAccessFile access = null;
        try {
            file.createNewFile();
            access = new RandomAccessFile(file, "rw");
            access.setLength(size);
        } finally {
            if (access != null)
                access.close();
        }
    }

    /**
     * 存储工具类 Get available space by path
     */
    public static long getAvailableSpace(String path) {
        StatFs sf = new StatFs(path);
        long blockSize = sf.getBlockSize();
        long availCount = sf.getAvailableBlocks();
        return availCount * blockSize;
    }
    /**
     * copy a file from srcFile to destFile, return true if succeed, return
     * false if fail
     */
    public static boolean copyFile(File srcFile, File destFile) {
        boolean result = false;
        try {
            InputStream in = new FileInputStream(srcFile);
            try {
                result = copyToFile(in, destFile);
            } finally {
                in.close();
            }
        } catch (IOException e) {
            result = false;
        }
        return result;
    }

    /**
     * Copy data from a source stream to destFile. Return true if succeed,
     * return false if failed.
     */
    public static boolean copyToFile(InputStream inputStream, File destFile) {
        try {
            if (destFile.exists()) {
                destFile.delete();
            }
            OutputStream out = new FileOutputStream(destFile);
            try {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) >= 0) {
                    out.write(buffer, 0, bytesRead);
                }
            } finally {
                out.close();
            }
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * Move file,
     */
    public static boolean move(File src, File des) {
        if (copyFile(src, des)) {
            src.delete();
            return true;
        } else {
            return false;
        }
    }

    /**
     * 删除目录（文件夹）以及目录下的文件
     *
     */
    public static boolean deleteDirectory(String dir) {
        // 如果dir不以文件分隔符结尾，自动添加文件分隔符
        try {
            if (!dir.endsWith(File.separator)) {
                dir = dir + File.separator;
            }
            File dirFile = new File(dir);
            // 如果dir对应的文件不存在，或者不是一个目录，则退出
            if (!dirFile.exists() || !dirFile.isDirectory()) {
                // Log.i(TAG, "delete dir fail!" + dirFile + " no exit!");
                return false;
            }
            boolean flag = true;
            // 删除文件夹下的所有文件(包括子目录)
            File[] files = dirFile.listFiles();
            if(files != null) {
                for (File file : files) {
                    // 删除子文件
                    if (file.isFile()) {
                        flag = deleteFile(file.getAbsolutePath());
                        if (!flag) {
                            break;
                        }
                    }
                    // 删除子目录
                    else {
                        flag = deleteDirectory(file.getAbsolutePath());
                        if (!flag) {
                            break;
                        }
                    }
                }
            }

            if (!flag) {
                return false;
            }

            // 删除当前目录
            return dirFile.delete();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 删除单个文件
     *
     */
    public static boolean deleteFile(String fileName) {
        File file = new File(fileName);
        if (file.isFile() && file.exists()) {
            file.delete();
            // Log.i(TAG, "delete the file" + fileName + " success!");
            return true;
        } else {
            // Log.i(TAG, "delete the file" + fileName + "fail!");
            return false;
        }
    }
}
