package com.ximalaya.ting.lite.main.customize;

import android.app.Activity;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.NormalModeActivity;
import com.ximalaya.ting.android.host.activity.TruckFriendModeActivity;
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.customize.CustomizeManager;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.model.user.CustomizeCategory;
import com.ximalaya.ting.android.host.model.user.InterestCardModel;
import com.ximalaya.ting.android.host.model.user.InterestCardSetting;
import com.ximalaya.ting.android.host.model.user.NewInterestCardModel;
import com.ximalaya.ting.android.host.model.user.NewInterestCardResp;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.host.fragment.earn.DeleteSignInEventPromptFragment;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created by dumingwei on 2021/1/14
 * <p>
 * Desc: 底部弹出的兴趣选择页面
 */
public class CustomizeBottomStyleDialog extends BaseFullScreenDialogFragment implements View.OnClickListener {

    private static final String TAG = "CustomizeFragment";

    private static final String INTERESTCARDSETTING = "INTERESTCARDSETTING";

    private static final int MIN_CAN_SELECT = 3;           //最少要选择3个兴趣卡片
    private static final int MAX_CAN_SELECT = 10;           //最多可选中10个兴趣卡片

    private static final int SPAN_COUNT = 3;               //网格一行有几个item

    private Button mBtnComplete;

    //用户选择的数据
    private InterestCardModel mSelectData;
    private LinearLayout mHobbyContentLayout;
    private final ArrayList<CustomizeCategory> mCategoryList = new ArrayList<>();
    private RecyclerView mRvCategory;

    private boolean canSkip = true;

    private int from = -1;
    private String mRecommTaitKey = "defaultKey";

    private int mMinCanSelected = MIN_CAN_SELECT;
    private int mMaxCanSelected = MAX_CAN_SELECT;

    private final List<String> interestBgColorList = new ArrayList<>(12);

    /**
     * 铺平后的兴趣卡片列表
     */
    private final ArrayList<NewInterestCardModel> flatInterestList = new ArrayList<>();
    private InterestCardAdapter mInterestAdapter;

    private DeleteSignInEventPromptFragment promptDialogFragment;

    private long mLastClickTime = 0;

    public CustomizeBottomStyleDialog() {
        initInterestColorList();
    }

    public static CustomizeBottomStyleDialog newInstance(InterestCardSetting setting) {
        CustomizeBottomStyleDialog customizeFragment = new CustomizeBottomStyleDialog();
        Bundle extra = new Bundle();
        extra.putParcelable(INTERESTCARDSETTING, setting);
        customizeFragment.setArguments(extra);
        return customizeFragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle args = getArguments();
        if (args != null) {
            InterestCardSetting setting = args.getParcelable(INTERESTCARDSETTING);
            if (setting != null) {
                from = setting.getFrom();
                canSkip = setting.getInterestCardCanSkip();
                mMinCanSelected = setting.getInterestCardMinCount();
                mMaxCanSelected = setting.getInterestCardMaxCount();
            }
        }

        mSelectData = new InterestCardModel();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.main_fra_bottom_style_customization, container, false);
        TextView tvSkip = view.findViewById(R.id.main_tv_skip);
        tvSkip.setOnClickListener(this);
        if (canSkip) {
            tvSkip.setVisibility(View.VISIBLE);
        } else {
            tvSkip.setVisibility(View.INVISIBLE);
        }
        mBtnComplete = view.findViewById(R.id.main_btn_complete);
        mBtnComplete.setOnClickListener(this);
        AutoTraceHelper.bindData(mBtnComplete, mSelectData);
        mHobbyContentLayout = view.findViewById(R.id.main_custom_hobby_content);
        mHobbyContentLayout.setClipChildren(false);
        mRvCategory = view.findViewById(R.id.main_rv_custom_category);
        initRvCategory();

        chooseInterest();

        trackOnShow();
        return view;
    }

    private void initInterestColorList() {
        interestBgColorList.add("#EDF3EE");
        interestBgColorList.add("#EAF2F4");
        interestBgColorList.add("#F4F4FA");

        interestBgColorList.add("#F7F1ED");
        interestBgColorList.add("#F7F4ED");
        interestBgColorList.add("#E9EEF1");

        interestBgColorList.add("#EDF3EE");
        interestBgColorList.add("#EAF2F4");
        interestBgColorList.add("#F4F4FA");

        interestBgColorList.add("#F7F1ED");
        interestBgColorList.add("#F7F4ED");
        interestBgColorList.add("#E9EEF1");
    }

    private void initRvCategory() {
        mInterestAdapter = new InterestCardAdapter(getContext(), SPAN_COUNT, flatInterestList);
        mInterestAdapter.setOnSelectedInterface(new InterestCardAdapter.OnSelectedInterface() {
            @Override
            public void onSelected(int position, @NotNull NewInterestCardModel model) {
                long currentTimeMillis = System.currentTimeMillis();
                if (currentTimeMillis - mLastClickTime < 300) {
                    return;
                }

                mLastClickTime = currentTimeMillis;

                boolean selected = model.getChosen();

                //添加到mSelectData.interestedCategories中
                List<String> interestedCategories = mSelectData.interestedCategories;

                if (selected) {
                    //先前是选中，现在取消选中
                    interestedCategories.remove(String.valueOf(model.getCode()));
                } else {
                    //在选中之前要检查是否已经选的够多了
                    if (interestedCategories.size() < mMaxCanSelected) {
                        new XMTraceApi.Trace()
                                .click(25791)
                                .put("currPage", "interestPage")
                                .put("item", model.getCategoryName())
                                .put("traitCode", mRecommTaitKey)
                                .createTrace();
                        interestedCategories.add(String.valueOf(model.getCode()));
                    } else {
                        CustomToast.showSuccessToast(getString(R.string.main_have_selected_too_many));
                        return;
                    }
                }
                //设置选中状态
                model.setChosen(!selected);

                List<NewInterestCardModel> subCategories = model.getSubCategories();
                mInterestAdapter.notifyItemChanged(position);

                if (CollectionUtil.isNotEmpty(subCategories)) {
                    //添加或者移除的开始位置
                    int startInsertPosition = position + 1;
                    int size = subCategories.size();
                    if (model.getChosen()) {//选中，如果有子级兴趣，则添加到适配器中
                        boolean childNotInFlatList = true;
                        for (NewInterestCardModel subCategory : subCategories) {
                            if (flatInterestList.contains(subCategory)) {
                                //至少有一个子级兴趣被选中
                                childNotInFlatList = false;
                                break;
                            }
                        }
                        //已经添加过了，就不再添加了
                        if (childNotInFlatList) {
                            for (int i = 0; i < size; i++) {
                                flatInterestList.add(startInsertPosition + i, subCategories.get(i));
                            }
                            mInterestAdapter.notifyItemRangeInserted(startInsertPosition, size);

                            mInterestAdapter.notifyItemRangeChanged(startInsertPosition, flatInterestList.size() - startInsertPosition);
                        }
                    } else {//取消选中，如果有子级兴趣，且没有一个子级兴趣被选中，则全部移除。
                        boolean noChildSelected = true;
                        for (NewInterestCardModel subCategory : subCategories) {
                            if (subCategory.getChosen()) {
                                //至少有一个子级兴趣被选中
                                noChildSelected = false;
                                break;
                            }
                        }
                        if (noChildSelected) {
                            flatInterestList.removeAll(subCategories);
                            mInterestAdapter.notifyItemRangeRemoved(startInsertPosition, size);
                            mInterestAdapter.notifyItemRangeChanged(startInsertPosition, flatInterestList.size() - startInsertPosition);
                        }
                    }
                }

                updatePageUi();
            }
        });

        mRvCategory.setAdapter(mInterestAdapter);
        mRvCategory.setLayoutManager(new GridLayoutManager(getContext(), SPAN_COUNT));

        DefaultItemAnimator defaultItemAnimator = new DefaultItemAnimator();
        mRvCategory.setItemAnimator(defaultItemAnimator);
    }

    public void setData(NewInterestCardResp resp) {
        if (!TextUtils.isEmpty(resp.getRecommTaitKey())) {
            mRecommTaitKey = resp.getRecommTaitKey();
        }

        List<NewInterestCardModel> interestCardModelList = resp.getList();
        flatInterestList.clear();

        //最多展示12个
        int maxShowSize = 12;
        if (interestCardModelList.size() <= maxShowSize) {
            flatInterestList.addAll(interestCardModelList);
        } else {
            List<NewInterestCardModel> tempList = interestCardModelList.subList(0, maxShowSize);
            flatInterestList.addAll(tempList);
        }

        if (CollectionUtil.isNotEmpty(flatInterestList)) {
            int colorListSize = interestBgColorList.size();
            for (int i = 0; i < flatInterestList.size(); i++) {
                flatInterestList.get(i).setBgColor(interestBgColorList.get(i % colorListSize));
            }
            onInterestCardFetched();
        }
    }

    private void onInterestCardFetched() {
        if (canUpdateUi() && mInterestAdapter != null) {
            mInterestAdapter.notifyDataSetChanged();
            updatePageUi();
        }
    }

    /**
     * 加载兴趣卡片页面
     */
    private void chooseInterest() {
        mHobbyContentLayout.setVisibility(View.VISIBLE);
        //需要重置，返回页面可能出现断网状态重叠
        updatePageUi();
        //每次选中兴趣卡片，都清除保存的数据，年龄和性别不同所请求到的兴趣卡片不同，所以需要重新loadData()
        mCategoryList.clear();
        mInterestAdapter.notifyDataSetChanged();
        mSelectData.interestedCategories.clear();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_btn_complete) {
            postCustomizationInfo();
        } else if (id == R.id.main_tv_skip) {
            dismiss();
            trackSkip();
        }
    }

    /**
     * 用户点击按钮提交，带loading，带回调处理
     */
    private void postCustomizationInfo() {
        if (CollectionUtil.isNullOrEmpty(flatInterestList)) {
            dismiss();
            return;
        }

        mBtnComplete.setEnabled(false);
        //选中的个数
        int count = 0;
        int size = flatInterestList.size();
        StringBuilder builder = new StringBuilder();

        String categories = null;
        List<String> tempList = new ArrayList<>();

        for (int index = 0; index < size; index++) {
            NewInterestCardModel cardModel = flatInterestList.get(index);
            if (cardModel.getChosen() && !TextUtils.isEmpty(cardModel.getCode())) {
                tempList.add(cardModel.getCode());
                if (index > 0) {
                    builder.append(",");
                }
                builder.append(cardModel.getCategoryName());
                count++;
            }
        }

        if (CollectionUtil.isNotEmpty(tempList)) {
            categories = JsonUtilKt.getInstance().toJson(tempList);
        }

        Map<String, String> params = new HashMap<>();
        if (UserInfoMannage.hasLogined()) {
            params.put("uid", UserInfoMannage.getUid() + "");
        }
        params.put("deviceId", DeviceUtil.getDeviceToken(getActivity()));
        if (!TextUtils.isEmpty(categories)) {
            params.put("newCodes", categories);
        }
        LiteCommonRequest.postCustomizationInfo(params, new
                IDataCallBack<JSONObject>() {
                    @Override
                    public void onSuccess(JSONObject object) {
                        if (!canUpdateUi()) {
                            return;
                        }
                        notifyRefreshHomePage();
                        //这里并不会因为保存选择的数据，导致首页重复刷新。因为这个弹出框不会导致HomeRecommendFragment重新onMyResume
                        CustomizeManager.getInstance().saveLocalCustomize(mSelectData);
                        showSuccessToastAndFinish();
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (!canUpdateUi()) {
                            return;
                        }
                        mBtnComplete.setEnabled(true);
                        ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
                    }
                });

        trackOnSubmit(builder.toString(), count);
    }

    private void notifyRefreshHomePage() {
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            NormalModeActivity normalModeActivity = ((MainActivity) activity).getNormalModeActivity();
            if (normalModeActivity != null) {
                normalModeActivity.refreshHomePage();
            }
            TruckFriendModeActivity truckFriendModeActivity = ((MainActivity) activity).getTruckFriendModeActivity();
            if (truckFriendModeActivity != null) {
                truckFriendModeActivity.refreshHomePage();
            }
        }
    }

    protected void showSuccessToastAndFinish() {
        if (from != 0) {
            CustomToast.showSuccessToast("修改成功");
        }
        exit();
    }

    /**
     * upDate
     * 刷新当前页面的UI
     */
    private void updatePageUi() {
        List<String> interestedCategories = mSelectData.interestedCategories;
        if (CollectionUtil.isNullOrEmpty(interestedCategories)) {
            mBtnComplete.setEnabled(false);
            mBtnComplete.setText(getString(R.string.main_selected_at_least, mMinCanSelected));
        } else {
            int size = interestedCategories.size();
            if (size < mMinCanSelected) {
                mBtnComplete.setEnabled(false);
                mBtnComplete.setText(getString(R.string.main_have_selected, size, mMinCanSelected));
            } else {
                mBtnComplete.setEnabled(true);
                mBtnComplete.setText(getString(R.string.main_enjoy_now));
            }
        }

    }

    private void showPromptDialog() {
        if (promptDialogFragment == null) {
            promptDialogFragment = new DeleteSignInEventPromptFragment();
            promptDialogFragment.setOnDialogConfirmListener(new BaseDialogFragment.OnDialogConfirmListener() {
                @Override
                public void onConfirm() {
                    exit();
                }
            });
            promptDialogFragment.setDialogTitle(getString(R.string.main_your_interest_not_saved));
        }
        if (canUpdateUi()) {
            promptDialogFragment.show(getActivity().getSupportFragmentManager(), "");
        }
    }

    /**
     * 退出兴趣选择，没有完成提交，然后退出兴趣选择页，统一调用这个方法
     */
    private void exit() {
        dismiss();
        statCloseAction();
    }

    private void statCloseAction() {
        new XMTraceApi.Trace()
                .pageExit2(25787)
                .put("currPage", "interestPage")
                .put("status", String.valueOf(from))//0表示是首页自动弹出的
                .createTrace();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    private void trackOnSubmit(String selectedCodes, int size) {
        if (!TextUtils.isEmpty(selectedCodes) && size > 0) {
            new XMTraceApi.Trace()
                    .click(25792)
                    .put("currPage", "interestPage")
                    .put("traitCode", mRecommTaitKey)
                    .put("status", String.valueOf(from))
                    .put("currItemId", selectedCodes)
                    .put("currItem", String.valueOf(size))
                    .createTrace();
        }
    }


    private void trackOnShow() {
        new XMTraceApi.Trace()
                .pageView(25786, "interestPage")
                .put("status", String.valueOf(from))//0表示是首页自动弹出的
                .createTrace();
    }

    @Override
    public boolean isShowFromBottomEnable() {
        return true;
    }

    /**
     * 跳过埋点
     */
    private void trackSkip() {
        new XMTraceApi.Trace()
                .click(25793)
                .put("currPage", "interestPage")
                .createTrace();
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        if (mInterestAdapter != null) {
            mInterestAdapter.notifyDataSetChanged();
        }
    }
}
