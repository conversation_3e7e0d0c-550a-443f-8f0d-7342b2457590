package com.ximalaya.ting.lite.main.earn;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil;

public class InspireAdRewardCoinManager {

    public static final int MAX_REWARD_TIME = 1;
    public static final String KEY_HAS_REWARD_TIME = "key_has_reward_time";
    public static final String KEY_LAST_REWARD_DATE = "key_last_reward_date";

    public static boolean requestInspireAdEnable() {
        return getMaxTimeReward() > hasRewardTime() ;
    }

    private static int hasRewardTime() {
        long lastDate = mmkvCommonUtil().getLong(KEY_LAST_REWARD_DATE, -1);
        if (lastDate <= 0 || !DateTimeUtil.isTodayTs(lastDate)){
            return 0;
        }
        return mmkvCommonUtil().getInt(KEY_HAS_REWARD_TIME, 0);
    }
    
    public static void recordReward(){
        mmkvCommonUtil().saveLong(KEY_LAST_REWARD_DATE, System.currentTimeMillis());
        mmkvCommonUtil().saveInt(KEY_HAS_REWARD_TIME, hasRewardTime() + 1);
    }
    
    private static int getMaxTimeReward(){
        int maxTime;
        try {
            maxTime = ConfigureCenter.getInstance().getInt(CConstants.Group_Base.GROUP_NAME,
                    CConstants.Group_Ad.ITEM_INSPIRE_AD_REWARD_COIN_MAX_TIME);
            mmkvCommonUtil().saveInt(CConstants.Group_Ad.ITEM_INSPIRE_AD_REWARD_COIN_MAX_TIME, maxTime);
        } catch (Throwable throwable) {
            maxTime = mmkvCommonUtil().getInt(CConstants.Group_Ad.ITEM_INSPIRE_AD_REWARD_COIN_MAX_TIME, MAX_REWARD_TIME);
        }
        return maxTime;
    }


    private static MmkvCommonUtil mmkvCommonUtil() {
        return MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext());
    }

}
