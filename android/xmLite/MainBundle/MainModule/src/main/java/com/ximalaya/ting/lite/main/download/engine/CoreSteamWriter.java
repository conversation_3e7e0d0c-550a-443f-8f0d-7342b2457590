package com.ximalaya.ting.lite.main.download.engine;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR> feiwen
 * date   : 2019/4/8
 * desc 流处理类，处理下载任务，这里只对流进行处理
 */
public class CoreSteamWriter {
    public static final int BUFFER_SIZE = 1024 * 32;

    public void transmit(InputStream in, ITransmitCallback callback, ITransmitControl control) throws IOException {
        int len = 0;
        int done = 0;
        byte[] buffer = new byte[BUFFER_SIZE];
        while (!control.cancel()) {
            try {
                len = in.read(buffer);
            } catch (IOException e) {
                if (control.cancel()) {
                    break;
                } else {
                    throw e;
                }
            }

            if (len == -1) {
                callback.progress(buffer, done, 0);
                break;
            }

            done += len;
            callback.progress(buffer, done, len);
        }
    }

    public interface ITransmitCallback {
        void progress(byte[] buffer, int done, int len);
    }

    public interface ITransmitControl {
        boolean cancel();
    }

}
