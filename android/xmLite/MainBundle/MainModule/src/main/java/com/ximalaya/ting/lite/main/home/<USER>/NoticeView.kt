package com.ximalaya.ting.lite.main.home.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.RelativeLayout
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.model.NoticeModel
import kotlinx.android.synthetic.main.main_notice_view.view.*

/**
 * Created by du<PERSON><PERSON> on 2020/9/21.
 *
 * Desc: 首页通知公告
 */

class NoticeView @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    var onCloseListener: OnClickListener? = null

    var onDetailClickListener: ((url: String) -> Unit)? = null

    private var url: String? = null

    init {
        View.inflate(context, R.layout.main_notice_view, this)
        main_iv_inner_close_notice.setOnClickListener {
            main_rl_notice_root_view.visibility = View.GONE
            onCloseListener?.onClick(this)
        }
        main_iv_outer_close_notice.setOnClickListener {
            main_rl_notice_root_view.visibility = View.GONE
            onCloseListener?.onClick(this)
        }
        main_rl_notice_body.setOnClickListener {
            url?.let {
                onDetailClickListener?.invoke(it)
            }
        }

        main_rl_notice_root_view.visibility = View.GONE
    }

    fun setData(model: NoticeModel) {
        main_rl_notice_root_view.visibility = View.VISIBLE
        main_tv_notice_content.text = model.title
        url = model.url
        if (url.isNullOrEmpty()) {
            //不展示详情按钮
            main_iv_inner_close_notice.visibility = View.VISIBLE
            main_tv_detail.visibility = View.GONE
            main_iv_outer_close_notice.visibility = View.GONE

            val layoutParams = main_rl_notice_body.layoutParams as RelativeLayout.LayoutParams
            layoutParams.topMargin = 0
            val noticeContentLayoutParams = main_tv_notice_content.layoutParams as RelativeLayout.LayoutParams
            noticeContentLayoutParams.addRule(RelativeLayout.START_OF, R.id.main_iv_inner_close_notice)

        } else {
            main_iv_outer_close_notice.visibility = View.VISIBLE
            main_tv_detail.visibility = View.VISIBLE
            main_iv_inner_close_notice.visibility = View.GONE
            val layoutParams = main_rl_notice_body.layoutParams as RelativeLayout.LayoutParams
            layoutParams.topMargin = BaseUtil.dp2px(context, 6f)

            val noticeContentLayoutParams = main_tv_notice_content.layoutParams as RelativeLayout.LayoutParams
            noticeContentLayoutParams.addRule(RelativeLayout.START_OF, R.id.main_tv_detail)
        }
    }

}