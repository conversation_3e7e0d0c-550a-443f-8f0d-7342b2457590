package com.ximalaya.ting.lite.main.earn.dialog;

import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment;
import com.ximalaya.ting.android.host.listener.IHomeDialogCanNextCallback;
import com.ximalaya.ting.android.host.listenertask.GlobalServiceTimeSyncManager;
import com.ximalaya.ting.android.host.manager.earn.NewUserRewardDialogManager;
import com.ximalaya.ting.android.host.model.user.NewUserRewardGiftList;
import com.ximalaya.ting.android.host.util.CountDownTimerFix;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.earn.dialog.adapter.OptimizedNewUserRewardGiftRvAdapter;
import com.ximalaya.ting.lite.main.manager.ITingHandler;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 06/14
 * <p> 新人奖励
 */
public class OptimizedLoginNewUserRewardDialogFragment extends BaseFullScreenDialogFragment implements View.OnClickListener {


    private boolean mMaskIsShow = false; //解决fragment重复添加crash问题
    private static final String ARGUMENT_KEY_REWARD_DIALOG_DATA_MODEL = "reward_dialog_data_model";
    private static final String ARGUMENT_KEY_REWARD_CAN_CACHE = "argument_key_reward_can_cache";
    private static final String TAG = "newUserGiftPage";


    private IHomeDialogCanNextCallback mCanNextCallback;


    private ImageView viewClose, ivTopNewUser;
    private TextView ivNewUserTime;
    private OptimizedNewUserRewardGiftRvAdapter earnGuideRvAdapter;

    private RecyclerView mRecyclerViewNewUser;

    private CountDownTimerFix mCountDownTimer;
    private NewUserRewardGiftList newUserRewardGiftList;
    private boolean isCanUseCache;


    public static Bundle newArgument(NewUserRewardGiftList newUserRewardGiftList, boolean isCanUseCache) {
        Bundle bundle = new Bundle();
        bundle.putSerializable(ARGUMENT_KEY_REWARD_DIALOG_DATA_MODEL, newUserRewardGiftList);
        bundle.putBoolean(ARGUMENT_KEY_REWARD_CAN_CACHE, isCanUseCache);
        return bundle;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            newUserRewardGiftList = (NewUserRewardGiftList) arguments.getSerializable(ARGUMENT_KEY_REWARD_DIALOG_DATA_MODEL);
            isCanUseCache = arguments.getBoolean(ARGUMENT_KEY_REWARD_CAN_CACHE);
        }
        View view = inflater.inflate(R.layout.main_fra_dialog_optimized_new_user_reward, container, false);
        mRecyclerViewNewUser = view.findViewById(R.id.rcy_new_user);
        ivTopNewUser = view.findViewById(R.id.iv_top_new_user);
        viewClose = view.findViewById(R.id.iv_top_new_user_close);
        ivNewUserTime = view.findViewById(R.id.iv_top_new_user_time);
        viewClose.setOnClickListener(this);
        earnGuideRvAdapter = new OptimizedNewUserRewardGiftRvAdapter(getActivity(), newUserRewardGiftList.getGifts(), new NewUserJumpRewardCallBack() {
            @Override
            public void jumpRewardBack(String jumpTxt, String jumpUrl) {
                if (!TextUtils.isEmpty(jumpUrl)) {
                    new ITingHandler().handleITing(getActivity(), Uri.parse(jumpUrl));
                }
                dismissAllowingStateLoss();
                // 新人礼包限时领取弹窗-btn  弹框控件点击
                new XMTraceApi.Trace()
                        .setMetaId(54916)
                        .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                        .put("dialogItem", jumpTxt)
                        .put("currPage", TAG)
                        .createTrace();
            }
        });
        mRecyclerViewNewUser.addItemDecoration(new SpaceItemDecoration(
                BaseUtil.dp2px(getContext(), 9.5f), 0, BaseUtil.dp2px(getContext(), 9.5f), 0));
        mRecyclerViewNewUser.setNestedScrollingEnabled(false);
        mRecyclerViewNewUser.setLayoutManager(new GridLayoutManager(getActivity(), 2, GridLayoutManager.VERTICAL, false));
        mRecyclerViewNewUser.setItemViewCacheSize(newUserRewardGiftList.getGifts().size());
        mRecyclerViewNewUser.setAdapter(earnGuideRvAdapter);
        loadDataByCanCache();
        return view;
    }

    private void loadDataByCanCache() {
        if (!isCanUseCache) return;
        NewUserRewardDialogManager.INSTANCE.getNewUserGiftsUrlFromNet(new IDataCallBack<NewUserRewardGiftList>() {
            @Override
            public void onSuccess(@Nullable NewUserRewardGiftList object) {
                try {
                    if (object != null && CollectionUtil.isNotEmpty(object.getGifts()) && earnGuideRvAdapter != null) {
                        List<NewUserRewardGiftList.NewUserRewardGift> newUserRewardGifts = earnGuideRvAdapter.getValueList();
                        newUserRewardGifts.clear();
                        if (object.getGifts() != null) {
                            newUserRewardGifts.addAll(object.getGifts());
                        }
                        earnGuideRvAdapter.notifyItemRangeChanged(0, newUserRewardGifts.size());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        startTimer(getDuration());
        // 新人礼包限时领取弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(54915)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put("currPage", TAG)
                .createTrace();
    }

    private long getCurrentServiceTimeMillis() {
        long time = GlobalServiceTimeSyncManager.getInstance().getCurrentServiceTimeMillis(BaseApplication.getMyApplicationContext());
        // 服务器时间为空 则使用本地时间
        if (time == 0L) {
            time = System.currentTimeMillis();
        }
        return time;
    }

    protected void startTimer(long millis) {
        if (millis < 0 || ivNewUserTime == null) return;
        stopTimer();
        if (mCountDownTimer == null) {
            mCountDownTimer = new CountDownTimerFix(1000, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    // 计算天数差
                    long days = TimeUnit.MILLISECONDS.toDays(millisUntilFinished);
                    // 剩余的毫秒数
                    long remainingMillis = millisUntilFinished - TimeUnit.DAYS.toMillis(days);
                    // 计算小时数差
                    long hours = TimeUnit.MILLISECONDS.toHours(remainingMillis);
                    // 剩余的毫秒数
                    remainingMillis -= TimeUnit.HOURS.toMillis(hours);
                    // 计算分钟数差
                    long minutes = TimeUnit.MILLISECONDS.toMinutes(remainingMillis);
                    String result = days + "天" +
                            hours + "小时" +
                            minutes + "分";
                    SpannableString spannableString = new SpannableString(result);
                    int firstIndex = result.indexOf("天");
                    int secondIndex = result.indexOf("小");
                    int thirdIndex = result.indexOf("分");
                    spannableString.setSpan(new StyleSpan(Typeface.BOLD), 0, firstIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#FF4444")), 0, firstIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    spannableString.setSpan(new StyleSpan(Typeface.BOLD), firstIndex + 1, secondIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#FF4444")), firstIndex + 1, secondIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    spannableString.setSpan(new StyleSpan(Typeface.BOLD), secondIndex + 2, thirdIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#FF4444")), secondIndex + 2, thirdIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    ivNewUserTime.setText(spannableString);
                }

                @Override
                public void onFinish() {

                }
            };
        }
        mCountDownTimer.resetMillisInFuture(millis + 1000);
        mCountDownTimer.start();
    }

    protected void stopTimer() {
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
        }
    }

    private long getDuration() {
        if (newUserRewardGiftList == null || newUserRewardGiftList.getExpiryTime() < 0) {
            return -1;
        }
        Date startDate = new Date(getCurrentServiceTimeMillis());  // 获取服务端时间
        Date endDate = new Date(newUserRewardGiftList.getExpiryTime());    // 第二个时间
        // 计算时间差（毫秒）
        return endDate.getTime() - startDate.getTime();
    }


    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        stopTimer();
    }


    @Override
    public int show(FragmentTransaction transaction, String tag) {
        if (mMaskIsShow) {
            return 0;
        }
        mMaskIsShow = true;
        return super.show(transaction, tag);
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        if (mMaskIsShow) {
            return;
        }
        mMaskIsShow = true;
        super.show(manager, tag);
    }


    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.iv_top_new_user_close) {
            dismissAllowingStateLoss();
            // 新人礼包限时领取弹窗-关闭  弹框控件点击
            new XMTraceApi.Trace()
                    .setMetaId(54917)
                    .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                    .put("currPage", TAG)
                    .createTrace();
        }
    }

    class SpaceItemDecoration extends RecyclerView.ItemDecoration {
        private final int mLeftSpace;
        private final int mTopSpace;
        private final int mRightSpace;
        private final int mBottomSpace;

        public SpaceItemDecoration(int leftSpace, int topSpace, int rightSpace, int bottomSpace) {
            mLeftSpace = leftSpace;
            mTopSpace = topSpace;
            mRightSpace = rightSpace;
            mBottomSpace = bottomSpace;
        }

        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            if (parent.getLayoutManager() instanceof GridLayoutManager) {
                outRect.left = mLeftSpace;
                outRect.top = mTopSpace;
                outRect.right = mRightSpace;
                outRect.bottom = mBottomSpace;
            }
        }
    }

    public interface NewUserJumpRewardCallBack {
        void jumpRewardBack(String jumpTxt, String jumpUrl);
    }
}
