package com.ximalaya.ting.lite.main.home.viewmodel;

import com.ximalaya.ting.android.opensdk.model.album.Album;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/1/14
 *
 * <AUTHOR>
 */
public class HomeItemSubscriptionViewModel {

    //禁止随意,可以往后追加，可视化埋点在使用
    public static final int ITEM_ADD = 0;
    public static final int ITEM_SUBSCRIBE_ALBUM = 1;
    public static final int ITEM_RECOMMEND_ALBUM = 2;
    public static final int ITEM_MORE = 3;
    public static final int ITEM_LINE = 4;

    public int viewType;
    public Album album;
    public int hasSubscribeCount = 0;
}
