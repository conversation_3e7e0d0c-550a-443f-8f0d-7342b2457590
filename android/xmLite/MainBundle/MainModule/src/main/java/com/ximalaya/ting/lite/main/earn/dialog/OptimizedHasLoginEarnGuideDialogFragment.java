package com.ximalaya.ting.lite.main.earn.dialog;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.NormalModeActivity;
import com.ximalaya.ting.android.host.activity.manager.AppModeManager;
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment;
import com.ximalaya.ting.android.host.listener.IHomeDialogCanNextCallback;
import com.ximalaya.ting.android.host.model.earn.HasLoginEarnGuideDataModel;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.view.CircleMaskView;

/**
 * Created by dumingwei on 2021/4/19
 * <p>
 * Desc: 参考HasLoginEarnGuideDialogFragment
 */
public class OptimizedHasLoginEarnGuideDialogFragment extends BaseFullScreenDialogFragment {

    private static final String ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL = "listen_earn_dialog_data_model";

    private static final String TAG = "OptimizedHasLoginEarnGu";

    private boolean mMaskIsShow = false; //解决fragment重复添加crash问题

    //是否跳转了登录页面，跳转了，首页弹屏之后的操作将终止
    private boolean mIsGoLoginPage = false;


    private IHomeDialogCanNextCallback mCanNextCallback;

    //奖励数据载体
    private HasLoginEarnGuideDataModel mDataModel;

    private RelativeLayout rlRootContainer;

    private CircleMaskView ivGuideBanner;
    private ImageView viewClose;

    private ImageView ivDrawCash;
    private AnimatorSet breathAnimator;

    //后面的气泡闪动和移动的动画
    private AnimatorSet lightAnimatorSet;

    private ImageView ivLight1;
    private ImageView ivLight2;

    /**
     * 点击图片的时候，是否要跳转到提现页面，还是直接关闭弹窗
     */
    private boolean shouldJumpToWithDrawPage;

    private RelativeLayout rlRotateY;


    /***小金币平移动画所需要的变量开始***/
    private ImageView ivCenterCoin;
    private final int[] sizeOfIvCenterCoin = new int[2];
    private final int[] locationOfIvCenterCoin = new int[2];
    private final int[] sizeOfTarget = new int[2];
    private final int[] locationOfTarget = new int[2];
    private AnimatorSet ivCenterCoinAnimatorSet;
    /***小金币平移动画所需要的变量结束***/

    private ValueAnimator changeWidthAnimator;


    public static Bundle newArgument(HasLoginEarnGuideDataModel dialogDataModel) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL, dialogDataModel);
        return bundle;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mDataModel = arguments.getParcelable(ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL);
        }
        if (mDataModel == null) {
            mDataModel = new HasLoginEarnGuideDataModel();
        }

        //shouldJumpToWithDrawPage = mDataModel.shouldJumpToWithDrawPage;
        shouldJumpToWithDrawPage = true;

        View view = inflater.inflate(R.layout.main_fra_dialog_optimized_has_login_earn_guide, container, false);

        rlRootContainer = view.findViewById(R.id.main_rl_root_container);

        rlRotateY = view.findViewById(R.id.main_rl_rotate_y);

        ivGuideBanner = view.findViewById(R.id.main_guide_banner);

        int startPx = BaseUtil.dp2px(getContext(), 191);
        ivGuideBanner.setSrcCanvasRadius(startPx);
        ivGuideBanner.setInnerRadius(startPx);

        ivLight1 = view.findViewById(R.id.main_iv_light_1);
        ivLight2 = view.findViewById(R.id.main_iv_light_2);


        ivDrawCash = view.findViewById(R.id.main_iv_draw_cash);

        viewClose = view.findViewById(R.id.main_iv_close);

        ivGuideBanner.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Logger.i(TAG, "onClick");
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                if (!canUpdateUi()) {
                    return;
                }
                FragmentActivity activity = getActivity();
                if (activity == null || activity.isFinishing()) {
                    return;
                }
                if (shouldJumpToWithDrawPage) {
                    Activity mainActivity = BaseApplication.getMainActivity();
                    if (mainActivity instanceof MainActivity) {
                        //跳转到提现页面
                        //ToolUtil.clickUrlAction(((MainActivity) mainActivity), UrlConstants.getInstanse().getWithDrawUrl(), null, v);
//                        if (AppModeManager.isAppModeForNormal()) {
//                            ((MainActivity) mainActivity).switchWelfareTab(null);
//                        } else {
//                            ((MainActivity) mainActivity).toSingleWelfarePage();
//                        }
                        ((MainActivity) mainActivity).switchWelfareTab(null);
                    }
                }
                new XMTraceApi.Trace()
                        .setMetaId(6193)
                        .setServiceId("dialogClick")
                        .put("dialogTitle", "新人奖励")
                        .put("coinCount", mDataModel.awardNumber + "")
                        .put("prizeType", mDataModel.awardType == HasLoginEarnGuideDataModel.TYPE_COIN ? "coin" : "cash")
                        .createTrace();
                //选中福利页面
                mIsGoLoginPage = true;
                dismissAllowingStateLoss();
            }
        });
        viewClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }

                //418dp是遮罩层的的直径，半径是209dp，14dp是中间小金币的半径
                changeWidthAnimator = ValueAnimator.ofInt(startPx, BaseUtil.dp2px(getContext(), 14));
                changeWidthAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        int width = (int) animation.getAnimatedValue();
                        //Logger.i(TAG, "onAnimationUpdate width = " + width);
                        //circleMaskView.setInnerRadius(width);
                        ivGuideBanner.setInnerRadiusAndInValidate(width);
                    }
                });
                changeWidthAnimator.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        //移动小金币动画
                        ivGuideBanner.setVisibility(View.INVISIBLE);
                        rlRootContainer.setBackground(null);
                        ivCenterCoin.setVisibility(View.VISIBLE);

                        ivCenterCoinAnimatorSet = new AnimatorSet();

                        ObjectAnimator rotateYAnimator = ObjectAnimator.ofFloat(ivCenterCoin, "rotationY", 0, 360f);
                        rotateYAnimator.setDuration(400);


                        int translationX = locationOfTarget[0] - locationOfIvCenterCoin[0] + sizeOfTarget[0] / 2 + sizeOfIvCenterCoin[0] / 2;
                        ObjectAnimator centerCoinTranslationXAnimator = ObjectAnimator.ofFloat(ivCenterCoin, "translationX", 0, translationX);
                        ObjectAnimator centerCoinTranslationYAnimator = ObjectAnimator.ofFloat(ivCenterCoin, "translationY", 0, locationOfTarget[1] - locationOfIvCenterCoin[1]);
                        centerCoinTranslationXAnimator.setDuration(500);
                        centerCoinTranslationYAnimator.setDuration(500);


                        Logger.i(TAG, " ivCenterCoin.getTranslationX() = " +
                                ivCenterCoin.getTranslationX() + " ivCenterCoin.getTranslationY() " + ivCenterCoin.getTranslationY());


                        Activity activity = getActivity();
                        if (activity instanceof MainActivity) {

                            int size = ((MainActivity) activity).getMangeFragmentSize();
                            if (size == 0 && AppModeManager.isAppModeForNormal()) {
                                //在主界面，没有其他界面遮挡，有收起的动画
                                ivCenterCoinAnimatorSet.play(centerCoinTranslationXAnimator).with(centerCoinTranslationYAnimator).after(rotateYAnimator);
                            } else {
                                //没有收起动画
                                ivCenterCoinAnimatorSet.play(rotateYAnimator);
                            }
                            ivCenterCoinAnimatorSet.addListener(new AnimatorListenerAdapter() {
                                @Override
                                public void onAnimationEnd(Animator animation) {
                                    dismissAllowingStateLoss();
                                }
                            });
                        }
                        ivCenterCoinAnimatorSet.start();
                    }
                });

                int duration = 400;
                changeWidthAnimator.setDuration(duration);
                changeWidthAnimator.start();

                if (breathAnimator != null) {
                    breathAnimator.cancel();
                }
                ivDrawCash.setVisibility(View.INVISIBLE);

                ObjectAnimator ivCloseAlphaAnimator = ObjectAnimator.ofFloat(viewClose, "alpha", 1f, 0f);
                ivCloseAlphaAnimator.setDuration(duration);
                ivCloseAlphaAnimator.start();
            }
        });

        ivCenterCoin = view.findViewById(R.id.main_iv_center_coin);

        ivCenterCoin.post(new Runnable() {
            @Override
            public void run() {
                sizeOfIvCenterCoin[0] = ivCenterCoin.getMeasuredWidth();
                sizeOfIvCenterCoin[1] = ivCenterCoin.getMeasuredHeight();

                ivCenterCoin.getLocationOnScreen(locationOfIvCenterCoin);

                Activity activity = BaseApplication.getMainActivity();
                if (activity instanceof MainActivity) {
                    NormalModeActivity modeActivity = ((MainActivity) activity).getNormalModeActivity();
                    if (modeActivity != null) {
                        RadioButton radioButton = modeActivity.getRbWelfare();

                        sizeOfTarget[0] = radioButton.getMeasuredWidth();
                        sizeOfTarget[1] = radioButton.getMeasuredHeight();

                        radioButton.getLocationOnScreen(locationOfTarget);
                    }
                }

                Logger.i(TAG, "sizeOfIvCenterCoin[0] = " + sizeOfIvCenterCoin[0] + " sizeOfIvCenterCoin[1] = " + sizeOfIvCenterCoin[1]
                        + " locationOfIvCenterCoin[0] = " + locationOfIvCenterCoin[0] + " locationOfIvCenterCoin[1] = " + locationOfIvCenterCoin[1]);

                Logger.i(TAG, "sizeOfTarget[0] = " + sizeOfTarget[0] + " sizeOfTarget[1] = " + sizeOfTarget[1]
                        + " locationOfTarget[0] = " + locationOfTarget[0] + " locationOfTarget[1] = " + locationOfTarget[1]);

            }
        });

        AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(viewClose, AutoTraceHelper.MODULE_DEFAULT, "");

        new XMTraceApi.Trace()
                .setMetaId(6192)
                .setServiceId("dialogView")
                .put("dialogTitle", "新人奖励")
                .put("coinCount", mDataModel.awardNumber + "")
                .put("prizeType", mDataModel.awardType == HasLoginEarnGuideDataModel.TYPE_COIN ? "coin" : "cash")
                .createTrace();
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        initAnimatorAndStart();

        if (breathAnimator == null) {
            breathAnimator = new AnimatorSet();
        }


        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(ivDrawCash, "scaleX", 1f, 1.06f, 1f);
        scaleXAnimator.setRepeatCount(ValueAnimator.INFINITE);

        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(ivDrawCash, "scaleY", 1f, 1.06f, 1f);
        scaleYAnimator.setRepeatCount(ValueAnimator.INFINITE);

        breathAnimator.setDuration(1000);
        breathAnimator.playTogether(scaleXAnimator, scaleYAnimator);
        breathAnimator.setStartDelay(1000);
        breathAnimator.start();
    }

    private void initAnimatorAndStart() {
        //12490这个数值随便写的。但是一定要是一个比较大的值。
        rlRotateY.setCameraDistance(BaseUtil.dp2px(getContext(), 12490));
        rlRotateY.setRotationY(-180f);
        rlRotateY.animate().rotationY(0f).setDuration(800).setListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                ivLight1.setVisibility(View.VISIBLE);
                ivLight2.setVisibility(View.VISIBLE);

                lightAnimatorSet = new AnimatorSet();
                lightAnimatorSet.setDuration(1200);

                ObjectAnimator ivLight1AlphaAnimator = ObjectAnimator.ofFloat(ivLight1, "alpha", 0f, 1f, 0f, 1f);
                ObjectAnimator ivLight2AlphaAnimator = ObjectAnimator.ofFloat(ivLight2, "alpha", 1f, 0f, 1f, 0f);

                ObjectAnimator ivLight1TranslationAnimator = ObjectAnimator.ofFloat(ivLight1, "translationY", 0f, -240f);
                ObjectAnimator ivLight2TranslationAnimator = ObjectAnimator.ofFloat(ivLight2, "translationY", 1f, -240f);

                lightAnimatorSet.playTogether(ivLight1AlphaAnimator, ivLight2AlphaAnimator, ivLight1TranslationAnimator, ivLight2TranslationAnimator);

                lightAnimatorSet.start();

                lightAnimatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        ivLight1.setVisibility(View.GONE);
                        ivLight2.setVisibility(View.GONE);
                    }
                });
            }
        }).start();

    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mMaskIsShow = false;
        //屏幕消失
        if (!mIsGoLoginPage) {
            //没有执行跳转登录操作，首页之后的操作可以接着进行
            if (mCanNextCallback != null) {
                mCanNextCallback.canShowNext();
            }
        }
        mIsGoLoginPage = false;
        if (breathAnimator != null) {
            breathAnimator.cancel();
        }
    }

    public boolean isShowing() {
        return mMaskIsShow;
    }

    @Override
    public int show(FragmentTransaction transaction, String tag) {
        if (mMaskIsShow) {
            return 0;
        }
        mMaskIsShow = true;
        return super.show(transaction, tag);
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        if (mMaskIsShow) {
            return;
        }
        mMaskIsShow = true;
        super.show(manager, tag);
    }

    /**
     * 是否可以接着执行之后的弹框
     */
    public void setCanNextCallback(IHomeDialogCanNextCallback canNextCallback) {
        this.mCanNextCallback = canNextCallback;
    }

}
