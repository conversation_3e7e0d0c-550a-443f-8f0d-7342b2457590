package com.ximalaya.ting.lite.main.shortplay

import android.content.DialogInterface
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.FrameLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.handmark.pulltorefresh.library.PullToRefreshBase
import com.handmark.pulltorefresh.library.PullToRefreshRecyclerView
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.view.SlideView
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.manager.ShortPlayTrackManager
import com.ximalaya.ting.android.host.model.album.AlbumVideoInfoModel
import com.ximalaya.ting.android.host.model.duanju.XmDuanJuItemTransferModel
import com.ximalaya.ting.android.host.view.TopSlideView2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.shortplay.adapter.XVideoPlayListDialogAdapterNew
import com.ximalaya.ting.lite.main.tab.presenter.XVideoPlayListPresenter

/**
 * 竖屏非短剧的播放列表
 */
class XVideoPlayListDialogFragmentNew(
    private val mVideoInfo: AlbumVideoInfoModel.AlbumVideoInfo?,
    private val mPresenter: XVideoPlayListPresenter?,
    private val xmDuanJuItemTransferModel: XmDuanJuItemTransferModel?,
    private val playVideoId: Long,
    private val callback: Callback
) :
    BaseDialogFragment<XVideoPlayListDialogFragmentNew>(), XVideoPlayListPresenter.Listener {
    private var mDismissListener: DialogInterface.OnDismissListener? = null
    private var mRvVideos: RecyclerView? = null
    private val mAdapter = XVideoPlayListDialogAdapterNew(this::handleOnItemClick)
    private var mPullToRefreshRecyclerView: PullToRefreshRecyclerView? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        configureDialogStyle()
        return inflater.inflate(R.layout.main_fra_dialog_video_play_list_new, container, false)
    }

    private fun configureDialogStyle() {
        val dialog = dialog ?: return
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.let {
            it.setWindowAnimations(R.style.host_popup_window_from_bottom_animation)
            it.setBackgroundDrawableResource(R.color.main_transparent)
            if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
                it.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
                var systemUiVisibility = it.decorView.systemUiVisibility
                systemUiVisibility = systemUiVisibility or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                it.decorView.systemUiVisibility = systemUiVisibility
                it.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                it.statusBarColor = Color.TRANSPARENT
            }
        }
    }

    override fun onStart() {
        super.onStart()
        val uiOptions = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            or View.SYSTEM_UI_FLAG_FULLSCREEN)
        dialog?.window?.let {
            it.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            val windowParams = it.attributes
            windowParams.gravity = Gravity.RIGHT
            it.setDimAmount(0f)
            it.attributes = windowParams
            it.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            StatusBarManager.hideStatusBar(it, true)
            it.decorView.setSystemUiVisibility(uiOptions)
        }
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        init()
    }

    private fun isCalSlide(): Boolean {
        if (mPresenter == null) {
            return false
        }

        return !mPresenter.canPrev()
    }

    private fun init() {
        mPresenter?.addListener(this)
        val data: List<AlbumVideoInfoModel.AlbumVideoInfo> = mPresenter?.list ?: emptyList()
        mAdapter.videoList.addAll(data)

        val topSlideView = findViewById(R.id.top_slide_view) as TopSlideView2
        topSlideView.isEnableScrolling = isCalSlide()
        mPresenter?.headPageChangeCallback =
            object : XVideoPlayListPresenter.HeadPageChangeCallback {
                override fun onChange(page: Int) {
                    topSlideView.isEnableScrolling = isCalSlide()
                }
            }
        findViewById(R.id.root_view).setOnClickListener {
            dismiss()
        }
        val llContent = findViewById(R.id.ll_content)
        val prams = llContent.layoutParams
        if (prams is FrameLayout.LayoutParams) {
            prams.height = (BaseUtil.getScreenHeight(context) * 0.7f).toInt()
            llContent.layoutParams = prams
        }

        mPullToRefreshRecyclerView = findViewById(R.id.main_rv_videos) as? PullToRefreshRecyclerView

        topSlideView.setInnerScrollView(mPullToRefreshRecyclerView?.refreshableView)
        topSlideView.setSlideListener(object : SlideView.SlideListener {
            override fun slideStart() {
            }

            override fun slideEnd() {
                dismiss()
            }

            override fun keepFragment() {
            }

        })

        mPullToRefreshRecyclerView?.let {
            it.mode = PullToRefreshBase.Mode.PULL_FROM_START
            it.isHasMore = true
            it.setOnRefreshLoadMoreListener(object :
                PullToRefreshRecyclerView.IRefreshLoadMoreListener {
                override fun onRefresh() {
                    mPresenter?.loadPrev()
                }

                override fun onMore() {
                    mPresenter?.loadNext()
                }
            })
            mRvVideos = it.refreshableView
        }

        mRvVideos?.let {
            it.layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            mAdapter.setCurVideoId(playVideoId)
            it.adapter = mAdapter
        }
        mRvVideos?.post {
            val index = data.indexOfFirst { it.trackId == playVideoId }
            if (index >= 0) {
                mRvVideos?.scrollToPosition(index)
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        mDismissListener?.onDismiss(dialog)
    }

    private fun handleOnItemClick(position: Int, download: Boolean) {
        val videoInfo = mAdapter.videoList.getOrNull(position) ?: return
        this.callback.clickVideoItem(videoInfo)
        dismiss()
    }

    private fun finishLoadingState() {
        mPullToRefreshRecyclerView?.post {
            mPullToRefreshRecyclerView?.onRefreshComplete()
            mPullToRefreshRecyclerView?.finishLoadingMore()
        }
    }

    fun setOnDismissListener(dismissListener: DialogInterface.OnDismissListener) {
        mDismissListener = dismissListener
    }

    override fun onNextLoaded(list: List<AlbumVideoInfoModel.AlbumVideoInfo>?, recommend: Boolean) {
        if (activity == null || requireActivity().isFinishing) {
            return
        }

        list?.let {
            mAdapter.videoList.addAll(it)
            mAdapter.notifyDataSetChanged()
        }
        finishLoadingState()
    }

    override fun onPrevLoaded(list: List<AlbumVideoInfoModel.AlbumVideoInfo>?) {
        list?.let {
            mAdapter.videoList.addAll(0, it)
            mAdapter.notifyDataSetChanged()
        }
        finishLoadingState()
    }

    override fun onNotifyAdapter() {

    }

    override fun onGoNext(trackId: Long) {
    }

    interface Callback {
        fun clickVideoItem(video: AlbumVideoInfoModel.AlbumVideoInfo)
    }

    override fun onResume() {
        super.onResume()
        mVideoInfo?.apply {
            ShortPlayTrackManager.xmTraceShowDramaGallery(mVideoInfo,xmDuanJuItemTransferModel)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mPresenter?.headPageChangeCallback = null
    }
}