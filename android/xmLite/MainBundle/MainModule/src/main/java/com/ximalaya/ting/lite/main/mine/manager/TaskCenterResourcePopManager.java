package com.ximalaya.ting.lite.main.mine.manager;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.listenertask.ListenTaskUtilForPlayProcess;
import com.ximalaya.ting.android.host.model.earn.CommonPopupDialogDataModel;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.lite.main.earn.dialog.CommonPopupDialogFragment;
import com.ximalaya.ting.lite.main.model.mine.TaskCenterPopResourceModel;
import com.ximalaya.ting.lite.main.model.mine.TaskCenterPopResourceRep;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by qinhuifeng on 2019-11-12
 *
 * <AUTHOR>
 */
public class TaskCenterResourcePopManager {

    private static final String SP_KEY_TASK_CENTER_POP_NEXT_SHOW_TS = "sp_key_task_center_pop_next_show_ts";
    private static final String SP_KEY_TASK_CENTER_POP_SHOW_DAY = "sp_key_task_center_pop_can_next_show_day";
    private static final String SP_KEY_TASK_CENTER_POP_SHOW_NUMBER = "sp_key_task_center_pop_show_number";

    public static void checkAndShowDialog(final FragmentActivity fragmentActivity) {
        if (fragmentActivity == null) {
            return;
        }
        final SharedPreferencesUtil preferencesUtil = SharedPreferencesUtil.getInstance(fragmentActivity);
        String currentDay = ListenTaskUtilForPlayProcess.getInstance().formatDateForMillis(System.currentTimeMillis());
        String saveDay = preferencesUtil.getString(SP_KEY_TASK_CENTER_POP_SHOW_DAY);
        FuliLogger.log("我页网赚--日期save:" + saveDay);
        FuliLogger.log("我页网赚--日期curr:" + currentDay);
        if (saveDay == null) {
            saveDay = "";
        }
        //saveLong是异步操作，提前取出来进行处理
        long nextCanShowTs = preferencesUtil.getLong(SP_KEY_TASK_CENTER_POP_NEXT_SHOW_TS, 0);
        //日期不一致，清除当天展示次数
        if (!saveDay.equals(currentDay)) {
            FuliLogger.log("我页网赚--不一致清除保存的次数:重置日期");
            //重置展示的次数
            preferencesUtil.saveInt(SP_KEY_TASK_CENTER_POP_SHOW_NUMBER, 0);
            //重置当前的日期
            preferencesUtil.saveString(SP_KEY_TASK_CENTER_POP_SHOW_DAY, currentDay);
            //重置下次展示的时间
            preferencesUtil.saveLong(SP_KEY_TASK_CENTER_POP_NEXT_SHOW_TS, 0);
            //saveLong是异步操作，下次直接获取可能，变量重置
            nextCanShowTs = 0;
        }

        FuliLogger.log("我页网赚--:nextTs=" + nextCanShowTs);
        FuliLogger.log("我页网赚--:currTs=" + System.currentTimeMillis());
        //没有达到时间，不能弹出，不用发起请求
        if (System.currentTimeMillis() < nextCanShowTs) {
            FuliLogger.log("我页网赚--:没有达到展示的时间return");
            return;
        }
        FuliLogger.log("我页网赚--发起请求");
        Map<String, String> params = new HashMap<>();
        LiteCommonRequest.getTaskCenterConfigPopResource(params, new IDataCallBack<TaskCenterPopResourceRep>() {
            @Override
            public void onSuccess(@Nullable TaskCenterPopResourceRep object) {
                if (object == null) {
                    return;
                }
                //间隔小时
                if (object.popInterval <= 0) {
                    return;
                }
                List<TaskCenterPopResourceModel> resources = object.resources;
                if (resources == null || resources.size() == 0) {
                    return;
                }
                TaskCenterPopResourceModel resourceModel = resources.get(0);
                if (resourceModel == null) {
                    return;
                }
                if (!resourceModel.checkParams()) {
                    return;
                }
                int showTimesOneDay = resourceModel.showTimesOneDay;
                if (showTimesOneDay <= 0) {
                    return;
                }
                //展示的间隔,单位小时
                //当天展示的次数已经到达上限
                int currentHasShowNumber = preferencesUtil.getInt(SP_KEY_TASK_CENTER_POP_SHOW_NUMBER, 0);
                FuliLogger.log("我页网赚--当前已经展示的次数==" + currentHasShowNumber);
                FuliLogger.log("我页网赚--最大可展展示的次数==" + resourceModel.showTimesOneDay);

                if (currentHasShowNumber >= resourceModel.showTimesOneDay) {
                    return;
                }
                //保存下次可弹出的时间戳，showTimesOneDay每天可弹出几次来计算弹出间隔
                long nextCanShowTs = System.currentTimeMillis() + object.popInterval * 60000;
                preferencesUtil.saveLong(SP_KEY_TASK_CENTER_POP_NEXT_SHOW_TS, nextCanShowTs);
                //保存展示的次数
                preferencesUtil.saveInt(SP_KEY_TASK_CENTER_POP_SHOW_NUMBER, (currentHasShowNumber + 1));

                //弹出弹框，展示
                CommonPopupDialogDataModel dataModel = new CommonPopupDialogDataModel(CommonPopupDialogDataModel.FROM_TASK_CENTER_POP_RESOURCE);
                dataModel.imageUrl = resourceModel.image;
                dataModel.linkUrl = resourceModel.targetUrl;
                CommonPopupDialogFragment fragment = new CommonPopupDialogFragment();
                Bundle bundle = CommonPopupDialogFragment.newArgument(dataModel);
                fragment.setArguments(bundle);
                fragment.show(fragmentActivity.getSupportFragmentManager(), "");
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }
}
