package com.ximalaya.ting.lite.main.home.adapter

import android.graphics.Color
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import com.ximalaya.ting.android.framework.adapter.HolderAdapter
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.track.AlbumSubscribeTsManager
import com.ximalaya.ting.android.host.view.other.NoScrollViewPager
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain
import com.ximalaya.ting.lite.main.home.fragment.HomeItemMySubscriptionFragment
import com.ximalaya.ting.lite.main.home.fragment.HomeItemPlayHistoryFragment
import com.ximalaya.ting.lite.main.home.manager.HomeRecommendAdapterAddFloorManager
import com.ximalaya.ting.lite.main.home.presenter.HomeRecommendContact
import com.ximalaya.ting.lite.main.home.viewmodel.HomeSubscribeHistoryViewModel
import com.ximalaya.ting.lite.main.home.viewmodel.HomeSubscribePageViewModel
import com.ximalaya.ting.lite.main.mylisten.view.AllHistoryFragment

/**
 * Created by qinhuifeng on 2021/1/12
 *
 * 我听和最近收听楼层
 *
 * <AUTHOR>
 */
class HomeSubscriptionProvider(val baseFragment2: BaseFragment2, private val fragmentView: HomeRecommendContact.IFragmentView, val extraDataProvider: HomeRecommedExtraDataProvider) : IMulitViewTypeViewAndData<HomeSubscriptionProvider.Holder, HomeSubscribeHistoryViewModel> {

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup?): View {
        return layoutInflater.inflate(R.layout.main_item_my_subscription_floor, null)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView, baseFragment2, fragmentView, extraDataProvider)
    }

    override fun bindViewDatas(holder: Holder?, t: ItemModel<HomeSubscribeHistoryViewModel>?, convertView: View?, position: Int) {

        FuliLogger.log("订阅历史模块==:更新整个模块bindViewDatas--all")

        if (holder == null || t == null) {
            FuliLogger.log("订阅历史模块==:更新整个模块bindViewDatas--error")
            return
        }
        val homeSubscribeHistoryViewModel: HomeSubscribeHistoryViewModel = t.getObject();
        //更新订阅fragment，禁止更新view以及选中指定页面
        holder.updateMySubscriptionFragment(homeSubscribeHistoryViewModel)
        //更新播放历史界面
        holder.updatePlayHistoryFragment(homeSubscribeHistoryViewModel)
        //判断是否隐藏和占位图和首次展示占位图
        holder.checkHideProImageAndSwitchTab(homeSubscribeHistoryViewModel)
        //更新右上角文案
        if (holder.viewPage.currentItem == holder.POSITION_HISTORY) {
            //当前在历史页面
            holder.rightAllCountText.text = "全部"
        } else {
            //当前在订阅页面，跳转到订阅
            val hasSubscribeCount = if (homeSubscribeHistoryViewModel.subscribePageViewModel != null && homeSubscribeHistoryViewModel.subscribePageViewModel != null) {
                homeSubscribeHistoryViewModel.subscribePageViewModel.subscribeCount
            } else {
                0
            }
            holder.rightAllCountText.text = "全部订阅(${hasSubscribeCount})"
        }

        holder.viewPage.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(state: Int) {

            }

            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

            }

            override fun onPageSelected(position: Int) {
                if (holder.viewPage.currentItem == holder.POSITION_HISTORY) {
                    //当前在历史页面
                    holder.rightAllCountText.text = "全部"

                    //选中历史tab
                    holder.switchTab(holder.POSITION_HISTORY)
                } else {
                    //当前在订阅页面，跳转到订阅
                    val hasSubscribeCount = if (homeSubscribeHistoryViewModel.subscribePageViewModel != null) {
                        homeSubscribeHistoryViewModel.subscribePageViewModel.subscribeCount
                    } else {
                        0
                    }
                    holder.rightAllCountText.text = "全部订阅(${hasSubscribeCount})"

                    //选中订阅tab
                    holder.switchTab(holder.POSITION_SUBSCRIPTION)
                }
            }
        })
    }

    class Holder(convertView: View, val baseFragment2: BaseFragment2, val iFragmentView: HomeRecommendContact.IFragmentView, private val extraDataProvider: HomeRecommedExtraDataProvider) : HolderAdapter.BaseViewHolder() {
        var viewPage: NoScrollViewPager
        var rightAllCountAllLayout: LinearLayout
        var rightAllCountText: TextView
        var tvTabSubscription: TextView
        var tvTabHistory: TextView

        //展位图布局
        var mProImageLayout: ViewGroup
        private val mViewPageAdapter: SubscriptionHistoryTabViewPageAdapter;
        private val mFragmentList = arrayListOf<Fragment>()
        private val mTitlesList = arrayListOf("订阅", "最近播放")
        private val mMySubscriptionFragment: HomeItemMySubscriptionFragment = HomeItemMySubscriptionFragment.newInstance(extraDataProvider.from)
        private val mPlayHistoryFragment: HomeItemPlayHistoryFragment = HomeItemPlayHistoryFragment()
        val POSITION_SUBSCRIPTION = 0
        val POSITION_HISTORY = 1

        init {
            FuliLogger.log("订阅历史模块==:更新订阅列表==Holder==start")
            mProImageLayout = convertView.findViewById(R.id.main_layout_pro_image)
            tvTabSubscription = convertView.findViewById(R.id.main_tab_subscription)
            tvTabHistory = convertView.findViewById(R.id.main_tab_history)
            rightAllCountAllLayout = convertView.findViewById(R.id.main_layout_right_all_count)
            rightAllCountText = convertView.findViewById(R.id.main_right_all_count)
            viewPage = convertView.findViewById(R.id.main_view_page_subscription_history)
            viewPage.setNoScroll(true)

            //初始化操作，只允许在创建的时候初始化1次
            mFragmentList.clear()
            mFragmentList.add(mMySubscriptionFragment);
            mFragmentList.add(mPlayHistoryFragment);
            mViewPageAdapter = SubscriptionHistoryTabViewPageAdapter(baseFragment2.childFragmentManager, mFragmentList, mTitlesList);
            viewPage.adapter = mViewPageAdapter
            //多设置1个缓存，注意在buildHolder的时候不能随意调用setCurrentItem，缓存如果够用可以调用
            //只要触发重建会出现崩溃，此时还未添加到界面，使用fragment findViewById是找不到的
            viewPage.offscreenPageLimit = mFragmentList.size

            //订阅被点击
            tvTabSubscription.setOnClickListener(object : View.OnClickListener {
                override fun onClick(v: View?) {
                    viewPage.currentItem = POSITION_SUBSCRIPTION

                    switchTab(POSITION_SUBSCRIPTION)
                }
            })

            //订阅历史被点击
            tvTabHistory.setOnClickListener(object : View.OnClickListener {
                override fun onClick(v: View?) {
                    viewPage.currentItem = POSITION_HISTORY
                    switchTab(POSITION_HISTORY)
                }
            })

            viewPage.currentItem = 0

            //设置右上角跳转监听
            rightAllCountAllLayout.setOnClickListener(object : View.OnClickListener {
                override fun onClick(v: View?) {
                    if (viewPage.currentItem == POSITION_HISTORY) {
                        //当前在历史页面，全部跳转到历史
                        baseFragment2.startFragment(AllHistoryFragment.newInstance(true, false, true))
                    } else {
                        //当前在订阅页面，跳转到订阅
                        if (baseFragment2.activity is MainActivity) {
                            (baseFragment2.activity as MainActivity).switchTingProgramTab(null)
                        }
                    }
                }
            })
            mMySubscriptionFragment.setOnScrollListLastListener(object : HomeItemMySubscriptionFragment.OnScrollListLastListener {
                override fun onScrollLastLast() {
                    val homeRecommendAdapterAddFloorManager: HomeRecommendAdapterAddFloorManager? = iFragmentView.homeRecommendAdapterAddFloorManager
                    //请求订阅下一页数据
                    homeRecommendAdapterAddFloorManager?.subscribeFloorPresenter?.loadNextPageRecommend()
                }
            })
            FuliLogger.log("订阅历史模块==:更新订阅列表==Holder==end")
        }

        fun switchTab(position: Int) {
            if (position == POSITION_HISTORY) {
                //当前在历史页面
                tvTabSubscription.textSize = 18F
                tvTabSubscription.setTextColor(Color.parseColor("#999999"))
                tvTabSubscription.typeface = Typeface.defaultFromStyle(Typeface.NORMAL);

                //加粗展示
                tvTabHistory.textSize = 18F
                tvTabHistory.setTextColor(Color.parseColor("#333333"))
                val familyName = "sans-serif-light"
                val boldTypeface = Typeface.create(familyName, Typeface.BOLD)
                tvTabHistory.typeface = boldTypeface
            } else {
                tvTabSubscription.textSize = 18F
                tvTabSubscription.setTextColor(Color.parseColor("#333333"))
                val familyName = "sans-serif-light"
                val boldTypeface = Typeface.create(familyName, Typeface.BOLD)
                tvTabSubscription.typeface = boldTypeface

                tvTabHistory.textSize = 18F
                tvTabHistory.setTextColor(Color.parseColor("#999999"))
                tvTabHistory.typeface = Typeface.defaultFromStyle(Typeface.NORMAL);
            }
        }

        /**
         *
         * 禁止调用更新方法
         *
         * 更新订阅
         */
        fun updateMySubscriptionFragment(pageViewModel: HomeSubscribeHistoryViewModel) {
            FuliLogger.log("订阅历史模块==:更新订阅列表==updateMySubscriptionFragment")
            if (mMySubscriptionFragment == null) {
                FuliLogger.log("订阅历史模块==:更新订阅列表==error==frgament还没创建")
                return
            }
            //如果为空也要展示添加按钮和更多按钮
            if (pageViewModel.subscribePageViewModel == null) {
                pageViewModel.subscribePageViewModel = HomeSubscribePageViewModel()
            }
            mMySubscriptionFragment.updateMySubscriptionFragmentList(pageViewModel.subscribePageViewModel.build())
        }

        /**
         * 更新播放历史，禁止调用viewPage方法
         */
        fun updatePlayHistoryFragment(pageViewModel: HomeSubscribeHistoryViewModel) {
            FuliLogger.log("订阅历史模块==:更新订阅列表==History")
            if (mPlayHistoryFragment == null) {
                FuliLogger.log("订阅历史模块==:更新订阅列表==error==History")
                return
            }
            mPlayHistoryFragment.updatePlayHistoryFragmentList(pageViewModel.historyPageViewModel)
        }

        /**
         * 多设置1个缓存，注意在buildHolder的时候不能随意调用setCurrentItem，缓存如果够用可以调用
         * 只要触发重建会出现崩溃，此时还未添加到界面，使用fragment findViewById是找不到的
         */
        fun checkHideProImageAndSwitchTab(viewModel: HomeSubscribeHistoryViewModel) {
            if (mProImageLayout.visibility == View.GONE) {
                return
            }
            if (!viewModel.initRequestFinish) {
                return
            }
            //隐藏占位图
            mProImageLayout.visibility = View.GONE

            //添加try保护，防止重建崩溃
            try {
                //多设置1个缓存，注意在buildHolder的时候不能随意调用setCurrentItem，缓存如果够用可以调用
                //只要触发重建会出现崩溃，此时还未添加到界面，使用fragment findViewById是找不到的
                //选中默认的tab
                val defSwitchPosition = getDefSwitchPosition(viewModel)
                viewPage.setCurrentItem(defSwitchPosition, false)
                switchTab(defSwitchPosition)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }


        private fun getDefSwitchPosition(viewModel: HomeSubscribeHistoryViewModel): Int {
            //选中默认的tab，只选中1次
            //历史是否为0
            var isHistoryCountEmpty = isHistoryCountEmpty(viewModel);
            //两天内是否有订阅
            var isHasSubscribeIn2Day = isHasSubscribeIn2Day(viewModel)
            //最近一条播放历史是否是5天内产生的
            var isFirstHistoryIn5Day = isFirstHistoryIn5Day(viewModel);
            //是否来源是首页
            var isFromHome = isFromHome()

            //历史是0，选中订阅
            if (isHistoryCountEmpty) {
                return POSITION_SUBSCRIPTION
            }

            //已订阅里有近两天订阅的专辑，选中订阅
            if (isHasSubscribeIn2Day) {
                return POSITION_SUBSCRIPTION
            }

            //最近一条历史是5天内产生的，选中历史
            if (isFirstHistoryIn5Day) {
                return POSITION_HISTORY
            }

            //是否在听节目页，首页，选中订阅
            if (isFromHome) {
                return POSITION_SUBSCRIPTION
            }

            //其他选中历史
            return POSITION_HISTORY
        }

        /**
         * 订阅列表是否存在2天内订阅的专辑
         */
        private fun isHasSubscribeIn2Day(viewModel: HomeSubscribeHistoryViewModel): Boolean {
            if (viewModel.subscribePageViewModel == null) {
                return false;
            }
            val subscriptionItemModelList = viewModel.subscribePageViewModel.subscriptionItemModelList
            if (subscriptionItemModelList == null || subscriptionItemModelList.size == 0) {
                return false
            }
            for (value in subscriptionItemModelList) {
                if (value.album != null) {
                    val albumSubscribeTs = AlbumSubscribeTsManager.getAlbumSubscribeTs(value.album.id)
                    //在2天内订阅的专辑
                    if (albumSubscribeTs > 0 && (System.currentTimeMillis() - albumSubscribeTs <= 172800000L)) {
                        return true
                    }
                }
            }
            return false
        }

        /**
         * 播放历史是否为空
         */
        private fun isHistoryCountEmpty(viewModel: HomeSubscribeHistoryViewModel) = viewModel.historyPageViewModel == null || viewModel.historyPageViewModel.historyViewModelList == null || viewModel.historyPageViewModel.historyViewModelList.size == 0


        /**
         * 是否来源于首页
         */
        private fun isFromHome() = extraDataProvider.from != BundleValueConstantsInMain.FROM_TING_BOOK

        /**
         * 最近一条播放历史是否在5天内产生的
         */
        private fun isFirstHistoryIn5Day(viewModel: HomeSubscribeHistoryViewModel): Boolean {
            if (viewModel.historyPageViewModel == null || viewModel.historyPageViewModel.historyViewModelList == null || viewModel.historyPageViewModel.historyViewModelList.size == 0) {
                return false
            }
            val firstHistory = viewModel.historyPageViewModel.historyViewModelList[0]
            if (firstHistory == null || firstHistory.historyViewModel == null) {
                return false
            }
            if (firstHistory.historyViewModel.endedAt <= 0) {
                return false
            }
            //上次收听时间在5天内
            if (System.currentTimeMillis() - firstHistory.historyViewModel.endedAt < 432000000L) {
                return true
            }
            return false
        }
    }
}