package com.ximalaya.ting.lite.main.download;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.host.adapter.MyFragmentStatePagerAdapter;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;

import java.util.Locale;

/**
 * <AUTHOR> on 2018/3/26.
 */

public class DownloadAlbumDetailTabAdapter extends MyFragmentStatePagerAdapter {
    private long mAlbumId;
    private long mAlbumUid;
    private int mTrackDownloadCount;
    private int mVideoDownloadCount;
    private boolean mPlayFirst;

    public DownloadAlbumDetailTabAdapter(FragmentManager fm, long albumId, long albumUid, boolean playFirst) {
        super(fm);
        mAlbumId = albumId;
        mAlbumUid = albumUid;
        mPlayFirst = playFirst;
    }

    @Override
    public int getCount() {
        if (mVideoDownloadCount <= 0) { //无下载视频时不显示视频tab
            return 1;
        }

        return 2;
    }

    @Override
    public Fragment getItem(int position) {
        Fragment fragment = null;

        if(position == 0){
            fragment = DownloadedAlbumTrackListFragment.newInstance(mAlbumId, mAlbumUid);

            Bundle arguments = fragment.getArguments();
            if(arguments == null) {
               arguments = new Bundle();
               fragment.setArguments(arguments);
            }

            arguments.putBoolean(BundleKeyConstants.KEY_PLAY_FIRST ,mPlayFirst);
        }

        return fragment;
    }

    @Override
    public CharSequence getPageTitle(int position) {
        if(position == 0){
            return String.format(Locale.US, "声音(%d)", mTrackDownloadCount);
        }else if(position == 1){
            return String.format(Locale.US, "视频(%d)", mVideoDownloadCount);
        }

        return "";
    }

    /**
     * 更新声音下载数和视频下载数
     */
    public void updateDownloadCountView(int trackDownloadCount, int videoDownloadCount) {
        mTrackDownloadCount = trackDownloadCount;
        mVideoDownloadCount = videoDownloadCount;
    }
}
