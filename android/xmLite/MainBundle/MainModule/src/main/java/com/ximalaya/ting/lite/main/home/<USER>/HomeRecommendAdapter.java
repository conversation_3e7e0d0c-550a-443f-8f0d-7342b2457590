package com.ximalaya.ting.lite.main.home.adapter;

import androidx.collection.ArrayMap;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;
import com.ximalaya.ting.lite.main.home.presenter.HomeRecommendContact;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeAlbumRankFloorViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel;
import com.ximalaya.ting.lite.main.model.album.CategoryRecommendRefresh;
import com.ximalaya.ting.lite.main.model.album.HomeAlbumRankItem;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 发现-分类 具体分类下的推荐数据适配器
 *
 * <AUTHOR>
 */
public class HomeRecommendAdapter extends BaseAdapter implements View.OnClickListener, HomeRecommendRefreshProvider.IRefreshContext, HomeRecommedExtraDataProvider {

    /*****type 添加 start--注意：禁止在中间添加，只能在最后的type后面自增，可视化埋点在使用这个type作为唯一路径*****************************************************/
    private static int VIEW_TYPE_BASE = 0;
    private static final int VIEW_TYPE_NONE = VIEW_TYPE_BASE;
    public static final int VIEW_TYPE_TITLE_NORMAL = VIEW_TYPE_BASE++;//title(titleName  更多)
    public static final int VIEW_TYPE_TITLE_NORMAL_V2 = VIEW_TYPE_BASE++;//title(titleName，更多，带副标题
    public static final int VIEW_TYPE_ALBUM_VERTICAL = VIEW_TYPE_BASE++;//垂直排列专辑item
    public static final int VIEW_TYPE_REFRESH = VIEW_TYPE_BASE++; //换一批
    public static final int VIEW_TYPE_ALBUM_HORIZONT = VIEW_TYPE_BASE++;//水平排列专辑item
    public static final int VIEW_TYPE_CALABASH_LINE = VIEW_TYPE_BASE++;//首页分类糖葫芦
    public static final int VIEW_TYPE_CATEGORY_RANK_ONEKEY = VIEW_TYPE_BASE++;//分类页-排行榜和一键听
    public static final int VIEW_TYPE_FEED_TRACK = VIEW_TYPE_BASE++; // 信息流中的声音条
    public static final int VIEW_TYPE_FEED_ALBUM = VIEW_TYPE_BASE++; // 信息流中的专辑条
    public static final int VIEW_TYPE_ONEKEY_LISTENER = VIEW_TYPE_BASE++; // 信息流中的专辑条
    //不再使用的类型禁止删除，禁止移动位置，可视化埋点需要保证位置
    @Deprecated
    public static final int VIEW_TYPE_AD_SELF_RANDING_SMALL = VIEW_TYPE_BASE++; // 信息流中的自渲染小尺寸广告
    //不再使用的类型禁止删除，禁止移动位置，可视化埋点需要保证位置
    @Deprecated
    public static final int VIEW_TYPE_AD_SELF_RANDING_BIG = VIEW_TYPE_BASE++; // 信息流中的自渲染大尺寸广告
    public final static int VIEW_TYPE_REAL_TIME_RECOMMEND_STRONG_MODULE = VIEW_TYPE_BASE++; // 实时推荐强露出模块
    public final static int VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION = VIEW_TYPE_BASE++; // 信息流激励视频广告
    public final static int VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3 = VIEW_TYPE_BASE++; // 信息流激励视频广告样式3
    public final static int VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4 = VIEW_TYPE_BASE++; // 信息流激励视频广告样式3
    public final static int VIEW_TYPE_ONE_KEY_RADIO = VIEW_TYPE_BASE++; // 一键听电台
    public final static int VIEW_TYPE_ONE_KEY_RADIO_SETTING = VIEW_TYPE_BASE++; // 一键听电台兴趣设置入口
    public final static int VIEW_TYPE_VIP_TOP_REGION = VIEW_TYPE_BASE++;//VIP精选顶部的VIP区域
    public final static int VIEW_TYPE_VIP_BAR = VIEW_TYPE_BASE++;//VIP信息条
    public final static int VIEW_TYPE_VIP_FOCUS_IMAGE = VIEW_TYPE_BASE++;//VIP焦点图
    public final static int VIEW_TYPE_TANGHULU_FLOAT_HOT_WORD = VIEW_TYPE_BASE++;//热词糖葫芦，cardClass = float
    public final static int VIEW_TYPE_TANGHULU_NORMAL_HOT_WORD = VIEW_TYPE_BASE++;//热词糖葫芦，cardClass = normal
    public final static int VIEW_TYPE_VIP_HOT_WORD_ALBUM = VIEW_TYPE_BASE++;//优化的热词糖葫芦下面的专辑数据
    public final static int VIEW_TYPE_TANGHULU_FEED_ALBUM = VIEW_TYPE_BASE++;//优化的热词糖葫芦下面的专辑数据
    public final static int VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_ALBUM = VIEW_TYPE_BASE++;//横向滑动单行专辑模块
    public final static int VIEW_TYPE_HORIZONTAL_SCROLL_THREE_LINE_ALBUM = VIEW_TYPE_BASE++;//横向滑动三行专辑模块
    public final static int VIEW_TYPE_NET_OPERATE = VIEW_TYPE_BASE++;//网页运营
    public final static int VIEW_TYPE_ALBUM_OPERATE = VIEW_TYPE_BASE++;//专辑运营
    public final static int VIEW_TYPE_HOT_SEARCH_RANK = VIEW_TYPE_BASE++;//热搜榜
    public final static int VIEW_TYPE_INTEREST_RECOMMEND_REGION = VIEW_TYPE_BASE++;//兴趣推荐区
    public final static int VIEW_TYPE_REPLENISH_SEARCH_REGION = VIEW_TYPE_BASE++;//补充搜索区
    public final static int VIEW_TYPE_FLEX_BOX = VIEW_TYPE_BASE++;//补充搜索区
    /**
     * 这个并不是正常的数据类型
     */
    public final static int VIEW_TYPE_LIST_VIEW_NO_CONTENT = VIEW_TYPE_BASE++;
    public static final int VIEW_TYPE_FEED_TRACK_STYLE_V2 = VIEW_TYPE_BASE++; // 信息流中的声音条styleV2版本
    public static final int VIEW_TYPE_FEED_ALBUM_STYLE_V2 = VIEW_TYPE_BASE++; // 信息流中的专辑条,styleV2版本
    public static final int VIEW_TYPE_FEED_ALBUM_STYLE_V3 = VIEW_TYPE_BASE++; // 信息流中的专辑条,styleV3,专辑+声音
    public static final int VIEW_TYPE_TING_UPDATE = VIEW_TYPE_BASE++; //听更新
    public static final int VIEW_TYPE_TANGHULU_HOME_V2 = VIEW_TYPE_BASE++;//首页分类糖葫芦V2版本，可滑动
    public static final int VIEW_TYPE_MY_SUBSCRIPTION = VIEW_TYPE_BASE++;//我的订阅
    public static final int VIEW_TYPE_FEED_ALBUM_STYLE_V4 = VIEW_TYPE_BASE++; // 信息流中的专辑条,styleV4版本，带评分
    //因为嵌套viewpager的样式只允许缓存一个，viewpager只允许初始化一次，后续都不允许再次做其变更成操作
    //此处添加3个相同的type，支持同一个页面添加3个排行榜楼层，支持一个页面多个type
    //如果需要判断排行榜楼层，需要这几个类型全部都判断
    public static final int VIEW_TYPE_ALBUM_RANK_1 = VIEW_TYPE_BASE++; // 专辑排行榜
    public static final int VIEW_TYPE_ALBUM_RANK_2 = VIEW_TYPE_BASE++; // 专辑排行榜
    public static final int VIEW_TYPE_ALBUM_RANK_3 = VIEW_TYPE_BASE++; // 专辑排行榜
    public static final int VIEW_TYPE_ALBUM_RANK_4 = VIEW_TYPE_BASE++; // 专辑排行榜
    public static final int VIEW_TYPE_ALBUM_RANK_5 = VIEW_TYPE_BASE++; // 专辑排行榜

    //NOTO:注意可视化埋点在使用上面的type，上面的顺序禁止修改，只能在最后增加
    //所有类型必须放在VIEW_TYPE_MAX_COUNT之前，VIEW_TYPE_MAX_COUNT是返回listview的type的个数的，前面有个VIEW_TYPE_NONE
    //只使用map的size返回，不准确，如果类型不添加到map里会造成返回的type个数有问题
    public static final int VIEW_TYPE_MAX_COUNT = VIEW_TYPE_BASE++; //必须放在最后，禁止修改
    /***type end--注意：禁止在中间添加，只能在最后的type后面自增，可视化埋点在使用这个type作为唯一路径*****************************************************/

    private Map<Integer, IMulitViewTypeViewAndData> adapterMap;

    private HomeRecommendContact.IFragmentView iFragmentView;
    private final BaseFragment2 mFragment;
    private final MainActivity mActivity;
    private final AlbumAdapter albumAdapter;
    private LayoutInflater inflater;
    private List<ItemModel> listData;
    private IRecommendFeedItemActionListener recommendFeedItemActionListener;

    //保护的额外信息，所有的子卡片使用同一个对象，如果需要更新可以使用updateSelf进行更新
    private HomeRecommendExtraViewModel mExtraViewModel = new HomeRecommendExtraViewModel();

    private HomeRecommendOneKeyRadioProvider mOneKeyRadioProvider;

    public HomeRecommendAdapter(HomeRecommendContact.IFragmentView fragmentView, HomeRecommendExtraViewModel viewModel) {
        this.iFragmentView = fragmentView;
        mFragment = this.iFragmentView.getBaseFragment2();
        mActivity = (MainActivity) this.iFragmentView.getActivity();
        inflater = LayoutInflater.from(mActivity);
        listData = new ArrayList<>(0);
        if (viewModel != null) {
            mExtraViewModel.updateSelf(viewModel);
        }

        this.recommendFeedItemActionListener = recommendFeedItemActionListener;

        createAdapterMap();

        albumAdapter = new AlbumAdapter(mActivity, null);
    }

    public void setRecommendFeedItemActionListener(IRecommendFeedItemActionListener recommendFeedItemActionListener) {
        this.recommendFeedItemActionListener = recommendFeedItemActionListener;
    }

    private void createAdapterMap() {
        adapterMap = new ArrayMap<>();
        adapterMap.put(VIEW_TYPE_TITLE_NORMAL, new HomeRecommendNormalTitleProvider(this,iFragmentView));
        adapterMap.put(VIEW_TYPE_TITLE_NORMAL_V2, new HomeRecommendNormalTitleProviderV2());
        adapterMap.put(VIEW_TYPE_ALBUM_VERTICAL, null); //未分离出去
        adapterMap.put(VIEW_TYPE_ALBUM_HORIZONT, new HomeRecommendAlbumHorizontalProvider(mFragment, recommendFeedItemActionListener));
        adapterMap.put(VIEW_TYPE_REFRESH, new HomeRecommendRefreshProvider(mFragment, this, mExtraViewModel));
        adapterMap.put(VIEW_TYPE_FEED_TRACK, new HomeRecommendFeedTrackAdapterProvider(mFragment, this, recommendFeedItemActionListener));
        adapterMap.put(VIEW_TYPE_FEED_TRACK_STYLE_V2, new HomeRecommendFeedTrackStyleV2AdapterProvider(mFragment, this, recommendFeedItemActionListener));
        adapterMap.put(VIEW_TYPE_FEED_ALBUM, new HomeRecommendFeedAlbumAdapterProvider(mFragment, this, recommendFeedItemActionListener));
        adapterMap.put(VIEW_TYPE_FEED_ALBUM_STYLE_V2, new HomeRecommendFeedAlbumStyleV2AdapterProvider(mFragment, this, recommendFeedItemActionListener));
        adapterMap.put(VIEW_TYPE_FEED_ALBUM_STYLE_V3, new HomeRecommendFeedAlbumStyleV3AdapterProvider(mFragment, this, recommendFeedItemActionListener));
        adapterMap.put(VIEW_TYPE_FEED_ALBUM_STYLE_V4, new HomeRecommendFeedAlbumStyleV4AdapterProvider(mFragment, this, recommendFeedItemActionListener));
        adapterMap.put(VIEW_TYPE_TING_UPDATE, new HomeRecommendTingUpdataAdapterProvider(mFragment, this, recommendFeedItemActionListener));
        adapterMap.put(VIEW_TYPE_REAL_TIME_RECOMMEND_STRONG_MODULE, new HomeRecommendRealTimeRecommendStrongModuleAdapterProvider(mFragment));

        adapterMap.put(VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION, new HomeRecommendExcitationAdapterProvider(mFragment, this));
        adapterMap.put(VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3, new HomeRecommendExcitationStyle3AdapterProvider(mFragment, this));
        adapterMap.put(VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4, new HomeRecommendExcitationStyle4AdapterProvider(mFragment, this));
        mOneKeyRadioProvider = new HomeRecommendOneKeyRadioProvider(mFragment);
        adapterMap.put(VIEW_TYPE_ONE_KEY_RADIO, mOneKeyRadioProvider);
        adapterMap.put(VIEW_TYPE_ONE_KEY_RADIO_SETTING, new HomeRecommendOneKeyRadioSettingProvider(mFragment));

        adapterMap.put(VIEW_TYPE_VIP_TOP_REGION, new VipSelectedTopRegionProvider(mFragment, mExtraViewModel.vipTabModel));
        adapterMap.put(VIEW_TYPE_VIP_BAR, new VipBarProvider(mFragment));
        adapterMap.put(VIEW_TYPE_VIP_FOCUS_IMAGE, new VipBannerProvider(mFragment, mExtraViewModel.vipTabModel));
        adapterMap.put(VIEW_TYPE_TANGHULU_FLOAT_HOT_WORD, new TanghuluFloatHotWordProvider(mFragment, mExtraViewModel.adapterDataSyncListener));
        adapterMap.put(VIEW_TYPE_TANGHULU_NORMAL_HOT_WORD, new TanghuluNormalHotWordProvider(mFragment, mExtraViewModel));
        adapterMap.put(VIEW_TYPE_VIP_HOT_WORD_ALBUM, new VipHotWordAlbumProvider(mFragment));
        adapterMap.put(VIEW_TYPE_TANGHULU_FEED_ALBUM, new TanghuluHotWordFeedProvider(mFragment));

        adapterMap.put(VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_ALBUM, new HorizontalScrollOneLineAlbumProvider(mFragment, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_HORIZONTAL_SCROLL_THREE_LINE_ALBUM, new HorizontalScrollThreeLineAlbumProvider(mFragment, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_NET_OPERATE, new NetOperateProvider(mFragment, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_ALBUM_OPERATE, new AlbumOperateProvider(mFragment, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_HOT_SEARCH_RANK, new HotSearchRankProvider(mFragment, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_INTEREST_RECOMMEND_REGION, new HorizontalScrollOneLineAlbumProvider(mFragment, mExtraViewModel));

        adapterMap.put(VIEW_TYPE_REPLENISH_SEARCH_REGION, new ReplenishSearchRegionProvider(mFragment, mExtraViewModel, this));

        adapterMap.put(VIEW_TYPE_LIST_VIEW_NO_CONTENT, new ListViewNoContentProvider(mFragment, mExtraViewModel.refreshListener));

        adapterMap.put(VIEW_TYPE_TANGHULU_HOME_V2, new HorizontalScrollTanghuluV2Provider(mFragment));

        adapterMap.put(VIEW_TYPE_MY_SUBSCRIPTION, new HomeSubscriptionProvider(mFragment, iFragmentView, this));

        //添加3份一样的楼层,每个楼层必须传入唯一的id，来重置viewpager的唯一id
        adapterMap.put(VIEW_TYPE_ALBUM_RANK_1, new HomeAlbumRankProvider(mFragment, this, VIEW_TYPE_ALBUM_RANK_1));
        adapterMap.put(VIEW_TYPE_ALBUM_RANK_2, new HomeAlbumRankProvider(mFragment, this, VIEW_TYPE_ALBUM_RANK_2));
        adapterMap.put(VIEW_TYPE_ALBUM_RANK_3, new HomeAlbumRankProvider(mFragment, this, VIEW_TYPE_ALBUM_RANK_3));
        adapterMap.put(VIEW_TYPE_ALBUM_RANK_4, new HomeAlbumRankProvider(mFragment, this, VIEW_TYPE_ALBUM_RANK_4));
        adapterMap.put(VIEW_TYPE_ALBUM_RANK_5, new HomeAlbumRankProvider(mFragment, this, VIEW_TYPE_ALBUM_RANK_5));
    }


    private void checkViewType(int viewType) {
        if (adapterMap == null || !adapterMap.containsKey(viewType)) {
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException("设置ViewType时要先进行配置");
            }
        }
    }

    public ItemModel add(Object dataObj, int viewType) {
        return add(dataObj, viewType, null);
    }

    public ItemModel add(Object dataObj, int viewType, Object extra) {
        if (dataObj == null) return null;
        checkViewType(viewType);
        ItemModel itemModel = new ItemModel(dataObj, viewType);
        itemModel.setTag(extra);
        listData.add(itemModel);
        return itemModel;
    }

    public ItemModel add(ItemModel itemModel) {
        listData.add(itemModel);
        return itemModel;
    }

    @Override
    public List<ItemModel> getListData() {
        if (listData == null) {
            listData = new ArrayList<>();
        }
        return listData;
    }

    public void clear() {
        if (listData != null) {
            listData.clear();
            notifyDataSetChanged();
        } else {
            listData = new ArrayList<>();
        }
    }

    @Override
    public int getCount() {
        return listData.size();
    }

    @Override
    public ItemModel getItem(int position) {
        if (listData != null && listData.size() > 0 && position < listData.size() && position >= 0) {
            return listData.get(position);
        }
        return null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getViewTypeCount() {
//      int viewTypeSize = 0;
//      if (adapterMap != null) {
//          viewTypeSize = adapterMap.size();
//      }
//      // 有一个默认的VIEW_TYPE_NONE类型
//      return viewTypeSize + 1;
        //如果viewType没有添加到adapterMap中，那使用size的数量会不一致，造成无法展示
        return VIEW_TYPE_MAX_COUNT;
    }

    @Override
    public int getItemViewType(int position) {
        ItemModel itemModel = getItem(position);
        if (itemModel != null) {
            return itemModel.getViewType();
        }
        if (ConstantsOpenSdk.isDebug) {
            throw new RuntimeException(getClass().getName() + " : 相关的viewType 没有注册");
        }
        return VIEW_TYPE_NONE;
    }

    @Override
    public int getFrom() {
        if (mExtraViewModel != null) {
            return mExtraViewModel.from;
        }
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ItemModel itemModel = listData.get(position);
        Object object = itemModel.getObject();
        int viewType = getItemViewType(position);

        HolderAdapter.BaseViewHolder viewHolder;
        if (viewType == VIEW_TYPE_ALBUM_VERTICAL) {
            AlbumM albumM = (AlbumM) object;
            AlbumAdapter.ViewHolder albumHolder;
            if (convertView == null) {
                convertView = inflater.inflate(R.layout.main_item_album_home_lite, parent, false);
                albumHolder = new AlbumAdapter.ViewHolder(convertView);
                convertView.setTag(albumHolder);
            } else {
                albumHolder = (AlbumAdapter.ViewHolder) convertView.getTag();
            }
            albumAdapter.bindViewDatas(albumHolder, albumM, position);
        } else {
            IMulitViewTypeViewAndData adapterProvider = adapterMap.get(viewType);
            if (convertView == null) {
                convertView = adapterProvider.getView(inflater, position, parent);
                viewHolder = adapterProvider.buildHolder(convertView);
                convertView.setTag(viewHolder);
            } else {
                //不需要使用第二tag校验，直接使用缓存
                viewHolder = (HolderAdapter.BaseViewHolder) convertView.getTag();
            }
            adapterProvider.bindViewDatas(viewHolder, itemModel, convertView, position);
        }
        return convertView;
    }


    public void resetValByOnRefresh() {
        if (mOneKeyRadioProvider != null) {
            mOneKeyRadioProvider.reset();
        }
    }

    @Override
    public void onClick(View v) {

    }

    public void release() {
        Set<Map.Entry<Integer, IMulitViewTypeViewAndData>> entrySet = adapterMap.entrySet();
        Iterator<Map.Entry<Integer, IMulitViewTypeViewAndData>> iterator = entrySet.iterator();
        while (iterator.hasNext()) {
            Object obj = iterator.next().getValue();
            if (obj instanceof IAdapterRelease) {
                ((IAdapterRelease) obj).onRelease();
            }
        }
    }

    @Override
    public HomeRecommendExtraViewModel getHomeRecommendExtraViewModel() {
        return mExtraViewModel;
    }

    @Override
    public int getCategoryId() {
        if (getHomeRecommendExtraViewModel() != null) {
            return getHomeRecommendExtraViewModel().categoryId;
        }
        return -1;
    }

    @Override
    public void removeItem(int position) {
        if (listData == null) {
            return;
        }
        if (position < 0) {
            return;
        }
        if (position >= listData.size()) {
            return;
        }
        listData.remove(position);
        notifyDataSetChanged();
    }

    public interface IAdapterRelease {
        void onRelease();
    }


    @Override
    public void onRefreshSuccess() {
        notifyDataSetChanged();
    }

    @Override
    public void onRefreshSuccess(List<AlbumM> object, CategoryRecommendRefresh refreshAndMore, int position) {
        if (object == null || refreshAndMore == null) {
            return;
        }
        updateDataByOffset(position, object, refreshAndMore.albumMList.getCardClass());
    }

    /**
     * 更新数据根据偏移量
     * <p>
     * refreshItemPosition 刷新item的position
     * <p>
     * data网络请求的数据
     * <p>
     * direction当前的方向
     */
    private void updateDataByOffset(int refreshItemPosition, List<AlbumM> data, String direction) {
        //之前的首页列表没有数据，直接return
        if (listData == null || listData.size() == 0) {
            return;
        }
        if (data == null || data.size() == 0) {
            return;
        }
        //之前的数据列表发生了变化，首页item的个数小于了本次刷新控件的position，不能在使用刷新时候的position，
        if (listData.size() <= refreshItemPosition) {
            return;
        }
        //横向布局，更新数量小于3个，不够一个item的数据，return
        if (MainAlbumMList.ITEM_DIRECTION_HORI.equals(direction) && data.size() < 3) {
            return;
        }
        //网络请求回来的需要更新的数据增个数
        int count = data.size();
        for (int i = 0; i < (MainAlbumMList.ITEM_DIRECTION_HORI.equals(direction) ? count / 3 : count); i++) {
            //水平布局，1行展示3个
            if (MainAlbumMList.ITEM_DIRECTION_HORI.equals(direction)) {
                int index = refreshItemPosition - count / 3 + i;
                if (listData.size() > index && index >= 0) {
                    ItemModel remove = listData.get(index);
                    if (remove == null) {
                        continue;
                    }
                    //获取到之前列表的item，判断当前item列表中的是否和需要替换数据的的类型一致，不一致不能进行更新会出现setObject类型错误
                    if (remove.getViewType() != HomeRecommendAdapter.VIEW_TYPE_ALBUM_HORIZONT) {
                        continue;
                    }
                    if ((data.size() >= i * 3 + 3)) {
                        remove.setObject(new ArrayList(data.subList(i * 3, i * 3 + 3)));
                    }
                }
            } else if (MainAlbumMList.ITEM_DIRECTION_VERT.equals(direction)) {
                int index = refreshItemPosition - count + i;
                if (listData.size() > index && index > 0) {
                    ItemModel remove = listData.get(index);
                    if (remove == null) {
                        continue;
                    }
                    //获取到之前列表的item，判断当前item列表中的是否和需要替换数据的的类型一致，不一致不能进行更新会出现setObject类型错误
                    if (remove.getViewType() != HomeRecommendAdapter.VIEW_TYPE_ALBUM_VERTICAL) {
                        continue;
                    }
                    if (data.size() > i) {
                        ItemModel element = new ItemModel(data.get(i), remove.getViewType(), remove.getDataType());
                        element.setTag(remove.getTag());
                        listData.set(index, element);
                    }
                }
            }
        }
        notifyDataSetChanged();
    }

    /**
     * 3
     * 设置额外使用的各种信息
     */
    public void setHomeRecommendExtraViewModel(HomeRecommendExtraViewModel viewModel) {
        mExtraViewModel.updateSelf(viewModel);
    }
}