package com.ximalaya.ting.lite.main.home.viewmodel;

import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.lite.main.model.newhome.LiteTabModel;
import com.ximalaya.ting.lite.main.model.newhome.listener.FilterPanelProvider;
import com.ximalaya.ting.lite.main.model.vip.VipTabModel;
import com.ximalaya.ting.lite.main.vip.listener.AdapterDataSyncListener;
import com.ximalaya.ting.lite.main.vip.listener.FilterPanelSyncListener;
import com.ximalaya.ting.lite.main.vip.listener.ListViewNoContentRefreshListener;
import com.ximalaya.ting.lite.main.vip.listener.LiteChildTabChangeListener;

/**
 * Created by qinhuifeng on 2019-07-17
 * <p>
 * 承载adapter中的一些信息传递
 *
 * <AUTHOR>
 */
public class HomeRecommendExtraViewModel {

    public int categoryId = -1;

    public int pageId = -1;

    public VipTabModel vipTabModel;

    /**
     * 喜小说页面使用这个
     */
    public LiteTabModel liteTabModel;

    public AdapterDataSyncListener adapterDataSyncListener;

    public FilterPanelSyncListener filterPanelSyncListener;

    public FilterPanelProvider filterPanelProvider;

    public ListViewNoContentRefreshListener refreshListener;

    public LiteChildTabChangeListener childTabChangeListener;

    public RefreshLoadMoreListView refreshLoadMoreListView;

    public int from;

    public boolean isNewUser;

    /**
     * 更新自己的信息
     */
    public void updateSelf(HomeRecommendExtraViewModel viewModel) {
        if (viewModel == null) {
            return;
        }
        this.from = viewModel.from;
        this.categoryId = viewModel.categoryId;
        this.pageId = viewModel.pageId;
        this.vipTabModel = viewModel.vipTabModel;
        this.liteTabModel = viewModel.liteTabModel;
        this.adapterDataSyncListener = viewModel.adapterDataSyncListener;
        this.refreshListener = viewModel.refreshListener;
        this.filterPanelSyncListener = viewModel.filterPanelSyncListener;
        this.filterPanelProvider = viewModel.filterPanelProvider;
        this.childTabChangeListener = viewModel.childTabChangeListener;
        this.refreshLoadMoreListView = viewModel.refreshLoadMoreListView;
    }
}
