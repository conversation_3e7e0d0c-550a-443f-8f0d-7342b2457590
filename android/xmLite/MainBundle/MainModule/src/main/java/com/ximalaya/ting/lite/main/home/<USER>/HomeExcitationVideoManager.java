package com.ximalaya.ting.lite.main.home.manager;

import android.app.Activity;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.listenertask.ListenEarnCoinDialogManager;
import com.ximalaya.ting.android.host.listenertask.callback.JssdkFuliRewardCallback;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.earn.FuliBallType;
import com.ximalaya.ting.android.host.model.earn.FulliCoinRewardReqModel;
import com.ximalaya.ting.android.host.model.earn.RewardCoinObtainRsp;
import com.ximalaya.ting.android.host.model.homepage.HomeAwardSetting;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommedExtraDataProvider;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by le.xin on 2019-12-27.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class HomeExcitationVideoManager {

    //默认是样式1
    public static final int STYLE_DEF = 1;
    //样式3
    public static final int STYLE_3 = 3;
    //样式4
    public static final int STYLE_4 = 4;

    public List<HomeAwardSetting.TitlesBean> getTitlesBeans() {
        return mTitlesBeans;
    }

    @NonNull
    public HomeAwardSetting.TitlesBean getNeedShowTitleBean(int remendNumber) {
        List<HomeAwardSetting.TitlesBean> useTitlesBeans;
        //登录状态后面还剩0个的时候，取最后一个数组的标题
        if (remendNumber == 0 && UserInfoMannage.hasLogined()) {
            useTitlesBeans = mLastTitles;
        } else {
            useTitlesBeans = mTitlesBeans;
        }
        if (useTitlesBeans == null || useTitlesBeans.size() == 0) {
            return createTitleBeanForError();
        }
        HomeAwardSetting.TitlesBean needShowBean = null;
        if (useTitlesBeans.size() > useTitlePosition) {
            needShowBean = useTitlesBeans.get(useTitlePosition);
        }
        //没有获取到，或者useTitlePosition配置错误，取第一条数据
        if (needShowBean == null) {
            needShowBean = useTitlesBeans.get(0);
        }
        //如果仍然发生错误，创建默认数据
        if (needShowBean == null) {
            needShowBean = createTitleBeanForError();
        }
        //此处创建一个新的TitlesBean返回，因为"恭喜您获得%d幸运金币"像这种文案，在使用后，原始对象的%d这种进行替换，造成下次再展示替换不成功
        //此处每次使用都创建一个全新的来使用，保证原数据不会受到影响
        HomeAwardSetting.TitlesBean createNewTitleShow = new HomeAwardSetting.TitlesBean(needShowBean.getTitle(), needShowBean.getSubTitle(), needShowBean.getSubTitle2());
        return createNewTitleShow;
    }

    private HomeAwardSetting.TitlesBean createTitleBeanForError() {
        if (!UserInfoMannage.hasLogined()) {
            return new HomeAwardSetting.TitlesBean("恭喜您获得%d幸运金币！", "前往福利页有更多金币可领哦", "已有%d人领取");
        }
        //已登录
        return new HomeAwardSetting.TitlesBean("恭喜您获得%d幸运金币！", "前往福利页有更多金币可领哦", "已有%d人领取");
    }

    public void setTitlesBeans(HomeAwardSetting setting) {
        if (setting == null) {
            return;
        }
        List<HomeAwardSetting.TitlesBean> titles = setting.getTitles();
        if (titles != null && titles.size() > 0) {
            mTitlesBeans.clear();
            mTitlesBeans.addAll(titles);
        }
        List<HomeAwardSetting.TitlesBean> lastTitles = setting.getLastTitles();
        if (lastTitles != null && lastTitles.size() > 0) {
            mLastTitles.clear();
            mLastTitles.addAll(lastTitles);
        }
    }

    private static class Holder {
        private static final HomeExcitationVideoManager INSTANCE = new HomeExcitationVideoManager();
    }

    //那种样式取配置中心，abtest
    private int homeAwardStyle = STYLE_DEF;

    //需要取服务端哪条数据，方便abtest，需要走配置中心
    private int useTitlePosition = 0;

    private List<HomeAwardSetting.TitlesBean> mTitlesBeans = new ArrayList<>();

    private List<HomeAwardSetting.TitlesBean> mLastTitles = new ArrayList<>();

    public static HomeExcitationVideoManager getInstance() {
        return Holder.INSTANCE;
    }

    public int getHomeAwardStyle() {
        return homeAwardStyle;
    }

    public void setHomeAwardStyle() {
        int awardStyle = ConfigureCenter.getInstance().getInt("ximalaya_lite_ad", "xxlReward", HomeExcitationVideoManager.STYLE_DEF);
        switch (awardStyle) {
            case STYLE_DEF:
            case STYLE_3:
            case STYLE_4:
                this.homeAwardStyle = awardStyle;
                break;
            default:
                this.homeAwardStyle = STYLE_DEF;
                break;
        }
        //设置取文案位置
        useTitlePosition = ConfigureCenter.getInstance().getInt("ximalaya_lite_ad", "xxlAdTitle", 0);
    }

    public View.OnClickListener getAwardClickListener(final HomeAwardSetting.GameTimesBean object, final HomeRecommedExtraDataProvider recommedExtraDataProvider) {
        return new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(MainApplication.getMyApplicationContext());
                    return;
                }
                getActivityAward(object, recommedExtraDataProvider);
            }
        };
    }

    private void getActivityAward(final HomeAwardSetting.GameTimesBean object, final HomeRecommedExtraDataProvider recommedExtraDataProvider) {
        //阶段奖励领取，并且弹出弹框
        FulliCoinRewardReqModel rewardReqModel = new FulliCoinRewardReqModel();
        rewardReqModel.ballAward = null;
        rewardReqModel.ballType = FuliBallType.BALL_TYPE_HOME_VIDEO;
        ListenEarnCoinDialogManager dialogManager = ListenEarnCoinDialogManager.getInstance();
        dialogManager.getCoinNormalReward(rewardReqModel, new JssdkFuliRewardCallback() {
            @Override
            public void onAwardSuccess(int successCode, RewardCoinObtainRsp obtainRsp) {
                if (object != null) {
                    object.setUsed(true);
                }

                FuliLogger.log("Jssdk==成功回调==阶段");
                if (recommedExtraDataProvider != null) {
                    recommedExtraDataProvider.notifyDataSetChanged();
                }

                //每次领取成功更新一次标题
                HomeExcitationVideoManager.getInstance().updateTitleBean();
            }

            @Override
            public void onError(int errorCode, String message, RewardCoinObtainRsp obtainRsp) {
                FuliLogger.log("Jssdk==失败回调==阶段");
                if (recommedExtraDataProvider != null) {
                    recommedExtraDataProvider.notifyDataSetChanged();
                }
            }
        });
    }

    private void showLoading() {
        Activity topActivity = BaseApplication.getTopActivity();
        if (!(topActivity instanceof MainActivity)) {
            return;
        }
        MainActivity mainActivity = (MainActivity) topActivity;
        mainActivity.showGlobalLoading("正在加载...");
    }

    private void hideLoading() {
        Activity topActivity = BaseApplication.getTopActivity();
        if (!(topActivity instanceof MainActivity)) {
            return;
        }
        MainActivity mainActivity = (MainActivity) topActivity;
        mainActivity.hideGlobalLoading();
    }

    /**
     * 最后一个标题不一致，需要更新一下标题
     */
    public void updateTitleBean() {
        CommonRequestM.queryActivitySetting(new IDataCallBack<HomeAwardSetting>() {
            @Override
            public void onSuccess(@Nullable HomeAwardSetting object) {
                //重新设置标题
                setTitlesBeans(object);
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }
}
