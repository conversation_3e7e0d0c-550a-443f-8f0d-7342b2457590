package com.ximalaya.ting.lite.main.home.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.model.onekey.OneKeyRadioModel;
import com.ximalaya.ting.lite.main.onekey.OneKeyRadioFragment;
import com.ximalaya.ting.lite.main.onekey.playpage.OneKeyRadioPlayFragment;
import com.ximalaya.ting.lite.main.utils.OneKeyRadioUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ZhuPeipei on 2020-04-10 20:28.
 *
 * @Description: 首页 分类页 radio adapter
 */
public class RecommendOneKeyNewItemAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private static final String TAG = "RecommendOneKeyNewItemAdapter";

    private static final int TYPE_RADIO = 0;
    private static final int TYPE_MORE = 1;

    private Context mContext;
    private BaseFragment2 mFragment;
    private List<Object> listData = new ArrayList<>();
    private long mLastPlayedRadioId = -1;

    public RecommendOneKeyNewItemAdapter(Context context, BaseFragment2 fragment) {
        mContext = context;
        mFragment = fragment;
        mLastPlayedRadioId = OneKeyRadioUtil.getLastRadioId(mContext);
    }

    public void setListData(List<Object> datas) {
        if (ToolUtil.isEmptyCollects(datas)) {
            return;
        }
        listData.clear();
        listData.addAll(datas);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_RADIO) {
            View view = LayoutInflater.from(mContext)
                    .inflate(R.layout.main_home_recommend_item_onekey_radio_view, parent, false);
            return new RadioViewHolder(view);
        } else if (viewType == TYPE_MORE) {
            View view = LayoutInflater.from(mContext)
                    .inflate(R.layout.main_home_recommend_item_onekey_radio_more_view, parent, false);
            return new MoreViewHolder(view);
        }
        return null;
    }

    @Override
    public void onViewAttachedToWindow(@NonNull RecyclerView.ViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        if (holder instanceof RadioViewHolder) {
            ((RadioViewHolder) holder).playLottie.playAnimation();
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        Object model = getData(position);
        if (holder instanceof RadioViewHolder) {
            if (model instanceof OneKeyRadioModel) {
                onBindRadioViewHolder((OneKeyRadioModel) model, (RadioViewHolder) holder);
            }
        } else if (holder instanceof MoreViewHolder) {
            if (model instanceof Integer) {
                ((MoreViewHolder) holder).countTv.setText("共" + model + "个");
            }
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mFragment == null) {
                        return;
                    }
                    mFragment.startFragment(OneKeyRadioFragment.newInstance());
                }
            });
        }
    }

    private void onBindRadioViewHolder(final OneKeyRadioModel model, final RadioViewHolder holder) {
        holder.radioTv.setText(model.getName());
        setRadioIv(holder.radioIv, holder.radiusFl, model.getCoverPath());
        holder.playLottie.playAnimation();
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (model.getRadioId() == mLastPlayedRadioId) {
                    mLastPlayedRadioId = -1;
                }
                if (OneKeyRadioUtil.isInCurRadio(mContext, model)) {
                    if (XmPlayerManager.getInstance(mContext).isPlaying()) {
                        XmPlayerManager.getInstance(mContext).pause();
                        return;
                    }
                }
                if (mFragment == null) {
                    return;
                }
                mFragment.startFragment(OneKeyRadioPlayFragment.newInstance(model));
            }
        });
//        holder.radioIv.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (model.getRadioId() == mLastPlayedRadioId) {
//                    mLastPlayedRadioId = -1;
//                }
//                if (OneKeyRadioUtil.isInCurRadio(mContext, model)) {
//                    if (XmPlayerManager.getInstance(mContext).isPlaying()) {
//                        XmPlayerManager.getInstance(mContext).pause();
//                    } else {
//                        XmPlayerManager.getInstance(mContext).play();
//                    }
//                    return;
//                }
//                AnimationUtil.rotateView(mContext, holder.playIv);
//                holder.playIv.setImageResource(R.drawable.main_onekey_radio_loading);
//                if (model.getType() == OneKeyRadioModel.TYPE_OTHER_RADIO) {
//                    long toTrackId = OneKeyRadioUtil.getLastTrackIdByChannelId(mContext, model.getId());
//                    OneKeyRadioUtil.requestTracksAndPlay(mFragment, toTrackId, model);
//                } else {
//                    OneKeyRadioUtil.requestHeadLineTracksAndPlay(mFragment, -1, model);
//                }
//            }
//        });
        AnimationUtil.stopAnimation(holder.playIv);
        if (OneKeyRadioUtil.isCurRadioPlaying(mContext, model)) {
            holder.playIv.setImageResource(R.drawable.main_onekey_radio_pause);
        } else if (OneKeyRadioUtil.isCurRadioLoading(mContext, model)) {
            holder.playIv.setImageResource(R.drawable.main_onekey_radio_loading);
            AnimationUtil.rotateView(mContext, holder.playIv);
        } else {
            holder.playIv.setImageResource(R.drawable.main_onekey_radio_play);
        }
        if (mLastPlayedRadioId == model.getRadioId()
                && !OneKeyRadioUtil.isCurRadioPlaying(mContext, model)) {
            holder.lastPlayTv.setVisibility(View.VISIBLE);
            holder.lastPlayTv.setText("上次收听");
            holder.lastPlayTv.setBackground(
                    mContext.getResources().getDrawable(R.drawable.main_recomemnd_onekey_radio_recently_tag));
        } else if (model.isNewRecommend()) {
            holder.lastPlayTv.setVisibility(View.VISIBLE);
            holder.lastPlayTv.setText(model.getRecommendReason()); // "上新推荐"
            holder.lastPlayTv.setBackground(
                    mContext.getResources().getDrawable(R.drawable.main_recomemnd_onekey_radio_good_tag));
        } else {
            holder.lastPlayTv.setVisibility(View.INVISIBLE);
        }
        AutoTraceHelper.bindData(holder.itemView, AutoTraceHelper.MODULE_DEFAULT, model);
        AutoTraceHelper.bindData(holder.radioIv, AutoTraceHelper.MODULE_DEFAULT, model);
    }

    private void setRadioIv(RoundImageView radioIv, final FrameLayout backgroundView, String url) {
        ImageManager.from(mContext).displayImage(radioIv, url, R.drawable.main_onekey_default_ic,
                new ImageManager.DisplayCallback() {
                    @Override
                    public void onCompleteDisplay(String lastUrl, final Bitmap bitmap) {
                        if (mFragment == null || !mFragment.canUpdateUi()) {
                            return;
                        }
                        if (bitmap == null) {
                            backgroundView.setBackground(mContext.getResources().getDrawable(
                                    R.drawable.main_round_bg_radius_748c8f_gradient_dp4
                            ));
                            return;
                        }
                        LocalImageUtil.setMainColor(backgroundView, bitmap, new LocalImageUtil.Callback() {
                            @Override
                            public void onMainColorGot(int color) {
                                float[] hsvColor = new float[3];
                                @ColorInt int dstColor = 0xFF2D2D2D;
                                if (color == 0xFF4A4A4A) {
                                    // 取色失败
                                    color = bitmap.getPixel(2, 2);
                                }
                                Color.colorToHSV(color, hsvColor);
                                if (hsvColor[1] < 0.1 && hsvColor[2] > 0.9
                                        || hsvColor[1] < 0.1 && hsvColor[2] < 0.1
                                        || hsvColor[1] > 0.9 && hsvColor[2] < 0.1) {

                                } else {
                                    hsvColor[1] = 0.3F;
                                    hsvColor[2] = 0.5F;
                                    dstColor = Color.HSVToColor(255, hsvColor);
                                }

                                String strColor = String.format("%06X", 0xFFFFFF & dstColor);

                                try {
                                    GradientDrawable gradientDrawable = new GradientDrawable(
                                            GradientDrawable.Orientation.TR_BL,
                                            new int[]{
                                                    Color.parseColor("#4d" + strColor),
                                                    Color.parseColor("#e6" + strColor)});
                                    gradientDrawable.setCornerRadius(BaseUtil.dp2px(mContext, 4));

                                    backgroundView.setBackground(gradientDrawable);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        });
                    }
                });
    }

    private Object getData(int index) {
        if (index >= 0 && index < listData.size()) {
            return listData.get(index);
        }
        return null;
    }

    @Override
    public int getItemViewType(int position) {
        if (position >= 0 && position < listData.size()) {
            return listData.get(position) instanceof OneKeyRadioModel ? TYPE_RADIO : TYPE_MORE;
        }
        return super.getItemViewType(position);
    }

    @Override
    public int getItemCount() {
        return listData.size();
    }

    public void resetLastPlayedRadioId() {
        mLastPlayedRadioId = OneKeyRadioUtil.getLastRadioId(mContext);
    }

    class RadioViewHolder extends RecyclerView.ViewHolder {
        FrameLayout radiusFl;
        TextView radioTv;
        XmLottieAnimationView playLottie;
        RoundImageView radioIv;
        ImageView playIv;
        TextView lastPlayTv;

        public RadioViewHolder(View itemView) {
            super(itemView);
            radiusFl = itemView.findViewById(R.id.main_onekey_radio_item_fl);
            radioIv = itemView.findViewById(R.id.main_onekey_radio_item_iv);
            radioTv = itemView.findViewById(R.id.main_onekey_radio_item_radio_tv);
            playLottie = itemView.findViewById(R.id.main_onekey_radio_item_lottie_view);
            playIv = itemView.findViewById(R.id.main_onekey_radio_item_play_iv);
            lastPlayTv = itemView.findViewById(R.id.main_onekey_radio_item_last_play_tv);
        }
    }

    class MoreViewHolder extends RecyclerView.ViewHolder {
        TextView countTv;

        public MoreViewHolder(View itemView) {
            super(itemView);
            countTv = itemView.findViewById(R.id.main_item_count_radios);
        }
    }
}
