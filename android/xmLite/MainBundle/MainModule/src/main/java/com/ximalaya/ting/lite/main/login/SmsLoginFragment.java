package com.ximalaya.ting.lite.main.login;

import android.os.Bundle;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.login.LoginPageTraceManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * Created by nali on 2018/5/3.
 * 短信登录fragment
 *
 * <AUTHOR>
 * <p>
 * 拷贝主app：SmsLoginFragment
 */
public class SmsLoginFragment extends BaseLoginFragment {
    private TextView mTitle;
    private LoginView mLoginView;
    private LoginArgeementView mLoginAgreementView;

    private final ILoginViewHandle mLoginViewHandle = new ILoginViewHandle() {
        @Override
        public boolean checkAgreementSelectedAndShowHint() {
            if (mLoginAgreementView == null) {
                return true;
            }
            return mLoginAgreementView.checkAgreementSelectedAndShowHint();
        }
    };

    @Override
    public View onCreateView(final LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.main_sms_login_layout, null);
        mTitle = view.findViewById(R.id.main_sms_login_title);

        mLoginAgreementView = view.findViewById(R.id.main_view_login_agreement_view);
        mLoginAgreementView.initUi(getActivity(), getArguments());

        mLoginView = view.findViewById(R.id.main_login_view);
        mLoginView.initUi(this, getArguments(), mLoginViewHandle);

        // 设置标题
        mTitle.setText(LoginBundleParamsManager.getLoginTitle(getArguments()));

        ImageView ivClose = view.findViewById(R.id.main_iv_close);
        ivClose.setAlpha(0.4f);
        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mActivity != null) {
                    mActivity.finish();
                } else {
                    finishFragment();
                }
            }
        });

        //切换监听
        mLoginView.setShowLayoutListener(new LoginViewShowLayoutCallback() {
            @Override
            public void showLayoutPage(int showLayoutType) {
                if (mTitle == null) {
                    return;
                }
                if (showLayoutType == LoginView.TYPE_SHOW_LAYOUT_INPUT_PHONE) {
                    mTitle.setVisibility(View.VISIBLE);
                    mLoginAgreementView.setVisibility(View.VISIBLE);
                } else {
                    mTitle.setVisibility(View.INVISIBLE);
                    mLoginAgreementView.setVisibility(View.INVISIBLE);
                }
            }
        });

        LoginPageTraceManager.traceLoginPageShow(false, false);

        return view;
    }

    @Override
    protected String getPageLogicName() {
        return "smsLogin";
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        if (mLoginView != null) {
            mLoginView.onMyResume();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mLoginView != null) {
            mLoginView.onPause();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mLoginView != null) {
            mLoginView.onDestroy();
        }
    }

    @Override
    public boolean onBackPressed() {
        //登录view拦截返回操作
        if (mLoginView != null && mLoginView.onBackPressed()) {
            return true;
        }
        return super.onBackPressed();
    }
}
