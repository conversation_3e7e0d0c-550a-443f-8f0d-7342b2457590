package com.ximalaya.ting.lite.main.manager

import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.dialog.common.DailyAlbumOrTrackDialog
import com.ximalaya.ting.android.host.dialog.common.EveryDayCoinReportDialog
import com.ximalaya.ting.android.host.dialog.common.LiteHomeRecommendNovelDialog
import com.ximalaya.ting.android.host.listener.IShowDialog
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager
import com.ximalaya.ting.android.host.util.ContextUtils
import com.ximalaya.ting.android.host.view.OpenPushSettingDialog
import com.ximalaya.ting.lite.main.dialog.MemberBenefitsDialog
import com.ximalaya.ting.lite.main.earn.dialog.LoginGuideDialogStyle3Fragment
import com.ximalaya.ting.lite.main.earn.dialog.OptimizedHasLoginEarnGuideDialogFragment
import com.ximalaya.ting.lite.main.earn.dialog.OptimizedHasLoginEarnGuideDialogFragmentNew

object DialogShowManager {

    private const val TAG = "DialogShowManager"

    private val mDialogs = mutableListOf<IShowDialog>()

    private var isFirstResume = true

    // 是否拦截显示
    private var mIsIntercept = false

    // 是否需要恢复弹窗
    private var mIsNeedResumeDialog = false

    // 登录引导弹窗＞会员权益弹窗＞小说弹窗＞不活跃推专辑弹窗>通知权限弹窗
    private val mUnLoginPriority = mutableListOf(
        LoginGuideDialogStyle3Fragment::class.java.simpleName,
        MemberBenefitsDialog::class.java.simpleName,
        LiteHomeRecommendNovelDialog::class.java.simpleName,
        DailyAlbumOrTrackDialog::class.java.simpleName,
        OpenPushSettingDialog::class.java.simpleName
    )

    // 新人奖励弹窗＞权益弹窗（本期新增）＞每日播报弹窗＞小说弹窗＞不活跃推专辑弹窗＞通知权限弹窗；
    private val mLoginPriority = mutableListOf(
        OptimizedHasLoginEarnGuideDialogFragmentNew::class.java.simpleName,
        MemberBenefitsDialog::class.java.simpleName,
        EveryDayCoinReportDialog::class.java.simpleName,
        LiteHomeRecommendNovelDialog::class.java.simpleName,
        DailyAlbumOrTrackDialog::class.java.simpleName,
        OpenPushSettingDialog::class.java.simpleName
    )

    private fun printTab(tag: String, tabId: Int) {
        when (tabId) {
            TabFragmentManager.TAB_HOME -> {
                FuliLogger.log(TAG, "$tag:首页")
            }

            TabFragmentManager.TAB_I_LISTEN -> {
                FuliLogger.log(TAG, "$tag:我听")
            }

            TabFragmentManager.TAB_WELFARE -> {
                FuliLogger.log(TAG, "$tag:福利")
            }

            TabFragmentManager.TAB_MINE -> {
                FuliLogger.log(TAG, "$tag:我页")
            }
        }
    }

    // 拦截弹窗展示  (用户在登录引导弹窗点击登录时,新用户登陆成功会触发新手奖励弹窗,不拦截会导致会员权益弹窗先出)
    @JvmStatic
    fun interceptDialog() {
        mIsIntercept = true
        FuliLogger.log(TAG, "拦截一次弹窗触发")
    }

    @JvmStatic
    fun resumeDialog() {
        FuliLogger.log(TAG, "恢复弹窗触发mIsNeedResumeDialog:$mIsNeedResumeDialog")
        mIsIntercept = false
        if (mIsNeedResumeDialog) {
            mIsNeedResumeDialog = false
            showDialog()
        }
    }

    @JvmStatic
    fun onPageResume(tabId: Int) {
        printTab("当前界面可见", tabId)

        if (isFirstResume) {
            FuliLogger.log(TAG, "首次触发不执行")
            isFirstResume = false
            return
        }

        if (tabId == TabFragmentManager.TAB_WELFARE) {
            FuliLogger.log(TAG, "福利页不触发弹窗")
            return
        }

        if (isExistOtherDialog()) {
            return
        }

        if (mIsIntercept) {
            FuliLogger.log(TAG, "拦截本次弹窗触发逻辑")
            mIsIntercept = false
            mIsNeedResumeDialog = true
            return
        }

        showDialog()
    }

    @JvmStatic
    fun showDialog() {
        showDialog(null)
    }

    @JvmStatic
    fun showDialog(dialog: IShowDialog?) {
        if (dialog != null) {
            // 某些弹窗  比如每日金播报  新手奖励弹窗等  如果未写入已展示标记  在某些场景可能会重复触发
            if (isExistDialog(dialog.dialogName)) {
                FuliLogger.log(TAG, dialog.dialogName + "弹窗正在已存在,不添加")
                return
            }

            setDialogLevel(dialog)

            // 去重逻辑   默认的优先级可能会有很多弹窗  不去重
            if (dialog.priority != IShowDialog.DEFAULT_PRIORITY) {
                val list = mDialogs.filter {
                    it.priority == dialog.priority
                }

                if (list.isNotEmpty()) {
                    FuliLogger.log(TAG, "弹窗列表已存在当前弹窗,不处理:$dialog")
                    return
                }
            }

            mDialogs.add(dialog)
            FuliLogger.log(TAG, "---->加入弹窗管理:$dialog----->")

            mDialogs.sortBy {
                it.priority
            }
        }

//        FuliLogger.log(TAG, "--->当前弹窗列表")
//        mDialogs.forEach {
//            FuliLogger.log(TAG, it.toString())
//        }
//        FuliLogger.log(TAG, "--->当前弹窗列表")

        if (mDialogs.isNotEmpty()) {
            // 触发显示逻辑
            if (dialog == null) {
                mDialogs.removeAt(0).show()
                return
            }

            // 如果加入的弹窗是第一个  则判断目前有没有要显示的弹窗
            if (dialog == mDialogs[0]) {
                // 为啥延时  因为fragment加入到activity是异步的  可能正好有弹窗再加入
                HandlerManager.postOnUIThread {
                    if (mDialogs.isNotEmpty() && !isExistOtherDialog() && dialog == mDialogs[0]) {
                        // 如果加入的弹窗是第一个  则直接显示
                        mDialogs.removeAt(0).show()
                    }
                }
                return
            }
        }
    }

    private fun setDialogLevel(dialog: IShowDialog) {
        val mPriority = if (UserInfoMannage.hasLogined()) {
            mLoginPriority
        } else {
            mUnLoginPriority
        }

        if (mPriority.contains(dialog.dialogName)) {
            dialog.priority = mPriority.indexOf(dialog.dialogName)
        }
    }

    private fun isExistOtherDialog(): Boolean {
        val mainActivity = BaseApplication.getMainActivity()
        if (!ContextUtils.checkActivity(mainActivity) || mainActivity !is FragmentActivity) {
            return false
        }
        val fragmentManager: FragmentManager = mainActivity.supportFragmentManager
        for (fragment in fragmentManager.fragments) {
            if (fragment != null && (mUnLoginPriority.contains(fragment.javaClass.simpleName) || mLoginPriority.contains(
                    fragment.javaClass.simpleName
                ))
            ) {
                FuliLogger.log(TAG, fragment.javaClass.simpleName + "弹窗已存在,不显示其他弹窗")
                return true
            }
        }
        return false
    }

    private fun isExistDialog(className: String): Boolean {
        val mainActivity = BaseApplication.getMainActivity()
        if (!ContextUtils.checkActivity(mainActivity) || mainActivity !is FragmentActivity) {
            return false
        }
        val fragmentManager: FragmentManager = mainActivity.supportFragmentManager
        for (fragment in fragmentManager.fragments) {
            if (fragment != null && fragment.javaClass.simpleName == className) {
                return true
            }
        }
        return false
    }
}