package com.ximalaya.ting.lite.main.home.viewmodel;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by qinhuifeng on 2019-08-09
 *
 * <AUTHOR>
 */
public class HomeGuessYouLikeViewModel implements Parcelable {
    public int categoryId;
    public int keywordId;
    public int moduleType;
    public String moduleId;
    public String personalRecSubType;  // 模块类型为26时，会有这个字段
    public String title;
    public long channelId;
    public String subTitle;
    public String interestId;

    public HomeGuessYouLikeViewModel() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.categoryId);
        dest.writeInt(this.keywordId);
        dest.writeInt(this.moduleType);
        dest.writeString(this.moduleId);
        dest.writeString(this.personalRecSubType);
        dest.writeString(this.title);
        dest.writeLong(this.channelId);
        dest.writeString(this.subTitle);
        dest.writeString(this.interestId);
    }

    protected HomeGuessYouLikeViewModel(Parcel in) {
        this.categoryId = in.readInt();
        this.keywordId = in.readInt();
        this.moduleType = in.readInt();
        this.moduleId = in.readString();
        this.personalRecSubType = in.readString();
        this.title = in.readString();
        this.channelId = in.readLong();
        this.subTitle = in.readString();
        this.interestId = in.readString();
    }

    public static final Creator<HomeGuessYouLikeViewModel> CREATOR = new Creator<HomeGuessYouLikeViewModel>() {
        @Override
        public HomeGuessYouLikeViewModel createFromParcel(Parcel source) {
            return new HomeGuessYouLikeViewModel(source);
        }

        @Override
        public HomeGuessYouLikeViewModel[] newArray(int size) {
            return new HomeGuessYouLikeViewModel[size];
        }
    };
}
