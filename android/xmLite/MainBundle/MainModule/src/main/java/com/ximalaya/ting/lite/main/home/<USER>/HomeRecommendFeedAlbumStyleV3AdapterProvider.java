package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;
import com.ximalaya.ting.lite.main.model.album.RecommendAlbumItem;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qinhuifeng on 2019-07-31
 *
 * <AUTHOR>
 */
public class HomeRecommendFeedAlbumStyleV3AdapterProvider implements IMulitViewTypeViewAndData<HomeRecommendFeedAlbumStyleV3AdapterProvider.ViewHolder, RecommendItemNew> {
    private Activity mActivity;
    protected BaseFragment2 mFragment;
    private HomeRecommedExtraDataProvider dataProvider;
    private IRecommendFeedItemActionListener recommendFeedItemActionListener;

    public HomeRecommendFeedAlbumStyleV3AdapterProvider(BaseFragment2 baseFragment2, HomeRecommedExtraDataProvider dataProvider, IRecommendFeedItemActionListener recommendFeedItemActionListener) {
        mFragment = baseFragment2;
        mActivity = baseFragment2.getActivity();
        this.dataProvider = dataProvider;
        this.recommendFeedItemActionListener = recommendFeedItemActionListener;
    }

    @Override
    public void bindViewDatas(ViewHolder viewHolder, final ItemModel<RecommendItemNew> t, View convertView, final int position) {
        if (t == null || t.object == null) {
            return;
        }
        final RecommendItemNew recommendItem = (RecommendItemNew) t.getObject();
        Object item = t.object.getItem();
        if (!(item instanceof RecommendAlbumItem)) {
            return;
        }
        final RecommendAlbumItem albumItem = (RecommendAlbumItem) item;
        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, albumItem);
        //添加专辑Item的ContentDescription
        if (viewHolder.root != null) {
            if (!TextUtils.isEmpty(albumItem.getAlbumTitle())) {
                viewHolder.root.setContentDescription(albumItem.getAlbumTitle());
            } else {
                viewHolder.root.setContentDescription("");
            }
        }

        //设置专辑图
        ImageManager.from(mActivity).displayImage(viewHolder.cover, albumItem.getLargeCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145, com.ximalaya.ting.android.host.R.drawable.host_default_album_145);

        //设置vip等角标
        if (AlbumTagUtil.getAlbumCoverTag(albumItem) != -1) {
            viewHolder.ivTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable(albumItem, mActivity, AlbumTagUtil.ZOOM_IN_RATIO_78_percent));
            viewHolder.ivTag.setVisibility(View.VISIBLE);
        } else {
            viewHolder.ivTag.setVisibility(View.INVISIBLE);
        }

        //设置标题，完播等标签
        int textSize = (int) viewHolder.title.getTextSize();
        Spanned richTitle = AlbumAdapter.getRichTitle(albumItem, mActivity, textSize);
        viewHolder.title.setText(richTitle);

        //隐藏副标题
        viewHolder.subtitle.setVisibility(View.GONE);


        //设置播放量，使用第1个位置
        String playCountStr = StringUtil.getFriendlyNumStr(albumItem.getPlayCount()) + "播放";
        viewHolder.mAlbumInfoFirst.setCompoundDrawables(LocalImageUtil.getDrawable(mActivity, R.drawable.main_ic_common_play_count), null, null, null);
        viewHolder.mAlbumInfoFirst.setText(playCountStr);

        //设置订阅量，使用第2个位置
        String subscribedCountStr = StringUtil.getFriendlyNumStr(albumItem.getSubscribeCount()) + "订阅";
        viewHolder.mAlbumInfoSecond.setCompoundDrawables(LocalImageUtil.getDrawable(mActivity, R.drawable.main_ic_common_play_book), null, null, null);
        viewHolder.mAlbumInfoSecond.setText(subscribedCountStr);


        //根据状态设置标题颜色和前面的图标
        AnimationUtil.stopAnimation(viewHolder.ivPlayBtn);
        viewHolder.ivPlayBtn.setImageResource(R.drawable.main_btn_feed_stream_track_play_v2);
        if (isCurrentAlbumTrack(albumItem.getId())) {
            if (XmPlayerManager.getInstance(mActivity).isPlaying()) {
                viewHolder.ivPlayBtn.setImageResource(R.drawable.main_btn_feed_stream_track_pause_v2);
            } else if (XmPlayerManager.getInstance(mActivity).isBuffering()) {
                viewHolder.ivPlayBtn.setImageResource(R.drawable.main_img_feed_stream_track_v2_loading);
                AnimationUtil.rotateView(mActivity, viewHolder.ivPlayBtn);
            }
        }

        //开始播放点击
        viewHolder.ivPlayBtnContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isCurrentAlbumTrack(albumItem.getId())) {
                    if (XmPlayerManager.getInstance(mActivity).isPlaying()) {
                        XmPlayerManager.getInstance(mActivity).pause();
                    } else {
                        XmPlayerManager.getInstance(mActivity).play();
                    }
                } else {
                    List<TrackM> topTracks = albumItem.getTopTracks();
                    if (topTracks != null && topTracks.size() > 0) {
                        playAlbumTrack(mActivity, albumItem, 0, false);
                    } else {
                        //如果服务端没有返回声音列表，容错处理，从专辑第一个声音开始播放
                        playAlbumFirstTrack(albumItem);
                    }
                }
            }
        });

        //设置item点击
        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AlbumEventManage.startMatchAlbumFragment(albumItem, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, albumItem.getRecSrc(), albumItem.getRecTrack(), -1, mActivity);
                notifyItemAction(albumItem, IRecommendFeedItemActionListener.ActionType.CLICK, recommendItem, t);
            }
        });
        //处理rv声音列表
        bindAlbumTrackRecyclerView(albumItem, viewHolder);
    }

    /**
     * @param albumItem
     * @param viewHolder
     */
    private void bindAlbumTrackRecyclerView(RecommendAlbumItem albumItem, ViewHolder viewHolder) {
        if (viewHolder.mAlbumTrackList.getAdapter() == null) {
            viewHolder.mAlbumTrackList.setAdapter(new HomeFreeAlbumTrackRvAdapter(mActivity, new ArrayList<>()));
            //禁止滑动
            viewHolder.mAlbumTrackList.setNestedScrollingEnabled(false);
            viewHolder.mAlbumTrackList.setLayoutManager(new LinearLayoutManager(mActivity));
            viewHolder.mAlbumTrackList.setItemViewCacheSize(3);
        }
        if (!(viewHolder.mAlbumTrackList.getAdapter() instanceof HomeFreeAlbumTrackRvAdapter)) {
            return;
        }
        HomeFreeAlbumTrackRvAdapter albumTrackListAdapter = (HomeFreeAlbumTrackRvAdapter) viewHolder.mAlbumTrackList.getAdapter();
        List<TrackM> valueList = albumTrackListAdapter.getValueList();
        List<TrackM> topTracks = albumItem.getTopTracks();
        //无数据的时候也需要清除，防止上次的的声音view被复用
        valueList.clear();
        if (topTracks != null) {
            valueList.addAll(topTracks);
        }
        albumTrackListAdapter.updateRecommendAlbumItem(albumItem);
        albumTrackListAdapter.notifyDataSetChanged();
    }

    /**
     * 播放专辑第一首声音，不打开专辑页，不打开播放页
     */
    public static void playAlbumTrack(Activity activity, RecommendAlbumItem album, int playIndex, boolean isOpenPlayPage) {
        if (album == null) {
            return;
        }
        List<TrackM> topTracks = album.getTopTracks();
        if (topTracks == null || topTracks.size() <= 0) {
            return;
        }
        if (playIndex < 0 || playIndex >= topTracks.size()) {
            playIndex = 0;
        }
        TrackM trackM = topTracks.get(playIndex);
        if (trackM == null) {
            return;
        }
        PlayTools.playTrackHistoyWithAsc(activity, trackM.getDataId(), album.getId(), null, ConstantsOpenSdk.PLAY_FROM_NONE, isOpenPlayPage, album.isAsc);
    }


    /**
     * 播放专辑第一首声音，不打开专辑页，不打开播放页
     */
    private void playAlbumFirstTrack(Album album) {
        if (album == null) {
            return;
        }
        Activity activity = BaseApplication.getTopActivity();
        if (activity == null) {
            return;
        }
        PlayTools.playByAlbumByIdIfHasHistoryUseHistory(mActivity, album.getId(), null);
    }

    private boolean isCurrentAlbumTrack(long albumId) {
        PlayableModel curModel = XmPlayerManager.getInstance(mActivity).getCurrSound();
        if (curModel == null) {
            return false;
        }
        if (curModel.getDataId() <= 0) {
            return false;
        }
        if (!(curModel instanceof Track)) {
            return false;
        }
        SubordinatedAlbum album = ((Track) curModel).getAlbum();
        if (album == null) {
            return false;
        }
        return album.getAlbumId() == albumId;
    }

    private boolean isCurrentAlbumTrackPlaying(long albumId) {
        return isCurrentAlbumTrack(albumId) && XmPlayerManager.getInstance(mActivity).isPlaying();
    }

    private void notifyItemAction(RecommendAlbumItem album, IRecommendFeedItemActionListener.ActionType actionType, RecommendItemNew itemData, ItemModel itemModel) {
        if (recommendFeedItemActionListener != null && album != null) {
            recommendFeedItemActionListener.onItemAction(IRecommendFeedItemActionListener.FeedItemType.ALBUM, album.getId(), actionType, album.getCategoryId(), itemData, itemModel);
        }
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_album_home_feed_album_v3, null);
    }

    @Override
    public ViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    public class ViewHolder extends BaseAlbumAdapter.ViewHolder {
        private ImageView offSale; //下架icon
        private ImageView ivTag;
        ViewGroup ivPlayBtnContainer;
        ImageView ivPlayBtn;
        TextView mAlbumInfoFirst;
        TextView mAlbumInfoSecond;
        RecyclerView mAlbumTrackList;

        public ViewHolder(View convertView) {
            super(convertView);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
            cover = (ImageView) convertView.findViewById(R.id.main_iv_album_cover);
            border = convertView.findViewById(R.id.main_album_border);
            title = (TextView) convertView.findViewById(R.id.main_tv_album_title);
            subtitle = (TextView) convertView.findViewById(R.id.main_tv_album_subtitle);
            offSale = (ImageView) convertView.findViewById(R.id.main_iv_off_sale);
            ivPlayBtn = convertView.findViewById(R.id.main_iv_play_btn);
            ivPlayBtnContainer = convertView.findViewById(R.id.main_layout_play_btn_container);
            mAlbumInfoFirst = convertView.findViewById(R.id.main_tv_album_info_first);
            mAlbumInfoSecond = convertView.findViewById(R.id.main_tv_album_info_second);
            mAlbumTrackList = convertView.findViewById(R.id.main_rv_album_track_list);
        }
    }
}
