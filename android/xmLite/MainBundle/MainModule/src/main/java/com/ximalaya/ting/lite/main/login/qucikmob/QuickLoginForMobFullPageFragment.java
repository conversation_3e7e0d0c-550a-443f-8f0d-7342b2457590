/**
 * LoginFragment.java
 * com.ximalaya.ting.android.host.mSmsLoginFragment.other.login
 * <p/>
 * Function： TODO
 * <p/>
 * ver     date      		author
 * ──────────────────────────────────
 * 2015-11-25 		jack.qin
 * <p/>
 * Copyright (c) 2015, TNT All Rights Reserved.
 */
package com.ximalaya.ting.lite.main.login.qucikmob;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;


import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.login.LoginPageTraceManager;
import com.ximalaya.ting.android.host.manager.login.QuickLoginLogger;
import com.ximalaya.ting.android.host.manager.login.mobquick.IMobLoginCallBack;
import com.ximalaya.ting.android.host.manager.login.mobquick.MobLoginParams;
import com.ximalaya.ting.android.host.manager.login.mobquick.MobQuickManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.util.common.TextFontManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.login.BaseLoginFragment;
import com.ximalaya.ting.lite.main.login.LoginArgeementView;

/**
 * <AUTHOR> on 2017/6/29.
 */
public class QuickLoginForMobFullPageFragment extends BaseLoginFragment {

    private TextView mTvPhoneNumber;
    private TextView mTvOperator;
    private TextView mTvLoginBtn;
    private ViewGroup mLayoutOtherLogin;
    private MobLoginParams mMobLoginParams;
    private LoginArgeementView mLoginAgreementView;


    @SuppressWarnings("NullAway")
    public QuickLoginForMobFullPageFragment() {
        super(true, null);
    }

    public static QuickLoginForMobFullPageFragment newInstance(Bundle bundle) {
        QuickLoginForMobFullPageFragment loginFragment = new QuickLoginForMobFullPageFragment();
        loginFragment.setArguments(bundle);
        return loginFragment;
    }

    @Override
    protected String getPageLogicName() {
        return "QuickLoginForMobFullPageFragment";
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_login_for_quick_mob_full;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_head_layout;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mMobLoginParams = LoginBundleParamsManager.getQuickMobParams(getArguments());
        //参数传递错误
        if (mMobLoginParams == null) {
            UserInfoMannage.loginBySelfPage(mContext, LoginBundleParamsManager.getLoginBy(getArguments()), getArguments());
            finish();
            return;
        }
        setTitle(LoginBundleParamsManager.getLoginTitle(getArguments()));

        initView();

        LoginPageTraceManager.traceLoginPageShow(true, true);
    }

    private void initView() {
        mLoginAgreementView = findViewById(R.id.main_view_login_agreement_view);
        //配置需要在initUi之前进行设置
        mLoginAgreementView.setQuickProtocolConfig(mMobLoginParams.protocolName, mMobLoginParams.protocolUrl);
        mLoginAgreementView.initUi(getActivity(), getArguments());

        mTvPhoneNumber = findViewById(R.id.main_mob_tv_phone_number);
        mTvOperator = findViewById(R.id.main_mob_tv_operator);
        mTvLoginBtn = findViewById(R.id.main_mob_tv_quick_login);
        mLayoutOtherLogin = findViewById(R.id.main_mob_quick_other_login);

        //设置din字体
        TextFontManager.setFontForDIN_Alternate_Bold(mTvPhoneNumber);
        //设置手机号码
        mTvPhoneNumber.setText(mMobLoginParams.number);
        //设置运营商认证信息
        if (!TextUtils.isEmpty(mMobLoginParams.protocolName) && mMobLoginParams.protocolName.contains("联通")) {
            mTvOperator.setText("中国联通提供认证服务");
        } else if (!TextUtils.isEmpty(mMobLoginParams.protocolName) && mMobLoginParams.protocolName.contains("移动")) {
            mTvOperator.setText("中国移动提供认证服务");
        } else if (!TextUtils.isEmpty(mMobLoginParams.protocolName) && mMobLoginParams.protocolName.contains("电信")) {
            mTvOperator.setText("中国电信提供认证服务");
        }
        //去登录点击
        mTvLoginBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                //弹出选中提醒
                if (!mLoginAgreementView.checkAgreementSelectedAndShowHint()) {
                    return;
                }
                //登录点击埋点，在校验之后
                LoginPageTraceManager.traceLoginBtnClick(true, true);
                QuickLoginLogger.log("全屏登录界面=====开始登录===");
                //进行一键登录
                //展示loading
                showLoginProgress(mActivity);
                MobQuickManager.gotoMobQuickLogin(getArguments(),new IMobLoginCallBack() {
                    @Override
                    public void onMobLoginSuccess(LoginInfoModelNew loginInfoModelNew) {
                        //隐藏loading
                        dismissLoginProgress();
                        //登录成功了，关闭当前页面
                        finish();

                        QuickLoginLogger.log("全屏登录界面=====开始登录=success=");
                        if (loginInfoModelNew != null) {
                            QuickLoginLogger.log("全屏登录界面=====开始登录=success=getUid=" + loginInfoModelNew.getUid());
                            QuickLoginLogger.log("全屏登录界面=====开始登录=success=getNickname=" + loginInfoModelNew.getNickname());
                        }
                    }

                    @Override
                    public void onMobLoginFailure(int code, String message) {
                        //隐藏loading
                        dismissLoginProgress();
                        //toast提示
                        CustomToast.showFailToast("登录失败，已自动切换为验证码登录");
                        //切换到短信登录
                        UserInfoMannage.loginBySelfPage(mContext, LoginBundleParamsManager.getLoginBy(getArguments()), getArguments());
                        //关闭当前页面
                        finish();
                        QuickLoginLogger.log("全屏登录界面=====开始登录=error=" + code + " msg=" + message);
                    }
                });
            }
        });
        //切换到其他方式登录
        mLayoutOtherLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                UserInfoMannage.loginBySelfPage(mContext, LoginBundleParamsManager.getLoginBy(getArguments()), getArguments());
                //关闭当前页面
                finish();
                //埋点
                LoginPageTraceManager.traceLoginSelectOtherLoginClick(true);
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    @Override
    protected void loadData() {

    }

    @Override
    public void finish() {
        super.finish();
        if (mActivity != null) {
            mActivity.finish();
            mActivity.overridePendingTransition(0, 0);
        }
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        UserInfoMannage.fromLoginUrl = null;
    }
}
