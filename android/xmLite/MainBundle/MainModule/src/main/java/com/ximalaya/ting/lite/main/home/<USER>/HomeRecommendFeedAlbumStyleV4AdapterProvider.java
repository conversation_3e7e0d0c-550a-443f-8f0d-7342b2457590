package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import android.graphics.drawable.Drawable;
import android.text.Html;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ImageSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.TagResult;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.util.view.VerticalImageSpan;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;
import com.ximalaya.ting.lite.main.model.album.RecommendAlbumItem;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qinhuifeng on 2019-07-31
 * <p>
 * 信息流专辑样式V4版本，带评分的item对应的uiType=3
 *
 * <AUTHOR>
 */
public class HomeRecommendFeedAlbumStyleV4AdapterProvider implements IMulitViewTypeViewAndData<HomeRecommendFeedAlbumStyleV4AdapterProvider.ViewHolder, RecommendItemNew> {
    private Activity mActivity;
    protected BaseFragment2 mFragment;
    private HomeRecommedExtraDataProvider dataProvider;
    private IRecommendFeedItemActionListener recommendFeedItemActionListener;
    private Drawable pointDrawable = null;

    public HomeRecommendFeedAlbumStyleV4AdapterProvider(BaseFragment2 baseFragment2, HomeRecommedExtraDataProvider dataProvider, IRecommendFeedItemActionListener recommendFeedItemActionListener) {
        mFragment = baseFragment2;
        mActivity = baseFragment2.getActivity();
        this.dataProvider = dataProvider;
        this.recommendFeedItemActionListener = recommendFeedItemActionListener;

        pointDrawable = ContextCompat.getDrawable(baseFragment2.requireContext(), R.drawable.main_icon_common_point);
        if (pointDrawable != null) {
            int width = pointDrawable.getIntrinsicWidth();
            int height = pointDrawable.getIntrinsicHeight();
            pointDrawable.setBounds(0, 0, width, height);
        }
    }

    @Override
    public void bindViewDatas(ViewHolder viewHolder, final ItemModel<RecommendItemNew> t, View convertView, final int position) {
        if (t == null || t.object == null) {
            return;
        }
        final RecommendItemNew recommendItem = (RecommendItemNew) t.getObject();
        Object item = t.object.getItem();
        if (!(item instanceof RecommendAlbumItem)) {
            return;
        }
        final RecommendAlbumItem albumItem = (RecommendAlbumItem) item;
        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, albumItem);
        //添加专辑Item的ContentDescription
        if (viewHolder.root != null) {
            if (!TextUtils.isEmpty(albumItem.getAlbumTitle())) {
                viewHolder.root.setContentDescription(albumItem.getAlbumTitle());
            } else {
                viewHolder.root.setContentDescription("");
            }
        }

        //设置专辑图
        ImageManager.from(mActivity).displayImage(viewHolder.cover, albumItem.getLargeCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145, com.ximalaya.ting.android.host.R.drawable.host_default_album_145);

        //设置vip等角标
        if (AlbumTagUtil.getAlbumCoverTag(albumItem) != -1) {
            viewHolder.ivTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable(albumItem, mActivity, AlbumTagUtil.ZOOM_IN_RATIO_78_percent));
            viewHolder.ivTag.setVisibility(View.VISIBLE);
        } else {
            viewHolder.ivTag.setVisibility(View.INVISIBLE);
        }

        //设置标题，完播等标签
        int textSize = (int) viewHolder.title.getTextSize();
        Spanned richTitle = AlbumAdapter.getRichTitle(albumItem, mActivity, textSize);
        viewHolder.title.setText(richTitle);

        //设置副标题
        String subTitle = albumItem.getIntro();
        if (!TextUtils.isEmpty(subTitle)) {
            viewHolder.subtitle.setText(Html.fromHtml(subTitle));
        } else {
            viewHolder.subtitle.setText("");
        }


        //设置播放量，使用第1个位置
        String playCountStr = StringUtil.getFriendlyNumStr(albumItem.getPlayCount()) + "播放";
        viewHolder.mAlbumInfoFirst.setText(playCountStr);
        viewHolder.mAlbumInfoFirst.setCompoundDrawables(LocalImageUtil.getDrawable(mActivity, R.drawable.main_ic_common_play_count), null, null, null);

        //展示标签列表
        SpannableString albumTagSourceListString = getAlbumTagSourceListString(albumItem);
        if (!TextUtils.isEmpty(albumTagSourceListString)) {
            //有分类标签的时候展示分类标签
            viewHolder.mAlbumTagSourceList.setText(albumTagSourceListString);
            viewHolder.mAlbumTagSourceList.setVisibility(View.VISIBLE);

            viewHolder.mAlbumInfoSecond.setVisibility(View.GONE);
        } else {
            //2个位置隐藏
            viewHolder.mAlbumTagSourceList.setVisibility(View.GONE);

            //没有分类标签的时候，展示集数
            String albumCountStr = StringUtil.getFriendlyNumStr(albumItem.getIncludeTrackCount()) + " 集";
            viewHolder.mAlbumInfoSecond.setCompoundDrawables(LocalImageUtil.getDrawable(mActivity, R.drawable.main_ic_common_track_count), null, null, null);
            viewHolder.mAlbumInfoSecond.setText(albumCountStr);
            viewHolder.mAlbumInfoSecond.setVisibility(View.VISIBLE);
        }

        //设置专辑评分
        String albumScore = albumItem.getAlbumScore();
        if (TextUtils.isEmpty(albumScore) ||
                "0".equals(albumScore) ||
                "0.0".equals(albumScore) ||
                "0.00".equals(albumScore)
        ) {
            viewHolder.mAlbumScore.setText("");
            viewHolder.mAlbumScore.setVisibility(View.GONE);
        } else {
            viewHolder.mAlbumScore.setText(albumScore + "分");
            viewHolder.mAlbumScore.setVisibility(View.VISIBLE);
        }


        //设置item点击
        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AlbumEventManage.startMatchAlbumFragment(albumItem, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, albumItem.getRecSrc(), albumItem.getRecTrack(), -1, mActivity);
                notifyItemAction(albumItem, IRecommendFeedItemActionListener.ActionType.CLICK, recommendItem, t);
            }
        });
    }


    /**
     * 获取标签列表的数据
     */
    private SpannableString getAlbumTagSourceListString(RecommendAlbumItem albumItem) {
        List<TagResult> tagResults = albumItem.getTagResults();
        if (tagResults == null || tagResults.size() == 0) {
            return null;
        }
        List<Integer> pointPosition = new ArrayList<>();
        StringBuilder tagBuilder = new StringBuilder();
        String point = "•";
        for (int i = 0; i < tagResults.size(); i++) {
            TagResult tagResult = tagResults.get(i);
            if (tagResult == null) {
                continue;
            }
            if (TextUtils.isEmpty(tagResult.getTagName())) {
                continue;
            }
            tagBuilder.append(tagResult.getTagName());
            if (i != tagResults.size() - 1) {
                tagBuilder.append(" ");
                pointPosition.add(tagBuilder.toString().length());
                tagBuilder.append(point)
                        .append(" ");

            }
        }
        String content = tagBuilder.toString();
        if (TextUtils.isEmpty(content)) {
            return null;
        }
        SpannableString spannableString = new SpannableString(content);
        for (int j = 0; j < pointPosition.size(); j++) {
            int start = pointPosition.get(j);
            int end = start + point.length();
            if (start >= 0 && end >= 0 && start <= content.length() && end <= content.length()) {
                //点在屏幕上画出来不是圆的，修改字体大小会影响整体高度，使用图片替换点展示
                if (pointDrawable != null) {
                    VerticalImageSpan imageSpan = new VerticalImageSpan(pointDrawable);
                    spannableString.setSpan(imageSpan, start, end, ImageSpan.ALIGN_BASELINE);
                }
            }
        }
        return spannableString;
    }

    /**
     * 播放专辑第一首声音，不打开专辑页，不打开播放页
     */
    private void playAlbumFirstTrack(Album album) {
        if (album == null) {
            return;
        }
        Activity activity = BaseApplication.getTopActivity();
        if (activity == null) {
            return;
        }
        PlayTools.playByAlbumByIdIfHasHistoryUseHistory(mActivity, album.getId(), null);
    }

    private boolean isCurrentAlbumTrack(long albumId) {
        PlayableModel curModel = XmPlayerManager.getInstance(mActivity).getCurrSound();
        if (curModel == null) {
            return false;
        }
        if (curModel.getDataId() <= 0) {
            return false;
        }
        if (!(curModel instanceof Track)) {
            return false;
        }
        SubordinatedAlbum album = ((Track) curModel).getAlbum();
        if (album == null) {
            return false;
        }
        return album.getAlbumId() == albumId;
    }

    private boolean isCurrentAlbumTrackPlaying(long albumId) {
        return isCurrentAlbumTrack(albumId) && XmPlayerManager.getInstance(mActivity).isPlaying();
    }

    private void notifyItemAction(RecommendAlbumItem album, IRecommendFeedItemActionListener.ActionType actionType, RecommendItemNew itemData, ItemModel itemModel) {
        if (recommendFeedItemActionListener != null && album != null) {
            recommendFeedItemActionListener.onItemAction(IRecommendFeedItemActionListener.FeedItemType.ALBUM, album.getId(), actionType, album.getCategoryId(), itemData, itemModel);
        }
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_album_home_feed_album_v4, null);
    }

    @Override
    public ViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    public class ViewHolder extends BaseAlbumAdapter.ViewHolder {
        private ImageView offSale; //下架icon
        private ImageView ivTag;
        TextView mAlbumInfoFirst;
        TextView mAlbumInfoSecond;
        //专辑评分
        TextView mAlbumScore;
        //来源标签列表
        TextView mAlbumTagSourceList;

        public ViewHolder(View convertView) {
            super(convertView);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
            cover = (ImageView) convertView.findViewById(R.id.main_iv_album_cover);
            border = convertView.findViewById(R.id.main_album_border);
            title = (TextView) convertView.findViewById(R.id.main_tv_album_title);
            subtitle = (TextView) convertView.findViewById(R.id.main_tv_album_subtitle);
            offSale = (ImageView) convertView.findViewById(R.id.main_iv_off_sale);
            mAlbumInfoFirst = convertView.findViewById(R.id.main_tv_album_info_first);
            mAlbumInfoSecond = convertView.findViewById(R.id.main_tv_album_info_second);
            mAlbumScore = convertView.findViewById(R.id.main_album_score);
            mAlbumTagSourceList = convertView.findViewById(R.id.main_tag_source_list);
        }
    }
}
