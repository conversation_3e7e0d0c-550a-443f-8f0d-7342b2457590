package com.ximalaya.ting.lite.main.model.album

import com.ximalaya.ting.lite.main.model.vip.VipTabModel

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/7/6
 *
 * Desc: 补充搜索
 */

class ReplenishSearchResponse(
        var ret: Int,
        var msg: String,
        var data: ReplenishSearchData?
)

class ReplenishSearchData {
    var moduleId: Int? = null
    var moduleType: Int? = null
    var list: List<ReplenishSearchModel>? = null
}

class ReplenishSearchModel {

    companion object {
        const val TYPE_SEARCH = "search"
        const val TYPE_CATEGORY = "category"
    }

    var type: String? = null //搜索词类型：search-跳搜索，category-跳分类页
    var categoryId: Int? = null//分类页Id
    var word: String? = null //interest word

}