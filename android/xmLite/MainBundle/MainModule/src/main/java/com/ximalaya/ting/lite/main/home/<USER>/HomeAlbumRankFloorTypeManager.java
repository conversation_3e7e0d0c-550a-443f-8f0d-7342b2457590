package com.ximalaya.ting.lite.main.home.manager;

import android.text.TextUtils;

import androidx.annotation.IdRes;

import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendAdapter;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeAlbumRankFloorViewModel;
import com.ximalaya.ting.lite.main.model.album.HomeAlbumRankItem;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.Queue;

/**
 * Created by qinhuifeng on 2021/1/24
 * <p>
 * 排行榜支持多个楼层，最多支持5个楼层
 *
 * <AUTHOR>
 */
public class HomeAlbumRankFloorTypeManager {
    private Queue<Integer> canUseTypeQueue = new LinkedList<>();
    private Map<String, Integer> hasUseTypeMap = new HashMap<>();

    public HomeAlbumRankFloorTypeManager() {
        //使用队列操作，禁止在之前添加VIEW_TYPE_ALBUM_RANK_1必须子啊队列头部
        canUseTypeQueue.add(HomeRecommendAdapter.VIEW_TYPE_ALBUM_RANK_1);
        canUseTypeQueue.add(HomeRecommendAdapter.VIEW_TYPE_ALBUM_RANK_2);
        canUseTypeQueue.add(HomeRecommendAdapter.VIEW_TYPE_ALBUM_RANK_3);
        canUseTypeQueue.add(HomeRecommendAdapter.VIEW_TYPE_ALBUM_RANK_4);
        canUseTypeQueue.add(HomeRecommendAdapter.VIEW_TYPE_ALBUM_RANK_5);
        //新增类型必须添加在之后，必须按照顺序添加，不按照顺序添加会造成可视化埋点出现混乱
    }

    /**
     * 获取viewPager排行榜的第二tag
     * 使用排行榜前两个唯一id作为tag
     */
    public int getCanUseAlbumRankFloorType(HomeAlbumRankFloorViewModel model) {
        String albumRankOnlyId = getAlbumRankOnlyId(model);
        if (TextUtils.isEmpty(albumRankOnlyId)) {
            return -1;
        }
        //已经使用了，直接返回对应的type
        Integer integer = hasUseTypeMap.get(albumRankOnlyId);
        if (integer != null && integer > 0) {
            return integer;
        }
        //还没使用，从队列里获取一个

        //如果队列中的全部使用完了，后续不再支持新的楼层
        if (canUseTypeQueue.isEmpty()) {
            return -1;
        }
        Integer floorType = canUseTypeQueue.poll();
        if (floorType != null) {
            hasUseTypeMap.put(albumRankOnlyId, floorType);
            return floorType;
        }
        return -1;
    }

    /**
     * 获取viewPager排行榜的第二tag
     * 使用排行榜前两个唯一id作为唯一标识
     */
    private String getAlbumRankOnlyId(HomeAlbumRankFloorViewModel model) {
        if (model == null || model.homeAlbumRankItemList == null || model.homeAlbumRankItemList.size() == 0) {
            return "";
        }
        HomeAlbumRankItem item = model.homeAlbumRankItemList.get(0);
        if (item == null) {
            return "";
        }
        //使用排行榜前两个作为第二个tag
        StringBuilder builder = new StringBuilder();
        builder.append(item.rankingListId);
        if (model.homeAlbumRankItemList.size() > 1) {
            HomeAlbumRankItem item1 = model.homeAlbumRankItemList.get(1);
            if (item1 != null) {
                builder.append("_")
                        .append(item1.rankingListId);
            }
        }
        String secondTag = builder.toString();
        return secondTag;
    }

    @IdRes
    public static int getViewPagerOnlyIdInFragment(int floorViewType) {
        if (floorViewType == HomeRecommendAdapter.VIEW_TYPE_ALBUM_RANK_1) {
            return R.id.main_home_album_rank_viewpager_id_1;
        }
        if (floorViewType == HomeRecommendAdapter.VIEW_TYPE_ALBUM_RANK_2) {
            return R.id.main_home_album_rank_viewpager_id_2;
        }
        if (floorViewType == HomeRecommendAdapter.VIEW_TYPE_ALBUM_RANK_3) {
            return R.id.main_home_album_rank_viewpager_id_3;
        }
        if (floorViewType == HomeRecommendAdapter.VIEW_TYPE_ALBUM_RANK_4) {
            return R.id.main_home_album_rank_viewpager_id_4;
        }
        if (floorViewType == HomeRecommendAdapter.VIEW_TYPE_ALBUM_RANK_5) {
            return R.id.main_home_album_rank_viewpager_id_5;
        }
        return -1;
    }
}
