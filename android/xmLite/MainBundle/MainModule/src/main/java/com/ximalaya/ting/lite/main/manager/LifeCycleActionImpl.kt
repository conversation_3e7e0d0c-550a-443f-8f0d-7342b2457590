package com.ximalaya.ting.lite.main.manager

import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.ILifeCycleAction
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.lite.main.play.manager.CalculatePlayTimeManager
import com.ximalaya.ting.lite.main.shortplay.utils.ShortPlayConfigUtil
import com.ximalaya.ting.lite.main.utils.ShareUtils

object LifeCycleActionImpl : ILifeCycleAction {


    override fun onCreate() {
        CoinStyleManager.init(BaseApplication.sInstance.application)

        // 存储首页广告开光状态
        HandlerManager.postOnUIThreadDelay({
            HomeFeedAdABManager.saveHomeAdSwitch()
            ListenerTimeTaskManager.cacheConfigData()
            Router.getReadActionRouter()?.functionAction?.cacheConfig()
            ShortPlayConfigUtil.cacheConfig()
        }, 2000)

        CalculatePlayTimeManager.init()
        ListenerTimeTaskManager.init()
    }

    override fun onDestroy() {
        CoinStyleManager.destroy(BaseApplication.sInstance.application)

        HomeFeedAdManager.destroy()
        ShareUtils.destroy()
        CalculatePlayTimeManager.destroy()
        ListenerTimeTaskManager.onDestroy()
    }
}