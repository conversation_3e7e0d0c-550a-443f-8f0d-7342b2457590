package com.ximalaya.ting.lite.main.manager

import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt
import com.ximalaya.ting.android.host.manager.LiteEncryptManager
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew
import com.ximalaya.ting.android.host.manager.UnlockTimeTrackManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.ContextUtils
import com.ximalaya.ting.android.host.util.common.DeviceUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.view.ComProgressDialog
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.free.FreeModelGuideDetailFragment
import com.ximalaya.ting.lite.main.free.FreeModelGuideDialog
import com.ximalaya.ting.lite.main.free.ad.FreeModelAdHelper
import com.ximalaya.ting.lite.main.free.ad.FreeModelAdListener
import com.ximalaya.ting.lite.main.model.CoinExchangeModel
import com.ximalaya.ting.lite.main.model.RewardVideoExchangeModel
import com.ximalaya.ting.lite.main.model.newhome.LiteTabModel
import com.ximalaya.ting.lite.main.playnew.dialog.UnlockListenTimeDialog
import org.json.JSONObject


object HomeFreeModelGuideManager {

    private const val TAG = "HomeFreeModelGuideManager"

    //避免重复添加fragment，因为广告涉及的sdk和回掉有点多
    private var mHasAddFreeFragment = false

    private fun isAllowShowFreeModelGuideView(): Boolean {
        return ConfigureCenter.getInstance().getBool(
            CConstants.Group_Base.GROUP_NAME,
            CConstants.Group_Base.ITEM_SHOW_ALL_FREE_PAGE,
            true
        )
    }

    private fun isAllowShowFreeModelDialog(): Boolean {
        return ConfigureCenter.getInstance().getBool(
            CConstants.Group_Base.GROUP_NAME,
            CConstants.Group_Base.ITEM_SHOW_ALL_FREE_DIALOG,
            true
        )
    }

    @JvmStatic
    fun showFreeModelView(rootView: ViewGroup?, callback: IGetTabCallBack) {
        if (rootView == null || !ContextUtils.checkActivity(rootView.context)) {
            return
        }
        if (!UserInfoMannage.hasLogined()) {
            //未登录，使用现有逻辑
            if (!UnlockListenTimeManagerNew.isNoLoginCanShowUnlockGuideTipsAndButton()) {
                try {
                    rootView.removeAllViews()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                return
            }
        } else {
            //已登录使用原有逻辑
            if (!UnlockListenTimeManagerNew.isOpenUnlockTime() || UnlockListenTimeManagerNew.unlockConfigModel == null || UnlockListenTimeManagerNew.isNewDevice() || UserInfoMannage.isVipUser() || !isAllowShowFreeModelGuideView()) {
                try {
                    rootView.removeAllViews()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                return
            }
        }

        val freeView = rootView.findViewById<View?>(R.id.main_ll_free_model)

        var visible = View.VISIBLE
        // 短剧界面隐藏免费畅听
        val tab = callback.getCurTab()
        if (tab?.pageType == LiteTabModel.TYPE_CSJ_DUANJU || tab?.pageType == LiteTabModel.TYPE_CSJ_DUANJU_FEED) {
            visible = View.GONE
            // 未初始化直接拦截不往下走了
            if (freeView == null) {
                return
            }
        }

        val context = rootView.context

        if (freeView != null) {
            freeView.visibility = visible
            return
        }
        rootView.removeAllViews()

        try {
            LayoutInflater.from(context).inflate(R.layout.main_fra_home_free_model_layout, rootView)

            rootView.findViewById<View?>(R.id.main_ll_free_model)?.setOnClickListener{ v ->
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return@setOnClickListener
                }
                //没有登陆
                if (!UserInfoMannage.hasLogined()) {
                    val activity = BaseApplication.getMainActivity()
                    UserInfoMannage.gotoLogin(activity);
                    return@setOnClickListener
                }
                //判断是否直接跳转激励视频
                mHasAddFreeFragment = false
                if (FreeModelAdHelper.isDirectOpenAd(true)) {
                    //直接看激励视频广告
                    toDirectAdVideo()
                } else {
                    toOpenFreeFragment()
                }
                FreeModelAdHelper.addClickFreeAdCount(true)
                // 新首页-免费畅听入口  点击事件
                XMTraceApi.Trace()
                    .click(51201) // 用户点击时上报
                    .put("currPage", "homePageV2")
                    .createTrace()
            }

            // 新首页-免费畅听入口  控件曝光
            XMTraceApi.Trace()
                .setMetaId(51202)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "homePageV2")
                .put("exploreType", "homePageV2") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                .createTrace()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    private fun toDirectAdVideo() {
        FreeModelAdHelper.requestAd(true, false, object : FreeModelAdListener {
            override fun onFailure() {
                super.onFailure()
                toOpenFreeFragment()
            }

            override fun onSuccess() {
                super.onSuccess()
                toOpenFreeFragment()
                FreeModelAdHelper.onAdPlayCompleteSuccess()
            }
        })
    }


    private fun toOpenFreeFragment() {
        val activity = BaseApplication.getMainActivity()
        if (activity is MainActivity) {
            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(activity);
                return
            }
            if(!mHasAddFreeFragment){
                mHasAddFreeFragment = true
                activity.startFragment(FreeModelGuideDetailFragment())
            }
        }
    }

    @JvmStatic
    fun showGuideDialog() {
        if (!UnlockListenTimeManagerNew.isOpenUnlockTime() || UnlockListenTimeManagerNew.unlockConfigModel == null || UnlockListenTimeManagerNew.isNewDevice() || UserInfoMannage.isVipUser()) {
            FuliLogger.log(
                TAG,
                "全免费开关关闭or vip用户or新设备 ${UnlockListenTimeManagerNew.unlockConfigModel}"
            )
            return
        }

        val activity = BaseApplication.getMainActivity()
        if (!ContextUtils.checkActivity(activity)) {
            return
        }

        val isShow = MmkvCommonUtil.getInstance(activity?.application)
            .getBoolean("key_free_model_dialog_show", false)
        if (isShow) {
            FuliLogger.log(TAG, "已显示不再显示")
            return
        }

        if (!isAllowShowFreeModelDialog()) {
            FuliLogger.log(TAG, "开关关闭")
            return
        }

        if (activity is FragmentActivity) {
            try {
                if (UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(activity);
                    return;
                }
                FreeModelGuideDialog().show(activity.supportFragmentManager, "FreeModelGuideDialog")
                MmkvCommonUtil.getInstance(activity?.application)
                    .getBoolean("key_free_model_dialog_show", true)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private var progressDialog: ComProgressDialog? = null

    fun performRewardVideoExchangeListenTime(
        needToast: Boolean,
        callBack: UnlockListenTimeDialog.IRequestCallBack<RewardVideoExchangeModel>?
    ) {
        val activity = BaseApplication.getMainActivity()
        if (progressDialog?.isShowing == true || !ContextUtils.checkActivity(activity)) {
            return
        }
        if (progressDialog == null) {
            progressDialog = ComProgressDialog(activity)
        }
        progressDialog?.show()

        requestRewardVideoExchangeListenTime(needToast, object :
            UnlockListenTimeDialog.IRequestCallBack<RewardVideoExchangeModel?> {
            override fun onResult(result: RewardVideoExchangeModel?) {
                if (result != null) {
                    callBack?.onResult(result)
                    UnlockListenTimeManagerNew.performSaveUnlockTime(false)
                }
                progressDialog?.dismiss()
                progressDialog = null
            }
        })
    }


    private fun requestRewardVideoExchangeListenTime(
        needToast: Boolean,
        callBack: UnlockListenTimeDialog.IRequestCallBack<RewardVideoExchangeModel?>?
    ) {
        val config = UnlockListenTimeManagerNew.unlockConfigModel
        val map = mutableMapOf<String, String?>()
        val context = BaseApplication.getMyApplicationContext()
        map["deviceId"] = DeviceUtil.getDeviceToken(context)
        map["timestamp"] = System.currentTimeMillis().toString()
        map["uid"] = UserInfoMannage.getUid().toString()
        map["signature"] =
            LiteEncryptManager.getActivitySignature(BaseApplication.getMainActivity(), map)
        map.remove("uid")

        val url =
            UrlConstants.getInstanse().serverNetAddressHost + "lite-mobile/audi/func/v1/videoExchange"

        val startTime = SystemClock.elapsedRealtime()
        CommonRequestM.basePostRequestParmasToJson(
            url,
            map,
            object : IDataCallBack<RewardVideoExchangeModel> {
                override fun onSuccess(result: RewardVideoExchangeModel?) {
                    if (result == null) {
                        ToastManager.showToast("领取时间失败")
                        callBack?.onResult(null)

                        UnlockTimeTrackManager.trackClick(
                            "激励视频兑换时长", SystemClock.elapsedRealtime() - startTime,
                            "兑换失败"
                        )
                    } else {
                        if (needToast) {
                            ToastManager.showToast("已领取${(config?.videoExchangeRateListenDuration ?: 0) / 60}分钟免费时长")
                        }
                        config?.availableListenTime =
                            UnlockListenTimeManagerNew.decryptTimeData(result.availableListenDuration)
                        FuliLogger.log(TAG, "领取时长成功:${result}")
                        callBack?.onResult(result)

                        UnlockTimeTrackManager.trackClick(
                            "激励视频兑换时长", SystemClock.elapsedRealtime() - startTime,
                            "兑换成功, return:${result.availableListenDuration}"
                        )
                    }
                }

                override fun onError(code: Int, message: String?) {
                    ToastManager.showToast(message ?: "领取时间失败")
                    val detail = "激励视频兑换时长失败: code:${code} message:$message"
                    FuliLogger.log(TAG, detail)
                    callBack?.onResult(null)

                    UnlockTimeTrackManager.trackClick(
                        "激励视频兑换时长", SystemClock.elapsedRealtime() - startTime,
                        detail
                    )
                }
            }) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            if (ret == 0) {
                JsonUtilKt.instance.toObjectOfType<RewardVideoExchangeModel>(
                    json.optString("data"),
                    object : TypeToken<RewardVideoExchangeModel>() {}.type
                )
            } else null
        }
    }

    fun performCoinExchangeListenTime(callBack: UnlockListenTimeDialog.IRequestCallBack<CoinExchangeModel>?) {
        val config = UnlockListenTimeManagerNew.unlockConfigModel
        if (config == null || config.availableCoinExchangedNum <= 0) {
            ToastManager.showToast("金币兑换收听时长今日已达上限")
            return
        }

        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(BaseApplication.mAppInstance)
            return
        }

        if (progressDialog?.isShowing == true) {
            return
        }

        val activity = BaseApplication.getTopActivity()
        if (ContextUtils.checkActivity(activity)) {
            DialogBuilder<DialogBuilder<*>>(activity)
                .setMessage("是否消耗${config.coinExchangeRateCoinNum}金币兑换${config.coinExchangeRateListenDuration / 60}分钟收听时长?")
                .setOkBtn("确定")
                .setCancelBtn("取消")
                .setOkBtn {
                    // 免费畅听主页面-金币兑换确认弹窗曝光  点击事件
                    XMTraceApi.Trace()
                        .click(51209) // 用户点击时上报
                        .put("currPage", "Allfreemainpage")
                        .createTrace()

                    if (progressDialog == null) {
                        progressDialog = ComProgressDialog(activity)
                    }
                    progressDialog?.show()

                    coinExchangeListenTime(object :
                        UnlockListenTimeDialog.IRequestCallBack<CoinExchangeModel?> {
                        override fun onResult(result: CoinExchangeModel?) {
                            if (result != null) {
                                config.availableCoinExchangedNum -= 1
                                callBack?.onResult(result)
                                UnlockListenTimeManagerNew.performSaveUnlockTime(false)

                                // 免费畅听主页面-金币兑换确认成功  控件曝光
                                XMTraceApi.Trace()
                                    .setMetaId(51211)
                                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                                    .put("currPage", "Allfreemainpage")
                                    .put(
                                        "exploreType",
                                        "Allfreemainpage"
                                    ) // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                                    .createTrace()
                            }
                        }
                    })

                }.showConfirm()

            // 免费畅听主页面-金币兑换确认弹窗曝光  控件曝光
            XMTraceApi.Trace()
                .setMetaId(51210)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "Allfreemainpage")
                .put("exploreType", "Allfreemainpage") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                .createTrace()
        }
    }

    /**
     * 金币兑换收听时长
     */
    private fun coinExchangeListenTime(callBack: UnlockListenTimeDialog.IRequestCallBack<CoinExchangeModel?>?) {
        val config = UnlockListenTimeManagerNew.unlockConfigModel
        val map = mutableMapOf<String, String?>()
        val context = BaseApplication.getMyApplicationContext()
        map["deviceId"] = DeviceUtil.getDeviceToken(context)
        map["timestamp"] = System.currentTimeMillis().toString()
        map["uid"] = UserInfoMannage.getUid().toString()
        map["signature"] =
            LiteEncryptManager.getActivitySignature(BaseApplication.getMainActivity(), map)
        map.remove("uid")

        val url =
            UrlConstants.getInstanse().serverNetAddressHost + "lite-mobile/audi/func/v1/coinExchange"

        val startTime = SystemClock.elapsedRealtime()
        CommonRequestM.basePostRequestParmasToJson(
            url,
            map,
            object : IDataCallBack<CoinExchangeModel> {
                override fun onSuccess(result: CoinExchangeModel?) {
                    if (result == null) {
                        ToastManager.showToast("兑换收听时长失败")
                        UnlockTimeTrackManager.trackClick(
                            "首页金币兑换",
                            SystemClock.elapsedRealtime() - startTime,
                            "兑换失败"
                        )
                    } else {
                        ToastManager.showToast("已领取${(config?.coinExchangeRateListenDuration ?: 0) / 60}分钟免费时长")
                        FuliLogger.log(TAG, "兑换时间成功:${result}")
                        // 更新可用时长和ui
                        config?.availableCoinExchangedNum = result.availableCoinExchangedNum
                        config?.availableListenTime =
                            UnlockListenTimeManagerNew.decryptTimeData(result.availableListenDuration)
                        callBack?.onResult(result)

                        UnlockTimeTrackManager.trackClick(
                            "首页金币兑换",
                            SystemClock.elapsedRealtime() - startTime,
                            "兑换成功, return:result${result.availableListenDuration}"
                        )
                    }
                    progressDialog?.dismiss()
                }

                override fun onError(code: Int, message: String?) {
                    ToastManager.showToast(message ?: "兑换收听时长失败")
                    val detail = "金币兑换收听时长失败: code:${code} message:${message}"
                    FuliLogger.log(TAG, detail)
                    progressDialog?.dismiss()
                    UnlockTimeTrackManager.trackClick(
                        "首页金币兑换",
                        SystemClock.elapsedRealtime() - startTime,
                        detail
                    )
                }
            }) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            if (ret == 0) {
                JsonUtilKt.instance.toObjectOfType<CoinExchangeModel>(
                    json.optString("data"),
                    object : TypeToken<CoinExchangeModel>() {}.type
                )
            } else null
        }
    }

    interface IGetTabCallBack {
        fun getCurTab(): LiteTabModel?
    }
}