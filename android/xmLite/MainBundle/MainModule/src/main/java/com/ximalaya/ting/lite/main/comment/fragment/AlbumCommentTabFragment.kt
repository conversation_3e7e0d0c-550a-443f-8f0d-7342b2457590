package com.ximalaya.ting.lite.main.comment.fragment

import android.graphics.Typeface
import android.os.Bundle
import android.view.View
import android.widget.*
import androidx.core.content.ContextCompat
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction.AbstractHomePageFragment
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction.HomeFragmentItingInterface
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.model.album.Album
import com.ximalaya.ting.lite.main.album.fragment.LiteAlbumFragment
import com.ximalaya.ting.lite.main.comment.CommentDetailDialogFragment
import com.ximalaya.ting.lite.main.comment.CommentListPresenter
import com.ximalaya.ting.lite.main.comment.manager.AlbumCommentTabFragmentManager
import com.ximalaya.ting.lite.main.tab.SimpleHomeFragment

/**
 * Created by dumingwei on 2021/11/18
 *
 * Desc: 极速听模式下 节目&小说tab界面
 */
class AlbumCommentTabFragment : AbstractHomePageFragment(), IMainFunctionAction.ICommentTabFragment,
    View.OnClickListener {

    companion object {
        private val TAG = AlbumCommentTabFragment::class.java.name
        const val ALBUM_KEY = "album"
        const val COMMENT_TOTAL_COUNTS = "COMMENT_TOTAL_COUNTS"
        fun newInstance(): AlbumCommentTabFragment {
            val args = Bundle()
            val fragment = AlbumCommentTabFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private lateinit var radioGroup: RadioGroup

    //听节目
    private lateinit var rbListenProgram: RadioButton

    //看小说
    private lateinit var rbReadBook: RadioButton

    private lateinit var flContent: FrameLayout

    private lateinit var tabFragmentManager: AlbumCommentTabFragmentManager

    private var isWhiteBgWhenReadNovelChecked = false
    private lateinit var tvCommentCount: TextView
    private var mAlbum: Album? = null
    private var commentTotalCounts: Long = 0
    private lateinit var fragmentContainer: FrameLayout
    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_album_multi_comments
    }

    override fun initUi(savedInstanceState: Bundle?) {
        arguments?.also {
            mAlbum = it.getParcelable(ALBUM_KEY)
            commentTotalCounts = it.getLong(COMMENT_TOTAL_COUNTS)
        }
        radioGroup = findViewById(R.id.main_rg)
        rbListenProgram = findViewById(R.id.main_tv_header_sort_hot)
        rbReadBook = findViewById(R.id.main_tv_header_sort_time)
        flContent = findViewById(R.id.main_fl_fragment_container)
        tvCommentCount = findViewById(R.id.main_tv_count)
        fragmentContainer = findViewById(R.id.main_fl_fragment_container)
        initListeners()
        tabFragmentManager = AlbumCommentTabFragmentManager(
            mActivity,
            childFragmentManager,
            R.id.main_fl_fragment_container
        )
        showListenProgramTab()
        setCommentCount(0, isAdd = true)
    }

    override fun loadData() {
    }

    override fun onMyResume() {
        setFilterStatusBarSet(true)
        super.onMyResume()
        if (rbReadBook.isChecked && ChildProtectManager.isChildProtectOpen(mContext)) {
            //CustomToast.showToast("青少年模式下无法使用该功能");
            rbListenProgram.isChecked = true
            return
        }
        if (isWhiteBgWhenReadNovelChecked) {
            StatusBarManager.setStatusBarColor(window, true)
        } else {
            StatusBarManager.setStatusBarColor(window, false)
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden);
        if (!isAdded) {
            return
        }
    }

    override fun getPageLogicName() = TAG

    override fun onClick(view: View) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return
        }
    }

    /**
     * 初始化监听事件
     */
    private fun initListeners() {
        radioGroup.setOnCheckedChangeListener { group, checkedId ->
            val tag = group.tag
            when (checkedId) {
                R.id.main_tv_header_sort_hot -> {
                    tabFragmentManager.showFragment(checkedId, tag)
                    setWhiteBg()
                }
                R.id.main_tv_header_sort_time -> {
                    tabFragmentManager.showFragment(checkedId, tag)
                    setGradientBg()
                }
            }
        }
    }

    private fun showListenProgramTab() {
        if (rbListenProgram.isChecked) {
            //这个比较特殊，需要在已经选中状态下处理iting
            val fragment = tabFragmentManager.currFragment
            if (fragment is BaseFragment2) {
                fragment.arguments = radioGroup.tag as? Bundle?
            }
            if (fragment is HomeFragmentItingInterface) {
                fragment.handleIting()
            }
        } else {
            //默认选中听节目
            rbListenProgram.isChecked = true
        }
    }

    private fun showReadBookTab() {
        rbReadBook.isChecked = true
    }


    override fun onRefresh() {
    }

    override fun isShowTruckFloatPlayBar(): Boolean {
        if (tabFragmentManager.currFragment is SimpleHomeFragment) {
            return (tabFragmentManager.currFragment as SimpleHomeFragment).isShowTruckFloatPlayBar
        }
        return true
    }

    private fun setWhiteBg() {
        rbListenProgram.setTextColor(ContextCompat.getColor(mContext, R.color.main_color_111111))
        rbListenProgram.textSize = 13f
        rbListenProgram.typeface = Typeface.defaultFromStyle(Typeface.BOLD)

        rbReadBook.setTextColor(ContextCompat.getColor(mContext, R.color.host_color_999999))
        rbReadBook.textSize = 13f
        rbReadBook.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
    }

    /**
     * 设置渐变背景
     */
    private fun setGradientBg() {
        rbListenProgram.setTextColor(ContextCompat.getColor(mContext, R.color.host_color_999999))
        rbListenProgram.textSize = 13f
        rbListenProgram.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)

        rbReadBook.setTextColor(ContextCompat.getColor(mContext, R.color.main_color_111111))
        rbReadBook.textSize = 13f
        rbReadBook.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
    }

    override fun darkStatusBar() = false

    fun showCommentDetailFragment(bundle: Bundle) {
        val commentDialogFragment = CommentDetailDialogFragment.getInstance(bundle)
        commentDialogFragment.show(childFragmentManager, CommentDetailDialogFragment.TAG)
        commentDialogFragment.setOnDismissListener {
        }
    }


    fun setCommentCount(commentCount: Long, isAdd: Boolean) {
        if (isAdd) {
            commentTotalCounts += commentCount
        } else {
            commentTotalCounts -= commentCount
        }
        if (commentTotalCounts <= 0L) return
        val counts = "${StringUtil.getFriendlyNumStrEn(commentTotalCounts)}"
        tvCommentCount.text = counts
        if (parentFragment is LiteAlbumFragment) {
            (parentFragment as LiteAlbumFragment).updateAlbumCommentCounts(counts)
        }
    }


    fun updatePresent(presenter: CommentListPresenter): CommentListPresenter {
        mAlbum?.also {
            presenter.mAlbum = it
            presenter.sourceId = it.id
        }
        return presenter
    }

    fun getCommentPresent(): CommentListPresenter? {
        val fragment = tabFragmentManager.currFragment
        if (fragment is AlbumHotCommentListFragment) {
            return fragment.presenter
        } else if (fragment is AlbumCommentListFragment) {
            return fragment.presenter
        }
        return null
    }

    override fun getInnerScrollView(): View {
        val fragment = tabFragmentManager.currFragment
        if (fragment is AlbumHotCommentListFragment) {
            return fragment.mList
        } else if (fragment is AlbumCommentListFragment) {
            return fragment.mList
        }
        return fragmentContainer
    }

    fun updateCommentTotalCounts(commentCounts: Long) {
        this.commentTotalCounts = commentCounts
        setCommentCount(0, isAdd = true)
    }
}
