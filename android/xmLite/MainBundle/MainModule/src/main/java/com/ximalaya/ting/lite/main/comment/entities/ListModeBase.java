package com.ximalaya.ting.lite.main.comment.entities;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 */
public class ListModeBase<T> {
    private int ret;
    private int maxPageId;
    private int totalCount;
    private int pageId = 1;
    private int pageSize;
    private List<T> list;
    private String msg;
    private boolean isDown;
    private int categoryId;
    private String title;
    private Map<String, String> params;
    private Object extraData;//额外数据
    private int dubTrackUploadCount;
    private boolean hasMore;
    private boolean isShowDubTrack;
    private int hotCount;
    private int userShortContentCount;
    private int videoTotalCount;
    private int allowCommentType;
    private int approvedCount;    // 审核通过数量
    private int approvingCount;    // 待审核数量
    private int rejectedCount;    // 审核不通过数量
    private int topCount;    // 用于评论接口返回根评论数
    private int userType;//用于可能感兴趣的人
    private int displayTime; //搜索框底词滚动时间间隔

    public int getTopCount() {
        return topCount;
    }

    private ListModeMetaModel meta;

    public Map<String, String> getParams() {
        return this.params;
    }

    public void setParams(Map<String, String> params) {
        this.params = params;
    }

    @SuppressWarnings("unchecked")
    public static CommonTrackList toCommonTrackList(ListModeBase baseList) {
        CommonTrackList commonTrackList = CommonTrackList.newInstance();
        if (baseList != null) {
            commonTrackList.setParams(baseList.params);
            commonTrackList.setTracks(baseList.list);
            commonTrackList.setTotalPage(baseList.maxPageId);
            commonTrackList.setTotalCount(baseList.totalCount);
        }
        return commonTrackList;
    }

    /**
     * 更新一些参数，供播放器加载上一页，下一页使用
     *
     * @param baseList
     */
    @SuppressWarnings("unchecked")
    public void updateListModeBase(ListModeBase baseList) {
        maxPageId = baseList.maxPageId;
        totalCount = baseList.totalCount;
        params = baseList.params;
        msg = baseList.msg;
        if (list != null) {
            list.addAll(baseList.list);
        } else {
            list = baseList.list;
        }
    }

    /**
     * 更新一些参数，供播放器加载上一页，下一页使用
     *
     * @param baseList
     */
    @SuppressWarnings("unchecked")
    public void updateListModeBaseParams(ListModeBase baseList) {
        maxPageId = baseList.maxPageId;
        totalCount = baseList.totalCount;
        pageId = baseList.pageId;
        params = baseList.params;
    }

    public ListModeBase() {
        list = new ArrayList<>();
    }

    public ListModeBase(String json, Class<T> classType, String listType) throws JSONException {
        this(json, classType, listType, true);
    }

    /**
     * @param json
     * @param classType
     * @param listType  表示在json中，list数据的标签
     * @throws JSONException
     */
    public ListModeBase(String json, Class<T> classType, String listType, boolean manual)
            throws JSONException
    // TODO:1:没有maxPageId时候,使用pageSize*pageId<totalCount判断
    // 2:json解析可能会出现非标准字段需要用if兼容
    {
        try {
            JSONObject jsonObject = new JSONObject(json);
            ret = jsonObject.optInt("ret");
            maxPageId = -1;
            if (jsonObject.has("maxPageId")) {
                maxPageId = jsonObject.optInt("maxPageId", -1);
            } else if (jsonObject.has("pages")) {
                maxPageId = jsonObject.optInt("pages", -1);
            } else if (jsonObject.has("totalPage")) {
                maxPageId = jsonObject.optInt("totalPage", -1);
            }
            if (jsonObject.has("topCount")) {
                topCount = jsonObject.optInt("maxPageId", 0);
            }
            if (jsonObject.has("totalCount")) {
                totalCount = jsonObject.optInt("totalCount", -1);
            } else if (jsonObject.has("total")) {
                totalCount = jsonObject.optInt("total", -1);
            } else if (jsonObject.has("totalSize")) {
                totalCount = jsonObject.optInt("totalSize", -1);
            } else if (jsonObject.has("count")) {
                totalCount = jsonObject.optInt("count", -1);
            }
            if (jsonObject.has("dubTrackUploadCount")) {
                dubTrackUploadCount = jsonObject.optInt("dubTrackUploadCount", 0);
            }
            if (jsonObject.has("isShowDubTrack")) {
                isShowDubTrack = jsonObject.optBoolean("isShowDubTrack", false);
            }
            if (jsonObject.has("userShortContentCount")) {
                userShortContentCount = jsonObject.optInt("userShortContentCount", 0);
            }
            if (jsonObject.has("page")) {
                pageId = jsonObject.optInt("page", -1);
            } else if (jsonObject.has("pageNum")) {
                pageId = jsonObject.optInt("pageNum", -1);
            } else if (jsonObject.has("pageNo")) {
                pageId = jsonObject.optInt("pageNo", -1);
            } else {
                pageId = jsonObject.optInt("pageId", -1);
            }

            if (jsonObject.has("rows")) {
                pageSize = jsonObject.optInt("rows", DTransferConstants.DEFAULT_PAGE_SIZE);
            } else {
                pageSize = jsonObject.optInt("pageSize", DTransferConstants.DEFAULT_PAGE_SIZE);
            }
            msg = jsonObject.optString("msg");
            isDown = jsonObject.optBoolean("isDown", true);
            title = jsonObject.optString("categoryTitle", "付费");
            if (jsonObject.has("recAlbumsPanelTitle")) {//个性化推荐专辑  title专用字段
                title = jsonObject.optString("recAlbumsPanelTitle");
            } else if (jsonObject.has("albumTitle")) {//群组字段
                title = jsonObject.optString("albumTitle");
            }
            categoryId = jsonObject.optInt("categoryId", 33);
            String content = jsonObject.optString(listType);
            //fix content is "null"
            if (TextUtils.isEmpty(content) || "null".equals(content)) {
                return;
            }
            if (jsonObject.has("hasMore")) {
                hasMore = jsonObject.optBoolean("hasMore");
            }


            JSONArray jsonArray = new JSONArray(content);
            if (jsonArray != null) {
                list = new ArrayList<>();
                for (int i = 0; i < jsonArray.length(); i++) {
                    T clazz = createInstance(classType, jsonArray.optString(i), manual, new Gson());
                    if (clazz != null) {
                        list.add(clazz);
                    }
                }
            }

            if (jsonObject.has("trackTotalCount")) {
                extraData = jsonObject.optInt("trackTotalCount", 0);
            }

            if (jsonObject.has("videoTotalCount")) {
                videoTotalCount = jsonObject.optInt("videoTotalCount", 0);
            }
            if (jsonObject.has("meta")) {
                meta = new Gson().fromJson(jsonObject.optString("meta"), ListModeMetaModel.class);
            }

            if (jsonObject.has("approvedCount")) {
                approvedCount = jsonObject.optInt("approvedCount", 0);
            }

            if (jsonObject.has("approvingCount")) {
                approvingCount = jsonObject.optInt("approvingCount", 0);
            }

            if (jsonObject.has("rejectedCount")) {
                rejectedCount = jsonObject.optInt("rejectedCount", 0);
            }

            if (jsonObject.has("userType")) {
                userType = jsonObject.optInt("userType", 0);
            }

        } catch (Exception e) {
            e.printStackTrace();
            Logger.i("getStringByUrlForOpenSDK", "getStringByUrlForOpen ListModeBase e = " + e.toString());
            throw e;
        }
    }

    public boolean isDown() {
        return isDown;
    }

    public void setDown(boolean isDown) {
        this.isDown = isDown;
    }

    public static <T> T createInstance(Class<T> cls, String str, boolean manual) {
        return createInstance(cls, str, manual, null);
    }

    public static <T> T createInstance(Class<T> cls, String str, boolean manual, @Nullable Gson gson) {
        T obj = null;
        try {
            if (manual) {
                Constructor<T> constructor = cls
                        .getDeclaredConstructor(new Class[]{String.class});
                obj = constructor.newInstance(new Object[]{str});
            } else {
                try {
                    if (gson == null) {
                        gson = new Gson();
                    }
                    obj = gson.fromJson(str, cls);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        } catch (Exception e) {
            try {
                if (gson == null) {
                    gson = new Gson();
                }
                obj = gson.fromJson(str, cls);
            } catch (Exception se) {
                se.printStackTrace();
            }
        }
        return obj;
    }

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public int getMaxPageId() {
        return maxPageId;
    }

    public void setMaxPageId(int maxPageId) {
        this.maxPageId = maxPageId;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getPageId() {
        return pageId;
    }

    public void setPageId(int pageId) {
        this.pageId = pageId;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Object getExtraData() {
        return extraData;
    }

    public void setExtraData(Object extraData) {
        this.extraData = extraData;
    }

    public boolean isHasMore() {
        return hasMore;
    }

    public void setHasMore(boolean b) {
        hasMore = b;
    }

    public int getDubTrackUploadCount() {
        return dubTrackUploadCount;
    }

    public void setDubTrackUploadCount(int dubTrackUploadCount) {
        this.dubTrackUploadCount = dubTrackUploadCount;
    }

    public boolean isShowDubTrack() {
        return isShowDubTrack;
    }

    public void setShowDubTrack(boolean showDubTrack) {
        isShowDubTrack = showDubTrack;
    }

    public int getHotCount() {
        return hotCount;
    }

    public void setHotCount(int hotCount) {
        this.hotCount = hotCount;
    }

    public int getUserShortContentCount() {
        return userShortContentCount;
    }

    public void setUserShortContentCount(int userShortContentCount) {
        this.userShortContentCount = userShortContentCount;
    }

    public int getVideoTotalCount() {
        return videoTotalCount;
    }

    public void setVideoTotalCount(int videoTotalCount) {
        this.videoTotalCount = videoTotalCount;
    }

    public int getAllowCommentType() {
        return allowCommentType;
    }

    public void setAllowCommentType(int allowCommentType) {
        this.allowCommentType = allowCommentType;
    }

    public int getApprovedCount() {
        return approvedCount;
    }

    public void setApprovedCount(int approvedCount) {
        this.approvedCount = approvedCount;
    }

    public int getApprovingCount() {
        return approvingCount;
    }

    public void setApprovingCount(int approvingCount) {
        this.approvingCount = approvingCount;
    }

    public int getRejectedCount() {
        return rejectedCount;
    }

    public void setRejectedCount(int rejectedCount) {
        this.rejectedCount = rejectedCount;
    }

    public ListModeMetaModel getMeta() {
        return meta;
    }

    public void setMeta(ListModeMetaModel meta) {
        this.meta = meta;
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public int getDisplayTime() {
        return displayTime;
    }

    public void setDisplayTime(int displayTime) {
        this.displayTime = displayTime;
    }


    public CommentListBean commentTo2CommentListBean(ListModeBase<CommentModel> commentList) {
        CommentListBean commentBean = new CommentListBean();
        List<CommentModel> list = commentList.getList();
        List<CommentListItemBean> itemBeans = new ArrayList<>();
        int size = list != null ? list.size() : 0;
        for (int i = 0; i < size; i++) {
            itemBeans.add(CommentModel.commentTo2CommentListItemBean(list.get(i)));
        }
        commentBean.setDataList(itemBeans);
        commentBean.setPageId(commentList.getPageId());
        commentBean.setPageSize(commentList.getPageSize());
        commentBean.setTotalCount(commentList.getTotalCount());
        commentBean.setMaxPageId(commentList.getMaxPageId());
        return commentBean;
    }

    public CommentListBean albumCommentTo2CommentListBean(ListModeBase<AlbumCommentModel> albumCommentList,Long allCommentTotalCount) {
        CommentListBean commentBean = new CommentListBean();
        List<AlbumCommentModel> list = albumCommentList.getList();
        List<CommentListItemBean> itemBeans = new ArrayList<>();
        for (AlbumCommentModel albumCommentModel : list) {
            itemBeans.add(albumCommentModel.commentTo2CommentListItemBean());
        }
        commentBean.setDataList(itemBeans);
        commentBean.setPageId(albumCommentList.getPageId());
        commentBean.setPageSize(albumCommentList.getPageSize());
        commentBean.setTotalCount(allCommentTotalCount);
        commentBean.setMaxPageId(albumCommentList.getMaxPageId());
        return commentBean;
    }
}
