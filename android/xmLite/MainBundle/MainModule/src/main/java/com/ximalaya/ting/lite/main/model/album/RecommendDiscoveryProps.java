package com.ximalaya.ting.lite.main.model.album;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/**
 * Created by xmly on 10/08/2018.
 *
 * <AUTHOR>
 */
public class RecommendDiscoveryProps implements Parcelable {
    public long rankClusterId;
    private int categoryId;
    private String subCategory;
    private String albumTag;
    private long albumId;
    private String trackTag;
    private String contentType;
    private long specialId;
    private long activityId;
    private String key;
    private int radioType;
    private int liveReferId;
    private String liveType;
    private String name;
    private long uid;
    private long xzoneId;
    private int rankingListId;
    @SerializedName("subcategory_id")
    private int subcategoryId;
    @SerializedName("search_word")
    private String searchWord;
    private int keywordId;
    private long liveId;
    private String url;
    private String uri; // 行如"iting://open?msg_type=72"

    private long audioStreamId;


    public long getAudioStreamId() {
        return audioStreamId;
    }

    public void setAudioStreamId(long audioStreamId) {
        this.audioStreamId = audioStreamId;
    }


    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public String getAlbumTag() {
        return albumTag;
    }

    public void setAlbumTag(String albumTag) {
        this.albumTag = albumTag;
    }

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public String getTrackTag() {
        return trackTag;
    }

    public void setTrackTag(String trackTag) {
        this.trackTag = trackTag;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public long getSpecialId() {
        return specialId;
    }

    public void setSpecialId(long specialId) {
        this.specialId = specialId;
    }

    public long getActivityId() {
        return activityId;
    }

    public void setActivityId(long activityId) {
        this.activityId = activityId;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int getRadioType() {
        return radioType;
    }

    public void setRadioType(int radioType) {
        this.radioType = radioType;
    }

    public int getLiveReferId() {
        return liveReferId;
    }

    public void setLiveReferId(int liveReferId) {
        this.liveReferId = liveReferId;
    }

    public String getLiveType() {
        return liveType;
    }

    public void setLiveType(String liveType) {
        this.liveType = liveType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getXzoneId() {
        return xzoneId;
    }

    public void setXzoneId(long xzoneId) {
        this.xzoneId = xzoneId;
    }

    public int getRankingListId() {
        return rankingListId;
    }

    public void setRankingListId(int rankingListId) {
        this.rankingListId = rankingListId;
    }

    public int getSubcategoryId() {
        return subcategoryId;
    }

    public void setSubcategoryId(int subcategoryId) {
        this.subcategoryId = subcategoryId;
    }

    public String getSearchWord() {
        return searchWord;
    }

    public void setSearchWord(String searchWord) {
        this.searchWord = searchWord;
    }

    public int getKeywordId() {
        return keywordId;
    }

    public void setKeywordId(int keywordId) {
        this.keywordId = keywordId;
    }

    public long getLiveId() {
        return liveId;
    }

    public void setLiveId(long liveId) {
        this.liveId = liveId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.rankClusterId);
        dest.writeLong(this.categoryId);
        dest.writeString(this.subCategory);
        dest.writeString(this.albumTag);
        dest.writeLong(this.albumId);
        dest.writeString(this.trackTag);
        dest.writeString(this.contentType);
        dest.writeLong(this.specialId);
        dest.writeLong(this.activityId);
        dest.writeString(this.key);
        dest.writeInt(this.radioType);
        dest.writeInt(this.liveReferId);
        dest.writeString(this.liveType);
        dest.writeString(this.name);
        dest.writeLong(this.uid);
        dest.writeLong(this.xzoneId);
        dest.writeInt(this.rankingListId);
        dest.writeInt(this.subcategoryId);
        dest.writeString(this.searchWord);
        dest.writeInt(this.keywordId);
        dest.writeLong(this.liveId);
        dest.writeString(this.url);
        dest.writeString(this.uri);
        dest.writeLong(this.audioStreamId);
    }

    public RecommendDiscoveryProps() {
    }

    protected RecommendDiscoveryProps(Parcel in) {
        this.rankClusterId = in.readLong();
        this.categoryId = in.readInt();
        this.subCategory = in.readString();
        this.albumTag = in.readString();
        this.albumId = in.readLong();
        this.trackTag = in.readString();
        this.contentType = in.readString();
        this.specialId = in.readLong();
        this.activityId = in.readLong();
        this.key = in.readString();
        this.radioType = in.readInt();
        this.liveReferId = in.readInt();
        this.liveType = in.readString();
        this.name = in.readString();
        this.uid = in.readLong();
        this.xzoneId = in.readLong();
        this.rankingListId = in.readInt();
        this.subcategoryId = in.readInt();
        this.searchWord = in.readString();
        this.keywordId = in.readInt();
        this.liveId = in.readLong();
        this.url = in.readString();
        this.uri = in.readString();
        this.audioStreamId = in.readLong();
    }

    public static final Parcelable.Creator<RecommendDiscoveryProps> CREATOR = new Parcelable.Creator<RecommendDiscoveryProps>() {
        @Override
        public RecommendDiscoveryProps createFromParcel(Parcel source) {
            return new RecommendDiscoveryProps(source);
        }

        @Override
        public RecommendDiscoveryProps[] newArray(int size) {
            return new RecommendDiscoveryProps[size];
        }
    };
}
