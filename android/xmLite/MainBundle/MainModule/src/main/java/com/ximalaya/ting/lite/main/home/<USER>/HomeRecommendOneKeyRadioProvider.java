package com.ximalaya.ting.lite.main.home.adapter;

import android.content.Context;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.lite.main.model.onekey.OneKeyRadioModel;
import com.ximalaya.ting.lite.main.onekey.OneKeyRadioFragment;
import com.ximalaya.ting.lite.main.view.LinearItemDecoration;
import com.ximalaya.ting.lite.main.view.RecyclerViewCanDisallowIntercept;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by ZhuPeipei on 2020-04-10 18:33.
 *
 * @Description: 一键听 新版频道列表页
 */
public class HomeRecommendOneKeyRadioProvider
        implements IMulitViewTypeViewAndData<HomeRecommendOneKeyRadioProvider.Holder,
        List<OneKeyRadioModel>> {
    private final String KEY_NEW_RECOMMEND_RADIO = "key_new_recommend_radio";

    private Context mContext;
    private BaseFragment2 mFragment;
    private RecommendOneKeyNewItemAdapter adapter;

    public HomeRecommendOneKeyRadioProvider(BaseFragment2 fragment) {
        this.mFragment = fragment;
        this.mContext = fragment.getActivity();
    }

    @Override
    public void bindViewDatas(Holder holder, ItemModel<List<OneKeyRadioModel>> t, View convertView, int position) {
        if (holder == null || t == null || !(t.getTag() instanceof MainAlbumMList)) {
            return;
        }
        MainAlbumMList albumMList = (MainAlbumMList) t.getTag();
        if (TextUtils.isEmpty(albumMList.getTitle())) {
            holder.onekeyTitleTv.setText("今日电台");
        } else {
            holder.onekeyTitleTv.setText(albumMList.getTitle());
        }
        if (albumMList.getAllCount() > 0) {
            holder.onekeyMoreTv.setText("共" + albumMList.getAllCount() + "个电台");
            holder.onekeyMoreTv.setVisibility(View.VISIBLE);
        } else {
            holder.onekeyMoreTv.setVisibility(View.GONE);
        }
        holder.onekeyMoreTv.setOnClickListener(v -> {
            if (mFragment == null) {
                return;
            }
            mFragment.startFragment(OneKeyRadioFragment.newInstance());
        });
        if (adapter != null && !ToolUtil.isEmptyCollects(t.getObject())) {
            List<Object> objs = new ArrayList<>(fixNewRecommend(t.getObject()));
            if (albumMList.isHasMore()) {
                objs.add(albumMList.getAllCount());
            }
            adapter.setListData(objs);
        }
    }

    private List<OneKeyRadioModel> fixNewRecommend(List<OneKeyRadioModel> list) {
        if (ToolUtil.isEmptyCollects(list)) {
            return new ArrayList<>();
        }
        if (list.size() < 3 || list.get(2) == null) {
            return list;
        }
        OneKeyRadioModel thirdRadio = list.get(2);
        if (!thirdRadio.isNewRecommend()) {
            SharedPreferencesUtil.getInstance(mContext).saveString(KEY_NEW_RECOMMEND_RADIO, null);
            return list;
        }

        // 判断是否要去掉上新推荐radio
        long lastShowTime = -1;
        long showTimes = -1;
        String newRecommend = SharedPreferencesUtil.getInstance(mContext).getString(KEY_NEW_RECOMMEND_RADIO);
        if (!TextUtils.isEmpty(newRecommend)) {
            List<Long> args = parseNewRecommend(newRecommend);
            if (args != null && args.size() == 3
                    && args.get(0) == thirdRadio.getRadioId()) {
                lastShowTime = args.get(1);
                showTimes = args.get(2);
                if (showTimes == 1 && !isInOneDay(lastShowTime)) {
                    showTimes++;
                    lastShowTime = System.currentTimeMillis();
                } else if (showTimes == 2 && !isInOneDay(lastShowTime)) {
                    list.remove(thirdRadio);
                }
            }
        }

        // 替换sp数据
        String str;
        if (lastShowTime <= 0 || showTimes <= 0) {
            str = thirdRadio.getRadioId() + "," + System.currentTimeMillis() + "," + 1;
        } else {
            str = thirdRadio.getRadioId() + "," + lastShowTime + "," + showTimes;
        }
        SharedPreferencesUtil.getInstance(mContext).saveString(KEY_NEW_RECOMMEND_RADIO, str);
        return list;
    }

    private boolean isInOneDay(long time) {
        return Math.abs(System.currentTimeMillis() - time) <= 24 * 3600 * 1000;
    }

    private List<Long> parseNewRecommend(String newRecommendSp) {
        try {
            String[] args = newRecommendSp.split(",");
            long radioId = Long.parseLong(args[0]);
            long lastShowTime = Long.parseLong(args[1]);
            int showTimes = Integer.parseInt(args[2]);
            if (radioId <= 0 || lastShowTime <= 0 || showTimes <= 0) {
                return null;
            }
            return Arrays.asList(radioId, lastShowTime, (long) showTimes);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_home_recommend_item_onekey_view, parent, false);
    }

    @Override
    public Holder buildHolder(View convertView) {
        Holder holder = new Holder(convertView);
        adapter = new RecommendOneKeyNewItemAdapter(mContext, mFragment);
        holder.radioRv.setAdapter(adapter);
        int margin = BaseUtil.dp2px(mContext, 16);
        holder.radioRv.addItemDecoration(new LinearItemDecoration(margin / 2, margin));
        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false);
        holder.radioRv.setLayoutManager(layoutManager);
        holder.radioRv.setDisallowInterceptTouchEventView((ViewGroup) mFragment.getView());
        return holder;
    }

    public void reset() {
        if (adapter != null) {
            adapter.resetLastPlayedRadioId();
        }
    }

    public static class Holder extends HolderAdapter.BaseViewHolder {

        private TextView onekeyTitleTv;
        private TextView onekeyMoreTv;
        private RecyclerViewCanDisallowIntercept radioRv;

        private Holder(View convertView) {
            onekeyTitleTv = convertView.findViewById(R.id.main_recommend_onekey_title_tv);
            onekeyMoreTv = convertView.findViewById(R.id.main_recommend_onekey_more);
            radioRv = convertView.findViewById(R.id.main_recommend_onekey_radio_rv);
        }
    }
}
