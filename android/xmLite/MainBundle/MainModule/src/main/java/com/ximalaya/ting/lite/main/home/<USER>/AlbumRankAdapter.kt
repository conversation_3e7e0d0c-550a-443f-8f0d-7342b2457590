package com.ximalaya.ting.lite.main.home.adapter

import android.annotation.SuppressLint
import android.graphics.Rect
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.AlbumTagUtil
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter
import kotlinx.android.synthetic.main.main_item_album_rank.view.*

/**
 * Created by dumingwei on 2020/6/10.
 *
 * Desc:分类排行适配器
 */
public class AlbumRankAdapter(
        val fragment: BaseFragment2

) : AbRecyclerViewAdapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val ITEM_TYPE_ALBUM = 1
        private const val ITEM_TYPE_MORE_BTN = 2
    }

    var mAlbumMList: List<AlbumM>? = null

    var mOnMoreBtnClickListener: View.OnClickListener? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        if (viewType == ITEM_TYPE_ALBUM) {
            val view = inflater.inflate(R.layout.main_item_album_rank, parent, false)
            return ViewHolder(view)
        }
        val view = inflater.inflate(R.layout.main_recommend_more_btn_f3f4f5, parent, false)
        return MoreBtnViewHolder(view)

    }

    override fun getItemViewType(position: Int): Int {
        val albumSize = mAlbumMList?.size ?: 0
        if (CollectionUtil.isNotEmpty(mAlbumMList) && position < albumSize) {
            return ITEM_TYPE_ALBUM
        }
        return ITEM_TYPE_MORE_BTN
    }

    override fun getItemCount(): Int {
        var itemCount = mAlbumMList?.size ?: 0
        if (mOnMoreBtnClickListener != null) {
            itemCount++
        }
        return itemCount
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val albumSize = mAlbumMList?.size ?: 0
        if (holder is ViewHolder && position < albumSize) {
            val album: AlbumM? = mAlbumMList?.get(position)
            with(holder.itemView) {
                album?.let {
                    mainTvRankNumber.text = "${position + 1}"

                    //改变角标颜色
                    val rankColor = when (position) {
                        0 -> {
                            ContextCompat.getColor(context, R.color.main_rank_first_color)
                        }
                        1 -> {
                            ContextCompat.getColor(context, R.color.main_rank_second_color)
                        }
                        2 -> {
                            ContextCompat.getColor(context, R.color.main_rank_third_color)
                        }
                        else -> {
                            ContextCompat.getColor(context, R.color.main_rank_normal_color)
                        }
                    }
                    mainIvRankNumber.setColorFilter(rankColor)

                    ImageManager.from(context).displayImage(mainIvAlbumCover, album.largeCover,
                            R.drawable.host_default_album_145, R.drawable.host_default_album_145)
                    mainTvAlbumTitle.text = album.albumTitle
                    mainTvPlayCount.text = StringUtil.getFriendlyNumStr(album.playCount)
                    mainTvTrackCount.text = StringUtil.getFriendlyNumStr(album.includeTrackCount) + " 集"

                    @DrawableRes val coverTagResId: Int = AlbumTagUtil.getAlbumCoverTag(album)
                    if (coverTagResId != -1) {
                        mainIvAlbumCoverTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable(album,
                                context, 0.7f))
                        mainIvAlbumCoverTag.visibility = View.VISIBLE
                    } else {
                        mainIvAlbumCoverTag.visibility = View.INVISIBLE
                    }

                    holder.itemView.setOnClickListener {
                        handleItemClick(album)
                    }
                }
            }

        } else if (holder is MoreBtnViewHolder) {
            with(holder.itemView) {
                val layoutParams = holder.itemView.layoutParams as RecyclerView.LayoutParams
                layoutParams.rightMargin = BaseUtil.dp2px(context, 16f)
                layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT
                holder.itemView.layoutParams = layoutParams
                holder.itemView.setOnClickListener(mOnMoreBtnClickListener)
            }
        }
    }

    private fun handleItemClick(album: AlbumM) {
        if (!TextUtils.isEmpty(album.contentType)) {
            val pageUrl = UrlConstants.getInstanse().getSubjectDetailPageUrl(album.specialId.toString())
            fragment.startFragment(NativeHybridFragment.newInstance(pageUrl, true), fragment.containerView)
        } else {
            AlbumEventManage.startMatchAlbumFragment(album, AlbumEventManage.FROM_DISCOVERY_CATEGORY,
                    0, album.recSrc, album.recTrack, -1, fragment.activity)
        }
    }

    override fun getItem(position: Int): Any? {
        val albumSize = mAlbumMList?.size ?: 0
        if (CollectionUtil.isNotEmpty(mAlbumMList) && position >= 0 && position < albumSize) {
            return mAlbumMList?.get(position)
        }
        return null
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    class MoreBtnViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    class MyItemDecoration : RecyclerView.ItemDecoration() {

        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
            super.getItemOffsets(outRect, view, parent, state)
            //val position = parent.getChildAdapterPosition(view)
            outRect.bottom = BaseUtil.dp2px(view.context, 20f)
        }
    }



}