package com.ximalaya.ting.lite.main.album.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Paint
import android.text.TextPaint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RatingBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter
import com.ximalaya.ting.lite.main.model.album.LiteHotComment
import com.ximalaya.ting.lite.main.view.text.StaticLayoutManager
import com.ximalaya.ting.lite.main.view.text.StaticLayoutView

/**
 * Created by dumingwei on 2021/5/26
 *
 * Desc: 热评列表适配器
 */
class LiteHotCommentAdapterTwo(
        private val context: Context,
        var list: MutableList<LiteHotComment>?
) : AbRecyclerViewAdapter<LiteHotCommentAdapterTwo.ViewHolder>() {

    private var imageSpanHeight = 0
    private var commentWidth = 0

    private var textPaint: TextPaint

    var onLikeAction: ((position: Int, comment: LiteHotComment) -> Unit)? = null

    init {
        //高度必须和文字大小一致
        imageSpanHeight = BaseUtil.dp2px(context, 16f)
        //评论的宽度 76 = 屏幕宽度 -16 -34（头像大大小) - 10 -16
        commentWidth = BaseUtil.getScreenWidth(context) - BaseUtil.dp2px(context, 76f)

        textPaint = TextPaint(Paint.ANTI_ALIAS_FLAG)
        textPaint.color = ContextCompat.getColor(context, R.color.main_color_333333)
        textPaint.textSize = BaseUtil.dp2pxReturnFloat(context, 16f)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = inflater.inflate(R.layout.main_item_hot_comment_two, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount() = list?.size ?: 0

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        onBindAlbumRankViewHolder(holder, position)
    }

    @SuppressLint("SetTextI18n")
    private fun onBindAlbumRankViewHolder(holder: ViewHolder, position: Int) {
        val model: LiteHotComment = list?.get(position) ?: return

        val commentText = model.content
        if (commentText.isNullOrEmpty()) {
            holder.slvComment.visibility = View.GONE
        } else {
            holder.slvComment.visibility = View.VISIBLE

            val staticLayout = if (model.expanded == true) {
                StaticLayoutManager.getInstance().getNormalLayout(commentText, commentWidth, textPaint, 1.2f)
            } else {
                StaticLayoutManager.getInstance().getLimitLayout(commentText, commentWidth, textPaint) {
                    model.expanded = true
                    notifyDataSetChanged()
                }
            }
            if (staticLayout != null) {
                holder.slvComment.setLayout(staticLayout)
                holder.slvComment.invalidate()
            }
        }

        ImageManager.from(context).displayImage(holder.ivAvatar, model.smallLogo, R.drawable.host_default_album, R.drawable.host_default_album)

        holder.tvCommentatorName.text = model.nickname

        val commentScore = model.albumScore ?: 6

        when {
            commentScore <= 7 -> {
                holder.ratingBar.rating = 3.5f
            }
            commentScore <= 8 -> {
                holder.ratingBar.rating = 4f
            }
            commentScore <= 9 -> {
                holder.ratingBar.rating = 4.5f
            }
            else -> {
                holder.ratingBar.rating = 5f
            }
        }

        holder.tvLikeCount.text = model.likeCount?.toString()

        if (model.liked == true) {
            holder.ivLike.background = ContextCompat.getDrawable(context, R.drawable.main_ic_liked)
        } else {
            holder.ivLike.background = ContextCompat.getDrawable(context, R.drawable.main_ic_like_normal)
        }

        holder.ivLike.setOnClickListener { onLikeAction?.invoke(position, model) }
    }

    override fun getItem(position: Int): Any? {
        val albumSize = list?.size ?: 0
        if (CollectionUtil.isNotEmpty(list) && position >= 0 && position < albumSize) {
            return list?.get(position)
        }
        return null
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        //评论头像
        val ivAvatar: RoundImageView = itemView.findViewById(R.id.main_iv_avatar)
        val tvCommentatorName: TextView = itemView.findViewById(R.id.main_tv_commentator)
        val ratingBar: RatingBar = itemView.findViewById(R.id.main_comment_rating_star)

        val tvLikeCount: TextView = itemView.findViewById(R.id.main_tv_like_count)
        val ivLike: ImageView = itemView.findViewById(R.id.main_iv_like)

        val slvComment: StaticLayoutView = itemView.findViewById(R.id.main_slv_comment)
    }

}