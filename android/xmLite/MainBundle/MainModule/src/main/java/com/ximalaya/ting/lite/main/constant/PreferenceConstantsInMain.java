package com.ximalaya.ting.lite.main.constant;


import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;

/**
 * <AUTHOR>
 * @Date 16/11/29
 * <p>
 */

public class PreferenceConstantsInMain implements PreferenceConstantsInOpenSdk {

    public final static String TINGMAIN_KEY_SHARED_PRE_COUNTRY_CODE = "countryCode";
    // 专辑 定位正在播放声音的定位提示
    public static final String KEY_HAS_SHOW_LOCATION_TOAST = "key_has_show_location_toast";
    // 当前播放倍速
    public static final String KEY_PLAY_TEMPO = "key_play_tempo";
    public static final String KEY_ONEKEY_CATEGORY_SETTING_WRITED = "key_onekey_category_setting_writed";
    //上次显示HomeRecommendFragment是否显示底部的设置兴趣入口的时间戳
    public static final String KEY_LAST_SHOW_SET_INTEREST_TIP_TIMESTAMP = "key_last_show_set_interest_tip_timestamp";

}
