package com.ximalaya.ting.lite.main.history;

import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshRecyclerView;
import com.ximalaya.ting.android.host.db.model.SkitsHistoryInfo;
import com.ximalaya.ting.android.host.db.repository.SkitsHistoryRecordRepository;
import com.ximalaya.ting.android.host.db.repository.SkitsHistoryRepository;
import com.ximalaya.ting.android.host.db.utils.BookUtils;
import com.ximalaya.ting.android.host.db.utils.SkitsHistoryUtils;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.lite.main.history.adapter.SkitsHistoryListAdapter;
import com.ximalaya.ting.lite.main.history.presenter.ISkitsHistoryPresenter;
import com.ximalaya.ting.lite.main.history.presenter.SkitsHistoryPresenter;
import com.ximalaya.ting.lite.main.history.view.ISkitsHistoryView;
import com.ximalaya.ting.lite.main.mylisten.inter.IHistoryCallBack;
import com.ximalaya.ting.lite.main.mylisten.view.AllHistoryFragment;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 短剧历史记录
 */
public class SkitsHistoryFragment extends BaseFragment2 implements IHistoryCallBack {

    private static final String TAG = "SkitsHistoryFragment";

    private RefreshRecyclerView mRecyclerView;

    private final List<SkitsHistoryInfo> mHistoryList = new ArrayList<>();

    private SkitsHistoryListAdapter mHistoryListAdapter;

    private ISkitsHistoryPresenter mHistoryPresenter;

    private DialogBuilder<?> mRemoveOneItemDialog;

    private long mCurTime;

    private final Handler mHandler = new Handler();

    @Override
    protected String getPageLogicName() {
        return getClass().getSimpleName();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRecyclerView = findViewById(R.id.main_rv_skits_history_list);
        mRecyclerView.getRefreshableView().setLayoutManager(new LinearLayoutManager(getContext()));
        mRecyclerView.setMode(PullToRefreshBase.Mode.PULL_FROM_START);

        mHistoryListAdapter = new SkitsHistoryListAdapter(mContext, mHistoryList);
        mRecyclerView.setAdapter(mHistoryListAdapter);

        mHistoryPresenter = new SkitsHistoryPresenter(new ISkitsHistoryView() {
            @Override
            public void setData(@Nullable List<SkitsHistoryInfo> list) {
                if (canUpdateUi()) {
                    setRecyclerViewData(list);
                }
            }

            @Override
            public void setTotalCount(int totalCount) {

            }

            @Override
            public void loadDataEnd() {
                mRecyclerView.onRefreshComplete(false);
            }
        });

        initListener();

        onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
    }

    @Override
    protected boolean onPrepareNoContentView() {
        setNoContentImageView(R.drawable.host_no_content);
        setNoContentTitle("还没有记录呢");
        return false;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_skits_history_layout;
    }

    private void initListener() {
        mRecyclerView.setOnRefreshLoadMoreListener(new IRefreshLoadMoreListener() {
            @Override
            public void onRefresh() {
                requestData();
            }

            @Override
            public void onMore() {

            }
        });

        mHistoryListAdapter.setOnSkitsClickListener(new SkitsHistoryListAdapter.onSkitsClickListener() {
            @Override
            public void onClickItems(int position) {
                SkitsHistoryInfo historyInfo = mHistoryList.get(position);

                if (historyInfo == null || historyInfo.isOffShelf()) {
                    CustomToast.showToast("该内容已下架");
                    return;
                }

                mHistoryList.remove(position);
                historyInfo.setLastUpdatedTime(BookUtils.INSTANCE.getLastUpdatedTime());
                // 移动到第一本
                mHistoryList.add(0, historyInfo);

                //跳转到阅读器
                try {
                    BaseFragment fragment = Router.getMainActionRouter().getFragmentAction().newPlayletDetailFragment(historyInfo.getSkitsId(), historyInfo.getAlbumId(), historyInfo.getOrderNum());
                    startFragment(fragment);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                // 延时刷新
                mHandler.postDelayed(() -> mHistoryListAdapter.notifyDataSetChanged(), 500);
            }

            @Override
            public void onDelItem(int position) {
                showDelDialog("确认删除该条播放记录", () -> {

                    SkitsHistoryInfo historyInfo = mHistoryList.remove(position);
                    mHistoryListAdapter.notifyDataSetChanged();

                    if (historyInfo != null) {
                        SkitsHistoryUtils.INSTANCE.operatingData(historyInfo, BookUtils.TYPE_DEL);
                        mHistoryPresenter.syncHistoryModifyRecord();
                    }
                    checkCleanAll();
                });
            }
        });
    }

    private void showDelDialog(String msg, DialogBuilder.DialogCallback callback) {
        if (mRemoveOneItemDialog == null) {
            mRemoveOneItemDialog = new DialogBuilder<>(mActivity);
        }
        mRemoveOneItemDialog.setTitleVisibility(false)
                .setMessage(msg)
                .setOkBtn(callback);

        if (!mRemoveOneItemDialog.isShowing()) {
            mRemoveOneItemDialog.showConfirm();
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        // 界面可见时触发
        if (isRealVisable()) {
            FuliLogger.log(TAG, "setUserVisibleHint:" + isRealVisable());
            requestData();
        }
    }

    private void requestData() {
        if (mHistoryPresenter != null) {
            FuliLogger.log(TAG, "requestData");
            mHistoryPresenter.loadData();
        }
    }

    @Override
    protected void loadData() {
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        // 界面可见时加载数据  防止频繁刷新
        long sysTime = SystemClock.elapsedRealtime();
        if (sysTime - mCurTime > 5000L) {
            FuliLogger.log(TAG, "onMyResume requestData");
            mCurTime = sysTime;
            requestData();
        }
    }

    private void setRecyclerViewData(List<SkitsHistoryInfo> list) {
        // 清除所有数据  因为每次都是从数据库获取全量数据
        mHistoryList.clear();

        if (CollectionUtil.isNotEmpty(list)) {
            HashSet<Long> hashSet = new HashSet<>();
            // 数据去重
            for (SkitsHistoryInfo info : list) {
                if (info == null) {
                    continue;
                }
                // 判断是否存在同一本书
                if (hashSet.contains(info.getSkitsId())) {
                    FuliLogger.log(TAG, "存在同一本书:" + info.getVideoTitle() + " uid:" + info.getUid());
                } else {
                    hashSet.add(info.getSkitsId());
                    mHistoryList.add(info);
                }
            }
        }

        mHistoryListAdapter.notifyDataSetChanged();
        checkCleanAll();
    }

    @Override
    public void clearAll() {
        if (mHistoryList.isEmpty()) {
            return;
        }

        // 删除
        showDelDialog("确认清空全部历史记录", () -> {

            if (!UserInfoMannage.hasLogined()) {
                mHistoryList.clear();
                mHistoryListAdapter.notifyDataSetChanged();
                checkCleanAll();

                // 清除本地记录和本地操作记录  只能删除当前用户的数据
                SkitsHistoryRepository.INSTANCE.removeAll();
                SkitsHistoryRecordRepository.INSTANCE.removeAll();
                return;
            }

            onPageLoadingCompleted(LoadCompleteType.LOADING);

            // 接口请求成功后再删除
            mHistoryPresenter.clearAllHistoryRecord(new IDataCallBack<Boolean>() {
                @Override
                public void onSuccess(@Nullable Boolean object) {
                    mHistoryList.clear();
                    mHistoryListAdapter.notifyDataSetChanged();
                    checkCleanAll();
                }

                @Override
                public void onError(int code, String message) {
                    if (canUpdateUi()) {
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        CustomToast.showToast(message);
                    }
                }
            });
        });
    }

    private void checkCleanAll() {
        boolean isNoData = mHistoryList.isEmpty();
        checkDelViewVisible(isNoData ? View.GONE : View.VISIBLE);

        onPageLoadingCompleted(isNoData ? LoadCompleteType.NOCONTENT : LoadCompleteType.OK);
    }

    public void checkDelViewVisible(int visible) {
        Fragment fragment = getParentFragment();
        if (fragment instanceof AllHistoryFragment) {
            AllHistoryFragment allHistoryFragment = (AllHistoryFragment) fragment;
            allHistoryFragment.checkDelBtnVisible(visible, 2);
        }
    }
}
