package com.ximalaya.ting.lite.main.dialog

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.DialogInterface
import android.os.Bundle
import android.os.SystemClock
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.CountDownTimerFix
import com.ximalaya.ting.android.host.util.VersionUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import java.text.SimpleDateFormat
import java.util.*


// 会员权益翻窗
class MemberBenefitsDialog : BaseFullScreenDialogFragment() {
    val format = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    private var rootDialogView: View? = null
    private var tvTitle: TextView? = null
    private var tvJustGetOK: TextView? = null       //立即领取，增加呼吸动效
    private var vgDialogCloseLayout: ViewGroup? = null  //关闭按钮布局
    private var countdownHour: TextView? = null         //小时
    private var countdownMinute: TextView? = null       //分钟
    private var countdownSecond: TextView? = null       //秒
    private var mBtnBreatheAnimatorSet: AnimatorSet? = null     //呼吸动效
    private var mDialogScaleAnimatorSet: AnimatorSet? = null    //弹框缩放动效
    private var mCountTimer: CountDownTimerFix? = null
    private var mMillisUntilFinished: Long = 0
    private var mTimeStopTs: Long = 0;
    private var isVisibleStatus: Boolean = false
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val rootView = inflater.inflate(R.layout.main_fra_dialog_memberbenefits, container, false)
        tvTitle = rootView.findViewById(R.id.main_tv_title)
        tvJustGetOK = rootView.findViewById(R.id.main_tv_just_get)
        vgDialogCloseLayout = rootView.findViewById(R.id.main_dialog_close_layout)
        countdownHour = rootView.findViewById(R.id.main_countdown_hour)
        countdownMinute = rootView.findViewById(R.id.main_countdown_minute)
        countdownSecond = rootView.findViewById(R.id.main_countdown_second)

        val titleConfig = ConfigureCenter.getInstance().getString(
            CConstants.Group_Base.GROUP_NAME,
            CConstants.Group_Base.ITEM_NovelRights_title,
            "送您1个月奇迹体验会员"
        )
        tvTitle!!.text = if (VersionUtil.isNewInstall()) "您有一份新手福利待领取" else titleConfig
        vgDialogCloseLayout!!.setOnClickListener { dismissAllowingStateLoss() }
        tvJustGetOK!!.setOnClickListener {

            // 奇迹权益弹窗-btn  弹框控件点击
            XMTraceApi.Trace()
                .setMetaId(54275)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("abtestNumber", "0")
                .put("currPage", "HomePage")
                .createTrace()

            dismissAllowingStateLoss()
            if (activity is MainActivity) {
                val url = ConfigureCenter.getInstance().getString(
                    CConstants.Group_Base.GROUP_NAME,
                    CConstants.Group_Base.ITEM_HOME_QIJI_QUANYI,
                    "https://m.ximalaya.com/growth-ssr-speed-welfare-center/page/jisu-popup?_ext=0&businessType=0&channelName=jisubanpopup&scheme=xread%3A%2F%2Fopen%3Fmsg_type%3D45%26businessType%3D0%26channelName%3Djisubanpopup"
                )
                ToolUtil.clickUrlAction(activity as MainActivity, url, null, null)
            } else {
                CustomToast.showFailToast("发生异常，请重新领取")
            }
        }
        rootDialogView = rootView
        startDialogScaleAnimator()

        // 奇迹权益弹窗  弹框展示
        XMTraceApi.Trace()
            .setMetaId(54274)
            .setServiceId("dialogView") // 弹窗展示时上报
            .put("abtestNumber", "0")
            .put("currPage", "HomePage")
            .createTrace()

        return rootView
    }

    private fun startTimer() {
        if (mCountTimer == null) {
            mTimeStopTs = 0
            mMillisUntilFinished = 12 * 60 * 60 * 1000L
            mCountTimer = object : CountDownTimerFix(1000, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    mMillisUntilFinished = millisUntilFinished
                    mTimeStopTs = 0;
                    updateTimeUI(millisUntilFinished)
                }

                override fun onFinish() {
                    updateTimeUI(0)
                }
            }
        }
        var millis = mMillisUntilFinished;
        if (mTimeStopTs > 0) {
            millis = mMillisUntilFinished - (SystemClock.elapsedRealtime() - mTimeStopTs)
        }
        mTimeStopTs = 0;
        //如果时间已经比结束了，更新后return
        if (millis <= 0) {
            updateTimeUI(0)
            return
        }
        mCountTimer!!.resetMillisInFuture(millis)
        mCountTimer!!.start()
    }

    private fun stopTimer() {
        if (mCountTimer != null) {
            mTimeStopTs = SystemClock.elapsedRealtime();
            mCountTimer?.cancel()
        }
    }

    private fun updateTimeUI(time: Long) {
        if (countdownHour == null || countdownMinute == null || countdownSecond == null) {
            return
        }
        var timeFormat: String? = ""
        try {
            //注意该方法倒计时最多只支持23小说59分59秒
            val cal = Calendar.getInstance()
            cal.set(
                cal.get(Calendar.YEAR),
                cal.get(Calendar.MONTH),
                cal.get(Calendar.DAY_OF_MONTH),
                0,
                0,
                0
            )
            timeFormat = format.format(Date((cal.timeInMillis + time)))
            val split = timeFormat.split(":")
            if (split.size != 3) {
                return
            }
            countdownHour?.text = split[0]
            countdownMinute?.text = split[1]
            countdownSecond?.text = split[2]
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun startDialogScaleAnimator() {
        if (rootDialogView == null) {
            return
        }
        val animatorDuration = 500
        if (mDialogScaleAnimatorSet == null) {
            mDialogScaleAnimatorSet = AnimatorSet()
            //高度缩小动画
            val changeSmallY =
                ObjectAnimator.ofFloat(rootDialogView, "scaleY", 0.05f, 1.0f)
            changeSmallY.duration = animatorDuration.toLong()
            //宽度缩小动画
            val changeSmallX =
                ObjectAnimator.ofFloat(rootDialogView, "scaleX", 0.05f, 1.0f)
            changeSmallX.duration = animatorDuration.toLong()
            mDialogScaleAnimatorSet!!.play(changeSmallY).with(changeSmallX)

        }
        mDialogScaleAnimatorSet!!.start()
    }

    private fun stopDialogScaleAnimator() {
        mDialogScaleAnimatorSet?.cancel()
    }

    /**
     * 按钮-启动呼吸动效
     */
    private fun startBtnBreatheAnimator() {
        //没有创建和不可见不启动
        if (tvJustGetOK == null) {
            return
        }
        //停止呼吸动效
        stopBtnBreatheAnimator()
        if (mBtnBreatheAnimatorSet == null) {
            mBtnBreatheAnimatorSet = AnimatorSet()
            val animatorDuration = 400
            //高度缩小动画
            val changeSmallY =
                ObjectAnimator.ofFloat(tvJustGetOK, "scaleY", 1.0f, 0.955f)
            changeSmallY.duration = animatorDuration.toLong()
            //宽度缩小动画
            val changeSmallX =
                ObjectAnimator.ofFloat(tvJustGetOK, "scaleX", 1.0f, 0.955f)
            changeSmallX.duration = animatorDuration.toLong()

            //高度变大动画
            val changeBigY = ObjectAnimator.ofFloat(tvJustGetOK, "scaleY", 0.955f, 1.0f)
            changeBigY.duration = animatorDuration.toLong()
            //宽度变大动画
            val changeBigX = ObjectAnimator.ofFloat(tvJustGetOK, "scaleX", 0.955f, 1.0f)
            changeBigX.duration = animatorDuration.toLong()

            //changeSmallX和changeSmallY一起播放
            mBtnBreatheAnimatorSet!!.play(changeSmallY).with(changeSmallX)
            //changeSmallX在changeBigY后执行
            mBtnBreatheAnimatorSet!!.play(changeBigY).after(changeSmallX)
            //changeBigY在600毫秒后执行
            mBtnBreatheAnimatorSet!!.play(changeBigY).after(animatorDuration.toLong())
            //changeBigX跟随changeBigY一起执行
            mBtnBreatheAnimatorSet!!.play(changeBigY).with(changeBigX)
            mBtnBreatheAnimatorSet!!.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                    FuliLogger.log("呼吸动画==onAnimationStart")
                }

                override fun onAnimationEnd(animation: Animator) {
                    FuliLogger.log("呼吸动画==onAnimationEnd")
                    //弹框已经弹出，并且没有被销毁，可以展示
                    if (!isShowing || !isVisibleStatus) {
                        return
                    }
                    HandlerManager.postOnUIThreadDelay(Runnable { //弹框已经弹出，并且没有被销毁，可以展示
                        //不可见不用弹出延时
                        if (!isShowing || !isVisibleStatus) {
                            FuliLogger.log("呼吸动画==禁止延时")
                            return@Runnable
                        }
                        FuliLogger.log("呼吸动画==postOnUIThreadDelay1000")
                        startBtnBreatheAnimator()
                    }, 600)
                }

                override fun onAnimationCancel(animation: Animator) {
                    FuliLogger.log("呼吸动画==onAnimationCancel")
                }

                override fun onAnimationRepeat(animation: Animator) {
                    FuliLogger.log("呼吸动画==onAnimationRepeat")
                }
            })
        }
        if (mBtnBreatheAnimatorSet!!.isRunning) {
            return
        }
        mBtnBreatheAnimatorSet!!.start()
    }

    /**
     * 按钮-停止呼吸动效
     */
    private fun stopBtnBreatheAnimator() {
        mBtnBreatheAnimatorSet?.cancel()
    }

    override fun onPause() {
        super.onPause()
        isVisibleStatus = false
        stopBtnBreatheAnimator()
        stopTimer()
    }

    override fun onResume() {
        super.onResume()
        isVisibleStatus = true
        startBtnBreatheAnimator()
        startTimer();
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        stopBtnBreatheAnimator()
        stopTimer()
        stopDialogScaleAnimator()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        stopBtnBreatheAnimator()
        stopTimer()
        stopDialogScaleAnimator()
    }
}