package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import android.graphics.Color;
import androidx.annotation.Nullable;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;
import com.ximalaya.ting.lite.main.home.view.DislikeFeedbackWindowNew;
import com.ximalaya.ting.android.host.model.album.DislikeReason;
import com.ximalaya.ting.lite.main.model.album.RecommendAlbumItem;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by qinhuifeng on 2019-07-31
 *
 * <AUTHOR>
 */
public class HomeRecommendFeedAlbumAdapterProvider implements IMulitViewTypeViewAndData<HomeRecommendFeedAlbumAdapterProvider.ViewHolder, RecommendItemNew> {

    private Activity mActivity;
    protected BaseFragment2 mFragment;
    private HomeRecommedExtraDataProvider dataProvider;
    private IRecommendFeedItemActionListener recommendFeedItemActionListener;

    public HomeRecommendFeedAlbumAdapterProvider(BaseFragment2 baseFragment2, HomeRecommedExtraDataProvider dataProvider,
                                                 IRecommendFeedItemActionListener recommendFeedItemActionListener) {
        mFragment = baseFragment2;
        mActivity = baseFragment2.getActivity();
        this.dataProvider = dataProvider;
        this.recommendFeedItemActionListener = recommendFeedItemActionListener;
    }

    @Override
    public void bindViewDatas(ViewHolder viewHolder, final ItemModel<RecommendItemNew> t, View convertView, final int position) {
        if (t == null || t.object == null) {
            return;
        }
        final RecommendItemNew recommendItem = (RecommendItemNew) t.getObject();
        Object item = t.object.getItem();
        if (!(item instanceof RecommendAlbumItem)) {
            return;
        }
        final RecommendAlbumItem albumItem = (RecommendAlbumItem) item;
        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, albumItem);
        //添加专辑Item的ContentDescription
        if (viewHolder.root != null) {
            if (!TextUtils.isEmpty(albumItem.getAlbumTitle())) {
                viewHolder.root.setContentDescription(albumItem.getAlbumTitle());
            } else {
                viewHolder.root.setContentDescription("");
            }
        }
        ImageManager.from(mActivity).displayImage(viewHolder.cover, albumItem.getLargeCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145, com.ximalaya.ting.android.host.R.drawable.host_default_album_145);

        if(AlbumTagUtil.getAlbumCoverTag(albumItem) != -1) {
            viewHolder.ivTag.setImageResource(AlbumTagUtil.getAlbumCoverTag(albumItem));
            viewHolder.ivTag.setVisibility(View.VISIBLE);
        } else {
            viewHolder.ivTag.setVisibility(View.INVISIBLE);
        }

        int textSize = (int) viewHolder.title.getTextSize();
        Spanned richTitle = AlbumAdapter.getRichTitle(albumItem, mActivity, textSize);
        viewHolder.title.setText(richTitle);

        String subTitle = albumItem.getIntro();
        if (!TextUtils.isEmpty(subTitle)) {
            viewHolder.subtitle.setText(Html.fromHtml(subTitle));
        } else {
            viewHolder.subtitle.setText("");
        }
        String playCountStr = StringUtil.getFriendlyNumStr(albumItem.getPlayCount()) + "播放";
        int drawable = R.drawable.main_ic_common_play_count;
        BaseAlbumAdapter.addAlbumInfo(mActivity, viewHolder.layoutAlbumInfo, drawable, playCountStr, Color.parseColor("#999999"), false, false);
        BaseAlbumAdapter.addAlbumInfo(mActivity, viewHolder.layoutAlbumInfo, R.drawable.main_ic_common_track_count, StringUtil.getFriendlyNumStr(albumItem.getIncludeTrackCount()) + " 集", Color.parseColor("#999999"));

        //设置item点击
        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AlbumEventManage.startMatchAlbumFragment(albumItem, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0,
                        albumItem.getRecSrc(), albumItem.getRecTrack(), -1, mActivity);
                notifyItemAction(albumItem, IRecommendFeedItemActionListener.ActionType.CLICK, recommendItem, t);
            }
        });
        //不喜欢该专辑
        viewHolder.ivTrackDislike.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                handleDislike(albumItem, v, position, recommendItem, t);
            }
        });
    }


    private void handleDislike(final RecommendAlbumItem recommendAlbumItem, View anchorView, final int position,
                               final RecommendItemNew recommendItem, final ItemModel itemModel) {
        if (recommendAlbumItem == null) {
            return;
        }
        if (!ToolUtil.isEmptyCollects(recommendAlbumItem.dislikeReasons)) {
            showDislikeFeedbackWindow(recommendAlbumItem, anchorView, new IDataCallBack<JSONObject>() {
                @Override
                public void onSuccess(@Nullable JSONObject object) {
                    //异步操作，需要判断
                    if (mFragment == null) {
                        return;
                    }
                    if (!mFragment.canUpdateUi()) {
                        return;
                    }
                    CustomToast.showSuccessToast("将减少类似推荐");
                    if (dataProvider != null) {
                        dataProvider.removeItem(position);
                    }
                    notifyItemAction(recommendAlbumItem, IRecommendFeedItemActionListener.ActionType.UNINTERESTED, recommendItem, itemModel);
                }

                @Override
                public void onError(int code, String message) {
                    //异步操作，需要判断
                    if (mFragment == null) {
                        return;
                    }
                    if (!mFragment.canUpdateUi()) {
                        return;
                    }
                    CustomToast.showFailToast("操作失败");
                }
            });
        } else {
            if (dataProvider != null) {
                dataProvider.removeItem(position);
            }
        }
    }

    private void notifyItemAction(RecommendAlbumItem album, IRecommendFeedItemActionListener.ActionType actionType
            , RecommendItemNew itemData, ItemModel itemModel) {
        if (recommendFeedItemActionListener != null && album != null) {
            recommendFeedItemActionListener.onItemAction(IRecommendFeedItemActionListener.FeedItemType.ALBUM, album.getId()
                    , actionType, album.getCategoryId(), itemData, itemModel);
        }
    }


    private void showDislikeFeedbackWindow(final RecommendAlbumItem recommendAlbumItem, View anchorView, final IDataCallBack<JSONObject> dislikeCallback) {
        if (recommendAlbumItem != null && recommendAlbumItem.dislikeReasons != null) {
            Activity activity = BaseApplication.getTopActivity();
            if (activity == null) {
                return;
            }
            final DislikeFeedbackWindowNew window = new DislikeFeedbackWindowNew(activity, recommendAlbumItem.dislikeReasons);
            if (mFragment != null) {
                window.setFragment(mFragment);
            }
            window.setOnDismissListener(new PopupWindow.OnDismissListener() {
                @Override
                public void onDismiss() {
                    DislikeReason reason = window.getSelectedDislikeReason();
                    if (reason == null) {
                        return;
                    }
                    Map<String, String> params = new HashMap<>();
                    params.put(HttpParamsConstants.PARAM_ALBUM_ID, recommendAlbumItem.getId() + "");
                    params.put(HttpParamsConstants.PARAM_LEVEL, "album");
                    params.put(HttpParamsConstants.PARAM_SOURCE, "discoveryFeed");
                    params.put("name", reason.name);
                    params.put("value", reason.value);
                    LiteCommonRequest.dislike(params, dislikeCallback);
                }
            });
            window.customShowAtLocation(activity, anchorView);
        }
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_album_home_feed_album, null);
    }

    @Override
    public ViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    public class ViewHolder extends BaseAlbumAdapter.ViewHolder {
        private ImageView offSale; //下架icon
        private ImageView ivTag;
        private ImageView ivTrackDislike;

        public ViewHolder(View convertView) {
            super(convertView);
            ivTrackDislike = convertView.findViewById(R.id.main_iv_track_dislike);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
            cover = (ImageView) convertView.findViewById(R.id.main_iv_album_cover);
            border = convertView.findViewById(R.id.main_album_border);
            title = (TextView) convertView.findViewById(R.id.main_tv_album_title);
            subtitle = (TextView) convertView.findViewById(R.id.main_tv_album_subtitle);
            layoutAlbumInfo = (LinearLayout) convertView.findViewById(R.id.main_layout_album_info);
            offSale = (ImageView) convertView.findViewById(R.id.main_iv_off_sale);
        }
    }
}
