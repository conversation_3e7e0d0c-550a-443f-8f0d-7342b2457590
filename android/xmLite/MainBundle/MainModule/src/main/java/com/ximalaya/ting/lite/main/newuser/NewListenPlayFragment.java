package com.ximalaya.ting.lite.main.newuser;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.Blur;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.model.newuser.QuickListenModel;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.MMKVKeyConstantsKt;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.view.ColorUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.host.view.other.MyViewPager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.model.newuser.NewListenPlayPageModel;
import com.ximalaya.ting.lite.main.model.onekey.OneKeyRadioModel;
import com.ximalaya.ting.lite.main.newuser.adapter.NewUserQuickListenCardItemAdapter;
import com.ximalaya.ting.lite.main.onekey.playpage.pathlayoutmanager.PathLayoutManager;
import com.ximalaya.ting.lite.main.onekey.playpage.view.CardItemView;
import com.ximalaya.ting.lite.main.onekey.playpage.view.OneKeyBackgroundView;
import com.ximalaya.ting.lite.main.onekey.playpage.view.OneKeyRadioPlayCircleView;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.view.LinearItemDecoration;
import com.ximalaya.ting.lite.main.view.RecyclerViewCanDisallowIntercept;
import com.ximalaya.ting.lite.main.view.StickyNavLayout;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by dumingwei on 2021/4/20
 * <p>
 * Desc: copy from OneKeyRadioPlayFragment
 */
public class NewListenPlayFragment extends BaseFragment2 implements View.OnClickListener {

    private static final String TAG = "NewListenPlayFragment";

    public static final String NEW_LISTEN_MODEL = "key_new_listen_model";
    private static final String TITLE_PLAY_ACTION = "title_play_action";
    private static final String TITLE_LEFT_TV = "left_title";

    private StickyNavLayout mStickyNavLayout;
    private MyViewPager myViewPager;
    private TabCommonAdapter mAdapter;
    private boolean mInitStartPlay = false;
    private long mInitPlayRadioId = -1;
    //外部传进来的新人极速听model
    private QuickListenModel mOneKeyRadioModel;
    private View mTitleView;
    //    private ImageView mTopCoverIv;
    private OneKeyBackgroundView mBackgroundView;
    private TextView mScrollToTopTv;
    private ImageView mScrollToTopIv;

    private ImageView mRandomPlay;//随机播放
    private ImageView mPrev;
    private ImageView mPlayOrPause;
    private ImageView mNext;
    private ImageView mIvChangeChannel;
    private RecyclerViewCanDisallowIntercept mCircleRadioRv;
    private PathLayoutManager mLayoutManager;
    private NewUserQuickListenCardItemAdapter mCircleAdapter;
    private boolean mIsTop = false; // 一开始scrollView处于底部

    private List<QuickListenModel> mChannelList;
    private TextView mTrackTitleTv;
    private Bitmap mBlurImage;
    private int mBackgroundColor;
    private int mFinishedBackgroundViewTaskCount = 0;
    private View mTopView;
    private View mLeftGradientView;
    private View mRightGradientView;
    private View mLeftGradientView1;
    private View mRightGradientView1;
    private View mLeftGradientView2;
    private View mRightGradientView2;

    /*引导相关控件开始*/
    private RelativeLayout rlGuideMask;
    private XmLottieAnimationView guideLottieView;
    private TextView tvKnowGuide;
    /*引导相关控件结束*/

    public NewListenPlayFragment() {
        super(false, null);
    }

    public static NewListenPlayFragment newInstance(QuickListenModel model) {
        Bundle args = new Bundle();
        args.putParcelable(NEW_LISTEN_MODEL, model);
        NewListenPlayFragment fragment = new NewListenPlayFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        return "NewUserQuickListenFragment";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        View titleView = findViewById(R.id.main_onekey_radio_play_titlebar);
        View titleGradientView = findViewById(R.id.main_onekey_title_bar_gradient_view);
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            int statusBarHeight = BaseUtil.getStatusBarHeight(mContext);
            titleView.setPadding(0, statusBarHeight, 0, 0);
            titleGradientView.getLayoutParams().height
                    = titleGradientView.getLayoutParams().height + statusBarHeight;
        }

        parseBundle();

        initHeaderRv();
        initHeaderView();
        updateTitleTv();

        initGuideViews();
        mScrollToTopTv = findViewById(R.id.main_scroll_to_top_tv);
        mScrollToTopIv = findViewById(R.id.main_scroll_to_top_iv);
        findViewById(R.id.main_id_stickynavlayout_indicator).setOnClickListener(v -> scrollToTopOrBottom());

        mTopView = findViewById(R.id.main_id_stickynavlayout_topview);

        mStickyNavLayout = findViewById(R.id.main_oneke_radio_play_stickynav);
        mStickyNavLayout.setScrollListener(new StickyScrollListener());
        myViewPager = findViewById(R.id.main_id_stickynavlayout_content);
        myViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                QuickListenModel model = mCircleAdapter.getItemData(position);
                if (model == null) {
                    return;
                }
                Logger.d(TAG, "onPageSelected: position = " + position + " model title = " + model.getTitle());
                if (ConstantsOpenSdk.isDebug) {
                    CustomToast.showToast(model.getTitle());
                }
                scrollAndShowPage(model);

                // 极速听-频道切换  点击事件
                new XMTraceApi.Trace()
                        .click(30783) // 用户点击时上报
                        .put("channelId", String.valueOf(model.getPoolId()))
                        .put("channelName", model.getTitle())
                        .put("currPage", "newUserFastListen")
                        .createTrace();
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

        updatePlayControl();
        mTitleView.setVisibility(View.INVISIBLE);
    }

    /**
     * 初始化引导相关的View
     */
    private void initGuideViews() {
        boolean showGuide = MmkvCommonUtil.getInstance(mContext).getBoolean(MMKVKeyConstantsKt.MMKV_SHOW_NEW_USER_QUICK_LISTEN_GESTURE_GUIDE, true);
        if (showGuide) {
            rlGuideMask = findViewById(R.id.main_rl_guide_mask);
            rlGuideMask.setVisibility(View.VISIBLE);
            guideLottieView = findViewById(R.id.main_guide_lottie_view);
            tvKnowGuide = findViewById(R.id.main_tv_know_guide);
            tvKnowGuide.setOnClickListener(this);
            if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
                Logger.i(TAG, "initGuideViews change top guide");
                int statusBarHeight = BaseUtil.getStatusBarHeight(mContext);
                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) guideLottieView.getLayoutParams();
                params.topMargin = params.topMargin + statusBarHeight;
                guideLottieView.setLayoutParams(params);
            }
            HandlerManager.postOnUIThreadDelay(new Runnable() {
                @Override
                public void run() {
                    guideLottieView.playAnimation();
                }
            }, 300);
        }
    }

    private void updateTitleTv() {
        if (mOneKeyRadioModel != null) {
            View view = titleBar.getActionView(TITLE_LEFT_TV);
            if (view instanceof TextView) {
                ((TextView) view).setText(mOneKeyRadioModel.getTitle());
            }
        }
    }

    private void initHeaderRv() {
        mCircleRadioRv = findViewById(R.id.main_onekey_radio_play_header_rv);
        mCircleRadioRv.setDisallowInterceptTouchEventView(getSlideView());
        mCircleAdapter = new NewUserQuickListenCardItemAdapter(this);
        mCircleRadioRv.setAdapter(mCircleAdapter);
        mCircleRadioRv.addItemDecoration(new LinearItemDecoration(BaseUtil.dp2px(mContext, 10), BaseUtil.dp2px(mContext, 10)));
        Path path = new Path();
        int length = BaseUtil.dp2px(mContext, 205);//两个item中心弧长
        float lengthSwipe = 10f; //length对应的夹角，单位是角度
        float radius = (float) (length * 180f / Math.PI / lengthSwipe);//根据弧长和弧度求出半径
        float swipeHalf = fixAngle((float) (Math.asin((BaseUtil.getScreenWidth(mContext) / 2f / radius)) * 180F / Math.PI));
        float startAngle = 90 + swipeHalf;

        Logger.i(TAG, "length = " + length +
                ", radius = " + radius +
                ", swipeHalf = " + swipeHalf +
                ", startAngle = " + startAngle
        );

        float x = BaseUtil.getScreenWidth(mContext) / 2f;
        //100 RecyclerView高度一半
        float y = BaseUtil.dp2px(mContext, 100) - radius;

        RectF oval = new RectF(x - radius, y - radius, x + radius, y + radius);
        path.addArc(oval, startAngle, -1 * swipeHalf * 2);

        //使用path创建mLayoutManager
        mLayoutManager = new PathLayoutManager(path, BaseUtil.dp2px(mContext, 105), RecyclerView.HORIZONTAL);
        mLayoutManager.setItemDirectionFixed(false); // 保持垂直
//        mLayoutManager.setItemScaleRatio(0.9f, 0, 1f, 0.5f, 0.9f, 1f);

        mLayoutManager.setScrollMode(PathLayoutManager.SCROLL_MODE_LOOP);
        mLayoutManager.setAutoSelect(true);
        mLayoutManager.setItemDirectionFixed(true);

        //RecylerView设置PathLayoutManager
        mCircleRadioRv.setLayoutManager(mLayoutManager);

        //刻度线View
        OneKeyRadioPlayCircleView circleView = findViewById(R.id.main_onekey_radio_play_header_circle_view);
        // final Path path1 = new Path();
        //radius = radius + BaseUtil.dp2px(mContext, 100);
        //RectF oval1 = new RectF(x - radius, y - radius, x + radius, y + radius);
        //path1.addArc(oval1, startAngle, -1 * swipeHalf * 2);

        mLayoutManager.setItemChangeListener(new PathLayoutManager.ItemChangeListener() {
            @Override
            public void itemChange(View item, float angle, float fraction) {
                Logger.i(TAG, "itemChange");
                if (item instanceof CardItemView) {
                    ((CardItemView) item).updateView(fixAngleForTan(angle));
                }
//                item.setRotation(angle / 2f);
            }

            @Override
            public void scrollChange(float fraction) {
                Logger.i(TAG, "scrollChange");
                //重绘刻度线
                circleView.drawCircle(fraction * 360, (int) (y + BaseUtil.dp2px(mContext, 200 - 70)));
            }
        });
        mLayoutManager.setOnItemSelectedListener(new PathLayoutManager.OnItemSelectedListener() {
            @Override
            public void onSelected(int position) {
                QuickListenModel model = mCircleAdapter.getItemData(position);
                if (model == null) {
                    return;
                }
                Logger.d(TAG, "onSelected: " + model.getTitle());
                if (ConstantsOpenSdk.isDebug) {
                    CustomToast.showToast(model.getTitle());
                }
                scrollAndShowPage(model);
            }
        });
    }

    private float fixAngleForTan(float angle) {
        float tan = 0f;
        if (angle < 90) {
            tan = (float) Math.tan(angle * Math.PI / 180);
        } else if (angle > 270) {
            tan = (float) (-1 * Math.tan((360 - angle) * Math.PI / 180));
        } else {
            tan = 0;
        }
        if (tan > 1) {
            return 1;
        }
        if (tan < -1) {
            return -1;
        }
        return tan;
    }

    /**
     * 调整角度，使其在0 ~ 360之间
     *
     * @param rotation 当前角度
     * @return 调整后的角度
     */
    private float fixAngle(float rotation) {
        float angle = 360F;
        if (rotation < 0) {
            rotation += angle;
        }
        if (rotation > angle) {
            rotation %= angle;
        }
        return rotation;
    }

    private void initHeaderView() {
        mRandomPlay = findViewById(R.id.main_random_play);
        mPrev = findViewById(R.id.main_prev);
        mPlayOrPause = findViewById(R.id.main_play_or_pause);
        mNext = findViewById(R.id.main_next);
        mIvChangeChannel = findViewById(R.id.main_change_channel);
        mRandomPlay.setOnClickListener(this);
        mPrev.setOnClickListener(this);
        mPlayOrPause.setOnClickListener(this);
        mNext.setOnClickListener(this);
        mIvChangeChannel.setOnClickListener(this);

        mLeftGradientView = findViewById(R.id.main_onekey_radio_play_header_left_gradient_view);
        mRightGradientView = findViewById(R.id.main_onekey_radio_play_header_right_gradient_view);
        mLeftGradientView1 = findViewById(R.id.main_onekey_radio_play_header_left_gradient_view1);
        mRightGradientView1 = findViewById(R.id.main_onekey_radio_play_header_right_gradient_view1);
        mLeftGradientView2 = findViewById(R.id.main_onekey_radio_play_header_left_gradient_view2);
        mRightGradientView2 = findViewById(R.id.main_onekey_radio_play_header_right_gradient_view2);

        mTrackTitleTv = findViewById(R.id.main_one_key_cur_track_title_tv);
        mBackgroundView = findViewById(R.id.main_onekey_radio_play_top_cover_bg_view);
//        mTopCoverIv = findViewById(R.id.main_onekey_radio_play_top_cover_iv);
        updateBgView();
    }

    private void updateBgView() {
        if (mOneKeyRadioModel == null) {
            return;
        }
        // int defaultColor = mContext.getResources().getColor(R.color.main_color_748c8f);
        if (TextUtils.isEmpty(mOneKeyRadioModel.getCover())) {
            return;
        }
        ImageManager.from(mContext).downloadBitmap(mOneKeyRadioModel.getCover(), new ImageManager.DisplayCallback() {
            @Override
            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                if (!canUpdateUi() || bitmap == null) {
                    return;
                }
                Logger.i(TAG, "onCompleteDisplay bitmap = " + bitmap);
                mFinishedBackgroundViewTaskCount = 0;
                doBlurTask(mOneKeyRadioModel.getCover(), bitmap);
                doFetchColorTask(bitmap);
            }
        });
    }

    private void parseBundle() {
        Bundle args = getArguments();
        if (args != null) {
            mOneKeyRadioModel = args.getParcelable(NEW_LISTEN_MODEL);
        }
    }

    public void doBlurTask(String url, Bitmap bitmap) {
        Context context = getContext();
        new MyAsyncTask<Void, Void, Bitmap>() {
            @Override
            protected Bitmap doInBackground(Void... voids) {
                return Blur.fastBlur(context, bitmap, 30);
            }

            @Override
            protected void onPostExecute(Bitmap bitmap) {
                if (mOneKeyRadioModel != null
                        && !TextUtils.isEmpty(url)
                        && url.equals(mOneKeyRadioModel.getCover())) {
                    mBlurImage = bitmap;
                    onBackgroundViewTaskFinish();
                }
            }
        }.execute();
    }

    private void doFetchColorTask(Bitmap bitmap) {
        LocalImageUtil.getDomainColor(bitmap, 0xff444444, color -> {
            mBackgroundColor = ColorUtil.covertColorToDarkMuted(color);
            onBackgroundViewTaskFinish();
        });
    }

    private void onBackgroundViewTaskFinish() {
        mFinishedBackgroundViewTaskCount++;
        if (mFinishedBackgroundViewTaskCount >= 2 && canUpdateUi()) {
            float[] hsvColor = new float[3];
            Color.colorToHSV(mBackgroundColor, hsvColor);
            if (hsvColor[2] < 0.5) {
                hsvColor[2] = 0.5f;
                mBackgroundColor = Color.HSVToColor(hsvColor);
            }
            mBackgroundView.setImageAndColor(mBlurImage, mBackgroundColor);

            try {
                int height = mTopView.getHeight() + 3;

                String strColor = String.format("%06X", 0xFFFFFF & mBackgroundColor);
                GradientDrawable leftDrawable = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT,
                        new int[]{
                                Color.parseColor("#E6" + strColor),
                                Color.parseColor("#00" + strColor)});

                GradientDrawable rightDrawable = new GradientDrawable(GradientDrawable.Orientation.RIGHT_LEFT,
                        new int[]{
                                Color.parseColor("#E6" + strColor),
                                Color.parseColor("#00" + strColor)});
                mLeftGradientView.setBackgroundDrawable(leftDrawable);
                mLeftGradientView.getLayoutParams().height = height;

                mRightGradientView.setBackgroundDrawable(rightDrawable);
                mRightGradientView.getLayoutParams().height = height;

                mLeftGradientView1.setBackgroundDrawable(leftDrawable);
                mLeftGradientView2.setBackgroundDrawable(leftDrawable);
                mRightGradientView1.setBackgroundDrawable(rightDrawable);
                mRightGradientView2.setBackgroundDrawable(rightDrawable);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
        titleBar.removeView(TitleBar.ActionType.TITLE);

        if (titleBar.getBack() instanceof ImageView) {
            ((ImageView) titleBar.getBack()).setImageResource(R.drawable.main_icon_back_white);
        }

        // 左侧标题
        TitleBar.ActionType titleActionType = new TitleBar.ActionType(TITLE_LEFT_TV,
                TitleBar.LEFT, -1, 0, R.color.main_white, TextView.class
        );
        titleActionType.setFontSize(18);
        titleBar.addAction(titleActionType, null);
        titleBar.update();
        mTitleView = titleBar.getActionView(TITLE_LEFT_TV);
    }

    @Override
    protected void loadData() {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        ArrayMap<String, String> params = new ArrayMap<>(1);
        LiteCommonRequest.getQuickListenTopData(params, new IDataCallBack<NewListenPlayPageModel>() {
            @Override
            public void onSuccess(@Nullable NewListenPlayPageModel playPageModel) {
                if (!canUpdateUi()) {
                    return;
                }
                if (playPageModel == null || ToolUtil.isEmptyCollects(playPageModel.getList())) {
                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                    return;
                }
                doAfterAnimation(() -> {
                    onPageLoadingCompleted(LoadCompleteType.OK);

                    mChannelList = playPageModel.getList();

                    // 从肚脐眼进来的model没有radioId 这时补全model信息
                    // 或者从uting跳转过来的进行model补全
                    if (mOneKeyRadioModel != null) {
                        QuickListenModel findModelWithRedioIdModel = null;
                        for (int i = 0; i < mChannelList.size(); i++) {
                            QuickListenModel channelModel = mChannelList.get(i);
                            if (channelModel == null) {
                                continue;
                            }
                            //查找到进行定位,查找到进行break
                            if (mOneKeyRadioModel.getPoolId() == channelModel.getPoolId()) {
                                findModelWithRedioIdModel = channelModel;
                                //从肚脐眼点进来的时候，会有一个willPlayTrackId，直接播放这个声音id就可以
                                findModelWithRedioIdModel.setWillPlayTrackId(mOneKeyRadioModel.getWillPlayTrackId());
                                break;
                            }
                        }
                        //没有查找到，取第一条数据进行播放
                        if (findModelWithRedioIdModel == null && mChannelList.size() > 0) {
                            findModelWithRedioIdModel = mChannelList.get(0);
                        }
                        //查到了数据，重新进行赋值，进行播放
                        if (findModelWithRedioIdModel != null) {
                            mOneKeyRadioModel = findModelWithRedioIdModel;
                        }
                    } else {
                        mOneKeyRadioModel = mChannelList.get(0);
                    }
                    updateBgView();
                    updateTitleTv();
                    setDataForView();
                });
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
            }
        });
    }

    private void setDataForView() {
        mCircleAdapter.addListData(mChannelList);
        locationCurRadio(false);

        List<TabCommonAdapter.FragmentHolder> fragmentHolders = new ArrayList<>(mChannelList.size());

        int selection = 0;
        boolean found = false; // 保证只取到第一个 和 locationCurRadio 定位一致
        for (int i = 0; i < mChannelList.size(); i++) {
            QuickListenModel model = mChannelList.get(i);
            if (model == null) {
                continue;
            }
            Bundle args = new Bundle();
            args.putParcelable(NEW_LISTEN_MODEL, model);
            TabCommonAdapter.FragmentHolder playListFrag = new TabCommonAdapter.FragmentHolder(
                    NewListenTrackListFragment.class, "播放列表", args);
            fragmentHolders.add(playListFrag);
            if (!found && mOneKeyRadioModel != null && mOneKeyRadioModel.equals(model)) {
                selection = i;
                found = true;
            }
        }
        // 用于第一次播放
        if (mOneKeyRadioModel != null) {
            mInitStartPlay = true;
            mInitPlayRadioId = mOneKeyRadioModel.getPoolId();
        }

        mAdapter = new TabCommonAdapter(getChildFragmentManager(), fragmentHolders);
        myViewPager.setAdapter(mAdapter);
        myViewPager.setCurrentItem(selection);
    }

    private boolean containsModel(List<QuickListenModel> models, QuickListenModel model) {
        if (ToolUtil.isEmptyCollects(models) || model == null) {
            return false;
        }
        for (QuickListenModel obj : models) {
            if (obj != null && obj.getPoolId() == model.getPoolId()) {
                return true;
            }
        }
        return false;
    }

    private void locationCurRadio(boolean smooth) {
        int position = getPosition(mOneKeyRadioModel);
        if (position >= 0) {
            if (smooth) {
                mCircleRadioRv.smoothScrollToPosition(position);
            } else {
                mCircleRadioRv.scrollToPosition(position);
            }
        }
    }

    public void scrollAndShowPage(QuickListenModel model) {
        if (model == null || model.getPoolId() <= 0) {
            return;
        }
        if (mOneKeyRadioModel != null && model.equals(mOneKeyRadioModel)) {
            return;
        }
        mOneKeyRadioModel = model;
        int position = getPosition(model);
        if (position < 0) {
            return;
        }
        myViewPager.setCurrentItem(position);
        locationCurRadio(true);
        updateBgView();
        updateTitleTv();
        mInitStartPlay = false;
    }

    /**
     * 滚动到正确的位置，这个方法是关键哦
     *
     * @param radioModel
     * @return
     */
    private int getPosition(QuickListenModel radioModel) {
        int position = -1;
        if (radioModel == null || radioModel.getPoolId() <= 0) {
            return position;
        }
        List<QuickListenModel> radios = mCircleAdapter.getListData();
        if (ToolUtil.isEmptyCollects(radios)) {
            return position;
        }
        for (int i = 0; i < radios.size(); i++) {
            QuickListenModel model = radios.get(i);
            if (model != null && radioModel.getPoolId() == model.getPoolId()) {
                position = i;
                break;
            }
        }
        return position;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        Logger.i(TAG, "onMyResume");
        XmPlayerManager.getInstance(mContext).addPlayerStatusListener(mPlayStatusListener);

        //todo 这段代码应该不需要
//        Track track = PlayTools.getCurTrack(mContext);
//        QuickListenModel model = getCurFragArgsModel();
//        if (track != null && model != null
//                && (track.getChannelType() != model.getType()
//                || track.getChannelId() != model.getPoolId())) {
//            for (QuickListenModel model1 : mChannelList) {
//                if (model1 != null
//                        && track.getChannelId() == model1.getPoolId()
//                        && track.getChannelType() == model1.getType()) {
//                    scrollAndShowPage(model1);
//                    break;
//                }
//            }
//        }
        //updateTrackTitle();
        setNextAndPreBtnStatus();
        updatePlayControl();
        updateBgView();
    }

    private void updateTrackTitle() {
        Track track = PlayTools.getCurTrack(mContext);
        if (track != null && mOneKeyRadioModel != null && track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_NEW_LISTEN) {
            mTrackTitleTv.setText(track.getTrackTitle());
        }
    }

    public void updateTrackTitle(String trackTitle) {
        if (canUpdateUi()) {
            mTrackTitleTv.setText(trackTitle);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        XmPlayerManager.getInstance(mContext).removePlayerStatusListener(mPlayStatusListener);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_new_listen_play;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_onekey_radio_play_titlebar;
    }

    @Override
    protected boolean darkStatusBar() {
        return false;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    private void scrollToTopOrBottom() {
        mStickyNavLayout.smoothScroll(!mIsTop);
        mIsTop = !mIsTop;
        setScrollTopState(mIsTop);
    }

    private void setScrollTopState(boolean top) {
        //mScrollToTopIv.setRotation(top ? 180 : 0);
        //mScrollToTopTv.setText(top ? "收起" : "");
    }

    private boolean isCurRadioPlaying() {
        Track track = PlayTools.getCurTrack(mContext);
        return mOneKeyRadioModel != null && track != null
                //&& track.getChannelId() == mOneKeyRadioModel.getPoolId()
                && XmPlayerManager.getInstance(mContext).isPlaying();
    }

    private void updatePlayControl() {
        if (!canUpdateUi()) {
            return;
        }
        boolean isPlaying = isCurRadioPlaying();
        updatePlayCtlUi(isPlaying);
    }

    private void updatePlayCtlUi(boolean playing) {
        mPlayOrPause.setImageResource(playing ? R.drawable.main_ic_onekey_radio_pause : R.drawable.main_ic_onekey_radio_play);
//        Drawable drawable = null;
//        Drawable titleDrawable = null;
//        String text = null;
//        if (playing) {
//            drawable = LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_lastplay_pause);
//            titleDrawable = LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_lastplay_pause);
//            text = "暂停";
//        } else {
//            drawable = LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_lastplay_play);
//            titleDrawable = LocalImageUtil.getDrawable(mContext, R.drawable.main_ic_lastplay_play);
//            text = "播放";
//        }
//        mPlayControlTv.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null);
//        mPlayControlTv.setText(text);
//        if (mTitlePlayControlTv != null) {
//            mTitlePlayControlTv.setCompoundDrawablesWithIntrinsicBounds(titleDrawable, null, null, null);
//            mTitlePlayControlTv.setText(text);
//        }
    }

    private QuickListenModel getCurFragArgsModel() {
        if (myViewPager == null || mAdapter == null) {
            return null;
        }
        int index = myViewPager.getCurrentItem();
        TabCommonAdapter.FragmentHolder fh = mAdapter.getFragmentHolderAtPosition(index);
        if (fh != null && fh.args != null) {
            return fh.args.getParcelable(NEW_LISTEN_MODEL);
        }
        return null;
    }

    /**
     * 获取当前的声音列表Fragment
     *
     * @return
     */
    private NewListenTrackListFragment getPlayListFragment() {
        if (myViewPager == null || mAdapter == null) {
            return null;
        }
        int index = myViewPager.getCurrentItem();
        Fragment frag = mAdapter.getFragmentAtPosition(index);
        if (frag instanceof NewListenTrackListFragment) {
            return (NewListenTrackListFragment) frag;
        }
        return null;
    }

    public boolean needStartPlay(long radioId) {
        if (mInitPlayRadioId == radioId && mInitStartPlay) {
            return true;
        }
        return false;
    }

    public void updateShowTrackTitle(Track track) {
        if (track != null) {
            mTrackTitleTv.setText(track.getTrackTitle());
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        if (id == R.id.main_random_play) {
            //stepUp(true);
            randomPlay();
            QuickListenModel model = getCurFragArgsModel();
            if (model != null) {
                new XMTraceApi.Trace()
                        .click(30788) // 用户点击时上报
                        .put("channelId", String.valueOf(model.getPoolId()))
                        .put("channelName", model.getTitle())
                        .put("currPage", "newUserFastListen")
                        .createTrace();
            }
        } else if (id == R.id.main_change_channel) {
            changeChannel();
            QuickListenModel model = getCurFragArgsModel();
            if (model != null) {
                new XMTraceApi.Trace()
                        .click(30787) // 用户点击时上报
                        .put("channelId", String.valueOf(model.getPoolId()))
                        .put("channelName", model.getTitle())
                        .put("currPage", "newUserFastListen")
                        .createTrace();
            }
        } else if (id == R.id.main_prev) {
            PlayTools.playPre(mContext);
            QuickListenModel model = getCurFragArgsModel();
            if (model != null) {
                new XMTraceApi.Trace()
                        .click(30785) // 用户点击时上报
                        .put("channelId", String.valueOf(model.getPoolId()))
                        .put("channelName", model.getTitle())
                        .put("currPage", "newUserFastListen")
                        .createTrace();
            }
        } else if (id == R.id.main_next) {
            PlayTools.playNext(mContext);
            QuickListenModel model = getCurFragArgsModel();
            if (model != null) {
                new XMTraceApi.Trace()
                        .click(30786) // 用户点击时上报
                        .put("channelId", String.valueOf(model.getPoolId()))
                        .put("channelName", model.getTitle())
                        .put("currPage", "newUserFastListen")
                        .createTrace();
            }
        } else if (id == R.id.main_play_or_pause) {
            if (XmPlayerManager.getInstance(mContext).isPlaying()) {
                XmPlayerManager.getInstance(mContext).pause();
            } else {
                XmPlayerManager.getInstance(mContext).play();
            }
            QuickListenModel model = getCurFragArgsModel();
            if (model != null) {
                new XMTraceApi.Trace()
                        .click(30609) // 用户点击时上报
                        .put("channelId", String.valueOf(model.getPoolId()))
                        .put("channelName", model.getTitle())
                        .put("currPage", "newUserFastListen")
                        .createTrace();
            }
        } else if (id == R.id.main_tv_know_guide) {
            MmkvCommonUtil.getInstance(mContext).saveBoolean(MMKVKeyConstantsKt.MMKV_SHOW_NEW_USER_QUICK_LISTEN_GESTURE_GUIDE, false);
            if (rlGuideMask != null) {
                rlGuideMask.setVisibility(View.GONE);
            }
            if (guideLottieView != null) {
                guideLottieView.cancelAnimation();
            }
        }
    }

    /**
     * 需要调用NewListenTrackListFragment的方法
     */
    private void randomPlay() {
        NewListenTrackListFragment trackListFragment = getPlayListFragment();
        if (trackListFragment != null && trackListFragment.canUpdateUi()) {
            trackListFragment.randomPlay();
        }
    }

    private void stepUp(boolean flag) {
        int duration = XmPlayerManager.getInstance(mContext).getDuration();
        int pos = XmPlayerManager.getInstance(mContext).getPlayCurrPositon() + (flag ? (-15 * 1000) : (15 * 1000));

        if (pos < 0) {
            pos = 0;
        }

        pos = pos > duration ? duration : pos;
        XmPlayerManager.getInstance(mContext).seekTo(pos);
    }

    private void changeChannel() {
        int nextItem = myViewPager.getCurrentItem() + 1;
        if (myViewPager.getAdapter() != null) {
            if (nextItem < myViewPager.getAdapter().getCount()) {
                myViewPager.setCurrentItem(nextItem);
            } else {
                myViewPager.setCurrentItem(0);
            }
        }
    }

    private void setNextAndPreBtnStatus() {
        if (!canUpdateUi()) {
            return;
        }
        boolean hasNext = XmPlayerManager.getInstance(mContext).hasNextSound();
        boolean hasPrevious = XmPlayerManager.getInstance(mContext).hasPreSound();

        XmPlayListControl.PlayMode playMode = XmPlayerManager.getInstance(mContext).getPlayMode();
        if (playMode == XmPlayListControl.PlayMode.PLAY_MODEL_LIST_LOOP
                && !XmPlayerManager.getInstance(mContext).getPlayList().isEmpty()) {
            hasNext = true;
            hasPrevious = true;
        } else if (true /**isFromOneKeyPlay**/) {
            hasNext = true;
        }
        mNext.setAlpha(hasNext ? 1 : 0.5f);
        mPrev.setAlpha(hasPrevious ? 1 : 0.5f);
    }

    private IXmPlayerStatusListener mPlayStatusListener = new IXmPlayerStatusListener() {
        @Override
        public void onPlayStart() {
            if (!canUpdateUi()) {
                return;
            }
//            Track track = PlayTools.getCurTrack(mContext);
//            if (track != null) {
//                mTrackTitleTv.setText(track.getTrackTitle());
//            }
            //updateTrackTitle();
            updatePlayControl();
            setNextAndPreBtnStatus();
        }

        @Override
        public void onPlayPause() {
            updatePlayControl();
        }

        @Override
        public void onPlayStop() {
            updatePlayControl();
        }

        @Override
        public void onSoundPlayComplete() {
            updatePlayControl();
        }

        @Override
        public void onSoundPrepared() {

        }

        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
            if (lastModel instanceof Track && curModel == null) {
                if (!PlayTools.canPlayTrackForMainProcess((Track) lastModel)) {
                    CustomToast.showFailToast("播放失败，请稍后重试");
                }
            }
        }

        @Override
        public void onBufferingStart() {

        }

        @Override
        public void onBufferingStop() {

        }

        @Override
        public void onBufferProgress(int percent) {

        }

        @Override
        public void onPlayProgress(int currPos, int duration) {

        }

        @Override
        public boolean onError(XmPlayerException exception) {
            return false;
        }
    };

    class StickyScrollListener implements StickyNavLayout.ScrollListener {
        private int mShowScrollY = BaseUtil.dp2px(mContext, 50);

        @Override
        public void onScroll(int scrollY, int totalScrollY) {
            mBackgroundView.setTranslationY(-scrollY);
            if (scrollY >= mShowScrollY) {
                float alpha = (scrollY - mShowScrollY) * 1f / (totalScrollY - mShowScrollY);
                mTitleView.setAlpha(alpha);
                mTitleView.setVisibility(View.VISIBLE);
//                mTitlePlayControlTv.setAlpha(alpha);
//                mTitlePlayControlTv.setVisibility(View.VISIBLE);
            } else {
                mTitleView.setVisibility(View.INVISIBLE);
//                mTitlePlayControlTv.setVisibility(View.INVISIBLE);
            }

            if (scrollY <= 0 && mIsTop) {
                mIsTop = false;
                setScrollTopState(mIsTop);
            } else if (mStickyNavLayout.isTopHidden() && !mIsTop) {
                mIsTop = true;
                setScrollTopState(mIsTop);
            }
        }

        @Override
        public void onScrollStop(int orient, int scrollY, int totalScrollY) {
        }

        @Override
        public void onScrollToEdge(int scrollY, int totalScrollY) {

        }

        @Override
        public void onStateChange(boolean isStick) {

        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mLayoutManager != null) {
            mLayoutManager.onDestroy();
        }

        QuickListenModel model = getCurFragArgsModel();
        if (model != null) {
            new XMTraceApi.Trace()
                    .click(30791) // 用户点击时上报
                    .put("channelId", String.valueOf(model.getPoolId()))
                    .put("channelName", model.getTitle())
                    .put("currPage", "newUserFastListen")
                    .createTrace();
        }
    }
}
