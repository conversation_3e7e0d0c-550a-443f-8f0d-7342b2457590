package com.ximalaya.ting.lite.main.model.album;

/**
 * <AUTHOR> on 17/8/24.
 */

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.host.model.base.ListModeBase;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.List;

/**
 * 首页局部刷新时的数据model
 * <AUTHOR>
 */

public class RecommendRefreshModel<T> extends ListModeBase<T> {
    public List<RecommendCategoryTag> keywords;

    public RecommendRefreshModel(List<T> list, List<RecommendCategoryTag> keywords) {
        setList(list);
        this.keywords = keywords;
    }

    public RecommendRefreshModel(String json, Class<T> classType, String listTypem)
            throws JSONException {
        super(json, classType, listTypem);

        try {
            JSONObject jsonObject = new JSONObject(json);

            if (jsonObject.has("keywords")) {
                Type type = new TypeToken<List<RecommendCategoryTag>>() {
                }.getType();
                keywords = new Gson().fromJson(jsonObject.optString("keywords"), type);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
