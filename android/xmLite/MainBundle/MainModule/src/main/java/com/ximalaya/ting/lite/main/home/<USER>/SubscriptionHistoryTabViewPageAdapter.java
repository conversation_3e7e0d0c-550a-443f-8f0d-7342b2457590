package com.ximalaya.ting.lite.main.home.adapter;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import java.util.List;

/**
 * Created by qinhuifeng on 2021/1/17
 *
 * <AUTHOR>
 */
public class SubscriptionHistoryTabViewPageAdapter extends FragmentStatePagerAdapter {

    private List<Fragment> mFragmentList;
    private List<String> mTitles;

    public SubscriptionHistoryTabViewPageAdapter(@NonNull FragmentManager fm, List<Fragment> list, List<String> titles) {
        super(fm);
        mFragmentList = list;
        mTitles = titles;
    }


    @NonNull
    @Override
    public Fragment getItem(int position) {
        if (mFragmentList != null && mFragmentList.size() > position) {
            Fragment fragment = mFragmentList.get(position);
            if (fragment != null) {
                return fragment;
            }
        }
        return new Fragment();
    }

    @Override
    public int getCount() {
        if (mFragmentList != null) {
            return mFragmentList.size();
        }
        return 0;
    }

    @Nullable
    @Override
    public CharSequence getPageTitle(int position) {
        if (mTitles != null && mTitles.size() > position) {
            String title = mTitles.get(position);
            if (!TextUtils.isEmpty(title)) {
                return title;
            }
        }
        return "";
    }
}
