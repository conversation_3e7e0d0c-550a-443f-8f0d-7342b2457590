package com.ximalaya.ting.lite.main.home.presenter;

import android.app.Activity;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.lite.main.home.manager.HomeRecommendAdapterAddFloorManager;

/**
 * Created by qinhuifeng on 2019-08-05
 *
 * <AUTHOR>
 */
public class HomeRecommendContact {

    public interface IFragmentView {
        Activity getActivity();

        BaseFragment2 getBaseFragment2();

        void setHasMore(boolean hasMore);

        RefreshLoadMoreListView getRefreshLoadMoreListView();

        @Nullable
        HomeRecommendAdapterAddFloorManager getHomeRecommendAdapterAddFloorManager();
    }
}
