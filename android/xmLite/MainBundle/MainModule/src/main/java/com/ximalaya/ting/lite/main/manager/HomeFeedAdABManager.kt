package com.ximalaya.ting.lite.main.manager

import com.ximalaya.ting.android.adsdk.AdSDK
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.android.xmabtest.ABTest

object HomeFeedAdABManager {

    private const val KEY_HOME_AD_SWITCH = "key_home_ad_switch"

    fun saveHomeAdSwitch() {
        // false 不移除广告  显示首页广告
        val result: Boolean = when (ABTest.getString("mainpage_no_ad", "false")) {
            "true" -> {
                false
            }
            else -> {
                true
            }
        }

        MmkvCommonUtil.getInstance(BaseApplication.mAppInstance).saveBoolean(KEY_HOME_AD_SWITCH, result)
    }

    fun isShowHomeAd(): Boolean {
        return MmkvCommonUtil.getInstance(BaseApplication.mAppInstance).getBoolean(KEY_HOME_AD_SWITCH, true)
    }

    fun isNewProvider(): Boolean {
        var result = false
        when (ConfigureCenter.getInstance().getInt(CConstants.Group_Ad.GROUP_NAME, CConstants.Group_Ad.ITEM_HOME_FEED_AD_SWITCH_KEY, 1)) {
            1 -> {
                // 老版本代码
                result = false
            }
            2 -> {
                // 新版本代码
                result = true
            }
            3 -> {
                // AB测试 value=1，表示走老版本 2 走新版本
                result = ABTest.getInt("FlowAdVersion", 1) == 2
            }
        }
        if (result) {
            result = AdSDK.checkInitSuccess()
        }
        return result
    }
}