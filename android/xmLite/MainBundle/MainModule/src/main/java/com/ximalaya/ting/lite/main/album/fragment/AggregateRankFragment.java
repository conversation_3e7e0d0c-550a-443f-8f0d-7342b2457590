package com.ximalaya.ting.lite.main.album.fragment;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.viewpager.widget.ViewPager;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.astuetz.PagerSlidingTabStrip;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankArgsModel;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankCategoryModel;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankListTabsDataItemRsp;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankListTabsDataRsp;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 聚合排行榜
 *
 * <AUTHOR> on 2016/12/2.
 */
public class AggregateRankFragment extends BaseFragment2 implements View.OnClickListener {

    public static int DEF_SHOW_TAB_NUMBER = 3;
    private PagerSlidingTabStrip mTabs;
    private ViewPager mPager;
    private TabCommonAdapter mAdapter;
    private ImageView mIVBack;

    public static final String ARGS_SELECT_RANK_MODEL = "args_select_rank_model";

    //分组信息，类型分类信息
    public List<AggregateRankListTabsDataItemRsp> mAggregateRankList = new ArrayList<>();
    private List<TabCommonAdapter.FragmentHolder> mFragmentList = new CopyOnWriteArrayList<>();

    private AggregateRankArgsModel mSelectRankModel;

    public static Bundle newArgument(AggregateRankArgsModel selectRankModel) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ARGS_SELECT_RANK_MODEL, selectRankModel);
        return bundle;
    }

    public AggregateRankFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_RELATIVELAYOUT, null);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mSelectRankModel = arguments.getParcelable(ARGS_SELECT_RANK_MODEL);
        }
        if (mSelectRankModel == null) {
            mSelectRankModel = new AggregateRankArgsModel();
        }
    }

    @Override
    protected String getPageLogicName() {
        return "AggregateRankFragment";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mTabs = findViewById(R.id.main_tabs);
        mPager = findViewById(R.id.main_content);
        mTabs.setTabPaddingLeftRight(BaseUtil.dp2px(getActivity(), 17));
        mTabs.setDisallowInterceptTouchEventView((ViewGroup) mTabs.getParent());
        mIVBack = findViewById(R.id.main_iv_back);
        mIVBack.setOnClickListener(this);
        AutoTraceHelper.bindData(mIVBack, "");

        mPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageSelected(int page) {
                Logger.log("GroupRank" + "onPageSelected" + page);
                if (getSlideView() != null) {
                    if (page == 0) {
                        getSlideView().setSlide(true);
                    } else {
                        getSlideView().setSlide(false);
                    }
                }
            }

            @Override
            public void onPageScrolled(int arg0, float arg1, int arg2) {
            }

            @Override
            public void onPageScrollStateChanged(int arg0) {
            }
        });
    }

    @Override
    protected void loadData() {
        requestRankCategory();
    }

    /**
     * 请求排行榜分组信息
     */
    private void requestRankCategory() {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        Map<String, String> params = new ArrayMap<>();
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        params.put("speed", "2");
        LiteCommonRequest.getSimpleAggregateRank(params, new IDataCallBack<AggregateRankListTabsDataRsp>() {
            @Override
            public void onSuccess(@Nullable final AggregateRankListTabsDataRsp object) {
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        if (object == null || object.aggregateRankList == null) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }
                        if (object.aggregateRankList.size() == 0) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }
                        mAggregateRankList.clear();
                        mFragmentList.clear();

                        //通过selectRankClusterId或者selectRankingListId反向查找selectClusterType
                        findSelectClusterType(object);

                        for (int i = 0; i < object.aggregateRankList.size(); i++) {
                            //目前产品要求，只取前3个tab，后续增加客源直接修改此处
                            //打开的话，需要注意选中状态的处理
                            if (i >= DEF_SHOW_TAB_NUMBER) {
                                break;
                            }
                            AggregateRankListTabsDataItemRsp itemRsp = object.aggregateRankList.get(i);
                            if (itemRsp == null || itemRsp.aggregateListConfig == null || itemRsp.rankGroups == null || itemRsp.rankGroups.size() == 0) {
                                continue;
                            }
                            mAggregateRankList.add(itemRsp);
                            int clusterType = itemRsp.aggregateListConfig.clusterType;
                            long selectRankListId = -1;
                            //找到对应的tab，设置选中item
                            if (clusterType == mSelectRankModel.selectClusterType) {
                                selectRankListId = mSelectRankModel.selectRankingListId;
                            }
                            Bundle bundle = AggregateAlbumRankFragment.newArgument(clusterType, selectRankListId);
                            TabCommonAdapter.FragmentHolder recommendHolder = new TabCommonAdapter.FragmentHolder(AggregateAlbumRankFragment.class, itemRsp.aggregateListConfig.aggregateName, bundle);
                            mFragmentList.add(recommendHolder);
                        }
                        if (mFragmentList.size() == 0) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }
                        mAdapter = new TabCommonAdapter(getChildFragmentManager(), mFragmentList);
                        //只有一个的情况下，不展示tab
                        mTabs.setVisibility(mFragmentList.size() < 2 ? View.GONE : View.VISIBLE);
                        //4个内，平分布局，超过4个往后排列
                        mTabs.setShouldExpand(mFragmentList.size() < 5);
                        mPager.setAdapter(mAdapter);
                        mTabs.setViewPager(mPager);

                        //上个循环会做数据校验操作，重新遍历，计算选中的tab
                        int selectTabPosition = 0;
                        for (int i = 0; i < mAggregateRankList.size(); i++) {
                            AggregateRankListTabsDataItemRsp itemRsp = object.aggregateRankList.get(i);
                            if (itemRsp == null || itemRsp.aggregateListConfig == null || itemRsp.rankGroups == null || itemRsp.rankGroups.size() == 0) {
                                continue;
                            }
                            if (itemRsp.aggregateListConfig.clusterType == mSelectRankModel.selectClusterType) {
                                selectTabPosition = i;
                            }
                        }
                        mPager.setCurrentItem(selectTabPosition);

                        if (getSlideView() != null) {
                            if (selectTabPosition == 0) {
                                getSlideView().setSlide(true);
                            } else {
                                getSlideView().setSlide(false);
                            }
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                ApiErrorToastManager.showToast(code, message);
            }
        });
    }

    /**
     * 根据默认选中的selectClusterType或者selectRankClusterId反向查找selectClusterType
     */
    private void findSelectClusterType(AggregateRankListTabsDataRsp object) {
        if (object == null) {
            return;
        }
        //全部大于0，不在做查找操作
        if (mSelectRankModel.selectClusterType > 0 && mSelectRankModel.selectRankClusterId > 0 && mSelectRankModel.selectRankingListId > 0) {
            return;
        }
        //根据selectRankClusterId和selectRankingListId查找selectClusterType
        if (mSelectRankModel.selectRankClusterId > 0 || mSelectRankModel.selectRankingListId > 0) {
            for (int i = 0; i < object.aggregateRankList.size(); i++) {
                //目前产品要求，只取前3个tab，后续增加客源直接修改此处
                //打开的话，需要注意选中状态的处理
                if (i >= DEF_SHOW_TAB_NUMBER) {
                    break;
                }
                AggregateRankListTabsDataItemRsp itemRsp = object.aggregateRankList.get(i);
                if (itemRsp == null || itemRsp.aggregateListConfig == null || itemRsp.rankGroups == null || itemRsp.rankGroups.size() == 0) {
                    continue;
                }
                for (int j = 0; j < itemRsp.rankGroups.size(); j++) {
                    AggregateRankCategoryModel rankCategoryModel = itemRsp.rankGroups.get(j);
                    if (rankCategoryModel == null) {
                        continue;
                    }
                    if (mSelectRankModel.selectRankingListId > 0 && mSelectRankModel.selectRankingListId == rankCategoryModel.rankingListId) {
                        //selectRankingListId>0,使用selectRankingListId反向查找
                        mSelectRankModel.selectRankClusterId = rankCategoryModel.rankClusterId;
                        mSelectRankModel.selectClusterType = itemRsp.aggregateListConfig.clusterType;
                        break;
                    } else if (mSelectRankModel.selectRankClusterId > 0 && mSelectRankModel.selectRankClusterId == rankCategoryModel.rankClusterId) {
                        //selectRankClusterId>0,使用rankClusterId反向查找
                        mSelectRankModel.selectRankingListId = rankCategoryModel.rankingListId;
                        mSelectRankModel.selectClusterType = itemRsp.aggregateListConfig.clusterType;
                        break;
                    }
                }
                //内部for循环已经查询到了，终止外层for循环
                if (mSelectRankModel.selectClusterType > 0) {
                    break;
                }
            }
        }
        //最终没有查询到selectClusterType，selectRankClusterId和selectRankingListId也没有意义，防止滑动到其他tab默认选中情况
        //清除selectRankingListId和selectRankClusterId
        if (mSelectRankModel.selectClusterType <= 0) {
            mSelectRankModel.selectRankClusterId = -1;
            mSelectRankModel.selectRankingListId = -1;
        }
    }

    /**
     * 获取子页面相关的分组和分类信息
     */
    public AggregateRankListTabsDataItemRsp getAggregateRankCategoryModelByClusterType(long clusterType) {
        for (int i = 0; i < mAggregateRankList.size(); i++) {
            AggregateRankListTabsDataItemRsp itemRsp = mAggregateRankList.get(i);
            if (itemRsp == null || itemRsp.aggregateListConfig == null) {
                continue;
            }
            if (itemRsp.aggregateListConfig.clusterType == clusterType) {
                return itemRsp;
            }
        }
        return null;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_aggregate_rank;
    }

    @Override
    public void onClick(View v) {
        int i1 = v.getId();
        if (i1 == R.id.main_iv_back) {
            finishFragment();
        }
    }

}
