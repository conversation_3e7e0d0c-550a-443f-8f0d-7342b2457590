package com.ximalaya.ting.lite.main.login;

import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import androidx.annotation.NonNull;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.util.PadAdaptUtil;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.view.VerticalSlideRelativeLayout;

import java.lang.ref.WeakReference;

import static android.view.KeyEvent.KEYCODE_BACK;

/**
 * Created by nali on 2018/5/4.
 * <p>
 *
 * <AUTHOR>
 * <p>
 * 拷贝主app SmsLoginProxyFragment
 */
public class SmsLoginProxyFragment extends BaseDialogFragment implements VerticalSlideRelativeLayout.ISlideListener {
    private SmsLoginFragment mSmsLoginFragment;
    private VerticalSlideRelativeLayout mSlideRelativeLayout;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.main_sms_login_proxy_layout, null);
        if (view == null) {
            dismiss();
            return null;
        }
        mSlideRelativeLayout = view.findViewById(R.id.main_sms_login_proxy_slide);
        mSlideRelativeLayout.setSlideListen(this);

        View mClickView = view.findViewById(R.id.main_sms_login_click_view);
        mClickView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mSmsLoginFragment == null) {
                    dismiss();
                    return;
                }
                if (mSmsLoginFragment.onBackPressed()) {
                    return;
                }
                dismiss();
            }
        });
        try {
            mSmsLoginFragment = new SmsLoginFragment();
            mSmsLoginFragment.setArguments(getArguments());
            getChildFragmentManager().beginTransaction().add(R.id.main_sms_login_proxy_fra, mSmsLoginFragment).addToBackStack(null).commit();
            getChildFragmentManager().executePendingTransactions();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return view;
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
    }

    @Override
    public void onResume() {
        super.onResume();

        HandlerManager.postOnUIThread(new MyRunnable(this));
    }

    static class MyRunnable implements Runnable {

        WeakReference<SmsLoginProxyFragment> mWeakReference;

        MyRunnable(SmsLoginProxyFragment smsLoginProxyFragment) {
            mWeakReference = new WeakReference<>(smsLoginProxyFragment);
        }

        @Override
        public void run() {
            if (mWeakReference != null && mWeakReference.get() != null && mWeakReference.get().canUpdateUi()) {
                SmsLoginProxyFragment smsLoginProxyFragment = mWeakReference.get();
                Dialog dialog = smsLoginProxyFragment.getDialog();
                if (dialog != null) {
                    Window window = smsLoginProxyFragment.getDialog().getWindow();
                    if (window != null) {
                        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
                    }
                }
            }

        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Dialog dialog = getDialog();
        if (dialog == null || dialog.getWindow() == null || dialog.getWindow().getAttributes() == null || !(getActivity() instanceof BaseFragmentActivity)) {
            return;
        }
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            dialog.getWindow().getAttributes().width = Math.min(PadAdaptUtil.getWidth(getActivity()), PadAdaptUtil.getHeight(getActivity()));
        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            dialog.getWindow().getAttributes().width = WindowManager.LayoutParams.MATCH_PARENT;
        }
        dialog.getWindow().setAttributes(dialog.getWindow().getAttributes());
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        if (dialog == null) {
            return null;
        }
        dialog.setOnKeyListener(onKeyListener);
        dialog.setCanceledOnTouchOutside(false);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);

        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            window.getDecorView().setPadding(0, 0, 0, 0);
            window.setGravity(Gravity.BOTTOM);
            window.setBackgroundDrawableResource(R.color.host_transparent);

            if (getActivity() instanceof BaseFragmentActivity
                    && PadAdaptUtil.isPad(getActivity())
                    && getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
                lp.width = Math.min(PadAdaptUtil.getWidth(getActivity()), PadAdaptUtil.getHeight(getActivity()));
            } else {
                lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            }
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            window.setAttributes(lp);
            window.setWindowAnimations(R.style.host_dialog_push_in_out);

            // 自动弹出键盘
            window.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE
                    | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        }
        setCancelable(true);
        return dialog;
    }

    DialogInterface.OnKeyListener onKeyListener = new DialogInterface.OnKeyListener() {
        @Override
        public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
            if (keyCode == KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_UP && mSmsLoginFragment != null) {
                return mSmsLoginFragment.onBackPressed();
            }
            return false;
        }
    };

    @Override
    public void onDestroy() {
        if (getDialog() != null) {
            getDialog().setOnKeyListener(null);
        }

        if (mSlideRelativeLayout != null) {
            mSlideRelativeLayout.setSlideListen(null);
            mSlideRelativeLayout = null;
        }
        super.onDestroy();
        if (mOnDestroyHandle != null) {
            mOnDestroyHandle.onReady();
            mOnDestroyHandle = null;
        }

        mSmsLoginFragment = null;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Logger.log("SmsLoginFragment : onActivityResult " + requestCode + "  " + resultCode);
        if (mSmsLoginFragment != null) {
            mSmsLoginFragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onSlideOut() {
        dismiss();
    }
}
