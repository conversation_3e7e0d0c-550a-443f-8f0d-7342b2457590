package com.ximalaya.ting.lite.main.model.album;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.model.album.AlbumM;

import java.util.List;

/**
 * Created by <PERSON> on 2017/10/25.
 *
 * <AUTHOR>
 */

public class AlbumMInWoTing extends AlbumM {
    private boolean showDiscount;
    private List<AlbumFeatureLabel> featureLabelList;
    private TrackingCampInfo trackingCampInfo;

    public boolean isShowDiscount() {
        return showDiscount;
    }

    public void setShowDiscount(boolean showDiscount) {
        this.showDiscount = showDiscount;
    }

    public List<AlbumFeatureLabel> getFeatureLabelList() {
        return featureLabelList;
    }

    public void setFeatureLabelList(List<AlbumFeatureLabel> featureLabelList) {
        this.featureLabelList = featureLabelList;
    }

    public TrackingCampInfo getTrackingCampInfo() {
        return trackingCampInfo;
    }

    public void setTrackingCampInfo(TrackingCampInfo trackingCampInfo) {
        this.trackingCampInfo = trackingCampInfo;
    }

    public static class AlbumFeatureLabel {
        @SerializedName("text")
        private String text;
        @SerializedName("color")
        private String color;

        public String getText() {
            return text;
        }

        public String getColor() {
            return color;
        }
    }

    public static class TrackingCampInfo {
        @SerializedName("campGroupId")
        private long campGroupId;//训练营groupId
        @SerializedName("campUnreadNum")
        private int campUnreadNum;//训练营消息未读数

        public long getCampGroupId() {
            return campGroupId;
        }

        public int getCampUnreadNum() {
            return campUnreadNum;
        }
    }
}
