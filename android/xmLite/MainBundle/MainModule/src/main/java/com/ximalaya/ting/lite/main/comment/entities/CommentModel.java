package com.ximalaya.ting.lite.main.comment.entities;

import androidx.annotation.DrawableRes;

import com.google.android.exoplayer2.C;
import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;

import org.jetbrains.annotations.Nullable;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 声音评论的Model
 */

/**
 * <AUTHOR>
 */
public class CommentModel implements Serializable, Cloneable {
    public final static int GROUP_TYPE_HOT = 1;
    public final static int GROUP_TYPE_ALL = 0;
    public final static int GROUP_TYPE_MY_COMMENT = 2;

    private static final long serialVersionUID = 1L;
    public static final int SUCCESS = 0;
    public int ret = -1;//’:0,							//0:成功,其他都是失败
    public String msg;//’:’’,

    @SerializedName(value = "id", alternate = {"commentId"})
    public long id;//":2734,//评论id
    public long uid;//":73,//评论用户id
    public String nickname;//":"xuxiyuan",//评论用户昵称
    public String smallHeader;//":"group1/M00/04/E6/kM106.jpg", //用户小头像
    public String content;//":"sfsdfsfds",  		//评论内容
    @SerializedName(value = "createdAt", alternate = {"createAt", "created_at"})
    public long createdAt;//":1345690794000,		//评论时间
    @SerializedName(value = "trackId", alternate = {"track_id", "recordId"})
    public long trackId;//评论声音id
    @SerializedName(value = "trackUid", alternate = {"recordUid"})
    public long trackUid;//当前声音主播id
    public long parentId;//”: 5173//父评论id
    public long parentUid;//父评论用户id
    public String pNickName;//父评论用户昵称
    public long ancestorId;//原始评论id
    public long startTime;
    public int likes;
    public boolean isVip;
    public long vipExpireTime;//vip到期时间
    public List<CommentModel> replies;//当前评论最热三条回复内容
    public int replyCount;
    public int shareCount;
    public String trackTitle;
    public boolean userPost;
    // 语音评论相关
    // 语音是否在播放
    public boolean isPlaying;
    // 语音地址
    public String voiceUrl;
    // 语音时长
    public int voiceDuration;
    // 语音本地地址
    public String voicePath;
    // 图片评论
    public List<String> imageUrls = new ArrayList<>();
    public String pictureUrl;
    // 会员弹幕颜色
    public int bulletColor;
    // 类型
    // 0 - 普通
    // 1 - 礼物
    // 2 - 语音
    public int type;
    public int groupType;
    public String albumTitle;
    public String albumCover;
    public String trackCover;
    public String albumAuthor;
    @SerializedName(value = "albumId", alternate = {"album_id"})
    public long albumId;
    public double second;//”:2.123,	//评论在声音中的时间，秒数,浮点数，小数点后3位
    @SerializedName(value = "updatedAt", alternate = {"updated_at"})
    public long updatedAt;//":1345690794000,	//评论更新时间
    public boolean liked;
    public boolean lookAlled;    // 是否点击了查看全部
    public boolean isFromDubbing = false;
    public int business = 0;
    public @DrawableRes
    int iconRes;
    public boolean isTop;
    public boolean isDeleted;

    public boolean isTrackDetailTop = false; //评论详情页-顶部评论，本地使用
    public int commentThemeEntryFlag; //主播活动,用于控制header入口，本地使用,在CommentViewNew中定义 -1不显示 0创建活动 1编辑活动
    public int awardFloor;  //评论活动中奖的楼层
    //0，无标签;1，主播标签；2，关注的人；3，超级听友；4，关注的人标签 + 超级听友标签；5，ximi；6，vip；
    //7，关注的人标签 + ximi标签；8，关注的人标签 + vip标签；9，超级听友标签 + ximi标签；10，超级听友标签 + vip标签；
    //11，ximi标签 + vip标签
    public int topTagStatus;
    public CommentVipInfo vipInfo;
    public int likeTagStatus; // 1，展示主播点过赞，0,不展示
    public int awardLevel; // 中奖等级
    public int bumpTimes; // 灌水次数
    public boolean isBumpedComment; // 本评论是否是灌水评论

    public List<UserLevelTag> userTags;
    public String region;

    public CommentModel() {
    }

    /**
     * 注意！！！！新添加的字段请加入到该方法里 Fire the hall
     */
    public CommentModel(CommentModel in) {
        this.ret = in.ret;
        this.msg = in.msg;
        this.id = in.id;
        this.uid = in.uid;
        this.nickname = in.nickname;
        this.smallHeader = in.smallHeader;
        this.content = in.content;
        this.createdAt = in.createdAt;
        this.trackId = in.trackId;
        this.trackUid = in.trackUid;
        this.parentId = in.parentId;
        this.parentUid = in.parentUid;
        this.pNickName = in.pNickName;
        this.ancestorId = in.ancestorId;
        this.startTime = in.startTime;
        this.likes = in.likes;
        this.isVip = in.isVip;
        this.vipExpireTime = in.vipExpireTime;
        this.replies = in.replies;
        this.replyCount = in.replyCount;
        this.shareCount = in.shareCount;
        this.trackTitle = in.trackTitle;
        this.userPost = in.userPost;
        this.isPlaying = in.isPlaying;
        this.voiceUrl = in.voiceUrl;
        this.voiceDuration = in.voiceDuration;
        this.voicePath = in.voicePath;
        this.imageUrls = in.imageUrls;
        this.pictureUrl = in.pictureUrl;
        this.bulletColor = in.bulletColor;
        this.type = in.type;
        this.groupType = in.groupType;
        this.albumTitle = in.albumTitle;
        this.albumCover = in.albumCover;
        this.trackCover = in.trackCover;
        this.albumAuthor = in.albumAuthor;
        this.albumId = in.albumId;
        this.second = in.second;
        this.updatedAt = in.updatedAt;
        this.liked = in.liked;
        this.lookAlled = in.lookAlled;
        this.isFromDubbing = in.isFromDubbing;
        this.business = in.business;
        this.iconRes = in.iconRes;
        this.isTop = in.isTop;
        this.isDeleted = in.isDeleted;
        this.isTrackDetailTop = in.isTrackDetailTop;
        this.commentThemeEntryFlag = in.commentThemeEntryFlag;
        this.awardFloor = in.awardFloor;
        this.topTagStatus = in.topTagStatus;
        this.likeTagStatus = in.likeTagStatus;
        this.awardLevel = in.awardLevel;
        this.bumpTimes = in.bumpTimes;
        this.isBumpedComment = in.isBumpedComment;
        this.userTags = in.userTags;
        this.region = in.region;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof CommentModel) {
            if (this == obj) return true;
            if (this.id == ((CommentModel) obj).id && this.uid == ((CommentModel) obj).uid) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        Object cloned = super.clone();
        return cloned;
    }

    public boolean isSameUser(CommentModel model) {
        if (model == null) return false;
        if (this == model) return true;
        return this.uid == model.uid;
    }


    public String getAlbumTitle() {
        return albumTitle;
    }


    public static CommentListItemBean commentTo2CommentListItemBean(CommentModel commentModel) {
        CommentListItemBean commentListItemBean = new CommentListItemBean();
        commentListItemBean.setCreateTime(commentModel.createdAt);
        commentListItemBean.setLikeCount(commentModel.likes);
        commentListItemBean.setLikeStatus(commentModel.liked);
        commentListItemBean.setCanDelete(commentModel.uid == UserInfoMannage.getUid());
        commentListItemBean.setReplyCount(commentModel.replyCount);
        CommentUserBean commentUserBean = new CommentUserBean();
        commentUserBean.setAvatar(commentModel.smallHeader);
        commentUserBean.setNickname(commentModel.nickname);
        commentUserBean.setVip(commentModel.isVip);
        commentUserBean.setUid(commentModel.uid);
        List<UserLevelTag> userTags = commentModel.userTags;
        int userTagSize = userTags != null ? userTags.size() : 0;
        boolean isUserTagTip = false;
        for (int i = 0; i < userTagSize; i++) {
            UserLevelTag userLevelTag = userTags.get(0);
            if ("vip".equals(userLevelTag.businessName)) {
                isUserTagTip = true;
                break;
            }
        }
        boolean isVipUseTip = false;
        if (commentModel.vipInfo != null && !TextUtils.isEmpty(commentModel.vipInfo.icon)) {
            isVipUseTip = true;
        }
        if (isVipUseTip || isUserTagTip) {
            commentUserBean.setVip(true);
        }
        commentListItemBean.setContent(commentModel.content);
        commentListItemBean.setUser(commentUserBean);
        commentListItemBean.setCommentId(commentModel.id);
        commentListItemBean.setPNickName(commentModel.pNickName);
        commentListItemBean.setParentCommentId(commentModel.parentId);
        commentListItemBean.setParentUid(commentModel.parentUid);
        CommentUserBean parentUser = new CommentUserBean();
        parentUser.setNickname(commentModel.pNickName);
        parentUser.setUid(commentModel.parentUid);
        commentListItemBean.setRegion(commentModel.region);
        commentListItemBean.setParentUser(parentUser);
        ArrayList<CommentListItemBean> commentReplyList = new ArrayList<>();
        int size = commentModel.replies != null ? commentModel.replies.size() : 0;
        for (int i = 0; i < size; i++) {
            CommentModel tempComment = commentModel.replies.get(i);
            commentReplyList.add(CommentModel.commentTo2CommentListItemBean(tempComment));
        }
        commentListItemBean.setReplys(commentReplyList);
        return commentListItemBean;
    }


}
