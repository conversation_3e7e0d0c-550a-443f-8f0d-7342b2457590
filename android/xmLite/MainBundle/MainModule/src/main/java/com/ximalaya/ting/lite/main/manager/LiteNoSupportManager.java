package com.ximalaya.ting.lite.main.manager;

import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.lite.main.history.dialog.LiteNoSupportDialogFragment;

/**
 * Created by qinhuifeng on 2019-07-06
 * <p>
 * 极速版不支持管理类,未安装喜马拉雅完整版，提示下载，安装了的提示打开喜马拉雅完整版，跳转到对应的页面
 *
 * <AUTHOR>
 */
public class LiteNoSupportManager {

    /**
     * 弹出极速版不支持弹框---for HistoryModel历史记录
     * <p>
     * true 弹出了 false 没有弹出
     */
    public static boolean checkAndShowLiteNoSupportDialogForHistory(FragmentManager manager, HistoryModel historyModel) {
        if (historyModel == null || manager == null) {
            return false;
        }
        LiteNoSupportDialogFragment fragment = new LiteNoSupportDialogFragment();
        //当前是广播
        if (historyModel.isRadio) {
            Radio radio = historyModel.getRadio();
            if (radio != null) {
                long dataId = radio.getDataId();
                fragment.setRadioId(dataId);
            }
            fragment.show(manager, "LiteNoSupportDialogFragment");
            return true;
        }
        //当前不需要进行拦截
        return false;
    }

    /**
     * 弹出极速版不支持弹框---for Track
     * <p>
     * 已经安装主app，跳转到主app的播放页面
     */
    public static boolean checkAndShowLiteNoSupportDialogForTrack(FragmentManager manager, int pageSourceFrom, Track track, long albumId) {
        if (track == null || manager == null) {
            return false;
        }
        LiteNoSupportDialogFragment fragment = new LiteNoSupportDialogFragment();
        //当前是vip声音，不拦截
        if (track.isVipFree() || track.getVipFreeType() == 1) {
            return false;
        }
        //判断是否是付费节目
        if (track.isPayTrack()) {
            fragment.setTrackId(track.getDataId());
            fragment.setAlbumId(albumId);
            fragment.setPageSourceFrom(pageSourceFrom);
            fragment.show(manager, "LiteNoSupportDialogFragment");
            return true;
        }
        //当前不需要进行拦截
        return false;
    }

    /**
     * 弹出极速版不支持弹框---for Album
     * <p>
     * 已经安装主app，跳转到主app的专辑页
     */
    public static boolean checkAndShowLiteNoSupportDialogForAlbum(FragmentManager manager, Album album) {
        if (album == null || manager == null) {
            return false;
        }
        if (album instanceof AlbumM) {
            //当前是vip专辑，不拦截
            if (((AlbumM) album).isVipFree() || ((AlbumM) album).getVipFreeType() == 1) {
                return false;
            }
        }
        LiteNoSupportDialogFragment fragment = new LiteNoSupportDialogFragment();
        //判断是否是付费节目
        if (album.isPaid()) {
            fragment.setAlbumId(album.getId());
            fragment.show(manager, "LiteNoSupportDialogFragment");
            return true;
        }
        //当前不需要进行拦截
        return false;
    }

    /**
     * 弹出极速版不支持弹框---for Radio
     * <p>
     * 已经安装主app，跳转到主app的广播播放页面
     */
    public static boolean checkAndShowLiteNoSupportDialogForRadio(FragmentManager manager, Radio radio) {
        if (radio == null || manager == null) {
            return false;
        }
        LiteNoSupportDialogFragment fragment = new LiteNoSupportDialogFragment();
        long dataId = radio.getDataId();
        fragment.setRadioId(dataId);
        fragment.show(manager, "LiteNoSupportDialogFragment");
        return true;
    }
}
