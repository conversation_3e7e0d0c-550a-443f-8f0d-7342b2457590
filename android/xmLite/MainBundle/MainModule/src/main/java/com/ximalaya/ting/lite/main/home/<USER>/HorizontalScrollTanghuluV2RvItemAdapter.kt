package com.ximalaya.ting.lite.main.home.adapter

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder
import com.ximalaya.ting.lite.main.home.HomeTanghuluPresenter
import com.ximalaya.ting.lite.main.home.viewmodel.TanghuluClickViewModel
import com.ximalaya.ting.lite.main.model.album.RecommendDiscoveryM

/**
 * Created by qinhuifeng on 2021/1/11
 *
 * <AUTHOR>
 */
class HorizontalScrollTanghuluV2RvItemAdapter(val baseFragment2: BaseFragment2, dataList: List<RecommendDiscoveryM>) : MultiRecyclerAdapter<RecommendDiscoveryM, SuperRecyclerHolder>(baseFragment2.context, dataList) {

    private var tanghuluPresenter: HomeTanghuluPresenter? = null
    val dp8: Float = BaseUtil.dp2px(baseFragment2.context, 8f).toFloat()
    val itemWith: Int
    val itemHegith: Int
    var textSizePx: Float

    init {

        val screenDensity = BaseUtil.getScreenDensity(baseFragment2.context)
        val screenWidth = BaseUtil.getScreenWidth(baseFragment2.context)
        val screenWidthDp = (1.00f * screenWidth / screenDensity + 0.5).toInt()

        //左边间距12
        var removeMargin = BaseUtil.dp2px(baseFragment2.context, 12f);

        //默认最大字号为19dp
        var maxTextSizePx = BaseUtil.dp2px(baseFragment2.context, 19f)
        var bgViewCount = 3.7f;
        if (screenWidthDp > 360) {
            bgViewCount = 4.5f
            //展示4.5个的时候，最大字号为18sp
            maxTextSizePx = BaseUtil.dp2px(baseFragment2.context, 18f)
        }
        //加上中间间距
        val marginLeftReight = BaseUtil.dp2px(baseFragment2.context, 6f)
        val marginTop = BaseUtil.dp2px(baseFragment2.context, 6f)
        removeMargin += bgViewCount.toInt() * marginLeftReight

        val bgViewWith: Int = (1.0f * (screenWidth - removeMargin) / bgViewCount).toInt();
        //宽高比例为42/82
        val bgViewHeight: Int = (1.0f * bgViewWith * 42.0f / 82.0f).toInt()

        //item的宽度为背景块的宽度+左右间距6fp
        itemWith = bgViewWith + marginLeftReight
        //居上的高度为6dp
        itemHegith = bgViewHeight + marginTop;

        //默认字号，15dp
        textSizePx = BaseUtil.sp2px(baseFragment2.context, 15f).toFloat();
        //适配大字模式，最大字体不能超过18dp
        if (textSizePx > maxTextSizePx) {
            textSizePx = maxTextSizePx.toFloat();
        }
    }

    override fun createMultiViewHolder(mCtx: Context?, itemView: View, viewType: Int): SuperRecyclerHolder {
        val params: ViewGroup.LayoutParams = ViewGroup.LayoutParams(itemWith, itemHegith)
        itemView.layoutParams = params
        return SuperRecyclerHolder.createViewHolder(mCtx, itemView)
    }

    override fun onBindMultiViewHolder(holder: SuperRecyclerHolder?, t: RecommendDiscoveryM?, viewType: Int, position: Int) {
        if (holder == null || t == null) {
            return
        }
        //设置渐变背景
        var titleColor: Int
        var bgColors: IntArray
        try {
            //不判断，抛异常了使用默认色
            val split = t.backColor.split(",")
            bgColors = intArrayOf(Color.parseColor(split[0]), Color.parseColor(split[1]))
            titleColor = Color.parseColor(split[2])
        } catch (e: Exception) {
            bgColors = intArrayOf(Color.parseColor("#EDEFE8"), Color.parseColor("#EDEFE8"))
            titleColor = Color.parseColor("#000000")
        }
        //设置渐变背景
        val itemBg: View = holder.getViewById(R.id.main_view_bg)
        if (itemBg.background is GradientDrawable) {
            val bgGradientDrawable = itemBg.background as GradientDrawable
            (bgGradientDrawable.mutate() as GradientDrawable).colors = bgColors
            bgGradientDrawable.shape = GradientDrawable.RECTANGLE
            bgGradientDrawable.gradientType = GradientDrawable.LINEAR_GRADIENT;
            bgGradientDrawable.cornerRadii = floatArrayOf(dp8, dp8, dp8, dp8, dp8, dp8, dp8, dp8);
            bgGradientDrawable.orientation = GradientDrawable.Orientation.LEFT_RIGHT
            itemBg.background = bgGradientDrawable
        } else {
            val bgGradientDrawable = GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, bgColors)
            bgGradientDrawable.shape = GradientDrawable.RECTANGLE
            bgGradientDrawable.gradientType = GradientDrawable.LINEAR_GRADIENT;
            bgGradientDrawable.cornerRadii = floatArrayOf(dp8, dp8, dp8, dp8, dp8, dp8, dp8, dp8);
            itemBg.background = bgGradientDrawable
        }
        //设置标题
        holder.setTextColor(R.id.main_tv_title, titleColor)
        holder.setText(R.id.main_tv_title, t.title)
        holder.setTextSize(R.id.main_tv_title, TypedValue.COMPLEX_UNIT_PX, textSizePx)

        //设置点击跳转
        holder.setOnItemClickListenner(object : View.OnClickListener {
            override fun onClick(v: View?) {
                val tanghuluModel = TanghuluClickViewModel();
                tanghuluModel.recommendDiscoveryM = t
                if (tanghuluPresenter == null) {
                    tanghuluPresenter = HomeTanghuluPresenter(baseFragment2);
                }
                tanghuluPresenter!!.dealWithTanghuluClick(v, tanghuluModel)
            }
        })
    }

    override fun getMultiItemViewType(t: RecommendDiscoveryM?, position: Int): Int {
        return 0
    }

    override fun getMultiItemLayoutId(viewType: Int): Int {
        return R.layout.main_item_horizontal_scroll_tanghulu_v2_rv_item;
    }
}