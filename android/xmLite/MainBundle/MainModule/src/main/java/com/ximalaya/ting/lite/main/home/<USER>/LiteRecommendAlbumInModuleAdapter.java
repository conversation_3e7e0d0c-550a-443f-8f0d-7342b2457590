package com.ximalaya.ting.lite.main.home.adapter;

import android.content.Context;
import android.graphics.Color;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTrackCookie;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel;
import com.ximalaya.ting.lite.main.model.newhome.LiteFloorModel;

import java.util.List;

/**
 * Created by WolfXu on 2018/5/31.
 * <p>
 * 推荐页专辑列表模块中专辑适配器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class LiteRecommendAlbumInModuleAdapter extends AbRecyclerViewAdapter {

    private static final String TAG = "RecommendAlbumInModuleA";

    private static final int ITEM_TYPE_ALBUM = 1;
    private static final int ITEM_TYPE_MORE_BTN = 2;

    private List<AlbumM> mAlbumMList;
    private View.OnClickListener mOnMoreBtnClickListener;  // 如果更多按钮监听不为空，则添加更多按钮
    private final Context mContext;
    private final BaseFragment2 mFragment;
    private boolean mShowScore;

    private final int itemWidth;

    private String mModuleId;
    private String mModuleName;
    private int mModuleType;
    private int pageId;
    private final boolean mIsRecommendChannel;

    /**
     * @param showScore 是否展示专辑评分
     */
    public LiteRecommendAlbumInModuleAdapter(BaseFragment2 fragment, boolean showScore, HomeRecommendExtraViewModel mExtraModel) {
        mFragment = fragment;
        mContext = BaseApplication.getMyApplicationContext();
        this.mShowScore = showScore;

        int screenWidth = BaseUtil.getScreenWidth(mFragment.getContext());
        //屏幕宽度减去4个12dp的间距然后除以3.5，就是每个item的宽度
        int dp12 = BaseUtil.dp2px(mFragment.getContext(), 12f);
        int allSpaceWidth = 4 * dp12;
        itemWidth = (int) ((screenWidth - allSpaceWidth) / 7f * 2f);
        mIsRecommendChannel = mExtraModel.from == BundleValueConstantsInMain.FROM_NEW_HOME_RECOMMEND;
        pageId = mExtraModel.pageId;
    }

    public void setShowScore(boolean showScore) {
        this.mShowScore = showScore;
    }

    public void setModuleData(String mModuleId, String mModuleName, int moduleType) {
        this.mModuleId = mModuleId;
        this.mModuleName = mModuleName;
        this.mModuleType = moduleType;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        if (viewType == ITEM_TYPE_MORE_BTN) {
            int moreBtnLayoutId = R.layout.main_recommend_more_btn_f3f4f5;
            View view2 = inflater.inflate(moreBtnLayoutId, parent, false);
            return new MoreBtnViewHolder(view2);
        }
        int layoutId = R.layout.main_lite_item_recommend_album_in_module_new;
        View view1 = inflater.inflate(layoutId, parent, false);
        int mTitleMaxLine = 2;
        return new AlbumViewHolder(view1, mTitleMaxLine);
    }

    @Override
    public void onBindViewHolder(@NonNull final RecyclerView.ViewHolder h, int position) {
        Logger.d(TAG, "onBindViewHolder position = " + position);
        if (h instanceof AlbumViewHolder && getItem(position) != null) {
            final AlbumViewHolder holder = (AlbumViewHolder) h;
            final AlbumM albumM = (AlbumM) getItem(position);
            if (albumM == null) {
                return;
            }

            if (mModuleType == LiteFloorModel.MODULE_CONTENT_POOL) {
                if (mIsRecommendChannel) {
                    // 新首页-内容池-专辑item  控件曝光
                    new XMTraceApi.Trace()
                            .setMetaId(29659)
                            .setServiceId("slipPage")
                            .put("title", mModuleName)
                            .put("currPageId", String.valueOf(pageId))
                            .put("title", String.valueOf(pageId))
                            .put("albumId", String.valueOf(albumM.getId()))
                            .put("moduleId", mModuleId)
                            .put("currPage", "homePageV2")
                            .createTrace();
                } else {
                    // 新首页-内容池-专辑item  控件曝光
                    new XMTraceApi.Trace()
                            .setMetaId(29760)
                            .setServiceId("slipPage")
                            .put("title", mModuleName)
                            .put("currPageId", String.valueOf(pageId))
                            .put("albumId", String.valueOf(albumM.getId()))
                            .put("moduleId", mModuleId)
                            .put("currPage", "homePageV2")
                            .createTrace();
                }
            } else if (mModuleType == LiteFloorModel.MODULE_KEYWORD_CARD) {
                if (mIsRecommendChannel) {
                    // 新首页-热词-专辑item  控件曝光
                    new XMTraceApi.Trace()
                            .setMetaId(29657)
                            .setServiceId("slipPage")
                            .put("title", mModuleName)
                            .put("currPageId", String.valueOf(pageId))
                            .put("albumId", String.valueOf(albumM.getId()))
                            .put("moduleId", mModuleId)
                            .put("currPage", "homePageV2")
                            .createTrace();
                } else {
                    // 新首页-热词-专辑item  控件曝光
                    new XMTraceApi.Trace()
                            .setMetaId(29763)
                            .setServiceId("slipPage")
                            .put("title", mModuleName)
                            .put("currPageId", String.valueOf(pageId))
                            .put("albumId", String.valueOf(albumM.getId()))
                            .put("moduleId", mModuleId)
                            .put("currPage", "homePageV2")
                            .createTrace();
                }
            }


            LinearLayout.LayoutParams topContainerLayoutParams =
                    (LinearLayout.LayoutParams) holder.flTopContainer.getLayoutParams();

            if (topContainerLayoutParams.width != itemWidth || topContainerLayoutParams.height != itemWidth) {
                topContainerLayoutParams.width = itemWidth;
                topContainerLayoutParams.height = itemWidth;
            }

            LinearLayout.LayoutParams containerLayoutParams =
                    (LinearLayout.LayoutParams) holder.llContainer.getLayoutParams();

            if (containerLayoutParams.width != itemWidth) {
                containerLayoutParams.width = itemWidth;
            }
            Logger.i(TAG, "containerLayoutParams start margin = " + containerLayoutParams.getMarginStart());

            String contentDescription = "";

            bindTitle(holder, albumM);
            contentDescription += albumM.getAlbumTitle();

            double score = albumM.getScore();

            if (mShowScore && score > 6.0) {
                holder.tvPlayCount.setVisibility(View.GONE);
                holder.tvAlbumScore.setVisibility(View.VISIBLE);
                holder.tvAlbumScore.setText(String.valueOf(score));
            } else {
                holder.tvPlayCount.setVisibility(View.VISIBLE);
                holder.tvAlbumScore.setVisibility(View.GONE);
                holder.tvPlayCount.setText(StringUtil.getFriendlyNumStr(albumM.getPlayCount()));
            }

            contentDescription += "，" + StringUtil.getFriendlyNumStr(albumM.getPlayCount());
            holder.ivCover.setBackgroundColor(Color.TRANSPARENT);
            holder.ivCover.setPadding(0, 0, 0, 0);

            if (mContext != null) {
                ImageManager.from(mContext).displayImage(holder.ivCover, getVaildCover(albumM),
                        R.drawable.host_default_album, R.drawable.host_default_album);
            }

            @DrawableRes int coverTagResId = AlbumTagUtil.getAlbumCoverTag(albumM);
            if (coverTagResId != -1) {
                holder.ivAlbumCoverTag.setImageResource(coverTagResId);
                holder.ivAlbumCoverTag.setVisibility(View.VISIBLE);
            } else {
                holder.ivAlbumCoverTag.setVisibility(View.INVISIBLE);
            }

            View.OnClickListener itemClickListener = v -> {
                if (OneClickHelper.getInstance().onClick(v)) {
                    UserTrackCookie.getInstance()
                            .setXmContent("interestCard", "homepage", "album", null);
                    handleItemClick(albumM, holder);
                }
            };

            holder.ivCover.setOnClickListener(itemClickListener);

            if (albumM.getAdInfo() != null) {
                holder.ivAdTag.setVisibility(View.VISIBLE);
                ImageManager.from(mContext).displayImage(holder.ivAdTag, albumM.getAdInfo().getAdMark(),
                        R.drawable.main_ad_tag_album_list);
            } else {
                holder.ivAdTag.setVisibility(View.GONE);
            }

            if (holder.vActivity123Image != null) {
                if (TextUtils.isEmpty(albumM.getActivityTag())) {
                    holder.vActivity123Image.setVisibility(View.GONE);
                } else {
                    holder.vActivity123Image.setImageDrawable(null);
                    holder.vActivity123Image.setVisibility(View.VISIBLE);
                    ImageManager.from(mContext).displayImage(holder.vActivity123Image, albumM.getActivityTag(), -1);
                }
            }
            holder.itemView.setContentDescription(contentDescription);
        } else if (h instanceof MoreBtnViewHolder) {
            RecyclerView.LayoutParams layoutParams = ((RecyclerView.LayoutParams) h.itemView.getLayoutParams());
            layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT;
            layoutParams.width = BaseUtil.dp2px(mFragment.getContext(), 96f);
            h.itemView.setLayoutParams(layoutParams);
            h.itemView.setOnClickListener(mOnMoreBtnClickListener);
        }
    }

    private String getVaildCover(AlbumM albumM) {
        String coverUrl = albumM.getValidCover();
        if (TextUtils.isEmpty(coverUrl)) {
            coverUrl = albumM.getAlbumCoverUrl290();
        }
        return coverUrl;
    }

    private void handleItemClick(AlbumM albumM, AlbumViewHolder holder) {
        // 如果是主播广告，走广告那边的处理
        if (AdManager.checkAnchorAdCanClick(albumM.getAdInfo())) {
            AdManager.handlerAdClickToXmADSDK(mContext, albumM.getAdInfo(), albumM.getAdInfo()
                    .createAdReportModel(AppConstants.AD_LOG_TYPE_SITE_CLICK, holder.getAdapterPosition()).build());
        } else {
            if (TextUtils.isEmpty(albumM.getMaterialType()) || "album".equals(albumM.getMaterialType())) {
                AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_OTHER
                        , ConstantsOpenSdk.PLAY_FROM_OTHER, albumM.getRecSrc(), albumM
                                .getRecTrack()
                        , -1, BaseApplication.getOptActivity());

                if (mModuleType == LiteFloorModel.MODULE_CONTENT_POOL) {
                    if (mIsRecommendChannel) {
                        // 新首页-内容池-专辑item  点击事件
                        new XMTraceApi.Trace()
                                .click(29658) // 用户点击时上报
                                .put("title", mModuleName)
                                .put("currPageId", String.valueOf(pageId))
                                .put("albumId", String.valueOf(albumM.getId()))
                                .put("moduleId", mModuleId)
                                .put("currPage", "homePageV2")
                                .createTrace();
                    } else {
                        // 新首页-内容池-专辑item  点击事件
                        new XMTraceApi.Trace()
                                .click(29759) // 用户点击时上报
                                .put("title", mModuleName)
                                .put("currPageId", String.valueOf(pageId))
                                .put("albumId", String.valueOf(albumM.getId()))
                                .put("moduleId", mModuleId)
                                .put("currPage", "homePageV2")
                                .createTrace();
                    }
                } else if (mModuleType == LiteFloorModel.MODULE_KEYWORD_CARD) {
                    if (mIsRecommendChannel) {
                        // 新首页-内容池-专辑item  点击事件
                        new XMTraceApi.Trace()
                                .click(29656) // 用户点击时上报
                                .put("title", mModuleName)
                                .put("currPageId", String.valueOf(pageId))
                                .put("albumId", String.valueOf(albumM.getId()))
                                .put("moduleId", mModuleId)
                                .put("currPage", "homePageV2")
                                .createTrace();
                    } else {
                        // 新首页-内容池-专辑item  点击事件
                        new XMTraceApi.Trace()
                                .click(29762) // 用户点击时上报
                                .put("title", mModuleName)
                                .put("currPageId", String.valueOf(pageId))
                                .put("albumId", String.valueOf(albumM.getId()))
                                .put("moduleId", mModuleId)
                                .put("currPage", "homePageV2")
                                .createTrace();
                    }
                }
            }
        }
    }

    private void bindTitle(AlbumViewHolder holder, AlbumM albumM) {
        Spanned titleWithTag = null;
        int textSize = (int) holder.tvTitle.getTextSize();
        // 底下带色块的样式，且是精品模块，会显示深色，需要把图标变成白色
        if (albumM.getIsFinished() == 2) {
            titleWithTag = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(mContext,
                    " " + albumM.getAlbumTitle(), R.drawable.main_ic_end_tag_brown, textSize);
        }
        if (titleWithTag != null) {
            holder.tvTitle.setText(titleWithTag);
        } else {
            holder.tvTitle.setText(albumM.getAlbumTitle());
        }
        holder.tvTitle.setTextSize(13f);
    }

    @Override
    public int getItemCount() {
        int itemCount = 0;
        if (mAlbumMList != null) {
            itemCount += mAlbumMList.size();
        }
        if (mOnMoreBtnClickListener != null) {
            itemCount++;
        }
        return itemCount;
    }

    @Override
    public Object getItem(int position) {
        if (mAlbumMList != null && position >= 0 && position < mAlbumMList.size()) {
            return mAlbumMList.get(position);
        }
        return null;
    }

    @Override
    public int getItemViewType(int position) {
        if (mAlbumMList != null && position < mAlbumMList.size()) {
            return ITEM_TYPE_ALBUM;
        } else {
            return ITEM_TYPE_MORE_BTN;
        }
    }

    public void setAlbumMList(List<AlbumM> albumMList) {
        mAlbumMList = albumMList;
    }

    public void setOnMoreBtnClickListener(View.OnClickListener onMoreBtnClickListener) {
        mOnMoreBtnClickListener = onMoreBtnClickListener;
    }

    private static class MoreBtnViewHolder extends RecyclerView.ViewHolder {

        MoreBtnViewHolder(View itemView) {
            super(itemView);
        }
    }

    protected static class AlbumViewHolder extends RecyclerView.ViewHolder {

        private final ImageView ivCover;
        private final TextView tvTitle;
        private final TextView tvPlayCount;
        private final ImageView ivAlbumCoverTag;
        private final ImageView ivAdTag;
        private final ImageView vActivity123Image;

        private final TextView tvAlbumScore;
        private final LinearLayout llContainer;

        private final FrameLayout flTopContainer;

        AlbumViewHolder(View itemView, int titleMaxLines) {
            super(itemView);
            ivCover = itemView.findViewById(R.id.main_iv_cover);
            tvTitle = itemView.findViewById(R.id.main_tv_title);
            tvPlayCount = itemView.findViewById(R.id.main_tv_play_count);
            ivAlbumCoverTag = itemView.findViewById(R.id.main_iv_album_cover_tag);
            ivAdTag = itemView.findViewById(R.id.main_iv_recommend_ad_tag);
            vActivity123Image = itemView.findViewById(R.id.main_iv_activity_tag);
            tvAlbumScore = itemView.findViewById(R.id.main_tv_album_score);

            llContainer = itemView.findViewById(R.id.main_ll_container);

            flTopContainer = itemView.findViewById(R.id.main_fl_top_container);

            if (titleMaxLines > 0) {
                tvTitle.setMaxLines(titleMaxLines);
            }
        }
    }
}
