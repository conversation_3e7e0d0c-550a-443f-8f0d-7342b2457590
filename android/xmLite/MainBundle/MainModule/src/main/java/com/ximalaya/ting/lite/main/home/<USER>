package com.ximalaya.ting.lite.main.home;


import android.app.Activity;


import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.util.VersionUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.base.IXmDataChangedCallback;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendAdapter;
import com.ximalaya.ting.lite.main.home.fragment.HomeRecommendFragment;
import com.ximalaya.ting.lite.main.home.manager.HomeTingUpdateMaskManager;
import com.ximalaya.ting.lite.main.home.presenter.HomeRecommendContact;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeItemSubscriptionViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeSubscribeHistoryViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeSubscribePageViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qinhuifeng on 2020/11/23
 * 听更新+历史模块数据管理
 * <p>
 * 需要从本地加载相关数据
 *
 * <AUTHOR>
 */
public class HomeSubscribeFloorPresenter implements AlbumEventManage.CollectListener, IXmDataChangedCallback {

    //订阅数小于等于3需要展示推荐
    private int showRecommendMinSubscribeCount = 3;
    //听更新声音列表
    private HomeSubscribeHistoryViewModel mCacheSubscribeHistoryViewModel;
    public boolean isRequestAlling = false;
    //是否正在请求推荐的下一页数据
    public boolean isRequestRecommendNextPage = false;
    private HomeRecommendAdapter mAdapter;
    private Activity mActivity;
    private HomeRecommendContact.IFragmentView fragmentView;


    public HomeSubscribeFloorPresenter() {

    }

    public HomeSubscribeFloorPresenter(HomeRecommendContact.IFragmentView fragmentView, HomeRecommendAdapter mAdapter) {
        this.mAdapter = mAdapter;
        this.mActivity = fragmentView.getActivity();
        this.fragmentView = fragmentView;

        //注意需要处理反注册，不处理会造成内存泄露
        //注册专辑取消订阅监听
        AlbumEventManage.addListener(this);

        //注意需要处理反注册，不处理会造成内存泄露
        //注册播放历史发生变化的监听
        IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
        if (historyManager != null) {
            historyManager.registerOnHistoryUpdateListener(this);
        }
    }

    public void requestDataAll(int hasSubscribeCount) {
        FuliLogger.log("订阅历史模块:presenter:requestDataAll");
        if (isRequestAlling) {
            return;
        }
        isRequestAlling = true;
        HomeSubscribeRequest.OnSubscribeHistoryFinishCallBack subscribeHistoryFinishCallBack = new HomeSubscribeRequest.OnSubscribeHistoryFinishCallBack() {
            @Override
            public void onSubscribeHistoryFinish(HomeSubscribeHistoryViewModel model) {
                isRequestAlling = false;
                //更新一次数据数据

                //每次下拉刷新后，清除推荐数据，推荐会加载更多
                if (mCacheSubscribeHistoryViewModel != null && mCacheSubscribeHistoryViewModel.subscribePageViewModel != null) {
                    mCacheSubscribeHistoryViewModel.subscribePageViewModel.recommendItemModelList = new ArrayList<>();
                }

                updateAdapterAndData(model);

                //检测展示tips
                //订阅数大于0或者是新版本升级上来的需要检测是否需要弹出tips
                if (hasSubscribeCount > 0 || VersionUtil.isNewVersion()) {
                    HomeTingUpdateMaskManager.setNeedShowTingUpdateMask();
                    HomeTingUpdateMaskManager.checkTingUpdataAndShow(mActivity, mAdapter, (HomeRecommendFragment) fragmentView.getBaseFragment2(), 250);
                }
            }
        };
        HomeSubscribeRequest homeSubscribeRequest = new HomeSubscribeRequest();
        //没有登录，只请求推荐专辑
        if (!UserInfoMannage.hasLogined() || hasSubscribeCount == 0) {
            homeSubscribeRequest
                    .addRequestRecommend()
                    .addRequestPlayHistory()
                    .startRequestAll(subscribeHistoryFinishCallBack);
            return;
        }
        //已登录，订阅数小于3，需要请求订阅+推荐
        if (hasSubscribeCount <= showRecommendMinSubscribeCount) {
            homeSubscribeRequest
                    .addRequestSubscribe()
                    .addRequestRecommend()
                    .addRequestPlayHistory()
                    .startRequestAll(subscribeHistoryFinishCallBack);
        } else {
            //大于3，只请求订阅
            homeSubscribeRequest
                    .addRequestSubscribe()
                    .addRequestPlayHistory()
                    .startRequestAll(subscribeHistoryFinishCallBack);
        }
    }

    private void updateAdapterAndData(HomeSubscribeHistoryViewModel model) {
        //先填充和保存数据
        if (mCacheSubscribeHistoryViewModel == null) {
            mCacheSubscribeHistoryViewModel = new HomeSubscribeHistoryViewModel();
        }
        mCacheSubscribeHistoryViewModel.fillViewModel(model);

        //更新界面中的数据
        if (mAdapter == null) {
            return;
        }
        //数据获取成功，更新一次数据
        List<ItemModel> listData = mAdapter.getListData();
        if (listData == null || listData.size() == 0) {
            return;
        }
        for (int i = 0; i < listData.size(); i++) {
            ItemModel itemModel = listData.get(i);
            if (itemModel == null) {
                continue;
            }
            if (itemModel.getViewType() != HomeRecommendAdapter.VIEW_TYPE_MY_SUBSCRIPTION) {
                continue;
            }
            //是订阅历史楼层
            Object itemObject = itemModel.getObject();
            if (!(itemObject instanceof HomeSubscribeHistoryViewModel)) {
                continue;
            }
            HomeSubscribeHistoryViewModel homeSubscribeHistoryViewModel = (HomeSubscribeHistoryViewModel) itemObject;
            if (homeSubscribeHistoryViewModel.subscribePageViewModel == null) {
                homeSubscribeHistoryViewModel.subscribePageViewModel = new HomeSubscribePageViewModel();
            }
            homeSubscribeHistoryViewModel.fillViewModel(getCacheSubscribeHistoryViewModel());
            homeSubscribeHistoryViewModel.initRequestFinish = true;
            //更新数据
            mAdapter.notifyDataSetChanged();
        }
    }

    public HomeSubscribeHistoryViewModel getCacheSubscribeHistoryViewModel() {
        //缓存的数据都是从接口获取的
        if (mCacheSubscribeHistoryViewModel != null) {
            mCacheSubscribeHistoryViewModel.initRequestFinish = true;
        }
        //退出登录后，清除订阅数据
        if (mCacheSubscribeHistoryViewModel != null && mCacheSubscribeHistoryViewModel.subscribePageViewModel != null) {
            //未登录需要清除登录数据
            if (!UserInfoMannage.hasLogined()) {
                mCacheSubscribeHistoryViewModel.subscribePageViewModel.updateSubscribeCount(0);
                mCacheSubscribeHistoryViewModel.subscribePageViewModel.subscriptionItemModelList = new ArrayList<>();
            } else {
                //已登录，如果订阅数大于3，需要清除推荐数据，放在多次退出登录再登录展示了推荐数据
                if (mCacheSubscribeHistoryViewModel.subscribePageViewModel.subscriptionItemModelList != null && mCacheSubscribeHistoryViewModel.subscribePageViewModel.getSubscribeCount() > showRecommendMinSubscribeCount) {
                    mCacheSubscribeHistoryViewModel.subscribePageViewModel.recommendItemModelList = new ArrayList<>();
                }
            }
        }
        return mCacheSubscribeHistoryViewModel;
    }


    @Override
    public void onCollectChanged(boolean collect, long id) {
        //订阅发生改变了
        //如果正在请求全部的接口，不需要重新请求单独接口
        if (isRequestAlling) {
            return;
        }

        int subscribeCount = 0;
        HomeSubscribeHistoryViewModel cacheSubscribeHistoryViewModel = getCacheSubscribeHistoryViewModel();
        if (cacheSubscribeHistoryViewModel != null && cacheSubscribeHistoryViewModel.subscribePageViewModel != null) {
            subscribeCount = cacheSubscribeHistoryViewModel.subscribePageViewModel.getSubscribeCount();
        }
        if (collect) {
            subscribeCount++;
        } else {
            subscribeCount--;
        }
        //刚好是3的时候重新请求所有数据，相当于刷新操作
        if (subscribeCount == showRecommendMinSubscribeCount) {
            requestDataAll(subscribeCount);
            return;
        }
        FuliLogger.log("订阅历史模块:订阅发生改变了:collect=" + collect + "  专辑id=" + id);
        HomeSubscribeRequest homeSubscribeRequest = new HomeSubscribeRequest();
        //不需要请求推荐接口，只需要请求订阅以及移除推荐即可
        //检测是否需要移除推荐中的专辑
        //重新请求订阅数据
        //取消移除本地推荐的操作
        homeSubscribeRequest
                .addRequestSubscribe()
                .startRequestAll(new HomeSubscribeRequest.OnSubscribeHistoryFinishCallBack() {
                    @Override
                    public void onSubscribeHistoryFinish(HomeSubscribeHistoryViewModel model) {
                        //更新一次数据数据
                        updateAdapterAndData(model);

                        //解决用户订阅从0变成1个的时候，需要检测是否展示订阅tips
                        if (model != null && model.subscribePageViewModel != null) {
                            //检测展示tips
                            //订阅数大于0或者是新版本升级上来的需要检测是否需要弹出tips
                            if (model.subscribePageViewModel.getSubscribeCount() > 0 || VersionUtil.isNewVersion()) {
                                HomeTingUpdateMaskManager.setNeedShowTingUpdateMask();
                                HomeTingUpdateMaskManager.checkTingUpdataAndShow(mActivity, mAdapter, (HomeRecommendFragment) fragmentView.getBaseFragment2(), 250);
                            }
                        }
                    }
                });
    }

    /**
     * 请求下一页加载更多
     */
    public void loadNextPageRecommend() {
        //如果正在请求全部，不在处理加载更多
        if (isRequestAlling) {
            return;
        }
        //正在请求下一页数据，可能会被多次触发，return
        if (isRequestRecommendNextPage) {
            return;
        }
        HomeSubscribeHistoryViewModel cacheSubscribeHistoryViewModel = getCacheSubscribeHistoryViewModel();
        if (cacheSubscribeHistoryViewModel == null) {
            return;
        }
        if (cacheSubscribeHistoryViewModel.subscribePageViewModel == null) {
            return;
        }
        //当前如果没有展示推荐数，不发起下一页请求
        List<HomeItemSubscriptionViewModel> recommendItemModelList = cacheSubscribeHistoryViewModel.subscribePageViewModel.recommendItemModelList;
        if (recommendItemModelList == null || recommendItemModelList.size() == 0) {
            return;
        }
        isRequestRecommendNextPage = true;
        //请求推荐下一页数据
        HomeSubscribeRequest homeSubscribeRequest = new HomeSubscribeRequest();
        homeSubscribeRequest
                .addRequestRecommend()
                .startRequestAll(new HomeSubscribeRequest.OnSubscribeHistoryFinishCallBack() {
                    @Override
                    public void onSubscribeHistoryFinish(HomeSubscribeHistoryViewModel model) {
                        isRequestRecommendNextPage = false;
                        if (model == null || model.subscribePageViewModel == null) {
                            return;
                        }
                        //设置来源为加载下一页
                        model.subscribePageViewModel.isRecommendLoadMore = true;
                        //更新一次数据数据
                        updateAdapterAndData(model);
                    }
                });
    }

    @Override
    public void onDataChanged() {
        FuliLogger.log("订阅历史模块:播放历史发生了变化，需要更新界面");
        //如果正在请求全部的接口，不需要重新请求单独接口
        if (isRequestAlling) {
            return;
        }
        HomeSubscribeRequest homeSubscribeRequest = new HomeSubscribeRequest();
        homeSubscribeRequest
                .addRequestPlayHistory()
                .startRequestAll(new HomeSubscribeRequest.OnSubscribeHistoryFinishCallBack() {
                    @Override
                    public void onSubscribeHistoryFinish(HomeSubscribeHistoryViewModel model) {
                        //更新一次数据数据
                        updateAdapterAndData(model);
                    }
                });
    }

    public void onDestroy() {
        //移除专辑收藏监听
        AlbumEventManage.removeListener(this);

        //移除播放历史发生变化监听
        IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
        if (historyManager != null) {
            historyManager.unRegisterOnHistoryUpdateListener(this);
        }
    }
}
