package com.ximalaya.ting.lite.main.home;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.util.ThreadUtil;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeHistoryPageViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeItemPlayHistoryViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeItemSubscriptionViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeSubscribeHistoryViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeSubscribePageViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.SubscribeFirstAlbumPageModel;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by qinhuifeng on 2021/1/18
 *
 * <AUTHOR>
 */
public class HomeSubscribeRequest {
    private static ExecutorService mExecutorService = ThreadUtil.newFixedThreadPoolExecutor(5, "xmLite_home_subscribe_history_request");
    private CountDownLatch mCountDownLatch;
    private HomeSubscribeHistoryViewModel callBackModel;
    private List<Runnable> runnableList = new ArrayList<>();

    public HomeSubscribeRequest() {
        callBackModel = new HomeSubscribeHistoryViewModel();
        //默认重置为空
        callBackModel.subscribePageViewModel = null;
        callBackModel.historyPageViewModel = null;
    }


    public HomeSubscribeRequest addRequestSubscribe() {
        //放在线程外初始化，防止并发初始化
        if (callBackModel.subscribePageViewModel == null) {
            callBackModel.subscribePageViewModel = new HomeSubscribePageViewModel();
        }
        Runnable subscribeRunnable = new Runnable() {
            @Override
            public void run() {
                FuliLogger.log("订阅历史模块:HomeSubscribeRequest:订阅请求--start");
                if (!UserInfoMannage.hasLogined()) {
                    //未登录，清空订阅数据
                    callBackModel.subscribePageViewModel.updateSubscribeCount(0);
                    callBackModel.subscribePageViewModel.subscriptionItemModelList = new ArrayList<>();

                    //未登录，展示空
                    if (mCountDownLatch != null) {
                        mCountDownLatch.countDown();
                    }
                    return;
                }
                LiteCommonRequest.getMySubscribeByUpdateFirstAlbumPage(new IDataCallBack<SubscribeFirstAlbumPageModel>() {
                    @Override
                    public void onSuccess(@Nullable SubscribeFirstAlbumPageModel object) {
                        FuliLogger.log("订阅历史模块:HomeSubscribeRequest:订阅请求--success");
                        List<HomeItemSubscriptionViewModel> list = new ArrayList<>();
                        if (object != null && object.albumList != null) {
                            for (int i = 0; i < object.albumList.size(); i++) {
                                Album album = object.albumList.get(i);
                                HomeItemSubscriptionViewModel viewModel = new HomeItemSubscriptionViewModel();
                                viewModel.album = album;
                                viewModel.viewType = HomeItemSubscriptionViewModel.ITEM_SUBSCRIBE_ALBUM;
                                list.add(viewModel);
                            }
                        }
                        //更新订阅数量
                        if (object != null) {
                            callBackModel.subscribePageViewModel.updateSubscribeCount(object.hasSubscribeCount);
                        } else {
                            callBackModel.subscribePageViewModel.updateSubscribeCount(0);
                        }
                        //没请求到数据
                        callBackModel.subscribePageViewModel.subscriptionItemModelList = list;
                        //执行完成
                        if (mCountDownLatch != null) {
                            mCountDownLatch.countDown();
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        FuliLogger.log("订阅历史模块:HomeSubscribeRequest:订阅请求--error");
                        if (mCountDownLatch != null) {
                            mCountDownLatch.countDown();
                        }
                    }
                });
            }
        };

        FuliLogger.log("订阅历史模块:HomeSubscribeRequest:订阅请求--add");
        runnableList.add(subscribeRunnable);
        return this;
    }

    public HomeSubscribeRequest addRequestRecommend() {
        //放在线程外初始化，防止并发初始化
        if (callBackModel.subscribePageViewModel == null) {
            callBackModel.subscribePageViewModel = new HomeSubscribePageViewModel();
        }
        Runnable recommendRunnable = new Runnable() {
            @Override
            public void run() {
                FuliLogger.log("订阅历史模块:HomeSubscribeRequest:推荐请求--start");
                Map<String, String> params = new HashMap<>();
                params.put(HttpParamsConstants.PARAM_CATEGORY_ID, "-1");
                params.put("channelId", "0");
                params.put("vipShow", "1");
                //来源为听节目/听书中的推荐，会过滤声音
                params.put("from", "1");
                LiteCommonRequest.getCategoryRecommendOnlyAlbum(params, new IDataCallBack<List<AlbumM>>() {
                    @Override
                    public void onSuccess(@Nullable List<AlbumM> object) {
                        FuliLogger.log("订阅历史模块:HomeSubscribeRequest:推荐请求--success");
                        if (object != null) {
                            List<HomeItemSubscriptionViewModel> list = new ArrayList<>();
                            for (int i = 0; i < object.size(); i++) {
                                Album album = object.get(i);
                                HomeItemSubscriptionViewModel viewModel = new HomeItemSubscriptionViewModel();
                                viewModel.album = album;
                                viewModel.viewType = HomeItemSubscriptionViewModel.ITEM_RECOMMEND_ALBUM;
                                list.add(viewModel);
                            }
                            //推荐接口返回空的话，不在处理
                            if (list.size() > 0) {
                                callBackModel.subscribePageViewModel.recommendItemModelList = list;
                            }
                        }
                        //执行完成
                        if (mCountDownLatch != null) {
                            mCountDownLatch.countDown();
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        FuliLogger.log("订阅历史模块:HomeSubscribeRequest:推荐请求--error");
                        //执行完成
                        if (mCountDownLatch != null) {
                            mCountDownLatch.countDown();
                        }
                    }
                });

            }
        };
        FuliLogger.log("订阅历史模块:HomeSubscribeRequest:推荐请求--add");
        runnableList.add(recommendRunnable);
        return this;
    }


    /**
     * 检测是否需要移除推荐中的item
     */
    public HomeSubscribeRequest addCheckNeedRemoveRecommendItem(HomeSubscribeHistoryViewModel oldModel, long albumId) {
        if (oldModel == null || oldModel.subscribePageViewModel == null || albumId <= 0) {
            return this;
        }
        if (oldModel.subscribePageViewModel.recommendItemModelList == null || oldModel.subscribePageViewModel.recommendItemModelList.size() == 0) {
            return this;
        }
        List<HomeItemSubscriptionViewModel> list = new ArrayList<>(oldModel.subscribePageViewModel.recommendItemModelList);
        //放在线程外初始化，防止并发初始化
        if (callBackModel.subscribePageViewModel == null) {
            callBackModel.subscribePageViewModel = new HomeSubscribePageViewModel();
        }
        Runnable checkRemoveRunnable = new Runnable() {
            @Override
            public void run() {

                FuliLogger.log("订阅历史模块:HomeSubscribeRequest:检查是否需要移除推荐");

                int needRemoveIndex = -1;
                for (int i = 0; i < list.size(); i++) {
                    HomeItemSubscriptionViewModel viewModel = list.get(i);
                    if (viewModel == null || viewModel.album == null) {
                        continue;
                    }
                    if (viewModel.album.getId() == albumId) {
                        needRemoveIndex = i;
                        break;
                    }
                }
                if (needRemoveIndex >= 0) {
                    list.remove(needRemoveIndex);
                }
                callBackModel.subscribePageViewModel.recommendItemModelList = list;

                //执行完成
                if (mCountDownLatch != null) {
                    mCountDownLatch.countDown();
                }
            }
        };
        runnableList.add(checkRemoveRunnable);
        return this;
    }

    public HomeSubscribeRequest addRequestPlayHistory() {
        FuliLogger.log("订阅历史模块:HomeSubscribeRequest:播放历史--start");
        Runnable historyRunnable = new Runnable() {
            @Override
            public void run() {
                FuliLogger.log("订阅历史模块:HomeSubscribeRequest:播放历史--success");

                List<HistoryModel> historyModels = new ArrayList<>();
                IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
                if (historyManager != null) {
                    historyModels = historyManager.getTrackList();
                }
                List<HomeItemPlayHistoryViewModel> historyViewModelList = new ArrayList<>();
                if (historyModels != null && historyModels.size() > 0) {
                    for (int i = 0; i < historyModels.size(); i++) {
                        HistoryModel historyModel = historyModels.get(i);
                        if (historyModel == null) {
                            continue;
                        }
                        if (historyModel.isRadio) {
                            continue;
                        }
                        HomeItemPlayHistoryViewModel viewModel = new HomeItemPlayHistoryViewModel();
                        viewModel.fillViewTypeForPlayHistoryItem();
                        viewModel.historyViewModel = historyModel;
                        historyViewModelList.add(viewModel);

                        //最多取20个，业务展示的时候临界点位等于20
                        if (historyViewModelList.size() >= 20) {
                            break;
                        }
                    }
                }
                if (callBackModel.historyPageViewModel == null) {
                    callBackModel.historyPageViewModel = new HomeHistoryPageViewModel();
                }
                callBackModel.historyPageViewModel.historyViewModelList = historyViewModelList;
                //执行完成
                if (mCountDownLatch != null) {
                    mCountDownLatch.countDown();
                }
            }
        };
        FuliLogger.log("订阅历史模块:HomeSubscribeRequest:播放历史--add");
        runnableList.add(historyRunnable);
        return this;
    }

    public void startRequestAll(OnSubscribeHistoryFinishCallBack callBack) {
        if (runnableList.size() == 0) {
            callBack.onSubscribeHistoryFinish(null);
            return;
        }
        mCountDownLatch = new CountDownLatch(runnableList.size());
        Runnable checkFinishCallBack = new Runnable() {
            @Override
            public void run() {
                FuliLogger.log("订阅历史模块:HomeSubscribeRequest:checkFinish--await");
                try {
                    //等等并发请求操作完成,限制最大等待时间为5秒，超时时间为5秒
                    mCountDownLatch.await(5, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                FuliLogger.log("订阅历史模块:HomeSubscribeRequest:全部完成或者5秒超时，进行回调=" + Thread.currentThread().getName());
                if (callBack != null) {
                    HandlerManager.postOnMainAuto(new Runnable() {
                        @Override
                        public void run() {
                            callBack.onSubscribeHistoryFinish(callBackModel);
                        }
                    });
                }
            }
        };
        //checkFinishCallBack内部使用了await会阻塞当前线程，创建新线程，不使用线程池
        //启动完成监控
        Thread thread = new Thread(checkFinishCallBack, "xmLite_home_subscribe_history_check_finish");
        thread.start();

        FuliLogger.log("订阅历史模块:HomeSubscribeRequest:allRequest--start--请求个数--" + runnableList.size());
        //使用线程池并发执行请求操作
        for (int i = 0; i < runnableList.size(); i++) {
            Runnable runnable = runnableList.get(i);
            mExecutorService.execute(runnable);
        }
    }

    public interface OnSubscribeHistoryFinishCallBack {
        void onSubscribeHistoryFinish(HomeSubscribeHistoryViewModel model);
    }
}
