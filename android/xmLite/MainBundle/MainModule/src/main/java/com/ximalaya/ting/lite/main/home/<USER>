package com.ximalaya.ting.lite.main.home;


import android.app.Activity;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendAdapter;
import com.ximalaya.ting.lite.main.home.presenter.HomeRecommendContact;
import com.ximalaya.ting.lite.main.model.album.HomeTingUpdateModel;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.android.host.model.track.EverydayUpdateResp;
import com.ximalaya.ting.android.host.model.track.EverydayUpdateTrack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by qinhuifeng on 2020/11/23
 *
 * <AUTHOR>
 */
public class HomeTingUpdatePresenter {

    //听更新声音列表
    private List<EverydayUpdateTrack> everydayUpdateTracks;
    public boolean isRequesting = false;
    private HomeRecommendAdapter mAdapter;
    private Activity mActivity;
    private HomeRecommendContact.IFragmentView fragmentView;

    public HomeTingUpdatePresenter(HomeRecommendContact.IFragmentView fragmentView, HomeRecommendAdapter mAdapter) {
        this.mAdapter = mAdapter;
        this.mActivity = fragmentView.getActivity();
        this.fragmentView = fragmentView;
    }

    public void requestEveryDayUpdateTrack() {
        if (mAdapter == null) {
            return;
        }
        //未登录，不发起请求
        if (!UserInfoMannage.hasLogined()) {
            return;
        }
        if (isRequesting) {
            return;
        }
        isRequesting = true;
        long timeline = -1;
        int sign = 2;//请求第一页
        int pageSize = 30;
        CommonRequestM.getEverydayUpdate(timeline, sign, pageSize, new IDataCallBack<EverydayUpdateResp>() {
            @Override
            public void onSuccess(@Nullable EverydayUpdateResp object) {
                if (object == null) {
                    isRequesting = false;
                    return;
                }
                final List<EverydayUpdateTrack> apiRspTrackList = object.getTrackResults();
                if (apiRspTrackList == null || apiRspTrackList.size() == 0) {
                    isRequesting = false;
                    return;
                }
                MyAsyncTask.execute(new Runnable() {
                    @Override
                    public void run() {
                        String today = DateTimeUtil.getCurrentDate4yyyyMMdd();
                        //今天小于90%进度的声音
                        List<EverydayUpdateTrack> validTrack = new ArrayList<>();
                        List<EverydayUpdateTrack> unValidTrack = new ArrayList<>();
                        Map<Long, EverydayUpdateTrack> todayLessThan90TrackMap = new HashMap<>();
                        for (int i = 0; i < apiRspTrackList.size(); i++) {
                            EverydayUpdateTrack updateTrack = apiRspTrackList.get(i);
                            if (updateTrack == null) {
                                continue;
                            }
                            int lastPos = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getHistoryPos(updateTrack.getDataId());
                            int percent = ToolUtil.getPlayPercent(lastPos, updateTrack.getDuration());
                            if (percent < 90 && percent > 0) {
                                String tsDate4yyyyMMdd = DateTimeUtil.getTsDate4yyyyMMdd(updateTrack.getTimeline());
                                if (!TextUtils.isEmpty(today) && today.equalsIgnoreCase(tsDate4yyyyMMdd)) {
                                    todayLessThan90TrackMap.put(updateTrack.getDataId(), updateTrack);
                                }
                                validTrack.add(updateTrack);
                            } else if (percent == 0) {
                                validTrack.add(updateTrack);
                            } else {
                                unValidTrack.add(updateTrack);
                            }
                        }
                        EverydayUpdateTrack needTopTrack = null;
                        if (todayLessThan90TrackMap.size() > 0) {
                            IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
                            if (historyManager != null) {
                                List<HistoryModel> historyTrackList = historyManager.getTrackList();
                                if (historyTrackList != null && historyTrackList.size() > 0) {
                                    for (int i = 0; i < historyTrackList.size(); i++) {
                                        HistoryModel historyModel = historyTrackList.get(i);
                                        if (historyModel == null || historyModel.getEndedAt() == 0) {
                                            continue;
                                        }
                                        String endDate4yyyyMMdd = DateTimeUtil.getTsDate4yyyyMMdd(historyModel.getEndedAt());
                                        if (!TextUtils.isEmpty(today) && today.equalsIgnoreCase(endDate4yyyyMMdd)) {
                                            Track historyModelTrack = historyModel.getTrack();
                                            if (historyModelTrack != null) {
                                                EverydayUpdateTrack inTodayTrack = todayLessThan90TrackMap.get(historyModelTrack.getDataId());
                                                if (inTodayTrack != null) {
                                                    needTopTrack = inTodayTrack;
                                                    break;
                                                }
                                            }
                                        } else {
                                            //遇到不是今天的记录，直接brack，后面不需要再遍历
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        if (needTopTrack != null) {
                            validTrack.remove(needTopTrack);
                            //需要置顶该声音
                            needTopTrack.isNeedTopPosition = true;
                            validTrack.add(0, needTopTrack);
                        }
                        //不足3条填充满3条
                        if (validTrack.size() < 3) {
                            for (int i = 0; i < unValidTrack.size(); i++) {
                                if (validTrack.size() >= 3) {
                                    break;
                                }
                                validTrack.add(unValidTrack.get(i));
                            }
                        }
                        if (everydayUpdateTracks == null) {
                            everydayUpdateTracks = new ArrayList<>();
                        }
                        everydayUpdateTracks.clear();
                        everydayUpdateTracks.addAll(validTrack);

                        HandlerManager.postOnMainAuto(new Runnable() {
                            @Override
                            public void run() {
                                isRequesting = false;
                                long lastTimeLine = -1;
                                EverydayUpdateTrack lastTrack = apiRspTrackList.get(apiRspTrackList.size() - 1);
                                if (lastTrack != null) {
                                    lastTimeLine = lastTrack.getTimeline();
                                }
                                //切换到主线程更新，更新一次adapter
                                updateAdapter(lastTimeLine);
                            }
                        });
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                isRequesting = false;
            }
        });
    }


    private void updateAdapter(long lastTimeLine) {
        if (mAdapter == null) {
            return;
        }
        List<EverydayUpdateTrack> updateTrackList = getEverydayUpdateTrackList();
        if (updateTrackList == null || updateTrackList.size() == 0) {
            return;
        }
        //数据获取成功，更新一次数据
        List<ItemModel> listData = mAdapter.getListData();
        if (listData == null || listData.size() == 0) {
            return;
        }
        for (int i = 0; i < listData.size(); i++) {
            ItemModel itemModel = listData.get(i);
            if (itemModel == null) {
                continue;
            }
            if (itemModel.getViewType() != HomeRecommendAdapter.VIEW_TYPE_TING_UPDATE) {
                continue;
            }
            //是听更新楼层
            Object itemObject = itemModel.getObject();
            if (!(itemObject instanceof MainAlbumMList)) {
                continue;
            }
            HomeTingUpdateModel homeTingUpdateModel = ((MainAlbumMList) itemObject).getHomeTingUpdateModel();
            if (homeTingUpdateModel == null) {
                homeTingUpdateModel = new HomeTingUpdateModel();
                ((MainAlbumMList) itemObject).setHomeTingUpdateModel(homeTingUpdateModel);
            }
            if (homeTingUpdateModel.allTracks == null) {
                homeTingUpdateModel.allTracks = new ArrayList<>();
            }
            homeTingUpdateModel.allTracks.clear();
            List<EverydayUpdateTrack> trackList = new ArrayList<>(updateTrackList);
            homeTingUpdateModel.allTracks.addAll(trackList);
            //设置接口最后一条的时间线
            homeTingUpdateModel.lastTimeline = lastTimeLine;
            mAdapter.notifyDataSetChanged();
        }
//        if (fragmentView.getBaseFragment2() instanceof HomeRecommendFragment) {
//            HomeTingUpdateMaskManager.setNeedShowTingUpdateMask();
//            HomeTingUpdateMaskManager.checkTingUpdataAndShow(mActivity, mAdapter, (HomeRecommendFragment) fragmentView.getBaseFragment2(), 250);
//        }
    }

    public List<EverydayUpdateTrack> getEverydayUpdateTrackList() {
        return everydayUpdateTracks;
    }

    public void clearTingUpdateData() {
        if (everydayUpdateTracks != null) {
            everydayUpdateTracks.clear();
            everydayUpdateTracks = null;
        }
    }
}
