package com.ximalaya.ting.lite.main.home;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.ximalaya.ting.android.host.activity.web.WebActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.lite.main.album.fragment.GroupRankSingleFragment;
import com.ximalaya.ting.lite.main.home.fragment.KeywordMetadataFragment;
import com.ximalaya.ting.lite.main.home.viewmodel.TanghuluClickViewModel;
import com.ximalaya.ting.lite.main.manager.ITingHandler;
import com.ximalaya.ting.lite.main.model.album.RecommendDiscoveryM;
import com.ximalaya.ting.lite.main.model.album.RecommendDiscoveryProps;

/**
 * Created by qinhuifeng on 2019-07-18
 *
 * <AUTHOR>
 */
public class HomeTanghuluPresenter {
    //分类页面糖葫芦返回的h5类型是html5
    public static final String DISCOVER_TYPE_HTML5 = "html5";
    //首页糖葫芦返回的h5类型是h5
    public static final String DISCOVER_TYPE_H5 = "h5";
    public static final String DISCOVER_TYPE_ALBUM = "album_detail";
    public static final String DISCOVER_TYPE_GROUP_RANK = "rank_cluster";
    public static final String DISCOVER_TYPE_ALBUM_SUB_CATEGORY = "album_sub_category";
    public static final String DISCOVER_TYPE_ITING = "iting";
    public static final String DISCOVER_TYPE_UTING = "uting";

    private BaseFragment2 mFragment;

    public HomeTanghuluPresenter(BaseFragment2 mBaseFragment) {
        this.mFragment = mBaseFragment;
    }

    public void dealWithTanghuluClick(View v, final TanghuluClickViewModel clickViewModel) {
        if (mFragment == null || clickViewModel == null || clickViewModel.recommendDiscoveryM == null) {
            return;
        }
        RecommendDiscoveryM discoveryM = clickViewModel.recommendDiscoveryM;
        //contentType为空的情况，尝试解析h5和uting以及iting
        if (TextUtils.isEmpty(discoveryM.getContentType())) {
            dealWithH5OrUting(v, discoveryM);
            return;
        }
        RecommendDiscoveryProps discoveryProps = discoveryM.getProperties();
        switch (discoveryM.getContentType()) {
            case DISCOVER_TYPE_ITING:
            case DISCOVER_TYPE_UTING:
            case DISCOVER_TYPE_HTML5:
            case DISCOVER_TYPE_H5:
                dealWithH5OrUting(v, discoveryM);
                break;
            case DISCOVER_TYPE_ALBUM:
                // 专辑详情
                if (discoveryProps == null) {
                    return;
                }
                AlbumEventManage.startMatchAlbumFragment(discoveryProps.getAlbumId(), AlbumEventManage.FROM_DISCOVERY_HEAD, ConstantsOpenSdk.PLAY_FROM_OTHER, null, null, -1, mFragment.getActivity());
                break;
            case DISCOVER_TYPE_GROUP_RANK:
                //聚合榜
                if (discoveryProps == null) {
                    return;
                }
                mFragment.startFragment(GroupRankSingleFragment.getInstance(discoveryProps.rankClusterId));
                break;
            case DISCOVER_TYPE_ALBUM_SUB_CATEGORY:
                if (discoveryProps == null) {
                    return;
                }
                //跳转热词页面，待筛选的热词页面
                int categoryIdForSub = discoveryProps.getCategoryId();
                int keywordIdForSub = discoveryProps.getKeywordId();
                String keyNameForSub = discoveryProps.getSubCategory();
                Bundle argumentForSub = KeywordMetadataFragment.createArgumentFromSinglePage(categoryIdForSub, keywordIdForSub, keyNameForSub);
                KeywordMetadataFragment fragmentForSub = new KeywordMetadataFragment();
                fragmentForSub.setArguments(argumentForSub);
                mFragment.startFragment(fragmentForSub);
                break;
            default:
                //没有配置正确contentType的情况，尝试解析h5和uting以及iting
                dealWithH5OrUting(v, discoveryM);
                break;
        }
    }


    /**
     * 处理uting和iting以及h5的跳转
     */
    private void dealWithH5OrUting(View v, RecommendDiscoveryM discoveryM) {
        if (discoveryM == null) {
            return;
        }
        RecommendDiscoveryProps discoveryProps = discoveryM.getProperties();
        //iting形式的跳转，uting跳转
        String useUrl = "";
        //优先获取外部的url，首页糖葫芦使用的是url,获取不到的情况，取RecommendDiscoveryProps中的uri字段
        if (!TextUtils.isEmpty(discoveryM.getUrl())) {
            useUrl = discoveryM.getUrl();
        } else {
            if (discoveryProps != null && !TextUtils.isEmpty(discoveryProps.getUri())) {
                useUrl = discoveryProps.getUri();
            }
        }
        if (TextUtils.isEmpty(useUrl)) {
            return;
        }
        if (useUrl.startsWith("iting://") || useUrl.startsWith("uting://")) {
            ITingHandler tingHandlerH5 = new ITingHandler();
            tingHandlerH5.handleITing(mFragment.getActivity(), Uri.parse(useUrl));
        } else if (useUrl.startsWith("http")) {
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, useUrl);
            bundle.putBoolean(NativeHybridFragment.SHOW_SHARE_BTN, discoveryM.isEnableShare());
            bundle.putString(NativeHybridFragment.SHARE_COVER_PATH, discoveryM.getSharePic());
            bundle.putBoolean(NativeHybridFragment.IS_EXTERNAL_URL, discoveryM.isExternalUrl());
            mFragment.startFragment(NativeHybridFragment.class, bundle, v);
        }
    }
}
