package com.ximalaya.ting.lite.main.mine;

import android.content.Context;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import android.view.View;

import com.ximalaya.ting.android.host.fragment.BaseFragment2;

/**
 * MVP接口，业务逻辑和view层抽离
 * <p>
 * 只定义了接口，没有定义BaseMySpacePresenter
 *
 * <AUTHOR>
 */
public interface MineContract {

    //IView接口 MySpaceFragmentNew实现
    interface IMineView {

        boolean canUpdateUi();

        Context getContext();

        FragmentActivity getActivity();

        boolean isRealVisable();

        Bundle getArguments();

        void startFragment(Fragment fra, int inAnim, int outAnim);

        Fragment startFragment(Class<?> className, Bundle bundle, View fromView);

        FragmentManager getChildFragmentManager();

        void startFragment(Fragment fra);

        void startFragment(final Fragment fra, final View fromView);

        BaseFragment2 getBaseFragment2();

        <T extends View> T findViewById(int id);
    }

    interface IMySpacePagePresenter {

    }
}
