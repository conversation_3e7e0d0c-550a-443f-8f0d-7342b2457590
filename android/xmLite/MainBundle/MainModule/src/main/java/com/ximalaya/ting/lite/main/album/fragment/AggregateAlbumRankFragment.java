package com.ximalaya.ting.lite.main.album.fragment;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;
import android.widget.AdapterView;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.lite.main.album.adapter.AggregateAlbumRankAdapter;
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter;
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder;
import com.ximalaya.ting.lite.main.model.rank.AggregateListConfigModel;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankCategoryModel;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankListTabsDataItemRsp;
import com.ximalaya.ting.lite.main.model.rank.GroupRankAlbumList;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by qinhuifeng on 2019-11-16
 *
 * <AUTHOR>
 */
public class AggregateAlbumRankFragment extends BaseFragment2 implements AdapterView.OnItemClickListener {

    public static final String ARGS_CLUSTER_TYPE = "args_cluster_type";
    public static final String ARGS_SELECT_RANK_LIST_ID = "args_select_rank_list_Id";

    //分类列表
    private RecyclerView mRvCategory;
    private List<AggregateRankCategoryModel> mCategoryList = new ArrayList<>();
    private MultiRecyclerAdapter<AggregateRankCategoryModel, SuperRecyclerHolder> mRvRankCateGoryAdapter;
    private long mClusterType = 0;
    private long mSelectRankListId = 0;

    private boolean mIsLoading = false;
    private RefreshLoadMoreListView mListView;
    private AggregateAlbumRankAdapter mAdapter;
    private int mPageId = 1;
    private long mTotalCount = -1;

    public static Bundle newArgument(long clusterType, long selectRankListId) {
        Bundle bundle = new Bundle();
        bundle.putLong(ARGS_CLUSTER_TYPE, clusterType);
        bundle.putLong(ARGS_SELECT_RANK_LIST_ID, selectRankListId);
        return bundle;
    }

    @Override
    protected String getPageLogicName() {
        return "AggregateAlbumRankFragment";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mClusterType = arguments.getLong(ARGS_CLUSTER_TYPE, 0);
            mSelectRankListId = arguments.getLong(ARGS_SELECT_RANK_LIST_ID, 0);
        }
        mRvCategory = findViewById(R.id.main_rv_category);

        initRvCategoryAdapter();

        mListView = findViewById(R.id.main_list_rank);
        mListView.setOnRefreshLoadMoreListener(new IRefreshLoadMoreListener() {
            @Override
            public void onRefresh() {
                mPageId = 1;
                requestAlbumList();
            }

            @Override
            public void onMore() {
                requestAlbumList();
            }
        });
        mListView.getRefreshableView().setPadding(0, 0, 0, getResourcesSafe().getDimensionPixelSize(R.dimen.host_bottom_bar_height));
        mListView.getRefreshableView().setClipToPadding(false);
        mListView.setOnItemClickListener(this);
        mAdapter = new AggregateAlbumRankAdapter((MainActivity) mActivity, null);
        mListView.setAdapter(mAdapter);
    }

    private void initRvCategoryAdapter() {
        mRvRankCateGoryAdapter = new MultiRecyclerAdapter<AggregateRankCategoryModel, SuperRecyclerHolder>(mActivity, mCategoryList) {
            @Override
            public SuperRecyclerHolder createMultiViewHolder(Context mCtx, @NonNull View itemView, int viewType) {
                return SuperRecyclerHolder.createViewHolder(mCtx, itemView);
            }

            @Override
            public void onBindMultiViewHolder(SuperRecyclerHolder holder, AggregateRankCategoryModel categoryModel, int viewType, int position) {
                holder.setText(R.id.main_rank_category_title_item, categoryModel.displayName);
                if (mSelectRankListId == categoryModel.rankingListId) {
                    holder.setVisibility(R.id.main_rank_category_title_item_selected, View.VISIBLE);
                    holder.setTextColor(R.id.main_rank_category_title_item, Color.parseColor("#111111"));
                    holder.setBackgroundColor(R.id.main_rank_category_title_item_layout, Color.parseColor("#ffffff"));
                } else {
                    holder.setVisibility(R.id.main_rank_category_title_item_selected, View.INVISIBLE);
                    holder.setTextColor(R.id.main_rank_category_title_item, Color.parseColor("#666666"));
                    holder.setBackgroundColor(R.id.main_rank_category_title_item_layout, Color.parseColor("#f3f4f5"));
                }
                holder.setOnItemClickListenner(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (!OneClickHelper.getInstance().onClick(view)) {
                            return;
                        }
                        if (mSelectRankListId == categoryModel.rankingListId) {
                            return;
                        }
                        mSelectRankListId = categoryModel.rankingListId;
                        mRvRankCateGoryAdapter.notifyDataSetChanged();
                        //选中的item发生变化，重新请求
                        mPageId = 1;
                        requestAlbumList();
                    }
                });
            }

            @Override
            public int getMultiItemViewType(AggregateRankCategoryModel model, int position) {
                return 0;
            }

            @Override
            public int getMultiItemLayoutId(int viewType) {
                return R.layout.main_item_aggregate_rank_category_title;
            }
        };
        mRvCategory.setAdapter(mRvRankCateGoryAdapter);
        LinearLayoutManager layoutManager = new LinearLayoutManager(mActivity);
        mRvCategory.setLayoutManager(layoutManager);

        mCategoryList.clear();
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof AggregateRankFragment) {
            AggregateRankListTabsDataItemRsp listTabsDataItemRsp = ((AggregateRankFragment) parentFragment).getAggregateRankCategoryModelByClusterType(mClusterType);
            if (listTabsDataItemRsp != null && listTabsDataItemRsp.rankGroups != null && listTabsDataItemRsp.rankGroups.size() > 0) {
                mCategoryList.addAll(listTabsDataItemRsp.rankGroups);
                mRvRankCateGoryAdapter.notifyDataSetChanged();
            }
        }
        int selectPosition = -1;
        for (int i = 0; i < mCategoryList.size(); i++) {
            AggregateRankCategoryModel categoryModel = mCategoryList.get(i);
            if (categoryModel == null) {
                continue;
            }
            if (mSelectRankListId == categoryModel.rankingListId) {
                selectPosition = i;
                break;
            }
        }
        //没有查到选中的item，默认选中0,mSelectRankClusterId选中0号元素
        if (selectPosition == -1) {
            selectPosition = 0;
            if (mCategoryList.size() > 0) {
                AggregateRankCategoryModel model = mCategoryList.get(0);
                if (model != null) {
                    mSelectRankListId = model.rankingListId;
                }
            }
        }
        layoutManager.scrollToPosition(selectPosition);
    }

    @Override
    protected void loadData() {
        if (mCategoryList.size() == 0) {
            mRvCategory.setVisibility(View.INVISIBLE);
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            return;
        }
        //请求排行榜专辑内容
        requestAlbumList();
    }

    private void requestAlbumList() {
        if (mIsLoading) {
            return;
        }
        mIsLoading = true;
        HashMap<String, String> p = new HashMap<>();
        p.put("device", "android");
        p.put("version", DeviceUtil.getVersion(mContext));
        p.put("pageId", String.valueOf(mPageId));
        p.put("pageSize", "20");
        p.put("rankingListId", String.valueOf(mSelectRankListId));
        p.put("speed", "2");
        //mClusterType=12代表的是免费榜，客户端改动下参数，使用免费榜参数
        if (mClusterType == AggregateListConfigModel.CLUSTER_TYPE_FREE_RANK) {
            p.put("speed", "1");
        }
        LiteCommonRequest.getAggregateRankGroupAlbumList(p, new IDataCallBack<GroupRankAlbumList>() {
            @Override
            public void onSuccess(@Nullable final GroupRankAlbumList object) {
                mIsLoading = false;
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        if (object == null) {
                            checkAndShowNetError();
                            return;
                        }
                        mTotalCount = object.totalCount;
                        if (object.list == null) {
                            checkAndShowNetError();
                            return;
                        }
                        checkAndShowNoContent();
                        if (mPageId == 1) {
                            mAdapter.clear();
                        }
                        mAdapter.addListData(object.list);
                        if (mPageId == 1) {
                            mListView.getRefreshableView().setSelection(0);
                        }
                        List<Album> listData = mAdapter.getListData();
                        if (listData != null) {
                            if (listData.size() < mTotalCount) {
                                //还有下一页
                                mPageId++;
                                mListView.onRefreshComplete(true);
                            } else {
                                //没有下一页了
                                mListView.onRefreshComplete(false);
                            }
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                mIsLoading = false;
                if (!canUpdateUi()) {
                    return;
                }
                checkAndShowNetError();
            }
        });
    }

    private void checkAndShowNetError() {
        if (mAdapter == null) {
            return;
        }
        List<Album> listData = mAdapter.getListData();
        if (listData != null && listData.size() == 0) {
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        }
    }

    private void checkAndShowNoContent() {
        if (mAdapter == null) {
            return;
        }
        List<Album> listData = mAdapter.getListData();
        if (listData != null && listData.size() == 0) {
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_aggregate_album_rank;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        List<Album> listData = mAdapter.getListData();
        if (listData == null || listData.size() == 0) {
            return;
        }
        if (index >= 0 && index < mAdapter.getCount()) {
            Album album = mAdapter.getListData().get(index);
            if (!(album instanceof AlbumM)) {
                return;
            }
            AlbumM albumM = (AlbumM) album;
            AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_OTHER, ConstantsOpenSdk.PLAY_FROM_RANK, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
        }
    }
}
