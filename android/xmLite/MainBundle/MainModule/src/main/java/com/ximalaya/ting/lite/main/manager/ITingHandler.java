package com.ximalaya.ting.lite.main.manager;

import static com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router.getMainActionRouter;
import static com.ximalaya.ting.android.host.util.constant.MMKVKeyConstantsKt.MMKV_VILLAGE_ACCESS_FROM_URBAN_CULTURE;
import static com.ximalaya.ting.android.host.util.constant.MMKVKeyConstantsKt.MMKV_VILLAGE_ACCESS_PARAM;
import static com.ximalaya.ting.android.host.util.constant.MMKVKeyConstantsKt.MMKV_VILLAGE_ACCESS_UTM_SOURCE;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.ximalaya.ting.android.adsdk.ADActivity;
import com.ximalaya.ting.android.adsdk.bridge.IActivity;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.TruckFriendModeActivity;
import com.ximalaya.ting.android.host.activity.ad.PullNewActivity;
import com.ximalaya.ting.android.host.activity.manager.AppModeManager;
import com.ximalaya.ting.android.host.activity.utils.MainActivityUtils;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.FreeListenVipAlbumDetailFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.hybrid.utils.ComponentSecuritySignUtils;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.manager.ThirdExchangeManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.StartRoomIntent;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.search.ISearchFragmentActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectDialogHintManager;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.feedback.CustomerFeedBackManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.iting.ItingManager;
import com.ximalaya.ting.android.host.manager.iting.ItingManagerBugFix;
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.newuser.NewListenTrackIdManager;
import com.ximalaya.ting.android.host.manager.pay.PayManager;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.newuser.NewListenPlayingInfo;
import com.ximalaya.ting.android.host.model.newuser.QuickListenModel;
import com.ximalaya.ting.android.host.model.push.PushModel;
import com.ximalaya.ting.android.host.model.truck.TruckRecommendModel;
import com.ximalaya.ting.android.host.model.truck.TruckRecommendTrackM;
import com.ximalaya.ting.android.host.util.ContextUtils;
import com.ximalaya.ting.android.host.util.ReadUtils;
import com.ximalaya.ting.android.host.util.ShareNewUtils;
import com.ximalaya.ting.android.host.util.ShareStringCodeUtils;
import com.ximalaya.ting.android.host.util.StringCodeManager;
import com.ximalaya.ting.android.host.util.StringCodeUtils;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.view.CustomTipsView;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.lite.main.album.fragment.AggregateRankFragment;
import com.ximalaya.ting.lite.main.album.fragment.NewAggregateRankFragment;
import com.ximalaya.ting.lite.main.book.fragment.SingletonSubscribeFragment;
import com.ximalaya.ting.lite.main.download.DownloadedAlbumListFragment;
import com.ximalaya.ting.lite.main.home.fragment.HomeAllCategoryListFragment;
import com.ximalaya.ting.lite.main.home.fragment.HomeCategoryContentTabFragment;
import com.ximalaya.ting.lite.main.home.fragment.HomeCategoryRecommendFragment;
import com.ximalaya.ting.lite.main.home.fragment.KeywordMetadataFragment;
import com.ximalaya.ting.lite.main.home.fragment.NewContentPoolListFragment;
import com.ximalaya.ting.lite.main.home.fragment.NewHomeCategoryContentTabFragment;
import com.ximalaya.ting.lite.main.home.fragment.NewSinglePageCategoryMetadataFragment;
import com.ximalaya.ting.lite.main.home.fragment.PlayPageTagCategoryMetadataFragment;
import com.ximalaya.ting.lite.main.home.fragment.TrackContentListFragment;
import com.ximalaya.ting.lite.main.home.fragment.VipAlbumContentListFragment;
import com.ximalaya.ting.lite.main.model.onekey.OneKeyRadioModel;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankArgsModel;
import com.ximalaya.ting.lite.main.model.uting.MetaSelectArgs;
import com.ximalaya.ting.lite.main.model.uting.ParsingDtingRsp;
import com.ximalaya.ting.lite.main.mylisten.view.AllHistoryFragment;
import com.ximalaya.ting.lite.main.newhome.fragment.LiteHomeNormalFragment;
import com.ximalaya.ting.lite.main.newuser.NewListenPlayFragment;
import com.ximalaya.ting.lite.main.newuser.NewUserGuideFragment;
import com.ximalaya.ting.lite.main.onekey.OneKeyRadioFragment;
import com.ximalaya.ting.lite.main.onekey.playpage.OneKeyRadioPlayFragment;
import com.ximalaya.ting.lite.main.playlet.fragment.PlayletDetailFragment;
import com.ximalaya.ting.lite.main.read.fragment.LoveNovelTabFragment;
import com.ximalaya.ting.lite.main.read.fragment.NovelClassifyFragment;
import com.ximalaya.ting.lite.main.read.fragment.NovelRankFragment;
import com.ximalaya.ting.lite.main.read.fragment.NovelRecommendFragment;
import com.ximalaya.ting.lite.main.read.fragment.NovelTypeFilterFragment;
import com.ximalaya.ting.lite.main.read.model.LoveNovelRankArgsModel;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.setting.SettingFragment;
import com.ximalaya.ting.lite.main.truck.model.TruckPlayPageInfo;
import com.ximalaya.ting.lite.main.truck.tab.TruckHomeFragment;
import com.ximalaya.ting.lite.main.vip.VipTabFragment;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * iTIng接口的处理类
 *
 * <AUTHOR> on 2016/9/6.
 */
public class ITingHandler {
    interface ITing {
        boolean handleITing(final Activity activity, final PushModel pm);
    }

    private final static String TAG = ITingHandler.class.getSimpleName();
    boolean isOuterLink = false;
    private Uri mData;

    private long mLastHandleTrackTime;


    private static List<Track> parseTrackList(String tracks) {
        if (TextUtils.isEmpty(tracks))
            return null;
        if (tracks.contains("["))
            tracks.replace("[", "");
        if (tracks.contains("]"))
            tracks.replace("]", "");
        String[] trackIds = tracks.split(",");

        List<Track> trackList = new ArrayList<>();
        for (int i = 0; i < trackIds.length; i++) {
            Track sound = new Track();
            sound.setDataId(Long.valueOf(trackIds[i]));
            sound.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PUSH);
            trackList.add(sound);
        }
        return trackList;
    }

    private static int parseInt(String number) {
        if (TextUtils.isEmpty(number))
            return 0;

        try {
            return Integer.parseInt(number);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return 0;
    }

    private static long parseLong(String number) {
        if (TextUtils.isEmpty(number))
            return 0;

        try {
            return Long.parseLong(number);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return 0;
    }

    private static double parseDouble(String number) {
        if (TextUtils.isEmpty(number))
            return 0;

        try {
            return Double.parseDouble(number);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return 0;
    }

    private static boolean parseBoolean(String bool) {
        if (TextUtils.isEmpty(bool))
            return false;

        return Boolean.parseBoolean(bool);
    }

    public boolean handleITing(final Activity activity, Uri data) {
        mData = data;
        if (mData != null && !TextUtils.isEmpty(mData.toString())) {
            if (mData.toString().contains("uting") || mData.toString().contains("iting")) {
                new XMTraceApi.Trace()
                        .setMetaId(14300)
                        .setServiceId("openIting")
                        .put("itingUrl", mData.toString())
                        .createTrace();
            }
        }
        return baseHandleITing(activity, getPushModelFromUri(data));
    }


    private boolean handleITing(Activity activity, Uri uri, PushModel oldPm) {
        mData = uri;

        PushModel newPm = getPushModelFromUri(uri);
        if (newPm != null && oldPm != null) {
            newPm.msgId = oldPm.msgId;
        }
        return baseHandleITing(activity, newPm);
    }

    public boolean handleITing(final Activity activity, final PushModel pm) {
        if (pm == null) {
            return false;
        }
        return baseHandleITing(activity, pm);
    }

    public boolean baseHandleITing(final Activity activity, final PushModel pm) {
        if (pm == null) {
            return false;
        }
        Logger.d("MainActivity",
                "handleITing messageType " + pm.messageType + ", " + pm.recSrc + ", " + pm.msgId);
        MainActivity mainActivity = null;
        if (activity instanceof MainActivity) {
            mainActivity = (MainActivity) activity;
        }
        if (!pm.keepWeb && null != mainActivity) {
//            mainActivity.closeWebFragment();
            //增加isNeedForbidCloseWebFragment判断解决选中底部tab页面透明页重叠，选中底部tab
            // 的关闭页面可以使用clearAllFragmentFromStacks
            if (!isNeedForbidCloseWebFragment(pm.messageType)) {
                MainActivityUtils.closeWebFragment(mainActivity);
            }
        }
        if (toWebComponent(activity, pm.schema)) {
            return true;
        }
        if (mainActivity == null) {
            return false;
        }
        if (pm.isPush) {
            return handleITing(activity, Uri.parse(pm.url));
        }
        if (mData != null && !TextUtils.isEmpty(mData.toString())) {
            if (mData.toString().contains("uting") || mData.toString().contains("iting")) {
                String value14300 = mData.toString();
                if (ItingManagerBugFix.xMPointTraceApiHasInit) {
                    new XMTraceApi.Trace()
                            .setMetaId(14300)
                            .setServiceId("openIting")
                            .put("itingUrl", value14300)
                            .createTrace();
                } else {
                    HandlerManager.postOnUIThreadDelay(new Runnable() {
                        @Override
                        public void run() {
                            new XMTraceApi.Trace()
                                    .setMetaId(14300)
                                    .setServiceId("openIting")
                                    .put("itingUrl", value14300)
                                    .createTrace();
                        }
                    }, 3000);
                }
            }
        }
        try {
            switch (pm.messageType) {
                case 0: //open app
                    break;
                case AppConstants.PAGE_SOUND:
                    //打开播放页面
                    handleSoundPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_ALBUM:
                    //跳转到专辑详情页面
                    handleAlbumPlayPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_URL:
                    //打开h5页面
                    handeH5Page(mainActivity, pm);
                    break;
                case AppConstants.PAGE_LOGIN:
                    //打开登录页面
                    handleLoginPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_EDIT:
                    //打开个人信息编辑页面
                    break;
                case AppConstants.PAGE_TO_RECHARGE_ITEM:
                    handleRechargePage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_FEEDBACK_CHAT:
                    //打开客服页面
                    CustomerFeedBackManager.jumpToFeedBackChat();
                    break;
                case AppConstants.PAGE_TO_RADIO_PLAY:
                    //打开广播页面
                    break;
                case AppConstants.PAGE_TO_CHILD_PROTECT_FORBID_PLAYPAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    //跳转到儿童保护禁止播放页面
                    ChildProtectDialogHintManager.handUtingForChildProtect(pm.from);
                    break;
                case AppConstants.PAGE_ONE_KEY_TING_RADIO_PLAY_PAGE:
                    //打开电台，一键听
                    handleOneKeyPlayPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_ONE_KEY_LISTEN_LIST:
                    //打开电台列表页面
                    handleOneKeyRadioListPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_SEARCH:
                    //打开搜索页面并搜索热词
                    handleSearchPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_CATEGORY_RECOMMED_PAGE:
                    //打开分类--相关推荐页面
                    handleCategoryRecommedPage(mainActivity, pm);
                    break;
                case AppConstants.VIP_TAB_PAGE:
                    //打开VIP界面
                    handleVipPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_CATEGORY_CONTENT_TAB_PAGE:
                    //跳转到分类---tab页面--默认选中全部，根据keywordid和title选中指定页面
                    handleCategoryContentTabPage(mainActivity, pm);
                    //handleNewCategoryContentTabPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_CATEGORY_HOT_KEY_PAGE:
                    //分类热词单独页面
                    handleCategoryHotKeyPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_RANK_LIST:
                    //排行榜
                    handleRankList(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_BOTTOM_TAB:
                    //选中底部的tab
                    handlePageBottomTab(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_CM_GAME_CENTER:
                    //跳转到CM游戏中心页面
                    handlePageCMGameCenter(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_OPEN_PLAY_PAGE:
                    //打开播放页，并播放，播放列表为空，跳转到排行榜
                    handleOpenPlayPageAndEmpty(mainActivity, pm);
                    break;
                case AppConstants.PAGE_LOGIN_OUT:
                    handleLoginOut(mainActivity, pm);
                    break;
                case AppConstants.NEW_USER_MUST_LISTEN:
                    handleNewUserMustListener(mainActivity, pm);
                    break;
                case AppConstants.PAGE_UTING_DTING_REDIRECT:
                    //动态uting跳转，请求一个真实的跳转地址
                    handleUtingDtingRedirect(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_PLAY_HISTORY:
                    //跳转到播放历史
                    handlePlayHistory(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_PLAY_MY_DOWNLOAD:
                    //跳转到我的下载
                    handleMyDownload(mainActivity, pm);
                    break;
                case AppConstants.NEW_SINGLE_META_DATA_PAGE:
                    //新的元数据筛选页
                    handleNewSingleMetaDataPage(mainActivity, pm);
                    break;
                case AppConstants.NEW_RANK_DETAIL:
                    //新的排行榜
                    handleNewRankList(mainActivity, pm);
                    break;
                case AppConstants.NEW_MALE_FEMALE_META_DATA_PAGE:
                    //新的排行榜
                    handleNewCategoryContentTabPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_ALL_CATEGORY:
                    //全部分类
                    handleAllCategory(mainActivity, pm);
                    break;
                case AppConstants.PAGE_NEW_HOME_PAGE:
                    //定位到新的首页tab
                    handleNewHomePage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_CONTENT_POOL_PAGE:
                    //内容池列表页面
                    handleContentPoolListFragment(mainActivity, pm);
                    break;
                case AppConstants.PAGE_SINGLE_LITE_NORMAL:
                    //分类页
                    handleSingleLiteHomeNormalFragment(mainActivity, pm);
                    break;
                case AppConstants.PAGE_NEW_LISTEN:
                    //新人极速听
                    handleNewListen(mainActivity, pm);
                    break;
                case AppConstants.PAGE_SETTING:
                    //跳转到设置页
                    handleSetting(mainActivity, pm);
                    break;
                case AppConstants.PAGE_ONLY_FILTER:
                    handleOnlyFilter(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TRUCK_MODE_RADIO_PAGE:
                    //handleSoundPage(mainActivity, pm);
                    handleTruckModeRadioTab(mainActivity, pm);
                    break;
                case AppConstants.PAGE_NOVEL_READER:
                    //阅读器
                    handleReadPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_SUBSCRIBE_PAGE:
                    // 订阅界面  location为1时，是播放页
                    handleSubscribePage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_NOVEL_RANK_LIST_PAGE:
                    // 小说排行榜 选填rankingList
                    handleNovelRankingList(mainActivity, pm);
                    break;
                case AppConstants.PAGE_NOVEL_CLASSIFY_PAGE:
                    // 小说一级分类页
                    handleNovelClassifyPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_NOVEL_POOL_PAGE:
                    // 内容池页面 poolId必填，title选填,需utf-8编码
                    handleNovelPoolPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_HISTORY_PAGE:
                    // 历史界面
                    handleHistoryPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_NOVEL_FILTER_PAGE:
                    // 小说二级分类页
                    handleNovelTypeFilterPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_RN: {//前往视频直播间

                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.bundle != null && pm.bundle.equals("live_mall")) {
                        CustomToast.showToast("暂不支持跳转商城");
                        return true;
                    }
                    CustomToast.showToast("暂不支持跳转");
                    break;

                }
                case AppConstants.PAGE_TO_LIVE_HOME_PAGE:// 52 打开直播首页
                case AppConstants.PAGE_TO_LIVE_HOME_PAGE_SELECTED_CATEGORY_TAB:// 151 打开娱乐派对
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (pm.segmentId <= 0 && pm.open_type == 0) {
                        // 把主站的 tab 切换到 直播
//                        gotoMainBundleAsync(pm, mainActivity);
                    } else {
                        gotoLiveBundleAsync(pm, mainActivity);
                    }
                    break;
                case AppConstants.PAGE_TO_LIVE_BY_ROOM_ID:
                case AppConstants.PAGE_TO_LIVE_CATEGORY:
                case AppConstants.PAGE_TO_LIVE_ADMIN_LIST:
                case AppConstants.PAGE_TO_LIVE_RECORD_LIST:
                case AppConstants.PAGE_TO_LIVE_CREATE:
                case AppConstants.PAGE_TO_OPEN_H5_IN_LIVE_DIALOG:
                case AppConstants.PAGE_TO_RECOMMEND_LIVE:
                case AppConstants.PAGE_TO_ENT_HOME_FRAGMENT:
                case AppConstants.PAGE_TO_ENT_HALL_ROOM_FRAGMENT:
                case AppConstants.PAGE_TO_EDIT_DANMU_GIFT:
                case AppConstants.PAGE_TO_LIVE_DECORATE_CENTER:
                case AppConstants.PAGE_TO_KTV_LIST_FRAGMENT:
                case AppConstants.PAGE_TO_KTV_ROOM_FRAGMENT:
                case AppConstants.PAGE_TO_OPEN_PK_RESULT_DIALOG:
                case AppConstants.PAGE_TO_OPEN_LISTEN_AWARD_DIALOG:
                case AppConstants.PAGE_TO_OPEN_LIVE_FROM_ADVERTISEMENT:
                case AppConstants.PAGE_TO_LIVE_MY_LIVES_FRAGMENT:
                case AppConstants.PAGE_TO_LIVE_PROVIDE_FOR_H5_CUSTOMER_DIALOG:
                case AppConstants.PAGE_TO_OPEN_MY_JOINED_GUARDIAN_PAGE:
                case AppConstants.PAGE_TO_OPEN_GIFT_PANEL:
                case AppConstants.PAGE_TO_OPEN_GIFT_PACKAGE_ITEM:
                case AppConstants.PAGE_TO_OPEN_LIVE_USER_CARD:
                case AppConstants.PAGE_TO_VIDEO_LIVE_ROOM:
                case AppConstants.PAGE_TO_VIDEO_LIVE_LIST:
                case AppConstants.PAGE_TO_LIVE_ALBUM_PAGE:
                case AppConstants.PAGE_TO_OPEN_LIVE_PODCAST_DIALOG:
                case AppConstants.PAGE_TO_CLOSE_VIDEO_FLOAT_WINDOW:
                case AppConstants.PAGE_TO_LIVE_RECHARGE_MODEL:
                case AppConstants.PAGE_TO_CHATROOM_LIVE_ROOM:
                case AppConstants.PAGE_TO_LIVE_GIFT_PACKAGE:
                case AppConstants.PAGE_TO_QUERY_RECOMMEND_LIVE_ROOM:
                case AppConstants.PAGE_TO_PIA_SCRIPT_DETAIL:
                case AppConstants.PAGE_TO_PIA_AUTHOR_DETAIL:
                case AppConstants.PAGE_TO_LIVE_MINE_FUNCTION:
                case AppConstants.PAGE_TO_LIVE_MINE_PAID_LIST:
                case AppConstants.PAGE_TO_RANDOM_UGC_ROOM:
                case AppConstants.PAGE_TO_SELL_TOGGLE:
                case AppConstants.PAGE_TO_NEW_CHAT_ROOM_CREATE_PAGE: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoLiveBundleAsync(pm, mainActivity);
                    break;
                }
                case AppConstants.PAGE_UTING_LIVE_HOME_PAGE:
                    if (pm.segmentId > 0) {
                        //配置了分类id
                        //打开带tab切换的直播页
                        handStartLiveTabAudioFragment(mainActivity, pm);
                    } else {
                        handleOpenLiveHomePage(mainActivity, pm);
                    }
                    break;
                case AppConstants.PAGE_UTING_RECOMMEND_LIVE:
                    handleRecommendLive(mainActivity, pm);
                    break;
                case AppConstants.PAGE_UTING_LIVE_ROOM_BY_ROOM_ID:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    final FragmentActivity mainActivity2 = mainActivity;
                    Router.getActionByCallback(Configure.BUNDLE_LIVE,
                            new Router.IBundleInstallCallback() {
                                @Override
                                public void onInstallSuccess(BundleModel bundleModel) {
                                    if (TextUtils.equals(Configure.liveBundleModel.bundleName,
                                            bundleModel.bundleName)) {
                                        if (pm.liveRoomId > 0) {
                                            PlayTools.playLiveAudioByRoomIdWithPlaySource(mainActivity2,
                                                    pm.liveRoomId, pm.disableSlide, pm.playSource, pm.showBack);
                                        }
                                    }
                                }

                                @Override
                                public void onLocalInstallError(Throwable t,
                                                                BundleModel bundleModel) {

                                }

                                @Override
                                public void onRemoteInstallError(Throwable t,
                                                                 BundleModel bundleModel) {

                                }
                            });

                    break;
                case AppConstants.PAGE_BOOK_CITY_PAGE:
                    //if (AppModeManager.isAppModeForNormal()) {
                    handleBookCityTabFragment(mainActivity, pm);
                    //}
                    break;
                case AppConstants.PAGE_PLAYLET_PLAYER:
                    handlePlayletPlayer(mainActivity, pm);
                    break;
                case AppConstants.PAGE_PULL_NEW_PAGE:
                    handlePullNewPage(mainActivity, pm);
                case AppConstants.PAGE_VILLAGE:
                    if (AppModeManager.isAppModeForNormal()) {
                        handleVillage(mainActivity, pm);
                    }
                    break;
                case AppConstants.ASSIST_TYPE_CODE:
                    // 分享助力
                    StringCodeManager.INSTANCE.openActivity(mainActivity, pm.schema);
                    break;
                case AppConstants.PAGE_FREE_LISTENER_ALBUM_LIST:
                    // 分享助力
                    // uting://open?msg_type=10038&albumId=1  带专辑id则跳详情, 不带则跳列表 兼容老版本
                    handleFreeListenerAlbum(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TRACK_CONTENT_POOL_PAGE:
                    //声音内容池列表页面
                    handleTrackContentPoolListFragment(mainActivity, pm);
                    break;
                default:
                    //不支持的类型
                    CustomToast.showToast("无法打开页面");
                    break;
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            if (mainActivity == null) {
                sendToMainActivityToHandle(activity);
            }
            return false;
        }
    }

    /**
     * 专辑解锁详情
     */
    private void handleFreeListenerAlbum(MainActivity activity, PushModel pm) {
        if (activity == null || pm == null) {
            return;
        }
        long albumId = 0L;
        if (!TextUtils.isEmpty(pm.schema)) {
            try {
                Uri uri = Uri.parse(pm.schema);
                albumId = Long.parseLong(uri.getQueryParameter("albumId"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        // 适配老版本  老板没不能跳详情
        if (albumId != 0) {
            activity.startFragment(FreeListenVipAlbumDetailFragment.Companion.newInstance(albumId, "", ""));
        } else {
            StringCodeUtils.INSTANCE.startVipAlbumContentListPage();
        }
    }

    private void handleNovelTypeFilterPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }

        try {
            Long categoryId = 0L;
            Long subCategoryId = 0L;
            if (!TextUtils.isEmpty(pm.schema)) {
                try {
                    Uri uri = Uri.parse(pm.schema);
                    categoryId = Long.parseLong(uri.getQueryParameter("channelId"));
                    subCategoryId = Long.parseLong(uri.getQueryParameter("categoryId"));

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            mainActivity.startFragment(
                    NovelTypeFilterFragment.newInstance(categoryId,
                            null,
                            subCategoryId,
                            null));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleNovelPoolPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }

        String poolId = ""; //pooId 必填
        String title = "";
        if (!TextUtils.isEmpty(pm.schema)) {
            try {
                Uri uri = Uri.parse(pm.schema);
                poolId = uri.getQueryParameter("poolId");
                title = uri.getQueryParameter("title");

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (poolId == null || poolId.isEmpty()) {
            return;
        }
        try {
            Fragment recommendFragment;
            if (title == null || title.isEmpty()) {
                recommendFragment = NovelRecommendFragment.newInstance(poolId);
            } else {
                recommendFragment = NovelRecommendFragment.newInstance(poolId, title);
            }
            mainActivity.startFragment(recommendFragment);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleNovelClassifyPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }

        try {
            mainActivity.startFragment(NovelClassifyFragment.newInstance());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleNovelRankingList(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        String rankingListId = "";
        if (!TextUtils.isEmpty(pm.schema)) {
            try {
                Uri uri = Uri.parse(pm.schema);
                rankingListId = uri.getQueryParameter("rankingListId");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        LoveNovelRankArgsModel model = new LoveNovelRankArgsModel();

        if (rankingListId == null || rankingListId.isEmpty()) {
            rankingListId = "-1";
        }

        model.setSelectRankingListId(Long.parseLong(rankingListId));
        NovelRankFragment rankFragment = NovelRankFragment.newInstance(model);
        mainActivity.startFragment(rankFragment);
    }

    private void handleHistoryPage(MainActivity mainActivity, PushModel pm) {
        if (!ContextUtils.checkActivity(mainActivity)) {
            return;
        }

        int location = 1;
        if (!TextUtils.isEmpty(pm.schema)) {
            try {
                Uri uri = Uri.parse(pm.schema);
                location = Integer.parseInt(uri.getQueryParameter("location"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        Bundle bundle = new Bundle();
        bundle.putInt("location", location);
        if (AppModeManager.isAppModeForNormal()) {
            //默认选中历史tab
            bundle.putInt("defaultIndex", 2);
            mainActivity.switchTingProgramTab(bundle);
        } else {
            AllHistoryFragment fragment = AllHistoryFragment.newInstance(true, false, true, location);
            mainActivity.startFragment(fragment);
        }
    }

    /**
     * 跳转到阅读器
     *
     * @param mainActivity
     * @param pm
     */
    private void handleReadPage(MainActivity mainActivity, PushModel pm) {
        if (!ContextUtils.checkActivity(mainActivity)) {
            return;
        }

        if (!TextUtils.isEmpty(pm.schema)) {
            try {
                Uri uri = Uri.parse(pm.schema);
                String bookId = uri.getQueryParameter("book_id");
                String chapterId = uri.getQueryParameter("chapter_id");
                if (!TextUtils.isEmpty(bookId)) {
                    if (!TextUtils.isEmpty(chapterId)) {
                        ReadUtils.startToReader(Long.parseLong(bookId),
                                Long.parseLong(chapterId));
                    } else {
                        ReadUtils.startToReader(Long.parseLong(bookId));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 跳转到订阅界面
     *
     * @param mainActivity activity
     * @param pm           消息类型
     */
    private void handleSubscribePage(MainActivity mainActivity, PushModel pm) {
        if (!ContextUtils.checkActivity(mainActivity)) {
            return;
        }

        int location = 1;
        if (!TextUtils.isEmpty(pm.schema)) {
            try {
                Uri uri = Uri.parse(pm.schema);
                location = Integer.parseInt(uri.getQueryParameter("location"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        Bundle bundle = new Bundle();
        bundle.putInt("location", location);

        // 经典模式  切换tab
        if (AppModeManager.isAppModeForNormal()) {
            //默认选中我听的订阅tab
            bundle.putInt("defaultIndex", 1);
            mainActivity.switchTingProgramTab(bundle);
        } else if (AppModeManager.isAppModeForTruckFriend()) {
            mainActivity.startFragment(SingletonSubscribeFragment.Companion.newInstance(bundle));
        }
    }

    private void handleAllCategory(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null) {
            return;
        }
        String source = pm.source;
        HomeAllCategoryListFragment allCategoryListFragment =
                HomeAllCategoryListFragment.newInstance(source);
        mainActivity.startFragment(allCategoryListFragment);
    }

    private void handleNewHomePage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null) {
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstants.KEY_PAGE_ID, pm.pageId);
        bundle.putInt(BundleKeyConstants.KEY_TAB_ID, pm.tabId);
        mainActivity.switchHomeTab(bundle);
        mainActivity.homePageHandleIting();
    }

    private void handleMyDownload(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null) {
            return;
        }
        mainActivity.startFragment(new DownloadedAlbumListFragment());
    }

    private void handlePlayHistory(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null) {
            return;
        }
        try {
            IMainFragmentAction fragmentAction = Router.getMainActionRouter().getFragmentAction();
            BaseFragment historyFragment = fragmentAction.newPlayHistoryFragment();
            mainActivity.startFragment(historyFragment);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 退出登录
     */
    private void handleLoginOut(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null) {
            return;
        }
        //未登录不做处理
        if (!UserInfoMannage.hasLogined()) {
            return;
        }
        //当前activity不是MainActivity,finish掉
        Activity topActivity = BaseApplication.getTopActivity();
        if (!(topActivity instanceof MainActivity)) {
            if (topActivity != null && !topActivity.isFinishing()) {
                topActivity.finish();
            }
        }
        //已登录退出登录，并且选中我页
        UserInfoMannage.logOut(mainActivity);
        mainActivity.switchMineTab(null);
    }

    /**
     * 跳转到CM游戏中心页面
     */
    private void handlePageCMGameCenter(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null) {
            return;
        }
    }

    /**
     * 选中底部tab
     */
    private void handlePageBottomTab(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        String navigation = pm.navigation;
        switch (navigation) {
            case "home":
            case "book":
                //选中首页，听书
                mainActivity.switchHomeTab(null);
                break;
            case "podcast":
                //听节目
                mainActivity.switchTingProgramTab(null);
                break;
            case "subscribe":
                //选中订阅和下载
                mainActivity.switchTingProgramTab(null);
                break;
            case "radio":
                handleOneKeyRadioListPage(mainActivity, pm);
                break;
            case "me":
                mainActivity.switchMineTab(null);
                break;
            case "task":
                ThirdExchangeManager.getInstance().updateExchangeRelateInfo(pm);
                mainActivity.switchWelfareTab(null);
                break;
            case "live":
                mainActivity.switchWelfareTab(null);
                break;
            case "novel":
                if (AppModeManager.isAppModeForTruckFriend()) {
                    Bundle bundle = new Bundle();
                    bundle.putInt(BundleKeyConstants.KEY_PAGE_ID, pm.pageId);
                    bundle.putInt(BundleKeyConstants.KEY_TAB_ID, pm.tabId);
                    mainActivity.selectBottomTbFragmentPage(TabFragmentManager.TAB_TRUCK_MODE_SELECTED, bundle);
                    mainActivity.homePageHandleIting();
                }
                break;
            default:
                //默认选中首页
                mainActivity.switchHomeTab(null);
                break;
        }
    }

    /**
     * 乡村专题页
     */
    private void handleVillage(MainActivity activity, PushModel pm) {
        if (activity == null || pm == null) {
            return;
        }
        if (pm.fromStr.equals("urbanCulture")) {
            Log.e("urbanCulture", "handleVillage");

            MmkvCommonUtil.getInstance(activity).saveBoolean(MMKV_VILLAGE_ACCESS_FROM_URBAN_CULTURE, true);
            if (TextUtils.isEmpty(pm.utmSource)) {
                return;
            }
            MmkvCommonUtil.getInstance(activity).saveString(MMKV_VILLAGE_ACCESS_PARAM, pm.villageParams);
            MmkvCommonUtil.getInstance(activity).saveString(MMKV_VILLAGE_ACCESS_UTM_SOURCE, pm.utmSource);
            activity.switchHomeTab(null);
        }
    }

    /**
     * 跳转到喜小说界面，也叫书城
     *
     * @param activity
     * @param pm
     */
    private void handleBookCityTabFragment(MainActivity activity, PushModel pm) {
        if (activity == null || pm == null) {
            return;
        }
        LoveNovelTabFragment novelTabFragment = LoveNovelTabFragment.newInstance(pm.pageId);
        activity.startFragment(novelTabFragment);
    }

    /**
     * 跳转到短剧播放器
     *
     * @param activity
     * @param pm
     */
    private void handlePlayletPlayer(MainActivity activity, PushModel pm) {
        if (activity == null || pm == null) {
            return;
        }
        PlayletDetailFragment playletDetailFragment = PlayletDetailFragment.newInstance(pm.trackId, pm.albumId, true);
        activity.startFragment(playletDetailFragment);
    }

    /**
     * 跳转到拉新拉活界面
     *
     * @param activity 活动
     * @param pm       数据
     */
    private void handlePullNewPage(MainActivity activity, PushModel pm) {
        if (activity == null || pm == null) {
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(activity);
            return;
        }
        int switchInt = ConfigureCenter.getInstance().getInt(CConstants.Group_Ad.GROUP_NAME, CConstants.Group_Ad.ITEM_PULL_UP_VERSION_SWITCH, 2);
        if (switchInt == 2) {
            activity.startActivity(new Intent(activity, PullNewActivity.class));
        } else {
            try {
                Intent intent = new Intent(activity, ADActivity.class);
                intent.putExtra(IActivity.DELEGATE_NAME_KEY, "PullUpTaskActivity");
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("slotId", "1463024");//必须
                jsonObject.put("uid", String.valueOf(UserInfoMannage.getUid()));//必须,用户id
                String json = jsonObject.toString();
                intent.putExtra("params", json);
                activity.startActivity(intent);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 选中车友模式底部的电台页面，顶部匹配界面
     */
    private void handleTruckModeRadioTab(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putLong("radioId", pm.truckModeRadioId);
        mainActivity.switchTruckModeRadioTab(bundle);
        mainActivity.homePageHandleIting();
    }

    /**
     * 排行榜页面
     */
    private void handleRankList(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        //聚合榜
        if (PushModel.TYPE_GROUP_RANK.equals(pm.type)) {
            //使用聚合排行榜
            AggregateRankFragment aggregateRankFragment = new AggregateRankFragment();
            AggregateRankArgsModel model = new AggregateRankArgsModel();
            //selectRankClusterId和selectRankingListId配置一个即可选中对应的tab
            //跳转后，优先使用selectRankingListId进行匹配，匹配不到，使用selectRankClusterId进行匹配
            model.selectRankingListId = pm.rankingListId;
            model.selectRankClusterId = pm.clusterId;
            Bundle bundle = AggregateRankFragment.newArgument(model);
            aggregateRankFragment.setArguments(bundle);
            mainActivity.startFragment(aggregateRankFragment);
        }
    }

    /**
     * 新的排行榜详情页
     */
    private void handleNewRankList(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        //聚合榜
        //if (PushModel.TYPE_GROUP_RANK.equals(pm.type)) {
        AggregateRankArgsModel model = new AggregateRankArgsModel();
        //selectRankClusterId和selectRankingListId配置一个即可选中对应的tab
        //跳转后，优先使用selectRankingListId进行匹配，匹配不到，使用selectRankClusterId进行匹配
        model.selectRankingListId = pm.rankingListId;
        NewAggregateRankFragment aggregateRankFragment =
                NewAggregateRankFragment.newInstance(model);
        mainActivity.startFragment(aggregateRankFragment);
        //}
    }

    private void handleNewUserMustListener(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        NewUserGuideFragment fragment = NewUserGuideFragment.newInstance();
        mainActivity.startFragment(fragment);
    }

    /**
     * 动态uting跳转
     */
    private void handleUtingDtingRedirect(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        //默认的跳转地址
        String defaultUrl = pm.defaultUrl;
        //请求接口的地址
        String requestUrl = pm.url;
        FuliLogger.log("uting处理:uting=10008,requestUrl=" + requestUrl + "  defaultUrl=" + defaultUrl);
        //当前是请求操作
        if (!TextUtils.isEmpty(requestUrl) && requestUrl.startsWith("http")) {
            //http开通的发起请求
            LiteCommonRequest.requestDtingUrl(requestUrl, new IDataCallBack<ParsingDtingRsp>() {
                @Override
                public void onSuccess(@Nullable ParsingDtingRsp object) {
                    String needJumpUrl = "";
                    if (object != null && !TextUtils.isEmpty(object.url)) {
                        if (object.url.startsWith("http") || object.url.startsWith("iting") || object.url.startsWith("uting")) {
                            needJumpUrl = object.url;
                        }
                    }
                    //没有获取到正取的地址，使用默认的跳转
                    if (TextUtils.isEmpty(needJumpUrl)) {
                        needJumpUrl = defaultUrl;
                    }
                    FuliLogger.log("uting处理:uting=10008,请求成功，跳转=needJumpUrl=" + needJumpUrl);
                    //执行跳转操作
                    handleDtingJump(mainActivity, needJumpUrl);
                }

                @Override
                public void onError(int code, String message) {
                    FuliLogger.log("uting处理:uting=10008,请求失败，跳转=needJumpUrl=" + defaultUrl);
                    //请求失败，执行默认的操作，默认跳转操作不是当前的10008，执行跳转操作
                    handleDtingJump(mainActivity, defaultUrl);
                }
            });
            return;
        }
        //请求的url是错误的，执行默认的操作，默认跳转操作不是当前的10008，执行跳转操作
        FuliLogger.log("uting处理:uting=10008，请求地址错误，跳转=needJumpUrl=" + defaultUrl);
        handleDtingJump(mainActivity, defaultUrl);
    }

    private void handleDtingJump(MainActivity mainActivity, String url) {
        if (mainActivity == null) {
            return;
        }
        //请求的url是错误的，执行默认的操作，默认跳转操作不是当前的10008，执行跳转操作
        if (!TextUtils.isEmpty(url) && !url.contains("open?msg_type=10008")) {
            ToolUtil.clickUrlAction(mainActivity, url, null, null);
        }
    }

    /**
     * 打开分类热词单独页面
     */
    private void handleCategoryHotKeyPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        //跳转热词页面，待筛选的热词页面
        int categoryId = pm.categoryId;
        int keywordId = pm.keywordId;
        String keyName = pm.title;
        Bundle argumentForSub = KeywordMetadataFragment.createArgumentFromSinglePage(categoryId,
                keywordId, keyName);
        KeywordMetadataFragment fragment = new KeywordMetadataFragment();
        fragment.setArguments(argumentForSub);
        mainActivity.startFragment(fragment);
    }

    /**
     * 打开具体分类---对应的推荐页面
     */
    private void handleCategoryRecommedPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        int recommedCategoryId = pm.categoryId;
        String categoryTitle = pm.title;
        if (TextUtils.isEmpty(categoryTitle)) {
            //主app使用的是tagName字段
            categoryTitle = pm.tagName;
        }
        HomeCategoryRecommendFragment recommedFragment =
                HomeCategoryRecommendFragment.newInstance(recommedCategoryId, categoryTitle);
        mainActivity.startFragment(recommedFragment);
    }

    /**
     * 打开Vip界面
     */
    private void handleVipPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        VipTabFragment vipTabFragment = new VipTabFragment();
        Bundle bundle = VipTabFragment.newArguments(pm.tabId);
        vipTabFragment.setArguments(bundle);
        mainActivity.startFragment(vipTabFragment);
    }

    /**
     * 打开具体分类--tab页面，默认选中的是全部，通过keywordid可以选中指定的tab
     */
    private void handleCategoryContentTabPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        //跳转到分类页面
        HomeCategoryContentTabFragment tabFragment = new HomeCategoryContentTabFragment();
        int tabCategoryId = pm.categoryId;
        int tabSelectHotKeyId = pm.keywordId;
        //此处的title为热词的name
        String title = pm.title;
        Bundle argumentDef;
        if (pm.keywordId > 0) {
            //有keywordId优先使用id进行匹配tab
            argumentDef =
                    HomeCategoryContentTabFragment.createArgumentSelectByKeywordId(tabCategoryId,
                            tabSelectHotKeyId, true);
        } else {
            //为获取到id使用name进行匹配tab
            argumentDef =
                    HomeCategoryContentTabFragment.createArgumentSelectByKeyWordName(tabCategoryId, title, true);
        }
        Logger.d("ItingHandle==",
                "handleCategoryContentTabPage==" + tabCategoryId + "  " + tabSelectHotKeyId);
        tabFragment.setArguments(argumentDef);
        mainActivity.startFragment(tabFragment);
    }

    /**
     * 打开具体分类--tab页面，默认选中的是全部，通过keywordid可以选中指定的tab
     */
    private void handleNewCategoryContentTabPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        //跳转到分类页面
        NewHomeCategoryContentTabFragment tabFragment = new NewHomeCategoryContentTabFragment();
        int tabCategoryId = pm.categoryId;
        //gender=1为男频，gender=2为女频
        int gender = pm.gender;
        int metaid = -1;

        try {
            metaid = Integer.parseInt(pm.metaid);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            Logger.i(TAG, "metaid is = " + metaid + " , is not int");
        }
        Bundle argumentDef = NewHomeCategoryContentTabFragment.
                createArgumentSelectByMetaId(tabCategoryId, metaid, gender, true);
        Logger.d("ItingHandle==", "handleCategoryContentTabPage==" + tabCategoryId + "  " + metaid);
        tabFragment.setArguments(argumentDef);
        mainActivity.startFragment(tabFragment);
    }

    /**
     * 打开具体分类--tab页面，默认选中的是全部，通过keywordid可以选中指定的tab
     */
    private void handleNewSingleMetaDataPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        MetaSelectArgs args = new MetaSelectArgs();
        args.title = pm.title;
        args.metaid = pm.metaid;
        args.categoryId = pm.categoryId;
        args.isPaid = pm.isPaid;
        args.isFinished = pm.isFinished;
        args.filterCode = pm.filterCode;

        //跳转到分类页面
        NewSinglePageCategoryMetadataFragment tabFragment =
                new NewSinglePageCategoryMetadataFragment();
        Bundle bundle = NewSinglePageCategoryMetadataFragment.createArguments(args, 0, true);
        tabFragment.setArguments(bundle);
        mainActivity.startFragment(tabFragment);
    }

    private void sendToMainActivityToHandle(Activity activity) {
        if (mData != null) {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(mData);
            ToolUtil.checkIntentAndStartActivity(activity, intent);
        }
    }

    public PushModel getPushModelFromUri(Uri data) {
        return ItingManager.getPushModelFromUri(data, "");
    }

    private boolean toWebComponent(Activity activity, String url) {
        if (activity != null && !(activity instanceof MainActivity)) {
            boolean result = false;
            if (!TextUtils.isEmpty(url)) {
                Uri uri = Uri.parse(url);
                String host = uri == null ? "" : uri.getHost();
                if (uri != null && !TextUtils.isEmpty(host) && host.contains("component.xm")) {
                    String queryString = uri.getQuery();
                    ComponentSecuritySignUtils.getInstance().randCount();
                    final int currentCount = ComponentSecuritySignUtils.getInstance().getCount();
                    if (!TextUtils.isEmpty(queryString)) {
                        if (url.endsWith("&")) {
                            url += "__signcheck=" + currentCount;
                        } else {
                            url += "&__signcheck=" + currentCount;
                        }
                    } else {
                        if (url.endsWith("?")) {
                            url += "__signcheck=" + currentCount;
                        } else {
                            url += "?__signcheck=" + currentCount;
                        }
                    }
                    mData = Uri.parse(url);
                    result = true;
                }
            }
            if (result) {
                sendToMainActivityToHandle(activity);
                return true;
            }

            return false;
        }
        if (!TextUtils.isEmpty(url)) {
            Uri uri = Uri.parse(url);
            if (activity != null && uri != null && !uri.isOpaque()) {
                String host = uri.getHost();
                if ("component.xm".equals(host)) {
                    MainActivity mainActivity;
                    if (activity instanceof MainActivity) {
                        mainActivity = (MainActivity) activity;
                        try {
                            Intent intent = new Intent();
                            intent.setData(uri);
                            mainActivity.startFragment(Router.getHybridViewActionRouter().getFragmentAction()
                                    .newHybridViewFragment(intent));
                            return true;
                        } catch (Exception e) {
                            e.printStackTrace();
                            return false;
                        }
                    }
                } else if ("component.xm.debug".equals(host)) {
                    try {
                        String compId = uri.getQueryParameter("compId");
                        String downloadUrl = uri.getQueryParameter("downloadUrl");
                        Router.getHybridViewActionRouter().getFunctionAction().toDebugComponent(compId, downloadUrl);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 打开播放页
     */
    private void handleSoundPage(final MainActivity mainActivity, final PushModel pm) {
        if (mainActivity == null || pm == null || pm.trackId <= 0) {
            return;
        }
        //车友模式
        if (AppModeManager.isAppModeForTruckFriend()) {
            //需要先选中播放页面
            Map<String, String> params = new HashMap<>();
            params.put("device", "android");
            params.put("trackId", pm.trackId + "");
            String addString = "/" + pm.trackId;
            params.put("scale", "1");
            params.put("version", DeviceUtil.getVersion(BaseApplication.getMyApplicationContext()));
            params.put("device", "android");
            params.put("network", CommonRequestM.getInstanse().getNetWorkType());
            params.put("operator",
                    NetworkType.getOperator(BaseApplication.getMyApplicationContext()) + "");
            params.put("deviceId",
                    DeviceUtil.getDeviceToken(BaseApplication.getMyApplicationContext()));
            params.put("appid", "0");
            if (UserInfoMannage.hasLogined()) {
                params.put("uid", UserInfoMannage.getUid() + "");
            }
            LiteCommonRequest.getPlayPageInfoNewForTruck(params,
                    new IDataCallBack<TruckPlayPageInfo>() {
                        @Override
                        public void onSuccess(TruckPlayPageInfo playPageInfo) {
                            //插入上次最后播放的声音
                            if (playPageInfo != null && playPageInfo.trackM != null && playPageInfo.albumM != null && playPageInfo.trackM.canPlayTrackForMainProcess()) {

                                //上次播放的声音和即将播放的声音不是同一专辑，不进行替换

                                String randomUUID = UUID.randomUUID().toString();
                                TruckRecommendModel recommendModel = new TruckRecommendModel();

                                //添加唯一id，进行定位使用
                                recommendModel.setLocalModelUuid(randomUUID);
                                playPageInfo.albumM.setLocalModelUuid(randomUUID);
                                playPageInfo.trackM.setLocalModelUuid(randomUUID);
                                if (pm.albumId > 0) {
                                    playPageInfo.trackM.setLocalPlayerSource(Track.LOCAL_PLAYER_SOURCE_TRUCK_DIANTAI_FEED_ALBUM);
                                } else {
                                    playPageInfo.trackM.setLocalPlayerSource(Track.LOCAL_PLAYER_SOURCE_TRUCK_DIANTAI_FEED_TRACK);
                                }
                                //设置插入来来源位uting
                                playPageInfo.trackM.setSecondPlaySource(ConstantsOpenSdk.PLAY_FROM_UTING);

                                List<TruckRecommendTrackM> topList = new ArrayList<>();
                                topList.add(playPageInfo.trackM);
                                playPageInfo.albumM.setTopTracks(topList);
                                recommendModel.setLocalModelUuid(randomUUID);

                                recommendModel.setItem(playPageInfo.albumM);
                                if (pm.albumId > 0) {
                                    recommendModel.setItemType(TruckRecommendModel.RECOMMEND_ITEM_ALBUM);
                                } else {
                                    recommendModel.setItemType(TruckRecommendModel.RECOMMEND_ITEM_TRACK);
                                }

                                mainActivity.selectBottomTbFragmentPage(TabFragmentManager.TAB_TRUCK_MODE_DIANTAI, null);
                                HandlerManager.postOnUIThreadDelay(new Runnable() {
                                    @Override
                                    public void run() {
                                        Fragment currFragment = null;
                                        TruckFriendModeActivity truckFriendModeActivity =
                                                mainActivity.getTruckFriendModeActivity();
                                        if (truckFriendModeActivity != null && truckFriendModeActivity.getTabFragmentManager() != null) {
                                            currFragment =
                                                    truckFriendModeActivity.getTabFragmentManager().getCurrFragment();
                                        }
                                        if (!(currFragment instanceof TruckHomeFragment)) {
                                            return;
                                        }
                                        TruckHomeFragment homeFragment =
                                                (TruckHomeFragment) currFragment;
                                        homeFragment.dealInsterUtingInsertPage(recommendModel);
                                    }
                                }, 200);
                            }
                        }

                        @Override
                        public void onError(int code, String message) {

                        }
                    }, addString);
        } else {
            handleSoundPageFornNrmal(mainActivity, pm);
        }
    }

    /**
     * 打开播放页
     */
    private void handleSoundPageFornNrmal(final MainActivity mainActivity, final PushModel pm) {
        if (mainActivity == null || pm == null || pm.trackId <= 0) {
            return;
        }
        final Track sound = new Track();
        sound.setDataId(pm.trackId);
        sound.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PUSH);
        if (XmPlayerManager.getInstance(mainActivity).isConnected()) {
            if (pm.albumId > 0) {
                PlayTools.playTrackHistoy(mainActivity, sound.getDataId(), pm.albumId, null,
                        ConstantsOpenSdk.PLAY_FROM_PUSH, false);
            } else {
                PlayTools.playTrackByCommonList(mainActivity, pm.trackId,
                        ConstantsOpenSdk.PLAY_FROM_PUSH, null, true);
            }
        } else {
            mLastHandleTrackTime = System.currentTimeMillis();
            XmPlayerManager.getInstance(mainActivity).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                @Override
                public void onConnected() {
                    XmPlayerManager.getInstance(mainActivity).removeOnConnectedListerner(this);
                    //超过30s放弃以前的操作
                    if (System.currentTimeMillis() - mLastHandleTrackTime > 30_000) {
                        return;
                    }
                    if (mainActivity == null || sound == null || pm == null) {
                        return;
                    }
                    if (pm.albumId > 0) {
                        PlayTools.playTrackHistoy(mainActivity, sound.getDataId(), pm.albumId,
                                null, ConstantsOpenSdk.PLAY_FROM_PUSH, false);
                    } else {
                        PlayTools.playTrackByCommonList(mainActivity, pm.trackId,
                                ConstantsOpenSdk.PLAY_FROM_PUSH, null, true);
                    }
                }
            });
        }
    }

    private void handleOpenPlayPageAndEmpty(final MainActivity mainActivity, final PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        //打开播放页
        //当前播放的是直播，并且开启了未成年保护模式，弹出弹框提醒
        //fix uting会进行播放问
        PlayableModel currSound = XmPlayerManager.getInstance(mainActivity).getCurrSound();
        if (PlayTools.isLiveMode(currSound)) {
            //当前是直播，儿童保护模式开启
            //儿童保护模式未开启,打开播放的时候跳转到排行榜
            String def = AppConstants.DEFAULT_PLAY_LIST_EMPTY_TO_RANK;
            try {
                Router.getMainActionRouter().getFunctionAction().handleIting(mainActivity,
                        Uri.parse(def));
            } catch (Exception e) {
                CustomToast.showToast("暂无播放内容");
            }
            return;
        }

        final Track sound = new Track();
        sound.setDataId(pm.trackId);
        sound.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PUSH);
        if (XmPlayerManager.getInstance(mainActivity).isConnected()) {
            openPlayOrGoToRankPage(mainActivity, pm);
        } else {
            mLastHandleTrackTime = System.currentTimeMillis();
            XmPlayerManager.getInstance(mainActivity).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                @Override
                public void onConnected() {
                    XmPlayerManager.getInstance(mainActivity).removeOnConnectedListerner(this);
                    //超过30s放弃以前的操作
                    if (System.currentTimeMillis() - mLastHandleTrackTime > 30_000) {
                        return;
                    }
                    openPlayOrGoToRankPage(mainActivity, pm);
                }
            });
        }
    }

    /**
     * 打开播放页，并播放，播放列表为空，跳转到排行榜
     */
    private void openPlayOrGoToRankPage(final MainActivity mainActivity, final PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        XmPlayerManager xmPlayerManager = XmPlayerManager.getInstance(mainActivity);
        if (xmPlayerManager.getPlayListSize() == 0) {
            String def = AppConstants.DEFAULT_PLAY_LIST_EMPTY_TO_RANK;
            try {
                Router.getMainActionRouter().getFunctionAction().handleIting(mainActivity,
                        Uri.parse(def));
            } catch (Exception e) {
                CustomToast.showToast("暂无播放内容");
            }
        } else {
            Bundle bundle = null;
            if (AppConstants.UTING_ACTION_GO_COIN_TAB.equals(pm.action)) {
                bundle = new Bundle();
                bundle.putBoolean(BundleKeyConstants.KEY_LOCATE_TO_EARN_REWARD_PAGE, true);
            }
            //如果是直播的并且是儿童保护模式不打开播放页
            if (PlayTools.isLiveMode(xmPlayerManager.getCurrSound()) && ChildProtectManager.isChildProtectOpen(mainActivity)) {
                ChildProtectDialogHintManager.showChildProtectForbidPlayDialogForLive();
                return;
            }
            mainActivity.showPlayFragment(null, 0, bundle, PlayerManager.PLAY_CURRENT);
            if (!xmPlayerManager.isPlaying()) {
                xmPlayerManager.play();
            }
        }
    }

    /**
     * 未登录打开登录页面
     */
    private void handleLoginPage(MainActivity mainActivity, PushModel pm) {
        if (UserInfoMannage.hasLogined()) {
            return;
        }
        if (mainActivity == null) {
            return;
        }
        int loginBy = LoginByConstants.LOGIN_BY_DEFUALT;
        String title = "";
        if (pm != null) {
            if ("1".equals(pm.type)) {
                loginBy = LoginByConstants.LOGIN_BY_FULL_SCREEN;
            }
            title = pm.title;
        }
        Bundle loginParams = new Bundle();
        if (!TextUtils.isEmpty(title)) {
            //设置登录标题
            LoginBundleParamsManager.setLoginTitle(loginParams, title);
        }
        UserInfoMannage.gotoLogin(mainActivity, loginBy, loginParams);
    }

    /**
     * 打开h5页面
     */
    private void handeH5Page(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        BaseFragment web = NativeHybridFragment.newInstance(pm.url, true);
        mainActivity.startFragment(web);
    }

    /**
     * 打开电台列表页
     */
    private void handleOneKeyRadioListPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        OneKeyRadioFragment oneKeyRadioFragment = OneKeyRadioFragment.newInstance();
        mainActivity.startFragment(oneKeyRadioFragment);
    }

    /**
     * 跳转到一键听播放页面
     */
    private void handleOneKeyPlayPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        //type = TYPE_RADIO_NONE; // 0 一键听 1 头条
        int channelType;
        if ("0".equals(pm.type)) {
            channelType = OneKeyRadioModel.TYPE_OTHER_RADIO;
        } else if ("1".equals(pm.type)) {
            channelType = OneKeyRadioModel.TYPE_HEADLINE_RADIO;
        } else {
            channelType = OneKeyRadioModel.TYPE_RADIO_NONE;
        }
        long channelId = pm.channelId;
        OneKeyRadioModel model = new OneKeyRadioModel();
        model.setId(channelId);
        model.setType(channelType);
        //进入播放页通过channelId来定位，radioId必现设置为0，模拟从肚脐眼进入
        model.setRadioId(0);
        OneKeyRadioPlayFragment oneKeyRadioPlayFragment =
                OneKeyRadioPlayFragment.newInstance(model);
        mainActivity.startFragment(oneKeyRadioPlayFragment);
    }

    /**
     * 跳转搜索页面
     */
    private void handleSearchPage(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        ISearchFragmentActionRouter actionRouter =
                SearchActionRouter.getInstance().getFragmentAction();
        if (actionRouter != null) {
            BaseFragment baseFragment =
                    actionRouter.newSearchFragmentByWordAndSearchNow(pm.searchWord);
            mainActivity.startFragment(baseFragment);
        } else {
            CustomToast.showFailToast("搜索模块加载失败，请联系客服");
        }
    }

    /**
     * 跳转到专辑详情页面
     */
    private void handleAlbumPlayPage(MainActivity mainActivity, PushModel pm) {
        if (pm == null || mainActivity == null) {
            return;
        }
        try {
            AlbumEventManage.AlbumFragmentOption option =
                    new AlbumEventManage.AlbumFragmentOption();
            option.isAutoPlay = pm.autoPlay;
            int from = (pm.from > 0) ? pm.from : AlbumEventManage.FROM_OUTER_LINK;
            int playSource = (pm.playSource > 0) ? pm.playSource : ConstantsOpenSdk.PLAY_FROM_OTHER;
            BaseFragment newFragment = getMainActionRouter().getFragmentAction().newAlbumFragment(
                    "",
                    pm.albumId,
                    from,
                    playSource,
                    null,
                    null,
                    -1,
                    option);
            String tag = newFragment.getClass().getSimpleName() + pm.albumId;
            // 如果已存在该tag的fragment，移除掉上面的其他的fragment
            Fragment fragment = mainActivity.getManageFragment().removeTopAndReturnTagFragment(tag);
            if (fragment != null) {
                CustomTipsView.onStartFragment(); //防止切换页面后tips突然显示出来
                mainActivity.hidePlayFragment(fragment);
            } else {
                mainActivity.startFragment(newFragment, tag, 0, 0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 声音内容池列表页
     */
    private void handleTrackContentPoolListFragment(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        TrackContentListFragment fragment = TrackContentListFragment.newInstance(pm.title, pm.poolId);
        mainActivity.startFragment(fragment);
    }

    /**
     * 内容池列表页
     */
    private void handleContentPoolListFragment(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        //from 对应 BundleValueConstantsInMain的FROM_NEW_HOME_RECOMMEND
        NewContentPoolListFragment contentPoolListFragment =
                NewContentPoolListFragment.newInstance(pm.title, pm.poolId, 4);
        mainActivity.startFragment(contentPoolListFragment);
    }

    private void handleSingleLiteHomeNormalFragment(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        LiteHomeNormalFragment singlePageHomeNormalFragment =
                LiteHomeNormalFragment.newInstance(pm.pageId, true, pm.title);
        mainActivity.startFragment(singlePageHomeNormalFragment);
    }

    /**
     * 打开新人极速听界面
     *
     * @param mainActivity
     * @param pm
     */
    private void handleNewListen(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        QuickListenModel model = new QuickListenModel();

        NewListenPlayingInfo savedPlayingInfo =
                NewListenTrackIdManager.getSavedNewUserListenPlayingInfo();

        if (savedPlayingInfo != null
                && savedPlayingInfo.poolId != 0
                && savedPlayingInfo.track != null
                && savedPlayingInfo.track.getDataId() != 0) {
            //跳转到新人极速听界面
            model.setPoolId(savedPlayingInfo.poolId);
            model.setWillPlayTrackId(savedPlayingInfo.track.getDataId());
        }

        //model.setPoolId(pm.poolId);

        NewListenPlayFragment singlePageHomeNormalFragment =
                NewListenPlayFragment.newInstance(model);
        mainActivity.startFragment(singlePageHomeNormalFragment);
    }

    private void handleSetting(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null) {
            return;
        }
        mainActivity.startFragment(new SettingFragment());
    }

    /**
     * 打开只有 综合排序，最热，最近播放的筛选页
     *
     * @param mainActivity
     * @param pm
     */
    private void handleOnlyFilter(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null) {
            return;
        }
        //跳转到分类页面
        PlayPageTagCategoryMetadataFragment tabFragment = new PlayPageTagCategoryMetadataFragment();
        int type = 2;

        try {
            int tagId = Integer.parseInt(pm.metaid);
            Bundle bundle = PlayPageTagCategoryMetadataFragment.createArguments(pm.title, type,
                    tagId, tagId);
            tabFragment.setArguments(bundle);
            mainActivity.startFragment(tabFragment);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }

    /**
     * bugfix:在handleITing中closeWebFragment操作会造成在app内部h5页面，执行iting操作如果是选中底部tab，出现页面透明并且重叠问题。
     * <p>
     * NOTE:需要处理的iting是需要切换选中底部导航栏，首页，我听，发现，我的等页面
     * 返回true 如果当前页面是h5页面，当前web页将不会进行关闭，如果要关闭，可以在对应iting处理的地方使用clearAllFragmentFromStacks
     */
    private boolean isNeedForbidCloseWebFragment(int messageType) {
        switch (messageType) {
            case AppConstants.PAGE_TO_BOTTOM_TAB:
                return true;
            default:
                break;
        }
        return false;
    }


    private void handleRechargePage(MainActivity mainActivity, PushModel pm) {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mainActivity);
            return;
        }
        try {
            if (pm.productType == 1) {
                //喜钻充值
                mainActivity.startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newRechargeDiamondFragment(Configure.RechargeFragmentFid.DIAMOND_RECHARGE, pm.amount));
            } else {
                //喜点充值
                mainActivity.startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newRechargeFragment(PayManager.TO_RECHARGE_CHOOSE, pm.amount));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 打开直播首页
     */
    private void handleOpenLiveHomePage(MainActivity mainActivity, PushModel pm) {
        if (null == mainActivity || null == pm) {
            return;
        }

        try {
            Fragment fragment =
                    Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFragmentAction().newLiveAudioFragment(true);
            Bundle bundle = fragment.getArguments();
            bundle.putInt(BundleKeyConstants.KEY_PLAY_SOURCE, pm.playSource);
            bundle.putInt(BundleKeyConstants.KEY_LIVE_HOME_PAGE_SELECTED_CATEGORY_ID, pm.segmentId);
            mainActivity.startFragment(fragment);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handStartPiaScriptDetailPage(final MainActivity mainActivity, final int dramaId) {
        try {
            Logger.i(TAG, "handStartPiaScriptDetailPage, start, scriptId = " + dramaId
                    + ", isBundleDevMode = " + ConstantsOpenSdk.isBundleDevelopMode);

            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    Logger.i(TAG,
                            "handStartEntHallRoomFragment, onInstallSuccess, bundleName = " + bundleModel.bundleName);

                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        BaseFragment2 piaScriptDetailFragment = null;
                        try {
                            piaScriptDetailFragment = Router.<LiveActionRouter>getActionRouter(
                                    Configure.BUNDLE_LIVE
                            ).getFragmentAction().newPiaScriptDetailFragment(dramaId);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (piaScriptDetailFragment != null) {
                            mainActivity.startFragment(piaScriptDetailFragment);
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    Logger.i(TAG,
                            "handStartEntHallRoomFragment, onInstallError, bundleName = " + bundleModel.bundleName);

                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        CustomToast.showFailToast("直播模块安装失败，请稍后重试");
                    }
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();

            Logger.i(TAG, "handStartPiaScriptDetailPage, Exception, bundleName = live, errorMsg ="
                    + " " + e.getMessage());
        }
    }

    private void handStartPiaAuthorDetailPage(final MainActivity mainActivity, final long authorUid) {
        try {
            Logger.i(TAG, "handStartPiaAuthorDetailPage, start, authorUid = " + authorUid
                    + ", isBundleDevMode = " + ConstantsOpenSdk.isBundleDevelopMode);

            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    Logger.i(TAG,
                            "handStartPiaAuthorDetailPage, onInstallSuccess, bundleName = " + bundleModel.bundleName);

                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        BaseFragment2 piaScriptDetailFragment = null;
                        try {
                            piaScriptDetailFragment = Router.<LiveActionRouter>getActionRouter(
                                    Configure.BUNDLE_LIVE
                            ).getFragmentAction().newPiaAuthorDetailFragment(authorUid);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (piaScriptDetailFragment != null) {
                            mainActivity.startFragment(piaScriptDetailFragment);
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    Logger.i(TAG, "handStartPiaAuthorDetailPage, onInstallError, bundleName = "
                            + bundleModel.bundleName);

                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {
                        CustomToast.showFailToast("直播模块安装失败，请稍后重试");
                    }
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();

            Logger.i(TAG, "handStartPiaAuthorDetailPage, Exception, bundleName = live, errorMsg ="
                    + " " + e.getMessage());
        }
    }

    private void handStartLiveTabAudioFragment(final MainActivity mainActivity, final PushModel pm) {
        try {
            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        BaseFragment liveAudioFragment = null;
                        try {
                            liveAudioFragment =
                                    Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFragmentAction().newLiveCategoryViewPagerFragmentWithPlaySource(pm.playSource, pm.segmentId);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (liveAudioFragment != null) {
                            mainActivity.startFragment(liveAudioFragment);
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void handStartEntHomeFragment(final MainActivity mainActivity, PushModel model) {
        try {
            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        BaseFragment entHomeFragment = null;
                        try {
                            int tabId = model.selectedTabId;
                            int showPopup = model.showPopup;
                            entHomeFragment = Router.<LiveActionRouter>getActionRouter(
                                    Configure.BUNDLE_LIVE
                            ).getFragmentAction().newInteractiveSquareRoom(tabId, showPopup);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (entHomeFragment != null) {
                            mainActivity.startFragment(entHomeFragment);
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        CustomToast.showFailToast("直播模块安装失败，请稍后重试");
                    }
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handStartEntHallRoomFragment(final MainActivity mainActivity, final long roomId, int playSource, boolean showBack) {
        try {
            Logger.i(TAG, "handStartEntHallRoomFragment, start, roomId = " + roomId
                    + ", isBundleDevMode = " + ConstantsOpenSdk.isBundleDevelopMode);
            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    Logger.i(TAG,
                            "handStartEntHallRoomFragment, onInstallSuccess, bundleName = " + bundleModel.bundleName);

                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        if (mainActivity != null) {
                            PlayTools.playEntHallWithPlaySource(mainActivity, roomId, playSource, showBack);
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    Logger.i(TAG,
                            "handStartEntHallRoomFragment, onInstallError, bundleName = " + bundleModel.bundleName);

                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        CustomToast.showFailToast("直播模块安装失败，请稍后重试");
                    }
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();

            Logger.i(TAG, "handStartEntHallRoomFragment, Exception, bundleName = live, errorMsg ="
                    + " " + e.getMessage());
        }
    }

    private void handStartKtvHomeItemFragment(final MainActivity mainActivity) {
        try {
            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        BaseFragment ktvHomeFragment = null;
                        try {
                            // 跳转到派对广场 KTV tab 页
                            ktvHomeFragment = Router.<LiveActionRouter>getActionRouter(
                                    Configure.BUNDLE_LIVE
                            ).getFragmentAction().newInteractiveSquareRoom(2, 0);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (ktvHomeFragment != null) {
                            mainActivity.startFragment(ktvHomeFragment);
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    CustomToast.showFailToast("直播模块安装失败，请稍后重试");

                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void handStartKtvRoomFragment(final MainActivity mainActivity, final long roomId) {
        try {
            Logger.i(TAG, "handStartKtvRoomFragment, start, roomId = " + roomId
                    + ", isBundleDevMode = " + ConstantsOpenSdk.isBundleDevelopMode);
            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    Logger.i(TAG,
                            "handStartKtvRoomFragment, onInstallSuccess, bundleName = " + bundleModel.bundleName);

                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                        if (mainActivity != null) {
                            PlayTools.playKtvRoomByRoomId(mainActivity, roomId);
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    CustomToast.showFailToast("直播模块安装失败，请稍后重试");
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();

            Logger.i(TAG, "handStartKtvRoomFragment, Exception, bundleName = live, errorMsg ="
                    + " " + e.getMessage());
        }
    }

    private void handStartUGCRoomFragment(final MainActivity mainActivity, final PushModel model) {
        if (model == null) {
            return;
        }
        try {
            // 支持跳转 UGC(6)、PGC(1) 房间
            Logger.i(TAG, "handStartUGCRoomFragment, start, roomId = " + model.roomId
                    + ", isBundleDevMode = " + ConstantsOpenSdk.isBundleDevelopMode);
            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    Logger.i(TAG,
                            "handStartUGCRoomFragment, onInstallSuccess, bundleName = " + bundleModel.bundleName);

                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {
                        Bundle bundle = new Bundle();
                        bundle.putLong(ILiveFunctionAction.KEY_ROOM_ID, model.roomId);
                        bundle.putInt(ILiveFunctionAction.KEY_OPEN_TYPE, model.open_type);
                        bundle.putInt(ILiveFunctionAction.KEY_PLAY_SOURCE, model.playSource);
                        if (mainActivity != null) {
                            if (model.roomMode == 6) {
                                PlayTools.playUGCRoom(mainActivity, bundle);
                            } else {
                                PlayTools.playEntHallRoom(mainActivity, bundle);
                            }
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    CustomToast.showFailToast("直播模块安装失败，请稍后重试");
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();

            Logger.i(TAG, "handStartUGCRoomFragment, Exception, bundleName = live, errorMsg ="
                    + " " + e.getMessage());
        }
    }

    //打开视频直播页面
    private void startVideoLiveFragmentPage(MainActivity mainActivity, PushModel model) {

        try {
            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {

                    if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {


                        Router.getActionByCallback(Configure.BUNDLE_VIDEO, new Router.IBundleInstallCallback() {
                            @Override
                            public void onInstallSuccess(BundleModel bundleModel) {
                                gotoVideoLiveRoomPage(mainActivity, model.liveId, model.videoLiveAlbumId, model.playSource);
                            }

                            @Override
                            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                                gotoVideoLiveRoomPage(mainActivity, model.liveId, model.videoLiveAlbumId, model.playSource);
                            }

                            @Override
                            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                                gotoVideoLiveRoomPage(mainActivity, model.liveId, model.videoLiveAlbumId, model.playSource);
                            }
                        });


                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    CustomToast.showFailToast("直播模块安装失败，请稍后重试");

                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });

        } catch (Exception e) {
            e.printStackTrace();

            Logger.i(TAG, "handStartKtvRoomFragment, Exception, bundleName = live, errorMsg ="
                    + " " + e.getMessage());
        }

    }


    private void handleRecommendLive(MainActivity mainActivity, PushModel pm) {
        if (null == mainActivity || null == pm) {
            return;
        }

        Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (Configure.liveBundleModel.bundleName.equals(bundleModel.bundleName)) {

                    try {
                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFragmentAction()
                                .startRecommendLive(mainActivity, pm.recSrc,
                                        pm.msgId, pm.playSource);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                CustomToast.showFailToast("直播模块安装失败，请稍后重试");
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }

    private void gotoVideoLiveRoomPage(MainActivity act, long liveId, long albumId, int playSource) {

        try {
            ILiveFunctionAction action = Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction();
            action.startLiveRoom(new StartRoomIntent()
                    .setActivity(act)
                    .setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE)
                    .setAlbumId(albumId)
                    .setPlaySource(playSource)
                    .setLiveId(liveId));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 将iting收敛至直播LiveBundle中
     *
     * @param pm       推送消息
     * @param activity 上下文环境
     */
    private void gotoLiveBundleAsync(final PushModel pm, final MainActivity activity) {
        if (pm == null || activity == null) {
            return;
        }

        if (pm.isPush) {
//            PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("2", "before install live bundle");
        }
        Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (TextUtils.equals(Configure.liveBundleModel.bundleName, bundleModel.bundleName)) {
                    if (pm.isPush) {
//                        PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("3", "after install live bundle");
                    }
                    try {
                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE)
                                .getFunctionAction().handleITing(pm, activity);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                CustomToast.showFailToast("直播模块安装失败，请稍后重试");
                if (pm.isPush) {
//                    PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("Live bundle LocalInstallError");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
            }
        }, true, BundleModel.DOWNLOAD_IN_BACKGROUND);
    }
}
