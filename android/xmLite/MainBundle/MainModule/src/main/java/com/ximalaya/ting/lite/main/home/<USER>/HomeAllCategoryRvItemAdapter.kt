package com.ximalaya.ting.lite.main.home.adapter

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.TypedValue
import android.view.View
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder
import com.ximalaya.ting.lite.main.model.album.HomeAllCategoryListItemModel

/**
 * Created by qinhuifeng on 2021/1/11
 *
 * <AUTHOR>
 */
class HomeAllCategoryRvItemAdapter(val baseFragment2: BaseFragment2, dataList: List<HomeAllCategoryListItemModel>) : MultiRecyclerAdapter<HomeAllCategoryListItemModel, SuperRecyclerHolder>(baseFragment2.context, dataList) {

    private val bgGradientDrawable: GradientDrawable = GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, intArrayOf(Color.parseColor("#FFF6F7F8"), Color.parseColor("#FFF6F7F8")))
    private val itemWith: Int
    private val itemHeight: Int
    private var textSizePx: Float

    init {
        bgGradientDrawable.shape = GradientDrawable.RECTANGLE;
        bgGradientDrawable.gradientType = GradientDrawable.LINEAR_GRADIENT;
        val dp2px: Float = BaseUtil.dp2px(baseFragment2.context, 8f).toFloat()
        bgGradientDrawable.cornerRadii = floatArrayOf(dp2px, dp2px, dp2px, dp2px, dp2px, dp2px, dp2px, dp2px)
        itemWith = (BaseUtil.getScreenWidth(baseFragment2.context) - BaseUtil.dp2px(baseFragment2.context, 48f)) / 4;
        itemHeight = (itemWith * 42.0f / 82.0f).toInt()

        //默认字号，15dp
        textSizePx = BaseUtil.sp2px(baseFragment2.context, 15f).toFloat();
        val maxTextSizePx = BaseUtil.dp2px(baseFragment2.context, 19f)
        //适配大字模式，最大字体不能超过19dp
        if (textSizePx > maxTextSizePx) {
            textSizePx = maxTextSizePx.toFloat();
        }
    }

    override fun createMultiViewHolder(mCtx: Context?, itemView: View, viewType: Int): SuperRecyclerHolder {
        return SuperRecyclerHolder.createViewHolder(mCtx, itemView)
    }

    override fun onBindMultiViewHolder(holder: SuperRecyclerHolder?, model: HomeAllCategoryListItemModel?, viewType: Int, position: Int) {
        if (holder == null) {
            return
        }
        //设置渐变背景
        val itemBg: View = holder.getViewById(R.id.main_view_bg)
        itemBg.background = bgGradientDrawable

        //设置item的宽度和高度
        val layoutParams = itemBg.layoutParams
        layoutParams.width = itemWith
        layoutParams.height = itemHeight
        itemBg.layoutParams = layoutParams

        //设置标题
        holder.setText(R.id.main_tv_title, model?.displayName)

        //适配大字模式
        holder.setTextSize(R.id.main_tv_title, TypedValue.COMPLEX_UNIT_PX, textSizePx)

        //设置点击跳转
        holder.setOnItemClickListenner(object : View.OnClickListener {
            override fun onClick(v: View?) {
                model?.uting?.let { ToolUtil.clickUrlAction(baseFragment2, it, v) }
            }
        })
    }

    override fun getMultiItemViewType(model: HomeAllCategoryListItemModel?, position: Int): Int {
        return 0
    }

    override fun getMultiItemLayoutId(viewType: Int): Int {
        return R.layout.main_item_home_all_category_rv_item
    }
}