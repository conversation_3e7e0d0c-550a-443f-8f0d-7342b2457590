package com.ximalaya.ting.lite.main.comment.view

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RatingBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import kotlin.math.abs


/**
 *  @Author: Junxiang Cheng
 *  @Mail: <EMAIL>
 *  @CreateTime: 1/10/22
 *
 *  @Description:
 */
class CommentItemView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val clRoot: ConstraintLayout

    val ivAvatar: RoundImageView
    val tvNickname: TextView
    val tvCommentTime: TextView
    val ivUserVip: ImageView
    val ivCommentLike: XmLottieAnimationView
    val tvLikeCount: TextView
    val clLikeHotArea: ConstraintLayout
    val llContentContainer: LinearLayout
    val tvCommentContent: TextView
    val tvCommentExpand: TextView
    val tvIpRegion: TextView

    val llReplyContainer: LinearLayout
    val tvReplyContent: TextView
    val tvReplayCount: TextView
    val ivRatingbar: RatingBar

    companion object {
        const val TYPE_COMMENT_LIST_ITEM = "TYPE_COMMENT_LIST_ITEM"
        const val TYPE_REPLY_LIST_ITEM = "TYPE_REPLY_LIST_ITEM"
//        const val TYPE_REPLY_LIST_HEADER_ITEM = "TYPE_REPLY_LIST_HEADER_ITEM"
    }

    private var mType = TYPE_COMMENT_LIST_ITEM
    var mTouchSlop = 0
    var mActivity: Activity
    var likeCount: Long = 0

    init {
        View.inflate(context, R.layout.main_layout_comment_list_item, this)

        clRoot = findViewById(R.id.main_cl_container)

        ivAvatar = findViewById(R.id.main_iv_avatar)
        tvNickname = findViewById(R.id.main_tv_nickname)
        tvCommentTime = findViewById(R.id.main_tv_comment_time)
        ivUserVip = findViewById(R.id.main_iv_user_vip)
        ivCommentLike = findViewById(R.id.main_iv_comment_like)
        tvLikeCount = findViewById(R.id.main_tv_like_count)
        clLikeHotArea = findViewById(R.id.main_cl_comment_like_hot_area)
        llContentContainer = findViewById(R.id.main_ll_content_container)
        tvCommentContent = findViewById(R.id.main_tv_comment_content)
        tvCommentExpand = findViewById(R.id.main_tv_content_expand)

        llReplyContainer = findViewById(R.id.main_ll_reply_container)
        tvReplyContent = findViewById(R.id.main_tv_comment_reply_content)
        tvReplayCount = findViewById(R.id.main_tv_comment_reply_count)

        ivRatingbar = findViewById(R.id.main_comment_ratingbar)
        tvIpRegion = findViewById(R.id.main_iv_user_ip_region)

        mTouchSlop = ViewConfiguration.get(context).scaledTouchSlop
        mActivity = context as Activity
    }

    fun setType(type: String) {
        if (type != TYPE_COMMENT_LIST_ITEM
            && type != TYPE_REPLY_LIST_ITEM
        ) {
            return
        }
        mType = type
        when (type) {
            TYPE_COMMENT_LIST_ITEM -> {
                llReplyContainer.visibility = VISIBLE
            }
            TYPE_REPLY_LIST_ITEM -> {
                llReplyContainer.visibility = GONE
            }
        }
    }

    fun setShowLike(showLike: Boolean) {
        if (showLike) {
            clLikeHotArea.visibility = VISIBLE
        } else {
            clLikeHotArea.visibility = GONE
        }
    }


    private var longClickListener: LongClickListener? = null
    private var isLongClick = true //是否是长点击事件
    private var isRelease = true //是否已经释放
    private val LONG_CLICK_TIME = 600L

    var downX = 0
    var downY = 0

    //自定义长点击事件接口
    interface LongClickListener {
        fun onLongClick(view: View): Boolean
    }

    fun setLongClickListener(l: LongClickListener?) {
        longClickListener = l
    }

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                downX = event.x.toInt()
                downY = event.y.toInt()
                isRelease = false
                isLongClick = false
                //延迟LONG_CLICK_TIME毫秒的时间，触发长点击事件
                postDelayed(countDownRunnable, LONG_CLICK_TIME)
            }
            MotionEvent.ACTION_MOVE ->
                //当横移或纵移的长度大于系统规定的滑动最短距离时，则视为用户取消了longclick事件
                if (!(abs(event.x - downX) < mTouchSlop || abs(event.y - downY) < mTouchSlop
                            || isRelease)
                ) {
                    isRelease = true
                    if (isLongClick) {
                        return true
                    }
                }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_OUTSIDE -> {
                isRelease = true
                if (isLongClick) {
                    //当已经是longClick事件时，parent则拦截该事件，child不会再收到该事件
                    return true
                }
            }
        }
        val isDispatch = super.dispatchTouchEvent(event)
        return if (event.action == MotionEvent.ACTION_DOWN && !isDispatch) {
            //当down事件返回false时 不触发up事件 所以返回true强制触发UP事件，否则会出现click父布局出现longclick的效果
            true
        } else isDispatch
    }

    private val countDownRunnable = Runnable {
        isLongClick = true
        //当用户在LONG_CLICK_TIME时间内没有做抬起滑动等取消动作，则触发longclick事件
        if (isRelease) {
            return@Runnable
        }
        isRelease = true
        mActivity.runOnUiThread {
            if (longClickListener != null) {
                longClickListener!!.onLongClick(this)
            }
            isLongClick = false
        }
    }
}