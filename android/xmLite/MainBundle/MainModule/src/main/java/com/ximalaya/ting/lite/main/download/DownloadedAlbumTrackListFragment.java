package com.ximalaya.ting.lite.main.download;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ImageButton;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.downloadservice.DownloadUtil;
import com.ximalaya.ting.android.downloadservice.base.BaseDownloadTask;
import com.ximalaya.ting.android.downloadservice.base.IDownloadTaskCallback;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.XDCSDataUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.server.DownloadTools;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.lite.main.base.BaseFragmentInMain;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2018/3/26.
 * <p>
 * 下载专辑声音列表页
 */

public class DownloadedAlbumTrackListFragment extends BaseFragmentInMain implements
        View.OnClickListener, AdapterView.OnItemClickListener, IDownloadTaskCallback {
    private RefreshLoadMoreListView mListView;
    private TextView mLastPlayedView;
    private TextView mSortBtn;
    private ImageButton mJumpTop;
    private TextView mBatchDelete;
    private TextView mTvDragSort;

    private RelativeLayout mRlManageContainer;

    private DownloadedTrackAdapter mAdapter;
    private long mAlbumId;
    private long mAlbumUserId;
    private boolean mIsRefresh = false;

    private boolean mIsAsc;

    private boolean mHasClickLastPlayView; // 上次播放历史是否点击过

    private boolean mHasSetPlayListener = false;

    private boolean mIsFirstLoading = false;

    private TextView mSearchTv;
    private View mSearchDividerLineV;
    private boolean mPlayFirst;

    private IXmPlayerStatusListener listener = new IXmPlayerStatusListener() {

        @Override
        public void onPlayStart() {
            showLastPlayedView(false);
        }

        @Override
        public void onPlayPause() {

        }

        @Override
        public void onPlayStop() {

        }

        @Override
        public void onSoundPlayComplete() {

        }

        @Override
        public void onSoundPrepared() {

        }

        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {

        }

        @Override
        public void onBufferingStart() {

        }

        @Override
        public void onBufferingStop() {

        }

        @Override
        public void onBufferProgress(int percent) {

        }

        @Override
        public void onPlayProgress(int currPos, int duration) {

        }

        @Override
        public boolean onError(XmPlayerException exception) {
            return false;
        }
    };


    public static DownloadedAlbumTrackListFragment newInstance(long albumId, long albumUid) {
        DownloadedAlbumTrackListFragment fragment = new DownloadedAlbumTrackListFragment();
        Bundle args = new Bundle();
        args.putLong(BundleKeyConstantsInMain.KEY_ALBUM_ID, albumId);
        args.putLong(BundleKeyConstantsInMain.KEY_ALBUM_UID, albumUid);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Bundle args = getArguments();
        if (args == null) {
            CustomToast.showDebugFailToast("DownloadedAlbumTrackListFragment args is null");
        } else {
            mAlbumId = args.getLong(BundleKeyConstantsInMain.KEY_ALBUM_ID);
            mAlbumUserId = args.getLong(BundleKeyConstantsInMain.KEY_ALBUM_UID);
            mPlayFirst = args.getBoolean(BundleKeyConstants.KEY_PLAY_FIRST);
        }

        mJumpTop = (ImageButton) findViewById(R.id.main_image_jump_top);
        mJumpTop.setOnClickListener(this);


        mAdapter = new DownloadedTrackAdapter(mActivity, null, false);
        mAdapter.setTrackType(AbstractTrackAdapter.TYPE_DOWNLOAD);


        mListView = (RefreshLoadMoreListView) findViewById(R.id.main_listview);
        mListView.setMode(PullToRefreshBase.Mode.DISABLED);
        mListView.setOnItemClickListener(this);

        mListView.setAdapter(mAdapter);
        mListView.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {

            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (firstVisibleItem > 2) {
                    mJumpTop.setVisibility(View.VISIBLE);
                } else {
                    mJumpTop.setVisibility(View.INVISIBLE);
                }
            }
        });


        mRlManageContainer = (RelativeLayout) findViewById(R.id.main_rl_manage_container);

        mSortBtn = (TextView) findViewById(R.id.main_sort);
        mSortBtn.setOnClickListener(this);

        mSearchTv = (TextView) findViewById(R.id.main_tv_batch_search);
        mSearchTv.setOnClickListener(this);
        mSearchDividerLineV = findViewById(R.id.main_search_divider_line);

        mBatchDelete = (TextView) findViewById(R.id.main_tv_batch_delete);
        mBatchDelete.setOnClickListener(this);

        mTvDragSort = (TextView) findViewById(R.id.main_tv_drag_sort);
        mTvDragSort.setOnClickListener(this);

        mLastPlayedView = (TextView) findViewById(R.id.main_tv_last_played_view);
        mLastPlayedView.setOnClickListener(this);
    }


    private void setSearchViewState(boolean show) {
        ViewStatusUtil.setVisible(show ? View.VISIBLE : View.INVISIBLE, mSearchTv, mSearchDividerLineV);
    }

    @Override
    protected void loadData() {
        if (mIsRefresh) {
            return;
        }
        mIsRefresh = true;

        loadDownloadedTrack();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_downloaded_album_track_list;
    }

    private void loadDownloadedTrack() {
        new LoadDownloadedTrackTask(this).myexec();
    }

    @Override
    public void onDownloadProgress(@Nullable BaseDownloadTask downloadTask) {

    }

    @Override
    public void onCancel(@Nullable BaseDownloadTask downloadTask) {
        loadData();
    }

    @Override
    public void onComplete(@Nullable BaseDownloadTask downloadTask) {
        loadData();
    }

    @Override
    public void onUpdateTrack(@Nullable BaseDownloadTask downloadTask) {

    }

    @Override
    public void onStartNewTask(@Nullable BaseDownloadTask downloadTask) {
        loadData();
    }

    @Override
    public void onError(@Nullable BaseDownloadTask downloadTask) {
        loadData();
    }

    @Override
    public void onDelete() {
        loadData();
    }

    private static class LoadDownloadedTrackTask extends MyAsyncTask<Void, Void, List<Track>> {
        private WeakReference<DownloadedAlbumTrackListFragment> mFragmentRef;
        private boolean mShouldUpdateOrderNum;
        private boolean mContainsOrderPositionInAlbum = false;

        LoadDownloadedTrackTask(DownloadedAlbumTrackListFragment fragment) {
            mFragmentRef = new WeakReference<>(fragment);
        }

        @Override
        protected @Nullable
        List<Track> doInBackground(Void... params) {
            List<Track> trackList;

            try {
                DownloadedAlbumTrackListFragment fragment = mFragmentRef.get();
                if (fragment != null) {
                    trackList = RouteServiceUtil.getDownloadService().getDownloadedTrackListInAlbum(fragment.mAlbumId);
                } else {
                    trackList = null;
                }
            } catch (NullPointerException e) {
                return null;
            }

            if (trackList == null) {
                return null;
            }

            for (Track track : trackList) {
                if (track.getOrderPositionInAlbum() > 0) {
                    mContainsOrderPositionInAlbum = true;
                    break;
                }
            }

            try {
                if (mContainsOrderPositionInAlbum) {
                    Collections.sort(trackList, new Comparator<Track>() {

                        @Override
                        public int compare(Track lhs, Track rhs) {
                            if (lhs.getOrderPositionInAlbum() <= 0
                                    && rhs.getOrderPositionInAlbum() <= 0) {
                                return (lhs.getDownloadCreated() > rhs.getDownloadCreated()) ? 1 : -1;
                            } else {
                                if (lhs.getOrderPositionInAlbum() > 0 && rhs.getOrderPositionInAlbum() > 0) {
                                    return lhs.getOrderPositionInAlbum() - rhs.getOrderPositionInAlbum();
                                } else {
                                    return rhs.getOrderPositionInAlbum() - lhs.getOrderPositionInAlbum();
                                }
                            }

                        }
                    });
                } else {
                    Collections.sort(trackList, new Comparator<Track>() {

                        @Override
                        public int compare(Track lhs, Track rhs) {
                            return lhs.getOrderNum() - rhs.getOrderNum();
                        }
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            mShouldUpdateOrderNum = !mContainsOrderPositionInAlbum &&
                    DownloadTools.shouldUpdateOrderNumber(trackList);

            return trackList;
        }

        protected void onPostExecute(final @Nullable List<Track> result) {
            DownloadedAlbumTrackListFragment fragment = mFragmentRef.get();
            if (fragment == null) {
                return;
            }

            fragment.mIsRefresh = false;

            if (!fragment.canUpdateUi() || fragment.mAdapter == null)
                return;

            fragment.setDefaultData(result);

            if (mShouldUpdateOrderNum) {
                fragment.updateOrderNum(result);
            }
        }

    }

    private void setDefaultData(final @Nullable List<Track> trackList) {
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (!RouteServiceUtil.getDownloadService().isFetchDataBase()) {
                    onPageLoadingCompleted(LoadCompleteType.OK);
                }
                if (trackList != null) {
                    setSearchViewState(trackList.size() > SearchActionRouter.DEFAULT_SHOW_SEARCH_DOWNLOAD_TRACK_COUNT);
                    if (trackList.size() == 0) {
                        if (mAdapter != null) {
                            mAdapter.clear();
                        }
                        if (!RouteServiceUtil.getDownloadService().isFetchDataBase()) {
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                        }
                        return;
                    }
                    SharedPreferencesUtil spu = SharedPreferencesUtil
                            .getInstance(getActivity());
                    int orderType = spu.getInt(
                            PreferenceConstantsInHost.TINGMAIN_KEY_DOWNLOAD_ALBUM_SOUNDLIST_ORDER
                                    + mAlbumId,
                            AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER);

                    updateSortView(orderType);

                    if (mAdapter != null) {
                        mAdapter.clear();
                        mAdapter.addListData(doGetSortedListByOrder(orderType,
                                trackList));
                    }
                    setTitle("详情");

                    updateLastPlayView();

                    checkWillPlay();

                } else {
                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                }
            }
        });
    }

    private void checkWillPlay() {
        if(!mPlayFirst) {
            return;
        }

        mPlayFirst = false;

        if (null != mAdapter) {
            List<Track> trackList = mAdapter.getListData();
            if (!ToolUtil.isEmptyCollects(trackList)) {
                final Track track = XmPlayerManager.getInstance(getActivity()).getLastPlayTrackInAlbum(mAlbumId);
                int i = trackList.indexOf(track);
                if (i >= 0) {
                    XmPlayerManager.getInstance(mContext).playList(trackList ,i);
                } else {
                    XmPlayerManager.getInstance(mContext).playList(trackList ,0);
                }
            }
        }

    }

    /**
     * 声音列表排序
     */
    private List<Track> doGetSortedListByOrder(int orderType, List<Track> source) {
        List<Track> result = new ArrayList<>();
        if (orderType == AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER) {
            result = source;
        } else {
            result.addAll(source);
            Collections.reverse(result);
        }
        return result;
    }


    private void updateOrderNum(final @Nullable List<Track> trackList) {
        if (trackList == null || !NetworkUtils.isNetworkAvaliable(getActivity())) {
            return;
        }
        StringBuilder trackIds = new StringBuilder();
        for (int i = 0; i < trackList.size(); i++) {
            Track track = trackList.get(i);
            if (i != 0) {
                trackIds.append(",");
            }
            trackIds.append(track.getDataId());
        }
        Map<String, String> params = new HashMap<>();
        params.put("albumId", String.valueOf(mAlbumId));
        params.put("uid", String.valueOf(mAlbumUserId));
        params.put("trackIds", trackIds.toString());
        LiteCommonRequest.updateAlbumTracksOrderNum(params, new IDataCallBack<Map<String, Integer>>() {
            @Override
            public void onSuccess(@Nullable Map<String, Integer> object) {
                if (!canUpdateUi()) return;
                if (object != null && !object.isEmpty()) {
                    updateOrderNumInBackground(trackList, object);
                }
            }

            @Override
            public void onError(int code, String message) {
                ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
            }
        });

    }

    private static class UpdateOrderNumTask extends MyAsyncTask<Void, Void, List<Track>> {
        private WeakReference<DownloadedAlbumTrackListFragment> mFragmentRef;
        private List<Track> mTracks;
        private Map<String, Integer> mOrderNums;

        UpdateOrderNumTask(DownloadedAlbumTrackListFragment fragment, List<Track> tracks,
                           Map<String, Integer> orderNums) {
            mFragmentRef = new WeakReference<>(fragment);
            mTracks = tracks;
            mOrderNums = orderNums;

        }

        @Override
        protected List<Track> doInBackground(Void... params) {
            for (Track track : mTracks) {
                Object object = mOrderNums.get(track.getDataId() + "");
                if (object != null) {
                    track.setOrderNum((Integer) object);
                    DownloadUtil.updateTrackToDb(track);
                }
            }
            DownloadTools.sortByDownloadTime(mTracks);
            return mTracks;
        }

        protected void onPostExecute(final List<Track> result) {
            DownloadedAlbumTrackListFragment fragment = mFragmentRef.get();
            if (fragment == null || !fragment.canUpdateUi() || fragment.mAdapter == null)
                return;
            fragment.setDefaultData(result);
        }

    }

    private void updateOrderNumInBackground(final List<Track> tracks, final Map<String, Integer> orderNums) {
        new UpdateOrderNumTask(this, tracks, orderNums).myexec();
    }

    private void updateSortView(int orderType) {
        if (orderType == AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER) {
            mSortBtn.setCompoundDrawablesWithIntrinsicBounds(
                    0, 0, R.drawable.main_ic_download_sort_desc_selector, 0);
            mSortBtn.setText(getString(R.string.main_sort_asc));
            mIsAsc = true;
        } else if (orderType == AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_REVERSE_ORDER) {
            mSortBtn.setCompoundDrawablesWithIntrinsicBounds(
                    0, 0, R.drawable.main_ic_download_sort_asc_selector, 0);
            mSortBtn.setText(getString(R.string.main_sort_desc));
            mIsAsc = false;
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.main_tv_drag_sort) {
            if (mAdapter != null) {
                SoundSortFragment soundSortFragment = SoundSortFragment
                        .newInstance((ArrayList<Track>) mAdapter.getListData(), true);
                soundSortFragment.setCallbackFinish(new IFragmentFinish() {

                    @Override
                    public void onFinishCallback(Class<?> cls, int fid, Object... params) {
                        if (params != null && params.length > 0) {
                            if (params[0] instanceof Boolean) {
                                boolean dataChanged = (Boolean) params[0];
                                if (dataChanged) {
                                    SharedPreferencesUtil spu = SharedPreferencesUtil.getInstance(getActivity());
                                    spu.saveInt(PreferenceConstantsInHost.TINGMAIN_KEY_DOWNLOAD_ALBUM_SOUNDLIST_ORDER
                                            + mAlbumId, AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER);
                                }
                            }
                        }

                        loadData();
                    }
                });
                startFragment(soundSortFragment, v);

                new UserTracking()
                        .setSrcPage("专辑下载页")
                        .setSrcModule("topTool")
                        .setItem("button")
                        .setItemId("手动排序")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            }
        } else if (v.getId() == R.id.main_tv_batch_delete) { // 批量删除
            if (null != mAdapter) {
                List<Track> trackList = mAdapter.getListData();
                BatchDeleteFragment batchDeleteFragment = BatchDeleteFragment.newInstance((ArrayList<Track>) trackList);
                startFragment(batchDeleteFragment, v);

                new UserTracking()
                        .setSrcPage("专辑下载页")
                        .setSrcModule("topTool")
                        .setItem("page")
                        .setItemId("下载批量删除页")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            }
        } else if (v.getId() == R.id.main_tv_last_played_view) {
            onPlayClick(v);
            mHasClickLastPlayView = true;
        } else if (v.getId() == R.id.main_sort) {
            if (mAdapter != null && null != mAdapter.getListData() && mAdapter.getListData().size() > 1) {
                SharedPreferencesUtil spu = SharedPreferencesUtil
                        .getInstance(getActivity());
                int albumSoundsOrderType = spu.getInt(
                        PreferenceConstantsInHost.TINGMAIN_KEY_DOWNLOAD_ALBUM_SOUNDLIST_ORDER
                                + mAlbumId,
                        AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER);

                switch (albumSoundsOrderType) {
                    case AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER:
                        spu.saveInt(PreferenceConstantsInHost.TINGMAIN_KEY_DOWNLOAD_ALBUM_SOUNDLIST_ORDER
                                + mAlbumId, AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_REVERSE_ORDER);
                        albumSoundsOrderType = AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_REVERSE_ORDER;

                        break;
                    case AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_REVERSE_ORDER:
                        spu.saveInt(PreferenceConstantsInHost.TINGMAIN_KEY_DOWNLOAD_ALBUM_SOUNDLIST_ORDER
                                + mAlbumId, AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER);
                        albumSoundsOrderType = AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER;

                        break;
                    default:
                        break;
                }

                updateSortView(albumSoundsOrderType);

                Collections.reverse(mAdapter.getListData());
                mAdapter.notifyDataSetChanged();

                new UserTracking()
                        .setSrcPage("专辑下载页")
                        .setSrcModule("topTool")
                        .setItem("button")
                        .setItemId("排序")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            }

        } else if (v.getId() == R.id.main_tv_last_played_view) {
            onPlayClick(v);
            mHasClickLastPlayView = true;

        } else if (v.getId() == R.id.main_image_jump_top) {
            if (mListView != null && mListView.getRefreshableView() != null) {
                mListView.getRefreshableView().smoothScrollToPosition(0);
            }
        } else if (v.getId() == R.id.main_tv_batch_search) {
            BaseFragment fragment = SearchActionRouter.getInstance().getFragmentAction() != null ? SearchActionRouter.getInstance().getFragmentAction().newSearchDownloadTrackFragment(mAlbumId) : null;
            if (fragment != null) {
                startFragment(fragment);
            }
        }
    }

    private void fileDeleted(final Track track) {

        if (track == null)
            return;

        new DialogBuilder(mActivity).setMessage("声音已被其它清理软件误删，要避免该问题，请勿清除喜马拉雅应用数据")
                .setOkBtn("重新下载").setOkBtn(new DialogBuilder.DialogCallback() {

            @Override
            public void onExecute() {
                if (NetworkType.getNetWorkType(mActivity) == NetworkType.NetWorkType.NETWORKTYPE_INVALID) {
                    CustomToast.showFailToast("没有网络");
                    return;
                }
                if (mAdapter != null) {

                    if (TextUtils.isEmpty(track.getDownloadUrl())) {
                        HashMap<String, String> params = new HashMap<>();
                        params.put("uid", (track.getAnnouncer() != null ? track
                                .getAnnouncer().getAnnouncerId() : 0) + "");
                        params.put("device", "android");
                        params.put("trackId", track.getDataId() + "");
                        params.put("traceId", XDCSDataUtil.getTraceId());
                        params.put("startTime", "" + System.currentTimeMillis());
                        params.put("sequenceId", track.getSequenceId());
                        params.put("sendDataTime ", "" + System.currentTimeMillis());
                        params.put("clientTraffic ", "" + track.getDownloadedSize());
                        long downloadedSize = track.getDownloadedSize();
                        long downloadSize = track.getDownloadSize();
                        long percent = 0;
                        if (downloadSize != 0) {
                            percent = (downloadedSize * 100) / downloadSize;
                        }

                        params.put("downloadPercent", percent + "");
                        CommonRequestM.getInstanse().getDownloadTrackInfo(params,
                                new IDataCallBack<Track>() {

                                    @Override
                                    public void onSuccess(@Nullable Track object) {
                                        if (object != null) {
                                            object.setPlayCount(track.getPlayCount());
                                            object.setFavoriteCount(track
                                                    .getFavoriteCount());
                                            object.setCommentCount(track
                                                    .getCommentCount());

                                            object.setCoverUrlLarge(track.getCoverUrlLarge());
                                            object.setCoverUrlMiddle(track.getCoverUrlMiddle());
                                            object.setCoverUrlSmall(track.getCoverUrlSmall());

                                            if (!object.isPaid() && TextUtils.isEmpty(object.getDownloadUrl())) {
                                                XDCSCollectUtil.statErrorToXDCS("download", "resource=DownloadedTrackListFragment1" + ";track={" + object.toString() + "}");
                                            }

                                            boolean isRemoveTrack = (!object.isPayTrack() || object.isAuthorized())
                                                    && RouteServiceUtil.getDownloadService().addTask(object) && mAdapter != null;
                                            if (isRemoveTrack) {
                                                mAdapter.getListData().remove(track);
                                                mAdapter.notifyDataSetChanged();
                                                CustomToast.showSuccessToast("重新加入下载列表");
                                            } else {
                                                CustomToast.showFailToast("重新下载失败");
                                            }
                                        }
                                    }

                                    @Override
                                    public void onError(int code, String message) {
                                        CustomToast.showFailToast("重新下载失败");
                                    }

                                });
                    } else {
                        if (!track.isPaid() && TextUtils.isEmpty(track.getDownloadUrl())) {
                            XDCSCollectUtil.statErrorToXDCS("download", "resource=DownloadedTrackListFragment2" + ";track={" + track.toString() + "}");
                        }
                        boolean result = RouteServiceUtil.getDownloadService().addTask(track);
                        if (result) {
                            mAdapter.getListData().remove(track);
                            mAdapter.notifyDataSetChanged();
                        } else {
                            CustomToast.showFailToast("重新下载失败");
                        }
                    }
                }
            }
        }).setCancelBtn("删除").setCancelBtn(new DialogBuilder.DialogCallback() {

            @Override
            public void onExecute() {
                if (mAdapter != null) {
                    mAdapter.getListData().remove(track);
                    mAdapter.notifyDataSetChanged();
                    RouteServiceUtil.getDownloadService().deleteDownloadedTasks(track);
                }
            }
        }).setCancelable(false).setLayoutWidth().showConfirm();
    }


    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position,
                            long id) {
        if (mListView == null) {
            return;
        }
        int index = position
                - mListView.getRefreshableView().getHeaderViewsCount();
        if (mAdapter == null || index < 0 || mAdapter.getCount() <= index)
            return;

        Track track = (Track) mAdapter.getItem(index);

        if (track != null) {
            if (TextUtils.isEmpty(track.getDownloadedSaveFilePath())) {
                fileDeleted(track);
                return;
            }
        }
        if (track != null) {
            File file = new File(track.getDownloadedSaveFilePath());
            if (!file.exists()) {
                fileDeleted(track);
                return;
            }
        }

        onPlayItemClick(view, index);

    }

    /**
     * 点击历史播放条
     */
    private void onPlayClick(View v) {
        if (null != mAdapter) {
            List<Track> trackList = mAdapter.getListData();
            if (!ToolUtil.isEmptyCollects(trackList)) {
                Track track = XmPlayerManager.getInstance(getActivity()).getLastPlayTrackInAlbum(mAlbumId);
                if (trackList.contains(track)) {
                    int index = trackList.indexOf(track);
                    onPlayItemClick(v, index);
                    showLastPlayedView(false);

                }
            }
        }
    }

    private void onPlayItemClick(View view, int index) {
        boolean isRightIndex = mAdapter == null || index < 0 || mAdapter.getCount() <= index;
        if (isRightIndex)
            return;
        for (Track track2 : mAdapter.getListData()) {
            track2.setPlaySource(ConstantsOpenSdk.PLAY_FROM_DOWNLOAD_ALBUM);
        }
        if (getActivity() != null) {
            int totalCount = mAdapter.getListData().size();

            if (totalCount > 200) {
                int startPosition;
                int endPosition;

                int indexInNewList;

                if (index + 100 > totalCount) {
                    endPosition = totalCount;
                } else {
                    endPosition = index + 100;
                }

                if (index - 100 < 0) {
                    startPosition = 0;
                    indexInNewList = index;
                } else {
                    startPosition = index - 100;
                    indexInNewList = 100;
                }

                CommonTrackList<Track> commonTrackList = getCommonTrackList(mAdapter.getListData().subList(startPosition, endPosition));
                PlayTools.playCommonListWithoutWifi(getActivity(), commonTrackList,
                        indexInNewList, true, view);
            } else {

                CommonTrackList<Track> commonTrackList = getCommonTrackList(mAdapter.getListData());
                PlayTools.playCommonListWithoutWifi(getActivity(), commonTrackList,
                        index, true, view);
            }
        }
    }

    private void showLastPlayedView(boolean isShow) {
        if (isShow) {
            if (mLastPlayedView != null) {
                mLastPlayedView.setVisibility(View.VISIBLE);
            }
        } else {
            if (mLastPlayedView != null) {
                mLastPlayedView.setVisibility(View.GONE);
            }
        }
    }

    private CommonTrackList<Track> getCommonTrackList(List<Track> tracks) {
        CommonTrackList<Track> commonTrackList = new CommonTrackList<>();
        commonTrackList.setTracks(tracks);

        Map<String, String> params = new HashMap<>();
        params.put(DTransferConstants.LOCAL_IS_ASC, String.valueOf(!mIsAsc));  // NOTICE: 播放器内部的升序代表的意义貌似和此处定义的相反
        commonTrackList.setParams(params);

        return commonTrackList;
    }

    private void updateLastPlayView() {
        if (null != mAdapter) {
            List<Track> trackList = mAdapter.getListData();
            if (!ToolUtil.isEmptyCollects(trackList)) {
                final Track track = XmPlayerManager.getInstance(getActivity()).getLastPlayTrackInAlbum(mAlbumId);
                if (trackList.contains(track) && !PlayTools.isCurrentTrackPlaying(mContext, track)) {
                    // 上次播放记录track存在于已下载列表
                    if (null != mLastPlayedView && !mHasClickLastPlayView) {
                        mLastPlayedView.setVisibility(View.VISIBLE);
                        mLastPlayedView.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (null != mLastPlayedView) {
                                    mLastPlayedView.setText("继续播放：" + track.getTrackTitle());
                                }
                            }
                        }, 350);
                    }
                } else {
                    if (null != mLastPlayedView) {
                        mLastPlayedView.setVisibility(View.GONE);
                    }
                }
            } else {
                if (null != mLastPlayedView) {
                    mLastPlayedView.setVisibility(View.GONE);
                }
            }
        }
    }


    @Override
    public void onMyResume() {
        super.onMyResume();

        if (!mHasSetPlayListener) {
            XmPlayerManager.getInstance(mContext).addPlayerStatusListener(mAdapter);
            RouteServiceUtil.getDownloadService().registerDownloadCallback(this);
            if (mAdapter != null) {
                mAdapter.setXmPlayerStatusListener(listener);
            }
            if (mIsFirstLoading) {
                mIsFirstLoading = false;
            } else {
                loadData();
            }
            mHasSetPlayListener = true;
        }
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    protected boolean isShowCoinGuide() {
        return false;
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mHasSetPlayListener) {
            XmPlayerManager.getInstance(mContext).removePlayerStatusListener(mAdapter);
            RouteServiceUtil.getDownloadService().unRegisterDownloadCallback(this);
            if (mAdapter != null) {
                mAdapter.setXmPlayerStatusListener(null);
            }
            mHasSetPlayListener = false;
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && mAdapter != null && !mHasSetPlayListener) {
            loadData();
            RouteServiceUtil.getDownloadService().registerDownloadCallback(this);
            XmPlayerManager.getInstance(mContext).addPlayerStatusListener(mAdapter);
            mHasSetPlayListener = true;
        }

    }

    @Override
    public void onPageLoadingCompleted(LoadCompleteType loadCompleteType) {
        super.onPageLoadingCompleted(loadCompleteType);

        if (loadCompleteType == LoadCompleteType.NOCONTENT) {
            mRlManageContainer.setVisibility(View.INVISIBLE);  //不能是GONE， 别的布局依赖他
        } else if (loadCompleteType == LoadCompleteType.OK) {
            mRlManageContainer.setVisibility(View.VISIBLE);
        }


    }
}
