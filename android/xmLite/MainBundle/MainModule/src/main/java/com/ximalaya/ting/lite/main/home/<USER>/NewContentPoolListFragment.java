package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.request.HttpParamsConstantsInMain;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.request.LiteUrlConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新的糖葫芦热词中内容池类型item点击进入此界面
 *
 * <AUTHOR>
 */
public class NewContentPoolListFragment extends BaseFragment2 implements IRefreshLoadMoreListener,
        AdapterView.OnItemClickListener {

    private RefreshLoadMoreListView mListView;
    private int mPageId = 1;
    private boolean mIsLoading = false;
    private AlbumAdapter mAdapter;
    private ImageView vNoContent;
    private List<Album> albumList = new ArrayList<>();

    private String title;
    private int poolId;//内容池id
    /**
     * (from == BundleValueConstantsInMain.FROM_NEW_HOME_RECOMMEND || from == BundleValueConstantsInMain.FROM_NEW_HOME_NORMAL)代表是从新的首页跳过来的内容池楼层跳转过来的
     */
    private int from = -1;

    private boolean mIsFirstResume = true;

    private final IGotoTop.IGotoTopBtnClickListener mTopBtnListener = new IGotoTop.IGotoTopBtnClickListener() {
        @Override
        public void onClick(View v) {
            if (!isRealVisable()) {
                return;
            }
            if (mListView == null) {
                return;
            }
            mListView.getRefreshableView().setSelection(0);
        }
    };

    public static NewContentPoolListFragment newInstance(String title, int poolId, int from) {
        Bundle args = new Bundle();
        args.putString(BundleKeyConstants.KEY_TITLE, title);
        args.putInt(BundleKeyConstants.CONTENT_POOL_ID, poolId);
        args.putInt(BundleKeyConstantsInMain.KEY_FROM, from);
        NewContentPoolListFragment fragment = new NewContentPoolListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_category_detail_guess_you_like;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            title = arguments.getString(BundleKeyConstants.KEY_TITLE);
            poolId = arguments.getInt(BundleKeyConstants.CONTENT_POOL_ID);
            from = arguments.getInt(BundleKeyConstants.KEY_FROM, -1);
        }
        //需要标题栏，设置为可滑动返回
        setCanSlided(true);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mListView = findViewById(R.id.main_listview);
        mAdapter = new AlbumAdapter(mActivity, albumList, true, false);
        initFooterView();

        mListView.setAdapter(mAdapter);
        mListView.setOnRefreshLoadMoreListener(this);
        mListView.setOnItemClickListener(this);
        mListView.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                //do nothing
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (getiGotoTop() != null) {
                    getiGotoTop().setState(firstVisibleItem > 12);
                }
            }
        });
        setTitle(title);
    }

    private void initFooterView() {
        LinearLayout ll = new LinearLayout(getActivity());
        ll.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        ll.setGravity(Gravity.CENTER);
        vNoContent = new ImageView(getActivity());
        vNoContent.setPadding(0, BaseUtil.dp2px(mContext, 30), 0, 0);
        vNoContent.setImageResource(R.drawable.main_bg_meta_nocontent);
        ll.addView(vNoContent);
        vNoContent.setVisibility(View.GONE);
        mListView.getRefreshableView().addFooterView(ll);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        List<Album> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (index < 0 || index >= listData.size()) {
            return;
        }
        Album album = listData.get(index);
        if (!(album instanceof AlbumM)) {
            return;
        }
        AlbumM albumM = (AlbumM) album;
        // 首页内容池-查看更多内页-专辑item  点击事件
        new XMTraceApi.Trace()
                .click(45539)
                .put("albumId", String.valueOf(albumM.getId()))
                .put("currPage", "See more inside pages")
                .createTrace();
        AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
    }

    @Override
    public void onRefresh() {
        refresh();
    }

    private void refresh() {
        mPageId = 1;
        if (mListView != null) {
            mListView.setFooterViewVisible(View.VISIBLE);
        }
        loadData();
    }

    @Override
    public void onMore() {
        mPageId++;
        loadData();
    }

    @Override
    protected void loadData() {
        if (mIsLoading) {
            return;
        }
        if (canUpdateUi() && mAdapter != null && mAdapter.getCount() == 0) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        mIsLoading = true;

        loadGuessLikeListRefresh();
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        // 首页内容池-查看更多内页  页面展示
        new XMTraceApi.Trace()
                .pageView(45537, "See more inside pages")
                .put("currPage", title)
                .createTrace();

        if (mIsFirstResume) {
            mIsFirstResume = false;
        } else {
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
    }

    private void loadGuessLikeListRefresh() {
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstantsInMain.PARAM_UID, String.valueOf(UserInfoMannage.getUid()));
        params.put("poolId", String.valueOf(poolId));
        params.put(HttpParamsConstants.PARAM_PAGE_ID, String.valueOf(mPageId));
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        IDataCallBack<List<AlbumM>> callBack = new IDataCallBack<List<AlbumM>>() {
            @Override
            public void onSuccess(final List<AlbumM> list) {
                mIsLoading = false;
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        boolean isNoContent = CollectionUtil.isNullOrEmpty(list);
                        if (isNoContent) {
                            mListView.setHasMoreNoFooterView(false);
                            List<Album> listData = mAdapter.getListData();
                            if (CollectionUtil.isNullOrEmpty(listData)) {
                                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                            }
                            return;
                        }
                        //加载数据
                        onLoadSuccess(list);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                onLoadFailure(message);
            }
        };

        String url = LiteUrlConstants.getContentPoolUrl() + "/" + System.currentTimeMillis();
        if (from == BundleValueConstantsInMain.FROM_NEW_HOME_RECOMMEND || from == BundleValueConstantsInMain.FROM_NEW_HOME_NORMAL) {
            url = LiteUrlConstants.newHomeContentPollUrl();
        }
        LiteCommonRequest.getContentPoolData(url, params, callBack);
    }

    /**
     * 数据加载成功
     */
    private void onLoadSuccess(final List<AlbumM> list) {
        List<Album> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (mPageId == 1) {
            listData.clear();
        }
        listData.addAll(list);
        mListView.onRefreshComplete(true);
    }

    /**
     * 数据加载失败
     */
    private void onLoadFailure(String message) {
        mIsLoading = false;
        if (!canUpdateUi()) {
            return;
        }
        if (mPageId == 1) {
            mAdapter.clear();
            mListView.onRefreshComplete(true);
            mListView.setHasMoreNoFooterView(false);
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        } else {
            CustomToast.showFailToast(message);
            mListView.onRefreshComplete(true);
        }
    }

    @Override
    protected void loadDataError() {
        if (mListView != null) {
            mListView.setMode(Mode.DISABLED);
            mListView.setHasMoreNoFooterView(false);
        }
    }

    @Override
    protected void loadDataOk() {
        if (mListView != null) {
            mListView.setMode(Mode.PULL_FROM_START);
        }
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }


    @Override
    protected String getPageLogicName() {
        return "NewContentPoolListFragment";
    }

    @Override
    public void onResume() {
        super.onResume();
        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(mTopBtnListener);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
    }

}
