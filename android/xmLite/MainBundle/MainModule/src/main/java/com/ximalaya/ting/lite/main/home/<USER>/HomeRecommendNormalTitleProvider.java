package com.ximalaya.ting.lite.main.home.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.manager.HomeFeedTrackStatusManager;
import com.ximalaya.ting.lite.main.home.manager.HomeRecommendAdapterAddFloorManager;
import com.ximalaya.ting.lite.main.home.presenter.HomeRecommendContact;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;
import com.ximalaya.ting.lite.main.model.album.RecommendTrackItem;
import com.ximalaya.ting.lite.main.model.album.TitleModule;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Lennon on 2017/10/28.
 *
 * <AUTHOR>
 */

public class HomeRecommendNormalTitleProvider implements IMulitViewTypeViewAndData<HomeRecommendNormalTitleProvider.NormalTitleHolder, TitleModule> {
    public static final int MAX_PLAY_ALL_LOAD_FEED_NUMBER = 50;
    HomeRecommedExtraDataProvider recommedExtraDataProvider;
    HomeRecommendContact.IFragmentView fragmentView;

    public HomeRecommendNormalTitleProvider(HomeRecommedExtraDataProvider recommedExtraDataProvider, HomeRecommendContact.IFragmentView iFragmentView) {
        this.recommedExtraDataProvider = recommedExtraDataProvider;
        fragmentView = iFragmentView;
    }

    @Override
    public void bindViewDatas(NormalTitleHolder holder, ItemModel<TitleModule> t, View convertView, int position) {
        TitleModule titleModule = t.getObject();
        if (titleModule == null || titleModule.getTitleBean() == null) {
            return;
        }
        MainAlbumMList titleBean = titleModule.getTitleBean();

        holder.titleNameText.setText(titleBean.getTitle());

        if (titleBean.isPlayAllBtn()) {
            //展示全部播放按钮
            holder.layoutPlayAll.setVisibility(View.VISIBLE);
            holder.titleMoreButton.setVisibility(View.GONE);
            holder.layoutPlayAll.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dealPlayAllClick(v, position);
                }
            });
            AutoTraceHelper.bindData(holder.layoutPlayAll, titleBean.getModuleType() + "", titleBean);

            List<Track> afterTrackList = getAfterTrackList(position);
            boolean currentPlayTrack = isCurrentPlayTrack(afterTrackList);
            boolean isPlaying = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying();
            if (isPlaying && currentPlayTrack) {
                //目前是正在播放，并且是当前列表声音，可以暂停播放
                holder.ivPlayAll.setImageResource(R.drawable.main_icon_home_item_album_ic_stop);
                holder.tvPlayAll.setText("暂停全部");
            } else if (!isPlaying && currentPlayTrack) {
                //目前暂停播放，并且是当前列表声音，可以继续播放
                holder.ivPlayAll.setImageResource(R.drawable.main_icon_home_item_album_ic_playall);
                holder.tvPlayAll.setText("继续播放");
            } else {
                //如果存在当前页面的断点续播，可以继续播放
                if (isLastPlayTrack(afterTrackList)) {
                    holder.ivPlayAll.setImageResource(R.drawable.main_icon_home_item_album_ic_playall);
                    holder.tvPlayAll.setText("继续播放");
                } else {
                    //其他情况，展示全部播放
                    holder.ivPlayAll.setImageResource(R.drawable.main_icon_home_item_album_ic_playall);
                    holder.tvPlayAll.setText("播放全部");
                }
            }
        } else if (titleBean.isHasMore()) {
            //展示更多按钮
            holder.layoutPlayAll.setVisibility(View.GONE);
            holder.titleMoreButton.setVisibility(View.VISIBLE);
            holder.titleMoreButton.setOnClickListener(titleModule.getMoreClickListener());
            AutoTraceHelper.bindData(holder.titleMoreButton, titleBean.getModuleType() + "", titleBean);
        } else {
            holder.layoutPlayAll.setVisibility(View.GONE);
            holder.titleMoreButton.setVisibility(View.GONE);
        }
    }

    /**
     * 获取当前位置之后的50条声音
     */
    private List<Track> getAfterTrackList(int currentPosition) {
        if (recommedExtraDataProvider == null) {
            return null;
        }
        List<ItemModel> itemList = recommedExtraDataProvider.getListData();
        if (itemList == null || itemList.size() == 0) {
            return null;
        }
        List<Track> trackItemList = new ArrayList<>();
        if (currentPosition >= 0 && currentPosition < itemList.size()) {
            for (int i = currentPosition; i < itemList.size(); i++) {
                ItemModel item = itemList.get(i);
                if (item == null) {
                    continue;
                }
                if (item != null && item.getObject() instanceof RecommendItemNew && ((RecommendItemNew) item.getObject()).getItem() instanceof RecommendTrackItem) {
                    trackItemList.add((Track) ((RecommendItemNew) item.getObject()).getItem());
                }
                if (trackItemList.size() >= MAX_PLAY_ALL_LOAD_FEED_NUMBER) {
                    break;
                }
            }
        }
        return trackItemList;
    }

    public void dealPlayAllClick(View view, int position) {
        List<Track> afterTrackList = getAfterTrackList(position);
        boolean currentPlayTrack = isCurrentPlayTrack(afterTrackList);
        boolean isPlaying = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying();
        if (isPlaying && currentPlayTrack) {
            //目前是正在播放，并且是当前列表声音，可以暂停播放
            XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).pause();
        } else if (!isPlaying && currentPlayTrack) {
            //目前暂停播放，并且是当前列表声音，可以继续播放
            XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).play();
        } else {
            //如果存在当前页面的断点续播，可以继续播放
            if (isLastPlayTrack(afterTrackList)) {
                //进行断点续播
                PlayTools.playList(BaseApplication.getMyApplicationContext(), afterTrackList, getLastPlayTrackPosition(afterTrackList), false, view);
            } else {
                //其他情况，展示全部播放
                PlayTools.playList(BaseApplication.getMyApplicationContext(), afterTrackList, 0, false, view);
            }
        }
    }

    private boolean isCurrentPlayTrack(List<Track> afterTrackList) {
        if (afterTrackList == null || afterTrackList.size() == 0) {
            return false;
        }
        PlayableModel curModel = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getCurrSound();
        if (curModel == null) {
            return false;
        }
        if (curModel.getDataId() <= 0) {
            return false;
        }
        if (!(curModel instanceof Track)) {
            return false;
        }
        for (int i = 0; i < afterTrackList.size(); i++) {
            Track trackM = afterTrackList.get(i);
            if (trackM.getDataId() == curModel.getDataId()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否是上次断点续播的id
     */
    private boolean isLastPlayTrack(List<Track> afterTrackList) {
        if (afterTrackList == null || afterTrackList.size() == 0) {
            return false;
        }
        if (fragmentView == null) {
            return false;
        }
        HomeRecommendAdapterAddFloorManager homeRecommendAdapterAddFloorManager = fragmentView.getHomeRecommendAdapterAddFloorManager();
        if (homeRecommendAdapterAddFloorManager == null) {
            return false;
        }
        HomeFeedTrackStatusManager feedTrackStatusManager = homeRecommendAdapterAddFloorManager.getFeedTrackStatusManager();
        if (feedTrackStatusManager == null) {
            return false;
        }
        long lastPlayTrackId = feedTrackStatusManager.getLastPlayTrackId();
        if (lastPlayTrackId <= 0) {
            return false;
        }
        for (int i = 0; i < afterTrackList.size(); i++) {
            Track trackM = afterTrackList.get(i);
            if (trackM.getDataId() == lastPlayTrackId) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否是上次断点续播的id
     */
    private int getLastPlayTrackPosition(List<Track> afterTrackList) {
        if (afterTrackList == null || afterTrackList.size() == 0) {
            return 0;
        }
        if (fragmentView == null) {
            return 0;
        }
        HomeRecommendAdapterAddFloorManager homeRecommendAdapterAddFloorManager = fragmentView.getHomeRecommendAdapterAddFloorManager();
        if (homeRecommendAdapterAddFloorManager == null) {
            return 0;
        }
        HomeFeedTrackStatusManager feedTrackStatusManager = homeRecommendAdapterAddFloorManager.getFeedTrackStatusManager();
        if (feedTrackStatusManager == null) {
            return 0;
        }
        long lastPlayTrackId = feedTrackStatusManager.getLastPlayTrackId();
        if (lastPlayTrackId <= 0) {
            return 0;
        }
        for (int i = 0; i < afterTrackList.size(); i++) {
            Track trackM = afterTrackList.get(i);
            if (trackM.getDataId() == lastPlayTrackId) {
                return i;
            }
        }
        return 0;
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_view_list_header, parent, false);
    }

    @Override
    public NormalTitleHolder buildHolder(View convertView) {
        return new NormalTitleHolder(convertView);
    }


    public static class NormalTitleHolder extends HolderAdapter.BaseViewHolder {
        View titleItemView;
        TextView titleNameText;
        View titleMoreButton;

        LinearLayout layoutPlayAll;
        ImageView ivPlayAll;
        TextView tvPlayAll;

        public NormalTitleHolder(View view) {
            titleItemView = view.findViewById(R.id.main_list_header);
            titleNameText = (TextView) view.findViewById(R.id.main_title_tv);

            //更多按钮
            titleMoreButton = view.findViewById(R.id.main_btn_more);

            layoutPlayAll = view.findViewById(R.id.main_layout_play_all);
            ivPlayAll = view.findViewById(R.id.main_iv_play_all);
            tvPlayAll = view.findViewById(R.id.main_tv_play_all);
        }
    }
}
