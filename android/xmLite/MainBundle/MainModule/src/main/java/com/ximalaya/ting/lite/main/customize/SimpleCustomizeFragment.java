package com.ximalaya.ting.lite.main.customize;

import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.TempDataManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.customize.CustomizeManager;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.model.user.InterestCardModel;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.customize.ageselector.IAgeSelector;

import com.ximalaya.ting.lite.main.onekey.OneKeyRadioSettingFragment;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR> on 2017/6/29.
 */
public class SimpleCustomizeFragment extends BaseFragment2 implements View.OnClickListener {

    private static final String TAG_ONEKEY_SETTING_FRAG = "onekey_setting_fragment_container";

    private static final String KEY_ALLOW_BACK = "allow_back";
    private static final String KEY_CAN_SKIP = "can_skip";

    private static final int PAGE_GENDER = 1;
    private static final int PAGE_AGE = 2;

    private int mStep = PAGE_GENDER;
    private TextView mTvSubTitle;
    private Button mBtnComplete;
    private RoundImageView mIvMale;
    private RoundImageView mIvFemale;
    private IAgeSelector mAgeSelector;
    private View mLayoutGender;
    private View mLayoutAge;

    //用户选择的数据
    private InterestCardModel mSelectData = new InterestCardModel();

    //是否是关闭当前fragment,需要拦截onBackPressed，造成finishFragment无法关闭页面，增加此参数判断是否是关闭，默认是false
    private boolean mIsFinishFragment = false;
    private RoundImageView mIvSelectedGender;
    private int mGenderH;
    private int mGenderW;
    private int mGenderR;
    private int[] mGenderPos;
    private TextView mTvMale;
    private TextView mTvFemale;
    private int mSrcHeight;
    private int mSrcWidth;
    private int mHGap;
    private int mWGap;
    private int mAgeSelectorTranslate = BaseUtil.getScreenHeight(mContext);
    private View mVComplete;

    private boolean isPosting;
    private int mXGap;
    private int mYGap;
    private View mVMale;
    private View mVFemale;
    private ViewGroup mVGender;

    private boolean allowBack = false;
    private boolean canSkip = false;
    private boolean mNeedFinishFrag = false;

    public SimpleCustomizeFragment() {
        super(false, null);
    }

    public static SimpleCustomizeFragment newInstance(boolean allowBack, boolean canSkip) {

        SimpleCustomizeFragment fragment = new SimpleCustomizeFragment();
        Bundle args = new Bundle();
        args.putBoolean(KEY_ALLOW_BACK, allowBack);
        args.putBoolean(KEY_CAN_SKIP, canSkip);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Bundle args = getArguments();

        if (args != null) {

            allowBack = args.getBoolean(KEY_ALLOW_BACK, false);
            canSkip = args.getBoolean(KEY_CAN_SKIP, false);
        }
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);

        if (allowBack) {

            titleBar.getBack().setVisibility(View.VISIBLE);
        } else {

            titleBar.getBack().setVisibility(View.INVISIBLE);
        }

        LocalImageUtil.setBackgroundDrawable(titleBar.getTitleBar(), null);
        titleBar.getBack().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mStep == PAGE_AGE) {

                    toGenderPage();
                } else if (allowBack) {

                    finishFragment();
                }
            }
        });
        AutoTraceHelper.bindData(titleBar.getBack(), "");

        if (canSkip) {

            TitleBar.ActionType action = new TitleBar.ActionType("skip", TitleBar.RIGHT,
                    0, 0, R.color.main_color_999999, TextView.class);
            action.setContentStr("跳过");
            action.setFontSize(14);
            titleBar.addAction(action, new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    mIsFinishFragment = true;
                    finishFragment();

                    trackSkip();
                }

            });
        }

        titleBar.update();

        View view = titleBar.getActionView("skip");
        if (view != null) {

            view.setPadding(0, 0, BaseUtil.dp2px(getActivity(), 7), 0);
            AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, "");
        }

    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mTvSubTitle = findViewById(R.id.main_customize_subtitle);
        mVComplete = findViewById(R.id.main_button_container);
        mBtnComplete = findViewById(R.id.main_btn_complete);
        mBtnComplete.setOnClickListener(this);
        mVComplete.setVisibility(View.INVISIBLE);
        AutoTraceHelper.bindData(mBtnComplete, mSelectData);
        mLayoutAge = findViewById(R.id.main_layout_age);
        mAgeSelector = findViewById(R.id.main_age_selector);
        mAgeSelector.setOnValueChangeListener(new IAgeSelector.OnValueChangeListener() {
            @Override
            public void onValueChanged(String value) {
                mSelectData.ageRange = value;
                new UserTracking()
                        .setSrcPage("年龄选择页")
                        .setItem("button")
                        .setItemId(value)
                        .setId("7742")
                        .statIting("pageClick");
                updateCompleteButton();
                postCustomizationInfo();
            }
        });
        mIvSelectedGender = findViewById(R.id.main_iv_selected_gender);
        mTvMale = findViewById(R.id.main_tv_male);
        mTvFemale = findViewById(R.id.main_tv_female);
        mLayoutGender = findViewById(R.id.main_layout_gender);
        mVGender = findViewById(R.id.main_rg_choose_sex);

        mVMale = findViewById(R.id.main_rb_handsome);
        mVFemale = findViewById(R.id.main_rb_beauty);
        mIvMale = findViewById(R.id.main_iv_male);
        mIvFemale = findViewById(R.id.main_iv_female);
        mVMale.setOnClickListener(this);
        mVFemale.setOnClickListener(this);
        AutoTraceHelper.bindData(mVMale, "男");
        AutoTraceHelper.bindData(mVFemale, "女");
        
        updatePageUi();
        trackPageVisible();
        if (getView() != null) {
            getView().postDelayed(new Runnable() {
                @Override
                public void run() {
                    mGenderPos = new int[2];
                    mIvSelectedGender.getLocationInWindow(mGenderPos);
                    ViewGroup.LayoutParams lp = mIvSelectedGender.getLayoutParams();
                    mGenderH = lp.height;
                    mGenderW = lp.width;
                    mGenderR = BaseUtil.dp2px(mContext, 100) - BaseUtil.dp2px(mContext, 8);

                    lp = mIvMale.getLayoutParams();
                    mSrcHeight = lp.height;
                    mSrcWidth = lp.width;
                    mHGap = Math.abs(mGenderH - mSrcHeight);
                    mWGap = Math.abs(mGenderW - mSrcWidth);

                    ((View) mAgeSelector).setTranslationY(mAgeSelectorTranslate);
                    mLayoutAge.setVisibility(View.VISIBLE);
                }
            }, 200);
        }

    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_simple_customization;

    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    private void toGenderPage() {
//        if (isABTest) {
//            reset();
//        }

        if (mIvFemale != null) {

            mIvFemale.setVisibility(View.VISIBLE);
        }

        if (mTvFemale != null) {

            mTvFemale.setVisibility(View.VISIBLE);
        }

        if (mIvMale != null) {

            mIvMale.setVisibility(View.VISIBLE);
        }

        if (mTvMale != null) {

            mTvMale.setVisibility(View.VISIBLE);
        }

        if (mIvSelectedGender != null) {
            mIvSelectedGender.setVisibility(View.GONE);
        }

        ((View) mAgeSelector).setTranslationY(mAgeSelectorTranslate);

        mStep = PAGE_GENDER;
        updatePageUi();
        trackPageVisible();
    }

    private void toAgePage() {

        if (mIvFemale != null) {

            mIvFemale.setVisibility(View.GONE);
        }

        if (mTvFemale != null) {

            mTvFemale.setVisibility(View.GONE);
        }

        if (mIvMale != null) {

            mIvMale.setVisibility(View.GONE);
        }

        if (mTvMale != null) {

            mTvMale.setVisibility(View.GONE);
        }

        if (mIvSelectedGender != null) {
            mIvSelectedGender.setVisibility(View.VISIBLE);
        }

        ((View) mAgeSelector).setTranslationY(0);

        mStep = PAGE_AGE;

        updatePageUi();
        trackPageVisible();
    }


    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public boolean isShowTruckFloatPlayBar() {
        return false;
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        if (id == R.id.main_btn_complete) {
            postCustomizationInfo();
            updatePageUi();
            return;
        }
        //选中男
        if (id == R.id.main_rb_handsome) {
            mSelectData.gender = InterestCardModel.GENDER_MAN;
            mIvSelectedGender.setImageResource(R.drawable.main_ic_customize_male);
            toAgePage();
            new UserTracking()
                    .setSrcPage("性别选择页")
                    .setItem("button")
                    .setItemId("男")
                    .setId("7739")
                    .statIting("pageClick");
            return;
        }
        //选中女
        if (id == R.id.main_rb_beauty) {
            mSelectData.gender = InterestCardModel.GENDER_WOMAN;
            mIvSelectedGender.setImageResource(R.drawable.main_ic_customize_female);
            toAgePage();
            new UserTracking()
                    .setSrcPage("性别选择页")
                    .setItem("button")
                    .setItemId("女")
                    .setId("7739")
                    .statIting("pageClick");
            return;
        }
    }

    /**
     * 用户点击按钮提交，带loading，带回调处理
     */
    private void postCustomizationInfo() {
        if (isPosting) {
            return;
        }
        mBtnComplete.setEnabled(false);
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        isPosting = true;
        LiteCommonRequest.postCustomizationInfo(buildPostCustomizationInfoParams(), new
                IDataCallBack<JSONObject>() {
                    @Override
                    public void onSuccess(JSONObject object) {
                        isPosting = false;
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);

                        //兴趣卡片缓存到本地,要在首页刷新前保存
                        CustomizeManager.getInstance().saveLocalCustomize(mSelectData);

                        if (allowBack) { //allowBack == true 表示是设置页面进入
//                            CustomToast.showSuccessToast("修改成功");
                            addFragment(getActivity(), OneKeyRadioSettingFragment.newInstance(),
                                    0, 0, R.id.main_onekey_setting_container);
//                            CustomizeManager.getInstance().setCustomizeChange(true);
                        }
                        TempDataManager.getInstance().saveBoolean(PreferenceConstantsInHost.KEY_SHOULD_REFRESH_WHEN_RESUME, true);

                        if (!allowBack) {
                            finishFragment();
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        isPosting = false;
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        mBtnComplete.setEnabled(true);
                        ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
                    }
                });
    }

    private void addFragment(FragmentActivity activity, BaseFragment fragment,
                             int inAnim, int outAnim, int containerId) {
        if (fragment == null || fragment.isAddFix())
            return;

        if (activity == null || activity.isFinishing() || activity.isDestroyed()) return;

        fragment.setIsAdd(true);
        FragmentManager manager = getChildFragmentManager();
        if (manager == null) {
            return;
        }
        FragmentTransaction transaction = manager.beginTransaction();
        if (inAnim != 0 && outAnim != 0) {
            transaction.setCustomAnimations(inAnim, outAnim, inAnim, outAnim);
        }
        transaction.replace(containerId, fragment, TAG_ONEKEY_SETTING_FRAG);
        transaction.commitAllowingStateLoss();
    }

    public void removeCurrentFragment(Fragment frag) {
        if (getActivity() == null || getActivity().isFinishing() || frag == null) {
            return;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            if (getActivity().isDestroyed()) {
                return;
            }
        }
        FragmentManager manager = getChildFragmentManager();
        FragmentTransaction transaction = manager.beginTransaction();
        transaction.remove(frag);
        transaction.commitAllowingStateLoss();
    }

    private Map<String, String> buildPostCustomizationInfoParams() {
        Map<String, String> params = new HashMap<>();
        if (UserInfoMannage.hasLogined()) {
            params.put("uid", UserInfoMannage.getUid() + "");
        }
        params.put("deviceId", DeviceUtil.getDeviceToken(mContext));
        params.put("gender", mSelectData.gender + "");
        params.put("ageRange", mSelectData.ageRange);
        params.put("interestedCategories", "[]");
        return params;
    }

    /**
     * 刷新当前页面的UI
     */
    private void updatePageUi() {
        switch (mStep) {
            case PAGE_GENDER:
                mLayoutGender.setVisibility(View.VISIBLE);

                mVComplete.setVisibility(View.INVISIBLE);
                setTitle("你是？");
                if (allowBack) {
                    mTvSubTitle.setText("只需三步找你爱听");
                    titleBar.getBack().setVisibility(View.VISIBLE);
                } else {
                    mTvSubTitle.setText("只需两步找你爱听");
                    titleBar.getBack().setVisibility(View.INVISIBLE);
                }
                mVFemale.setClickable(true);
                mVMale.setClickable(true);
                break;
            case PAGE_AGE:

                mVComplete.setVisibility(View.INVISIBLE);
                updateCompleteButton();
                setTitle("你是？");
                mTvSubTitle.setText("和同龄人一起听更有趣");
                mVFemale.setClickable(false);
                mVMale.setClickable(false);
                titleBar.getBack().setVisibility(View.VISIBLE);
                break;
            default:
                break;
        }
    }

    /**
     * 页面曝光埋点
     */
    private void trackPageVisible() {
        switch (mStep) {
            case PAGE_GENDER:
                new UserTracking()
                        .setItem("性别选择页")
                        .setId("7737")
                        .statIting("viewItem");
                break;
            case PAGE_AGE:
                new UserTracking()
                        .setItem("年龄选择页")
                        .setId("7740")
                        .statIting("viewItem");
                break;
            default:
                break;
        }
    }

    /**
     * 跳过埋点
     */
    private void trackSkip() {
        switch (mStep) {
            case PAGE_GENDER:
                new UserTracking()
                        .setSrcPage("性别选择页")
                        .setItem("button")
                        .setItemId("跳过")
                        .setId("7738")
                        .statIting("pageClick");
                break;
            case PAGE_AGE:
                new UserTracking()
                        .setSrcPage("年龄选择页")
                        .setItem("button")
                        .setItemId("跳过")
                        .setId("7741")
                        .statIting("pageClick");
                break;
            default:
                break;
        }
    }

    private void updateCompleteButton() {
        if (mSelectData.ageRange != null) {
            mBtnComplete.setEnabled(true);
            mBtnComplete.setText("进入喜马拉雅，听到更多精彩");
        } else {
            mBtnComplete.setEnabled(false);
            mBtnComplete.setText("上下滑动选择年龄段");
        }
    }

    @Override
    public boolean onBackPressed() {
        if (!mNeedFinishFrag && canUpdateUi() && getChildFragmentManager() != null) {
            Fragment frag = getChildFragmentManager().findFragmentByTag(TAG_ONEKEY_SETTING_FRAG);
            if (frag != null) {
                removeCurrentFragment(frag);
                return true;
            }
        }

        //每次return前mIsFinishFragment重新置成false
        if (mStep == PAGE_AGE && !mIsFinishFragment) {
            //在兴趣卡片页，触发返回按键，并且不是finishFragment操作的时候，用户返回操作，选中年龄选择页面
            mIsFinishFragment = false;
            toGenderPage();
            return true;
        }

        if (!mIsFinishFragment && !allowBack) {
            return true;
        }

        mIsFinishFragment = false;
        return super.onBackPressed();
    }

    @Override
    protected void finishFragment() {
        mIsFinishFragment = true;
        super.finishFragment();
    }

    public void finishMy() {
        mNeedFinishFrag = true;
        finishFragment();
    }
}
