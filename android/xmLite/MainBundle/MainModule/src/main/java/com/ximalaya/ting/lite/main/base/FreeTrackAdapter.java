package com.ximalaya.ting.lite.main.base;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.adapter.track.base.ITrackAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.model.album.TagResult;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.trace.TraceFreeTrackManager;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.view.CenterAlignImageSpan;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.shareservice.base.IShareDstType;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.UiUtil;
import com.ximalaya.ting.lite.main.utils.ShareUtilsInMain;

import java.util.ArrayList;
import java.util.List;


/**
 * 父类不做过多的业务逻辑处理，比如点击监听事件处理等。主要做方法的约定、变量的声明。
 * Created by hovi on 16/8/1.
 *
 * <AUTHOR>
 */
public class FreeTrackAdapter extends HolderAdapter<TrackM> implements ITrackAdapter {

    protected int mPlaysource = ConstantsOpenSdk.PLAY_FROM_NONE;
    private BaseFragment2 mFragment;

    public FreeTrackAdapter(Context context, List<TrackM> listData, BaseFragment2 fragment2) {
        super(context, listData);
        this.mFragment = fragment2;
        XmPlayerManager.getInstance(mFragment.getActivity()).addPlayerStatusListener(this);
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_recommend_track_style_v3;
    }

    /**
     * 建立视图Holder
     *
     * @param convertView
     * @return
     */
    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public Object getItem(int position) {
        Object obj = super.getItem(position);
        if (obj != null && obj instanceof Track && mPlaysource > 0) {
            ((Track) obj).setPlaySource(mPlaysource);
        }
        return obj;
    }


    /**
     * 绑定数据
     * 只处理所有声音条通用的数据
     *
     * @param h
     * @param track
     * @param position
     */
    @Override
    public void bindViewDatas(BaseViewHolder h, TrackM track, int position) {
        ViewHolder holder = (ViewHolder) h;

        if (track == null) {
            return;
        }
        ImageManager.from(context).displayImage(holder.ivTrackCover, track.getValidCover(), R.drawable.host_default_album_145);
        // 根据状态设置标题颜色和前面的图标
        AnimationUtil.stopAnimation(holder.ivPlayBtn);
        holder.ivPlayBtn.setImageResource(R.drawable.main_btn_feed_stream_track_play_v3);
        if (isCurrentTrack(track.getDataId())) {
            holder.tvTitle.setTextColor(Color.parseColor("#f86442"));
            if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying()) {
                holder.ivPlayBtn.setImageResource(R.drawable.main_btn_feed_stream_track_pause_v3);
            } else if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isBuffering()) {
                holder.ivPlayBtn.setImageResource(R.drawable.main_img_feed_stream_track_loading);
                AnimationUtil.rotateView(mFragment.getActivity(), holder.ivPlayBtn);
            }
        } else {
            holder.tvTitle.setTextColor(Color.parseColor("#333333"));
        }
        if (track.getPlayCount() > 0) {
            holder.playCountContainer.setVisibility(View.VISIBLE);
            holder.playCount.setText(StringUtil.getFriendlyNumStr(track.getPlayCount()));
        } else {
            holder.playCountContainer.setVisibility(View.GONE);
        }
        //声音时长
        if (track.getDuration() > 0) {
            holder.durationContainer.setVisibility(View.VISIBLE);
            holder.duration.setText(StringUtil.toTime(track.getDuration()));
        } else {
            holder.durationContainer.setVisibility(View.GONE);
        }
        //展示标签
        String albumTagSourceListString = getTrackTagSourceListString(track);
        if (!TextUtils.isEmpty(albumTagSourceListString)) {
            holder.mAlbumTagSourceList.setText(albumTagSourceListString);
            holder.mAlbumTagSourceList.setVisibility(View.VISIBLE);
        } else {
            holder.mAlbumTagSourceList.setVisibility(View.GONE);
        }
        StringBuilder stringBuilder = new StringBuilder();
        // 播放中，显示音波动效
        if (isCurrentTrackPlaying(track.getDataId())) {
            holder.ivPlayingIconBeforeTitle.setVisibility(View.VISIBLE);
            ((AnimationDrawable) holder.ivPlayingIconBeforeTitle.getDrawable()).start();
            stringBuilder.append("%").append(track.getTrackTitle());
        } else {
            AnimationUtil.stopAnimation(holder.ivPlayingIconBeforeTitle);
            holder.ivPlayingIconBeforeTitle.setVisibility(View.GONE);
            stringBuilder.append(track.getTrackTitle());
        }
        SpannableString spannableString = new SpannableString(stringBuilder.toString());
        if (stringBuilder.toString().startsWith("%")) {
            ToolUtil.updateSpannableStringByDrawable(spannableString, context,
                    R.drawable.host_background_transparent, (int) UiUtil.dp2px(18), (int) UiUtil.dp2px(14));
        }
        holder.tvTitle.setText(spannableString);
        if (track.getAlbumM() != null && !TextUtils.isEmpty(track.getAlbumM().getAlbumTitle())) {
            holder.tvAlbumName.setText(track.getAlbumM().getAlbumTitle());
            holder.tvAlbumName.setVisibility(View.VISIBLE);
        } else {
            holder.tvAlbumName.setVisibility(View.GONE);
        }

        holder.ivPlayBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean isCurrentTrackPlaying = isCurrentTrackPlaying(track.getDataId());
                if (isCurrentTrackPlaying) {
                    XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).pause();
                } else {
                    play(track, v, position, getListData());
                }
                TraceFreeTrackManager.INSTANCE.homeTrackPlayOrPauseView(track.getDataId());
            }
        });
        AutoTraceHelper.bindData(holder.ivTrackCover, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, track));
        holder.rootView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                play(track, v, position, getListData());
                mFragment.showPlayFragment(v, PlayerManager.PLAY_TAG);
                TraceFreeTrackManager.INSTANCE.homeTrackItemClickView(track.getDataId());
            }
        });
        AutoTraceHelper.bindData(holder.rootView, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, track));
        holder.ivShareToWeChat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                shareToWeChat(track);
                TraceFreeTrackManager.INSTANCE.homeTrackItemPageView(track.getDataId());
            }
        });
        AutoTraceHelper.bindData(holder.ivShareToWeChat, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, track));
    }

    /**
     * 获取标签，只取第一个
     */
    private String getTrackTagSourceListString(TrackM trackM) {
        List<TagResult> tagResults = trackM.getShowTagList();
        if (CollectionUtil.isNullOrEmpty(tagResults)) {
            return null;
        }
        TagResult tagResult = tagResults.get(0);
        if (tagResult != null) {
            return tagResult.getTagName();
        }
        return null;
    }

    public void play(TrackM track, View view, int position, List<TrackM> trackItemList) {
        if (track == null) {
            return;
        }
        if (isCurrentTrackPlaying(track.getDataId())) {
        } else if (isCurrentTrack(track.getDataId())) {
            XmPlayerManager.getInstance(mFragment.getActivity()).play();
        } else {
            if (trackItemList != null) {
                int index = trackItemList.indexOf(track);
                PlayTools.playList(mFragment.getActivity(), new ArrayList<>(trackItemList), index, false, view);
            }
        }
    }

    private void shareToWeChat(Track curTrack) {
        String channelAb = ABTest.getString("SharingChannelsABtest", "1");
        if ("1".equals(channelAb)) {
            ShareUtilsInMain.shareTrackWithoutXdcs(mFragment.getActivity(), curTrack, IShareDstType.SHARE_TYPE_WX_FRIEND,
                    ICustomShareContentType.SHARE_TYPE_TRACK);
        } else {
            ShareUtilsInMain.shareTrackWithoutXdcs(mFragment.getActivity(), curTrack, ICustomShareContentType.SHARE_TYPE_TRACK);
        }
    }


    private boolean isCurrentTrack(long trackId) {
        PlayableModel curModel = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getCurrSound();
        if (curModel == null) {
            return false;
        }
        if (curModel.getDataId() <= 0) {
            return false;
        }
        return curModel.getDataId() == trackId;
    }

    private boolean isCurrentTrackPlaying(long trackId) {
        return isCurrentTrack(trackId) && XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying();
    }

    public void setPlaySource(int playSource) {
        this.mPlaysource = playSource;
    }

    /**
     * 声音条播放
     *
     * @param track      当前声音
     * @param showPlay   是否显示播放页
     * @param confirmNet 是否确认流量弹框 true显示流量弹框,false直接播放用在离线
     * @param view       声音条view
     */
    @Override
    public void play(Track track, boolean showPlay, boolean confirmNet, View view) {
        if (track == null) {
            return;
        }
        if (PlayTools.isCurrentTrackPlaying(context, track)) {//暂停
            XmPlayerManager.getInstance(context).pause();
        } else if (PlayTools.isCurrentTrack(context, track)) {
            XmPlayerManager.getInstance(context).play();
        } else {
            if (listData != null && !listData.isEmpty()) {
                int index = listData.indexOf(track);
                if (index >= 0 && index < listData.size())
                    if (confirmNet) {
                        PlayTools.playList(context, new ArrayList<>(listData), index, showPlay, view);
                    } else {
                        PlayTools.playListWithoutWifi(context, new ArrayList<>(listData), index, false, view);
                    }
            } else {
                if (confirmNet) {
                    PlayTools.playTrack(context, track, showPlay, view);
                } else {
                    PlayTools.playTrackWithoutWifi(context, track, false, view);
                }
            }
        }
    }

    @Override
    public void onPlayStart() {
        notifyDataSetChanged();
    }

    @Override
    public void onPlayPause() {
        notifyDataSetChanged();
    }

    @Override
    public void onPlayStop() {

    }

    @Override
    public void onSoundPlayComplete() {
        if (!XmPlayerManager.getInstance(context).hasNextSound()) {
            notifyDataSetChanged();
        }
    }

    @Override
    public void onSoundPrepared() {
    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {

    }

    @Override
    public void onBufferingStart() {
        notifyDataSetChanged();
    }

    @Override
    public void onBufferingStop() {
        notifyDataSetChanged();
    }

    @Override
    public void onBufferProgress(int percent) {
    }

    @Override
    public void onPlayProgress(int currPos, int duration) {
    }

    @Override
    public boolean onError(XmPlayerException exception) {
        return false;
    }


    @Override
    public void onStartGetAdsInfo() {
        notifyDataSetChanged();
    }

    @Override
    public void onGetAdsInfo(AdvertisList ads) {
    }

    @Override
    public void onAdsStartBuffering() {
    }

    @Override
    public void onAdsStopBuffering() {
    }

    @Override
    public void onStartPlayAds(Advertis ad, int position) {
        notifyDataSetChanged();
    }

    @Override
    public void onCompletePlayAds() {

    }

    @Override
    public void onError(int what, int extra) {

    }

    public BaseViewHolder createViewHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void download(Track track, View view) {

    }

    @Override
    public boolean canDownload(Track track) {
        return false;
    }

    @Override
    public void onClick(View view, TrackM track, int position, BaseViewHolder holder) {

    }

    /**
     * 申明所有声音条通用的布局控件，但是具体的findview在子类完成
     */
    public static class ViewHolder extends HolderAdapter.BaseViewHolder {
        View rootView;
        TextView tvTitle;
        RelativeLayout llShare;
        ImageView ivShareToWeChat;
        ImageView ivTrackCover;
        ImageView ivPlayBtn;
        ImageView ivPlayingIconBeforeTitle;
        View playCountContainer;
        View durationContainer;
        TextView playCount;
        TextView duration;
        TextView mAlbumTagSourceList;
        TextView tvAlbumName;

        ViewHolder(View convertView) {
            rootView = convertView;
            tvTitle = convertView.findViewById(R.id.main_tv_title);
            llShare = convertView.findViewById(R.id.main_ll_share);
            tvAlbumName = convertView.findViewById(R.id.main_tv_album_name);
            ivShareToWeChat = convertView.findViewById(R.id.main_iv_share_to_wechat);
            ivTrackCover = convertView.findViewById(R.id.main_iv_track_cover);
            ivPlayBtn = convertView.findViewById(R.id.main_iv_play_btn);
            ivPlayingIconBeforeTitle = convertView.findViewById(R.id.main_iv_playing_icon_before_title);
            playCountContainer = convertView.findViewById(R.id.main_play_times_num_container);
            durationContainer = convertView.findViewById(R.id.main_tv_total_time_container);
            playCount = convertView.findViewById(R.id.main_play_times_num);
            duration = convertView.findViewById(R.id.main_tv_total_time);
            mAlbumTagSourceList = convertView.findViewById(R.id.main_tag_source_list);
        }
    }
}
