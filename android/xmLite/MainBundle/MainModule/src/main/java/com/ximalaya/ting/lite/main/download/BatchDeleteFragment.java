package com.ximalaya.ting.lite.main.download;


import android.annotation.SuppressLint;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.view.View;
import android.widget.AdapterView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.FileSizeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 批量删除已下载.
 * Use the {@link BatchDeleteFragment#newInstance} factory method to
 * create an instance of this fragment.
 *
 * Created by Chang.Xiao on 2017/11/24.
 * <AUTHOR>
 *
 * @version 1.0
 */
public class BatchDeleteFragment extends BaseFragment2 implements View.OnClickListener, AdapterView.OnItemClickListener {

    private @Nullable
    RefreshLoadMoreListView mListView;
    private @Nullable
    DownloadedTrackAdapter mAdapter;
    private View mSelectControlLayout;
    private ProgressBar mLoadProgress;
    private TextView mTopSizeTip;
    private TextView mSelectAll;
    private TextView mSelectListened;
    private View mBottomControlLayout;
    private TextView mCancelSelect;
    private LinearLayout mBatchDelate;
    private TextView mBatchDeleteTrack;
    private TextView mBottomSizeTip;

    private List<Track> mTrackList;
    private long availableMemorySize;
    private long allocateMemorySize;

    public BatchDeleteFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_RELATIVELAYOUT, null);
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @return A new instance of fragment BatchDeleteFragment.
     * @param trackList
     */
    // TODO: Rename and change types and number of parameters
    public static BatchDeleteFragment newInstance(ArrayList<Track> trackList) {
        BatchDeleteFragment fragment = new BatchDeleteFragment();
        Bundle args = new Bundle();
        args.putParcelableArrayList("trackList", trackList);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_top_layout;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public boolean isShowTruckFloatPlayBar() {
        return false;
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null){
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("批量删除");
        initViews();
        initListView();
    }

    private void parseBundle() {
        Bundle bundle = getArguments();
        if (null != bundle) {
            mTrackList = bundle.getParcelableArrayList("trackList");
        }
    }

    private void initViews() {
        mSelectControlLayout = findViewById(R.id.main_rl_select_control_layout);
        mLoadProgress = (ProgressBar) findViewById(R.id.main_load_progress);
        mTopSizeTip = (TextView) findViewById(R.id.main_tv_top_size_tip);
        mSelectAll = (TextView) findViewById(R.id.main_tv_select_all);
        mSelectListened = (TextView) findViewById(R.id.main_tv_select_listened);
        mCancelSelect = (TextView) findViewById(R.id.main_tv_cancel_select);
        mBottomControlLayout = findViewById(R.id.main_rl_bottom_control_layout);
        mBatchDelate = (LinearLayout) findViewById(R.id.main_ll_batch_delate);
        mBatchDeleteTrack = (TextView) findViewById(R.id.main_batch_delete_track);
        mBottomSizeTip = (TextView) findViewById(R.id.main_tv_bottom_size_tip);

        mSelectAll.setOnClickListener(this);
        mSelectListened.setOnClickListener(this);
        mCancelSelect.setOnClickListener(this);
        mBatchDelate.setOnClickListener(this);
        AutoTraceHelper.bindData(mSelectAll,"");
        AutoTraceHelper.bindData(mSelectListened,"");
        AutoTraceHelper.bindData(mCancelSelect,"");
        AutoTraceHelper.bindData(mBatchDelate,"");
    }

    private void initListView() {
        mListView = (RefreshLoadMoreListView) findViewById(R.id.main_listview);
        mListView.setMode(PullToRefreshBase.Mode.DISABLED);
        mListView.setOnItemClickListener(this);
        mAdapter = new DownloadedTrackAdapter(mActivity, mTrackList, false);
        mAdapter.setTrackType(AbstractTrackAdapter.TYPE_DOWNLOAD);

        mListView.setAdapter(mAdapter);
        mAdapter.setBatchDelete(true);
    }

    @Override
    protected void loadData() {
        if (canUpdateUi() && mAdapter != null && mAdapter.getCount() == 0) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }

        loadDownloadedTrack();
    }

    private void loadDownloadedTrack() {
        parseBundle();
        setDataForView(mTrackList);
    }

    private void setDataForView(final List<Track> trackList) {
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (ToolUtil.isEmptyCollects(trackList)) {
                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                } else {
                    onPageLoadingCompleted(LoadCompleteType.OK);
                    if (mAdapter != null) {
                        mAdapter.clear();
                        mAdapter.addListData(trackList);
                    }
                }
                refreshProgress();
                updateControlLayout();
                updateBottomSizeTip();
            }
        });
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_batch_delete;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();

        refreshProgress();
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        int index = position
                - mListView.getRefreshableView().getHeaderViewsCount();
        if (mAdapter == null || index < 0 || mAdapter.getCount() <= index)
            return;

        Track track = (Track) mAdapter.getItem(index);

        if (track != null && mAdapter.getBatchDelete()) {
            if (track.isChecked()) {
                track.setChecked(false);
                if (mAdapter.isSelectAll()) {
                    mAdapter.setSelectAll(false);
                    mSelectAll.setCompoundDrawablesWithIntrinsicBounds(
                            ContextCompat.getDrawable(mContext, R.drawable.main_uncheck_delete), null, null, null);
                } else if (mAdapter.isSelectListened()) {
                    if (mAdapter.isListenerOver(track)) {
                        mAdapter.setSelectListened(false);
                        mSelectListened.setCompoundDrawablesWithIntrinsicBounds(
                                LocalImageUtil.getDrawable(mContext, R.drawable.main_uncheck_delete), null, null, null);
                    }
                } else if (mAdapter.isAllCheckedListenerOver()) {
                    mAdapter.setSelectListened(true);
                    mSelectListened.setCompoundDrawablesWithIntrinsicBounds(
                            LocalImageUtil.getDrawable(mContext, R.drawable.main_check_delete), null, null, null);
                }
            } else {
                track.setChecked(true);
                if (isAllChecked(mAdapter)) {
                    mAdapter.setSelectListened(false);
                    mSelectListened.setCompoundDrawablesWithIntrinsicBounds(
                            LocalImageUtil.getDrawable(mContext, R.drawable.main_uncheck_delete), null, null, null);

                    mAdapter.setSelectAll(true);
                    mSelectAll.setCompoundDrawablesWithIntrinsicBounds(
                            ContextCompat.getDrawable(mContext, R.drawable.main_check_delete), null, null, null);
                } else if (mAdapter.isAllCheckedListenerOver()) {
                    mAdapter.setSelectListened(true);
                    mSelectListened.setCompoundDrawablesWithIntrinsicBounds(
                            LocalImageUtil.getDrawable(mContext, R.drawable.main_check_delete), null, null, null);
                } else {
                    mAdapter.setSelectListened(false);
                    mSelectListened.setCompoundDrawablesWithIntrinsicBounds(
                            LocalImageUtil.getDrawable(mContext, R.drawable.main_uncheck_delete), null, null, null);
                }
            }
            notifyAdapter();
            updateBottomSizeTip();
            return;
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_tv_select_all) {
            new UserTracking()
                    .setSrcPage("下载批量删除页")
                    .setSrcModule("topTool")
                    .setItem("button")
                    .setItemId("全选")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            // 全选
            if (mAdapter.isSelectAll()) {
                mAdapter.setAllSelected(false, true);
                mSelectAll.setCompoundDrawablesWithIntrinsicBounds(
                        LocalImageUtil.getDrawable(mContext, R.drawable.main_uncheck_delete), null, null, null);
            } else {
                mAdapter.setListenedSelected(false, false);
                mSelectListened.setCompoundDrawablesWithIntrinsicBounds(
                        LocalImageUtil.getDrawable(mContext, R.drawable.main_uncheck_delete), null, null, null);

                mAdapter.setAllSelected(true, true);
                mSelectAll.setCompoundDrawablesWithIntrinsicBounds(
                        LocalImageUtil.getDrawable(mContext, R.drawable.main_check_delete), null, null, null);
            }
            updateBottomSizeTip();
        } else if (id == R.id.main_tv_select_listened) {
            new UserTracking()
                    .setSrcPage("下载批量删除页")
                    .setSrcModule("topTool")
                    .setItem("button")
                    .setItemId("选择已听")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            // 选择已听完
            if (mAdapter.hasListenerOverTrack()) {
                if (mAdapter.isSelectListened()) {
                    mAdapter.setListenedSelected(false, true);
                    mSelectListened.setCompoundDrawablesWithIntrinsicBounds(
                            LocalImageUtil.getDrawable(mContext, R.drawable.main_uncheck_delete), null, null, null);
                } else {
                    mAdapter.setAllSelected(false, false);
                    mSelectAll.setCompoundDrawablesWithIntrinsicBounds(
                            LocalImageUtil.getDrawable(mContext, R.drawable.main_uncheck_delete), null, null, null);

                    mAdapter.setListenedSelected(true, true);
                    mSelectListened.setCompoundDrawablesWithIntrinsicBounds(
                            LocalImageUtil.getDrawable(mContext, R.drawable.main_check_delete), null, null, null);
                }
                updateBottomSizeTip();
            } else {
                CustomToast.showToast("暂无已听完的声音，赶紧去收听吧。");
            }
        } else if (id == R.id.main_tv_cancel_select) {
            // 取消
            cancelSelect();
        } else if (id == R.id.main_ll_batch_delate) {
            // 删除
            new DialogBuilder(getActivity()).setMessage("确定删除已选声音？").setOkBtn(new DialogBuilder.DialogCallback() {

                @Override
                public void onExecute() {
                    if (mAdapter.getListData() != null) {
                        Iterator<Track> trackIterator = mAdapter.getListData().iterator();
                        List<Track> tracks = new ArrayList<>();
                        while (trackIterator.hasNext()) {
                            Track track = trackIterator.next();
                            if (track.isChecked()) {
                                tracks.add(track);
                                trackIterator.remove();
                            }
                        }
                        if (tracks.size() > 0) {
                            RouteServiceUtil.getDownloadService().deleteDownloadedTasks(tracks);
                        }

                        notifyAdapter();
                        cancelSelect();
                        refreshProgress();
                    }
                }
            }).setCancelBtn(new DialogBuilder.DialogCallback() {

                @Override
                public void onExecute() {
                }
            }).showConfirm();
        }
    }

    @SuppressLint("StaticFieldLeak")
    public void refreshProgress() {
        new MyAsyncTask<Void, Void, Void>() {
            @Override
            protected Void doInBackground(Void... params) {
                allocateMemorySize = RouteServiceUtil.getDownloadService().getDownloadedFileSize();
                availableMemorySize = FileSizeUtil
                        .getAvailableMemorySize(RouteServiceUtil.getStoragePathManager().getCurSavePath());
                return null;
            }

            protected void onPostExecute(Void result) {
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        updateProgress();
                    }
                });

            }
        }.myexec();
    }

    /**
     * 每次下载完成后sd卡使用情况
     */
    private void updateProgress() {
        String string = "已占用"
                + StringUtil.getFriendlyFileSize(allocateMemorySize) + "/可用空间"
                + StringUtil.getFriendlyFileSize(availableMemorySize);

        int progress = (int) (allocateMemorySize * 100f / availableMemorySize);
        mLoadProgress.setProgress(progress);
        mTopSizeTip.setText(string);
    }

    private void updateControlLayout() {
        if (null != mSelectControlLayout && null != mBottomControlLayout) {
            if (null != mAdapter && mAdapter.getCount() > 0) {
                mSelectControlLayout.setVisibility(View.VISIBLE);
                mBottomControlLayout.setVisibility(View.VISIBLE);
            } else {
                mSelectControlLayout.setVisibility(View.GONE);
                mBottomControlLayout.setVisibility(View.GONE);
            }
        }
    }

    private void cancelSelect() {
        if (mAdapter.isSelectAll()) {
            mSelectAll.setCompoundDrawablesWithIntrinsicBounds(
                    LocalImageUtil.getDrawable(mContext, R.drawable.main_uncheck_delete), null, null, null);
            mAdapter.setAllSelected(false, true);
        } else if (mAdapter.isSelectListened()) {
            mSelectListened.setCompoundDrawablesWithIntrinsicBounds(
                    LocalImageUtil.getDrawable(mContext, R.drawable.main_uncheck_delete), null, null, null);
            mAdapter.setListenedSelected(false, true);
        } else {
            mAdapter.setAllSelected(false, true);
        }
        updateBottomSizeTip();
    }

    private void notifyAdapter() {
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }

    private void updateBottomSizeTip() {
        if (null != mBottomSizeTip) {
            int count = 0;
            int downloadedFileSize = 0;
            int totalTime = 0;
            List<Track> trackList = mAdapter.getListData();
            if (!ToolUtil.isEmptyCollects(trackList)) {
                for (Track track : trackList) {
                    if (track.isChecked()) {
                        count++;
                        downloadedFileSize += track.getDownloadSize();
                        totalTime += track.getDuration();
                    }
                }
            }
            mBottomSizeTip.setText("已选" + count + "首声音  " +
                    StringUtil.getFriendlyFileSize(downloadedFileSize) + " 共" + getTime(totalTime));
            if (null != mBatchDeleteTrack) {
                if (count > 0) {
                    mBatchDelate.setEnabled(true);
                    mBatchDeleteTrack.setEnabled(true);
                } else {
                    mBatchDelate.setEnabled(false);
                    mBatchDeleteTrack.setEnabled(false);
                }
            }
        }
    }

    private boolean isAllChecked(DownloadedTrackAdapter adapter) {
        for (Track track : adapter.getListData()) {
            if (!track.isChecked()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 超过999分钟，换算成x小时x分钟；
     *
     * @param seconds 秒
     * @return
     */
    private String getTime(long seconds) {
        String result = "";
        int minutes = (int) (seconds / 60);
        if (minutes > 999) {
            int hour = minutes / 60;
            int m = minutes % 60;
            result = hour + "小时" + m + "分钟";
        } else {
            return minutes + "分钟";
        }
        return result;
    }

    @Override
    public void onDestroyView() {
        if (null != mAdapter) {
            mAdapter.setAllSelected(false, false);
        }
        super.onDestroyView();
    }
}
