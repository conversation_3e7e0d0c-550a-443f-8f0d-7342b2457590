package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.trace.TraceFreeAlbumManager;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.lite.main.base.album.FreeAlbumAdapter;
import com.ximalaya.ting.lite.main.request.HttpParamsConstantsInMain;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.request.LiteUrlConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新的糖葫芦热词中内容池类型item点击进入此界面
 *
 * <AUTHOR>
 */
public class NewContentFreePoolListFragment extends BaseFragment2 implements AdapterView.OnItemClickListener {

    private RefreshLoadMoreListView mListView;
    private RelativeLayout mLlRefresh;
    private int mPageId = 1;
    private boolean mIsLoading = false;
    private FreeAlbumAdapter mAdapter;
    private ImageView vNoContent;
    private View vHeadView;
    private List<Album> albumList = new ArrayList<>();

    private String title;
    private int poolId;//内容池id

    private final IGotoTop.IGotoTopBtnClickListener mTopBtnListener = new IGotoTop.IGotoTopBtnClickListener() {
        @Override
        public void onClick(View v) {
            if (!isRealVisable()) {
                return;
            }
            if (mListView == null) {
                return;
            }
            mListView.getRefreshableView().setSelection(0);
        }
    };

    public static NewContentFreePoolListFragment newInstance(String title, int poolId) {
        Bundle args = new Bundle();
        args.putString(BundleKeyConstants.KEY_TITLE, title);
        args.putInt(BundleKeyConstants.CONTENT_POOL_ID, poolId);
        NewContentFreePoolListFragment fragment = new NewContentFreePoolListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_free_album_list;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            title = arguments.getString(BundleKeyConstants.KEY_TITLE);
            poolId = arguments.getInt(BundleKeyConstants.CONTENT_POOL_ID);
        }
        //需要标题栏，设置为可滑动返回
        setCanSlided(true);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mListView = findViewById(R.id.main_listview);
        mListView.setPullToRefreshEnabled(false);
        mLlRefresh = findViewById(R.id.ll_free_album_refresh);
        mAdapter = new FreeAlbumAdapter(mActivity, albumList);
        initHeadView();
        initFooterView();
        mListView.setAdapter(mAdapter);
        mListView.setOnItemClickListener(this);
        mLlRefresh.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mPageId++;
                loadData();
                TraceFreeAlbumManager.INSTANCE.freeVipAlbumPageChange();
            }
        });
        mListView.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                //do nothing
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (getiGotoTop() != null) {
                    getiGotoTop().setState(firstVisibleItem > 12);
                }
            }
        });
        setTitle(title);
        refresh();
    }

    private void initHeadView() {
        vHeadView = View.inflate(mContext, R.layout.main_fra_home_free_album_head_view, null);
        vHeadView.setVisibility(View.INVISIBLE);
        mListView.getRefreshableView().addHeaderView(vHeadView);
    }

    private void initFooterView() {
        LinearLayout ll = new LinearLayout(getActivity());
        ll.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        ll.setGravity(Gravity.CENTER);
        vNoContent = new ImageView(getActivity());
        vNoContent.setPadding(0, BaseUtil.dp2px(mContext, 30), 0, 0);
        vNoContent.setImageResource(R.drawable.main_bg_meta_nocontent);
        ll.addView(vNoContent);
        vNoContent.setVisibility(View.GONE);
        mListView.getRefreshableView().addFooterView(ll);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        List<Album> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (index < 0 || index >= listData.size()) {
            return;
        }
        Album album = listData.get(index);
        if (!(album instanceof AlbumM)) {
            return;
        }
        AlbumM albumM = (AlbumM) album;
        AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
        TraceFreeAlbumManager.INSTANCE.freeVipAlbumPageIntoAlbum("" + albumM.getId());
    }

    private void refresh() {
        mPageId = 1;
        if (mListView != null) {
            mListView.setFooterViewVisible(View.VISIBLE);
        }
        loadData();
    }

    @Override
    protected void loadData() {
        if (mIsLoading) {
            return;
        }
        if (canUpdateUi() && mAdapter != null) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        mIsLoading = true;

        loadGuessLikeListRefresh();
    }

    private void loadGuessLikeListRefresh() {
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstantsInMain.PARAM_UID, String.valueOf(UserInfoMannage.getUid()));
        params.put("poolId", String.valueOf(poolId));
        params.put(HttpParamsConstants.PARAM_PAGE_ID, String.valueOf(mPageId));
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, "6");
        IDataCallBack<List<AlbumM>> callBack = new IDataCallBack<List<AlbumM>>() {
            @Override
            public void onSuccess(final List<AlbumM> list) {
                mIsLoading = false;
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        try {
                            if (!canUpdateUi()) {
                                return;
                            }
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            int size = list != null ? list.size() : 0;
                            if (size == 0) {
                                mPageId = 0;
                                return;
                            }
                            AlbumM album = list.get(0);
                            if (album != null && album.getMaxPageId() == mPageId) {
                                mPageId = 0;
                            }
                            onLoadSuccess(list);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                onLoadFailure(message);
            }
        };
        LiteCommonRequest.getContentPoolData(LiteUrlConstants.newHomeContentPollUrl(), params, callBack);
    }

    /**
     * 数据加载成功
     */
    private void onLoadSuccess(final List<AlbumM> list) {
        List<Album> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (vHeadView != null) {
            vHeadView.setVisibility(View.VISIBLE);
        }
        listData.clear();
        listData.addAll(list);
        mListView.onRefreshComplete(false);
    }

    /**
     * 数据加载失败
     */
    private void onLoadFailure(String message) {
        mIsLoading = false;
        if (!canUpdateUi()) {
            return;
        }
        if (mPageId == 1) {
            mAdapter.clear();
            mListView.onRefreshComplete(true);
            mListView.setHasMoreNoFooterView(false);
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        } else {
            CustomToast.showFailToast(message);
            mListView.onRefreshComplete(true);
        }
    }

    @Override
    protected void loadDataError() {
        if (mListView != null) {
            mListView.setMode(Mode.DISABLED);
            mListView.setHasMoreNoFooterView(false);
        }
    }

    @Override
    protected void loadDataOk() {
        if (mListView != null) {
            mListView.setMode(Mode.DISABLED);
        }
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }


    @Override
    protected String getPageLogicName() {
        return "NewContentPoolListFragment";
    }

    @Override
    public void onResume() {
        super.onResume();
        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(mTopBtnListener);
        }
        TraceFreeAlbumManager.INSTANCE.freeVipAlbumPage();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
        TraceFreeAlbumManager.INSTANCE.exitFreeVipAlbumPage();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
