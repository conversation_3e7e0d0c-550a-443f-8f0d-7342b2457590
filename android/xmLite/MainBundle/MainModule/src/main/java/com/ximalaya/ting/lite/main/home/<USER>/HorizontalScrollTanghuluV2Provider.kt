package com.ximalaya.ting.lite.main.home.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.adapter.HolderAdapter
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.model.album.RecommendDiscoveryM
import kotlin.collections.ArrayList

/**
 * Created by qinhuifeng on 2021/1/11
 *
 * Desc: 新版糖葫芦，2行横滑样式
 */
class HorizontalScrollTanghuluV2Provider(val baseFragment2: BaseFragment2) : IMulitViewTypeViewAndData<HorizontalScrollTanghuluV2Provider.Holder, ArrayList<RecommendDiscoveryM>> {

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup?): View {
        return layoutInflater.inflate(R.layout.main_item_horizontal_scroll_tanghulu_v2, null)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(baseFragment2, convertView)
    }

    override fun bindViewDatas(holder: Holder?, dataList: ItemModel<ArrayList<RecommendDiscoveryM>>?, convertView: View?, position: Int) {
        if (holder == null || dataList == null) {
            return
        }
        val itemDataList = dataList.getObject()
        if (itemDataList == null || itemDataList.size == 0) {
            return
        }
        val valueList = holder.rvAdapter.valueList
        valueList.clear()
        valueList.addAll(itemDataList)
        holder.rvAdapter.notifyDataSetChanged()
    }

    class Holder(val baseFragment2: BaseFragment2, val convertView: View) : HolderAdapter.BaseViewHolder() {
        var rvList: RecyclerView
        val rvAdapter: HorizontalScrollTanghuluV2RvItemAdapter
        private val dataList: List<RecommendDiscoveryM> = arrayListOf();

        init {
            rvList = convertView.findViewById(R.id.main_rv_list)

            rvAdapter = HorizontalScrollTanghuluV2RvItemAdapter(baseFragment2, dataList);
            rvList.layoutManager = GridLayoutManager(baseFragment2.context, 2, LinearLayoutManager.HORIZONTAL, false)
            rvList.adapter = rvAdapter
        }
    }
}