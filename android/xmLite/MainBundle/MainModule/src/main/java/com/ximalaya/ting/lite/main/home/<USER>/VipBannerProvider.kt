package com.ximalaya.ting.lite.main.home.adapter

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.model.ad.BannerModel
import com.ximalaya.ting.android.host.view.BannerView
import com.ximalaya.ting.android.host.view.BannerView.OnBannerItemClickListener
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.model.vip.VipTabModel
import com.ximalaya.ting.lite.main.view.LinearGradientView
import java.lang.ref.WeakReference

/**
 * Created by dumingwei on 2020/5/11
 *
 * Desc: vip顶部的轮播图
 */
class VipBannerProvider @JvmOverloads constructor(
        private val baseFragment: BaseFragment2,
        private val vipTabModel: VipTabModel? = null,
        private val onBannerItemClickListener: OnBannerItemClickListener? = null
) : IMulitViewTypeViewAndData<VipBannerProvider.Holder, List<BannerModel>> {

    private val context: Context = MainApplication.getMyApplicationContext()
    private var mBannerView: WeakReference<BannerView?>? = null

    private var isSwapping = false

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        val view = layoutInflater.inflate(R.layout.main_item_vip_focus_image, parent, false)
        val lgvBannerBg = view.findViewById<LinearGradientView>(R.id.lgvBannerBg)
        vipTabModel?.backColor?.let {
            lgvBannerBg.setSameColor(Color.parseColor(it))
        }
        val bannerView = view.findViewById<BannerView>(R.id.bannerView)
        bannerView.setDefultCornerRadius(BaseUtil.dp2px(context, 8f))
        //动态设置宽高比
        val customBannerWidthAndHeight = BannerView.getCustomBannerWidthAndHeight(context)
        val lp = bannerView.layoutParams as RelativeLayout.LayoutParams
        lp.height = customBannerWidthAndHeight[1]
        bannerView.init(baseFragment, vipTabModel?.categoryId
                ?: BannerView.VIP_BANNER_CATEGORY_ID, false)
        return view
    }

    override fun buildHolder(convertView: View): Holder {
        val focusHolder = Holder(convertView)
        mBannerView = WeakReference(focusHolder.bannerView)
        return focusHolder
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<List<BannerModel>>, convertView: View, position: Int) {
        //设置轮播图的数据
        holder.bannerView.setData(t.getObject())
        //设置可见，会触发一次曝光埋点
        holder.bannerView.setCurrVisState(true)
        if (onBannerItemClickListener != null) {
            holder.bannerView.onBannerItemClickListener = onBannerItemClickListener
        }
    }

    fun stopAutoSwapFocusImage() {
        isSwapping = false
        mBannerView?.get()?.stopAutoSwapFocusImage()
    }

    fun startAutoSwapFocusImage() {
        onViewVisible()
        mBannerView?.get()?.startAutoSwapFocusImage()
    }

    fun release() {
        val bannerView = mBannerView?.get()
        bannerView?.removeAllListener()
        bannerView?.stopAutoSwapFocusImage()
    }

    fun setShowing(isShowing: Boolean) {
        mBannerView?.get()?.setShowing(isShowing)
    }

    class Holder(rootView: View) : BaseViewHolder() {
        var bannerView: BannerView = rootView.findViewById(R.id.bannerView)
    }

    private fun onViewVisible() {
        isSwapping = true
    }

}