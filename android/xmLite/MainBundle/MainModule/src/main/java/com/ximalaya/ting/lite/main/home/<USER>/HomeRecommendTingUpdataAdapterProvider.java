package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.model.album.HomeTingUpdateModel;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.lite.main.model.album.RecommendAlbumItem;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;
import com.ximalaya.ting.android.host.model.track.EverydayUpdateTrack;
import com.ximalaya.ting.lite.main.mylisten.view.ListenEverydayUpdateFragment;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by qinhuifeng on 2019-07-31
 * <p>
 * 听更新楼层
 *
 * <AUTHOR>
 */
public class HomeRecommendTingUpdataAdapterProvider implements IMulitViewTypeViewAndData<HomeRecommendTingUpdataAdapterProvider.ViewHolder, MainAlbumMList> {
    private Activity mActivity;
    protected BaseFragment2 mFragment;
    private HomeRecommedExtraDataProvider dataProvider;
    private IRecommendFeedItemActionListener recommendFeedItemActionListener;

    public HomeRecommendTingUpdataAdapterProvider(BaseFragment2 baseFragment2, HomeRecommedExtraDataProvider dataProvider, IRecommendFeedItemActionListener recommendFeedItemActionListener) {
        mFragment = baseFragment2;
        mActivity = baseFragment2.getActivity();
        this.dataProvider = dataProvider;
        this.recommendFeedItemActionListener = recommendFeedItemActionListener;
    }


    @Override
    public void bindViewDatas(ViewHolder viewHolder, final ItemModel<MainAlbumMList> t, View convertView, final int position) {
        if (t == null || t.object == null) {
            return;
        }
        HomeTingUpdateModel homeTingUpdateModel = t.object.getHomeTingUpdateModel();
        if (homeTingUpdateModel == null) {
            return;
        }
        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, homeTingUpdateModel);

        //设置图标日期
        Calendar calendar = Calendar.getInstance();
        int month = calendar.get(Calendar.MONTH) + 1;
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        viewHolder.mCoverMonth.setText(month + "月");
        String monthPro = "";
        if (dayOfMonth < 10) {
            monthPro = "0";
        }
        viewHolder.mCoverDay.setText(monthPro + dayOfMonth);

        //设置标题
        viewHolder.title.setText(t.object.getTitle());

        //隐藏副标题
        viewHolder.subtitle.setText(getSubTitle(homeTingUpdateModel));
        viewHolder.subtitle.setVisibility(View.VISIBLE);

        //根据状态设置标题颜色和前面的图标
        AnimationUtil.stopAnimation(viewHolder.ivPlayBtn);
        viewHolder.ivPlayBtn.setImageResource(R.drawable.main_btn_feed_stream_track_play_v2);
        if (isCurrentShowTrack(homeTingUpdateModel)) {
            if (XmPlayerManager.getInstance(mActivity).isPlaying()) {
                viewHolder.ivPlayBtn.setImageResource(R.drawable.main_btn_feed_stream_track_pause_v2);
            } else if (XmPlayerManager.getInstance(mActivity).isBuffering()) {
                viewHolder.ivPlayBtn.setImageResource(R.drawable.main_img_feed_stream_track_v2_loading);
                AnimationUtil.rotateView(mActivity, viewHolder.ivPlayBtn);
            }
        }

        //开始播放点击
        viewHolder.ivPlayBtnContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isCurrentShowTrack(homeTingUpdateModel)) {
                    if (XmPlayerManager.getInstance(mActivity).isPlaying()) {
                        XmPlayerManager.getInstance(mActivity).pause();
                    } else {
                        XmPlayerManager.getInstance(mActivity).play();
                    }
                } else {
                    List<EverydayUpdateTrack> showTracks = homeTingUpdateModel.showTracks;
                    if (showTracks != null && showTracks.size() > 0 && showTracks.get(0) != null) {
                        playTingUpdateTrack(mActivity, mFragment, homeTingUpdateModel, showTracks.get(0).getDataId(), false);
                    } else {
                        ListenEverydayUpdateFragment listenEverydayUpdateFragment = ListenEverydayUpdateFragment.newInstance();
                        mFragment.startFragment(listenEverydayUpdateFragment);
                    }
                }
            }
        });

        //设置item点击
        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ListenEverydayUpdateFragment fragment = ListenEverydayUpdateFragment.newInstance();
                mFragment.startFragment(fragment);
            }
        });
        //处理rv声音列表
        bindAlbumTrackRecyclerView(homeTingUpdateModel, viewHolder);
    }

    private String getSubTitle(HomeTingUpdateModel model) {
        if (model == null) {
            return "";
        }
        //今日更新大于等于5，展示"今日更新xx条"
        if (model.todayUpdateCnt > 5) {
            return "今日更新" + model.todayUpdateCnt + "条";
        }
        //未读声音不为0，展示"xx条节目更新"
        if (model.unreadNum != 0) {
            return model.unreadNum + "条节目更新";
        }
        //xx前更新
        if (model.lastUpdateAt == 0) {
            return "";
        }
        long diffTs = System.currentTimeMillis() - model.lastUpdateAt;
        if (diffTs < 0) {
            return "";
        }
        //分钟
        long min = diffTs / 1000 / 60;
        if (min < 60) {
            return min + "分钟前更新";
        }
        long hour = min / 60;
        if (hour < 24) {
            return hour + "小时前更新";
        }
        long day = hour / 24;
        return day + "天前更新";
    }

    /**
     * @param albumItem
     * @param viewHolder
     */
    private void bindAlbumTrackRecyclerView(HomeTingUpdateModel albumItem, ViewHolder viewHolder) {
        //初始化adapter
        if (viewHolder.mAlbumTrackList.getAdapter() == null) {
            albumItem.useProImage = true;
            //禁止滑动
            viewHolder.mAlbumTrackList.setAdapter(new HomeTingUpdateItemRvAdapter(mActivity, new ArrayList<>(), mFragment));
            viewHolder.mAlbumTrackList.setNestedScrollingEnabled(false);
            viewHolder.mAlbumTrackList.setLayoutManager(new LinearLayoutManager(mActivity));
            viewHolder.mAlbumTrackList.setItemViewCacheSize(3);
        }
        if (!(viewHolder.mAlbumTrackList.getAdapter() instanceof HomeTingUpdateItemRvAdapter)) {
            return;
        }
        HomeTingUpdateItemRvAdapter homeTingUpdateItemRvAdapter = (HomeTingUpdateItemRvAdapter) viewHolder.mAlbumTrackList.getAdapter();
        if (albumItem.showTracks == null) {
            albumItem.showTracks = new ArrayList<>();
        }
        albumItem.showTracks.clear();
        if (albumItem.allTracks != null) {
            for (int i = 0; i < albumItem.allTracks.size(); i++) {
                //只取前3条数据进行展示
                if (i >= 3) {
                    break;
                }
                EverydayUpdateTrack trackM = albumItem.allTracks.get(i);
                if (trackM == null) {
                    continue;
                }
                albumItem.showTracks.add(trackM);
            }
        }
        List<EverydayUpdateTrack> valueList = homeTingUpdateItemRvAdapter.getValueList();
        //无数据的时候也需要清除，防止上次的的声音view被复用
        valueList.clear();
        valueList.addAll(albumItem.showTracks);
        //更新adapter中的对象
        homeTingUpdateItemRvAdapter.updateHomeTingUpdateModel(albumItem);
        homeTingUpdateItemRvAdapter.notifyDataSetChanged();
        if (albumItem.useProImage && albumItem.showTracks.size() == 0) {
            //使用骨架图
            viewHolder.mProImage.setVisibility(View.VISIBLE);
            viewHolder.mAlbumTrackList.setVisibility(View.GONE);
        } else {
            albumItem.useProImage = false;
            viewHolder.mProImage.setVisibility(View.GONE);
            viewHolder.mAlbumTrackList.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 播放专辑第一首声音，不打开专辑页，不打开播放页
     */
    public static void playTingUpdateTrack(Activity activity, BaseFragment2 fragment, HomeTingUpdateModel album, long trackId, boolean isOpenPlayPage) {
        if (activity == null || album == null || album.allTracks == null || album.allTracks.size() == 0) {
            if (fragment != null) {
                ListenEverydayUpdateFragment listenEverydayUpdateFragment = ListenEverydayUpdateFragment.newInstance();
                fragment.startFragment(listenEverydayUpdateFragment);
            }
            return;
        }
        int playRealIndex = 0;
        for (int i = 0; i < album.allTracks.size(); i++) {
            TrackM trackM = album.allTracks.get(i);
            if (trackM == null) {
                continue;
            }
            if (trackM.getDataId() == trackId) {
                playRealIndex = i;
                break;
            }
        }

        CommonTrackList<EverydayUpdateTrack> trackList = new CommonTrackList<>();
        trackList.setTracks(album.allTracks);
        trackList.setPlayIndex(playRealIndex);

        Map<String, String> playerParams = new HashMap<>();
        //播放器需要使用此参数，给一个固定参数,播放器可以自动播放下一页
        playerParams.put(DTransferConstants.TRACK_BASE_URL, UrlConstants.getInstanse().getEverydaytUpdate());
        playerParams.put(DTransferConstants.TIME_LINE, album.lastTimeline + "");
        playerParams.put(DTransferConstants.TOTAL_PAGE, 100 + "");
        trackList.setTotalPage(100);
        trackList.setParams(playerParams);

        PlayTools.playCommonList(activity, trackList, playRealIndex, isOpenPlayPage, null);
    }

    private boolean isCurrentShowTrack(HomeTingUpdateModel homeTingUpdateModel) {
        if (homeTingUpdateModel == null || homeTingUpdateModel.showTracks == null) {
            return false;
        }
        PlayableModel curModel = XmPlayerManager.getInstance(mActivity).getCurrSound();
        if (curModel == null) {
            return false;
        }
        if (curModel.getDataId() <= 0) {
            return false;
        }
        if (!(curModel instanceof Track)) {
            return false;
        }
        for (int i = 0; i < homeTingUpdateModel.showTracks.size(); i++) {
            TrackM trackM = homeTingUpdateModel.showTracks.get(i);
            if (trackM.getDataId() == curModel.getDataId()) {
                return true;
            }
        }
        return false;
    }

    private boolean isCurrentShowTrackPlaying(HomeTingUpdateModel homeTingUpdateModel) {
        return isCurrentShowTrack(homeTingUpdateModel) && XmPlayerManager.getInstance(mActivity).isPlaying();
    }

    private void notifyItemAction(RecommendAlbumItem album, IRecommendFeedItemActionListener.ActionType actionType, RecommendItemNew itemData, ItemModel itemModel) {
        if (recommendFeedItemActionListener != null && album != null) {
            recommendFeedItemActionListener.onItemAction(IRecommendFeedItemActionListener.FeedItemType.ALBUM, album.getId(), actionType, album.getCategoryId(), itemData, itemModel);
        }
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_album_home_ting_update, null);
    }

    @Override
    public ViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    public class ViewHolder extends BaseAlbumAdapter.ViewHolder {
        ViewGroup ivPlayBtnContainer;
        ImageView ivPlayBtn;
        TextView mAlbumInfoFirst;
        TextView mAlbumInfoSecond;
        RecyclerView mAlbumTrackList;
        //骨架图
        ViewGroup mProImage;
        TextView mCoverMonth;
        TextView mCoverDay;

        public ViewHolder(View convertView) {
            super(convertView);
            border = convertView.findViewById(R.id.main_album_border);
            title = (TextView) convertView.findViewById(R.id.main_tv_album_title);
            subtitle = (TextView) convertView.findViewById(R.id.main_tv_album_subtitle);
            ivPlayBtn = convertView.findViewById(R.id.main_iv_play_btn);
            ivPlayBtnContainer = convertView.findViewById(R.id.main_layout_play_btn_container);
            mAlbumInfoFirst = convertView.findViewById(R.id.main_tv_album_info_first);
            mAlbumInfoSecond = convertView.findViewById(R.id.main_tv_album_info_second);
            mAlbumTrackList = convertView.findViewById(R.id.main_rv_album_track_list);
            mProImage = convertView.findViewById(R.id.main_pro_image);
            mCoverMonth = convertView.findViewById(R.id.main_ting_update_cover_month);
            mCoverDay = convertView.findViewById(R.id.main_ting_update_cover_day);
        }
    }
}
