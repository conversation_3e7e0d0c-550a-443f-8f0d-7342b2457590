package com.ximalaya.ting.lite.main.history.presenter

import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.host.db.model.ModifySkitsHistoryInfo
import com.ximalaya.ting.android.host.db.model.SkitsHistoryInfo
import com.ximalaya.ting.android.host.db.repository.SkitsHistoryRecordRepository
import com.ximalaya.ting.android.host.db.repository.SkitsHistoryRepository
import com.ximalaya.ting.android.host.db.utils.BookUtils
import com.ximalaya.ting.android.host.db.utils.SkitsHistoryUtils
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.lite.main.history.bean.SkitsHistoryList
import com.ximalaya.ting.lite.main.history.view.ISkitsHistoryView
import com.ximalaya.ting.lite.main.request.LiteUrlConstants
import org.json.JSONObject

class SkitsHistoryPresenter(val mHistoryView: ISkitsHistoryView?) : ISkitsHistoryPresenter {

    private val TAG: String = "SkitsHistoryPresenter"

    private var lastTimeMillis = 0L

    private var mIsLoadMore = false

    private val pageSize = 200

    private var isLoadData = false

    /**
     *  同步短剧记录
     */
    private var isSyncRecord = false

    private var allRequest = 0

    override fun loadData() {
        if (isLoadData) {
            FuliLogger.log(TAG, "用户短剧历史数据请求中拦截")
            return;
        }
        isLoadData = true

        if (UserInfoMannage.hasLogined()) {
            FuliLogger.log(TAG, "请求用户短剧历史数据")
            getSkitsHistoryList(lastTimeMillis, pageSize, object : IDataCallBack<SkitsHistoryList> {
                override fun onSuccess(historyList: SkitsHistoryList?) {
                    if (historyList?.dataList == null || historyList.dataList.isEmpty()) {
                        FuliLogger.log(TAG, "用户历史数据-> 空")
                        // 处理其他设备删除的短剧历史数据
                        dealLoginUserDelHistoryData(arrayListOf(), SkitsHistoryRepository.queryInUser(BookUtils.currentUserId))
                        requestNoLoginData()
                    } else {
                        printHistoryList("获取到的历史短剧列表:", historyList.dataList)
                        mHistoryView?.setTotalCount(historyList.totalCount)
                        val serviceList = historyList.dataList
                        // 设置短剧的用户id
                        for (info in serviceList) {
                            if (info != null) {
                                info.uid = BookUtils.currentUserId
                            }
                        }

                        // 处理其他设备删除的短剧历史数据
                        dealLoginUserDelHistoryData(serviceList, SkitsHistoryRepository.queryInUser(BookUtils.currentUserId))

                        // 使用服务端返回数据和本地数据作对比 找出新增数据
                        val addList: MutableList<SkitsHistoryInfo> = getAddData(serviceList, SkitsHistoryRepository.queryInUser(BookUtils.currentUserId))
                        performAddList(addList)
                        requestNoLoginData()
                    }
                    resetLoadMore()
                }

                override fun onError(code: Int, message: String) {
                    FuliLogger.log(TAG, "用户历史数据-> code:$code message:$message")
                    resetLoadMore()
                    requestNoLoginData()
                }
            })
        } else {
            FuliLogger.log(TAG, "未登录,不请求用户短剧历史数据")
            requestNoLoginData()
        }
    }

    /**
     * 执行新增数据 需要处理删除记录
     *
     * @param addList 短剧列表
     */
    private fun performAddList(addList: MutableList<SkitsHistoryInfo>) {
        if (CollectionUtil.isNullOrEmpty(addList)) {
            return
        }
        val iterator = addList.iterator()
        while (iterator.hasNext()) {
            val info = iterator.next()
            if (info == null) {
                iterator.remove()
            } else {
                val modifyInfo = SkitsHistoryRecordRepository.querySkits(info.albumId, info.skitsId)
                // 该本书本地有删除记录则不添加
                if (modifyInfo != null && modifyInfo.modifyType == BookUtils.TYPE_DEL) {
                    FuliLogger.log(TAG, "存在删除记录,不添加 ${info.videoTitle}")
                    iterator.remove()
                } else {
                    // 服务端返回数据 存本地标记用户
                    info.uid = BookUtils.currentUserId
                }
            }
        }

        // 新增短剧插入数据库
        if (CollectionUtil.isNotEmpty(addList)) {
            printHistoryList("添加历史短剧:", addList)
            SkitsHistoryRepository.insertOrReplace(addList)
        }
    }

    private fun getAddData(serviceList: MutableList<SkitsHistoryInfo>, curUserList: List<SkitsHistoryInfo>): MutableList<SkitsHistoryInfo> {
        // 无本地数据 全部是新增的
        if (CollectionUtil.isNullOrEmpty(curUserList)) {
            return serviceList
        }
        val needUpdateList: MutableList<SkitsHistoryInfo> = ArrayList()
        for (localInfo in curUserList) {
            if (localInfo == null) {
                continue
            }
            for (serviceInfo in serviceList) {
                if (serviceInfo != null && serviceInfo.albumId == localInfo.albumId) {
                    // 本地存在这本书  移除
                    serviceList.remove(serviceInfo)
                    var isNeedUpdate = false
                    // 同一本书取最近更新时间
                    if (serviceInfo.lastUpdatedTime > localInfo.lastUpdatedTime) {
                        // 更新了数据  将服务器上信息覆盖掉本地信息
                        localInfo.lastUpdatedTime = serviceInfo.lastUpdatedTime
                        localInfo.videoTitle = serviceInfo.videoTitle
                        localInfo.videoIntro = serviceInfo.videoIntro
                        localInfo.cover = serviceInfo.cover
                        localInfo.orderNum = serviceInfo.orderNum
                        localInfo.curTime = serviceInfo.curTime
                        localInfo.totalTime = serviceInfo.totalTime
                        localInfo.skitsId = serviceInfo.skitsId
                        isNeedUpdate = true
                    }
                    if (serviceInfo.state != localInfo.state) {
                        // 更新状态
                        localInfo.state = serviceInfo.state
                        isNeedUpdate = true
                    }

                    if (isNeedUpdate) {
                        needUpdateList.add(localInfo)
                    }
                    break
                }
            }
        }

        // 更新短剧时间
        if (needUpdateList.isNotEmpty()) {
            printHistoryList("更新历史短剧时间或状态:", needUpdateList)
            SkitsHistoryRepository.update(needUpdateList)
        }

        // 返回这次新增的短剧
        return serviceList
    }

    private fun requestNoLoginData() {
        // 查询本地历史数据
        val localList: MutableList<SkitsHistoryInfo> = SkitsHistoryRepository.queryInUser(BookUtils.defaultUId)
        var curUserList: MutableList<SkitsHistoryInfo> = arrayListOf()
        // 当前用户数据-已登录才有这个数据
        if (UserInfoMannage.hasLogined()) {
            curUserList = SkitsHistoryRepository.queryInUser(BookUtils.currentUserId)
        }

        dealSkitsData(localList, curUserList)
    }

    /**
     * 处理本地数据以及用户数据
     *
     * @param localList   本地推荐数据
     * @param curUserList 用户数据(只有登录了才有)
     */
    private fun dealSkitsData(localList: MutableList<SkitsHistoryInfo>, curUserList: MutableList<SkitsHistoryInfo>) {
        // 本地数据为空
        if (CollectionUtil.isNullOrEmpty(localList)) {
            if (CollectionUtil.isNullOrEmpty(curUserList)) {
                mHistoryView?.setData(null)
            } else {
                // 将线上用户数据添加到本地  有线上数据说明用户登录了
                mergeListData(localList, curUserList)

                mHistoryView?.setData(curUserList)
            }
        } else {
            // 本地有数据  服务端无数据  显示本地数据
            if (CollectionUtil.isNullOrEmpty(curUserList)) {
                // 用户已登录 本地短剧历史添加到该用户账号上  需要有"添加短剧历史操作"  未登录显示未登录历史列表即可
                if (UserInfoMannage.hasLogined()) {
                    mergeListData(localList, curUserList)
                }
                mHistoryView?.setData(localList)
            } else {
                mergeListData(localList, curUserList)
                val list: MutableList<SkitsHistoryInfo> = SkitsHistoryRepository.queryInUser(BookUtils.currentUserId)
                if (CollectionUtil.isNullOrEmpty(list)) {
                    mHistoryView?.setData(null)
                } else {
                    mHistoryView?.setData(list)
                }
            }
        }

        mHistoryView?.loadDataEnd()

        // 同步操作给服务端
        syncHistoryModifyRecord()

        isLoadData = false
    }

    /**
     * 合并短剧数据
     *
     * @param noLoginList   本地未登录用户历史数据
     * @param curUserList 本地用户数据+服务端返回数据
     */
    private fun mergeListData(noLoginList: MutableList<SkitsHistoryInfo>, curUserList: MutableList<SkitsHistoryInfo>) {
        if (CollectionUtil.isNullOrEmpty(noLoginList) && CollectionUtil.isNullOrEmpty(curUserList)) {
            return
        }
        val needUpdateList: MutableList<SkitsHistoryInfo> = ArrayList()

        if (CollectionUtil.isNotEmpty(noLoginList) && CollectionUtil.isNotEmpty(curUserList)) {
            // (只有远端数据这种情况不用处理  请求到数据后就会存入到数据库)
            // 远端本地都有数据  进行数据合并
            val iterator = curUserList.iterator()
            while (iterator.hasNext()) {
                val curUserInfo = iterator.next()
                if (curUserInfo == null) {
                    iterator.remove()
                } else {
                    for (noLoginInfo in noLoginList) {
                        if (noLoginInfo == null) {
                            continue
                        }
                        // 未登录本地数据和用户数据存在同一本书
                        if (curUserInfo.albumId == noLoginInfo.albumId) {
                            noLoginList.remove(noLoginInfo)
                            // 用户的本书操作时间小于本地时间  那才需要更新  (因为用户的数据都在数据库了)
                            if (curUserInfo.lastUpdatedTime < noLoginInfo.lastUpdatedTime) {
                                curUserInfo.lastUpdatedTime = noLoginInfo.lastUpdatedTime
                                needUpdateList.add(curUserInfo)
                            }
                            break
                        }
                    }
                }
            }
        }

        // 将剩余推荐数据添加到数据库
        if (noLoginList.isNotEmpty()) {
            //  数据倒序遍历  因为第一本书才是最后新增的  最后新增排在第一位(按照时间倒序)
            for (info in noLoginList.reversed()) {
                if (info != null) {
                    //  清除本地推荐数据的主键id   不然更新后会把推荐数据覆盖  应该是新增一份当前用户的短剧数据
                    info.id = null
                    info.uid = BookUtils.currentUserId

                    // 插入新增记录
                    SkitsHistoryUtils.operatingData(info, BookUtils.TYPE_ADD)
                }
            }
        }

        if (needUpdateList.isNotEmpty()) {
            // 更新短剧信息 更新归属者
            for (info in needUpdateList) {
                if (info != null) {
                    info.uid = BookUtils.currentUserId
                }
            }
            printHistoryList("更新短剧历史数据", needUpdateList)
            SkitsHistoryRepository.insertOrReplace(needUpdateList)
        }
    }

    private fun printHistoryList(msg: String, list: List<SkitsHistoryInfo>) {
        if (!ConstantsOpenSdk.isDebug) {
            return
        }
        if (CollectionUtil.isNotEmpty(list)) {
            for (info in list) {
                if (info != null) {
                    FuliLogger.log(TAG, msg + " skitsId:" + info.skitsId + " title:" +
                                        info.videoTitle + " uid:" + info.uid + " state:" + info.state + " time:" + info.lastUpdatedTime)
                }
            }
        }
    }

    private fun getSkitsHistoryList(lastTimeMillis: Long, pageSize: Int, callback: IDataCallBack<SkitsHistoryList>) {
        // 未登录请求单独接口
        if (!UserInfoMannage.hasLogined()) {
            callback.onError(-1, "")
            return;
        }

        val map = mutableMapOf<String, String>()
        map["lastTimeMillis"] = lastTimeMillis.toString()
        map["count"] = pageSize.toString()

        CommonRequestM.baseGetRequest(LiteUrlConstants.getSkitsHistoryListUrl(), map, callback) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            if (ret == 0) {
                JsonUtilKt.instance.toObjectOfType<SkitsHistoryList>(json.optString("data"),
                    object : TypeToken<SkitsHistoryList>() {}.type)
            } else null
        }
    }

    /**
     * 单条记录上报
     */
    private fun reportHistoryModifyRecord(info: ModifySkitsHistoryInfo, callback: IDataCallBack<Boolean>) {
        val map = mutableMapOf<String, String>()
        info.run {
            map["albumId"] = albumId.toString()
            map["trackId"] = skitsId.toString()
            map["breakPoint"] = curTime.toString()
            map["lastUpdatedTime"] = lastUpdatedTime.toString()

            CommonRequestM.basePostRequest(LiteUrlConstants.getSkitsHistoryReportUrl(), map, callback) { content ->
                val json = JSONObject(content)
                val ret = json.optInt("ret", -1)
                ret == 0
            }
        }
    }

    /**
     * 批量新增数据
     */
    private fun batchAddHistoryModifyRecord(list: MutableList<ModifySkitsHistoryInfo>, callback: IDataCallBack<Boolean>) {
        val data = JsonObject()
        val array = JsonArray()
        list.forEach {
            it.run {
                val map = JsonObject()
                map.addProperty("albumId", albumId)
                map.addProperty("trackId", skitsId)
                map.addProperty("breakPoint", curTime)
                map.addProperty("lastUpdatedTime", lastUpdatedTime)
                array.add(map)
            }
        }
        data.add("records", array)

        CommonRequestM.basePostRequestParmasToJson(LiteUrlConstants.getSkitsHistoryBatchReportUrl(),
            data, callback) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            ret == 0
        }

    }

    /**
     * 批量处理数据  包括删除  新增
     */
    private fun batchDelHistoryModifyRecord(list: MutableList<ModifySkitsHistoryInfo>, callback: IDataCallBack<Boolean>) {
        val data = JsonObject()
        val array = JsonArray()
        list.forEach {
            it.run {
                val map = JsonObject()
                map.addProperty("albumId", albumId)
                map.addProperty("trackId", skitsId)
                array.add(map)
            }
        }
        data.add("records", array)

        CommonRequestM.basePostRequestParmasToJson(LiteUrlConstants.getSkitsHistoryBatchDeleteUrl(),
            data, callback) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            ret == 0
        }
    }

    /**
     * 清除所有历史记录
     */
    override fun clearAllHistoryRecord(callback: IDataCallBack<Boolean>?) {
        val map = mutableMapOf<String, String>()
        // 框架必须要默认参数 所以加一个
        map["a"] = "0"
        CommonRequestM.basePostRequest(LiteUrlConstants.getClearAllSkitsHistoryUrl(), map, object : IDataCallBack<Boolean> {
            override fun onSuccess(result: Boolean?) {
                callback?.onSuccess(result)
                // 清除本地记录和本地操作记录
                SkitsHistoryRepository.removeAll()
                SkitsHistoryRecordRepository.removeAll()
            }

            override fun onError(code: Int, message: String?) {
                callback?.onError(code, message)
            }

        }) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            ret == 0
        }
    }

    override fun syncHistoryModifyRecord() {
        // 专门同步一个类处理同步逻辑
        if (!UserInfoMannage.hasLogined()) {
            return;
        }

        if (isSyncRecord) {
            return
        }

        val list = SkitsHistoryRecordRepository.queryAll()
        if (CollectionUtil.isNotEmpty(list)) {
            isSyncRecord = true

            FuliLogger.log(TAG, "开始同步短剧历史编辑记录")

            val addList = mutableListOf<ModifySkitsHistoryInfo>()
            val delList = mutableListOf<ModifySkitsHistoryInfo>()
            val hashSet = mutableSetOf<String>()
            var addCount = 0
            var delCount = 0

            list.forEach { modifyInfo ->
                modifyInfo.run {
                    // 同一本书 可能会有两条记录  一条是登录用户的 一条是未登录的  只保留最新的一条(列表是按照时间倒序排的)
                    val text = "${albumId}_$skitsId"
                    if (!hashSet.contains(text)) {
                        hashSet.add(text)

                        when (modifyType) {
                            BookUtils.TYPE_ADD, BookUtils.TYPE_MODIFY -> {
                                addCount++
                                addList.add(this)
                                FuliLogger.log(TAG, "上报新增历史记录 skitsId: $skitsId")
                            }
                            BookUtils.TYPE_DEL -> {
                                delCount++
                                delList.add(this)
                                FuliLogger.log(TAG, "上报删除历史记录 skitsId: $skitsId")
                            }
                        }
                    } else {
                        FuliLogger.log(TAG, "存在同一本书记录,不处理: skitsId: $skitsId")
                    }
                }
            }

            performUpload(addCount, delCount, addList, delList)
        }
    }

    override fun queryHistoryAmount(): Int {
        return SkitsHistoryRepository.queryCountByUser(BookUtils.currentUserId).toInt()
    }

    /**
     * 处理登录用户删除的短剧历史数据
     */
    private fun dealLoginUserDelHistoryData(serviceList: MutableList<SkitsHistoryInfo>, curUserList: MutableList<SkitsHistoryInfo>) {
        if (CollectionUtil.isNullOrEmpty(curUserList)) {
            return
        }

        // 线上没数据  本地有数据 清除本地数据
        if (CollectionUtil.isNullOrEmpty(serviceList)) {
            SkitsHistoryRepository.remove(curUserList)
            return
        }

        val iterator = curUserList.iterator()
        while (iterator.hasNext()) {
            val curUserInfo = iterator.next()
            if (curUserInfo == null) {
                iterator.remove()
            } else {
                var index = -1
                if (CollectionUtil.isNotEmpty(serviceList)) {
                    for (serviceInfo in serviceList) {
                        if (serviceInfo == null) {
                            continue
                        }
                        // 线上数据和本地用户数据存在同一本书
                        if (curUserInfo.albumId == serviceInfo.albumId) {
                            index = 0
                            break
                        }
                    }
                }
                // 本地有这本书 线上没有这本书
                if (index == -1) {
                    val modifyInfo = SkitsHistoryRecordRepository.querySkits(curUserInfo.albumId, curUserInfo.skitsId)
                    // 没有编辑记录 代表这本书在别的设备删除了  删除本地数据
                    if (modifyInfo == null) {
                        SkitsHistoryRepository.removeSkits(curUserInfo.albumId, curUserInfo.skitsId)
                    } else if (modifyInfo.modifyType != BookUtils.TYPE_ADD && modifyInfo.modifyType == BookUtils.TYPE_MODIFY) {
                        // 存在编辑记录 但是不是添加和编辑类型  也直接删除这本书
                        SkitsHistoryRepository.removeSkits(curUserInfo.albumId, curUserInfo.skitsId)
                    }
                }
            }
        }
    }

    private fun performUpload(addCount: Int, delCount: Int, addList: MutableList<ModifySkitsHistoryInfo>, delList: MutableList<ModifySkitsHistoryInfo>) {
        // 单条上报
        allRequest = 0;
        if (addCount == 1) {
            allRequest++
            val info = addList.first()
            info.run {
                reportHistoryModifyRecord(info, object : IDataCallBack<Boolean> {
                    override fun onSuccess(result: Boolean?) {
                        checkLoadEnd()
                        result?.let {
                            if (it) {
                                SkitsHistoryRecordRepository.remove(addList)
                            }
                            FuliLogger.log(TAG, "添加->短剧操作记录上报结果 $it")
                        }
                    }

                    override fun onError(code: Int, message: String?) {
                        FuliLogger.log(TAG, "添加->短剧操作记录上报结果 code:$code message:$message")
                        checkLoadEnd()
                    }
                })
            }
        } else if (addCount > 1) {
            allRequest++
            // 多条上报
            batchAddHistoryModifyRecord(addList, object : IDataCallBack<Boolean> {
                override fun onSuccess(result: Boolean?) {
                    checkLoadEnd()
                    result?.let {
                        if (it) {
                            SkitsHistoryRecordRepository.remove(addList)
                        }

                        FuliLogger.log(TAG, "添加->短剧操作记录上报结果 $it")
                    }
                }

                override fun onError(code: Int, message: String?) {
                    FuliLogger.log(TAG, "添加->短剧操作记录上报结果 code:$code message:$message")
                    checkLoadEnd()
                }
            })
        }

        // 上报删除
        if (delCount != 0) {
            allRequest++
            batchDelHistoryModifyRecord(delList, object : IDataCallBack<Boolean> {
                override fun onSuccess(result: Boolean?) {
                    checkLoadEnd()
                    result?.let {
                        if (it) {
                            SkitsHistoryRecordRepository.remove(delList)
                        }

                        FuliLogger.log(TAG, "删除->短剧操作记录上报结果 $it")
                    }
                }

                override fun onError(code: Int, message: String?) {
                    FuliLogger.log(TAG, "删除->短剧操作记录上报结果 code:$code message:$message")
                    checkLoadEnd()
                }
            })
        }
    }

    private fun checkLoadEnd() {
        allRequest--
        if (allRequest == 0) {
            isSyncRecord = false
        }
    }

    private fun resetLoadMore() {
        if (mIsLoadMore) {
            mIsLoadMore = false
        }
    }

    override fun loadMore() {

    }
}