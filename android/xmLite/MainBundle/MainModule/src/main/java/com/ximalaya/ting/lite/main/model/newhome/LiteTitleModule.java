package com.ximalaya.ting.lite.main.model.newhome;

import android.view.View;

/**
 * Created by du<PERSON><PERSON> on 2021/3/22
 * <p>
 * Desc:
 */

public class LiteTitleModule {

    private int categoryId;
    private LiteFloorModel titleBean;
    private View.OnClickListener moreClickListener;
    private boolean showTopDivider = true;
    private boolean showBottomDivider;

    public LiteTitleModule(int categoryId, LiteFloorModel titleBean, View.OnClickListener moreClickListener) {
        this.categoryId = categoryId;
        this.moreClickListener = moreClickListener;
        this.titleBean = titleBean;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public LiteFloorModel getTitleBean() {
        return titleBean;
    }

    public View.OnClickListener getMoreClickListener() {
        return moreClickListener;
    }

    public boolean isShowTopDivider() {
        return showTopDivider;
    }

    public void setShowTopDivider(boolean showTopDivider) {
        this.showTopDivider = showTopDivider;
    }

    public boolean isShowBottomDivider() {
        return showBottomDivider;
    }

    public void setShowBottomDivider(boolean showBottomDivider) {
        this.showBottomDivider = showBottomDivider;
    }
}
