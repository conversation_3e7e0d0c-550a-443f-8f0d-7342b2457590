package com.ximalaya.ting.lite.main.download.bean;

import android.text.TextUtils;

import com.ximalaya.ting.android.opensdk.httputil.Config;
import com.ximalaya.ting.lite.main.download.inter.ITaskInfoObserver;
import com.ximalaya.ting.lite.main.download.utils.DownloadConst;
import com.ximalaya.ting.lite.main.download.utils.TaskCode;
import com.ximalaya.ting.lite.main.download.utils.TaskUtil;

import java.io.File;

/**
 * <AUTHOR> feiwen
 * date   : 2019/4/9
 * desc   :
 */
public class TaskInfo {

    /**
     * 表示当前任务已经完成,状态值
     */
    public static final int DOWN_STATE_DONE = TaskState.STATE_DONE;
    /**
     * 表示当前状态未确定
     */
    public static final int DOWN_STATE_INVALID = TaskState.STATE_PENDING;

    /**
     * 非法的时间，或者是无设置时间
     */
    private static final long INVALID_TIME = -1;

    /**
     * 请求下载的URL
     */
    private String url;
    /**
     * 任务大小
     */
    private long size = -1;
    /**
     * 已下载的大小
     */
    private long currSize = 0;
    /**
     * 下载的文件
     */
    private File file;
    /**
     * 下载的文件文件夹路径名
     */
    private String dirPath;
    /**
     * 下载的文件名称
     */
    private String fileName;
    /**
     * 下载任务线程数
     */
    private int downloadType = DownloadConst.DOWNLOAD_TYPE_SINGLE_THREAD;
    /**
     * 下载连接类型
     */
    private int downloadConnectionType = DownloadConst.DOWNLOAD_URL_CONNECTION_NORMAL;
    /**
     * 表示任务状态
     */
    private int downloadState = DOWN_STATE_INVALID;
    /**
     * 任务的开始时间
     */
    private long startTime = INVALID_TIME;
    /**
     * 任务的完成时间（指的是下载完成并且文件合法）
     */
    private long finishTime = INVALID_TIME;
    /**
     * 是否初始化
     */
    private boolean inited = false;
    /**
     * 下载状态观察者
     */
    private ITaskInfoObserver mObserver;
    /**
     * 请求配置
     */
    private Config config;
    /**
     * 重试次数
     */
    private int retryCount;

    /**
     * 判断是否曾经下载完成过，但是，不代表文件是否存在， 该方法必须确定任务信息的来源是来自任务管理，而不是自己创建的实例类
     */
    public boolean done() {
        return downloadState == DOWN_STATE_DONE;
    }

    /**
     * 根据任务信息，判断是否已经存在了已完成的文件。
     */
    public boolean existDoneFile() {
        if (!inited) {
            init();
        }
        return TaskUtil.exist(getFile());
    }

    public void init() {
        boolean done = false;
        File result = new File(getDirPath(), getFileName());
        if (result.exists()) {
            done = true;
            setFile(result);
        }
        inited = true;
        if (done) {
            setCurrSize(result.length());
            setSize(result.length());
            return;
        }
        File temp = new File(getDirPath(), getFileName() + DownloadConst.MD_DATA_SUFFIX);
        if (temp.exists()) {
            setCurrSize(temp.length());
        }
    }

    @Override
    public boolean equals(Object o) {
        if (o == null) {
            return false;
        }

        TaskInfo info = (TaskInfo) o;
        return (dirPath + fileName).equals(info.getDirPath() + info.getFileName());
    }

    /**
     * 验证任务的信息，如果任务信息不通过，将不添加到下载管理中。
     */
    public int validation() {
        if (TextUtils.isEmpty(url)) {
            return TaskCode.ERROR_INVALID_INFO;
        }
        return TaskCode.SUCCESS;
    }

    public void setObserver(ITaskInfoObserver mObserver) {
        this.mObserver = mObserver;
    }

    public int getDownloadConnectionType() {
        return downloadConnectionType;
    }

    public void setDownloadConnectionType(int downloadConnectionType) {
        this.downloadConnectionType = downloadConnectionType;
    }

    public int getDownloadType() {
        return downloadType;
    }

    public void setDownloadType(int downloadType) {
        this.downloadType = downloadType;
    }

    public int getDownloadState() {
        return downloadState;
    }

    public void setDownloadState(int downloadState) {
        this.downloadState = downloadState;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 获取当前文件大小
     */
    public long getCurrSize() {
        if (currSize >= 0) {
            return currSize;
        }
        if (TaskUtil.exist(file)) {
            return file.length();
        }
        return 0;
    }

    public void setCurrSize(long size) {
        currSize = size;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        this.url = url;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public String getDirPath() {
        return dirPath;
    }

    public void setDirPath(String dirPath) {
        this.dirPath = dirPath;
    }

    /**
     * 键值，识别任务是否相同的标志位
     */
    public String key() {
        return getFileName() + "_" + url;
    }

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    public long getStartTime() {
        return startTime;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public Config getConfig() {
        return config;
    }

    public void setConfig(Config config) {
        this.config = config;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public static class TaskInfoBuilder {
        private String url;
        private long size = -1;
        private String dirPath;
        private String fileName;
        private int downloadType;
        private Config config;
        private int retryCount;
        private int downloadConnectionType;

        public TaskInfoBuilder setUrl(String url) {
            this.url = url;
            return this;
        }

        public TaskInfoBuilder setSize(long size) {
            this.size = size;
            return this;
        }

        public TaskInfoBuilder setDirPath(String dirPath) {
            this.dirPath = dirPath;
            return this;
        }

        public TaskInfoBuilder setFileName(String fileName) {
            this.fileName = fileName;
            return this;
        }

        public TaskInfoBuilder setDownloadType(int downloadType) {
            this.downloadType = downloadType;
            return this;
        }

        public TaskInfoBuilder setConfig(Config config) {
            this.config = config;
            return this;
        }

        public TaskInfoBuilder setRetryCount(int retryCount) {
            this.retryCount = retryCount;
            return this;
        }

        public TaskInfoBuilder setDownloadConnectionType(int downloadConnectionType) {
            this.downloadConnectionType = downloadConnectionType;
            return this;
        }

        public TaskInfo build() {
            TaskInfo taskInfo = new TaskInfo();
            taskInfo.url = url;
            taskInfo.size = size;
            taskInfo.dirPath = dirPath;
            taskInfo.fileName = fileName;
            taskInfo.downloadType = downloadType;
            taskInfo.config = config;
            taskInfo.retryCount = retryCount;
            taskInfo.downloadConnectionType = downloadConnectionType;
            return taskInfo;
        }


    }
}
