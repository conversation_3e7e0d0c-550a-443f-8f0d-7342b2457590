package com.ximalaya.ting.lite.main.manager

import android.view.View
import android.view.ViewGroup
import com.ximalaya.ting.android.adsdk.BusinessSDK
import com.ximalaya.ting.android.adsdk.external.XmLoadAdParams
import com.ximalaya.ting.android.adsdk.external.feedad.ILiteFeedAd
import com.ximalaya.ting.android.adsdk.external.feedad.ILiteFeedAdLoadCallBack
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants

object HomeFeedAdManager {

    const val TAG = "HomeFeedAdManager"

    private val mAllowRequestListeners = mutableSetOf<IAllowRequestListener>()

    private val mILiteFeedAdProvider = BusinessSDK.getInstance().liteFeedAdProvider

    private var mIsClickAd: Boolean = false

    /**
     * 底部tab切换首页可见  别的界面后退首页可见  后台回前台首页可见
     */
    fun homePageResume() {
        if (mIsClickAd) {
            FuliLogger.log(TAG, "广告详情页返回,直接拦击")
            mIsClickAd = false
            return
        }
        FuliLogger.log(TAG, "homePageResume")
        notifyAllowRequestAdListener()
    }

    /**
     * 首页下拉刷新
     */
    fun homePageRefresh() {
        FuliLogger.log(TAG, "homePageRefresh")
        notifyAllowRequestAdListener()
    }

    /**
     * 顶部tab切换首页可见
     */
    fun switchTopTabPageVisible() {
        FuliLogger.log(TAG, "switchTopTabPageVisible")
        notifyAllowRequestAdListener()
    }

    fun loadAd(slotId: String, moduleId: String, listener: ILoadAdListener) {
        val params = XmLoadAdParams(slotId)
        params.liteModuleId = moduleId
        mILiteFeedAdProvider?.requestFeedAd(params, object : ILiteFeedAdLoadCallBack {
            override fun onAdLoad(feedAD: ILiteFeedAd?) {
                if (feedAD != null) {
                    listener.loadAdSuccess(feedAD)
                } else {
                    listener.loadAdError(-1, "feedAD is null")
                }
            }

            override fun onLoadError(code: Int, message: String?) {
                listener.loadAdError(code, message ?: "message is null")
            }
        })
    }

    fun showAd(adContainer: ViewGroup, mCurFeedAD: ILiteFeedAd?, canShowCoin: Boolean, listener: IShowAdListener): Boolean {
        if (mCurFeedAD == null) {
            return false
        }
        val reportSize = ConfigureCenter.getInstance().getInt(CConstants.Group_Ad.GROUP_NAME, CConstants.Group_Ad.ITEM_HOME_FEED_AD_SHOW_REPORT_KEY, 10)
        FuliLogger.log(TAG, "reportSize:${reportSize}")

        mCurFeedAD.bindLiteAdToView(adContainer, reportSize, canShowCoin, object : ILiteFeedAd.ILiteAdInteractionListener {

            override fun onAdClicked(p0: View?, p1: ILiteFeedAd?, p2: Boolean) {
                listener.onClick(p1)
                mIsClickAd = true
            }

            override fun onAdShow(p0: ILiteFeedAd?, p1: ViewGroup?) {
                listener.onShow(p0, p1)
            }

            override fun onAdClose() {
                listener.onClose()
            }

            override fun onAdRenderFail() {
                listener.onRenderFail()
            }
        })
        return true
    }

    fun registerAllowRequestAdListener(listener: IAllowRequestListener) {
        if (!mAllowRequestListeners.contains(listener)) {
            mAllowRequestListeners.add(listener)
        }
    }

    private fun notifyAllowRequestAdListener() {
        mAllowRequestListeners.forEach {
            it.allowRequest()
        }
    }

    fun clearAllowRequestAdListener() {
        mAllowRequestListeners.clear()
    }

    fun destroy() {
        clearAllowRequestAdListener()
    }

    interface IAllowRequestListener {
        fun allowRequest()
    }

    interface ILoadAdListener {

        fun loadAdSuccess(feedAD: ILiteFeedAd)

        fun loadAdError(code: Int, message: String)
    }

    interface IShowAdListener {
        fun onRenderFail()

        fun onShow(ad : ILiteFeedAd?, p1: ViewGroup?)

        fun onClick(ad : ILiteFeedAd?)

        fun onClose()
    }
}