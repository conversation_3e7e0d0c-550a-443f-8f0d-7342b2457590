package com.ximalaya.ting.lite.main.home.adapter

import android.content.Context
import android.view.View
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder
import com.ximalaya.ting.lite.main.model.album.TanghuluHotWord
import com.ximalaya.ting.lite.main.vip.listener.AdapterDataSyncListener

/**
 * Created by du<PERSON><PERSON> on 2020/8/18.
 *
 * Desc:
 */
class TanghuluHotWordAdapter(context: Context, valueList: MutableList<TanghuluHotWord>
) : MultiRecyclerAdapter<TanghuluHotWord, SuperRecyclerHolder>(context, valueList) {

    private val TAG: String = "TanghuluHotWordAdapter"

    //点击的热词位置
    private var selectedPosition = -1

    var syncDataListener: AdapterDataSyncListener<TanghuluHotWord>? = null

    override fun createMultiViewHolder(mCtx: Context?, itemView: View, viewType: Int): SuperRecyclerHolder {
        return SuperRecyclerHolder.createViewHolder(mCtx, itemView)
    }

    override fun onBindMultiViewHolder(holder: SuperRecyclerHolder, t: TanghuluHotWord, viewType: Int, position: Int) {
        if (t.selected && (t.itemType == TanghuluHotWord.ITEM_FEED
                        || t.itemType == TanghuluHotWord.ITEM_POOL
                        || t.itemType == TanghuluHotWord.ITEM_KEY_WORD)
        ) {
            holder.setBackgroundResource(R.id.rlHotWord, R.drawable.main_bg_f39699_dp18)
            holder.setTextColorResource(R.id.tvHotWordTitle, R.color.host_color_e83f46)
        } else {
            holder.setBackgroundResource(R.id.rlHotWord, R.drawable.main_bg_dddddd_stroke_dp18)
            holder.setTextColorResource(R.id.tvHotWordTitle, R.color.main_color_333333)
        }

        val itemBean: TanghuluHotWord.ItemBean? = t.item
        val keywordName = itemBean?.name ?: ""

        if (TanghuluHotWord.NAME_ALL == keywordName) {
            holder.setVisibility(R.id.ivAllHotWord, View.VISIBLE)
        } else {
            holder.setVisibility(R.id.ivAllHotWord, View.GONE)
        }
        holder.setText(R.id.tvHotWordTitle, keywordName)

        holder.setOnItemClickListenner {
            if (syncDataListener?.isLoading() == true) {
                return@setOnItemClickListenner
            }

            if (position == selectedPosition) {
                if (selectedPosition == valueList.size - 1) {
                    syncDataListener?.syncDataOnClick(valueList, selectedPosition, valueList[selectedPosition])
                    return@setOnItemClickListenner
                } else if (valueList[selectedPosition].itemType == TanghuluHotWord.ITEM_UTING ||
                        valueList[selectedPosition].itemType == TanghuluHotWord.ITEM_H5) {

                    syncDataListener?.syncDataOnClick(valueList, selectedPosition, valueList[selectedPosition])
                    return@setOnItemClickListenner
                }
            }
            if (CollectionUtil.isNotEmpty(valueList) && position < valueList.size) {
                selectedPosition = position
                Logger.d(TAG, "clickedPosition = $selectedPosition")

                if (selectedPosition == valueList.size - 1) {
                    syncDataListener?.syncDataOnClick(valueList, selectedPosition, valueList[selectedPosition])
                } else {
                    valueList.forEach {
                        it.selected = false
                    }
                    valueList[selectedPosition].selected = true
                    notifyDataSetChanged()
                    syncDataListener?.syncDataOnClick(valueList, selectedPosition, valueList[selectedPosition])
                }
            }
        }
    }

    override fun getMultiItemViewType(model: TanghuluHotWord?, position: Int): Int {
        return 0
    }

    override fun getMultiItemLayoutId(viewType: Int): Int {
        return R.layout.main_item_hot_word_adapter_item
    }
}