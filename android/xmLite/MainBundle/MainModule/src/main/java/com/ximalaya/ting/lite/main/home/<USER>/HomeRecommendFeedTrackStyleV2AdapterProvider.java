package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTrackCookie;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;
import com.ximalaya.ting.lite.main.model.album.RecommendTrackItem;

import java.util.ArrayList;
import java.util.List;


/**
 * Created by WolfXu on 2018/5/29.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class HomeRecommendFeedTrackStyleV2AdapterProvider implements IMulitViewTypeViewAndData {

    private final BaseFragment2 mFragment;
    private final Activity mActivity;
    private final HomeRecommedExtraDataProvider dataProvider;
    private final IRecommendFeedItemActionListener recommendFeedItemActionListener;
    private final boolean mIsRecommendChannel;
    private final int pageId;

    public HomeRecommendFeedTrackStyleV2AdapterProvider(BaseFragment2 fragment, HomeRecommedExtraDataProvider dataProvider, IRecommendFeedItemActionListener recommendFeedItemActionListener) {
        mFragment = fragment;
        this.dataProvider = dataProvider;
        mActivity = fragment.getActivity();
        this.recommendFeedItemActionListener = recommendFeedItemActionListener;
        mIsRecommendChannel = dataProvider.getFrom() == BundleValueConstantsInMain.FROM_NEW_HOME_RECOMMEND;
        pageId = dataProvider.getHomeRecommendExtraViewModel() != null ? dataProvider.getHomeRecommendExtraViewModel().pageId : 0;
    }

    @Override
    public void bindViewDatas(HolderAdapter.BaseViewHolder viewHolder, final ItemModel t, View convertView, final int position) {
        if (viewHolder == null || t == null) {
            return;
        }
        if (!(t.object instanceof RecommendItemNew)) {
            return;
        }
        final RecommendItemNew recommendItem = (RecommendItemNew) t.object;
        if (!(recommendItem.getItem() instanceof RecommendTrackItem)) {
            return;
        }
        final RecommendTrackItem track = (RecommendTrackItem) recommendItem.getItem();
        final TrackViewHolder holder = (TrackViewHolder) viewHolder;
        ImageManager.from(mActivity).displayImage(mFragment, holder.ivTrackCover, track.getValidCover(), R.drawable.host_default_album);

        //设置标题
        SubordinatedAlbum album = track.getAlbum();
        String albumName = "";
        if (album != null && !TextUtils.isEmpty(album.getAlbumTitle())) {
            albumName = "专辑：" + album.getAlbumTitle();
        }
        //设置专辑名称
        holder.tvTitle.setText(albumName);
        holder.tvTitle.setVisibility(View.VISIBLE);
        //设置声音名称
        holder.tvTrackIntro.setText(track.getTrackTitle());
        holder.tvTrackIntro.setVisibility(View.VISIBLE);

        //设置声音简介
        String trackIntro = track.getTrackIntro();
        if (!TextUtils.isEmpty(trackIntro)) {
            holder.tvTrackInfo.setText(trackIntro);
            holder.tvTrackInfo.setVisibility(View.VISIBLE);
        } else {
            holder.tvTrackInfo.setText(trackIntro);
            holder.tvTrackInfo.setVisibility(View.GONE);
        }

        //资源位1，设置播放量
        int playCount = track.getPlayCount();
        holder.resInfoFirst.setText(StringUtil.getFriendlyNumStr(playCount) + "播放");
        holder.resInfoFirst.setCompoundDrawables(LocalImageUtil.getDrawable(mActivity, R.drawable.main_ic_common_play_count), null, null, null);
        holder.resInfoFirst.setVisibility(View.VISIBLE);

        holder.resInfoSecond.setText("时长" + StringUtil.toTime(track.getDuration()));
        holder.resInfoSecond.setCompoundDrawables(LocalImageUtil.getDrawable(mActivity, R.drawable.main_ic_common_play_duration), null, null, null);
        holder.resInfoSecond.setVisibility(View.VISIBLE);


        //根据状态设置标题颜色和前面的图标
        AnimationUtil.stopAnimation(holder.ivPlayBtn);
        holder.ivPlayBtn.setImageResource(R.drawable.main_btn_feed_stream_track_play_v2);

        //设置播放进度
        int lastPos = XmPlayerManager.getInstance(mActivity).getHistoryPos(track.getDataId());
        String playPercent = ToolUtil.getPlaySchedule(lastPos, track.getDuration());
        if (!TextUtils.isEmpty(playPercent)) {
            holder.hasPlayPercent.setText(playPercent);
            holder.hasPlayPercent.setVisibility(View.VISIBLE);
        } else {
            holder.hasPlayPercent.setVisibility(View.GONE);
        }

        String providerTag = track.getProviderTag();
        //是否展示订阅推荐标签
        if (!TextUtils.isEmpty(providerTag)) {
            holder.tagSubscribeUpdate.setText(providerTag);
            holder.tagSubscribeUpdate.setVisibility(View.VISIBLE);
        } else {
            holder.tagSubscribeUpdate.setVisibility(View.GONE);
        }

        if (isCurrentTrack(track.getDataId())) {
            if (XmPlayerManager.getInstance(mActivity).isPlaying()) {
                holder.ivPlayBtn.setImageResource(R.drawable.main_btn_feed_stream_track_pause_v2);
            } else if (XmPlayerManager.getInstance(mActivity).isBuffering()) {
                holder.ivPlayBtn.setImageResource(R.drawable.main_img_feed_stream_track_v2_loading);
                AnimationUtil.rotateView(mActivity, holder.ivPlayBtn);
            }
        }

        //设置头像点击
        holder.ivTrackCover.setOnClickListener(v -> {
            SubordinatedAlbum trackAlbum = track.getAlbum();
            if (trackAlbum == null) {
                return;
            }
            //点击头像跳转到专辑页面
            AlbumEventManage.startMatchAlbumFragment(trackAlbum.getAlbumId(), AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, trackAlbum.getRecSrc(), trackAlbum.getRecTrack(), -1, mActivity);
            //跳转专辑页之后返回后，实时推荐
            notifyItemAction(track, IRecommendFeedItemActionListener.ActionType.CLICK, recommendItem, t);
        });
        AutoTraceHelper.bindData(holder.ivTrackCover, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, recommendItem));

        SubordinatedAlbum trackAlbum = track.getAlbum();
        long albumId = 0;
        if (trackAlbum != null) {
            albumId = trackAlbum.getAlbumId();
        }
        long finalAlbumId = albumId;

        //设置播放按钮点击
        holder.ivPlayBtnContainer.setOnClickListener(v -> {
            if (isCurrentTrackPlaying(track.getDataId())) {
                XmPlayerManager.getInstance(mActivity).pause();
            } else {
                play(track, v, position);
                //dataProvider.notifyDataSetChanged();
            }
        });
        AutoTraceHelper.bindData(holder.ivPlayBtnContainer, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, recommendItem));
        //设置item点击
        convertView.setOnClickListener(v -> {
            //没有配置或为true，均打开播放页
            play(track, v, position);
            mFragment.showPlayFragment(v, PlayerManager.PLAY_TAG);
            dataProvider.notifyDataSetChanged();
            notifyItemAction(track, IRecommendFeedItemActionListener.ActionType.CLICK, recommendItem, t);
            statItemClick(track, position, t, recommendItem);
            if (mIsRecommendChannel) {
                // 新首页-信息流-声音item  点击事件
                new XMTraceApi.Trace()
                        .click(29668) // 用户点击时上报
                        .put("title", recommendItem.getModuleTitle())
                        .put("currPageId", String.valueOf(pageId))
                        .put("albumId", String.valueOf(finalAlbumId))
                        .put("trackId", String.valueOf(track.getDataId()))
                        .put("moduleId", String.valueOf(recommendItem.getModuleId()))
                        .put("currPage", "homePageV2")
                        .createTrace();
            } else {
                // 新首页-信息流-声音item  点击事件
                new XMTraceApi.Trace()
                        .click(29769) // 用户点击时上报
                        .put("title", recommendItem.getModuleTitle())
                        .put("currPageId", String.valueOf(pageId))
                        .put("albumId", String.valueOf(finalAlbumId))
                        .put("trackId", String.valueOf(track.getDataId()))
                        .put("moduleId", String.valueOf(recommendItem.getModuleId()))
                        .put("currPage", "homePageV2")
                        .createTrace();
            }
        });
        AutoTraceHelper.bindData(convertView, AutoTraceHelper.MODULE_DEFAULT, new AutoTraceHelper.DataWrap(position, recommendItem));

        if (mIsRecommendChannel) {
            // 新首页-信息流-声音item  控件曝光
            new XMTraceApi.Trace()
                    .setMetaId(29669)
                    .setServiceId("slipPage")
                    .put("title", recommendItem.getModuleTitle())
                    .put("currPageId", String.valueOf(pageId))
                    .put("albumId", String.valueOf(albumId))
                    .put("trackId", String.valueOf(track.getDataId()))
                    .put("moduleId", String.valueOf(recommendItem.getModuleId()))
                    .put("currPage", "homePageV2")
                    .createTrace();
        } else {
            // 新首页-信息流-声音item  控件曝光
            new XMTraceApi.Trace()
                    .setMetaId(29770)
                    .setServiceId("slipPage")
                    .put("title", recommendItem.getModuleTitle())
                    .put("currPageId", String.valueOf(pageId))
                    .put("albumId", String.valueOf(albumId))
                    .put("trackId", String.valueOf(track.getDataId()))
                    .put("moduleId", String.valueOf(recommendItem.getModuleId()))
                    .put("currPage", "homePageV2")
                    .createTrace();
        }
    }

    private void notifyItemAction(RecommendTrackItem track, IRecommendFeedItemActionListener.ActionType actionType, RecommendItemNew itemData, ItemModel itemModel) {
        if (recommendFeedItemActionListener != null && track != null) {
            recommendFeedItemActionListener.onItemAction(IRecommendFeedItemActionListener.FeedItemType.TRACK, track.getDataId(), actionType, track.getCategoryId(), itemData, itemModel);
        }
    }

    protected void statItemClick(RecommendTrackItem track, int position, ItemModel t, RecommendItemNew recommendItem) {
        UserTrackCookie.getInstance().setXmContent("flow", "homepage", "track", "");
        if (track.getRecInfo() != null) {
            UserTrackCookie.getInstance().setXmRecContent(track.getRecInfo().getRecTrack(), track.getRecInfo().getRecSrc());
        }
    }

    protected void statPlayBtnClick(boolean isCurrentTrackPlaying, RecommendTrackItem track, ItemModel t, RecommendItemNew recommendItem, int position) {
        UserTracking userTracking = new UserTracking();
        if (isCurrentTrackPlaying) {
            userTracking.setItemId("pause");
        } else {
            userTracking.setItemId("play");
            UserTrackCookie.getInstance().setXmContent("flow", "homepage", "track", "");
            if (track.getRecInfo() != null) {
                UserTrackCookie.getInstance().setXmRecContent(track.getRecInfo().getRecTrack(), track.getRecInfo().getRecSrc());
            }
        }
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_recommend_track_style_v2, parent, false);
    }

    @Override
    public HolderAdapter.BaseViewHolder buildHolder(View convertView) {
        return new TrackViewHolder(convertView);
    }

    public void play(Track track, View view, int position) {
        if (track == null) {
            return;
        }
        if (isCurrentTrackPlaying(track.getDataId())) {
        } else if (isCurrentTrack(track.getDataId())) {
            XmPlayerManager.getInstance(mActivity).play();
        } else {
            if (dataProvider != null) {
                List<ItemModel> itemList = dataProvider.getListData();
                List<Track> trackItemList = new ArrayList<>();
                for (ItemModel item : itemList) {
                    if (item != null && item.getObject() instanceof RecommendItemNew && ((RecommendItemNew) item.getObject()).getItem() instanceof RecommendTrackItem) {
                        trackItemList.add((Track) ((RecommendItemNew) item.getObject()).getItem());
                    }
                }
                List<Track> listToPlay = trackItemList;
                if (trackItemList.size() >= 200) {  //列表长度小于200直接播放，否则要对播放列表进行处理
                    //截取包含指定track在内的199条
                    int index = trackItemList.indexOf(track);
                    if ((index - 99) < 0) {
                        listToPlay = trackItemList.subList(0, 198);
                    } else if ((index + 99) >= trackItemList.size()) {
                        listToPlay = trackItemList.subList(trackItemList.size() - 199, trackItemList.size() - 1);
                    } else {
                        listToPlay = trackItemList.subList(index - 99, index + 99);
                    }
                }
                int index = listToPlay.indexOf(track);
                PlayTools.playList(mActivity, listToPlay, index, false, view);
            }
        }
    }


    private boolean isCurrentTrack(long trackId) {
        PlayableModel curModel = XmPlayerManager.getInstance(mActivity).getCurrSound();
        if (curModel == null) {
            return false;
        }
        if (curModel.getDataId() <= 0) {
            return false;
        }
        return curModel.getDataId() == trackId;
    }

    private boolean isCurrentTrackPlaying(long trackId) {
        return isCurrentTrack(trackId) && XmPlayerManager.getInstance(mActivity).isPlaying();
    }

    private static class TrackViewHolder extends HolderAdapter.BaseViewHolder {
        View rootView;
        TextView tvTitle;
        TextView tvTrackInfo; //声音简介
        TextView resInfoFirst;
        TextView resInfoSecond;
        TextView tvTrackIntro;
        LinearLayout llTrackInfo;
        ImageView ivTrackCover;
        ImageView ivPlayBtn;
        ViewGroup ivPlayBtnContainer;
        TextView hasPlayPercent;
        TextView tagSubscribeUpdate;

        TrackViewHolder(View convertView) {
            rootView = convertView;
            tvTitle = convertView.findViewById(R.id.main_tv_title);
            tvTrackInfo = convertView.findViewById(R.id.main_tv_track_info);
            resInfoFirst = convertView.findViewById(R.id.main_tv_album_info_first);
            resInfoSecond = convertView.findViewById(R.id.main_tv_album_info_second);
            tvTrackIntro = convertView.findViewById(R.id.main_tv_track_intro);
            llTrackInfo = convertView.findViewById(R.id.main_ll_track_info);
            ivTrackCover = convertView.findViewById(R.id.main_iv_track_cover);
            ivPlayBtn = convertView.findViewById(R.id.main_iv_play_btn);
            ivPlayBtnContainer = convertView.findViewById(R.id.main_layout_play_btn_container);
            hasPlayPercent = (TextView) convertView.findViewById(R.id.main_has_play_percent);
            tagSubscribeUpdate = (TextView) convertView.findViewById(R.id.main_tag_subscribe_update);
        }
    }
}
