package com.ximalaya.ting.lite.main.download;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.database.DataSetObserver;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.airbnb.lottie.LottieAnimationView;
import com.ximalaya.ting.android.downloadservice.DownLoadedAlbum;
import com.ximalaya.ting.android.downloadservice.base.BaseDownloadTask;
import com.ximalaya.ting.android.downloadservice.base.IDownloadTaskCallback;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.FileSizeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.lite.main.tab.IListenTabFragment;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
public class DownloadedAlbumListFragment extends BaseFragment2 implements IDownloadTaskCallback {

    private View mDownloadingLayout;
    private LottieAnimationView ivDownloading;
    private TextView mTvDownloadingCount;

    private TextView mTvProgress;

    private FrameLayout fmTitleBar;

    private ListView mListView;
    private DownloadAlbumAdapter mAdapter;

    //是否展示标题栏
    private boolean mNeedTitleBar = true;

    private DataSetObserver mCountObserver = new DataSetObserver() {
        @Override
        public void onChanged() {
            super.onChanged();

            refreshProgress();
        }

        @Override
        public void onInvalidated() {
            super.onInvalidated();

            refreshProgress();
        }
    };

    private boolean isRefresh;
    private boolean mPlayFirst;

    private long mAvailableMemorySize;
    private long mAllocateMemorySize;

    public DownloadedAlbumListFragment() {
        super(AppConstants.isPageCanSlide, null);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_list_no_title_no_refresh;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("我的下载");

        if (getArguments() != null) {
            mNeedTitleBar = getArguments().getBoolean(BundleKeyConstants.KEY_NEED_TITLE_BAR, true);
        }
        if (!mNeedTitleBar) {
            fmTitleBar = findViewById(R.id.main_title_bar);
            fmTitleBar.setVisibility(View.GONE);
        }

        mListView = findViewById(R.id.main_listview);
        setScrollViewListener(mListView);

        initHeader();

        mAdapter = new DownloadAlbumAdapter((MainActivity) mActivity, new ArrayList<Album>());
        mAdapter.registerDataSetObserver(mCountObserver);

        mListView.setAdapter(mAdapter);
        mListView.setDividerHeight(0);
        mListView.setOnItemClickListener(new OnItemClickListener() {

            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (OneClickHelper.getInstance().onClick(view)) {
                    int index = position - mListView.getHeaderViewsCount();
                    if (index < 0) {
                        return;
                    }
                    Object o = mAdapter.getItem(index);
                    if (o instanceof AlbumM) {
                        DownLoadedAlbum album = ((AlbumM) o).getDownLoadedAlbum();
                        if (album == null)
                            return;
                        startFragment(DownloadedAlbumDetailFragment
                                .newInstance(album.getAlbum(), album.isPaid(), album.getAnnouncerNiceName()));
                    }
                }
            }
        });

        setNoContentImageViewSize();

        if (getArguments() != null) {
            mPlayFirst = getArguments().getBoolean(BundleKeyConstants.KEY_PLAY_FIRST, false);
        }

        updateDownloadingCount();
        refreshProgress();
    }


    /**
     * ListView添加下载状态和占用空间的head
     */
    private void initHeader() {
        if (mListView == null) {
            return;
        }

        mDownloadingLayout = LayoutInflater.from(mContext).inflate(R.layout.main_layout_downloading, mListView, false);

        ivDownloading = mDownloadingLayout.findViewById(R.id.main_iv_downloading);
        ivDownloading.setAnimation("lottie/download/downloading.json");
        //ivDownloading.loop(true);
        ivDownloading.setRepeatCount(ValueAnimator.INFINITE);

        mTvDownloadingCount = mDownloadingLayout.findViewById(R.id.main_downloading_count);

        mDownloadingLayout.setVisibility(View.GONE);

        mDownloadingLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {

                DownloadingFragment downloadingFragment = new DownloadingFragment();
                startFragment(downloadingFragment);
            }
        });


        //占用了多少内存是动态添加上来的。
        mTvProgress = new TextView(mContext);

        int padding = BaseUtil.dp2px(mContext, 5);

        mTvProgress.setGravity(Gravity.START);
        mTvProgress.setTextColor(ContextCompat.getColor(mContext, R.color.main_color_999999));
        mTvProgress.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        mTvProgress.setPadding(3 * padding, padding, 0, padding);

        View divider = new View(mContext);
        divider.setBackgroundColor(ContextCompat.getColor(mContext, R.color.main_color_eaeaea));

        LinearLayout header = new LinearLayout(mContext);
        header.setOrientation(LinearLayout.VERTICAL);

        header.addView(mDownloadingLayout, new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        header.addView(mTvProgress, new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        header.addView(divider, new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, BaseUtil.dp2px(mContext, 0.5f)));

        AbsListView.LayoutParams lpHeader =
                new AbsListView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        header.setLayoutParams(lpHeader);

        mListView.addHeaderView(header, null, false);

    }

    private void updateDownloadingCount() {

        if (mDownloadingLayout == null || mTvDownloadingCount == null) {

            return;
        }

        int count = RouteServiceUtil.getDownloadService().getUnfinishedTasks().size();
        if (count > 0) {
            mDownloadingLayout.setVisibility(View.VISIBLE);
            mTvDownloadingCount.setText(String.valueOf(count));

            if (RouteServiceUtil.getDownloadService().getAllDownloadingTask().size() > 0) {
                if (!ivDownloading.isAnimating()) {
                    ivDownloading.playAnimation();
                }
            } else {
                cancelDownloadingAnimation();
            }
        } else {
            cancelDownloadingAnimation();
            mDownloadingLayout.setVisibility(View.GONE);
        }
        //updateParentFragmentDownloadCount(count);
    }

    private void cancelDownloadingAnimation() {

        if (ivDownloading == null) {

            return;
        }

        ivDownloading.cancelAnimation();
        ivDownloading.setFrame(35);
    }

    private void updateParentFragmentDownloadCount(int count) {
        if (getParentFragment() instanceof IListenTabFragment && ((IListenTabFragment) getParentFragment()).canUpdateUi()) {
            // ((IListenTabFragment) getParentFragment()).updateDownloadCount(count);
        }
    }

    private void setNoContentImageViewSize() {
        //正常尺寸为170dp
        if (getNoContentView() != null) {
            ImageView ivNoContent = getNoContentView().findViewById(R.id.image_no_content);
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) ivNoContent.getLayoutParams();
            layoutParams.width = BaseUtil.dp2px(mContext, 250);
            layoutParams.height = BaseUtil.dp2px(mContext, 250);
            ivNoContent.setScaleType(ImageView.ScaleType.CENTER_CROP);
            ivNoContent.setLayoutParams(layoutParams);
        }
    }

    @SuppressLint("StaticFieldLeak")
    public void refreshProgress() {
        new MyAsyncTask<Void, Void, Void>() {
            @Override
            protected Void doInBackground(Void... params) {
                mAllocateMemorySize = RouteServiceUtil.getDownloadService().getDownloadedFileSize();
                String path = RouteServiceUtil.getStoragePathManager().getCurSavePath();
                mAvailableMemorySize = FileSizeUtil
                        .getAvailableMemorySize(path);
                return null;
            }

            protected void onPostExecute(Void result) {
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        updateProgress();
                    }
                });

            }
        }.myexec();
    }

    /**
     * 每次下载完成后sd卡使用情况
     */
    private void updateProgress() {

        if (mTvProgress == null) {
            return;
        }

        String string = "已占用"
                + StringUtil.getFriendlyFileSize(mAllocateMemorySize) + "/可用空间"
                + StringUtil.getFriendlyFileSize(mAvailableMemorySize);

        mTvProgress.setText(string);
    }

    private void updateUi(final List<DownLoadedAlbum> albums) {
        isRefresh = false;
        if (!canUpdateUi()) {
            return;
        }
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (!RouteServiceUtil.getDownloadService().isFetchDataBase()) {
                    onPageLoadingCompleted(LoadCompleteType.OK);
                }
                List<Album> albumList = new ArrayList<>();
                for (DownLoadedAlbum album :
                        albums) {
                    if (album.getAlbum() == null)
                        continue;
                    AlbumM copy = new AlbumM();
                    copy.setIncludeTrackCount(album.getDownloadTrackCount());
                    copy.setAlbumTitle(album.getAlbum().getAlbumTitle());
                    copy.setCoverUrlMiddle(album.getAlbum().getValidCover());
                    copy.setDownLoadedAlbum(album);
                    albumList.add(copy);
                }

                if (mAdapter != null) {
                    if (albumList.size() > 0) {
                        mAdapter.clear();
                        mAdapter.addListData(albumList);
                        mAdapter.notifyDataSetChanged();
                    } else {
                        if (!RouteServiceUtil.getDownloadService().isFetchDataBase()) {
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                        }
                        mAdapter.clear();
                    }
                }
            }
        });

    }

    private static class LoadTask extends MyAsyncTask<Void, Void, List<DownLoadedAlbum>> {
        private WeakReference<DownloadedAlbumListFragment> mFragmentRef;

        LoadTask(DownloadedAlbumListFragment fragment) {
            mFragmentRef = new WeakReference<>(fragment);
        }

        @Override
        protected List<DownLoadedAlbum> doInBackground(Void... params) {
            if (mFragmentRef.get() == null) {
                return null;
            }

            return RouteServiceUtil.getDownloadService().getDownLoadedAlbumList();
        }

        @Override
        protected void onPostExecute(final List<DownLoadedAlbum> albums) {
            DownloadedAlbumListFragment fragment = mFragmentRef.get();
            if (fragment == null) {
                return;
            }

            fragment.updateUi(albums);

            if (fragment.mPlayFirst && !ToolUtil.isEmptyCollects(albums)) {
                fragment.mPlayFirst = false;

                DownLoadedAlbum downLoadedAlbum = albums.get(0);
                if (downLoadedAlbum == null) {
                    return;
                }

                DownloadedAlbumDetailFragment fra = DownloadedAlbumDetailFragment
                        .newInstance(downLoadedAlbum.getAlbum(), downLoadedAlbum.isPaid(),
                                downLoadedAlbum.getAnnouncerNiceName());

                Bundle arguments = fra.getArguments();
                if (arguments == null) {
                    arguments = new Bundle();
                }

                arguments.putBoolean(BundleKeyConstants.KEY_PLAY_FIRST, true);

                fra.setArguments(arguments);

                fragment.startFragment(fra);
            }
        }
    }

    @Override
    protected void loadData() {

        updateDownloadingCount();

        refreshProgress();

        if (isRefresh) {
            return;
        }

        if (canUpdateUi() && mAdapter != null && mAdapter.getCount() == 0) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }

        isRefresh = true;
        new LoadTask(this).myexec();
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        RouteServiceUtil.getDownloadService().registerDownloadCallback(this);
        loadData();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {

            RouteServiceUtil.getDownloadService().registerDownloadCallback(this);
            loadData();

        }
    }

    @Override
    public void onPause() {
        super.onPause();
        RouteServiceUtil.getDownloadService().unRegisterDownloadCallback(this);
    }


    @Override
    public void onDestroyView() {

        RouteServiceUtil.getDownloadService().unRegisterDownloadCallback(this);

        if (mAdapter != null && mCountObserver != null) {

            mAdapter.unregisterDataSetObserver(mCountObserver);
        }
        super.onDestroyView();
    }

    @Override
    public void onDownloadProgress(BaseDownloadTask track) {

    }

    @Override
    public void onCancel(BaseDownloadTask track) {

        updateDownloadingCount();
    }

    @Override
    public void onComplete(BaseDownloadTask track) {

        loadData();
    }

    @Override
    public void onUpdateTrack(BaseDownloadTask track) {

        loadData();
    }

    @Override
    public void onStartNewTask(BaseDownloadTask track) {

        updateDownloadingCount();
    }

    @Override
    public void onError(BaseDownloadTask track) {

        updateDownloadingCount();
    }

    @Override
    public void onDelete() {

        loadData();
    }

    @Override
    protected boolean onPrepareNoContentView() {
        setNoContentTitle("没有下载的专辑");
        return false;
    }


    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

}
