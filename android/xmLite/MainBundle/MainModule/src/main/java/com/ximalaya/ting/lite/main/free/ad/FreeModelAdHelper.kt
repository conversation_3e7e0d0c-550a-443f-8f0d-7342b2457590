package com.ximalaya.ting.lite.main.free.ad

import android.os.SystemClock
import android.text.TextUtils
import android.util.Log
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.adsdk.callback.ISimpleReawardPlayComplete
import com.ximalaya.ting.android.host.adsdk.manager.ThirdReawardAdShowManger
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.listenertask.ListenEarnCoinDialogManager
import com.ximalaya.ting.android.host.listenertask.callback.JssdkFuliRewardCallback
import com.ximalaya.ting.android.host.listenertask.callback.OnLoadOneSelfAdLoadListener
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew
import com.ximalaya.ting.android.host.manager.UnlockTimeTrackManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.model.ad.AdWrapper
import com.ximalaya.ting.android.host.model.ad.LoadReawardParams
import com.ximalaya.ting.android.host.model.earn.FuliBallDialogDataModel
import com.ximalaya.ting.android.host.model.earn.FuliBallType
import com.ximalaya.ting.android.host.model.earn.RewardCoinObtainRsp
import com.ximalaya.ting.android.host.util.constant.AdConstants
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.lite.main.manager.HomeFreeModelGuideManager
import com.ximalaya.ting.lite.main.model.RewardVideoExchangeModel
import com.ximalaya.ting.lite.main.playnew.dialog.UnlockListenTimeDialog
import java.text.SimpleDateFormat
import java.util.Date

object FreeModelAdHelper {

    //免费畅听是否打开过
    private const val KEY_UNLOCK_TIME_SHOWED = "unlock_time_showed"

    //直接打开激励视频的次数
    private const val KEY_DIRECT_TIMES: String = "key_direct_times"

    //直接打开激励视频的日期
    private const val KEY_DIRECT_DATE: String = "key_direct_date"

    //用户生命周期内，是否已经点击过免费领取时长
    private var mHasReceiveUnLockTime = false
    //是否已经回掉过广告关闭/是吧
    private var mAdFailureCall = false

    /**
     * 判断是直接打开激励视频广告，还是打开免费畅听二级页面
     */
    fun isDirectOpenAd(homeIcon: Boolean): Boolean {
        if (UnlockListenTimeManagerNew.getAvailableListeningTime() >= 14400) {
            //CustomToast.showToast("剩余时长超过4个小时，快去听书放松吧")
           return false
        }
        if (hasReceiveUnlockTime()) {
            //畅听老用户
            val directCountMax = ConfigureCenter.getInstance().getInt(
                CConstants.Group_Ad.GROUP_NAME,
                if (homeIcon) {
                    CConstants.Group_Ad.ITEM_HOME_FREE_ICON_WATCH_VIDEO_DAILY_COUNT
                } else {
                    CConstants.Group_Ad.ITEM_PLAY_FREE_ICON_WATCH_VIDEO_DAILY_COUNT
                },
                0
            )
            if (directCountMax <= 0) {
                return false
            }

            //读取本地的次数
            var tag = getTagByFrom(homeIcon)
            var count = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                .getInt(KEY_DIRECT_TIMES + tag, 0)
            val date = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                .getString(KEY_DIRECT_DATE + tag)
            val simpleDateFormat = SimpleDateFormat("yyyy_MM_dd")
            val todayDate = simpleDateFormat.format(Date())
            if (!TextUtils.equals(todayDate, date)) {
                //日期不同，次数清0
                count = 0
                MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                    .saveInt(KEY_DIRECT_TIMES + tag, count)
                MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                    .saveString(KEY_DIRECT_DATE + tag, todayDate)
            }
            //不能超过服务端的次数
            if (count < directCountMax) {
                return true
            }
        } else {
            //畅听新用户
            return false
        }
        return false
    }


    /**
     * 直接观看激励视频的次数+1
     */
    fun addClickFreeAdCount(homeIcon: Boolean) {
        var count = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
            .getInt(KEY_DIRECT_TIMES + getTagByFrom(homeIcon), 0)
        if (count < Int.MAX_VALUE) {
            count = count + 1
        }
        MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
            .saveInt(KEY_DIRECT_TIMES + getTagByFrom(homeIcon), count)
    }


    private fun getTagByFrom(homeIcon: Boolean): String {
        return if (homeIcon) {
            "home"
        } else {
            "play"
        }
    }

    /**
     * 判断用户是否已经展示过免费听二级页面，因为涉及到老版本用户，因此之前点击过就算（没有唯一标识）
     */
    fun hasReceiveUnlockTime(): Boolean {
        if (mHasReceiveUnLockTime) {
            return true
        }
        //从sp里面读取已经展示过免费听页面
        val context = BaseApplication.getMyApplicationContext()
        var has = MmkvCommonUtil.getInstance(context).getBoolean(KEY_UNLOCK_TIME_SHOWED, false)
        if (has) {
            mHasReceiveUnLockTime = true
            return true
        }
        //之前点击过就算
        val key = PreferenceConstantsInHost.REWARD_TIME_KEY
        if (MmkvCommonUtil.getInstance(context).getInt(key + "sub_freetime_inspire_video", 0) > 0) {
            mHasReceiveUnLockTime = true
            return true
        }
        return false
    }


    /**
     * 保存记录已经展示过免费听二级页面
     */
    fun saveReceiveUnlockTime() {
        MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
            .saveBoolean(KEY_UNLOCK_TIME_SHOWED, true)
    }


    /**
     * 请求广告
     */
    fun requestAd(homeIcon: Boolean, secondPage: Boolean, listener: FreeModelAdListener?) {
        val positionName = "sub_freetime_inspire_video"
        val loadRewardParams = LoadReawardParams()
        val startTime = SystemClock.elapsedRealtime()
        mAdFailureCall = false
        ThirdReawardAdShowManger.loadRewardVideoAndShow(
            BaseApplication.getTopActivity(),
            object : ISimpleReawardPlayComplete {

                override fun onAdLoadError() {
                    if(mAdFailureCall){
                        return
                    }
                    if (secondPage) {
                        CustomToast.showFailToast("激励视频数据异常")
                    }
                    listener?.onFailure()
                }

                override fun onSdkRewardAdClose() {
                    mAdFailureCall = true
                    //if (listenFragment) {
                    //    CustomToast.showFailToast("激励视频数据异常")
                    //}
                    listener?.onFailure()
                }

                override fun onAdPlayComplete() {
                    if (homeIcon) {
                        //首页右上角埋点
                        UnlockTimeTrackManager.trackClick(
                            "首页激励视频兑换",
                            SystemClock.elapsedRealtime() - startTime,
                            "激励视频观看成功"
                        )
                    } else {
                        //播放页埋点
                        UnlockTimeTrackManager.trackClick(
                            "播放页弹窗激励视频兑换",
                            SystemClock.elapsedRealtime() - startTime,
                            "激励视频看成功"
                        )
                    }
                    if (secondPage) {
                        onAdPlayCompleteSuccess()
                    }
                    listener?.onSuccess()
                }
            },
            positionName,
            loadRewardParams
        )
    }


    /**
     * 广告播放成功了，需要调用服务的接口，领取金币
     */
    fun onAdPlayCompleteSuccess() {
        HomeFreeModelGuideManager.performRewardVideoExchangeListenTime(
            false,
            object : UnlockListenTimeDialog.IRequestCallBack<RewardVideoExchangeModel> {
                override fun onResult(result: RewardVideoExchangeModel) {
                    val duration =
                        (UnlockListenTimeManagerNew.unlockConfigModel?.videoExchangeRateListenDuration
                            ?: 0) / 60
                    if (UnlockListenTimeManagerNew.getAvailableListeningTime() >= 14400) {
                        ToastManager.showToast("已领取${duration}分钟免费时长")
                    } else {
                        showSupperCommonWithAdDialog(duration)
                    }
                }
            })
    }


    private fun showSupperCommonWithAdDialog(duration: Int) {
        val dataModel = FuliBallDialogDataModel(FuliBallType.BALL_TYPE_COMMON_DIALOG, 0)
        dataModel.awardDesc = "恭喜获得" + duration + "分钟免费时长"
        dataModel.adPositionName = AdConstants.XM_AD_NAME_CSJ_SUB_FULI_COMMON_REWARD_POPUP_LARGE
        dataModel.sourceName = FuliBallType.SOURCE_NAME_FREE_LISTEN_PAGE
        dataModel.awardTime = duration

        val loadRewardParams = LoadReawardParams()
        loadRewardParams.sourceName = dataModel.sourceName
        ListenEarnCoinDialogManager.getInstance()
            .loadFuliDialogButtomAdWithAdx(
                dataModel.adPositionName,
                loadRewardParams, object : OnLoadOneSelfAdLoadListener {
                    override fun loadAdSuccess(ttFeedAd: AdWrapper) {
                        showCommonAdDialog(dataModel, ttFeedAd)
                    }

                    override fun loadAdError(code: Int, message: String) {
                        ToastManager.showToast("已领取${duration}分钟免费时长")
                    }
                }
            )
    }


    private fun showCommonAdDialog(dataModel: FuliBallDialogDataModel, ttFeedAd: AdWrapper?) {
        val mainActivity = BaseApplication.getMainActivity()
        if (mainActivity is MainActivity) {
            try {
                val fragmentAction = Router.getMainActionRouter().fragmentAction
                if (UserInfoMannage.isVipUser().not()) { //vip不展示广告
                    val baseDialogFragment: BaseDialogFragment<*> =
                        fragmentAction.newFuliCoinBallDialogFragment(dataModel, ttFeedAd, object :
                            JssdkFuliRewardCallback {
                            override fun onAwardSuccess(
                                successCode: Int,
                                obtainRsp: RewardCoinObtainRsp?
                            ) {
                                HomeFreeModelGuideManager.performRewardVideoExchangeListenTime(
                                    true,
                                    null
                                )
                            }

                            override fun onError(
                                errorCode: Int,
                                message: String?,
                                obtainRsp: RewardCoinObtainRsp?
                            ) {
                                ToastManager.showToast("已领取${dataModel.awardTime}分钟免费时长")
                            }

                        })
                    baseDialogFragment.show(mainActivity.supportFragmentManager, "")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

}