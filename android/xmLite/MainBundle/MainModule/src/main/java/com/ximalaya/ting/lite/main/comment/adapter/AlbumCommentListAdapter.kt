package com.ximalaya.ting.lite.main.comment.adapter

import android.content.Context
import android.text.Html
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import com.ximalaya.ting.android.framework.adapter.HolderAdapter
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.host.util.view.EmotionUtil2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.comment.CommentListPresenter
import com.ximalaya.ting.lite.main.comment.CommonUtils
import com.ximalaya.ting.lite.main.comment.entities.CommentListItemBean
import com.ximalaya.ting.lite.main.comment.entities.LikeItemBean
import com.ximalaya.ting.lite.main.comment.view.CommentItemView


/**
 *  @Author: Junxiang Cheng
 *  @Mail: <EMAIL>
 *  @CreateTime: 12/28/21
 *
 *  @Description: 评论Item
 */
class AlbumCommentListAdapter(
    type: String,
    context: Context,
    var presenter: CommentListPresenter,
    listData: List<CommentListItemBean>?,
    onCommentItemClickListener: OnCommentItemClickListener
) : HolderAdapter<CommentListItemBean>(context, listData),
    CommentItemView.LongClickListener {

    companion object {
        const val TYPE_COMMENT = "TYPE_COMMENT"
        const val TYPE_REPLY = "TYPE_REPLY"
        const val LIKE_STATUS = "like"
        const val UN_LIKE_STATUS = "unlike"
    }

    private var mType = type
    var listener = onCommentItemClickListener
    private val mutableList = mutableMapOf<String, LikeItemBean>()

    init {
        if (type != TYPE_COMMENT && type != TYPE_REPLY) {
            throw IllegalArgumentException("type must be TYPE_COMMENT or TYPE_REPLY")
        }
    }

    override fun onClick(
        view: View,
        data: CommentListItemBean?,
        position: Int,
        holder: BaseViewHolder?
    ) {

        data?.let {
            when (view.id) {
                R.id.main_cl_comment_like_hot_area -> {//点击点赞
                    if (holder is ViewHolder) {
                        dealWithLikeOrDislikeClick(it, holder)
                    }
                }
                R.id.main_civ_container -> { //点击内容区域，调启回复该评论
                    listener.startReplyList(targetParentComment = it)
                }
                R.id.main_ll_reply_container -> {//点击回复热区，拉起回复
                    if (mType == TYPE_COMMENT) {
                        // 声音评论-父评论浮层-查看更多  弹框控件点击
                        XMTraceApi.Trace()
                            .setMetaId(41446)
                            .setServiceId("dialogClick")
                            .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
                            .put("trackId", presenter.mCurTrack?.dataId.toString())
                            .createTrace()

                        listener.startReplyList(it)
                    }
                }
            }
        }
    }

    override fun onLongClick(view: View): Boolean {
        val position = view.getTag(R.id.framework_view_holder_position) as Int
        val data: CommentListItemBean =
            view.getTag(R.id.framework_view_holder_data) as CommentListItemBean

        return if (data.isCanDelete) {
            listener.onLongClickDelete(position, data)
            true
        } else {
            false
        }
    }

    private fun setLongClickListener(
        view: CommentItemView?,
        data: CommentListItemBean?,
        position: Int
    ) {
        if (view == null) {
            return
        }
        view.setLongClickListener(this)
//        view.setOnLongClickListener(this)
        view.setTag(R.id.framework_view_holder_position, position)
        view.setTag(R.id.framework_view_holder_data, data)
    }

    private fun dealWithLikeOrDislikeClick(data: CommentListItemBean, holder: ViewHolder) {
        //点赞需要登录
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(context)
            return
        }
        val tag = holder.itemViewContainer.ivCommentLike.tag
        val commentUid = data.user?.uid ?: 0
        tag.equals(LIKE_STATUS).also {
            val callBack = object : IDataCallBack<Boolean> {
                override fun onSuccess(isSuccess: Boolean?) {
                    updateLikeStatus(isSuccess, it, data, holder)
                }

                override fun onError(code: Int, message: String?) {
                }
            }
            if (it) {
                presenter.requestDisLikeAlbumComment(commentUid, data.commentId, callBack)
            } else {
                presenter.requestLikeAlbumComment(commentUid, data.commentId, callBack)
            }
        }
    }

    fun updateLikeStatus(
        isSuccess: Boolean?,
        like: Boolean,
        data: CommentListItemBean,
        holder: ViewHolder
    ) {
        if (isSuccess != true) return
        holder.itemViewContainer.ivCommentLike.apply {
            if (like) {
                cancelAnimation()
                progress = 0f
            } else {
                playAnimation()
            }
            tag = if (like) UN_LIKE_STATUS else LIKE_STATUS
        }
        holder.itemViewContainer.tvLikeCount.apply {
            var tempLikeCount = text.toString().toLong()
            tempLikeCount = if (like) tempLikeCount - 1 else tempLikeCount + 1
            mutableList[data.commentId.toString()] = LikeItemBean(tempLikeCount, !like)
            text = tempLikeCount.toString()
            setTextColor(
                ContextCompat.getColor(
                    context, if (like)
                        R.color.host_color_999999
                    else
                        R.color.host_color_ff6110
                )
            )
        }
    }

    override fun getConvertViewId() = R.layout.main_item_comment_list_item

    override fun buildHolder(convertView: View): BaseViewHolder = ViewHolder(convertView)

    override fun isEmpty(): Boolean {
        return false
    }

    override fun bindViewDatas(holder: BaseViewHolder, data: CommentListItemBean?, position: Int) {
        if (data?.user == null) {
            return
        }
        val viewHolder = holder as ViewHolder
        viewHolder.itemViewContainer.apply {

            setType(
                if (mType == TYPE_COMMENT)
                    CommentItemView.TYPE_COMMENT_LIST_ITEM
                else
                    CommentItemView.TYPE_REPLY_LIST_ITEM
            )

            //评论正文
            if (!TextUtils.isEmpty(data.content)) {
                tvCommentContent.visibility = View.VISIBLE
                tvCommentContent.let {
//                it.text = data.content
                    val spannableStringBuilder = SpannableStringBuilder(
                        EmotionUtil2
                            .getInstance()
                            .convertEmotionText2Span(data.content)
                    )
                    it.text = spannableStringBuilder
                    it.post {
                        if (mType == TYPE_COMMENT && it.lineCount > 4) {
                            val lines = it.lineCount
                            if (!data.isExpanded) {
                                it.maxLines = 4
                                tvCommentExpand.visibility = View.VISIBLE
                                tvCommentExpand.setOnClickListener { _ ->
                                    tvCommentExpand.visibility = View.GONE
                                    it.maxLines = lines
                                    data.isExpanded = true
                                }
                            } else {
                                tvCommentExpand.visibility = View.GONE
                                it.maxLines = lines
                            }
                        } else {
                            tvCommentExpand.visibility = View.GONE
                            if (mType == TYPE_REPLY && data.parentUser != null
                                && data.parentCommentId != null
                            ) {
                                val parentName =
                                    CommonUtils.nameIsNullReplaceUid(
                                        data.parentUser?.nickname,
                                        data.parentUser?.uid ?: 0L
                                    )
                                val contentStrPre = StringBuilder()
                                    .append("<font color=\"#444444\">回复</font>")
                                    .append("<font color=\"#4990E2\">@${parentName}</font>")
                                val contentPreSpan = (Html.fromHtml(contentStrPre.toString()))

                                val contentSpan = SpannableStringBuilder()
                                contentSpan.append(contentPreSpan)
                                    .append(
                                        EmotionUtil2.getInstance()
                                            .convertEmotionText2Span(data.content)
                                    )

                                it.text = contentSpan
                            }
                            it.maxLines = it.lineCount
                        }
                    }
                }
            } else {
                tvCommentContent.visibility = View.GONE
            }
            setClickListener(this, data, position, viewHolder)

            //头像
            ImageManager.from(context).displayImage(
                ivAvatar,
                data.user?.avatar,
                R.drawable.host_ic_avatar_default,
                R.drawable.host_ic_avatar_default
            )
            val user = data.user
            tvNickname.text =
                CommonUtils.nameIsNullReplaceUid(user?.nickname, data.user?.uid ?: 0L)

            ivUserVip.visibility = if (user?.isVip == true) View.VISIBLE else View.GONE
            //更新时间
            val updateTime = data.createTime
            tvCommentTime.text =
                (if (updateTime > 0) TimeHelper.convertTimeNew(updateTime) else "")
            if (TextUtils.isEmpty(data.region)) {
                tvIpRegion.visibility = View.GONE
            } else {
                tvIpRegion.text = "来自 ${data.region}"
                tvIpRegion.visibility = View.VISIBLE
            }
            //点赞
            val likeItemBean = mutableList[data.commentId.toString()]
            val likeCount = likeItemBean?.likeCount ?: data.likeCount
            val likeStatus = likeItemBean?.likeStatus ?: data.likeStatus
            tvLikeCount.text = likeCount.toString()
            if (likeStatus) {
                tvLikeCount.setTextColor(context.resources.getColor(R.color.host_color_ff6110))
                ivCommentLike.progress = 1f
                ivCommentLike.tag = LIKE_STATUS
            } else {
                tvLikeCount.setTextColor(context.resources.getColor(R.color.host_color_999999))
                ivCommentLike.progress = 0f
                ivCommentLike.tag = UN_LIKE_STATUS
            }
            if (data.score <= 0) {
                ivRatingbar.visibility = View.GONE
            } else {
                ivRatingbar.visibility = View.VISIBLE
                ivRatingbar.progress = data.score.toInt()
            }
            setClickListener(clLikeHotArea, data, position, holder)

            //删除
            setLongClickListener(this, data, position)


            /**
             * 评论主楼-回复部分
             */
            if (mType == TYPE_COMMENT) {
                if (CollectionUtil.isNotEmpty(data.replys)) {
                    var replyUser = ""
                    var replyContent = ""
                    data.replys?.let {
                        val user = it.first().user
                        val parentUser = it.first().parentUser
                        replyUser =
                            CommonUtils.nameIsNullReplaceUid(user?.nickname, user?.uid ?: 0L)
                        replyContent = it.first().content.toString()

                        val contentStrPre = StringBuilder()
                            .append("<font color=\"#4990E2\">${replyUser}</font>")
                        if (parentUser != null) {
                            val parentName = CommonUtils.nameIsNullReplaceUid(
                                parentUser.nickname,
                                parentUser.uid
                            )
                            contentStrPre.append("<font color=\"#444444\"> 回复 </font>")
                                .append("<font color=\"#4990E2\">@${parentName}</font>")
                        }
                        val contentPreSpan = (Html.fromHtml(contentStrPre.toString()))

                        val contentSpan = SpannableStringBuilder()
                        contentSpan.append(contentPreSpan)
                            .append("：")
                            .append(
                                EmotionUtil2.getInstance().convertEmotionText2Span(replyContent)
                            )

                        tvReplyContent.text = contentSpan

                        if (data.replyCount > 1) {
                            tvReplayCount.visibility = View.VISIBLE
                            tvReplayCount.text = "查看全部${data.replyCount}条回复"
                        } else {
                            tvReplayCount.visibility = View.GONE
                        }
                    }
                } else {
                    llReplyContainer.visibility = View.GONE
                }
                setClickListener(llReplyContainer, data, position, holder)
            }

        }
    }

    class ViewHolder(val convertView: View) : BaseViewHolder() {
        val itemViewContainer: CommentItemView =
            convertView.findViewById(R.id.main_civ_container)
    }

    interface OnCommentItemClickListener {
        // 拉起评论输入
        fun startComment(targetParentComment: CommentListItemBean?)

        // 跳转回复列表
        fun startReplyList(targetParentComment: CommentListItemBean)

        //长按删除
        fun onLongClickDelete(position: Int, data: CommentListItemBean)
    }
}