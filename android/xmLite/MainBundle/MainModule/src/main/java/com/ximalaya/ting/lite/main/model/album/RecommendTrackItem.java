package com.ximalaya.ting.lite.main.model.album;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.host.model.ad.AnchorAlbumAd;
import com.ximalaya.ting.android.host.model.album.DislikeReason;
import com.ximalaya.ting.android.host.model.album.RecInfo;
import com.ximalaya.ting.android.host.model.track.TrackM;

import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.List;

/**
 * Created by WolfXu on 2018/5/28.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class RecommendTrackItem extends TrackM {

    //服务端类型，禁止随意修改
    public static int TRACK_ITEM_UI_TYPE_DEF = 0;
    public static int TRACK_ITEM_UI_TYPE_STYLE_V2 = 1;

    private List<DislikeReason> dislikeReasons;
    private RecInfo recInfo;
    private boolean clicked;
    private AnchorAlbumAd adInfo;    // 主播竞价广告module
    private int uiType; //0默认类型，1:样式

    //推荐的tag
    private String providerTag;

    public RecommendTrackItem(String json) {
        super(json);
    }

    @Override
    public void fillProperties(JSONObject jsonObject) {
        super.fillProperties(jsonObject);
        Gson gson = new Gson();
        try {
            if (jsonObject.has("dislikeReasons")) {
                Type type = new TypeToken<List<DislikeReason>>() {
                }.getType();
                dislikeReasons = new Gson().fromJson(jsonObject.optString("dislikeReasons"), type);
            }
            if (jsonObject.has("recInfo")) {
                recInfo = gson.fromJson(jsonObject.optString("recInfo"), RecInfo.class);
            }
            if (jsonObject.has("adInfo")) {
                adInfo = gson.fromJson(jsonObject.optString("adInfo"), AnchorAlbumAd.class);
            }
            if (jsonObject.has("uiType")) {
                uiType = jsonObject.optInt("uiType", TRACK_ITEM_UI_TYPE_DEF);
            }
            if (jsonObject.has("providerTag")) {
                providerTag = jsonObject.optString("providerTag", "");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<DislikeReason> getDislikeReasons() {
        return dislikeReasons;
    }

    public RecInfo getRecInfo() {
        return recInfo;
    }

    public void setRecInfo(RecInfo recInfo) {
        this.recInfo = recInfo;
    }

    public boolean isClicked() {
        return clicked;
    }

    public void setClicked(boolean clicked) {
        this.clicked = clicked;
    }

    public AnchorAlbumAd getAdInfo() {
        return adInfo;
    }

    public void setAdInfo(AnchorAlbumAd adInfo) {
        this.adInfo = adInfo;
    }

    public String getTrackTitle() {
        if (adInfo != null && !TextUtils.isEmpty(adInfo.getName())) {
            return adInfo.getName();
        }
        return super.getTrackTitle();
    }

    public String getTrackIntro() {
        if (adInfo != null && !TextUtils.isEmpty(adInfo.getDescription())) {
            return adInfo.getDescription();
        }
        return super.getTrackIntro();
    }

    @Override
    public String getCoverUrlSmall() {
        if (adInfo != null && !TextUtils.isEmpty(adInfo.getImageUrl())) {
            return adInfo.getImageUrl();
        }
        return super.getCoverUrlSmall();
    }

    @Override
    public String getCoverUrlMiddle() {
        if (adInfo != null && !TextUtils.isEmpty(adInfo.getImageUrl())) {
            return adInfo.getImageUrl();
        }
        return super.getCoverUrlMiddle();
    }

    @Override
    public String getCoverUrlLarge() {
        if (adInfo != null && !TextUtils.isEmpty(adInfo.getImageUrl())) {
            return adInfo.getImageUrl();
        }
        return super.getCoverUrlLarge();
    }

    /**
     * 返回一个可用的封面图
     *
     * @return
     */
    public String getValidCover() {
        if (!TextUtils.isEmpty(getCoverUrlLarge()))
            return getCoverUrlLarge();
        if (!TextUtils.isEmpty(getCoverUrlMiddle()))
            return getCoverUrlMiddle();
        if (!TextUtils.isEmpty(getCoverUrlSmall()))
            return getCoverUrlSmall();
        return "";
    }

    public int getUiType() {
        return uiType;
    }

    public void setUiType(int uiType) {
        this.uiType = uiType;
    }

    public String getProviderTag() {
        return providerTag;
    }
}
