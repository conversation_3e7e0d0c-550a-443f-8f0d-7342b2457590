package com.ximalaya.ting.lite.main.child;

import android.os.Bundle;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.model.childprotect.ChildProtectInfo;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;


/**
 * Created by qinhuifeng on 2018/11/07.
 * <p>
 * 未成年人保护模式--开关
 *
 * <AUTHOR>
 */
public class ChildProtectionSettingFragment extends BaseFragment2 {

    private static final String KEY_ARGUMENTS_PROTECT_INFO = "key_arguments_child_protect_info";

    private ImageView mIvProtect;
    private TextView mTvProtectInfo;
    private TextView mTvProtectSet;
    private ChildProtectInfo mProtectInfo;

    public static ChildProtectionSettingFragment newInstance() {
        return new ChildProtectionSettingFragment();
    }

    /**
     * 增加来源信息
     */
    public static Bundle newArguments(ChildProtectInfo protectInfo) {
        Bundle args = new Bundle();
        args.putParcelable(KEY_ARGUMENTS_PROTECT_INFO, protectInfo);
        return args;
    }

    public ChildProtectionSettingFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_FRAMELAYOUT, null);
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mProtectInfo = arguments.getParcelable(KEY_ARGUMENTS_PROTECT_INFO);
        }
        if (mProtectInfo == null) {
            mProtectInfo = new ChildProtectInfo();
        }

        setTitle("青少年模式");

        initVies();
    }

    private void initVies() {
        mIvProtect = findViewById(R.id.main_iv_protect);
        mTvProtectInfo = findViewById(R.id.main_tv_protect_info);
        mTvProtectSet = findViewById(R.id.main_tv_protect_set);

        mTvProtectSet.setOnClickListener(v -> {
            if (!OneClickHelper.getInstance().onClick(v)) {
                return;
            }
            if (ChildProtectManager.isChildProtectOpen(getContext())) {
                ChildProtectionPassWordFragment settingFragment = ChildProtectionPassWordFragment.newInstanceForClose();
                startFragment(settingFragment);
            } else {
                ChildProtectionPassWordFragment settingFragment = ChildProtectionPassWordFragment.newInstanceForOpen();
                startFragment(settingFragment);
            }
        });
        AutoTraceHelper.bindData(mTvProtectSet, AutoTraceHelper.MODULE_DEFAULT, "");
    }

    @Override
    protected void loadData() {

    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 100050;
        super.onMyResume();

        updateUi();
    }

    private void updateUi() {
        //每次可见的时候，更新未成年人保护的状态
        boolean childProtectOpen = ChildProtectManager.isChildProtectOpen(getContext());
        if (childProtectOpen) {
            //当前是开启状态
            mIvProtect.setImageResource(R.drawable.main_icon_child_protest_set_orange);
            mTvProtectInfo.setText("青少年模式已开启");
            mTvProtectSet.setText("关闭青少年模式");
        } else {
            //当前是关闭状态
            mIvProtect.setImageResource(R.drawable.host_img_child_protect_not_open);
            mTvProtectInfo.setText("青少年模式未开启");
            mTvProtectSet.setText("开启青少年模式");
        }
    }

    @Override
    public boolean onBackPressed() {
        return super.onBackPressed();
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_child_protection_setting;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public boolean isShowTruckFloatPlayBar() {
        return false;
    }
}
