package com.ximalaya.ting.lite.main.home.adapter

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.AlbumTagUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.lite.main.album.fragment.AggregateRankFragment
import com.ximalaya.ting.lite.main.album.fragment.NewAggregateRankFragment
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain
import com.ximalaya.ting.lite.main.home.viewmodel.HomeItemSubscriptionViewModel
import com.ximalaya.ting.lite.main.model.rank.AggregateRankArgsModel

/**
 * Created by qinhuifeng on 2021/1/14
 *
 * <AUTHOR>
 */
class SubscriptionFloorItemAdapter(val baseFragment2: BaseFragment2, context: Context, listData: List<HomeItemSubscriptionViewModel>, val from: Int) : MultiRecyclerAdapter<HomeItemSubscriptionViewModel, SuperRecyclerHolder>(context, listData) {

    override fun createMultiViewHolder(mCtx: Context?, itemView: View, viewType: Int): SuperRecyclerHolder {
        return SuperRecyclerHolder.createViewHolder(mCtx, itemView)
    }

    override fun onBindMultiViewHolder(holder: SuperRecyclerHolder?, viewModel: HomeItemSubscriptionViewModel?, viewType: Int, position: Int) {
        if (holder == null || viewModel == null) {
            return
        }
        when (viewModel.viewType) {
            HomeItemSubscriptionViewModel.ITEM_MORE -> {
                dealMoreSubscribeItem(holder, viewModel, position)
            }
            HomeItemSubscriptionViewModel.ITEM_ADD -> {
                dealAddSubscribeItem(holder, viewModel, position)
            }
            HomeItemSubscriptionViewModel.ITEM_RECOMMEND_ALBUM -> {
                dealRecommendAlbumItem(holder, viewModel, position)
            }
            HomeItemSubscriptionViewModel.ITEM_SUBSCRIBE_ALBUM -> {
                dealSubscriptionAlbumItem(holder, viewModel, position)
            }
            HomeItemSubscriptionViewModel.ITEM_LINE -> {
                //处理线
            }
            else -> {
                //没有类型不处理
            }
        }
    }

    override fun getMultiItemViewType(model: HomeItemSubscriptionViewModel?, position: Int): Int {
        if (model == null) {
            return 0
        }
        return model.viewType
    }

    override fun getMultiItemLayoutId(viewType: Int): Int {
        return when (viewType) {
            HomeItemSubscriptionViewModel.ITEM_MORE -> {
                R.layout.main_item_album_history_in_home_floor_rv_item_more
            }
            HomeItemSubscriptionViewModel.ITEM_ADD -> {
                R.layout.main_item_album_subscription_in_home_floor_rv_item_add
            }
            HomeItemSubscriptionViewModel.ITEM_LINE -> {
                R.layout.main_item_album_subscription_in_home_floor_rv_item_line
            }
            HomeItemSubscriptionViewModel.ITEM_SUBSCRIBE_ALBUM -> {
                R.layout.main_item_album_subscription_in_home_floor_rv_item
            }
            HomeItemSubscriptionViewModel.ITEM_RECOMMEND_ALBUM -> {
                R.layout.main_item_album_subscription_in_home_floor_rv_item
            }
            else -> {
                R.layout.main_item_album_subscription_in_home_floor_rv_item
            }
        }
    }

    fun dealSubscriptionAlbumItem(holder: SuperRecyclerHolder, viewModel: HomeItemSubscriptionViewModel, position: Int) {
        if (viewModel.album !is AlbumM) {
            return
        }
        val albumM: AlbumM = viewModel.album as AlbumM
        //vip角标相关
        val ivTag: ImageView = holder.getViewById(R.id.main_iv_space_album_tag) as ImageView
        //专辑图
        val ivCover: ImageView = holder.getViewById(R.id.main_iv_album_cover) as ImageView
        //更新条数
        val tvPlayPercent: TextView = holder.getViewById(R.id.main_tv_last_play_percent) as TextView
        //标题
        val tvTitle: TextView = holder.getViewById(R.id.main_album_item_title) as TextView

        tvTitle.text = albumM.albumTitle

        ImageManager.from(context).displayImage(ivCover, albumM.validCover, com.ximalaya.ting.android.host.R.drawable.host_default_album_145)

        //设置更新条数
        val attentionModel = albumM.attentionModel
        tvPlayPercent.text = if (attentionModel != null && attentionModel.unreadNum > 0) {
            tvPlayPercent.visibility = View.VISIBLE
            "${attentionModel.unreadNum}条更新"
        } else {
            tvPlayPercent.visibility = View.GONE
            ""
        }
        //如果已经下架了，展示专辑下架，放在更新条数之后
        if (albumM.status == 2) {
            tvPlayPercent.visibility = View.VISIBLE
            tvPlayPercent.text = "已下架"
            ivCover.alpha = 0.5f
        } else {
            ivCover.alpha = 1.0f
        }
        //设置角标
        ivTag.visibility = if (AlbumTagUtil.getAlbumCoverTag(albumM) != -1) {
            ivTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable(albumM, context, AlbumTagUtil.ZOOM_IN_RATIO_100_percent))
            View.VISIBLE
        } else {
            View.INVISIBLE
        }
        holder.setOnItemClickListenner(object : View.OnClickListener {
            override fun onClick(v: View?) {
                val attentionModel = albumM.attentionModel
                var unreadNum = 0
                if (attentionModel != null) {
                    unreadNum = attentionModel.unreadNum
                    attentionModel.unreadNum = 0
                    notifyDataSetChanged()
                }
                AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_FEED, ConstantsOpenSdk.PLAY_FROM_FEED, albumM.recSrc, albumM.recTrack, unreadNum, baseFragment2.activity)
            }
        })
    }

    fun dealRecommendAlbumItem(holder: SuperRecyclerHolder, viewModel: HomeItemSubscriptionViewModel, position: Int) {
        if (viewModel.album !is AlbumM) {
            return
        }
        val albumM: AlbumM = viewModel.album as AlbumM
        //vip角标相关
        val ivTag: ImageView = holder.getViewById(R.id.main_iv_space_album_tag) as ImageView
        //专辑图
        val ivCover: ImageView = holder.getViewById(R.id.main_iv_album_cover) as ImageView
        //更新条数
        val tvPlayPercent: TextView = holder.getViewById(R.id.main_tv_last_play_percent) as TextView
        //标题
        val tvTitle: TextView = holder.getViewById(R.id.main_album_item_title) as TextView
        tvTitle.text = albumM.albumTitle

        ImageManager.from(context).displayImage(ivCover, albumM.validCover, com.ximalaya.ting.android.host.R.drawable.host_default_album_145)

        //设置推荐订阅
        tvPlayPercent.text = "推荐订阅"
        tvPlayPercent.visibility = View.VISIBLE
        //如果已经下架了，展示专辑下架，放在推荐订阅设置之后
        if (albumM.status == 2) {
            tvPlayPercent.text = "已下架"
            ivCover.alpha = 0.5f
        } else {
            ivCover.alpha = 1.0f
        }


        //设置角标
        ivTag.visibility = if (AlbumTagUtil.getAlbumCoverTag(albumM) != -1) {
            ivTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable(albumM, context, AlbumTagUtil.ZOOM_IN_RATIO_100_percent))
            View.VISIBLE
        } else {
            View.INVISIBLE
        }
        holder.setOnItemClickListenner(object : View.OnClickListener {
            override fun onClick(v: View?) {
                val attentionModel = albumM.attentionModel
                var unreadNum = 0
                if (attentionModel != null) {
                    unreadNum = attentionModel.unreadNum
                }
                AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_FEED,
                        ConstantsOpenSdk.PLAY_FROM_FEED, albumM.recSrc,
                        albumM.recTrack, unreadNum, MainApplication.getMainActivity())
            }
        })
    }

    fun dealMoreSubscribeItem(holder: SuperRecyclerHolder?, viewModel: HomeItemSubscriptionViewModel, position: Int) {
        if (holder == null) {
            return
        }
        holder.setOnItemClickListenner(object : View.OnClickListener {
            override fun onClick(v: View?) {
                if (baseFragment2.activity is MainActivity) {
                    (baseFragment2.activity as MainActivity).switchTingProgramTab(null)
                }
            }
        })
    }

    fun dealAddSubscribeItem(holder: SuperRecyclerHolder?, viewModel: HomeItemSubscriptionViewModel, position: Int) {
        if (holder == null) {
            return
        }
        holder.setOnItemClickListenner(object : View.OnClickListener {
            override fun onClick(v: View?) {
                if (isFromTingBook()) {
                    //来源是听书页面使用听书榜
                    val model = AggregateRankArgsModel()
                    val aggregateRankFragment = NewAggregateRankFragment.newInstance(model)
                    baseFragment2.startFragment(aggregateRankFragment)
                } else {
                    //使用聚合排行榜
                    val aggregateRankFragment = AggregateRankFragment()
                    baseFragment2.startFragment(aggregateRankFragment)
                }
            }
        })
        val tvHasSubscribeCount: TextView = holder.getViewById(R.id.main_album_has_subscribe_count) as TextView
        val tvAddText: TextView = holder.getViewById(R.id.main_album_add_text) as TextView
        //放在0位置的时候使用单独样式
        if (position == 0) {
            tvHasSubscribeCount.text = "已订阅(${viewModel.hasSubscribeCount})"
            tvAddText.visibility = View.VISIBLE
        } else {
            tvHasSubscribeCount.text = "添加订阅"
            tvAddText.visibility = View.GONE
        }
    }

    private fun isFromTingBook() = from == BundleValueConstantsInMain.FROM_TING_BOOK

}