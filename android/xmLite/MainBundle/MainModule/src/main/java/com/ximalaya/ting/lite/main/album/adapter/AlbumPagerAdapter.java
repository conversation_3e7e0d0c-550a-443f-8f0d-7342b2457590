package com.ximalaya.ting.lite.main.album.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.view.text.AdaptiveTextView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.album.adapter.AlbumPagerAdapter.PageIndex;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2017/6/29.
 */
public class AlbumPagerAdapter extends HolderAdapter<PageIndex> {

    private int mPageId = 1;

    private int selectedTextColor;
    private int unSelectedTextColor;


    public AlbumPagerAdapter(Context context, List<PageIndex> listData) {
        super(context, listData);
        selectedTextColor = ContextCompat.getColor(context, R.color.main_color_white);
        unSelectedTextColor = ContextCompat.getColor(context, R.color.main_color_black);
    }

    /**
     * 计算选集页数信息
     *
     * @param trackCounts
     */
    public static List<PageIndex> computePagerIndex(int pageSize,
                                                    int trackCounts) {
        List<PageIndex> mPagerIndexs = new ArrayList<>();
        int group = (int) Math.ceil(trackCounts / (float) pageSize);
        for (int i = 0; i < group; i++) {
            PageIndex pi = new PageIndex();
            pi.setPageIndex(i + 1);
            pi.setStartIndex(pageSize * i + 1);
            pi.setEndIndex(Math.min(pageSize * (i + 1), trackCounts));
            pi.setPageString(pi.getStartIndex() + "~" + pi.getEndIndex());
            mPagerIndexs.add(pi);
        }
        return mPagerIndexs;
    }

    /**
     * 注意：选集面板的顺序要根据 当前专辑排序（isRecordDesc）^ 选择的正倒序（isAsc） =true 就正序显示，=false就倒序显示
     *
     * @param pageSize
     * @param trackCounts
     * @param isAsc       是否为升序（orderNo:1/2/3/4/5/...）否则（orderNo:.../5/4/3/2/1）
     * @return
     */
    public static List<PageIndex> computePagerIndex(int pageSize, int trackCounts, boolean isAsc) {
        List<PageIndex> mPagerIndexs = new ArrayList<>();
        int group = (int) Math.ceil(trackCounts / (float) pageSize);
        for (int i = 0; i < group; i++) {
            PageIndex pi = new PageIndex();
            pi.setPageIndex(i + 1);
            if (isAsc) {
                pi.setStartIndex(pageSize * i + 1);
                pi.setEndIndex(Math.min(pageSize * (i + 1), trackCounts));
                pi.setPageString(pi.getStartIndex() + "~" + pi.getEndIndex());
            } else {
                pi.setStartIndex(trackCounts - pageSize * i);
                pi.setEndIndex(Math.max(trackCounts - pageSize * (i + 1) + 1, 1));
                pi.setPageString(pi.getStartIndex() + "~" + pi.getEndIndex());
            }
            mPagerIndexs.add(pi);
        }
        return mPagerIndexs;
    }

    public void setPageId(int pageId) {
        this.mPageId = pageId;
    }

    @Override
    public void onClick(View view, PageIndex t, int position,
                        BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        AdaptiveTextView tv;
        if (convertView == null) {
            tv = new AdaptiveTextView(context);
            tv.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
            tv.setHeight(BaseUtil.dp2px(context, 30));
            tv.setTextSize(16);
            tv.setTextColor(Color.parseColor("#f5f8fa"));
            tv.setGravity(Gravity.CENTER);
            tv.setSingleLine();
            tv.setBackgroundResource(R.drawable.main_album_pager_item_bg_rect_gray);
            convertView = tv;
        }
        tv = (AdaptiveTextView) convertView;
        PageIndex pi = listData.get(position);
        tv.setText(pi.pageString);
        tv.setBackgroundResource(pi.pageIndex == mPageId ? R.drawable.main_album_pager_item_bg_rect_orange
                : R.drawable.main_album_pager_item_bg_rect_gray);
        tv.setTextColor(pi.pageIndex == mPageId ? selectedTextColor
                : unSelectedTextColor);
        return convertView;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new BaseViewHolder();
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, PageIndex t, int position) {

    }

    public static class PageIndex {
        public String pageString;
        private int pageIndex;
        private int startIndex;
        private int endIndex;

        public int getPageIndex() {
            return pageIndex;
        }

        public void setPageIndex(int pageIndex) {
            this.pageIndex = pageIndex;
        }

        public int getStartIndex() {
            return startIndex;
        }

        public void setStartIndex(int startIndex) {
            this.startIndex = startIndex;
        }

        public int getEndIndex() {
            return endIndex;
        }

        public void setEndIndex(int endIndex) {
            this.endIndex = endIndex;
        }

        public String getPageString() {
            return pageString;
        }

        public void setPageString(String pageString) {
            this.pageString = pageString;
        }
    }
}
