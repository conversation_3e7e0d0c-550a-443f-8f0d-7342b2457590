package com.ximalaya.ting.lite.main.earn.dialog;

import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.listener.CommonPopupDialogCallBack;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.model.earn.CommonPopupDialogDataModel;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.io.Serializable;

/**
 * 通用弹屏
 *
 * <AUTHOR>
 */
public class CommonPopupDialogFragment extends BaseFullScreenDialogFragment {
    private static final String KEY_ARGUMENT_DIALOG_DATA_MODEL = "key_argument_dialog_data_model";
    private ImageView mIvGuideBanner;
    public CommonPopupDialogDataModel mDataModel;
    //回调实现类
    private CommonPopupDialogCallBack mCallBack;

    public static Bundle newArgument(CommonPopupDialogDataModel dataModel) {
        Bundle bundle = new Bundle();
        bundle.putSerializable(KEY_ARGUMENT_DIALOG_DATA_MODEL, dataModel);
        return bundle;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            Serializable serializable = arguments.getSerializable(KEY_ARGUMENT_DIALOG_DATA_MODEL);
            if (serializable instanceof CommonPopupDialogDataModel) {
                mDataModel = (CommonPopupDialogDataModel) serializable;
            }
        }
        if (mDataModel == null) {
            mDataModel = new CommonPopupDialogDataModel(CommonPopupDialogDataModel.FROM_DEF);
        }
        View inflate = inflater.inflate(R.layout.main_fra_dialog_common_popup, container, false);
        mIvGuideBanner = inflate.findViewById(R.id.main_guide_banner);
        View viewClose = inflate.findViewById(R.id.main_iv_close);

        //加载引导的图片
        loadGuideImage();

        mIvGuideBanner.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                gotoLinkUrl(v);
                dismissAllowingStateLoss();
                trackClick();
            }
        });
        viewClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                dismissAllowingStateLoss();
            }
        });
        AutoTraceHelper.bindData(mIvGuideBanner, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(viewClose, AutoTraceHelper.MODULE_DEFAULT, "");

        trackShow();

        return inflate;
    }

    private void trackShow() {
        switch (mDataModel.from) {
            case CommonPopupDialogDataModel.FROM_TASK_CENTER_POP_RESOURCE:
                //我页资源位埋点
                new XMTraceApi.Trace()
                        .setMetaId(10084)
                        .setServiceId("dialogView")
                        .put("cardUrl", mDataModel.linkUrl)
                        .createTrace();
                break;
            case CommonPopupDialogDataModel.FROM_TASK_ONE_YUAN_EXTRACT_MONEY:
                //网赚中心,1元体现弹框
                new XMTraceApi.Trace()
                        .setMetaId(12095)
                        .setServiceId("dialogView")
                        .createTrace();
                break;
            default:
                break;
        }
    }

    private void trackClick() {
        switch (mDataModel.from) {
            case CommonPopupDialogDataModel.FROM_TASK_CENTER_POP_RESOURCE:
                //我页资源位埋点
                new XMTraceApi.Trace()
                        .setMetaId(10085)
                        .setServiceId("dialogClick")
                        .put("cardUrl", mDataModel.linkUrl)
                        .createTrace();
                break;
            case CommonPopupDialogDataModel.FROM_TASK_ONE_YUAN_EXTRACT_MONEY:
                //网赚中心,1元体现弹框
                new XMTraceApi.Trace()
                        .setMetaId(12096)
                        .setServiceId("dialogClick")
                        .put("item", "马上提现")
                        .createTrace();
                break;
            default:
                break;
        }
    }

    /**
     * 加载引导图片
     * <p>
     * 在show之前已经完成加载
     */
    void loadGuideImage() {
        if (mIvGuideBanner == null) {
            return;
        }
        ImageManager.from(getActivity()).displayImage(mIvGuideBanner, mDataModel.imageUrl, -1, new ImageManager.DisplayCallback() {
            @Override
            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                if (mIvGuideBanner == null) {
                    return;
                }
                if (bitmap == null) {
                    return;
                }
                int width = bitmap.getWidth();
                int height = bitmap.getHeight();
                FuliLogger.log("未登录弹框==宽度=" + width + "  高度==" + height);
                if (width <= 0 || height <= 0) {
                    return;
                }
                //图片展示的宽度固定是300dp，高度根据图片本身的宽度和高度进行计算
                int imageWith = BaseUtil.dp2px(getActivity(), 300);
                int imageHeight = imageWith * height / width;
                ViewGroup.LayoutParams layoutParams = mIvGuideBanner.getLayoutParams();
                if (layoutParams != null) {
                    layoutParams.width = imageWith;
                    layoutParams.height = imageHeight;
                    mIvGuideBanner.setLayoutParams(layoutParams);
                }
                FuliLogger.log("设置==宽度=" + imageWith + "  高度==" + imageHeight);
            }
        });
    }

    /**
     * 设置回调方法
     * <p>
     * 扩展使用
     */
    public void setCommonPopupDialogCallBack(CommonPopupDialogCallBack callBack) {
        mCallBack = callBack;
    }

    private void gotoLinkUrl(View view) {
        String url = mDataModel.linkUrl;
        if (TextUtils.isEmpty(url)) {
            return;
        }
        if (url.startsWith("iting://") || url.startsWith("uting://")) {
            try {
                Router.getMainActionRouter().getFunctionAction().handleITing(getActivity(), Uri.parse(url));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }
        //只能在MainActivity打开网页
        FragmentActivity activity = getActivity();
        if (!(activity instanceof MainActivity)) {
            return;
        }
        MainActivity mainActivity = (MainActivity) activity;
        if (url.startsWith("http") || url.startsWith("https")) {
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
            mainActivity.startFragment(NativeHybridFragment.newInstance(bundle), view, 0, 0);
        }
    }
}
