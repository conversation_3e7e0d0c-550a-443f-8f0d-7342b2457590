package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;
import android.text.TextUtils;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.astuetz.PagerSlidingTabStrip;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter.FragmentHolder;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.data.model.category.CategoryTagList;
import com.ximalaya.ting.android.host.data.model.category.Tag;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.lite.main.home.view.HomeChooseItemPopupWindow;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.request.LiteUrlConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 首页-具体分类页面
 *
 * <AUTHOR>
 */
public class HomeCategoryContentTabFragment extends BaseFragment2 implements OnClickListener {

    private static int VIEW_PAGE_PAGE_LIMIT = 6;
    private PagerSlidingTabStrip mTabs;
    private ViewPager mPager;
    private TabCommonAdapter mAdapter;
    private FrameLayout mFlFilterContainer;
    private View mPopDropDownView;

    //当前页面的分类
    private int mCategoryId = -1;

    //跳转进来，默认选中的页面
    private int mSelectKeywordId = -1;
    //跳转尽量，默认选中的页面，优先使用mSelectKeywordId匹配，如果无法匹配使用mSelectKeywordName
    //iting跳转可能会使用到name匹配
    private String mSelectKeywordName = "";
    private int mSelectMetadataId;
    private int mSelectMetadataValueId;

    private int from = -1;
    private boolean useV2Api;

    //类型选择
    private HomeChooseItemPopupWindow mChooseKeywordWindow;

    private List<FragmentHolder> mFragmentList = new CopyOnWriteArrayList<>();
    private List<Tag> mTagList = new CopyOnWriteArrayList<>();

    /**
     * 通过keywordId选中对应的tab
     */
    public static Bundle createArgumentSelectByKeywordId(int categoryId, int selectKeywordId) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_KEYWORD_ID, selectKeywordId);
        return bundle;
    }

    /**
     * 通过keywordId选中对应的tab
     *
     * @param useV2Api 是否使用v2版本的api
     */
    public static Bundle createArgumentSelectByKeywordId(int categoryId, int selectKeywordId, boolean useV2Api) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_KEYWORD_ID, selectKeywordId);
        bundle.putBoolean(BundleKeyConstantsInMain.KEYWORD_ALL_USE_V2_API, useV2Api);
        return bundle;
    }

    /**
     * 通过keywordName选中对应的tab
     */
    public static Bundle createArgumentSelectByKeyWordName(int categoryId, String selectKeyName) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putString(BundleKeyConstantsInMain.KEY_KEYWORD_NAME, selectKeyName);
        return bundle;
    }

    /**
     * 通过keywordName选中对应的tab
     */
    public static Bundle createArgumentSelectByKeyWordName(int categoryId, String selectKeyName, boolean useV2Api) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putString(BundleKeyConstantsInMain.KEY_KEYWORD_NAME, selectKeyName);
        bundle.putBoolean(BundleKeyConstantsInMain.KEYWORD_ALL_USE_V2_API, useV2Api);
        return bundle;
    }

    /**
     * 选中默认的"全部"
     */
    public static Bundle createArgumentDef(int categoryId) {
        return createArgumentSelectByKeywordId(categoryId, -1);
    }

    /**
     * 选中默认的"全部"并且是Vip
     *
     * @param from     1 表示来自Vip专区界面
     * @param useV2Api 是否是有v2版本的api
     */
    public static Bundle createArguments(int categoryId, int from, boolean useV2Api) {
        Bundle bundle = createArgumentSelectByKeywordId(categoryId, -1);
        bundle.putInt(BundleKeyConstantsInMain.KEY_FROM, from);
        bundle.putBoolean(BundleKeyConstantsInMain.KEYWORD_ALL_USE_V2_API, useV2Api);
        return bundle;
    }

    /**
     * 选中默认的"全部"并且是Vip
     *
     * @param selectKeywordId 通过keywordId选中对应的tab
     * @param from            1 表示来自Vip专区界面
     * @param useV2Api        是否是有v2版本的api
     */
    public static Bundle createArguments(int categoryId, int selectKeywordId, int from, boolean useV2Api) {
        Bundle bundle = createArgumentSelectByKeywordId(categoryId, -1);
        bundle.putInt(BundleKeyConstantsInMain.KEY_KEYWORD_ID, selectKeywordId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_FROM, from);
        bundle.putBoolean(BundleKeyConstantsInMain.KEYWORD_ALL_USE_V2_API, useV2Api);
        return bundle;
    }

    /**
     * 选中默认的"全部"并且是Vip
     *
     * @param selectKeyName 通过keywordName选中对应的tab
     * @param from          1 表示来自Vip专区界面
     * @param useV2Api      是否是有v2版本的api
     */
    public static Bundle createArguments(int categoryId, String selectKeyName, int from, boolean useV2Api) {
        Bundle bundle = createArgumentSelectByKeywordId(categoryId, -1);
        bundle.putString(BundleKeyConstantsInMain.KEY_KEYWORD_NAME, selectKeyName);
        bundle.putInt(BundleKeyConstantsInMain.KEY_FROM, from);
        bundle.putBoolean(BundleKeyConstantsInMain.KEYWORD_ALL_USE_V2_API, useV2Api);
        return bundle;
    }

    public HomeCategoryContentTabFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_RELATIVELAYOUT, null);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mCategoryId = arguments.getInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, -1);
            mSelectKeywordId = arguments.getInt(BundleKeyConstantsInMain.KEY_KEYWORD_ID, -1);
            mSelectKeywordName = arguments.getString(BundleKeyConstantsInMain.KEY_KEYWORD_NAME, "-1");
            mSelectMetadataId = arguments.getInt(BundleKeyConstantsInMain.KEY_METADATA_ID, -1);
            mSelectMetadataValueId = arguments.getInt(BundleKeyConstantsInMain.KEY_METADATA_VALUE_ID, -1);
            from = arguments.getInt(BundleKeyConstantsInMain.KEY_FROM);
            useV2Api = arguments.getBoolean(BundleKeyConstantsInMain.KEYWORD_ALL_USE_V2_API);
        }
        setTitle("");
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mPager = findViewById(R.id.main_view_page_content);
        mTabs = findViewById(R.id.main_tabs);
        ViewParent parent = mTabs.getParent();
        if (parent instanceof ViewGroup) {
            mTabs.setDisallowInterceptTouchEventView((ViewGroup) mTabs.getParent());
        }
        mFlFilterContainer = findViewById(R.id.main_fl_filter_container);
        mPopDropDownView = findViewById(R.id.main_pop_drop_down);
        initListeners();
    }


    @Override
    protected void loadData() {
        final Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_CATEGORY_ID, mCategoryId + "");
        params.put(HttpParamsConstants.PARAM_CHANNEL, DeviceUtil.getChannelInApk(getActivity()));
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        params.put(HttpParamsConstants.PARAM_VERSION, DeviceUtil.getVersion(getActivity()));
        params.put(HttpParamsConstants.PARAM_CONTENT_TYPE, "album");
        params.put(HttpParamsConstants.PARAM_CATEGORY_GENDER, "9");
        params.put("deviceId", DeviceUtil.getDeviceToken(getActivity()));
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        String url = LiteUrlConstants.getHomeCategoryKeywords();
        if (useV2Api) {
            url = LiteUrlConstants.getHomeCategoryKeywordsV2();
            params.put("vipPage", String.valueOf(from));
        }
        LiteCommonRequest.getHomeCategoryKeywordsTag(url, params, new IDataCallBack<CategoryTagList>() {
            @Override
            public void onSuccess(final CategoryTagList object) {
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        if (object == null) {
                            mFlFilterContainer.setVisibility(View.INVISIBLE);
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }
                        setTitle(object.getTitle());
                        doBuildTabs(object);
                        onPageLoadingCompleted(LoadCompleteType.OK);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }
                mFlFilterContainer.setVisibility(View.INVISIBLE);
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
            }
        });
    }

    private void doBuildTabs(CategoryTagList object) {
        if (!canUpdateUi() || object == null) {
            return;
        }
        buildTabs(object.getTags());
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
    }

    @Override
    protected String getPageLogicName() {
        return "HomeFragment";
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_lite_home_category_content_tab;
    }

    @Override
    public void onClick(View view) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int id = view.getId();
        //选择分类被点击
        if (id == R.id.main_fl_filter_container) {
            if (mChooseKeywordWindow != null && mPopDropDownView != null && mPager != null) {
                mChooseKeywordWindow.setChosenPosition(mPager.getCurrentItem());
                mChooseKeywordWindow.showAsDropDown(mPopDropDownView);
            }
            return;
        }
    }

    /**
     * 初始化监听事件
     */
    private void initListeners() {
        mFlFilterContainer.setOnClickListener(this);
        AutoTraceHelper.bindData(mFlFilterContainer, AutoTraceHelper.MODULE_DEFAULT, "");
        mTabs.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {

            @Override
            public void onPageSelected(int page) {
                if (mChooseKeywordWindow != null) {
                    mChooseKeywordWindow.setChosenPosition(page);
                }
            }

            @Override
            public void onPageScrolled(int arg0, float arg1, int arg2) {
            }

            @Override
            public void onPageScrollStateChanged(int arg0) {
            }
        });
    }

    /**
     * 创建tab内容
     */
    private void buildTabs(List<Tag> tags) {
        if (tags == null) {
            tags = new ArrayList<>();
        }
        mFragmentList.clear();
        mTagList.clear();

        //展示监听标签
        mFlFilterContainer.setVisibility(View.VISIBLE);

        //添加推荐tab数据
        Tag allTag = new Tag();
        allTag.setKeywordName("全部");
        allTag.setCategoryId(mCategoryId);
        allTag.setKeywordId(-2);
        mTagList.add(allTag);

        //添加全部页面
        Bundle argumentAll = CategoryMetadataFragment.createArgumentFromTab(allTag.getCategoryId(),
                mSelectMetadataId, mSelectMetadataValueId, from);
        FragmentHolder allHolder = new FragmentHolder(CategoryMetadataFragment.class, allTag.getKeywordName(), argumentAll);
        mFragmentList.add(allHolder);

        //默认选中哪个页面
        int selectDefPagePosition = 0;

        //之前手动插入页面的个数
        int nativeSize = mTagList.size();
        for (int i = 0; i < tags.size(); i++) {
            Tag tag = tags.get(i);
            if (tag == null) {
                continue;
            }
            Bundle argmentByHomeTab = KeywordMetadataFragment.createArgumentFromTab(
                    tag.getKeywordId(), tag.getCategoryId(), from);
            FragmentHolder holder = new FragmentHolder(KeywordMetadataFragment.class, tag.getKeywordName(), argmentByHomeTab);
            //记录tab数据
            mTagList.add(tag);
            //添加页面
            mFragmentList.add(holder);
            //默认选中的页面
            if (tag.getKeywordId() == mSelectKeywordId) {
                //使用keyWordId进行匹配tab
                //因为前面插入了一个 "全部"等页面，需要加nativeSize
                selectDefPagePosition = i + nativeSize;
            } else if (!TextUtils.isEmpty(mSelectKeywordName) && mSelectKeywordName.equals(tag.getKeywordName())) {
                //id没有匹配到，使用keyWordName进行匹配tab
                //因为前面插入了一个 "全部"等页面，需要加nativeSize
                selectDefPagePosition = i + nativeSize;
            }
        }

        mAdapter = new TabCommonAdapter(getChildFragmentManager(), mFragmentList);
        mPager.setAdapter(mAdapter);
        //默认设置缓存一个，加快打开首页速度，滑动后将缓存扩大
        mPager.setOffscreenPageLimit(1);
        mPager.setCurrentItem(selectDefPagePosition);
        mTabs.setViewPager(mPager);

        //选中的时候设置一次
        if (selectDefPagePosition == 0) {
            setSlideAble(true);
        } else {
            setSlideAble(false);
        }

        //当用户滑动tab后，将OffscreenPageLimit缓存扩大到6个
        mPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                int offscreenPageLimit = mPager.getOffscreenPageLimit();
                if (offscreenPageLimit != VIEW_PAGE_PAGE_LIMIT) {
                    Logger.d("HomeFragment", "当前缓存===" + offscreenPageLimit);
                    Logger.d("HomeFragment", "扩大ViewPage的缓存===");
                    mPager.setOffscreenPageLimit(VIEW_PAGE_PAGE_LIMIT);
                }
                if (position == 0) {
                    setSlideAble(true);
                } else {
                    setSlideAble(false);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        int selectPosition = 0;
        if (mTagList.size() > mPager.getCurrentItem() && mTagList.get(mPager.getCurrentItem()) != null) {
            selectPosition = mTagList.get(mPager.getCurrentItem()).getKeywordId();
        }
        mChooseKeywordWindow = new HomeChooseItemPopupWindow(getActivity(), mTagList, selectPosition);
        mChooseKeywordWindow.setOnChosenChangeListener(new HomeChooseItemPopupWindow.OnChosenChangeListener() {
            @Override
            public void onChosenChange(int chosenId, int chosenIdPosition) {
                if (mPager != null) {
                    mPager.setCurrentItem(chosenIdPosition, false);
                }
            }
        });
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
        TitleBar.ActionType action = new TitleBar.ActionType("action", TitleBar.RIGHT, R.string.main_string_empty_str, R.drawable.main_icon_title_bar_right_search_black, R.color.main_color_999999, TextView.class);
        action.setFontSize(14);
        titleBar.addAction(action, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                BaseFragment fragment = SearchActionRouter.getInstance().getFragmentAction() != null ? SearchActionRouter.getInstance().getFragmentAction().newSearchFragmentByHotWord(SearchActionRouter.TYPE_CATEGORY, mCategoryId, null) : null;
                if (fragment != null) {
                    startFragment(fragment);
                }
            }
        });
        titleBar.update();
        View view = titleBar.getActionView("action");
        if (view != null) {
            view.setVisibility(View.VISIBLE);
            view.setPadding(BaseUtil.dp2px(getActivity(), 7), 0, BaseUtil.dp2px(getActivity(), 5), 0);
        }
    }

    @Override
    public void onRefresh() {
        Logger.d("HomeCategoryContentTabFragment", "home触发刷新");
    }

    @Override
    public boolean canRepeatInActivity() {
        return true;
    }
}
