package com.ximalaya.ting.lite.main.base;

import androidx.annotation.ColorRes;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.main.R;

/**
 * <AUTHOR> on 2017/11/23.
 */

public abstract class BaseFragmentInMain extends BaseFragment2 {
    protected boolean mIsLoadingNetworkData = false;

    public BaseFragmentInMain() {

    }

    public BaseFragmentInMain(boolean canSlide, SlideView.IOnFinishListener onFinishListener) {
        super(canSlide, onFinishListener);
    }

    public BaseFragmentInMain(boolean canSiled, int slideViewContentViewLayoutType, @Nullable SlideView.IOnFinishListener onFinishListener) {
        super(canSiled, slideViewContentViewLayoutType, onFinishListener);
    }

    public BaseFragmentInMain(boolean canSlided, @SlideView.SlideViewContentViewLayoutType int contentViewType, @Nullable SlideView.IOnFinishListener onFinishListener, @ColorRes int contentViewBackgroundColor) {
        super(canSlided, contentViewType, onFinishListener, contentViewBackgroundColor);
    }

    public BaseFragmentInMain(boolean canSlided, int contentViewType, @Nullable SlideView.IOnFinishListener onFinishListener, boolean fullSlide) {
        super(canSlided, contentViewType, onFinishListener, fullSlide);
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }
}
