package com.ximalaya.ting.lite.main.manager

import androidx.fragment.app.FragmentManager
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.host.db.repository.BookHistoryRepository.queryInUser
import com.ximalaya.ting.android.host.db.utils.BookUtils.currentUserId
import com.ximalaya.ting.android.host.dialog.common.LiteHomeRecommendNovelDialog.Companion.newInstance
import com.ximalaya.ting.android.host.listener.IShowDialog
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.album.NovelListModel
import com.ximalaya.ting.android.host.model.ebook.EBook
import com.ximalaya.ting.android.host.model.ebook.RecommendNovelBean
import com.ximalaya.ting.android.host.util.NotificationUtil
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.DeviceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.lite.main.read.request.NovelExtensionPagesRequest
import org.json.JSONArray
import org.json.JSONObject
import java.lang.System.currentTimeMillis

object LiteHomeRecommendNovelManager {

    private const val TAG = "LiteHomeRecommendNovelManager"

    private const val MMKV_KEY_RECOMMEND_CONFIG = "mmkv_key_recommend_config"
    private const val MMKV_KEY_RECOMMEND_DIALOG = "mmkv_key_recommend_dialog"
    private const val MMKV_KEY_RECOMMEND_DIALOG_TS = "mmkv_key_recommend_dialog_ts"

    private const val KEY_NOT_SHOW_CHANNEL_ID = "key_not_show_channel_id"

    private var mShowDate = ""

    fun loadRecommendBookList(fragmentManager: FragmentManager) {
        FuliLogger.log(TAG, "loadRecommendBookList")

        if (ChildProtectManager.isChildProtectOpen(BaseApplication.getMyApplicationContext())) {
            return
        }

        if (mShowDate == DateTimeUtil.getCurrentDate4yyyyMMdd()) {
            FuliLogger.log(TAG, "今天展示过")
            return
        }
        try {
            initConfigureJsonData()

            val exchangeChannelId = DeviceUtil.getExchangeChannelId()
            // 本地有换量渠道
            if (!exchangeChannelId.isNullOrEmpty()) {
                val noShowIds =
                    MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                        .getString(KEY_NOT_SHOW_CHANNEL_ID, "")
                // 线上配置了不显示的渠道
                if (!noShowIds.isNullOrEmpty()) {
                    val data = noShowIds.split(",").find {
                        it == exchangeChannelId
                    }
                    if (!data.isNullOrEmpty()) {
                        FuliLogger.log(
                            TAG,
                            "配置该渠道不显示 channelId:$exchangeChannelId  noShowIds:$noShowIds"
                        )
                        return
                    }
                }
            }

            val configureJson = getConfigureJsonData()
            val poolId = configureJson.optInt("recommendPoolId", 62)
            val pageId = configureJson.optInt("recommendPageId", 1)
            val frequency = configureJson.optInt("frequency", 3)
            val switch = configureJson.optBoolean("switch", false)
            if (!switch) {
                FuliLogger.log(TAG, "开关已关闭")
                return
            }
            if (!isNeedNextShowBookRecommendDialog(frequency)) {
                FuliLogger.log(TAG, "不允许展示")
                return
            }
            val curUserBookList = queryInUser(currentUserId)

            if (CollectionUtil.isNotEmpty(curUserBookList)) return
            val params = HashMap<String, String>()
            params["poolId"] = "$poolId"
            params["pageId"] = "$pageId"
            params["pageSize"] = "20"

            NovelExtensionPagesRequest.getTodayRecommendBookList(
                params,
                object : IDataCallBack<RecommendNovelBean> {
                    override fun onSuccess(bean: RecommendNovelBean?) {
                        FuliLogger.log(TAG, "onSuccess bean:${bean}")
                        if (bean != null && CollectionUtil.isNotEmpty(bean.dataList)) {
                            val list = mutableListOf<EBook>()
                            if (bean.dataList!!.size > 6) {
                                for (i in 0..5) {
                                    val length = bean.dataList!!.size
                                    val index = (Math.random() * length).toInt()
                                    FuliLogger.log(TAG, "total:${length} index:${index}")
                                    list.add(bean.dataList!!.removeAt(index))
                                }
                            } else {
                                list.addAll(bean.dataList!!)
                            }

                            val homeRecommendNovelDialog = newInstance(NovelListModel(list))

                            // 增加3秒延时  因为优先级低于登录引导弹窗
                            HandlerManager.postOnUIThreadDelay({
                                DialogShowManager.showDialog(object : IShowDialog() {
                                    override fun getDialogName(): String {
                                        return homeRecommendNovelDialog::class.java.simpleName
                                    }

                                    override fun show() {
                                        saveShowBookRecommendDialogCacheData()
                                        homeRecommendNovelDialog.show(fragmentManager, "")
                                        mShowDate = DateTimeUtil.getCurrentDate4yyyyMMdd()
                                    }
                                })
                            }, 2000)
                        }
                    }

                    override fun onError(code: Int, message: String?) {
                        FuliLogger.log(TAG, "onError code:${code} message:${message}")
                    }
                })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 是否超过首次安装需要显示的天数
     */
    private fun isFirstInstallShowMoreNeedDay(): Boolean {
        val firstInstallTime = ToolUtil.firstInstallTime(BaseApplication.getMyApplicationContext())
        return NotificationUtil.getDayDuration(firstInstallTime) > 3
    }

    /**
     * 是否超过需要显示的天数
     */
    private fun isMoreNeedDay(firstInstallTime: Long, days: Int): Boolean {
        return NotificationUtil.getDayDuration(firstInstallTime) >= days
    }

    private fun isFirstShowBookRecommendDialog(): Boolean {
        return TextUtils.isEmpty(getFirstShowBookRecommendDialogCacheData())
    }

    private fun isNeedNextShowBookRecommendDialog(frequency: Int): Boolean {
        val cacheData = getFirstShowBookRecommendDialogCacheData()
        val saveDataJson = if (TextUtils.isEmpty(cacheData)) {
            JSONObject()
        } else {
            JSONObject(cacheData)
        }
        val lastSaveTimeMillis = saveDataJson.optLong(MMKV_KEY_RECOMMEND_DIALOG_TS)
        if (lastSaveTimeMillis == 0L) {
            return true
        }
        FuliLogger.log(
            TAG,
            "lastSaveTimeMillis:$lastSaveTimeMillis isMoreNeedDay:${
                isMoreNeedDay(
                    lastSaveTimeMillis,
                    frequency
                )
            }"
        )
        return isMoreNeedDay(lastSaveTimeMillis, frequency)
    }

    /**
     * 保存dialog用户id对应的显示时间
     */
    private fun saveShowBookRecommendDialogCacheData() {
        val cacheData = getFirstShowBookRecommendDialogCacheData()
        val saveDataJson = if (TextUtils.isEmpty(cacheData)) {
            JSONObject()
        } else {
            JSONObject(cacheData)
        }
        saveDataJson.put(MMKV_KEY_RECOMMEND_DIALOG_TS, currentTimeMillis())
        MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
            .saveString(MMKV_KEY_RECOMMEND_DIALOG, saveDataJson.toString())
    }

    private fun getFirstShowBookRecommendDialogCacheData(): String {
        return MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
            .getString(MMKV_KEY_RECOMMEND_DIALOG, "")
    }

    private fun getConfigureJsonData(): JSONObject {
        val novelPopConfigure =
            MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                .getString(MMKV_KEY_RECOMMEND_CONFIG, "")
        if (TextUtils.isEmpty(novelPopConfigure)) return JSONObject()
        return JSONObject(novelPopConfigure)
    }

    fun initConfigureJsonData() {
        try {
            val novelPopConfigure = ConfigureCenter.getInstance().getString(
                CConstants.Group_Base.GROUP_NAME, "Novel_pop-ups"
            )
            MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                .saveString(MMKV_KEY_RECOMMEND_CONFIG, novelPopConfigure)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            val notShowChannelId = ConfigureCenter.getInstance()
                .getJsonString(CConstants.Group_Base.GROUP_NAME, "changes_NovelPool_switch", "")
            if (notShowChannelId.isNullOrEmpty()) {
                MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                    .saveString(KEY_NOT_SHOW_CHANNEL_ID, "")
            } else {
                val array = JSONArray(notShowChannelId)
                val stringBuffer = StringBuffer()
                for (index in (0 until array.length())) {
                    stringBuffer.append(array.opt(index)).append(",")
                }
                val result: String = if (stringBuffer.isNotEmpty()) {
                    stringBuffer.substring(0, stringBuffer.length - 1)
                } else {
                    stringBuffer.toString()
                }
                MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                    .saveString(KEY_NOT_SHOW_CHANNEL_ID, result)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}