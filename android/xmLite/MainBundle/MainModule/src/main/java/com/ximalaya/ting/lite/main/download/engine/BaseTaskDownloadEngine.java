package com.ximalaya.ting.lite.main.download.engine;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.opensdk.httputil.Config;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.download.bean.BufferData;
import com.ximalaya.ting.lite.main.download.bean.MultiTaskInfo;
import com.ximalaya.ting.lite.main.download.bean.SingleTaskInfo;
import com.ximalaya.ting.lite.main.download.bean.SingleThreadTask;
import com.ximalaya.ting.lite.main.download.bean.Statistics;
import com.ximalaya.ting.lite.main.download.bean.TaskInfo;
import com.ximalaya.ting.lite.main.download.connection.DownloadOkClientUrlConnection;
import com.ximalaya.ting.lite.main.download.connection.DownloadUrlConnection;
import com.ximalaya.ting.lite.main.download.inter.IDownloadConnection;
import com.ximalaya.ting.lite.main.download.inter.IDownloadEngine;
import com.ximalaya.ting.lite.main.download.inter.IEngineObserver;
import com.ximalaya.ting.lite.main.download.manager.NewTaskExecutor;
import com.ximalaya.ting.lite.main.download.utils.BufferDataMgr;
import com.ximalaya.ting.lite.main.download.utils.DownloadConst;
import com.ximalaya.ting.lite.main.download.utils.TaskStateConst;
import com.ximalaya.ting.lite.main.download.utils.TaskUtil;

import java.io.File;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> feiwen
 * date   : 2019-07-17
 * desc   :
 */
public abstract class BaseTaskDownloadEngine implements IDownloadEngine {

    public static final int DEFAULT_THREAD_NUM = 1;// 限制的线程数
    static final long BLOCK_SIZE = 1024 * 1024 * 5;// 单个线程最多的下载量
    private static final int MAX_BUFFER_COUNT = 20;
    String mTag;
    String mTagName = "";
    String mDownloadUrl;
    String mSaveDir;
    String mFileName;
    IEngineObserver mObserver;
    CountDownLatch mCountDown;
    NewTaskExecutor mDefaultExecutor;
    BlockingQueue<BufferData> memoryBufferQueue;
    BufferDataMgr memoryBufferMgr;
    FileWriteTask mWriteTask;
    MultiTaskInfo multiTaskInfo;
    Statistics mStatistics;
    IDownloadConnection mPreConnection;
    Config mConfig;
    int mDownloadConnectionType;
    boolean mRequestCancel = false;
    private int mState = TaskStateConst.STATE_PENDING;
    int mRetryCount;

    static final ThreadFactory sThreadFactory = new ThreadFactory() {
        private final AtomicInteger mCount = new AtomicInteger(1);

        public Thread newThread(@NonNull Runnable r) {
            return new Thread(r, "BaseTaskDownloadEngine #" + mCount.getAndIncrement());
        }
    };

    BaseTaskDownloadEngine(int threadNum) {
        mTag = MessageFormat.format("DownloadEngine {0}", mTagName);
        memoryBufferQueue = new ArrayBlockingQueue<>(MAX_BUFFER_COUNT);
        memoryBufferMgr = new BufferDataMgr();
        memoryBufferMgr.initBufferSize(MAX_BUFFER_COUNT * 2);
        mDefaultExecutor = new NewTaskExecutor(sThreadFactory, mTag, threadNum);
        mStatistics = new Statistics();
    }

    @Override
    public void setData(TaskInfo taskInfo, IEngineObserver observer) {
        mTagName = taskInfo.getFileName() + "_" + hashCode();
        mSaveDir = taskInfo.getDirPath();
        mFileName = taskInfo.getFileName();
        mDownloadUrl = taskInfo.getUrl();
        mConfig = taskInfo.getConfig();
        mDownloadConnectionType = taskInfo.getDownloadConnectionType();
        mObserver = observer;
        mRetryCount = taskInfo.getRetryCount();
    }

    @Override
    public void start() throws Exception {
        if (!tryChangeState(TaskStateConst.STATE_RUNNING)) {
            Logger.w(mTag, "正在下载");
            return;
        }
        Logger.d(mTag, "开始任务");
        try {
            executeStart();
        } catch (Exception e) {
            Logger.d(mTag, e.toString());
            mState = TaskStateConst.STATE_ERROR;
            this.mObserver.onError(TaskUtil.getErrorCode(e));
            Logger.d(mTag, "通知错误");
        } finally {
            if (mCountDown != null) {
                while (mCountDown.getCount() > 0) {
                    mCountDown.countDown();
                }
            }
            // 清除内存中的数据
            reset();
            if (mWriteTask != null && mWriteTask.isBusy()) {
                mWriteTask.stop();
            }
            if (mPreConnection != null) {
                mPreConnection.disconnect();
            }
        }
        // 如果是请求停止，这个时候停止完毕，则通知出去
        if (mRequestCancel) {
            mState = TaskStateConst.STATE_PAUSED;
            mRequestCancel = false;
            mObserver.onPaused();
            Logger.d(mTag, "通知暂停");
        }
        Logger.d(mTag, "任务结束");
    }

    private void executeStart() throws Exception {
        try {
            innerStart();
        } catch (Exception ex) {
            requestRetryDownload();
        }
    }

    abstract void innerStart() throws Exception;

    void requestRetryDownload() throws Exception {
        if (mRetryCount-- > 0) {
            innerStart();
        }
    }

    /**
     * 内部使用的停止方法
     */
    private void innerStop() {
        ArrayList<Runnable> list = mDefaultExecutor.fetchExecutingTask();
        ArrayList<Runnable> waitingList = mDefaultExecutor.fetchWaitingTask();
        // 停止等待所有线程
        for (Runnable runnable : list) {
            if (runnable instanceof SingleThreadTask) {
                SingleThreadTask task = (SingleThreadTask) runnable;
                task.stop();
            }
        }
        // count down未开始下载的任务
        int size = waitingList.size();
        while (size > 0) {
            mCountDown.countDown();
            size--;
        }
        // 停止写线程的读取
        if (mWriteTask != null && mWriteTask.isBusy()) {
            mWriteTask.stop();
        }
        memoryBufferQueue.clear();
        memoryBufferMgr.recycleBuffer();
    }

    @Override
    public void stop() {
        if (mState != TaskStateConst.STATE_PAUSED) {
            mRequestCancel = true;
            innerStop();
        } else {
            Logger.d(mTag, "已暂停");
        }
    }

    @Override
    public void delete(boolean deleteTemp) {
        if (deleteTemp) {
            innerStop();
            // 删除配置文件
            new File(mSaveDir, mFileName + DownloadConst.MD_CONFIG_SUFFIX).delete();
            // 删除临时文件
            new File(mSaveDir, mFileName + DownloadConst.MD_DATA_SUFFIX).delete();
        }
    }

    @Override
    public boolean busy() {
        return mState == TaskStateConst.STATE_RUNNING;
    }

    IDownloadConnection getDownloadConnection(String url) throws IOException {
        if(mDownloadConnectionType == DownloadConst.DOWNLOAD_URL_CONNECTION_OKHTTP) {
            return new DownloadOkClientUrlConnection.Factory().create(url, mConfig);
        } else {
            return new DownloadUrlConnection.Factory().create(url, mConfig);
        }
    }

    /**
     * 重置数据
     */
    @Override
    public void reset() {
        mStatistics.reset();
        memoryBufferQueue.clear();
        memoryBufferMgr.recycleBuffer();
        if (mPreConnection != null) {
            mPreConnection.disconnect();
        }
        if(multiTaskInfo != null) {
            multiTaskInfo.reset();
        }
        mState = TaskStateConst.STATE_PENDING;
    }

    /**
     * 尝试改变任务状态，可能会失败.
     */
    private boolean tryChangeState(int state) {
        if (mState == state) {
            return false;
        } else {
            mState = state;
        }
        return true;
    }

    /**
     * 根据任务信息，打包数据
     */
    MultiTaskInfo dispatchTask(long total, long minSize) {
        MultiTaskInfo info = new MultiTaskInfo();
        AtomicInteger atomic = new AtomicInteger(0);
        boolean continueSplit = true;
        info.totalSize = total;
        info.blockSize = minSize;
        long rest = 0;
        long continuePos = 0;
        // 可读大小
        long read = 0;
        if (total > minSize) {
            read = minSize;
        } else if (total > 0) {
            read = total;
        } else {
            return info;
        }
        do {
            SingleTaskInfo data = new SingleTaskInfo();
            data.taskId = atomic.incrementAndGet();
            data.beginPos = continuePos;
            data.endPos = continuePos + read;
            data.haveDoneSize = 0;
            info.list.add(data);
            rest = total - (continuePos + read);
            if (rest >= minSize) {
                continuePos += minSize;
                read = minSize;
            } else if (rest > 0) {
                continuePos = total - rest;
                read = rest;
            } else {
                continueSplit = false;
            }
        } while (continueSplit);

        return info;
    }

    /**
     * 计算已完成大小
     */
    static long calculateDoneSize(ArrayList<SingleTaskInfo> list) {
        long size = 0;
        for (SingleTaskInfo info : list) {
            size += info.haveDoneSize;
        }
        return size;
    }
}
