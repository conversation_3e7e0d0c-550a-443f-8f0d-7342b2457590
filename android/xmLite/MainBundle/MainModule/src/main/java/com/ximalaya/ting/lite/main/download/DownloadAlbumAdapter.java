package com.ximalaya.ting.lite.main.download;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.downloadservice.DownLoadedAlbum;
import com.ximalaya.ting.android.downloadservice.base.BaseDownloadTask;
import com.ximalaya.ting.android.downloadservice.base.IDownloadStatus;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.base.BaseMainAlbumAdapter;

import java.util.List;


/**
 * Created by easoll on 17/6/23.
 * <AUTHOR>
 */

public class DownloadAlbumAdapter extends BaseMainAlbumAdapter {
    public DownloadAlbumAdapter(MainActivity activity, List<Album> listData) {
        super(activity, listData);
    }


    @Override
    public int getConvertViewIdByPosition(int position) {
        return R.layout.main_item_album_download;
    }


    @Override
    public BaseViewHolder buildHolderByPosition(View convertView, int position) {
        return new DownloadAlbumHolder(convertView);
    }

    /**
     * 获取专辑中的已下载完成的声音和视频的总大小
     *
     * @param tasks
     * @return
     */
    private String getFileSize(@Nullable List<Track> tasks) {
        if (tasks == null) {
            return "";
        }
        int count = tasks.size();
        long fileSize = 0;
        for (int i = 0; i != count; i++) {
            if (tasks.get(i) == null)
                continue;

            Track track = tasks.get(i);
            //获取声音大小

            if(track.getDownloadStatus() == IDownloadStatus.DOWNLOAD_FINISH &&  //只计算已下载完成的
                    !TextUtils.isEmpty(track.getDownloadedSaveFilePath())){
                if (track.getDownloadedSaveFilePath().endsWith(".xm")) {
                    fileSize += track.getChargeFileSize();
                } else {
                    fileSize += track.getDownloadedSize();
                }
            }

            // 获取视频大小
            if(track.getVideoDownloadStatus() == IDownloadStatus.DOWNLOAD_FINISH &&  //只计算已下载完成的
                    !TextUtils.isEmpty(track.getDownloadedVideoSaveFilePath())){
                fileSize += track.getVideoDownloadedSize();
            }
        }
        return StringUtil.toMBFormatString(fileSize);
    }

    @Override
    public void bindViewDatas(@NonNull BaseViewHolder h, @Nullable Album t, int position) {
        super.bindViewDatas(h, t, position);

        AlbumM albumM;
        if (!(t instanceof AlbumM)) {
            return;
        }
        albumM = (AlbumM) t;

        DownloadAlbumHolder holder = (DownloadAlbumHolder) h;
        DownLoadedAlbum downLoadedAlbum = albumM.getDownLoadedAlbum();
        if (downLoadedAlbum == null) {
            holder.fileSize.setText("0M");
        } else {
            SubordinatedAlbum subordinatedAlbum = downLoadedAlbum.getAlbum();
            List<Track> tracks = RouteServiceUtil.getDownloadService().getAllDownloadedTracksInAlbum(subordinatedAlbum.getAlbumId());
            if (tracks.size() > 0) {
                holder.fileSize.setText(getFileSize(tracks) + "M");
            } else {
                holder.fileSize.setText("0M");
            }
        }

        String trackCount = StringUtil.getFriendlyNumStr(albumM.getIncludeTrackCount()) + " 集";
        holder.trackCount.setText(trackCount);
        setClickListener(holder.actionDelete, albumM, position, holder);
        AutoTraceHelper.bindData(holder.actionDelete,AutoTraceHelper.MODULE_DEFAULT,albumM);

        AutoTraceHelper.bindData(holder.root,AutoTraceHelper.MODULE_DEFAULT,new AutoTraceHelper.DataWrap(position,albumM));

        if(AlbumTagUtil.getAlbumCoverTag(albumM) != -1) {
            holder.ivTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable(albumM, context, AlbumTagUtil.ZOOM_IN_RATIO_78_percent));
            holder.ivTag.setVisibility(View.VISIBLE);
        } else {
            holder.ivTag.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public void onClick(View view, final Album t, int position, BaseViewHolder holder) {
        if (view.getId() == R.id.main_iv_album_delete) {
            new DialogBuilder(context)
                    .setMessage(R.string.main_confirm_delete_all_track_in_album)
                    .setOkBtn(new DialogBuilder.DialogCallback() {

                        @Override
                        public void onExecute() {
                            if (!(t instanceof AlbumM) || ((AlbumM) t).getDownLoadedAlbum() == null)
                                return;
                            DownLoadedAlbum da = ((AlbumM) t).getDownLoadedAlbum();
                            if (da.getAlbum() != null) {
                                RouteServiceUtil
                                        .getDownloadService()
                                        .removeAllTrackListInAlbum(da.getAlbum().getAlbumId());
                                List<Track> trackList = RouteServiceUtil
                                        .getDownloadService()
                                        .getAllDownloadedTracksInAlbum(da.getAlbum().getAlbumId());
                                if (trackList != null && trackList.size() > 0){
                                    for (Track track:trackList){
                                        BaseDownloadTask downloadTask
                                                                = RouteServiceUtil
                                                                    .getDownloadService()
                                                                    .queryVideoTask(track);
                                        if (downloadTask != null) {
                                            RouteServiceUtil
                                                        .getDownloadService()
                                                        .deleteDownloadTask(downloadTask);
                                        }
                                    }
                                }
                                deleteListData(t);
                            }
                        }
                    }).showConfirm();
        }
    }

    @Override
    protected String getDefaultSubTitle(@Nullable Album album) {
        if (album == null) {
            return "";
        }
        String subTitle = "";
        DownLoadedAlbum downAlbum = ((AlbumM) album).getDownLoadedAlbum();
        if (downAlbum != null && !TextUtils.isEmpty(downAlbum.getAnnouncerNiceName())) {
            subTitle = downAlbum.getAnnouncerNiceName();
        }

        return subTitle;
    }

    public static class DownloadAlbumHolder extends BaseMainAlbumHolder {
        TextView fileSize;  //文件大小
        TextView trackCount; //专辑数
        ImageView actionDelete; //删除按钮
        ImageView ivTag;

        DownloadAlbumHolder(View convertView) {
            super(convertView);

            fileSize = (TextView) convertView.findViewById(R.id.main_tv_file_size);
            trackCount = (TextView) convertView.findViewById(R.id.main_tv_track_count);
            actionDelete = (ImageView) convertView.findViewById(R.id.main_iv_album_delete);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
        }
    }
}
