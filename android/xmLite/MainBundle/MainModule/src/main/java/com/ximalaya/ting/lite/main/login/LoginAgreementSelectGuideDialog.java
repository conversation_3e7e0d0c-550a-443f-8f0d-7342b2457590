package com.ximalaya.ting.lite.main.login;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.activity.web.WebActivity;
import com.ximalaya.ting.android.host.manager.login.XiMaUseRuleManager;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.view.UnderlineClickableSpan;

/**
 * 登录选中弹框提醒
 */
public class LoginAgreementSelectGuideDialog extends XmBaseDialog {

    private Activity mActivity;
    //运营商协议名称
    private String quickOperatorProtocolName = "";
    //运营商协议跳转地址
    private String quickOperatorProtocolUrl = "";

    public LoginAgreementSelectGuideDialog(@NonNull Activity context) {
        super(context);
        mActivity = context;
    }

    public LoginAgreementSelectGuideDialog(@NonNull Activity context, int themeResId) {
        super(context, themeResId);
        mActivity = context;
    }

    protected LoginAgreementSelectGuideDialog(@NonNull Activity context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        mActivity = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //必须放在setContentView之前，如果使用getDecorView,也要放在getDecorView之前
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        if (getWindow() != null) {
            getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            WindowManager.LayoutParams lp = getWindow().getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            lp.gravity = Gravity.CENTER;
            getWindow().setAttributes(lp);
        }
        setCanceledOnTouchOutside(false);
        setCancelable(true);
        setContentView(R.layout.main_dialog_login_agremment_select_guide);
        initUI();
    }

    private void initUI() {
        RelativeLayout rlClose = findViewById(R.id.main_rl_error_dialog_close);
        rlClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        //去实名认证，跳转到设置页面
        TextView mTvOK = findViewById(R.id.main_btn_know);
        mTvOK.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                if (mAgreementOkClick != null) {
                    mAgreementOkClick.onAgreementBtnClick();
                }
                dismiss();
            }
        });
        AutoTraceHelper.bindData(rlClose, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(mTvOK, AutoTraceHelper.MODULE_DEFAULT, "");

        initAgreementView();
    }


    private void initAgreementView() {
        TextView tvAgreementHint = findViewById(R.id.main_tv_agreement_hint);
        //同意《中国联通认证服务协议》和《用户协议》、《隐私协议》并授权喜马拉雅极速版获取本机号码
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder("");
        String hintText = "请先仔细阅读";
        String registerRule = "《用户协议》";
        String comma = "和";
        String privacyRule = "《隐私协议》";
        //运营商协议
        String operatorProtocolNameMark = "、";
        String operatorProtocolName = "";
        if (!TextUtils.isEmpty(quickOperatorProtocolName)) {
            operatorProtocolName = "《" + quickOperatorProtocolName + "》";
        }
        spannableStringBuilder.append(hintText);
        spannableStringBuilder.append(registerRule);
        spannableStringBuilder.append(comma);
        spannableStringBuilder.append(privacyRule);
        if (!TextUtils.isEmpty(operatorProtocolName)) {
            spannableStringBuilder.append(operatorProtocolNameMark);
            spannableStringBuilder.append(operatorProtocolName);
        }
        spannableStringBuilder.append("， 确认是否同意");

        //隐私协议点击
        int protocolColor = ContextCompat.getColor(mActivity, R.color.host_color_app_theme_color);

        //用户协议点击
        UnderlineClickableSpan registerRuleClickSpan = new UnderlineClickableSpan(protocolColor) {
            @Override
            public void onClick(View widget) {
                Intent intent = new Intent(mActivity, WebActivity.class);
                intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, XiMaUseRuleManager.getRegisterRule());
                mActivity.startActivity(intent);
            }
        };
        int registerRuleStartIndex = hintText.length();
        int registerRuleEndIndex = registerRuleStartIndex + registerRule.length();
        spannableStringBuilder.setSpan(registerRuleClickSpan, registerRuleStartIndex, registerRuleEndIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        UnderlineClickableSpan privacyRuleClickSpan = new UnderlineClickableSpan(protocolColor) {
            @Override
            public void onClick(View widget) {
                Intent intent = new Intent(mActivity, WebActivity.class);
                intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, XiMaUseRuleManager.getPrivacyRule());
                mActivity.startActivity(intent);
            }
        };
        int privacyRuleStartIndex = hintText.length() + registerRule.length() + comma.length();
        int privacyRuleEndIndex = privacyRuleStartIndex + privacyRule.length();
        spannableStringBuilder.setSpan(privacyRuleClickSpan, privacyRuleStartIndex, privacyRuleEndIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        //运营商协议点击
        if (!TextUtils.isEmpty(operatorProtocolName)) {
            //隐私协议点击
            UnderlineClickableSpan operatorProtocolClickSpan = new UnderlineClickableSpan(protocolColor) {
                @Override
                public void onClick(View widget) {
                    if (!TextUtils.isEmpty(quickOperatorProtocolUrl)) {
                        Intent intent = new Intent(mActivity, WebActivity.class);
                        intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, quickOperatorProtocolUrl);
                        mActivity.startActivity(intent);
                    }
                }
            };
            int operatorProtocolStartIndex = hintText.length() + registerRule.length() + comma.length() + privacyRule.length() + operatorProtocolNameMark.length();
            int operatorProtocolEndIndex = operatorProtocolStartIndex + operatorProtocolName.length();
            spannableStringBuilder.setSpan(operatorProtocolClickSpan, operatorProtocolStartIndex, operatorProtocolEndIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        tvAgreementHint.setText(spannableStringBuilder);
        tvAgreementHint.setMovementMethod(LinkMovementMethod.getInstance());
        tvAgreementHint.setHighlightColor(Color.TRANSPARENT);
    }

    /**
     * 一键登录运营商协议
     */
    public void setQuickProtocolConfig(String quickOperatorProtocolName, String quickOperatorProtocolUrl) {
        if (TextUtils.isEmpty(quickOperatorProtocolName) || TextUtils.isEmpty(quickOperatorProtocolUrl)) {
            return;
        }
        //跳转链接不合法
        if (!quickOperatorProtocolUrl.trim().startsWith("http")) {
            return;
        }
        this.quickOperatorProtocolName = quickOperatorProtocolName;
        this.quickOperatorProtocolUrl = quickOperatorProtocolUrl;
    }

    private IDialogAgreementOkClick mAgreementOkClick;

    public void setAgreementOkClick(IDialogAgreementOkClick mAgreementOkClick) {
        this.mAgreementOkClick = mAgreementOkClick;
    }

    public interface IDialogAgreementOkClick {
        void onAgreementBtnClick();
    }
}
