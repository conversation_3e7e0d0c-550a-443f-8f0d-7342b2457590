package com.ximalaya.ting.lite.main.home.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList
import kotlinx.android.synthetic.main.main_item_hot_search_rank.view.*

/**
 * Created by duming<PERSON> on 2020/6/17
 *
 * Desc: 热搜榜
 */
class HotSearchRankProvider @JvmOverloads constructor(
        val mFragment: BaseFragment2,
        private val mExtraModel: HomeRecommendExtraViewModel? = null,
        private val spanCount: Int = 5
) : IMulitViewTypeViewAndData<HotSearchRankProvider.Holder, MainAlbumMList> {

    private var mModel: MainAlbumMList? = null

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_hot_search_rank, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        val holder = Holder(convertView)
        initRvAlbum(holder)
        return holder
    }

    private fun initRvAlbum(holder: Holder) {
        val context = BaseApplication.getMyApplicationContext()
        with(holder.rootView) {
            mainRvHotSearchRank.setDisallowInterceptTouchEventView(mFragment.view as ViewGroup)
            val adapter = HotSearchRankAdapter(mFragment)
            mainRvHotSearchRank.adapter = adapter
            holder.adapter = adapter

            val gridLayoutManager = GridLayoutManager(context,
                    spanCount, LinearLayoutManager.HORIZONTAL, false)
            mainRvHotSearchRank.layoutManager = gridLayoutManager
        }
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<MainAlbumMList>, convertView: View, position: Int) {
        val model = t.getObject()
        mModel = model
        if (model is MainAlbumMList) {
            with(holder.rootView) {
                mainTvTitle.text = model.title
                holder.adapter?.let {
                    it.mDataList = model.hotSearchRankList
                    it.notifyDataSetChanged()
                }
            }
        }
    }

    class Holder(var rootView: View) : BaseViewHolder() {
        var adapter: HotSearchRankAdapter? = null
    }

}