package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.home.adapter.PlayHistoryFloorItemAdapter;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeHistoryPageViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeItemPlayHistoryViewModel;
import com.ximalaya.ting.lite.main.newhome.fragment.LiteHomeRecommendFragment;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by qinhuifeng on 2019-07-16
 * <p>
 * 我听楼层中的--播放历史页面
 *
 * <AUTHOR>
 */
public class HomeItemPlayHistoryFragment extends BaseFragment2 implements View.OnClickListener {

    private RecyclerView mRvList;
    private LinearLayout mLayoutNoHistory;
    private TextView mNoHistoryText;
    private TextView mGoToLogin;

    private PlayHistoryFloorItemAdapter mAdapter;
    private List<HomeItemPlayHistoryViewModel> mDataList = new CopyOnWriteArrayList<>();

    public HomeItemPlayHistoryFragment() {
        super(false, null);
    }

    @Override
    protected String getPageLogicName() {
        return "HomeItemPlayHistoryFragment";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRvList = findViewById(R.id.main_rv_list);
        mLayoutNoHistory = findViewById(R.id.main_layout_no_history);
        mNoHistoryText = findViewById(R.id.main_no_history_text);
        mGoToLogin = findViewById(R.id.main_goto_login);
        mAdapter = new PlayHistoryFloorItemAdapter(this, mActivity, mDataList);
        mRvList.setLayoutManager(new LinearLayoutManager(mActivity, LinearLayoutManager.HORIZONTAL, false));
        mRvList.setAdapter(mAdapter);
    }

    @Override
    protected void loadData() {

    }

    public void updatePlayHistoryFragmentList(HomeHistoryPageViewModel homeHistoryPageViewModel) {
        if (homeHistoryPageViewModel == null || mDataList == null || homeHistoryPageViewModel.historyViewModelList == null || homeHistoryPageViewModel.historyViewModelList.size() == 0) {
            FuliLogger.log("订阅历史模块==:History=mDataList=null");
            //没有获取到播放历史，展示空页面
            showNoPlayHistory();
            return;
        }
        FuliLogger.log("订阅历史模块==:History=更新数量=" + homeHistoryPageViewModel.historyViewModelList.size());
        //先添加数据，可能mRvList还没创建
        mDataList.clear();
        mDataList.addAll(homeHistoryPageViewModel.build());

        if (mRvList == null || mLayoutNoHistory == null) {
            return;
        }
        mLayoutNoHistory.setVisibility(View.GONE);
        mRvList.setVisibility(View.VISIBLE);
        if (mAdapter == null) {
            FuliLogger.log("订阅历史模块=History=:mAdapter=null");
            return;
        }
        if (mRvList == null) {
            FuliLogger.log("订阅历史模块=History=:mRvList=null");
            return;
        }
        mAdapter.notifyDataSetChanged();
    }


    private void showNoPlayHistory() {
        //可能还没创建
        if (mLayoutNoHistory == null || mRvList == null || mGoToLogin == null) {
            return;
        }
        mLayoutNoHistory.setVisibility(View.VISIBLE);
        mRvList.setVisibility(View.GONE);
        if (!UserInfoMannage.hasLogined()) {
            mNoHistoryText.setText("无最近播放的节目，登录可同步播放记录哦");
            mGoToLogin.setVisibility(View.VISIBLE);
            mGoToLogin.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //去登录
                    UserInfoMannage.gotoLogin(mActivity);
                }
            });
        } else {
            mNoHistoryText.setText("无最近播放的节目");
            mGoToLogin.setVisibility(View.GONE);
        }
    }

    @Override
    public void onMyResume() {
        //不做状态颜色处理
        setFilterStatusBarSet(true);
        super.onMyResume();
    }

    @Override
    public int getTitleBarResourceId() {
        return -1;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_item_fragment_play_history;
    }

    @Override
    public void onClick(View v) {

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    @Override
    protected boolean isShowSevenDay() {
        return true;
    }

    @Override
    public boolean isGlobalFloatViewGray() {
        if (getParentFragment() instanceof LiteHomeRecommendFragment) {
            return ((LiteHomeRecommendFragment) getParentFragment()).isGlobalFloatViewGray();
        }
        return false;
    }
}
