package com.ximalaya.ting.lite.main.base.album;

import android.content.Context;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.business.unlock.view.UnlockCountDownTextView;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.trace.TraceFreeAlbumManager;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBackNew;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.lite.main.model.album.AlbumShareModel;
import com.ximalaya.ting.lite.main.model.album.AlbumUserInfo;

import java.util.ArrayList;
import java.util.List;

public class UnLockVipAlbumAdapter extends HolderAdapter<AlbumShareModel> {

    private Context mContext;
    private List<AlbumShareModel> mTotalData;
    private IDataCallBackNew<AlbumM> iDataCallBack;
    private boolean isHasExpand;

    public UnLockVipAlbumAdapter(Context context, List<AlbumShareModel> listData,
                                 IDataCallBackNew<AlbumM> iDataCallBack) {
        super(context, listData);
        mContext = context;
        this.iDataCallBack = iDataCallBack;
    }

    public boolean isHasExpand() {
        return isHasExpand;
    }

    public void setHasExpand(boolean hasExpand) {
        isHasExpand = hasExpand;
    }

    public void updateTotalData(List<AlbumShareModel> listData) {
        this.mTotalData = listData;
        if (mTotalData != null && mTotalData.size() > 1) {
            iDataCallBack.updateContainerHeight(null, true, isHasExpand ? listData.size() : 1);
        } else {
            iDataCallBack.updateContainerHeight(null, false, 1);
        }
    }

    public List<AlbumShareModel> getMTotalData() {
        return mTotalData;
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_vip_album_unlock_item;
    }

    /**
     * 建立视图Holder
     */
    @Override
    public HolderAdapter.BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void onClick(View view, final AlbumShareModel t, final int position, BaseViewHolder holder) {

    }

    @Nullable
    @Override
    public List<AlbumShareModel> getListData() {
        return super.getListData();
    }

    @Override
    public void bindViewDatas(final BaseViewHolder holder, final AlbumShareModel shareModel, final int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        if (viewHolder.root != null) {
            if (!TextUtils.isEmpty(shareModel.getResourceName())) {
                viewHolder.root.setContentDescription(shareModel.getResourceName());
            } else {
                viewHolder.root.setContentDescription("");
            }
        }
        ImageManager.from(mContext).displayImage(viewHolder.cover, shareModel.getResourceCoverPage(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145, com.ximalaya.ting.android.host.R.drawable.host_default_album_145);
        if (!TextUtils.isEmpty(shareModel.getResourceName())) {
            viewHolder.title.setText(shareModel.getResourceName());
        } else {
            viewHolder.title.setText("");
        }
        AlbumM albumM = shareModel.getBasicAlbum();
        if (AlbumTagUtil.getAlbumCoverTag(albumM) != -1) {
            viewHolder.ivTag.setImageResource(AlbumTagUtil.getAlbumCoverTag(albumM));
            viewHolder.ivTag.setVisibility(View.VISIBLE);
        } else {
            viewHolder.ivTag.setVisibility(View.INVISIBLE);
        }
        viewHolder.cover.setOnClickListener(new MyClickListener("", albumM, shareModel));
        String content = "继续邀请";
        if (shareModel.getStatus() == 2) {
            content = "激活会员";
        } else if (shareModel.getStatus() == 3) {
            content = "已获得奖励";
        }
        viewHolder.tvInviteBtn.setText(content);
        viewHolder.llShareBtn.setOnClickListener(new MyClickListener(content, albumM, shareModel));
        if (shareModel.getStatus() == 2) {
            viewHolder.tvAlbumTime.setText("已邀请");
            viewHolder.llShareBtn.setBackground(ContextCompat.getDrawable(mContext, R.drawable.main_bg_ffbb1d_dp22));
            viewHolder.tvAlbumToast.setVisibility(View.INVISIBLE);
            if (!CollectionUtil.isNullOrEmpty(shareModel.getUserInfos())) {
                AlbumUserInfo userInfo = shareModel.getUserInfos().get(0);
                if (userInfo != null && !TextUtils.isEmpty(userInfo.getPhotoUrl())) {
                    ImageManager.from(context).displayImage(viewHolder.ivAssistHead,
                            userInfo.getPhotoUrl(), R.drawable.main_icon_invite_friend, R.drawable.main_icon_invite_friend);
                }
            }
        } else if (shareModel.getStatus() == 3) {
            viewHolder.tvAlbumTime.setText("已邀请");
            viewHolder.llShareBtn.setBackground(ContextCompat.getDrawable(mContext, R.drawable.main_bg_99ffbb1d_dp22));
            viewHolder.tvAlbumToast.setVisibility(View.INVISIBLE);
            if (!CollectionUtil.isNullOrEmpty(shareModel.getUserInfos())) {
                AlbumUserInfo userInfo = shareModel.getUserInfos().get(0);
                if (userInfo != null && !TextUtils.isEmpty(userInfo.getPhotoUrl())) {
                    ImageManager.from(context).displayImage(viewHolder.ivAssistHead,
                            userInfo.getPhotoUrl(), R.drawable.main_icon_invite_friend, R.drawable.main_icon_invite_friend);
                }
            }
        } else {
            viewHolder.tvAlbumToast.setVisibility(View.VISIBLE);
            viewHolder.llShareBtn.setBackground(ContextCompat.getDrawable(mContext, R.drawable.main_bg_fffe5501_ffff762a_dp22));
            if (shareModel.getExpireTime() > 0) {
                viewHolder.tvAlbumTime.setExpireTime(shareModel.getExpireTime(), "", null);
            }
        }
        boolean isHasMore = mTotalData != null && mTotalData.size() > listData.size() && position == 0;
        boolean isExpand = listData.size() > 1 && position == listData.size() - 1;
        if (isHasMore) {
            viewHolder.llLookLoadMore.setVisibility(View.VISIBLE);
            viewHolder.tvLookLoadMore.setText("查看更多");
            viewHolder.ivLoadStatusBtn.setImageResource(R.drawable.main_bg_share_down_arrow);
        } else if (isExpand) {
            viewHolder.tvLookLoadMore.setText("收起全部");
            viewHolder.llLookLoadMore.setVisibility(View.VISIBLE);
            viewHolder.ivLoadStatusBtn.setImageResource(R.drawable.main_bg_share_up_arrow);
        } else {
            viewHolder.llLookLoadMore.setVisibility(View.GONE);
        }
        viewHolder.llVipSelectTip.setVisibility(position == 0 ? View.VISIBLE : View.GONE);
        viewHolder.llLookLoadMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                List<AlbumShareModel> shareModels = getListData();
                if (isHasMore) {
                    if (CollectionUtil.isNullOrEmpty(mTotalData)) return;
                    if (shareModels != null) {
                        shareModels.clear();
                    } else {
                        shareModels = new ArrayList<>();
                    }
                    shareModels.addAll(getMTotalData());
                    isHasExpand = true;
                    iDataCallBack.updateContainerHeight(null, true, shareModels.size());
                    TraceFreeAlbumManager.INSTANCE.clickVipAlbumListLoadMorePage("查看更多");
                } else if (isExpand) {
                    if (CollectionUtil.isNullOrEmpty(mTotalData)) return;
                    if (shareModels != null) {
                        shareModels.clear();
                    } else {
                        shareModels = new ArrayList<>();
                    }
                    shareModels.add(mTotalData.get(0));
                    isHasExpand = false;
                    iDataCallBack.updateContainerHeight(null, true, shareModels.size());
                }
                notifyDataSetChanged();

            }
        });
        TraceFreeAlbumManager.INSTANCE.exposeVipAlbumLockItem(albumM.getId());
    }

    private class MyClickListener implements View.OnClickListener {
        private String content;
        private AlbumM albumM;
        private AlbumShareModel shareModel;

        public MyClickListener(String content, AlbumM album, AlbumShareModel shareModel) {
            this.content = content;
            this.albumM = album;
            this.shareModel = shareModel;
        }

        @Override
        public void onClick(View view) {
            iDataCallBack.onSuccess(albumM, shareModel.getStatus(), content);
        }
    }

    @Nullable
    public static Spanned getRichTitle(Album data, Context context, int maxHeight) {
        if (!(data instanceof AlbumM)) {
            return null;
        }
        if (context == null) {
            return null;
        }
        AlbumM album = (AlbumM) data;
        boolean isCompleteTag = album.getSerialState() == 2 || album.isCompleted() || (album.getAttentionModel() != null && album.getAttentionModel().getSerialState() == 2);
        List<Integer> resList = new ArrayList<>();
        if (isCompleteTag) {
            // 完本标签
            resList.add(R.drawable.main_tag_complete_top_new);
        }
        String intro;
        if (resList.size() > 0) {
            intro = " " + data.getAlbumTitle();
        } else {
            intro = data.getAlbumTitle();
        }
        Spanned titleWithTag = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(context, intro, resList, resList.size(), maxHeight);
        return titleWithTag;
    }

    public static class ViewHolder extends BaseAlbumAdapter.ViewHolder {
        private ImageView ivTag;
        private UnlockCountDownTextView tvAlbumTime;
        private TextView tvLookLoadMore;
        private LinearLayout llLookLoadMore;
        private TextView tvInviteBtn;
        private LinearLayout llVipSelectTip;
        private LinearLayout llShareBtn;
        private RoundImageView ivAssistHead;
        private TextView tvAlbumToast;
        private ImageView ivLoadStatusBtn;


        public ViewHolder(View convertView) {
            super(convertView);
            cover = convertView.findViewById(R.id.main_iv_album_cover);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
            border = convertView.findViewById(R.id.main_album_border);
            title = convertView.findViewById(R.id.main_tv_album_title);
            tvAlbumTime = convertView.findViewById(R.id.main_tv_album_time);
            tvLookLoadMore = convertView.findViewById(R.id.tvLookLoadMore);
            llLookLoadMore = convertView.findViewById(R.id.llLookLoadMore);
            ivLoadStatusBtn = convertView.findViewById(R.id.ivLoadStatusBtn);
            llVipSelectTip = convertView.findViewById(R.id.llVipSelectTip);
            tvInviteBtn = convertView.findViewById(R.id.tv_invite_btn);
            ivAssistHead = convertView.findViewById(R.id.main_iv_invited);
            tvAlbumToast = convertView.findViewById(R.id.main_tv_album_toast);
            llShareBtn = convertView.findViewById(R.id.ll_main_album_listener);

        }
    }
}
