package com.ximalaya.ting.lite.main.download.bean;


/**
 * <AUTHOR> feiwen
 * date   :
 * desc   : 下载任务,抽象类
 */
public abstract class Task<Info extends TaskInfo, State extends TaskState> implements Runnable {
	/**
	 * 获取任务信息
	 * 
	 */
	public abstract Info getInfo();

	/**
	 * 获取任务某个时刻的状态
	 * 
	 */
	public abstract State getState();

	/**
	 * 停止任务
	 * 
	 */
	public abstract int stop();

	/**
	 * 删除任务，是否需要删除文件
	 * 
	 */
	public abstract int delete(boolean delete);

	/**
	 * 获取任务信息
	 * 
	 */
	public abstract void reset();

	public abstract void release();

}
