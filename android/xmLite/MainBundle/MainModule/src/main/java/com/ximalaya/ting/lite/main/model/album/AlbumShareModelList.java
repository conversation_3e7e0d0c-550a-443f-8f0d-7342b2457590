package com.ximalaya.ting.lite.main.model.album;


import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


public class AlbumShareModelList {
    private List<AlbumShareModel> shareList;
    private String shareRule;

    public List<AlbumShareModel> getShareList() {
        return shareList;
    }

    public void setShareList(List<AlbumShareModel> shareList) {
        this.shareList = shareList;
    }

    public String getShareRule() {
        return shareRule;
    }

    public void setShareRule(String shareRule) {
        this.shareRule = shareRule;
    }

    public AlbumShareModelList(JSONObject json) {
        this.shareRule = json.optString("shareRule");
        JSONArray jsonArray = json.optJSONArray("shareList");
        if (jsonArray != null) {
            this.shareList = new ArrayList<>();
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.optJSONObject(i);
                if (jsonObject != null) {
                    AlbumShareModel albumShareModel = new AlbumShareModel(jsonObject);
                    this.shareList.add(albumShareModel);
                }
            }
        }
    }
}
