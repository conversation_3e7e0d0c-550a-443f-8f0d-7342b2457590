package com.ximalaya.ting.lite.main.model.newhome.listener;

import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.album.fragment.NewAggregateRankFragment;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.home.fragment.KeywordMetadataFragment;
import com.ximalaya.ting.lite.main.home.fragment.NewContentPoolListFragment;
import com.ximalaya.ting.lite.main.model.album.MainAlbumOtherData;
import com.ximalaya.ting.lite.main.model.newhome.LiteFloorModel;
import com.ximalaya.ting.lite.main.model.rank.AggregateRankArgsModel;

/**
 * Created by dumingwei on 2021/3/22
 * <p>
 * Desc:
 */
public class LiteNormalTitleClickListener implements View.OnClickListener {

    private final LiteFloorModel titleBean;
    private final BaseFragment2 mBaseFragment;

    private int from = -1;
    private int pageId = -1;
    private boolean mIsRecommendChannel = true;

    public LiteNormalTitleClickListener(LiteFloorModel titleBean, BaseFragment2 baseFragment) {
        this.titleBean = titleBean;
        this.mBaseFragment = baseFragment;
    }

    public void setData(int from,int curPageId) {
        this.from = from;
        this.pageId = curPageId;
        mIsRecommendChannel = from == BundleValueConstantsInMain.FROM_NEW_HOME_RECOMMEND;
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        if (titleBean == null) {
            return;
        }
        if (mBaseFragment == null) {
            return;
        }
        int moduleType = titleBean.getModuleType();
        Logger.d("首页_更多点击", "首页更多点击--楼层--" + moduleType);
        switch (moduleType) {
            case LiteFloorModel.MODULE_CONTENT_POOL:
                int poolId = 0;
                MainAlbumOtherData mainAlbumOtherData = titleBean.getOtherData();
                if (mainAlbumOtherData != null) {
                    poolId = mainAlbumOtherData.poolId;
                }
                NewContentPoolListFragment contentPoolListFragment = NewContentPoolListFragment.newInstance(titleBean.getTitle(), poolId, from);
                mBaseFragment.startFragment(contentPoolListFragment);

                if (mIsRecommendChannel) {
                    // 新首页-内容池-模块更多btn  点击事件
                    new XMTraceApi.Trace()
                            .click(29684) // 用户点击时上报
                            .put("title", titleBean.getTitle())
                            .put("currPageId", String.valueOf(pageId))
                            .put("moduleId", String.valueOf(titleBean.getModuleId()))
                            .put("currPage", "homePageV2")
                            .createTrace();
                } else {
                    // 新首页-内容池-模块更多btn  点击事件
                    new XMTraceApi.Trace()
                            .click(29761) // 用户点击时上报
                            .put("title", titleBean.getTitle())
                            .put("currPageId", String.valueOf(pageId))
                            .put("moduleId", String.valueOf(titleBean.getModuleId()))
                            .put("currPage", "homePageV2")
                            .createTrace();
                }
                break;
            case LiteFloorModel.MODULE_KEYWORD_CARD:
                int categoryId = titleBean.getCategoryId();
                int keywordId = titleBean.getKeywordId();
                //展示的是title字段
                String keyName = titleBean.getTitle();
                Bundle argumentForType5 = KeywordMetadataFragment.createArgumentFromSinglePage(categoryId, keywordId, keyName, from);
                KeywordMetadataFragment fragmentForType5 = new KeywordMetadataFragment();
                fragmentForType5.setArguments(argumentForType5);
                mBaseFragment.startFragment(fragmentForType5);

                if (mIsRecommendChannel) {
                    // 新首页-内容池-模块更多btn  点击事件
                    new XMTraceApi.Trace()
                            .click(29686) // 用户点击时上报
                            .put("title", titleBean.getTitle())
                            .put("currPageId", String.valueOf(pageId))
                            .put("moduleId", String.valueOf(titleBean.getModuleId()))
                            .put("currPage", "homePageV2")
                            .createTrace();
                } else {
                    // 新首页-内容池-模块更多btn  点击事件
                    new XMTraceApi.Trace()
                            .click(29766) // 用户点击时上报
                            .put("currPageId", String.valueOf(pageId))
                            .put("moduleId", String.valueOf(titleBean.getModuleId()))
                            .put("currPage", "homePageV2")
                            .createTrace();
                }
                break;
            case LiteFloorModel.MODULE_ALBUM_RANK:
                if (mIsRecommendChannel) {
                    // 新首页-排行榜-榜单更多btn  点击事件
                    new XMTraceApi.Trace()
                            .click(29680) // 用户点击时上报
                            .put("title", titleBean.getTitle())
                            .put("currPageId", String.valueOf(pageId))
                            .put("moduleId", String.valueOf(titleBean.getModuleId()))
                            .put("currPage", "homePageV2")
                            .createTrace();
                } else {
                    // 新首页-排行榜-榜单更多btn  点击事件
                    new XMTraceApi.Trace()
                            .click(29757) // 用户点击时上报
                            .put("title", titleBean.getTitle())
                            .put("currPageId", String.valueOf(pageId))
                            .put("moduleId", String.valueOf(titleBean.getModuleId()))
                            .put("currPage", "homePageV2")
                            .createTrace();
                }
                //不默认选中
                MainAlbumOtherData otherData = titleBean.getOtherData();
                if (otherData != null && !TextUtils.isEmpty(otherData.hasMoreLink)) {
                    ToolUtil.clickUrlAction(mBaseFragment, otherData.hasMoreLink, v);
                } else {
                    AggregateRankArgsModel model = new AggregateRankArgsModel();
                    //selectRankClusterId和selectRankingListId配置一个即可选中对应的tab
                    //跳转后，优先使用selectRankingListId进行匹配，匹配不到，使用selectRankClusterId进行匹配
                    model.selectRankingListId = -1;
                    NewAggregateRankFragment aggregateRankFragment = NewAggregateRankFragment.newInstance(model);
                    mBaseFragment.startFragment(aggregateRankFragment);
                }
                break;
                case LiteFloorModel.MODULE_FREE_BOOK_CONTENT_POOL_NEW:
                    if (MainActionRouter.getInstanse() != null && MainActionRouter.getInstanse().getFunctionAction() != null) {
                        MainActionRouter.getInstanse().getFunctionAction().handleITing(mBaseFragment.getActivity(),
                                Uri.parse("uting://open?msg_type=10033&pageId=62"));
                    }
            default:
                break;
        }
    }
}