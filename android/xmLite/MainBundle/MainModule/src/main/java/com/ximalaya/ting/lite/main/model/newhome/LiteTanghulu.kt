package com.ximalaya.ting.lite.main.model.newhome

import com.google.gson.annotations.SerializedName

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/3/23.
 *
 * Desc:新首页里面的糖葫芦
 */
class LiteTanghulu(
        var id: Long?,
        var title: String?,
        var cover: String?,
        var linType: String?,
        @SerializedName("linkUrl", alternate = ["url"])
        var linkUrl: String?,
        var moduleId: Int? = -1
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as LiteTanghulu

        if (id != other.id) return false
        if (title != other.title) return false
        if (cover != other.cover) return false
        if (linType != other.linType) return false
        if (linkUrl != other.linkUrl) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id?.hashCode() ?: 0
        result = 31 * result + (title?.hashCode() ?: 0)
        result = 31 * result + (cover?.hashCode() ?: 0)
        result = 31 * result + (linType?.hashCode() ?: 0)
        result = 31 * result + (linkUrl?.hashCode() ?: 0)
        return result
    }
}