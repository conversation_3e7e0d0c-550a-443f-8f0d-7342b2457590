package com.ximalaya.ting.lite.main.home.fragment;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AbsListView;
import android.widget.AbsListView.OnScrollListener;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.base.GotoTopFragment;
import com.ximalaya.ting.lite.main.base.album.AlbumScoreAdapter;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.lite.main.home.view.NewChooseMetadataView;
import com.ximalaya.ting.lite.main.home.view.SinglePageChooseMetadataView;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.request.LiteUrlConstants;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by dumingwei on 2021/5/10
 * <p>
 * Desc: 播放页的tag点击跳转的筛选页，只有综合排序、播放最多、最近更新。
 */
@SuppressLint("ValidFragment")
public class PlayPageTagCategoryMetadataFragment extends GotoTopFragment implements
        IRefreshLoadMoreListener, SinglePageChooseMetadataView.OnMetadataChangeListener {

    public static final String DISPLAY_TYPE = "displayType";
    public static final int CATEGORY_TYPE = 1;//使用老的接口加载专辑列表
    public static final int METADATA_TYPE = 2;//使用新的接口加载专辑列表lite-mobile-meta/tag/metadata/albums
    private static final String TAG = "NewSinglePageCategoryMe";

    private static final String KEY_TITLE = "KEY_TITLE";
    private static final String KEY_META_SELECT_ARGS = "KEY_META_SELECT_ARGS";

    private int mIsPaid = -1;
    private int mIsFinished = -1;
    private String mFilterCode;

    private String mMetadataIds = "";

    private String mTitle;
    private int mCategoryId = -1;
    private int mMetadataValueId = -1;
    private int mPageId = 1;

    private String mCalDimension = SinglePageChooseMetadataView.CAL_DIMEN_DEFAULT;

    private RefreshLoadMoreListView mListView;
    private AlbumScoreAdapter mAdapter;
    private FrameLayout mHeadPanelContainer;
    private FrameLayout mPullDownMenuContainer;
    private TextView tvMetadata;
    private SinglePageChooseMetadataView mChooseMetadataView;
    private String metadataParam;
    private LinearLayout layoutNoContent;
    private ImageView mIvHint;
    private boolean mLastItemVisible;
    private boolean isLoading = false;
    private boolean hasMore = true;
    private boolean mIsPullDownMenuShowing = false;
    private View mRlTitleBar; //状态栏

    //listview只能在metadata加载成功后才能加载数据
    private boolean mInitMetaDataSuccess = false;

    private final List<Album> albumMList = new ArrayList<>();
    private boolean mNeedTitleBar = true; //是否需要标题栏，默认是不需要，默认在首页tab使用

    private int mType = CATEGORY_TYPE;


    public PlayPageTagCategoryMetadataFragment() {
    }

    public static Bundle createArguments(String title, int type, int categoryId, int metadataValueId) {
        Bundle bundle = new Bundle();
        bundle.putString(KEY_TITLE, title);
        bundle.putInt(DISPLAY_TYPE, type);

        bundle.putInt(BundleKeyConstants.KEY_CATEGORY_ID, categoryId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_METADATA_VALUE_ID, metadataValueId);

        return bundle;
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mTitle = arguments.getString(KEY_TITLE, "");
            mType = arguments.getInt(DISPLAY_TYPE, -1);
            mCategoryId = arguments.getInt(BundleKeyConstants.KEY_CATEGORY_ID, -1);
            mMetadataValueId = arguments.getInt(BundleKeyConstantsInMain.KEY_METADATA_VALUE_ID, -1);
        }
        //需要标题栏，设置为可滑动返回
        setCanSlided(mNeedTitleBar);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_new_single_page_category_metadata;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRlTitleBar = findViewById(R.id.main_title_bar);
        mPullDownMenuContainer = findViewById(R.id.main_fl_pull_down_menu_container);
        tvMetadata = findViewById(R.id.main_tv_metadatas);
        mListView = findViewById(R.id.main_listview);
        mListView.setOnRefreshLoadMoreListener(this);
        //展示为带评分的样式
        mAdapter = new AlbumScoreAdapter(3, mActivity, albumMList);

        mChooseMetadataView = new SinglePageChooseMetadataView(getActivity());
        mChooseMetadataView.setFrom(NewChooseMetadataView.FROM_CATEGORY);
        mChooseMetadataView.setCategoryId(mCategoryId + "");
        mChooseMetadataView.addMetadataChangeListener(this);
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof NewHomeCategoryContentTabFragment) {
            NewHomeCategoryContentTabFragment fragment = (NewHomeCategoryContentTabFragment) parentFragment;
            mChooseMetadataView.setSlideView(fragment.getSlideView());
        } else {
            mChooseMetadataView.setSlideView(getSlideView());
        }
        mHeadPanelContainer = new FrameLayout(mContext);
        mHeadPanelContainer.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        mHeadPanelContainer.addView(mChooseMetadataView);

        //将筛选的View作为ListView的HeadView
        mListView.getRefreshableView().addHeaderView(mHeadPanelContainer);

        layoutNoContent = new LinearLayout(mContext);
        layoutNoContent.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        layoutNoContent.setGravity(Gravity.CENTER);
        mIvHint = new ImageView(mContext);
        mIvHint.setPadding(0, BaseUtil.dp2px(mContext, 130), 0, 0);
        mIvHint.setImageResource(R.drawable.main_bg_meta_nocontent);
        layoutNoContent.addView(mIvHint);
        layoutNoContent.setVisibility(View.GONE);
        mIvHint.setVisibility(View.GONE);
        //将无网络或者无内容的View作为ListView的FootView
        mListView.getRefreshableView().addFooterView(layoutNoContent);

        mListView.setAdapter(mAdapter);
        mListView.getRefreshableView().setOnScrollListener(new OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                if (scrollState == OnScrollListener.SCROLL_STATE_IDLE && mLastItemVisible && !isLoading && hasMore) {
                    showPullDowWindow(false);
                    loadData();
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (firstVisibleItem <= 1) {
                    mPullDownMenuContainer.setVisibility(View.INVISIBLE);
                } else {
                    mPullDownMenuContainer.setVisibility(View.VISIBLE);
                    showPullDowWindow(false);
                }
                mLastItemVisible = (totalItemCount > 0) && (firstVisibleItem + visibleItemCount >= totalItemCount - 1);
                if (getiGotoTop() != null) {
                    getiGotoTop().setState(firstVisibleItem > 12);
                }
            }
        });

        findViewById(R.id.main_fl_pull_down_menu_container).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                showPullDowWindow(true);
            }
        });
        AutoTraceHelper.bindData(findViewById(R.id.main_fl_pull_down_menu_container), "");
        mListView.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
                if (!OneClickHelper.getInstance().onClick(view)) {
                    return;
                }
                if (mAdapter.getListData() == null) {
                    return;
                }
                int index = (int) id;
                if (index < 0 || index >= mAdapter.getListData().size()) {
                    return;
                }
                AlbumM albumM = (AlbumM) mAdapter.getListData().get(index);
                if (albumM == null) {
                    return;
                }
                AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_DISCOVERY_CATEGORY, ConstantsOpenSdk.PLAY_FROM_FIND_CLASSIFICATION, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
            }
        });
        onPageLoadingCompleted(LoadCompleteType.LOADING);

        //设置状态栏展示
        if (mNeedTitleBar) {
            mRlTitleBar.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(mTitle)) {
                setTitle(mTitle);
            } else {
                setTitle("全部");
            }

            //单独页面，滑动冲突处理
            SlideView slideView = getSlideView();
            if (slideView != null) {
                mChooseMetadataView.setSlideView(slideView);
            }
        }
    }

    /**
     * @param showPullDowWindow 是否显示下拉的
     */
    private void showPullDowWindow(boolean showPullDowWindow) {
        boolean isNoShow = showPullDowWindow == mIsPullDownMenuShowing || (showPullDowWindow && isLoading);
        if (isNoShow) {
            //正在刷新页面时不显示下拉弹窗
            return;
        }
        Logger.i(TAG, "showPullDowWindow mIsPullDownMenuShowing = " + mIsPullDownMenuShowing);
        if (mIsPullDownMenuShowing) {
            //展示ListView的HeadView的筛选
            mIsPullDownMenuShowing = false;
            mPullDownMenuContainer.removeView(mChooseMetadataView);
            mChooseMetadataView.showBottomDivider(true);
            mHeadPanelContainer.addView(mChooseMetadataView);
            mChooseMetadataView.setBackgroundColor(Color.TRANSPARENT);
        } else {
            //展示悬浮的筛选
            mIsPullDownMenuShowing = true;
            mHeadPanelContainer.removeView(mChooseMetadataView);
            mChooseMetadataView.showBottomDivider(false);
            mPullDownMenuContainer.addView(mChooseMetadataView, mPullDownMenuContainer.getChildCount() - 1);
            mChooseMetadataView.setBackgroundColor(Color.WHITE);
        }
    }

    /**
     * 加载页面数据，需要保证加载分类数据以后才可以请求
     */
    private void loadPageListData() {
        Logger.i(TAG, "loadPageListData isLoading = " + isLoading);
        if (isLoading) {
            return;
        }

        final Map<String, String> params = getRequestParamsByType();

        isLoading = true;

        onPageLoadingCompleted(LoadCompleteType.LOADING);

        final IDataCallBack<ListModeBase<AlbumM>> callback = new IDataCallBack<ListModeBase<AlbumM>>() {
            @Override
            public void onSuccess(final ListModeBase<AlbumM> object) {
                if (!canUpdateUi()) {
                    return;
                }
                isLoading = false;
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        if (object == null) {
                            if (albumMList.size() == 0) {
                                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            }
                            return;
                        }
                        if (mPageId == 1) {
                            albumMList.clear();
                            mAdapter.notifyDataSetChanged();
                        }
                        List<AlbumM> reqList = object.getList();

                        int dataSize = (reqList != null ? reqList.size() : 0) + albumMList.size();

                        //空页面
                        if (dataSize == 0) {
                            mAdapter.notifyDataSetChanged();
                            mListView.setHasMoreNoFooterView(false);
                            setNoContentPage();
                            return;
                        }

                        if (object.getMaxPageId() > mPageId) {
                            mPageId++;
                            mListView.onRefreshComplete(true);
                            hasMore = true;
                        } else {
                            mListView.onRefreshComplete(false);
                            mListView.setHasMoreNoFooterView(false);
                            hasMore = false;
                        }
                        if (reqList != null) {
                            albumMList.addAll(reqList);
                        }
                        mAdapter.notifyDataSetChanged();
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                isLoading = false;
                if (!canUpdateUi()) {
                    return;
                }
                mListView.onRefreshComplete();
                onPageLoadingCompleted(LoadCompleteType.OK);
                if (albumMList.size() == 0) {
                    onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                } else {
                    CustomToast.showFailToast(R.string.main_network_error);
                }
            }
        };

        String url = "";
        if (mType == CATEGORY_TYPE) {
            url = LiteUrlConstants.getNewMetadataAlbums() + System.currentTimeMillis();
        } else if (mType == METADATA_TYPE) {
            url = LiteUrlConstants.getPlayPageTagMetadataAlbumsUrl() + System.currentTimeMillis();
        }
        LiteCommonRequest.getNewAlbumsByMetadata(url, params, callback);
    }

    @NotNull
    private Map<String, String> getRequestParamsByType() {
        final Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_PAGE_ID, mPageId + "");
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        params.put("sortType", mCalDimension);

        if (mType == CATEGORY_TYPE) {
            params.put(HttpParamsConstants.PARAM_CATEGORY_ID, mCategoryId + "");
            if (!TextUtils.isEmpty(metadataParam)) {
                params.put(HttpParamsConstants.PARAM_METADATAS, metadataParam);
            }

            if (mIsFinished != -1) {
                params.put("isPaid", String.valueOf(mIsPaid));
            }
            if (mIsPaid != -1) {
                params.put("isFinished", String.valueOf(mIsFinished));
            }
        } else if (mType == METADATA_TYPE) {
            params.put("metadataValueId", String.valueOf(mMetadataValueId));
        }

        return params;
    }

    /**
     * 只显示只有综合排序、播放最多、最近更新 一栏
     */
    private void initEmptyMetaDatas() {
        mInitMetaDataSuccess = true;//注意，这行代码，要放在mChooseMetadataView.setMetadata()前面，不然会死循环
        mChooseMetadataView.setMetadata(null, -1, -1);
    }

    private void setNoContentPage() {
        if (canUpdateUi()) {
            mListView.setMode(Mode.DISABLED);
            layoutNoContent.setVisibility(View.VISIBLE);
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
        }
    }

    private boolean isLoadingDataed = false;

    @Override
    protected void loadData() {
        if (!canUpdateUi()) {
            return;
        }
        Logger.d("NewSinglePageCategoryMe", "loadData");
        mListView.setMode(Mode.PULL_FROM_START);
        if (getUserVisibleHint()) {
            if (mInitMetaDataSuccess) {
                isLoadingDataed = true;
                loadPageListData();
            } else {
                initEmptyMetaDatas();
            }
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (!isLoadingDataed && isVisibleToUser && isResumed()) {
            loadData();
        }
    }

    @Override
    public void onRefresh() {
        mPageId = 1;
        loadData();
    }

    @Override
    public void onMore() {

    }

    @Override
    public void onMetadataChange(String calDimension, String metadataRequestParam, String hintStr) {
        showPullDowWindow(false);
        mListView.getRefreshableView().setSelection(0);

        mCalDimension = calDimension;

        if (!TextUtils.isEmpty(mMetadataIds)) {
            metadataParam = mMetadataIds + "," + metadataRequestParam;
        } else {
            metadataParam = metadataRequestParam;
        }
        if (!TextUtils.isEmpty(metadataParam) && metadataParam.endsWith(",")) {
            metadataParam = metadataParam.substring(0, metadataParam.length() - 1);
        }
        tvMetadata.setText(hintStr);
        Logger.d("CategoryMetadateFragment", "onMetadataChange=" + calDimension + "   " + metadataRequestParam + "   " + hintStr);

        mPageId = 1;
        loadData();
    }

    @Override
    public void gotoTop() {
        if (mListView == null) {
            return;
        }
        mListView.getRefreshableView().setSelection(0);
    }

    @Override
    public void onPageLoadingCompleted(LoadCompleteType loadCompleteType) {
        switch (loadCompleteType) {
            case OK:
            case LOADING:
                super.onPageLoadingCompleted(loadCompleteType);
                layoutNoContent.setVisibility(View.GONE);
                mIvHint.setVisibility(View.GONE);
                break;
            case NOCONTENT:
                super.onPageLoadingCompleted(LoadCompleteType.OK);
                layoutNoContent.setVisibility(View.VISIBLE);
                mIvHint.setImageResource(R.drawable.main_bg_meta_nocontent);
                mIvHint.setVisibility(View.VISIBLE);
                break;
            case NETWOEKERROR:
                super.onPageLoadingCompleted(LoadCompleteType.OK);
                layoutNoContent.setVisibility(View.VISIBLE);
                CustomToast.showFailToast(R.string.main_network_error);
                mIvHint.setImageResource(R.drawable.host_no_net);
                mIvHint.setVisibility(View.VISIBLE);
                break;
            default:
                break;
        }
    }
}
