package com.ximalaya.ting.lite.main.free.adapter

import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.free.ad.FreeModelAdHelper
import com.ximalaya.ting.lite.main.manager.HomeFreeModelGuideManager
import com.ximalaya.ting.lite.main.manager.ListenerTimeTaskManager
import com.ximalaya.ting.lite.main.model.CoinExchangeModel
import com.ximalaya.ting.lite.main.model.FreeTaskModel
import com.ximalaya.ting.lite.main.model.TASK_TYPE_COIN_REWARD
import com.ximalaya.ting.lite.main.model.TASK_TYPE_LISTENER_TIME_REWARD
import com.ximalaya.ting.lite.main.model.TASK_TYPE_VIDEO_REWARD
import com.ximalaya.ting.lite.main.playnew.dialog.UnlockListenTimeDialog


class FreeModelTaskAdapter(val list: MutableList<FreeTaskModel>) :
    RecyclerView.Adapter<FreeModelTaskAdapter.Holder>() {

    private var rewardDuration: Int = 0

    fun setRewardDuration(duration: Int) {
        rewardDuration = duration
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.main_item_free_model_task_layout, parent, false)
        return Holder(view)
    }

    override fun onBindViewHolder(holder: Holder, position: Int) {
        val model = list[position]
        holder.tvTitle?.text = model.title
        holder.tvSubTitle?.text = model.subTitle

        when (model.type) {
            TASK_TYPE_VIDEO_REWARD -> {
                holder.tvSubmit?.isSelected = true
                holder.tvSubmit?.text = "领取"

                // 免费畅听主页面-点击看视频领取  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(51206)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "Allfreemainpage")
                    .put("exploreType", "Allfreemainpage") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                    .createTrace()
            }

            TASK_TYPE_COIN_REWARD -> {
                if (UnlockListenTimeManagerNew.unlockConfigModel == null || UnlockListenTimeManagerNew.unlockConfigModel!!.availableCoinExchangedNum <= 0) {
                    holder.tvSubmit?.isSelected = false
                    holder.tvSubmit?.text = "明天可兑"
                } else {
                    holder.tvSubmit?.isSelected = true
                    holder.tvSubmit?.text = "兑换"
                }

                // 免费畅听主页面-金币兑换任务-点击兑换  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(51208)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "Allfreemainpage")
                    .put("exploreType", "Allfreemainpage") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                    .createTrace()
            }

            TASK_TYPE_LISTENER_TIME_REWARD -> {
                if (ListenerTimeTaskManager.mTotalListenerTime >= ListenerTimeTaskManager.mListenerTimeTaskModel!!.taskMinute * 60000) {
                    holder.tvSubmit?.isSelected = false
                    holder.tvSubmit?.text = "已完成，明天可领"
                } else {
                    holder.tvSubmit?.isSelected = true
                    holder.tvSubmit?.text = "去收听"
                }

                // 免费畅听主页面-收听时长任务-点击去收听  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(51213)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "Allfreemainpage")
                    .put("exploreType", "Allfreemainpage") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                    .createTrace()
            }
        }

        holder.tvSubmit?.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            when (model.type) {
                TASK_TYPE_VIDEO_REWARD -> {
                    // 免费畅听主页面-点击看视频领取  点击事件
                    XMTraceApi.Trace()
                        .click(51205) // 用户点击时上报
                        .put("currPage", "Allfreemainpage")
                        .createTrace()

                    if (UnlockListenTimeManagerNew.getAvailableListeningTime() >= 14400) {
                        CustomToast.showToast("剩余时长超过4个小时，快去听书放松吧")
                        return@setOnClickListener
                    }

                    //val loadRewardParams = LoadReawardParams()
                    //val positionName = "sub_freetime_inspire_video"
                    //val startTime = SystemClock.elapsedRealtime()

                    //保存激励时长
                    //val key = PreferenceConstantsInHost.REWARD_TIME_KEY + positionName
                    //MmkvCommonUtil.getInstance(BaseApplication.mAppInstance.applicationContext)
                    //    .saveInt(
                    //        key,
                    //        rewardDuration.toInt()
                    //    )
                    FreeModelAdHelper.requestAd(true, true, null)
                }

                TASK_TYPE_COIN_REWARD -> {
                    // 免费畅听主页面-金币兑换任务-点击兑换  点击事件
                    XMTraceApi.Trace()
                        .click(51207) // 用户点击时上报
                        .put("currPage", "Allfreemainpage")
                        .createTrace()

                    if (UnlockListenTimeManagerNew.unlockConfigModel != null && UnlockListenTimeManagerNew.unlockConfigModel!!.availableCoinExchangedNum <= 0) {
                        return@setOnClickListener
                    }

                    if (UnlockListenTimeManagerNew.getAvailableListeningTime() >= 14400) {
                        CustomToast.showToast("剩余时长超过4个小时，快去听书放松吧")
                        return@setOnClickListener
                    }

                    HomeFreeModelGuideManager.performCoinExchangeListenTime(object :
                        UnlockListenTimeDialog.IRequestCallBack<CoinExchangeModel> {
                        override fun onResult(result: CoinExchangeModel) {
                            if (UnlockListenTimeManagerNew.unlockConfigModel == null || UnlockListenTimeManagerNew.unlockConfigModel!!.availableCoinExchangedNum <= 0) {
                                holder.tvSubmit.isSelected = false
                                holder.tvSubmit.text = "明天可兑"
                            } else {
                                holder.tvSubmit.isSelected = true
                                holder.tvSubmit.text = "兑换"
                            }
                        }
                    })
                }

                TASK_TYPE_LISTENER_TIME_REWARD -> {
                    // 免费畅听主页面-收听时长任务-点击去收听  点击事件
                    XMTraceApi.Trace()
                        .click(51212) // 用户点击时上报
                        .put("currPage", "Allfreemainpage")
                        .createTrace()

                    if (ListenerTimeTaskManager.mTotalListenerTime >= ListenerTimeTaskManager.mListenerTimeTaskModel!!.taskMinute * 60000) {
                        CustomToast.showToast("明天继续收听即可直接领取")
                        return@setOnClickListener
                    }

                    // 模拟点击去播放页
                    val activity = BaseApplication.getMainActivity()
                    if (activity is MainActivity) {
                        val fragment = activity.playBarFragment
                        fragment?.findViewById<View>(com.ximalaya.ting.android.host.R.id.main_sound_cover_img)
                            ?.performClick()
                    }
                }
            }
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }

    class Holder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvTitle = itemView.findViewById<TextView?>(R.id.main_tv_title)
        val tvSubTitle = itemView.findViewById<TextView?>(R.id.main_tv_sub_title)
        val tvSubmit = itemView.findViewById<TextView?>(R.id.main_tv_submit)
    }
}