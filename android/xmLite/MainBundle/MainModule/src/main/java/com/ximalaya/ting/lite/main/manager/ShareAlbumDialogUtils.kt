package com.ximalaya.ting.lite.main.manager

import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.hybrid.provider.env.DeviceEnv
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.share.ShareDialog
import com.ximalaya.ting.android.shareservice.base.IShareDstType
import com.ximalaya.ting.android.xmtrace.XMTraceApi


object ShareAlbumDialogUtils {

    const val TAG = "ShareAlbumDialogUtils"

    fun execAddShareDialogAdvert(curPage: String?, shareDialog: ShareDialog?) {
        shareDialog?.run {
            shareDialog.setRecommend(listOf(IShareDstType.SHARE_TYPE_WX_CIRCLE))
            HandlerManager.postOnUIThread {
                if (isShowing) {
                    mRlTopView?.run {
                        val jsonObject = ConfigureCenter.getInstance().getJson(CConstants.Group_Base.GROUP_NAME, CConstants.Group_Base.ITEM_KEY_SHARE_DIALOG_BANNER)

                        var switch = 0
                        var ver = ""
                        var bannerImg = ""
                        var bannerUrl = ""
                        jsonObject?.run {
                            switch = jsonObject.optInt("switch")
                            ver = jsonObject.optString("ver")
                            bannerImg = jsonObject.optString("bannerImg")
                            bannerUrl = jsonObject.optString("bannerUrl")
                        }

                        if (switch == 0 || TextUtils.isEmpty(ver) || TextUtils.isEmpty(bannerImg) || TextUtils.isEmpty(bannerUrl)) {
                            FuliLogger.log(TAG, "配置问题,不允许显示 switch:$switch ver:$ver bannerImg:$bannerImg bannerUrl:$bannerUrl")
                            return@postOnUIThread
                        }

                        if (DeviceEnv.versionName() < ver) {
                            FuliLogger.log(TAG, "版本过低不显示 versionName:${DeviceEnv.versionName()} ver:$ver")
                            return@postOnUIThread
                        }

                        try {
                            val imageView = ImageView(context)
                            val marginWidth = BaseUtil.dp2px(context, 12f)
                            // 设计图比例351*80
                            val height = (BaseUtil.getScreenWidth(context) - 2 * marginWidth) / 351f * 80
                            val layoutParams = RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, height.toInt())

                            layoutParams.marginStart = marginWidth
                            layoutParams.marginEnd = marginWidth
                            layoutParams.topMargin = BaseUtil.dp2px(context, 2f)
                            imageView.layoutParams = layoutParams
                            imageView.setOnClickListener {
                                val activity = BaseApplication.getMainActivity()
                                if (activity is MainActivity) {
                                    // 分享弹框露出-资源位  弹框控件点击
                                    XMTraceApi.Trace()
                                        .setMetaId(48696)
                                        .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                                        .put("currPage", curPage)
                                        .createTrace()

                                    try {
                                        val intent = Intent()
                                        intent.data = Uri.parse(bannerUrl)
                                        activity.itingManager.doSomethingByIntentWithAppModeChange(activity, intent)
                                        shareDialog.dismiss()
                                    } catch (e: Exception) {
                                        e.printStackTrace()
                                    }
                                }
                            }
                            removeAllViews()
                            addView(imageView)
                            visibility = View.VISIBLE

                            imageView.post {
                                ImageManager.from(context).displayImage(imageView, bannerImg, -1) { _, bitmap ->
                                    if (bitmap == null || bitmap.isRecycled) {
                                        removeAllViews()
                                        visibility = View.GONE
                                    } else {
                                        // 分享弹框露出-资源位  控件曝光
                                        XMTraceApi.Trace()
                                            .setMetaId(48695)
                                            .setServiceId("slipPage") // 曝光时上报
                                            .put("currPage", curPage)
                                            .put("exploreType", "share") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                                            .createTrace()

                                        // 带活动引导图片的弹窗减小一点高度
                                        val layoutParam = shareDialog.mHContentView.layoutParams
                                        layoutParam.height = BaseUtil.dp2px(context, 112f)
                                        shareDialog.mHContentView.layoutParams = layoutParam
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }
        }
    }


}