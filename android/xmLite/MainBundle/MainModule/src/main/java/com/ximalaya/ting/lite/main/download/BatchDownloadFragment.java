package com.ximalaya.ting.lite.main.download;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.GridView;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.downloadservice.base.IDownloadCallback;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.FixPopupWindowHeightManager;
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.FileSizeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.host.view.BadgeView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.album.fragment.BatchActionFragment;
import com.ximalaya.ting.lite.main.play.fragment.ChooseTrackQualityDialog;
import com.ximalaya.ting.lite.main.request.HttpParamsConstantsInMain;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by Lennon on 2018/1/23.
 *
 * <AUTHOR>
 */

public class BatchDownloadFragment extends BaseFragment2
        implements
        IDownloadCallback,
        View.OnClickListener {
    public static final int ACTION_DOWNLOAD = BatchActionFragment.ACTION_DOWNLOAD;// 批量下载，只针对免费
    public static final int ACTION_DOWNLOAD_BUY = 3;//获取已购声音列表，只针对付费

    private int mType;
    private long mAlbumId;
    private boolean isAsc = true;

    private boolean isLoading = false;
    private static int pageSize = 50;//默认50,服务端会在第一次返回数据时候给出

    private AlbumM mAlbumM;
    private int mHeadPage = 1;
    private int mTailPage = 1;
    private boolean isFirstLoad = true;
    private boolean isChoosePager = false;
    private boolean isRefresh = false;
    private boolean isLoadMore = false;
    private List<Track> mData = new ArrayList<>(0);
    private List<BatchPagerAdapter.PageIndex> pageIndices = new ArrayList<>(0);


    private BatchPagerAdapter.PageIndex mPageIndex;
    private BatchDownloadAdapter mAdapter;

    private TextView mTrackCountText;
    private TextView mPageSelector;
    private TextView mDownloadInfoBar;
    private TextView mChooseAllButton;
    private Button mDownloadButton;
    private BadgeView mTvDownloadingCount;
    private RefreshLoadMoreListView mListView;

    public static BatchDownloadFragment newInstance(int type, long albumId) {
        BatchDownloadFragment fra = new BatchDownloadFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstants.KEY_FLAG, type);
        bundle.putLong(BundleKeyConstants.KEY_ALBUM_ID, albumId);
        fra.setArguments(bundle);
        return fra;
    }

    public BatchDownloadFragment() {
        super(AppConstants.isPageCanSlide, null);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle args = getArguments();
        if (args != null) {
            mType = args.getInt(BundleKeyConstants.KEY_FLAG);
            mAlbumId = args.getLong(BundleKeyConstants.KEY_ALBUM_ID);
        }
        isAsc = SharedPreferencesUtil.getInstance(mContext).getBoolean(PreferenceConstantsInHost.KEY_IS_ASC + mAlbumId, true);
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
        setTitle("批量下载");

        String tagTitleRight = "tagTitleRight";
        TitleBar.ActionType titleRight = new TitleBar.ActionType(
                tagTitleRight, TitleBar.RIGHT, R.string.main_downloading_current, 0,
                R.color.main_tab_text_color, TextView.class);
        titleRight.setFontSize(14);
        titleBar.addAction(titleRight, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到下载中界面
                startFragment(new DownloadingFragment());
            }
        });
        AutoTraceHelper.bindData(titleBar.getActionView(tagTitleRight), "");
        titleBar.update();
        TextView tvTitleRight = (TextView) titleBar.getActionView(tagTitleRight);
        tvTitleRight.setPadding(0,
                BaseUtil.dp2px(getActivity(), 12),
                BaseUtil.dp2px(getActivity(), 10),
                BaseUtil.dp2px(getActivity(), 12));

        mTvDownloadingCount = new BadgeView(getActivity());
        mTvDownloadingCount.setVisibility(View.GONE);
        mTvDownloadingCount.setTargetView(tvTitleRight);
        mTvDownloadingCount.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
        mTvDownloadingCount.setBackground(9, Color.parseColor("#F55233"));

        if (RouteServiceUtil.getDownloadService().getAllDownloadingTask().size() == 0) {
            tvTitleRight.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mTrackCountText = (TextView) findViewById(R.id.main_sound_count);
        mPageSelector = (TextView) findViewById(R.id.main_page_selector);
        mChooseAllButton = (TextView) findViewById(R.id.main_choose_all);
        mDownloadButton = (Button) findViewById(R.id.main_download);
        mDownloadInfoBar = (TextView) findViewById(R.id.main_bottom_info_bar);

        mListView = (RefreshLoadMoreListView) findViewById(R.id.main_listview);
        mListView.setMode(PullToRefreshBase.Mode.PULL_FROM_START);
        mListView.setOnRefreshLoadMoreListener(new ListRefreshListener());
        mListView.setOnItemClickListener(new ListItemClickListener());
        mListView.setOnScrollListener(new ListScrollListener());
        mAdapter = new BatchDownloadAdapter(getActivity(), mData);
        mListView.setAdapter(mAdapter);


        mPageSelector.setOnClickListener(this);
        mChooseAllButton.setOnClickListener(this);
        mDownloadButton.setOnClickListener(this);
        AutoTraceHelper.bindData(mPageSelector, "");
        AutoTraceHelper.bindData(mChooseAllButton, "");
        AutoTraceHelper.bindData(mDownloadButton, "");
    }

    private void updateDownloadingCount() {
        int size = RouteServiceUtil.getDownloadService().getUnfinishedTasks().size();
        if (size > 0) {
            titleBar.getActionView("tagTitleRight").setVisibility(View.VISIBLE);
            mTvDownloadingCount.setVisibility(View.VISIBLE);
            mTvDownloadingCount.setText("" + size);
        } else {
            mTvDownloadingCount.setVisibility(View.GONE);
            titleBar.getActionView("tagTitleRight").setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        RouteServiceUtil.getDownloadService().registerDownloadCallback(this);
        mAdapter.notifyDataSetChanged();
        setChooseAllButton();
        updateDownloadBarInfo();
        updateDownloadingCount();
    }

    @Override
    public void onPause() {
        super.onPause();
        RouteServiceUtil.getDownloadService().unRegisterDownloadCallback(this);
    }

    @Override
    protected void loadData() {
        loadData(1);
    }

    private void loadData(int pageId) {
        if (isLoading) return;

        int index = pageId - 1;
        if (!ToolUtil.isEmptyCollects(pageIndices) && index >= 0 && index < pageIndices.size()
                && pageIndices.get(index).getPageData() != null) {
            mAlbumM = pageIndices.get(index).getPageData();
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (!canUpdateUi()) {
                        return;
                    }
                    setDataForView();
                }
            }, 100);
        } else if (mType == ACTION_DOWNLOAD) {
            requestDownloadList(pageId);
        } else if (mType == ACTION_DOWNLOAD_BUY) {
            requestDownloadBuyList(pageId);
        }
    }

    private void requestDownloadBuyList(int pageId) {
        isLoading = true;
        onPageLoadingCompleted(LoadCompleteType.LOADING);

        final Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, String.valueOf(mAlbumId));
        params.put(HttpParamsConstants.PARAM_PAGE_ID, String.valueOf(pageId));
        params.put(HttpParamsConstants.PARAM_IS_ASC, String.valueOf(isAsc));
        int trackQualityLevel = RouteServiceUtil.getDownloadService().getTrackQualityLevel();
        trackQualityLevel = CommonRequestM.getRealTrackQuality(trackQualityLevel);
        params.put(HttpParamsConstantsInMain.PARAM_TRACK_QUALITY_LEVEL, String.valueOf(trackQualityLevel));
        LiteCommonRequest.getBuyedWithoutDownloadTracks(params,
                new IDataCallBack<JSONObject>() {

                    @Override
                    public void onSuccess(JSONObject jsonObject) {
                        isLoading = false;
                        AlbumM object = null;
                        try {
                            object = new AlbumM(jsonObject.getJSONObject("album").toString());
                            object.parseTracks(jsonObject.getJSONObject("tracks"), true);
                            pageSize = object.getPageSize();
//                            mVipUserDownloadVipAlbumReminder = jsonObject.optString("vipBatchDownloadText");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        mAlbumM = object;
                        if (mAlbumM == null) {
                            mListView.onRefreshComplete(false);
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                        }
                        setDataForView();
                    }

                    @Override
                    public void onError(int code, String message) {
                        isLoading = false;
                        onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                    }
                });
    }

    private void requestDownloadList(int pageId) {
        isLoading = true;
        onPageLoadingCompleted(LoadCompleteType.LOADING);

        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, String.valueOf(mAlbumId));
        params.put(HttpParamsConstants.PARAM_PAGE_ID, String.valueOf(pageId));
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        params.put(HttpParamsConstants.PARAM_IS_ASC, String.valueOf(isAsc));
        int trackQualityLevel = RouteServiceUtil.getDownloadService().getTrackQualityLevel();
        trackQualityLevel = CommonRequestM.getRealTrackQuality(trackQualityLevel);
        params.put(HttpParamsConstantsInMain.PARAM_TRACK_QUALITY_LEVEL, String.valueOf(trackQualityLevel));
        LiteCommonRequest.getAlbumBatchDownloadInfo(params,
                new IDataCallBack<JSONObject>() {

                    @Override
                    public void onSuccess(final JSONObject jsonObject) {
                        isLoading = false;

                        doAfterAnimation(new IHandleOk() {
                            @Override
                            public void onReady() {

                                if (!canUpdateUi()) {
                                    return;
                                }

                                AlbumM object = null;
                                try {
                                    object = new AlbumM(jsonObject.getJSONObject("album").toString());
                                    object.parseTracks(jsonObject.getJSONObject("tracks"), false);
                                    object.vipPriorListenDownloadPopupRes = jsonObject.optString("batchTracksDownloadPopupVipResourceInfo");
                                    pageSize = object.getPageSize();
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                                mAlbumM = object;
                                setDataForView();

                            }
                        });
                    }

                    @Override
                    public void onError(int code, String message) {
                        isLoading = false;
                        onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                    }
                });
    }

    private void initPageGroupData() {
        if (mAlbumM != null && ToolUtil.isEmptyCollects(pageIndices)) {
            pageIndices = BatchPagerAdapter.computePagerIndex(mAlbumM.getPageSize(), (int) mAlbumM.getIncludeTrackCount(), isAsc ^ mAlbumM.isRecordDesc());
        }
    }

    private void setDataForView() {
        if (!canUpdateUi()) {
            return;
        }

        if (mAlbumM == null) {
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            return;
        }

        mAdapter.setAlbumM(mAlbumM);
        List<TrackM> trackMList = null;
        if (mAlbumM.getCommonTrackList() != null
                && !ToolUtil.isEmptyCollects(mAlbumM.getCommonTrackList().getTracks())) {
            trackMList = mAlbumM.getCommonTrackList().getTracks();
        }
        if (ToolUtil.isEmptyCollects(trackMList)) {
            if (isFirstLoad) {
                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                mPageSelector.setVisibility(View.GONE);
                return;
            }
        }

        initPageGroupData();

        if (isFirstLoad) {
            mHeadPage = mTailPage = mAlbumM.getPageId();
        } else if (isRefresh) {
            mHeadPage = mAlbumM.getPageId();
        } else if (isLoadMore) {
            mTailPage = mAlbumM.getPageId();
        } else if (isChoosePager) {
            mHeadPage = mTailPage = mAlbumM.getPageId();
        }
        boolean hasNextPage = mTailPage < mAlbumM.getCommonTrackList().getTotalPage();


        if (mAlbumM.getPageId() > 0 && mAlbumM.getPageId() <= pageIndices.size()) {
            BatchPagerAdapter.PageIndex pi = pageIndices.get(mAlbumM.getPageId() - 1);
            pi.setPageData(mAlbumM);
        }

        if (isFirstLoad) {
            mData.addAll(trackMList);
            mAdapter.notifyDataSetChanged();
        } else if (isRefresh && trackMList != null && !mData.containsAll(trackMList)) {
            mData.addAll(0, trackMList);
            mAdapter.notifyDataSetChanged();
            mListView.getRefreshableView().setSelection(0);
        } else if (isLoadMore && trackMList != null && !mData.containsAll(trackMList)) {
            mData.addAll(trackMList);
            mAdapter.notifyDataSetChanged();
        } else if (isChoosePager) {
            mData.clear();
            if (trackMList != null) {
                mData.addAll(trackMList);
            }
            mAdapter.notifyDataSetChanged();
            mListView.getRefreshableView().setSelection(0);
        }
        mTrackCountText.setText("共" + mAlbumM.getIncludeTrackCount() + "集");
        setChooseAllButton();

        if (hasNextPage) {
            mListView.onRefreshComplete(true);
        } else {
            mListView.onRefreshComplete(false);
            mListView.setHasMoreNoFooterView(false);
            mListView.setFootViewText("已经到底了～");
        }
        onPageLoadingCompleted(LoadCompleteType.OK);

        isFirstLoad = false;
        isChoosePager = false;
        isLoadMore = false;
        isRefresh = false;
    }

    private PopupWindow mPagerPopup;
    private BatchPagerAdapter mAlbumPagerAdapter;

    private void showPagerSelector(View view) {
        if (mPagerPopup == null
                && !ToolUtil.isEmptyCollects(pageIndices)) {
            int[] loc = new int[2];
            view.getLocationInWindow(loc);
            //int heiPop = BaseUtil.getScreenHeight(getContext()) - loc[1] - view.getHeight();
            int heiPop = FixPopupWindowHeightManager.getInstance().getScreenHeightForToAppBottom() - loc[1] - view.getHeight();

            View mPagerSelectorContent = LayoutInflater.from(getActivity()).inflate(R.layout.main_layout_album_pager_selector, mListView, false);
            mPagerSelectorContent.findViewById(R.id.main_space).setOnClickListener(this);
            mPagerSelectorContent.findViewById(R.id.main_space1).setOnClickListener(this);
            AutoTraceHelper.bindData(mPagerSelectorContent.findViewById(R.id.main_space), "");
            AutoTraceHelper.bindData(mPagerSelectorContent.findViewById(R.id.main_space1), "");
            GridView mAlbumPager = (GridView) mPagerSelectorContent.findViewById(R.id.main_album_pager);
            mAlbumPagerAdapter = new BatchPagerAdapter(getActivity(), pageIndices);
            mAlbumPager.setAdapter(mAlbumPagerAdapter);
            mAlbumPager.setOnItemClickListener(new PagerItemClickListener());

            mPagerPopup = new PopupWindow(mPagerSelectorContent,
                    WindowManager.LayoutParams.MATCH_PARENT, heiPop, true);
            mPagerPopup.setAnimationStyle(R.style.host_popup_window_animation);
            mPagerPopup.setBackgroundDrawable(new ColorDrawable(Color.parseColor("#b0000000")));
            mPagerPopup.setOutsideTouchable(true);
            mPagerPopup.setOnDismissListener(new PopupWindow.OnDismissListener() {
                @Override
                public void onDismiss() {
                    mPageSelector.setCompoundDrawablesWithIntrinsicBounds(
                            R.drawable.main_divider_vertical_gray, 0,
                            R.drawable.main_arrow_down_gray, 0);
                }
            });
        }

        if (mPagerPopup == null) return;
        if (mPagerPopup.isShowing()) {
            mPageSelector.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.main_divider_vertical_gray, 0,
                    R.drawable.main_arrow_down_gray, 0);
            mPagerPopup.dismiss();
        } else {
            mAlbumPagerAdapter.setPageId(mPageIndex == null ? 0 : mPageIndex.getPageIndex());
            mAlbumPagerAdapter.notifyDataSetChanged();
            mPageSelector.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.main_divider_vertical_gray, 0,
                    R.drawable.main_arrow_up_gray, 0);
            mPagerPopup.showAsDropDown(view, 0, 3);
        }
    }


    @Override
    public void onDownloadProgress(Track track) {

    }

    @Override
    public void onCancel(Track track) {
        updateDownloadingCount();
    }

    @Override
    public void onComplete(Track track) {
        updateDownloadingCount();
    }

    @Override
    public void onUpdateTrack(Track track) {

    }

    @Override
    public void onStartNewTask(Track track) {
        updateDownloadingCount();
    }

    @Override
    public void onError(Track track) {
        updateDownloadingCount();
    }

    @Override
    public void onDelete() {
        updateDownloadingCount();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_batch_download;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public boolean isShowTruckFloatPlayBar() {
        return false;
    }

    @Override
    public void onClick(View v) {
        int vid = v.getId();
        if (vid == R.id.main_space
                || vid == R.id.main_space1) {
            mPagerPopup.dismiss();
        } else if (vid == R.id.main_page_selector) {
            showPagerSelector(v);
        } else if (vid == R.id.main_choose_all) {
            boolean isSelected = v.isSelected();
            boolean willDo = !isSelected;
            if (willDo) {
                mAdapter.checkAll();
            } else {
                mAdapter.checkNone();
            }
            v.setSelected(willDo);
            syncAllPagerChooseStatus();
            updateDownloadBarInfo();
        } else if (vid == R.id.main_download) {
            batchDownload();
        }
    }

    private BatchPagerAdapter.PageIndex getPageIndexByTrack(Track track) {
        if (mAlbumM == null) {
            return null;
        }
        if (track == null || !(track instanceof TrackM)) return null;
        TrackM trackM = (TrackM) track;

        int currentPage;
        if (isAsc ^ mAlbumM.isRecordDesc()) {
            //正序
            currentPage = trackM.getOrderNo() / pageSize - 1;
            if (trackM.getOrderNo() % pageSize != 0)
                currentPage += 1;
        } else {
            //倒序
            int endPageCount = (int) (mAlbumM.getIncludeTrackCount() % pageSize);
            if (endPageCount == 0) {
                int fullpage = trackM.getOrderNo() / pageSize;
                int partPage = (trackM.getOrderNo() % pageSize == 0) ? 0 : 1;
                currentPage = pageIndices.size() - fullpage - partPage;
            } else {
                currentPage = pageIndices.size() - 1;
                if (trackM.getOrderNo() > endPageCount) {
                    int fullpage = (trackM.getOrderNo() - endPageCount) / pageSize;
                    int partPage = ((trackM.getOrderNo() - endPageCount) % pageSize == 0) ? 0 : 1;
                    currentPage = currentPage - fullpage - partPage;
                }
            }
        }
        if (currentPage >= 0 && !ToolUtil.isEmptyCollects(pageIndices) && currentPage < pageIndices.size()) {
            return pageIndices.get(currentPage);
        }
        return null;
    }

    private void setChooseAllButton() {
        int status = mAdapter.getChooseStatus();
        if (status == BatchDownloadAdapter.CHOOSE_ALL_DOWNLOAD_ALL) {
            mChooseAllButton.setEnabled(false);
        } else if (status == BatchDownloadAdapter.CHOOSE_ALL_DOWNLOAD_PART) {
            mChooseAllButton.setEnabled(true);
            mChooseAllButton.setSelected(true);
        } else if (status == BatchDownloadAdapter.CHOOSE_PART_DOWNLOAD_PART) {
            mChooseAllButton.setEnabled(true);
            mChooseAllButton.setSelected(false);
        }
    }

    private void updateDownloadBarInfo() {
        if (!ToolUtil.isEmptyCollects(pageIndices)) {
            int checkedItem = 0;
            long checkedFileLength = 0;
            for (BatchPagerAdapter.PageIndex pi : pageIndices) {
                Collection<Track> tracks;
                if (!ToolUtil.isEmptyCollects(tracks = pi.getSelectedTrack())) {
                    for (Track track : tracks) {
                        checkedItem++;
                        checkedFileLength += track.getDownloadSize();
                    }
                }
            }

            if (checkedItem > 0) {
                mDownloadButton.setEnabled(true);
                mDownloadInfoBar.setVisibility(View.VISIBLE);
                long availableMemorySize = FileSizeUtil
                        .getAvailableMemorySize(RouteServiceUtil.getStoragePathManager().getCurSavePath());
                if (checkedFileLength > availableMemorySize) {
                    mDownloadInfoBar.setText(R.string.main_no_enough_storage);
                } else {
                    String text = getStringSafe(
                            R.string.main_select_counts_occupy_size,
                            checkedItem,
                            StringUtil.getFriendlyFileSize(checkedFileLength),
                            StringUtil.getFriendlyFileSize(availableMemorySize));
                    mDownloadInfoBar.setText(text);
                }
                return;
            }
        }
        mDownloadButton.setEnabled(false);
        mDownloadInfoBar.setVisibility(View.GONE);
    }

    private void setPagerIndex(Track track) {
        BatchPagerAdapter.PageIndex pi = getPageIndexByTrack(track);
        if (pi != null && pi != mPageIndex) {
            mPageIndex = pi;
            mPageSelector.setText("选集（" + mPageIndex.getStartIndex() + "~" + mPageIndex.getEndIndex() + "）");
        }
    }

    private void syncAllPagerChooseStatus() {
        if (!ToolUtil.isEmptyCollects(pageIndices)) {
            for (BatchPagerAdapter.PageIndex pi : pageIndices) {
                pi.syncChooseStatus();
            }
        }
    }


//    private boolean showDialogOfVipPriorListen() {
//        if (mAlbumM != null && UserInfoMannage.hasLogined() && mAlbumM.getUid() == UserInfoMannage.getUid()) {
//            return false;
//        }
//
//        List<Track> selectedToDownloadTracks = null;
//        if (!ToolUtil.isEmptyCollects(pageIndices)) {
//            selectedToDownloadTracks = new ArrayList<>();
//            for (BatchPagerAdapter.PageIndex pi : pageIndices) {
//                if (!ToolUtil.isEmptyCollects(pi.getSelectedTrack())) {
//                    selectedToDownloadTracks.addAll(pi.getSelectedTrack());
//                }
//            }
//        }
//
//        List<TrackM> noVipPriorListenPermissionTracks = null;
//        if (!UserInfoMannage.isVipUser() && !ToolUtil.isEmptyCollects(selectedToDownloadTracks)) {
//            for (Track track : selectedToDownloadTracks) {
//                if (track != null && track.vipPriorListenStatus == 1) {
//                    if (noVipPriorListenPermissionTracks == null) {
//                        noVipPriorListenPermissionTracks = new ArrayList<>();
//                    }
//                    noVipPriorListenPermissionTracks.add(((TrackM) track));
//                }
//            }
//            if (!ToolUtil.isEmptyCollects(noVipPriorListenPermissionTracks)) {
//                BatchDownloadVipPriorListenConfirmDialog dialog =
//                        new BatchDownloadVipPriorListenConfirmDialog(getActivity(), noVipPriorListenPermissionTracks);
//                dialog.setPageRes(mAlbumM.vipPriorListenDownloadPopupRes);
//                dialog.setIAction(new BatchDownloadVipPriorListenConfirmDialog.IAction() {
//                    @Override
//                    public void onGetVipButtonClick() {
//                        new UserTracking("5953", "album", "button").setSrcPageId(mAlbumId).setSrcModule("批量下载弹窗").setItemId("免费领会员")
//                                .setIsVipFirst(true)
//                                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_ALBUM_PAGE_CLICK);
//                    }
//
//                    @Override
//                    public void onDownloadFreeTrackAction() {
//                        unSelectNoPermissionTracks();
//                        updateDownloadBarInfo();
//                        batchDownload();
//                    }
//                });
//                dialog.show();
//                new UserTracking().setModuleType("批量下载弹窗").setSrcPage("album").setSrcPageId(mAlbumId).setID("5998")
//                        .setIsVipFirst(true)
//                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
//                return true;
//            }
//        }
//        return false;
//    }

    private void unSelectNoPermissionTracks() {
        if (!ToolUtil.isEmptyCollects(pageIndices)) {
            Iterator<BatchPagerAdapter.PageIndex> pageIndexIterator = pageIndices.iterator();
            while (pageIndexIterator.hasNext()) {
                BatchPagerAdapter.PageIndex pageIndex = pageIndexIterator.next();
                Collection<Track> tracks;
                if (!ToolUtil.isEmptyCollects(tracks = pageIndex.getSelectedTrack())) {
                    Iterator<Track> trackIterator = tracks.iterator();
                    while (trackIterator.hasNext()) {
                        Track track = trackIterator.next();
                        if (!UserInfoMannage.isVipUser()
                                && track != null
                                && track.vipPriorListenStatus == 1) {
                            trackIterator.remove();
                        }
                    }
                }
            }
        }
        mAdapter.unSelectNoPermissionItems();
        mAdapter.notifyDataSetChanged();
    }

    private void batchDownload() {
        if (RouteServiceUtil.getDownloadService().isTrackQualitySettingActive()) {
            addBatchDownloadTask();
        } else {
            ChooseTrackQualityDialog.ActionCallBack callBack = new ChooseTrackQualityDialog.ActionCallBack() {
                @Override
                public void onConfirm() {
                    addBatchDownloadTask();  //用户选择下载音质后不用刷新页面，直接下载便可
                }

                @Override
                public void onCancel() {

                }
            };
            ChooseTrackQualityDialog.newInstance(getActivity(), callBack).show();
        }
    }

    private void addBatchDownloadTask() {
        List<Track> collection = null;
        if (!ToolUtil.isEmptyCollects(pageIndices)) {
            collection = new ArrayList<>();
            for (BatchPagerAdapter.PageIndex pi : pageIndices) {
                if (!ToolUtil.isEmptyCollects(pi.getSelectedTrack())) {
                    collection.addAll(pi.getSelectedTrack());
                }
            }
        }
        if (!ToolUtil.isEmptyCollects(collection)) {
            for (Track track : collection) {
                track.setAntiLeech(track.vipPriorListenStatus == 1);
            }

            RouteServiceUtil.getDownloadService().addTasksByTrackList(collection, new IDataCallBack<AlbumM>() {

                @Override
                public void onSuccess(AlbumM object) {
                    if (canUpdateUi()) {
                        mAdapter.notifyDataSetChanged();
                    }
                }

                @Override
                public void onError(int code, String message) {

                }
            });

            CustomToast.showSuccessToast("已加入下载队列");
            mAdapter.notifyDataSetChanged();
            setChooseAllButton();
            updateDownloadBarInfo();
            clearAllPagerSelected();
        }
    }

    private void clearAllPagerSelected() {
        if (!ToolUtil.isEmptyCollects(pageIndices)) {
            for (BatchPagerAdapter.PageIndex pi : pageIndices) {
                pi.clearSelectedTrack();
            }
        }
    }

    class ListRefreshListener implements IRefreshLoadMoreListener {

        @Override
        public void onRefresh() {
            mHeadPage--;
            if (mHeadPage < 1)
                mHeadPage = 1;
            isRefresh = true;
            loadData(mHeadPage);
        }

        @Override
        public void onMore() {
            mTailPage++;
            isLoadMore = true;
            loadData(mTailPage);
        }
    }

    class ListItemClickListener implements AdapterView.OnItemClickListener {

        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
            if (OneClickHelper.getInstance().onClick(view)) {
                int index = position - mListView.getRefreshableView().getHeaderViewsCount();
                Track track = (Track) mAdapter.getItem(index);
                if (track == null) return;

                if (!RouteServiceUtil.getDownloadService().isAddToDownload(track) && !mAdapter.isTrackLock(track)) {
                    track.setExtra(!track.getExtra());
                    BatchPagerAdapter.PageIndex pi = getPageIndexByTrack(track);
                    if (pi != null) pi.setSelectedTrack(track);
                    setChooseAllButton();

                    updateDownloadBarInfo();
                    mAdapter.notifyDataSetChanged();
                }
            }
        }
    }

    class ListScrollListener implements AbsListView.OnScrollListener {
        int headerCount = 0;

        {
            headerCount = mListView.getRefreshableView().getHeaderViewsCount();
        }

        @Override
        public void onScrollStateChanged(AbsListView view, int scrollState) {

        }

        @Override
        public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
            if (isRefresh || isLoadMore) return;
            int lastVisibleItem = firstVisibleItem + visibleItemCount - headerCount - mListView.getRefreshableView().getHeaderViewsCount();
            Object o = mAdapter.getItem(lastVisibleItem);
            if (o != null && o instanceof TrackM) {
                TrackM tm = (TrackM) o;
                setPagerIndex(tm);
            }
        }
    }

    class PagerItemClickListener implements AdapterView.OnItemClickListener {

        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
            mPagerPopup.dismiss();
            BatchPagerAdapter.PageIndex clickPager = (BatchPagerAdapter.PageIndex) mAlbumPagerAdapter.getItem(position);
            if (mPageIndex != null && mPageIndex.getPageIndex() == clickPager.getPageIndex()) {//没有切换不处理
                return;
            }

            isChoosePager = true;
            if (clickPager.getPageData() == null) {
                //未加载过数据，发起请求
                loadData(clickPager.getPageIndex());
                mAlbumPagerAdapter.setPageId(clickPager.getPageIndex());
                setChooseAllButton();
                updateDownloadBarInfo();
            } else {
                loadData(clickPager.getPageIndex());
            }
        }
    }
}
