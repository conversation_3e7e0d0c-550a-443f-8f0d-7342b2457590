package com.ximalaya.ting.lite.main.book.presenter

import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.lite.main.book.bean.BookShelfList

interface IBookShelfPresenter {
    /**
     * 加载数据
     */
    fun loadData();

    /**
     * 加载更多
     */
    fun loadMore();

    /**
     * 获取书籍列表
     */
    fun getBookList(curPage: Int, callback: IDataCallBack<BookShelfList>)

    /**
     * 获取推荐书籍列表
     */
    fun getRecommendBookList(callback: IDataCallBack<BookShelfList>)

    /**
     * 同步书籍编辑记录
     */
    fun syncBookModifyRecord(isFromLogin: Boolean = false)

    /**
     * 是否来自登录  来自登录同步时需要弹出toast
     */
    fun setFromLogin(fromLogin: Boolean)
}