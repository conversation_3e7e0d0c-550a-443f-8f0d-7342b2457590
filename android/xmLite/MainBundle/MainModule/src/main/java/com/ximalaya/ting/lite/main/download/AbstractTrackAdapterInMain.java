package com.ximalaya.ting.lite.main.download;

import android.content.Context;
import android.view.View;

import com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.lite.main.play.fragment.ChooseTrackQualityDialog;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import java.util.List;

/**
 * <AUTHOR> on 2017/11/27.
 */

public abstract class AbstractTrackAdapterInMain extends AbstractTrackAdapter{
    public AbstractTrackAdapterInMain(Context context, List<Track> listData) {
        super(context, listData);
    }


    @Override
    public void download(final Track track, final View view) {
        if(RouteServiceUtil.getDownloadService().isTrackQualitySettingActive()) {
            super.download(track, view);
        }else {
            ChooseTrackQualityDialog.ActionCallBack callBack = new ChooseTrackQualityDialog.ActionCallBack() {
                @Override
                public void onConfirm() {
                    AbstractTrackAdapterInMain.super.download(track, view);
                }

                @Override
                public void onCancel() {

                }
            };

            try {
                ChooseTrackQualityDialog.newInstance(context, callBack).show();
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

}
