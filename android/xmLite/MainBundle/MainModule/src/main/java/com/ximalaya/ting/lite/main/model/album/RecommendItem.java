package com.ximalaya.ting.lite.main.model.album;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.host.model.ad.BannerModel;
import com.ximalaya.ting.android.host.model.ad.RecommendNewUseBannerModel;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.model.weike.WeikeCardItemM;
import com.ximalaya.ting.android.host.util.common.ToolUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * https://gitlab.ximalaya.com/ting/discovery/wikis/recommend
 * <p>
 * value	remark	内容	更多跳转
 * focus	焦点图
 * square	糖葫芦
 * gussYouLike	猜你喜欢	album	list
 * live	直播
 * paidCategory	付费精品	album	category
 * cityCategory	本地听	album
 * ad	广告	广告http接口
 * playlist	听单		list
 * categoriesForLong	一级分类_长期兴趣	album	category
 * categoriesForShort	一级分类_短期兴趣	album	category
 * categoriesForExplore	一级分类_探索兴趣	album	category
 * subCategoriesForLong	二级分类_长期兴趣	album	list
 * subCategoriesForShort	二级分类_短期兴趣	album	list
 * recommendAlbum	推荐流	推荐流接口
 *
 * <AUTHOR>
 */
public class RecommendItem {
    public static final String RECOMMEND_TYPE_FOCUS = "focus";
    public static final String RECOMMEND_TYPE_SQUARE = "square";
    public static final String RECOMMEND_TYPE_GUSSYOULIKE = "guessYouLike";
    public static final String RECOMMEND_TYPE_LIVE = "live";
    public static final String RECOMMEND_TYPE_PAIDCATEGORY = "paidCategory";
    public static final String RECOMMEND_TYPE_CITYCATEGORY = "cityCategory";
    public static final String RECOMMEND_TYPE_AD = "ad";
    public static final String RECOMMEND_TYPE_PLAYLIST = "playlist";
    public static final String RECOMMEND_TYPE_CATEGORIESFORLONG = "categoriesForLong";
    public static final String RECOMMEND_TYPE_CATEGORIESFORSHORT = "categoriesForShort";
    public static final String RECOMMEND_TYPE_CATEGORIESFOREXPLORE = "categoriesForExplore";
    public static final String RECOMMEND_TYPE_SUBCATEGORIESFORLONG = "subCategoriesForLong";
    public static final String RECOMMEND_TYPE_SUBCATEGORIESFORSHORT = "subCategoriesForShort";
    public static final String RECOMMEND_TYPE_RECOMMENDALBUM = "recommendAlbum";
    public static final String RECOMMEND_TYPE_BOUTIQUE_HOT = "recommendBoutiqueHot";//热门新品
    public static final String RECOMMEND_TYPE_KEYWORD = "keyword";//热词卡片
    public static final String RECOMMEND_TYPE_HEADLINE = "topBuzz"; //头条
    public static final String RECOMMEND_TYPE_ONE_KEY_LISTEN = "oneKeyListen"; //一键听
    public static final String RECOMMEND_TYPE_WEIKE = "microLesson";
    public static final String RECOMMEND_TYPE_NEW_USER_FOCUS = "newUserFocus";  // 新用户的焦点图

    public static final String RECOMMEND_DIRECTION_ROW = "row";
    public static final String RECOMMEND_DIRECTION_COLUMN = "column";

    public static final String RECOMMEND_CATEGORY_ID = "categoryId";
    public static final String RECOMMEND_SUB_CATEGORY_ID = "subcategoryId";
    public static final String RECOMMEND_KEYWORD_ID = "keywordId";
    public static final String RECOMMEND_TARGET_GROUP_ID = "groupId";

    public static final String ALBUM_INFO_TYPE_UPDATE = "update";
    public static final String ALBUM_INFO_TYPE_PLAY_OR_SCORE = "play_or_score";


    private String moduleType;
    private String direction;
    private int loopCount;
    private String title;
    private boolean hasMore;
    private boolean bottomHasMore;
    private Map<String, Integer> target;
    private List list;
    private int indexOfList;    // 在数据返回的列表中的位置
    private boolean showInterestCard;
    private List<RecommendCategoryTag> keywords;
    private int categoryId;
    private String recIting; // 推荐的跳转iting
    private String recEntranceName; // 推荐入口的文案

    // 刷新时 当前的loop index
    private int currentLoopCount = -1;
    private Set<Long> requestDataIds;
    private RecommendRefreshDataPool mRefreshDataPool;

    public RecommendItem() {
    }

    public RecommendItem(RecommendItem other) {
        this.moduleType = other.moduleType;
        this.direction = other.direction;
        this.loopCount = other.loopCount;
        this.title = other.title;
        this.hasMore = other.hasMore;
        this.bottomHasMore = other.bottomHasMore;
        this.target = other.target;
        this.indexOfList = other.indexOfList;
        this.showInterestCard = other.showInterestCard;
        this.categoryId = other.categoryId;
    }

    public static RecommendItem parseData(JSONObject jsonObject, Gson gson) {
        if (jsonObject == null || gson == null) {
            return null;
        }

        RecommendItem recommendItem = new RecommendItem();
        recommendItem.setModuleType(jsonObject.optString("moduleType"));
        recommendItem.setDirection(jsonObject.optString("direction"));
        recommendItem.setLoopCount(jsonObject.optInt("loopCount"));
        recommendItem.setTitle(jsonObject.optString("title"));
        recommendItem.setBottomHasMore(jsonObject.optBoolean("bottomHasMore"));
        recommendItem.setHasMore(jsonObject.optBoolean("hasMore"));
        recommendItem.setShowInterestCard(jsonObject.optBoolean("showInterestCard"));
        recommendItem.setCategoryId(jsonObject.optInt("categoryId"));
        recommendItem.setRecIting(jsonObject.optString("recIting"));
        recommendItem.setRecEntranceName(jsonObject.optString("recEntranceName"));


        JSONObject targetObject = jsonObject.optJSONObject("target");
        if (targetObject != null) {
            Map<String, Integer> maps = new HashMap<>();
            if (targetObject.has(RECOMMEND_CATEGORY_ID)) {
                maps.put(RECOMMEND_CATEGORY_ID, targetObject.optInt(RECOMMEND_CATEGORY_ID));
            } else if (targetObject.has(RECOMMEND_SUB_CATEGORY_ID)) {
                maps.put(RECOMMEND_SUB_CATEGORY_ID, targetObject.optInt(RECOMMEND_SUB_CATEGORY_ID));
            }
            if (targetObject.has(RECOMMEND_KEYWORD_ID)) {
                maps.put(RECOMMEND_KEYWORD_ID, targetObject.optInt(RECOMMEND_KEYWORD_ID));
            }

            if (targetObject.has(RECOMMEND_TARGET_GROUP_ID)) {  //头条使用到groupId
                try {
                    maps.put(RECOMMEND_TARGET_GROUP_ID, targetObject.optInt(RECOMMEND_TARGET_GROUP_ID));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            recommendItem.setTarget(maps);
        }

        if (jsonObject.has("list")) {
            List list = new ArrayList();
            String moduleType = recommendItem.getModuleType();

            if (RECOMMEND_TYPE_FOCUS.equals(moduleType)) {
                JSONArray jsonArray1 = jsonObject.optJSONArray("list");
                if (jsonArray1 != null && jsonArray1.length() > 0) {
                    JSONObject jsonObject1 = jsonArray1.optJSONObject(0);
                    if (jsonObject1 != null) {
                        long responseId = jsonObject1.optLong("responseId");
                        Type listType = new TypeToken<List<BannerModel>>() {
                        }.getType();
                        list = new Gson().fromJson(jsonObject1.optString("data"), listType);
                        if (!ToolUtil.isEmptyCollects(list)) {
                            for (Object bannerModel : list) {
                                if (bannerModel instanceof BannerModel) {
                                    ((BannerModel) bannerModel).setResponseId(responseId);
                                }
                            }
                        }
                    }
                }
            } else if (RECOMMEND_TYPE_SQUARE.equals(moduleType)) {
                JSONArray jsonArray1 = jsonObject.optJSONArray("list");
                if (jsonArray1 != null) {
                    for (int i = 0; i < jsonArray1.length(); i++) {
                        list.add(new RecommendDiscoveryM(jsonArray1.optJSONObject(i)));
                    }
                }
            } else if (RECOMMEND_TYPE_GUSSYOULIKE.equals(moduleType)
                    || RECOMMEND_TYPE_PAIDCATEGORY.equals(moduleType)
                    || RECOMMEND_TYPE_CITYCATEGORY.equals(moduleType)
                    || RECOMMEND_TYPE_CATEGORIESFORLONG.equals(moduleType)
                    || RECOMMEND_TYPE_CATEGORIESFORSHORT.equals(moduleType)
                    || RECOMMEND_TYPE_CATEGORIESFOREXPLORE.equals(moduleType)
                    || RECOMMEND_TYPE_SUBCATEGORIESFORLONG.equals(moduleType)
                    || RECOMMEND_TYPE_SUBCATEGORIESFORSHORT.equals(moduleType)
                    // 推荐流
                    || RECOMMEND_TYPE_RECOMMENDALBUM.equals(moduleType)
                    || RECOMMEND_TYPE_PLAYLIST.equals(moduleType)
                    || RECOMMEND_TYPE_KEYWORD.equals(moduleType)) {
                JSONArray jsonArray1 = jsonObject.optJSONArray("list");
                if (jsonArray1 != null) {
                    for (int i = 0; i < jsonArray1.length(); i++) {
                        AlbumMInMain albumM = new AlbumMInMain();
                        albumM.setIndexOfList(i + 1);
                        try {
                            albumM.parseAlbum(jsonArray1.optJSONObject(i));
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        list.add(albumM);
                    }
                }
            } else if (RECOMMEND_TYPE_HEADLINE.equals(moduleType)) {
                JSONArray trackArray = jsonObject.optJSONArray("list");
                if (trackArray != null) {
                    for (int i = 0; i < trackArray.length(); i++) {
                        try {
                            TrackM trackM = new TrackM(trackArray.optString(i));
                            list.add(trackM);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            } else if (RECOMMEND_TYPE_WEIKE.equals(moduleType)) {
                JSONArray jsonArray1 = jsonObject.optJSONArray("list");
                if (jsonArray1 != null) {
                    for (int i = 0; i < jsonArray1.length(); i++) {
                        WeikeCardItemM e = new WeikeCardItemM(jsonArray1.optString(i));
                        list.add(e);
                    }
                }
            } else if (RECOMMEND_TYPE_NEW_USER_FOCUS.equals(moduleType)) {
                JSONArray jsonArray1 = jsonObject.optJSONArray("list");
                if (jsonArray1 != null && jsonArray1.length() > 0) {
                    JSONObject jsonObject1 = jsonArray1.optJSONObject(0);
                    if (jsonObject1 != null) {
                        Type listType = new TypeToken<List<RecommendNewUseBannerModel>>() {
                        }.getType();
                        List tempList = new Gson().fromJson(jsonObject1.optString("data"), listType);
                        if (!ToolUtil.isEmptyCollects(tempList)) {
                            for (Object useBanner : tempList) {
                                if (useBanner instanceof RecommendNewUseBannerModel) {
                                    BannerModel bannerModel = new BannerModel();
                                    bannerModel.setNewUserBannerModel((RecommendNewUseBannerModel) useBanner);
                                    list.add(bannerModel);
                                }
                            }
                        }
                    }
                }
            } else {
                JSONArray jsonArray1 = jsonObject.optJSONArray("list");
                if (jsonArray1 != null) {
                    for (int i = 0; i < jsonArray1.length(); i++) {
                        AlbumMInMain albumM = new AlbumMInMain();
                        albumM.setIndexOfList(i + 1);
                        try {
                            albumM.parseAlbum(jsonArray1.optJSONObject(i));
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        list.add(albumM);
                    }
                }
            }

            recommendItem.setList(list);
        }

        return recommendItem;
    }

    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public String getDirection() {
        // 推荐流 直播 听单 方向固定
        if (RECOMMEND_TYPE_RECOMMENDALBUM.equals(moduleType)
                || RECOMMEND_TYPE_PLAYLIST.equals(moduleType)) {
            return RecommendItem.RECOMMEND_DIRECTION_COLUMN;
        }

        //  一键听 方向固定
        if (RECOMMEND_TYPE_ONE_KEY_LISTEN.equals(moduleType)
                || RECOMMEND_TYPE_WEIKE.equals(moduleType)) {
            return RecommendItem.RECOMMEND_DIRECTION_ROW;
        }

        if (TextUtils.isEmpty(direction)) {
            if (RECOMMEND_TYPE_GUSSYOULIKE.equals(moduleType)
                    || RECOMMEND_TYPE_PAIDCATEGORY.equals(moduleType)
                    || RECOMMEND_TYPE_CITYCATEGORY.equals(moduleType)
                    || RECOMMEND_TYPE_CATEGORIESFORLONG.equals(moduleType)
                    || RECOMMEND_TYPE_CATEGORIESFORSHORT.equals(moduleType)
                    || RECOMMEND_TYPE_CATEGORIESFOREXPLORE.equals(moduleType)
                    || RECOMMEND_TYPE_SUBCATEGORIESFORLONG.equals(moduleType)
                    || RECOMMEND_TYPE_SUBCATEGORIESFORSHORT.equals(moduleType)
                    || RECOMMEND_TYPE_KEYWORD.equals(moduleType)) {
                return RecommendItem.RECOMMEND_DIRECTION_ROW;
            }
        }
        return direction;
    }

    private void setDirection(String direction) {
        this.direction = direction;
    }

    public int getLoopCount() {
        return loopCount;
    }

    private void setLoopCount(int loopCount) {
        this.loopCount = loopCount;
        currentLoopCount = loopCount;
    }

    public String getTitle() {
        return title;
    }

    private void setTitle(String title) {
        this.title = title;
    }

    public boolean isHasMore() {
        return hasMore;
    }

    private void setHasMore(boolean hasMore) {
        this.hasMore = hasMore;
    }

    public boolean isBottomHasMore() {
        return bottomHasMore;
    }

    private void setBottomHasMore(boolean bottomHasMore) {
        this.bottomHasMore = bottomHasMore;
    }

    public List getList() {
        return list;
    }

    public void setList(List list) {
        this.list = list;
    }

    public Map<String, Integer> getTarget() {
        return target;
    }

    private void setTarget(Map<String, Integer> target) {
        this.target = target;
    }

    public int getIndexOfList() {
        return indexOfList;
    }

    public void setIndexOfList(int indexOfList) {
        this.indexOfList = indexOfList;
    }

    public boolean isShowInterestCard() {
        return showInterestCard;
    }

    private void setShowInterestCard(boolean showInterestCard) {
        this.showInterestCard = showInterestCard;
    }

    public void setKeywords(List<RecommendCategoryTag> keywords) {
        this.keywords = keywords;
    }

    public List<RecommendCategoryTag> getKeywords() {
        return keywords;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public int getCurrentLoopCount() {
        return currentLoopCount;
    }

    public void setCurrentLoopCount(int currentLoopCount) {
        this.currentLoopCount = currentLoopCount;
    }

    public Set<Long> getRequestDataIds() {
        return requestDataIds;
    }

    public void setRequestDataIds(Set<Long> requestDataIds) {
        this.requestDataIds = requestDataIds;
    }

    public RecommendRefreshDataPool getRefreshDataPool() {
        return mRefreshDataPool;
    }

    public void setRefreshDataPool(RecommendRefreshDataPool refreshDataPool) {
        mRefreshDataPool = refreshDataPool;
    }

    public String getRecIting() {
        return recIting;
    }

    public void setRecIting(String recIting) {
        this.recIting = recIting;
    }

    public String getRecEntranceName() {
        return recEntranceName;
    }

    public void setRecEntranceName(String recEntranceName) {
        this.recEntranceName = recEntranceName;
    }
}
