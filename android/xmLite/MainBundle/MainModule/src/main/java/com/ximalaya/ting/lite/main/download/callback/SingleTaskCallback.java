package com.ximalaya.ting.lite.main.download.callback;


import com.ximalaya.ting.lite.main.download.bean.Statistics;
import com.ximalaya.ting.lite.main.download.inter.IEngineObserver;
import com.ximalaya.ting.lite.main.download.inter.ITaskCallback;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   : 单线程下载的回调,直接通过IEngineObserver通知进度
 */
public class SingleTaskCallback  extends CountDownCallback implements ITaskCallback {
    private IEngineObserver observer;
    private Statistics statistics;

    public SingleTaskCallback(IEngineObserver obs, Statistics statistics) {
        observer = obs;
        this.statistics = statistics;
    }

    @Override
    public void onProgressUpdate(long len, long done, long fileSize) {
        if(statistics != null) {
            statistics.increment(len);
            if(observer != null) {
                observer.onDownloading(statistics.total, statistics.done, statistics.speed);
            }
        }
    }
}
