package com.ximalaya.ting.lite.main.download;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.AlbumUtils;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.view.bar.RoundProgressBar;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.util.ArrayList;
import java.util.List;


/**
 * 下载听模块的声音条
 *
 * <AUTHOR>
 */
public class DownloadedTrackAdapter extends AbstractTrackAdapterInMain{
    private boolean batchDelete = false;
    private boolean selectAll = false;
    private boolean mNeedSubtitle;
    private boolean mSelectListened; // 选择已听完

    public DownloadedTrackAdapter(Context context, @Nullable List<Track> listData) {
        this(context, listData, true);
    }

    public DownloadedTrackAdapter(Context context, @Nullable List<Track> listData, boolean needSubTitle) {
        super(context, listData);
        mNeedSubtitle = needSubTitle;
    }


    @Override
    public void onClick(View view, Track track, int position, BaseViewHolder holder) {
        int i = view.getId();
        if (i == R.id.main_iv_del) {
            if (mType == TYPE_SEARCH_DOWNLOAD) {
                long id = track != null ? track.getDataId() : 0;
            }
            delete(track);
        } else if (i == R.id.main_iv_cover) {
            play(track, false, false, view);
            if (!PlayTools.isCurrentTrackPlaying(context, track)) {
                if (track != null) {
                    new UserTracking()
                            .setSrcPage("专辑下载页")
                            .setSrcModule("声音条")
                            .setItem("button")
                            .setItemId("play")
                            .setSrcPosition(position)
                            .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                }
            }
        }
    }

    /**
     * 声音条播放
     *
     * @param track      当前声音
     * @param showPlay   是否显示播放页
     * @param confirmNet 是否确认流量弹框 true显示流量弹框,false直接播放用在离线
     * @param view       声音条view
     */
    @Override
    public void play(Track track, boolean showPlay, boolean confirmNet, View view) {
        if (track == null) {
            return;
        }
        if (PlayTools.isCurrentTrackPlaying(context, track)) {//暂停
            XmPlayerManager.getInstance(context).pause();
        } else if (PlayTools.isCurrentTrack(context, track)) {
            XmPlayerManager.getInstance(context).play();
        } else {
            int albumPageState = AlbumUtils.getAlbumPageState(albumM);
            if (albumPageState == AlbumUtils.ALBUM_PAGE_STATE_FREE) {
                doRealPlay(track, showPlay, false, view);
            } else {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(context);
                } else {
                    if (NetworkType.getNetWorkType(this.context) == NetworkType.NetWorkType.NETWORKTYPE_INVALID) {
                        // 可以播放VIP/精品专辑，当连上网络时，重新查询声音权限
                        doRealPlay(track, showPlay, false, view);
                    } else {
                        // VIP/精品专辑：每次播放声音时，需请求声音服务权限，根据是否有权限来决定是否能播放
                        doRealPlay(track, showPlay, true, view);
                    }
                }
            }

        }

    }

    private void doRealPlay(Track track, boolean showPlay, boolean confirmNet, View view) {
        if (listData != null && !listData.isEmpty()) {
            int index = listData.indexOf(track);
            if (index >= 0 && index < listData.size())
                if (confirmNet) {
                    PlayTools.playList(context, listData, index, showPlay, view);
                } else {
                    PlayTools.playListWithoutWifi(context, listData, index, false, view);
                }
        } else {
            if (confirmNet) {
                PlayTools.playTrack(context, track, showPlay, view);
            } else {
                PlayTools.playTrackWithoutWifi(context, track, false, view);
            }
        }
    }

    /**
     * 获取item布局文件id
     *
     * @return item view 的布局id
     */
    @Override
    public int getConvertViewId() {
        if (mNeedSubtitle) {
            return R.layout.main_item_downloaded_track;
        } else {
            return R.layout.main_item_downloaded_track_without_subtitle;
        }
    }

    protected void bindPlayFlagViewData(ViewHolder holder, Track track){
        if (PlayTools.isCurrentTrack(context, track)) {
            if (XmPlayerManager.getInstance(context).isBuffering()) {//正在加载中
                startLoading(holder.playFlag);
            } else {
                stopLoading(holder.playFlag);
                holder.playFlag.setImageResource(XmPlayerManager.getInstance(context).isPlaying() ? com.ximalaya.ting.android.host.R.drawable.host_pause_in_track_item : com.ximalaya.ting.android.host.R.drawable.host_play_in_track_item);
            }
        } else {
            stopLoading(holder.playFlag);
            holder.playFlag.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_play_in_track_item);
        }
    }

    protected void bindTitleViewData(ViewHolder holder, Track track){
        holder.title.setText(track.getTrackTitle());
    }

    protected void bindDownloadQualityViewData(ViewHolder holder, Track track){
        switch (track.getDownloadQualityLevel()){
            case Track.TRACK_QUALITY_HIGH:
                holder.ivTrackQuality.setImageResource(R.drawable.main_ic_track_quality_high);
                holder.ivTrackQuality.setVisibility(View.VISIBLE);
                break;
            default:
                holder.ivTrackQuality.setVisibility(View.GONE);
        }
    }

    @Override
    public void bindViewDatas(BaseViewHolder h, Track track, int position) {
        ViewHolder holder = (ViewHolder) h;

        bindPlayFlagViewData(holder, track);

        String coverUrl = TextUtils.isEmpty(track.getCoverUrlMiddle()) ? track
                .getCoverUrlSmall() : track.getCoverUrlMiddle();
        ImageManager.from(context).displayImage(holder.cover, coverUrl,
                com.ximalaya.ting.android.host.R.drawable.host_default_album_145);

        if(coverClickable()){
            holder.cover.setHasPressDownShade(true);
            setClickListener(holder.cover, track, position, holder);
            AutoTraceHelper.bindData(holder.cover,track);
        }else{
            holder.cover.setHasPressDownShade(false);
        }

        holder.duration.setText(StringUtil.toTime(track.getDuration()));

        bindTitleViewData(holder, track);

        if (mNeedSubtitle && track.getAlbum() != null) {
            holder.subtitle.setText(track.getAlbum().getAlbumTitle());
        }

        String fileSize = StringUtil.toMBFormatString(getDownloadSize(track)) + "M";
        holder.tvDownloadSize.setText(fileSize);


        bindDownloadQualityViewData(holder, track);


        if (batchDelete) {
            holder.ivDel.setVisibility(View.GONE);
            holder.ivBatchDelete.setVisibility(View.VISIBLE);
            if (track.isChecked()) {
                holder.ivBatchDelete.setImageResource(R.drawable.main_check_delete);
            } else {
                holder.ivBatchDelete.setImageResource(R.drawable.main_uncheck_delete);
            }
        } else {
            holder.ivDel.setVisibility(View.VISIBLE);
            holder.ivBatchDelete.setVisibility(View.GONE);
        }
        setClickListener(holder.ivDel, track, position, holder);
        AutoTraceHelper.bindData(holder.ivDel,track);


        bindTextColor(holder, track);
    }

    protected void bindTextColor(ViewHolder holder, Track track){
        int lastPos = XmPlayerManager.getInstance(context).getHistoryPos(track.getDataId());
        String ps = ToolUtil.getPlaySchedule(lastPos, track.getDuration());
        double percent = lastPos / (track.getDuration() * 1000d) * 100;
        if (!TextUtils.isEmpty(ps)) {
            holder.playSchedule.setVisibility(View.VISIBLE);
            holder.playSchedule.setText(ps);
            holder.playSchedule.setTextColor(0xFFff8300);
            if (percent >= 97 || lastPos == PlayerConstants.PLAY_COMPLETE) {//已播完
                holder.title.setTextColor(0xFF999999);
                if (mNeedSubtitle) {
                    holder.subtitle.setTextColor(0xFF999999);
                }
            } else {
                holder.title.setTextColor(context.getResources().getInteger(R.integer.main_color_black));
                if (mNeedSubtitle) {
                    holder.subtitle.setTextColor(context.getResources().getInteger(R.integer.main_color_black));
                }
            }
        } else {
            holder.playSchedule.setVisibility(View.GONE);
            holder.title.setTextColor(context.getResources().getInteger(R.integer.main_color_black));
            if (mNeedSubtitle) {
                holder.subtitle.setTextColor(context.getResources().getInteger(R.integer.main_color_black));
            }
        }

        // 处理播放中的显示
        if (PlayTools.isPlayCurrTrackById(context, track.getDataId())) {
            XmPlayerManager xManager = XmPlayerManager.getInstance(context);
            if (xManager.isPlaying()) {
                holder.title.setTextColor(0xFFF86442);
            }
        }
    }

    protected boolean coverClickable(){
        return true;
    }

    protected long getDownloadSize(@NonNull Track track){
        return track.getDownloadSize();
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    public boolean getBatchDelete() {
        return batchDelete;
    }

    public void setBatchDelete(boolean batchDelete) {
        this.batchDelete = batchDelete;
        notifyDataSetChanged();
    }

    public boolean isSelectAll() {
        return selectAll;
    }

    public void setSelectAll(boolean selectAll) {
        this.selectAll = selectAll;
    }

    public void setAllSelected(boolean selectAll, boolean needNotify) {
        this.selectAll = selectAll;
        if (listData != null && !listData.isEmpty()) {
            for (Track track : listData) {
                if (selectAll) {
                    track.setChecked(true);
                } else {
                    track.setChecked(false);
                }
            }
        }
        if (needNotify) {
            notifyDataSetChanged();
        }
    }


    /**
     * 删除已下载声音
     */
    protected void delete(Track track) {
        deleteListData(track);
        RouteServiceUtil.getDownloadService().deleteDownloadedTasks(track);
    }

    public boolean isSelectListened() {
        return mSelectListened;
    }

    public void setSelectListened(boolean selectListened) {
        this.mSelectListened = selectListened;
    }

    /**
     * 遍历选择已听完的track
     *
     */
    public void setListenedSelected(boolean selectListened, boolean needNotify) {
        this.mSelectListened = selectListened;
        if (listData != null && !listData.isEmpty()) {
            for (Track track : listData) {
                // 计算是否已听完, 已听完的判断依据：本条声音听完97%以上。
                if (isListenerOver(track)) {
                    track.setChecked(selectListened);
                } else {
                    track.setChecked(false);
                }
            }
        }
        if (needNotify) {
            notifyDataSetChanged();
        }
    }

    /**
     * 是否存在已听完的Track
     *
     * @return
     */
    public boolean hasListenerOverTrack() {
        if(getListData() != null) {
            for (Track track : getListData()) {
                if (isListenerOver(track)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 已听完的所有数据是否都被选中
     *
     * @return
     */
    private boolean isAllListenerOverSelected() {
        List<Track> listenerOverTracks = new ArrayList<>();
        for (Track track : getListData()) {
            if (isListenerOver(track)) {
                listenerOverTracks.add(track);
            }
        }
        for (Track track : listenerOverTracks) {
            if (!track.isChecked()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 所有选中的是否都是已听
     *
     * @return
     */
    public boolean isAllCheckedListenerOver() {
        List<Track> checkedTracks = new ArrayList<>();
        if(getListData() != null) {
            for (Track track : getListData()) {
                if (track.isChecked()) {
                    checkedTracks.add(track);
                }
            }
        }
        if (ToolUtil.isEmptyCollects(checkedTracks)) {
            return false;
        }
        for (Track track : checkedTracks) {
            if (!isListenerOver(track)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Track 是否已听完
     *
     */
    public boolean isListenerOver(Track track) {
        if (null == track)
            return false;
        // 计算是否已听完, 已听完的判断依据：本条声音听完97%以上。
        int lastPos = XmPlayerManager.getInstance(context).getHistoryPos(track.getDataId());
        double percent = lastPos / (track.getDuration() * 1000d) * 100;
        if (percent >= 97 || lastPos == PlayerConstants.PLAY_COMPLETE) { // 已播完
            return true;
        }
        return false;
    }

    protected class ViewHolder extends HolderAdapter.BaseViewHolder {

        public final ImageView ivBatchDelete;
        public final ImageView ivDel;
        public final RoundProgressBar progressBar;
        public final TextView tvStatus;
        public final TextView tvDownloadSize;
        public final View root;
        public final RoundImageView cover;
        public final TextView title;
        public final TextView duration;
        public final ImageView playFlag;
        public final View border;
        public final TextView subtitle;
        public final TextView playSchedule;
        ImageView ivTrackQuality;

        public ViewHolder(View convertView) {
            root = convertView;
            ivBatchDelete = (ImageView) convertView.findViewById(R.id.main_batch_delete_icon);
            cover =  convertView.findViewById(R.id.main_iv_cover);
            border = convertView.findViewById(R.id.main_border_bottom);
            playFlag = (ImageView) convertView.findViewById(R.id.main_play_icon);
            title = (TextView) convertView.findViewById(R.id.main_download_track_title);
            subtitle = (TextView) convertView.findViewById(R.id.main_tv_subtitle);
            duration = (TextView) convertView.findViewById(R.id.main_tv_total_time);
            ivDel = (ImageView) convertView.findViewById(R.id.main_iv_del);
            progressBar = (RoundProgressBar) convertView.findViewById(R.id.main_pb_download_progress);
            tvStatus = (TextView) convertView.findViewById(R.id.main_tv_status);
            tvDownloadSize = (TextView) convertView.findViewById(R.id.main_tv_file_size);
            playSchedule = (TextView) convertView.findViewById(R.id.main_tv_play_schedule);
            ivTrackQuality = (ImageView)convertView.findViewById(R.id.main_iv_track_quality);
        }
    }
}
