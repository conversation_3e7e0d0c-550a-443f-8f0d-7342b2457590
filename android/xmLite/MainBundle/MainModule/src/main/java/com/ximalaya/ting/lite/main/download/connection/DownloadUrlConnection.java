/*
 * Copyright (c) 2017 LingoChamp Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ximalaya.ting.lite.main.download.connection;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.framework.reflect.FieldUtils;
import com.ximalaya.ting.android.opensdk.httputil.Config;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.lite.main.download.inter.IDownloadConnection;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;

import okhttp3.Response;

/**
 * <AUTHOR> feiwen
 * date   :
 * desc   :
 */
public class DownloadUrlConnection implements IDownloadConnection {

    protected URLConnection connection;

    private DownloadUrlConnection(String url, Config config) throws IOException {
        // 对config为空的处理
        if(config == null) {
            IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
            if (freeFlowService != null) {
                config = freeFlowService.createConfig();
            }
        }
        String method = (null != config) ? config.method : Config.METHOD_GET;
        Config finalConfig = config;
        connection = FreeFlowServiceUtil.getHttpURLConnection(config, url, method, new IFreeFlowService.ISetHttpUrlConnectAttribute() {
                    @Override
                    public void setHttpUrlConnectAttributes(@NonNull HttpURLConnection urlConnection) {
                        if (finalConfig != null && finalConfig.property != null) {
                            for (Map.Entry<String, String> entry : finalConfig.property.entrySet()) {
                                urlConnection.setRequestProperty(entry.getKey(), entry.getValue());
                            }
                        }
                    }
                }
        );
    }

    @Override
    public void addHeader(String name, String value) {
        connection.addRequestProperty(name, value);
    }

    @Override
    public IDownloadConnection execute() throws IOException {
        connection.connect();
        return this;
    }

    @Override
    public int getResponseCode() throws IOException {
        if (connection instanceof HttpURLConnection) {
            return ((HttpURLConnection) connection).getResponseCode();
        }

        return IDownloadConnection.NO_RESPONSE_CODE;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return connection.getInputStream();
    }

    @Override
    public boolean setRequestMethod(@NonNull String method) throws ProtocolException {
        if (connection instanceof HttpURLConnection) {
            ((HttpURLConnection) connection).setRequestMethod(method);
            return true;
        }

        return false;
    }

    @Override
    public Map<String, List<String>> getResponseHeaderFields() {
        return connection.getHeaderFields();
    }

    @Override
    public String getResponseHeaderField(String name) {
        return connection.getHeaderField(name);
    }

    public String getResponseHeaderField2(String name) {
        try {
            Response response = (Response) FieldUtils.readField(connection, "networkResponse");
            return response.header(name);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * TODO
     */
    @Override
    public String getRedirectLocation() {
        return null;
    }

    @Override
    public void disconnect() {
        if (connection instanceof HttpURLConnection) {
            ((HttpURLConnection) connection).disconnect();
        } else {
            // the same to response#close on okhttp
            // real execute RealBufferedSource.InputStream#close
            try {
                final InputStream inputStream = connection.getInputStream();
                if (inputStream != null) inputStream.close();
            } catch (IOException ignored) {
            }
        }
    }

    @Override
    public Map<String, List<String>> getRequestProperties() {
        return connection.getRequestProperties();
    }

    public void addRequestHeaderFields(@NonNull Map<String, List<String>> headerFields) {
        for (Map.Entry<String, List<String>> entry : headerFields.entrySet()) {
            String key = entry.getKey();
            List<String> values = entry.getValue();
            for (String value : values) {
                this.addHeader(key, value);
            }
        }
    }

    @Override
    public String getRequestProperty(String key) {
        return connection.getRequestProperty(key);
    }

    public static class Factory implements IDownloadConnection.Factory {

        @Override
        public IDownloadConnection create(String originUrl, Config configuration) throws IOException {
            return new DownloadUrlConnection(originUrl, configuration);
        }

    }
}

