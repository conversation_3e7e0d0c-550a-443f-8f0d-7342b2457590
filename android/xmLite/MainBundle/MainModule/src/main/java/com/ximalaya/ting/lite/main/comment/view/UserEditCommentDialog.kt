package com.ximalaya.ting.lite.main.comment.view

import android.content.Context
import android.graphics.Rect
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.*
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.main.R

/**
 *  @Author: Junxiang Cheng
 *  @Mail: <EMAIL>
 *  @CreateTime: 1/5/22
 *
 *  @Description:
 */
class UserEditCommentDialog @JvmOverloads constructor(
    context: Context,
    private val hintText: CharSequence?,
    private val cachedText: CharSequence?
): XmBaseDialog<UserEditCommentDialog>(context, R.style.main_comment_edit_dialog) {

    var mContext: Context = context
    private lateinit var mRootView: ViewGroup
    private var mEditText: EditText? = null
    private var mTvConfirm: TextView? = null
    private var mTvLengthCount: TextView? = null
    private var mBg: View? = null

    private var mListener: OnTextConfirmListener? = null

    private var needCache = true

    init {
        initView(mContext)

        val window = window!!
        window.decorView.setPadding(0, 0, 0, 0)
        // 获取Window的LayoutParams
        val attributes = window.attributes
        attributes.width = WindowManager.LayoutParams.MATCH_PARENT
        attributes.height = WindowManager.LayoutParams.MATCH_PARENT
        attributes.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
        window.attributes = attributes
    }

    private fun initView(context: Context) {
        val inflater = context
            .getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val root = inflater.inflate(R.layout.main_lite_user_edit_comment_view, null) as ViewGroup
        mRootView = root

        setContentView(mRootView)
        mBg = mRootView.findViewById(R.id.main_view_dialog_background)
        mEditText = mRootView.findViewById(R.id.main_et_comment_content)
        mTvConfirm = mRootView.findViewById(R.id.main_tv_comment_send_confirm)

        mTvLengthCount = mRootView.findViewById(R.id.main_tv_comment_length)

        mEditText?.apply{
            hint = hintText?: mContext.getString(R.string.main_comment_preview_default_hint)
            setText(cachedText?: "")
            mTvConfirm?.isEnabled = !TextUtils.isEmpty(cachedText)
            mTvLengthCount?.text = (cachedText?.length ?: 0).toString() + "/300"

            setSelection(text.length)

            setOnFocusChangeListener { v, hasFocus ->
                if (!hasFocus) {
                    val manager = mContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager

                    manager.hideSoftInputFromWindow(v.windowToken,
                        InputMethodManager.HIDE_NOT_ALWAYS)
                }
            }

            setOnEditorActionListener { _, actionId, _ ->
                if (actionId == EditorInfo.IME_ACTION_DONE ||
                    actionId == EditorInfo.IME_ACTION_GO ||
                    actionId == EditorInfo.IME_ACTION_NEXT) {
                    confirmText()
                }
                return@setOnEditorActionListener false
            }

            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

                override fun afterTextChanged(s: Editable?) {
                    mTvConfirm?.isEnabled = !TextUtils.isEmpty(s)
                    mTvLengthCount?.text = (s?.length ?: 0).toString() + "/300"
                }
            })

        }

        mTvConfirm?.apply {
            setOnClickListener {
                if(!isEnabled) {
                    return@setOnClickListener
                }
                confirmText()
            }
        }

        mBg?.setOnClickListener {
            cancel()
        }

    }

    private fun showSoftInput() {
        if (mEditText != null) {
            mEditText?.apply{
                isFocusable = true
                isFocusableInTouchMode = true
                requestFocus()
                postDelayed({
                    val inputManager = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    inputManager.showSoftInput(this, 0)
                }, 100)
            }
        }
    }

    private fun confirmText() {
        val str = mEditText?.text

        if(TextUtils.isEmpty(str)) {
            return
        }
        str!!.toString().let{
            if(it.trim().isEmpty()){
                CustomToast.showFailToast("输入不能为空")
                return
            }
        }
        if (!UserInfoMannage.hasLogined()) {
            needCache = true
            dismiss()
            UserInfoMannage.gotoLogin(context)
        } else {
            mListener?.onConfirm(str.trimStart())
            needCache = false
            dismiss()
        }
    }

    override fun show() {
        super.show()
        mEditText?.post { this.showSoftInput() }
    }

    override fun dismiss() {
        if (needCache) {
            val str = mEditText?.text
            mListener?.onCancel(str)
        }

        mEditText?.post { mRootView.visibility = View.INVISIBLE }
        super.dismiss()
    }

    override fun onStop() {
        mListener = null
        super.onStop()
    }

    override fun onBackPressed() {
        needCache = true
        super.onBackPressed()
    }

    fun setOnTextConfirmListener(l: OnTextConfirmListener) {
        mListener = l
    }

    interface OnTextConfirmListener {
        fun onConfirm(cs: CharSequence)
        fun onCancel(cs: CharSequence?)
    }
}