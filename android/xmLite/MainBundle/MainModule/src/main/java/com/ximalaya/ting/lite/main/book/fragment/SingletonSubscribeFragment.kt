package com.ximalaya.ting.lite.main.book.fragment

import android.os.Bundle
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.mylisten.view.SubScribeFragment

/**
 * 单独的订阅界面  同 "我听-订阅" 内容一致  但是顶部标题栏不一样
 */
class SingletonSubscribeFragment : BaseFragment2() {

    private var isShowPlayButton: Boolean = true

    companion object {
        fun newInstance(args: Bundle): SingletonSubscribeFragment {
            val fragment = SingletonSubscribeFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun getPageLogicName(): String {
        return javaClass.simpleName
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_singleton_subscribe_layout
    }

    override fun getTitleBarResourceId(): Int {
        return R.id.main_single_subscribe_title_bar
    }

    override fun initUi(savedInstanceState: Bundle?) {
        setTitle("我的订阅")

        val transaction = childFragmentManager.beginTransaction()
        val fragment = SubScribeFragment()
        fragment.arguments = arguments
        transaction.add(R.id.main_singleton_container, fragment).commit()
    }

    override fun loadData() {

    }

    /**
     * 是否显示播放条
     */
    fun setShowPlayButton(isShow: Boolean) {
        isShowPlayButton = isShow
    }

    override fun isShowPlayButton(): Boolean {
        return isShowPlayButton
    }
}