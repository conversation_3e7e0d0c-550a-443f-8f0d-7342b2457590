package com.ximalaya.ting.lite.main.download;

import android.content.ContentValues;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.ximalaya.ting.android.downloadservice.database.DBConstant;
import com.ximalaya.ting.android.downloadservice.database.DBDataSupport;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.host.view.list.draglist.DragSortController;
import com.ximalaya.ting.android.host.view.list.draglist.DragSortListView;
import com.ximalaya.ting.android.host.view.list.draglist.DragSortListView.DropListener;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;

import java.util.ArrayList;
import java.util.List;



/**
 * 声音排序界面
 *
 * <AUTHOR>
 */
public class SoundSortFragment extends BaseFragment2 implements DropListener {

    private DragSortListView mListView;
    private SoundSortAdapter mAdapter;
    private List<Track> mData = new ArrayList<>();
    private boolean mApplyChange = false;
    private boolean mIsUpdatePositionForAlbum;

    public SoundSortFragment() {
        super(true, null);
    }

    public static SoundSortFragment newInstance(ArrayList<Track> list) {
        return newInstance(list, false);
    }

    public static SoundSortFragment newInstance(ArrayList<Track> list, boolean isUpdatePositionForAlbum) {
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleKeyConstants.KEY_LIST, list);
        bundle.putBoolean(BundleKeyConstants.KEY_UPDATE_POSITION_FOR_ALBUM, isUpdatePositionForAlbum);
        SoundSortFragment fra = new SoundSortFragment();
        fra.setArguments(bundle);
        return fra;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_top_layout;
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null){
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mListView = (DragSortListView) findViewById(android.R.id.list);
        mListView.setDragEnabled(true);

        DragSortController controller = new DragSortController(mListView);
        controller.setDragInitMode(DragSortController.ON_DOWN);
        controller.setDragHandleId(R.id.main_iv_drag_sort);
        controller.setSortEnabled(true);
        controller.setBackgroundColor(0x00000000);
        mListView.setFloatViewManager(controller);
        mListView.setOnTouchListener(controller);
        mListView.setDropListener(this);
        setTitle("手动排序");
    }

    private static final String TAG_TITLE_BAR_CANCEL = "cancel";
    private static final String TAG_TITLE_BAR_CONFIRM = "confirm";

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);

        titleBar.removeView(TitleBar.ActionType.BACK);

        TitleBar.ActionType cancel = new TitleBar.ActionType(TAG_TITLE_BAR_CANCEL, TitleBar.LEFT, 0, 0, 0, TextView.class);
        cancel.setContentStr("取消");
        cancel.setFontSize(14);

        titleBar.addAction(cancel, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finishFragment();
            }
        });

        TitleBar.ActionType confirm = new TitleBar.ActionType(TAG_TITLE_BAR_CONFIRM, TitleBar.RIGHT, 0, 0, 0, TextView.class);
        confirm.setContentStr("完成");
        confirm.setFontSize(14);

        titleBar.addAction(confirm, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                doSort();
            }
        });

        titleBar.update();
    }

    @Override
    @SuppressWarnings("unchecked")
    protected void loadData() {
        if (getArguments() != null && getArguments().getSerializable(BundleKeyConstants.KEY_LIST) != null) {
            // 注意此处拿到的对象和bundle.putSerializable(BundleKeyConstants.KEY_LIST,
            // list);的对象是同一个，必须重新new一个，否则容易出现
            // The content of the adapter has changed but ListView did not
            // receive a notification
            mData = new ArrayList<>((List<Track>) getArguments().getSerializable(BundleKeyConstants.KEY_LIST));
            mIsUpdatePositionForAlbum = getArguments().getBoolean(BundleKeyConstants.KEY_UPDATE_POSITION_FOR_ALBUM);
        }
        if (mData == null) {
            mData = new ArrayList<>();
        }
        if (mIsUpdatePositionForAlbum) {
            mAdapter = new SoundSortAdapter(mContext, mData, false);
        } else {
            mAdapter = new SoundSortAdapter(mContext, mData, true);
        }
        mListView.setAdapter(mAdapter);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_sound_sort;
    }

    @Override
    public void drop(int from, int to) {

        if (mAdapter != null && !mAdapter.isEmpty()) {
            if (from != to) {
                Track track = mAdapter.getListData().remove(from);
                mAdapter.getListData().add(to, track);
                mAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38484;
        super.onMyResume();
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public boolean isShowTruckFloatPlayBar() {
        return false;
    }

    private void doSort() {
        mApplyChange = true;
        final MyProgressDialog dialog = new MyProgressDialog(mActivity);
        dialog.setIndeterminate(true);
        dialog.setCancelable(true);
        dialog.setMessage("排序中···");
        dialog.delayShow();
        // 在主线程中修改adapter绑定的数据
        List<Track> data = mAdapter.getListData();
        for (int i = 0; i < data.size(); i++) {
            if (mIsUpdatePositionForAlbum) {
                data.get(i).setOrderPositionInAlbum(i + 1);
            } else {
                data.get(i).setOrderPositon(i);
            }
        }
        mAdapter.notifyDataSetChanged();
        new MyAsyncTask<Void, Void, Void>() {

            @Override
            public Void doInBackground(Void... params) {

                if (mData == null || mData.size() == 0)
                    return null;

                // 在非主线程中只做mAdapter数据查询操作
                List<Track> data = new ArrayList<>(mData);
                for (int i = 0; i < data.size(); i++) {
                    ContentValues values = new ContentValues();
                    if (mIsUpdatePositionForAlbum) {
                        values.put(DBConstant.ORDER_POSITION_IN_ALBUM, i + 1);
                    } else {
                        values.put(DBConstant.ORDER_POSITION, i);
                    }
                    DBDataSupport.updateTrack(data.get(i), values);
                }
                return null;
            }

            @Override
            public void onPostExecute(Void params) {

                if (!canUpdateUi())
                    return;

                dialog.cancel();
                setFinishCallBackData(true);
                finish();
            }

        }.myexec();
    }

}
