package com.ximalaya.ting.lite.main.home.adapter

import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel
import com.ximalaya.ting.lite.main.model.album.HorizontalScrollAlbumModel
import kotlinx.android.synthetic.main.main_item_horizontal_scroll_one_line_album.view.*

/**
 * Created by dumingwei on 2020/6/5
 *
 * Desc: 横向滑动三行专辑模块
 */
class HorizontalScrollThreeLineAlbumProvider @JvmOverloads constructor(
        val mFragment: BaseFragment2,
        private val mExtraModel: HomeRecommendExtraViewModel? = null,
        private val spanCount: Int = 3
) : IMulitViewTypeViewAndData<HorizontalScrollThreeLineAlbumProvider.Holder, HorizontalScrollAlbumModel> {

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_horizontal_scroll_three_line_album, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        val holder = Holder(convertView)
        initRvAlbum(holder)
        return holder
    }

    private fun initRvAlbum(holder: Holder) {
        val context = BaseApplication.getMyApplicationContext()
        with(holder.rootView) {
            mainRvAlbums.setDisallowInterceptTouchEventView(mFragment.view as ViewGroup)
            val adapter = AlbumRankAdapter(mFragment)
            mainRvAlbums.adapter = adapter
            holder.adapter = adapter

            val gridLayoutManager = GridLayoutManager(context,
                    spanCount, LinearLayoutManager.HORIZONTAL, false)
            gridLayoutManager.spanSizeLookup = MySpanSizeLookup(adapter, spanCount)

            mainRvAlbums.layoutManager = gridLayoutManager

            mainRvAlbums.addItemDecoration(AlbumRankAdapter.MyItemDecoration())
        }
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<HorizontalScrollAlbumModel>, convertView: View, position: Int) {
        val model = t.getObject()
        if (model is HorizontalScrollAlbumModel) {
            val albumMList = model.mainAlbumMList
            with(holder.rootView) {
                mainRvAlbums.clearOnScrollListeners()
                convertView.post {
                    (mainRvAlbums.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                            model.lastScrollPosition, model.lastScrollOffset)
                    mainRvAlbums.addOnScrollListener(OnScrollListener(model))
                }
                holder.adapter?.let {
                    if (albumMList.isHasMore) {
                        it.mOnMoreBtnClickListener = model.moreClickListener
                    } else {
                        it.mOnMoreBtnClickListener = null
                    }
                    it.mAlbumMList = albumMList.list
                    it.notifyDataSetChanged()
                }
            }
        }
    }

    class OnScrollListener(val mModel: HorizontalScrollAlbumModel) : RecyclerView.OnScrollListener() {

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            val firstView = recyclerView.layoutManager!!.getChildAt(0)
            if (firstView != null) {
                mModel.lastScrollPosition = recyclerView.layoutManager!!.getPosition(firstView)
                mModel.lastScrollOffset = firstView.left - recyclerView.layoutManager!!.getLeftDecorationWidth(firstView)
            }
        }
    }

    class Holder(var rootView: View) : BaseViewHolder() {
        var adapter: AlbumRankAdapter? = null
    }

    private class MySpanSizeLookup(private val mAdapter: AbRecyclerViewAdapter<*>?,
                                   private val spanCount: Int) : GridLayoutManager.SpanSizeLookup() {
        override fun getSpanSize(position: Int): Int {
            var spanSize = 0
            if (mAdapter != null) {
                // 为null，就是查看更多按钮
                spanSize = if (mAdapter.getItem(position) == null) {
                    spanCount
                } else {
                    1
                }
            }
            return spanSize
        }
    }

}