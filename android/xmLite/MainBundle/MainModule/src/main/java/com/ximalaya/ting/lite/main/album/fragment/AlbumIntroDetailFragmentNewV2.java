package com.ximalaya.ting.lite.main.album.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;

import com.ximalaya.ting.android.framework.util.FileUtil;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.view.other.LocalTemplateWebView;
import com.ximalaya.ting.android.host.view.other.RichWebView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.util.AsyncGson;
import com.ximalaya.ting.lite.main.base.BaseImageViewerFragment;
import com.ximalaya.ting.lite.main.model.album.AlbumIntroDetailTemplateModel;

/**
 * Created by WolfXu on 2018/8/8.
 * 专辑详情，左右滑动退出
 * <p>
 * 拷贝AlbumIntroDetailFragmentNew
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class AlbumIntroDetailFragmentNewV2 extends BaseImageViewerFragment {

    private static final String DEFAULT_ALBUM_COVER_URL = "https://fdfs.xmcdn.com/group45/M00/63/69/wKgKlFuOScHhcPyaAAAnJk4mQ3M951.png";

    private LocalTemplateWebView mRichContent;
    private AlbumM mAlbum;

    public static AlbumIntroDetailFragmentNewV2 newInstance(AlbumM data) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(BundleKeyConstants.KEY_ALBUM, data);
        AlbumIntroDetailFragmentNewV2 fra = new AlbumIntroDetailFragmentNewV2();
        fra.setArguments(bundle);
        return fra;
    }

    public AlbumIntroDetailFragmentNewV2() {
        super(AppConstants.isPageCanSlide, null);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {

        Bundle bundle = getArguments();
        if (bundle != null) {
            mAlbum = bundle.getParcelable(BundleKeyConstants.KEY_ALBUM);
        }

        setTitle(R.string.main_album_rich_intro);
        mRichContent = (LocalTemplateWebView) findViewById(R.id.main_webview_content);
        mRichContent.setVisibility(View.INVISIBLE);
        mRichContent.setOnImageClickListener(this);
        mRichContent.setURLClickListener(mURLClickListener);
        // 支持上下文复制
        mRichContent.enableSelectCopy();
    }

    @Override
    protected void loadData() {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        if (canUpdateUi()) {
            setDataForView();
        }
    }

    @Override
    protected String getPageLogicName() {
        return "albumIntroDetailNew";
    }

    private void setDataForView() {
        if (mAlbum == null) {
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            return;
        }
        setUpRichTextModule(mAlbum);
        onPageLoadingCompleted(LoadCompleteType.OK);
    }

    private void setUpRichTextModule(AlbumM album) {
        if (album != null) {
            AlbumIntroDetailTemplateModel templateModel = new AlbumIntroDetailTemplateModel();
            if (!TextUtils.isEmpty(album.getValidCover())) {
                templateModel.setCover(album.getValidCover());
            } else {
                templateModel.setCover(DEFAULT_ALBUM_COVER_URL);
            }
            templateModel.setIntro(album.getIntroRich());
            templateModel.setOutline(album.getOutline());
            templateModel.setSalePoint(album.getSalePointPopup());
            templateModel.setTitle(album.getAlbumTitle());
            new AsyncGson<String>().toJson(templateModel, new AsyncGson.IResult<String>() {
                @Override
                public void postResult(String result) {
                    String richTemplate = FileUtil.readAssetFileData(mContext, "albumDetailTemplate/index.html");
                    if (richTemplate.contains("var tplData")) {
                        richTemplate = richTemplate.replace("var tplData =", "var tplData = " + result);
                    }
                    if (canUpdateUi()) {
                        if (mRichContent != null) {
                            mRichContent.setData(richTemplate);
                            mRichContent.setVisibility(View.VISIBLE);
                        }
                    }
                }

                @Override
                public void postException(Exception e) {

                }
            });
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        if (mRichContent != null) {
            ViewGroup parent = (ViewGroup) mRichContent.getParent();
            if (null != parent) {
                parent.removeView(mRichContent);
            }
            mRichContent.destroy();
            mRichContent = null;
        }
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        if (mRichContent != null) {
            mRichContent.onResume();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if (mRichContent != null) {
            mRichContent.onPause();
        }
    }


    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_album_intro_new_v2;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    private RichWebView.URLClickListener mURLClickListener = new RichWebView.URLClickListener() {
        @Override
        public boolean urlClick(String url) {
            //极速版禁止专辑详情跳转
//            ToolUtil.recognizeItingUrl(AlbumIntroDetailFragmentNew.this, url);
            return true;
        }
    };
}
