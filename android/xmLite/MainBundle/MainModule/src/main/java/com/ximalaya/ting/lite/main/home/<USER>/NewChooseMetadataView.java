package com.ximalaya.ting.lite.main.home.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.HorizontalScrollViewInSlideView;
import com.ximalaya.ting.android.host.model.category.CategoryMetadata;
import com.ximalaya.ting.android.host.model.category.CategoryMetadataValue;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.constant.DPConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by dumingwei on 2021/1/12
 * <p>
 * Desc:
 */

public class NewChooseMetadataView extends LinearLayout implements View.OnClickListener {

    private static final String TAG = "NewChooseMetadataView";

    public static final String CAL_DIMEN_HOT = "hot";
    public static final String CAL_DIMEN_RECENT = "recent";
    public static final String CAL_DIMEN_CLASSIC = "classic";
    public static final String CAL_DIMEN_DEFAULT = CAL_DIMEN_HOT;

    public static final int FROM_CATEGORY = 1;
    public static final int FROM_KEYWORD = 2;
    public static final String DEFAULT_SORT = "综合排序";

    private int mFrom = FROM_CATEGORY;

    private String mCategoryId;
    private String mKeywordId;

    /**
     * 综合排序
     */
    private LinearLayout llSort;
    private int dp2;
    private int dp4;
    private int dp6;
    private int dp7;
    private int dp8;
    private int dp10;
    private int dp16;

    //用来标记综合排序一栏的topMargin应该是dp8还是dp16
    private int mLastHorizontalScrollViewBottomPadding;

    public void setCategoryId(String categoryId) {
        this.mCategoryId = categoryId;
    }

    public void setFrom(int from) {
        mFrom = from;
    }

    public void setKeywordId(String keywordId) {
        mKeywordId = keywordId;
    }

    private List<CategoryMetadata> mMetadata;
    private View mSlideView;
    private final List<String> mMetadataDisplayNameList = new ArrayList<>();
    private String mCalDimension = CAL_DIMEN_DEFAULT;
    private String mMetadataHttpRequestParam;
    private List<OnMetadataChangeListener> mMetadataChangeListeners = new ArrayList<>();

    private String chooseMetaData;

    private int selectedColor;
    private int secondLevelBgColor;
    private int unSelectedColor;

    public NewChooseMetadataView(Context context) {
        super(context);
        init();
    }

    public NewChooseMetadataView(Context context, String calDimension, String metadata) {
        super(context);
        mCalDimension = calDimension;
        chooseMetaData = metadata;

        init();
    }

    private void setCategoryMetaDataValue(List<CategoryMetadata> object, String chooseMetadata) {
        if (!TextUtils.isEmpty(chooseMetadata)) {
            if (!TextUtils.isEmpty(chooseMetadata)) {
                String[] selectedMetaDataStrArray = chooseMetadata.split(":");
                if (selectedMetaDataStrArray.length >= 2) {
                    int selectedMetadataId = Integer.parseInt(selectedMetaDataStrArray[0]);
                    int selectedMetadataValueId = Integer.parseInt(selectedMetaDataStrArray[1]);
                    for (CategoryMetadata categoryMetadata : object) {
                        if (categoryMetadata.getId() == selectedMetadataId) {
                            if (categoryMetadata.getMetadataValues() != null) {
                                for (CategoryMetadataValue categoryMetadataValue : categoryMetadata.getMetadataValues()) {
                                    if (categoryMetadataValue.getId() == selectedMetadataValueId) {
                                        categoryMetadata.setChosed(false);
                                        categoryMetadataValue.setChosed(true);
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    public NewChooseMetadataView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public NewChooseMetadataView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public void setSlideView(View slideView) {
        mSlideView = slideView;
    }

    private void init() {
        dp2 = DPConstants.getInstance(getContext()).DP_2;
        dp4 = DPConstants.getInstance(getContext()).DP_4;
        dp6 = DPConstants.getInstance(getContext()).DP_6;
        dp7 = DPConstants.getInstance(getContext()).DP_7;
        dp8 = DPConstants.getInstance(getContext()).DP_8;
        dp10 = DPConstants.getInstance(getContext()).DP_10;
        dp16 = DPConstants.getInstance(getContext()).DP_16;

        selectedColor = getResources().getColor(R.color.main_color_e83f46);
        secondLevelBgColor = getResources().getColor(R.color.main_color_f3f4f5);
        unSelectedColor = getResources().getColor(R.color.main_color_999999);

        setOrientation(LinearLayout.VERTICAL);

        setFocusable(false);
        setFocusableInTouchMode(false);
    }

    public void setMetadata(List<CategoryMetadata> metadata) {
        setMetadata(metadata, -1, -1);
    }

    /**
     * 递归查找出指定标签所在的节点
     */
    private CategoryMetadataValue getSelectMetadataOrMetadataValue(List<CategoryMetadata> metadata, int metadataValueId) {
        CategoryMetadataValue result = null;
        for (CategoryMetadata categoryMetadata : metadata) {
            List<CategoryMetadataValue> metadataValues = categoryMetadata.getMetadataValues();
            if (!ToolUtil.isEmptyCollects(metadataValues)) {
                for (CategoryMetadataValue categoryMetadataValue : metadataValues) {
                    if (categoryMetadataValue.getId() == metadataValueId) {
                        return categoryMetadataValue;
                    } else if (!ToolUtil.isEmptyCollects(categoryMetadataValue.getMetadatas())) {
                        result = getSelectMetadataOrMetadataValue(categoryMetadataValue.getMetadatas(), metadataValueId);
                        if (result != null) {
                            return result;
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 由指定节点递归向上回溯父节点，改变状态
     */
    private void requestUpdateMetadataValue(CategoryMetadataValue selectedMetadataValue) {
        updateSelectedMetadata(selectedMetadataValue);
        CategoryMetadata categoryMetadata = selectedMetadataValue.getParentMetadata();
        if (categoryMetadata != null) {
            CategoryMetadataValue categoryMetadataValue = categoryMetadata.getParentMetadataValue();
            if (categoryMetadataValue != null) {
                requestUpdateMetadataValue(categoryMetadataValue);
            }
        }
    }

    public void setMetadata(List<CategoryMetadata> metadata, int metadataId, int metadataValueId) {
        // 跳转过来有指定某一个标签（如专辑页标签跳转）
        if (metadataValueId != -1 && !ToolUtil.isEmptyCollects(metadata)) {
            CategoryMetadataValue selectedMetadataValue = getSelectMetadataOrMetadataValue(metadata, metadataValueId);
            if (null != selectedMetadataValue) {
                requestUpdateMetadataValue(selectedMetadataValue);
            }
        }

        mMetadata = metadata;

        inflateFilterPanel(this, metadata);

        parseMetaParams();

        notifyMetadataChangeListener();

    }

    private Map<CategoryMetadata, HorizontalScrollView> mHorizontalScrollViews = new HashMap<>();

    @NonNull
    private HorizontalScrollView getChild(CategoryMetadata categoryMetadata) {
        HorizontalScrollView scrollView = mHorizontalScrollViews.get(categoryMetadata);
        if (scrollView == null) {
            scrollView = new HorizontalScrollViewInSlideView(getContext());

            scrollView.setPadding(dp7, 0, 0, 0);

            if (categoryMetadata.getLevel() == 2) {
                scrollView.setBackgroundColor(secondLevelBgColor);
            }
            if (categoryMetadata.getLevel() >= 2) {
                scrollView.setPadding(dp7, dp8, 0, dp8);
            }

            if (mSlideView != null) {
                ((HorizontalScrollViewInSlideView) scrollView).
                        setDisallowInterceptTouchEventView((ViewGroup) mSlideView);
            }

            scrollView.setTag(categoryMetadata);
            scrollView.requestDisallowInterceptTouchEvent(true);
            scrollView.setHorizontalScrollBarEnabled(false);

            LinearLayout layout = new LinearLayout(getContext());
            LayoutParams lp = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layout.setLayoutParams(lp);
            scrollView.addView(layout);

            mHorizontalScrollViews.put(categoryMetadata, scrollView);
        }

        return scrollView;
    }

    private void doRecursiveInflate(ViewGroup viewGroup, List<CategoryMetadata> metadata) {
        if (metadata != null && metadata.size() > 0) {
            for (CategoryMetadata categoryMetadata : metadata) {
                Logger.i(TAG, "Metadata: " + categoryMetadata.getDisplayName() + " level " + categoryMetadata.getLevel());
                final HorizontalScrollView scrollView = getChild(categoryMetadata);
                LinearLayout.LayoutParams layoutParams = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
                if (categoryMetadata.getLevel() == 1) {
                    layoutParams.topMargin = DPConstants.getInstance(getContext()).DP_16;
                } else if (categoryMetadata.getLevel() == 2) {
                    layoutParams.topMargin = 0;
                } else if (categoryMetadata.getLevel() == 3) {
                    layoutParams.topMargin = dp8;
                } else {
                    layoutParams.topMargin = 0;
                }

                mLastHorizontalScrollViewBottomPadding = scrollView.getPaddingBottom();

                //第2级的时候特殊处理
                if (categoryMetadata.getLevel() == 2) {
                    mLastHorizontalScrollViewBottomPadding = 0;
                }

                Logger.i(TAG, "mLastHorizontalScrollViewBottomPadding = " + mLastHorizontalScrollViewBottomPadding);
                viewGroup.addView(scrollView, layoutParams);
                AutoTraceHelper.setLabelForCTWithMultiSameSubView(viewGroup);
                LinearLayout layout = (LinearLayout) scrollView.getChildAt(0);
                AutoTraceHelper.setLabelForCTWithMultiSameSubView(layout);


                LinearLayout.LayoutParams metaDataViewlayoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                int index = 0;
                View metaDataView;
                if (layout.getChildAt(index) == null) {
                    metaDataView = getView(categoryMetadata, index);
                    if (metaDataView instanceof TextView) {
                        if (categoryMetadata.getLevel() == 2) {
                            metaDataViewlayoutParams.leftMargin = 0;
                            metaDataViewlayoutParams.rightMargin = 0;
                        } else {
                            metaDataViewlayoutParams.leftMargin = dp2;
                            metaDataViewlayoutParams.rightMargin = dp2;
                        }
                    } else {
                        metaDataViewlayoutParams.leftMargin = 0;
                        metaDataViewlayoutParams.rightMargin = 0;
                    }
                    layout.addView(metaDataView, metaDataViewlayoutParams);
                } else {
                    metaDataView = layout.getChildAt(index);
                    if (metaDataView instanceof TextView) {
                        TextView textView = (TextView) metaDataView;
                        LinearLayout.LayoutParams textViewLayoutParams = ((LayoutParams) metaDataView.getLayoutParams());
                        if (categoryMetadata.isChosed()) {
                            setTextViewBold(textView, true);
                            textView.setTextColor(selectedColor);
                            textView.setOnClickListener(null);
                            if (categoryMetadata.getLevel() == 2) {
                                textViewLayoutParams.leftMargin = 0;
                                textViewLayoutParams.rightMargin = 0;
                                textView.setBackground(null);
                            } else {
                                textViewLayoutParams.leftMargin = dp2;
                                textViewLayoutParams.rightMargin = dp2;
                                textView.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12);
                            }
                        } else {
                            if (categoryMetadata.getLevel() == 2) {
                                textViewLayoutParams.leftMargin = 0;
                                textViewLayoutParams.rightMargin = 0;
                            } else {
                                textViewLayoutParams.leftMargin = dp2;
                                textViewLayoutParams.rightMargin = dp2;
                            }
                            setTextViewBold(textView, false);
                            textView.setTextColor(unSelectedColor);
                            textView.setBackground(null);
                            textView.setOnClickListener(this);
                            Map<String, Object> bindData = new HashMap<>();
                            bindData.put("rowID", categoryMetadata.getId());
                            bindData.put("displayName", categoryMetadata.getDisplayName());
                            AutoTraceHelper.bindData(metaDataView, AutoTraceHelper.MODULE_DEFAULT, bindData);
                        }

                        textView.setLayoutParams(textViewLayoutParams);

                    } else if (metaDataView instanceof FirstLevelMetaDataView) {
                        FirstLevelMetaDataView firstLevelMetaDataView = ((FirstLevelMetaDataView) metaDataView);
                        if (categoryMetadata.isChosed()) {
                            if (index == 0) {
                                firstLevelMetaDataView.setChosen(true, false);
                            } else {
                                firstLevelMetaDataView.setChosen(true);
                            }
                            firstLevelMetaDataView.setOnClickListener(null);
                        } else {
                            firstLevelMetaDataView.setChosen(false);
                            firstLevelMetaDataView.setOnClickListener(this);
                            Map<String, Object> bindData = new HashMap<>();
                            bindData.put("rowID", categoryMetadata.getId());
                            bindData.put("displayName", categoryMetadata.getDisplayName());
                            AutoTraceHelper.bindData(metaDataView, AutoTraceHelper.MODULE_DEFAULT, bindData);
                        }
                    }
                }

                LayoutParams childPs = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);

                //添加MetaValueView
                for (CategoryMetadataValue value : categoryMetadata.getMetadataValues()) {
                    Logger.i(TAG, "MetaValue: " + value.getDisplayName() + " level " + value.getLevel());

                    index++;
                    final View view;
                    if (index < layout.getChildCount() && layout.getChildAt(index) != null) {
                        view = layout.getChildAt(index);
                        if (view instanceof TextView) {
                            TextView textView = ((TextView) view);
                            LinearLayout.LayoutParams layoutParams1 = ((LayoutParams) textView.getLayoutParams());
                            if (value.isChosed()) {
                                setTextViewBold(textView, true);
                                textView.setTextColor(selectedColor);
                                textView.setOnClickListener(null);
                                if (value.getLevel() == 2) {
                                    textView.setBackground(null);
                                    layoutParams1.leftMargin = 0;
                                    layoutParams1.rightMargin = 0;
                                } else {
                                    layoutParams1.leftMargin = dp2;
                                    layoutParams1.rightMargin = dp2;
                                    textView.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12);
                                }
                            } else {
                                if (categoryMetadata.getLevel() == 2) {
                                    layoutParams1.leftMargin = 0;
                                    layoutParams1.rightMargin = 0;
                                } else {
                                    layoutParams1.leftMargin = dp2;
                                    layoutParams1.rightMargin = dp2;
                                }
                                setTextViewBold(textView, false);
                                textView.setTextColor(unSelectedColor);
                                textView.setBackground(null);
                                textView.setOnClickListener(this);
                                Map<String, Object> bindData = new HashMap<>();
                                bindData.put("rowID", categoryMetadata.getId());
                                bindData.put("columnID", value.getId());
                                bindData.put("displayName", value.getDisplayName());
                                AutoTraceHelper.bindData(metaDataView, AutoTraceHelper.MODULE_DEFAULT, bindData);
                            }
                            //重新设置布局参数，改变margin
                            textView.setLayoutParams(layoutParams1);

                        } else if (view instanceof FirstLevelMetaDataView) {
                            Logger.i(TAG, "MetaValue: " + value.getDisplayName() + " view instanceof FirstLevelMetaDataView");

                            FirstLevelMetaDataView valueMetaDataView = ((FirstLevelMetaDataView) view);
                            if (value.isChosed()) {
                                if (CollectionUtil.isNullOrEmpty(value.getMetadatas())) {
                                    valueMetaDataView.setChosen(true, false);
                                } else {
                                    valueMetaDataView.setChosen(true);
                                }
                                valueMetaDataView.setOnClickListener(null);
                            } else {
                                valueMetaDataView.setChosen(false);
                                valueMetaDataView.setOnClickListener(this);
                                Map<String, Object> bindData = new HashMap<>();
                                bindData.put("rowID", categoryMetadata.getId());
                                bindData.put("columnID", value.getId());
                                bindData.put("displayName", value.getDisplayName());
                                AutoTraceHelper.bindData(metaDataView, AutoTraceHelper.MODULE_DEFAULT, bindData);
                            }
                        }
                    } else {
                        view = getView(value, categoryMetadata);
                        if (value.getLevel() == 2 || view instanceof FirstLevelMetaDataView) {
                            childPs.leftMargin = 0;
                            childPs.rightMargin = 0;
                        } else {
                            childPs.leftMargin = dp2;
                            childPs.rightMargin = dp2;
                        }
                        layout.addView(view, childPs);
                    }
                    if (value.isChosed()) {
                        //搜索直达中需要HorizontalScrollView直接跳到被选中的TextView上
                        scrollToLocation(scrollView, view);
                        scrollView.addOnLayoutChangeListener(new OnLayoutChangeListener() {
                            @Override
                            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                                removeOnLayoutChangeListener(this);
                                if (view != null && view.getTag() != null) {
                                    Object tag = view.getTag();
                                    if (tag instanceof CategoryMetadata) {
                                        if (((CategoryMetadata) tag).isChosed()) {
                                            scrollToLocation(scrollView, view);
                                        }
                                    } else if (tag instanceof CategoryMetadataValue) {
                                        if (((CategoryMetadataValue) tag).isChosed()) {
                                            scrollToLocation(scrollView, view);
                                        }
                                    }
                                }
                            }
                        });
                    }
                }

                //添加次级数据
                if (!categoryMetadata.isChosed() && categoryMetadata.getMetadataValues() != null) {
                    Logger.i(TAG, categoryMetadata.getDisplayName() + " !categoryMetadata.isChosed() && categoryMetadata.getMetadataValues() != null");
                    for (CategoryMetadataValue value : categoryMetadata
                            .getMetadataValues()) {
                        if (value.isChosed()) {
                            doRecursiveInflate(viewGroup, value.getMetadatas());
                        }
                    }
                }
            }
        }
    }

    private void scrollToLocation(final HorizontalScrollView scrollView, final View view) {
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        if (location[0] > BaseUtil.getScreenWidth(getContext())) {
            scrollView.scrollTo(location[0] - view.getWidth() / 2, location[1]);
        }
    }

    private View getView(CategoryMetadata data, int index) {
        if (data.getLevel() == 1) {
            return getFirstLevelView(data, index);
        }
        TextView tv = new TextView(getContext());

        tv.setText(data.getDisplayName());
        tv.setTextSize(16);

        tv.setPadding(dp10, dp2, dp10, dp2);

       /* if (data.getLevel() == 2) {
            tv.setPadding(dp10, 0, dp10, 0);
        } else {
            tv.setPadding(dp10, 0, dp10, 0);
        }*/

        if (data.isChosed()) {
            //tv.setPadding(dp10, dp2, dp10, dp2);
            setTextViewBold(tv, true);
            tv.setTextColor(selectedColor);
            tv.setOnClickListener(null);
            if (data.getLevel() == 2) {
                tv.setBackground(null);
            } else {
                tv.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12);
            }
        } else {
            setTextViewBold(tv, false);
            tv.setTextColor(unSelectedColor);
            tv.setBackground(null);
            tv.setOnClickListener(this);
            Map<String, Object> bindData = new HashMap<>();
            bindData.put("rowID", data.getId());
            bindData.put("displayName", data.getDisplayName());
            AutoTraceHelper.bindData(tv, AutoTraceHelper.MODULE_DEFAULT, bindData);
        }
        tv.setTag(data);

        return tv;
    }

    private View getFirstLevelView(CategoryMetadata data, int index) {
        FirstLevelMetaDataView firstLevelMetaDataView = new FirstLevelMetaDataView(getContext());
        String displayName = data.getDisplayName();
        firstLevelMetaDataView.setText(displayName);
        firstLevelMetaDataView.setPadding(dp4, 0, dp4, 0);
        if (data.isChosed()) {
            if (index == 0) {
                firstLevelMetaDataView.setChosen(true, false);
            } else {
                firstLevelMetaDataView.setChosen(true);
            }
            firstLevelMetaDataView.setOnClickListener(null);
        } else {
            firstLevelMetaDataView.setChosen(false);
            firstLevelMetaDataView.setOnClickListener(this);
            Map<String, Object> bindData = new HashMap<>();
            bindData.put("rowID", data.getId());
            bindData.put("displayName", data.getDisplayName());
            AutoTraceHelper.bindData(firstLevelMetaDataView, AutoTraceHelper.MODULE_DEFAULT, bindData);
        }
        firstLevelMetaDataView.setTag(data);

        return firstLevelMetaDataView;
    }

    private View getView(CategoryMetadataValue value, CategoryMetadata categoryMetadata) {
        if (value.getLevel() == 1) {
            return getFirstLevelView(value, categoryMetadata);
        }
        TextView tv = new TextView(getContext());
        tv.setText(value.getDisplayName());
        tv.setTextSize(16);
        tv.setPadding(dp10, dp2, dp10, dp2);

        /*if (value.getLevel() == 2) {
            tv.setPadding(dp10, 0, dp10, 0);
        } else {
            tv.setPadding(dp10, 0, dp10, 0);
        }*/
        if (value.isChosed()) {
            setTextViewBold(tv, true);
            tv.setTextColor(selectedColor);
            tv.setOnClickListener(null);
            if (value.getLevel() == 2) {
                tv.setBackground(null);
            } else {
                tv.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12);
            }
        } else {
            setTextViewBold(tv, false);
            tv.setTextColor(unSelectedColor);
            tv.setBackground(null);
            tv.setOnClickListener(this);
            Map<String, Object> bindData = new HashMap<>();
            bindData.put("rowID", categoryMetadata.getId());
            bindData.put("columnID", value.getId());
            bindData.put("displayName", value.getDisplayName());
            AutoTraceHelper.bindData(tv, AutoTraceHelper.MODULE_DEFAULT, bindData);
        }
        tv.setTag(value);

        return tv;
    }

    private View getFirstLevelView(CategoryMetadataValue value, CategoryMetadata categoryMetadata) {
        FirstLevelMetaDataView firstLevelMetaDataView = new FirstLevelMetaDataView(getContext());
        String displayName = value.getDisplayName();
        firstLevelMetaDataView.setText(displayName);
        firstLevelMetaDataView.setPadding(dp4, 0, dp4, 0);
        if (value.isChosed()) {
            if (CollectionUtil.isNullOrEmpty(value.getMetadatas())) {
                firstLevelMetaDataView.setChosen(true, false);
            } else {
                firstLevelMetaDataView.setChosen(true);
            }
            firstLevelMetaDataView.setOnClickListener(null);
        } else {
            firstLevelMetaDataView.setChosen(false);
            firstLevelMetaDataView.setOnClickListener(this);
            Map<String, Object> bindData = new HashMap<>();
            bindData.put("rowID", categoryMetadata.getId());
            bindData.put("columnID", value.getId());
            bindData.put("displayName", value.getDisplayName());
            AutoTraceHelper.bindData(firstLevelMetaDataView, AutoTraceHelper.MODULE_DEFAULT, bindData);
        }
        firstLevelMetaDataView.setTag(value);
        return firstLevelMetaDataView;
    }


    /**
     * @param selectedItem 被选中的model 可能为CategoryMetadata 或 CategoryMetadataValue
     */
    private void updateSelectedMetadata(Object selectedItem) {
        if (selectedItem instanceof CategoryMetadata) {
            CategoryMetadata metadata = (CategoryMetadata) selectedItem;
            metadata.setChosed(true);

            for (CategoryMetadataValue value : metadata.getMetadataValues()) {
                value.setChosed(false);
            }
        } else if (selectedItem instanceof CategoryMetadataValue) {
            CategoryMetadataValue metadataValue = (CategoryMetadataValue) selectedItem;
            metadataValue.getParentMetadata().setChosed(false);

            for (CategoryMetadataValue value : metadataValue.getParentMetadata().getMetadataValues()) {
                if (value.getId() == metadataValue.getId()) {
                    value.setChosed(true);
                } else {
                    value.setChosed(false);
                }
            }
        }
    }

    /**
     * 分类过滤元数据布局初始化
     */
    private void inflateFilterPanel(ViewGroup viewGroup,
                                    List<CategoryMetadata> metadata) {
        viewGroup.removeAllViews();

        doRecursiveInflate(viewGroup, metadata);

        if (mShowDivider) {
            addDivider(viewGroup);
        }

        addCalDimension(viewGroup);

        addBottomDivider(viewGroup);
    }

    private void addDivider(ViewGroup viewGroup) {
        View divider = new View(getContext());
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 1);
        params.bottomMargin = BaseUtil.dp2px(getContext(), 10);
        divider.setLayoutParams(params);
        divider.setBackgroundColor(Color.parseColor("#E8E8E8"));
        viewGroup.addView(divider);
    }

    private View mBottomDivider;
    private boolean mShowBottomDivider = true;
    private boolean mShowDivider = false;

    private void addBottomDivider(ViewGroup viewGroup) {
        mBottomDivider = new View(getContext());
        LayoutParams lp = new LayoutParams(
                AbsListView.LayoutParams.MATCH_PARENT, BaseUtil.dp2px(
                getContext(), 1));
        lp.bottomMargin = BaseUtil.dp2px(getContext(), 10);
        lp.topMargin = BaseUtil.dp2px(getContext(), 10);
        mBottomDivider.setLayoutParams(lp);
        mBottomDivider.setBackgroundColor(Color.parseColor("#f3f4f5"));
        viewGroup.addView(mBottomDivider);
    }

    public void showBottomDivider(boolean show) {
        mShowBottomDivider = show;
        if (mBottomDivider == null) {
            return;
        }
        if (show) {
            mBottomDivider.setVisibility(VISIBLE);
        } else {
            mBottomDivider.setVisibility(INVISIBLE);
        }
    }

    private void addCalDimension(ViewGroup viewGroup) {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                LayoutParams.WRAP_CONTENT);
        params.leftMargin = dp7;
        if (mLastHorizontalScrollViewBottomPadding == 0) {
            params.topMargin = dp16;
        } else {
            params.topMargin = dp8;
        }
        params.bottomMargin = dp6;

        llSort = new LinearLayout(getContext());
        llSort.setGravity(Gravity.CENTER_VERTICAL);

        llSort.setLayoutParams(params);
        String[] calDimensions = {CAL_DIMEN_HOT,
                CAL_DIMEN_CLASSIC,
                CAL_DIMEN_RECENT};

        LinearLayout.LayoutParams childParams = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
                LayoutParams.WRAP_CONTENT);

        childParams.leftMargin = dp4;
        childParams.rightMargin = dp4;
        for (final String str : calDimensions) {
            final String displayName = getCalDimenDisplayName(str);
            TextView subTv = new TextView(getContext());
            subTv.setText(displayName);
            subTv.setTextSize(16);
            subTv.setPadding(dp10, dp2, dp10, dp2);

            if (getCalDimenDisplayName(mCalDimension).equals(displayName)) {
                setTextViewBold(subTv, true);
                subTv.setTextColor(selectedColor);
                subTv.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12);
            } else {
                setTextViewBold(subTv, false);
                subTv.setTextColor(unSelectedColor);
                subTv.setBackground(null);
                subTv.setOnClickListener(new OnClickListener() {

                    @Override
                    public void onClick(View v) {
                        UserTracking userTracking = new UserTracking();
                        String srcPage = "";
                        if (mFrom == FROM_KEYWORD) {
                            srcPage = "hotword";
                            userTracking.setSrcPageId(mKeywordId);
                        } else {
                            srcPage = "全部分类页";
                        }

                        userTracking.setSrcPage(srcPage).
                                setSrcModule("排序").
                                setItem("button").
                                setItemId(displayName).
                                setCategory(mCategoryId).
                                statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_CATEGORY_PAGE_CLICK);

                        if (!NetworkUtils.isNetworkAvaliable(getContext().getApplicationContext())) {
                            CustomToast.showFailToast(R.string.main_network_exeption_toast);
                            return;
                        }

                        mCalDimension = str;

                        //移除掉综合排序一栏
                        removeView(llSort);
                        addCalDimension(NewChooseMetadataView.this);

                        //重新移除和添加底部分割线，否则综合排序会添加到mBottomDivider下面
                        removeView(mBottomDivider);
                        addBottomDivider(NewChooseMetadataView.this);

                        notifyMetadataChangeListener();
                    }
                });
                AutoTraceHelper.setLabelForCTWithMultiSameSubView(llSort);
                AutoTraceHelper.bindData(subTv, AutoTraceHelper.MODULE_DEFAULT, "");
            }
            llSort.addView(subTv, childParams);
        }
        viewGroup.addView(llSort);
    }

    @Override
    public void onClick(View v) {
        if (!NetworkUtils.isNetworkAvaliable(getContext().getApplicationContext())) {
            CustomToast.showFailToast(R.string.main_network_exeption_toast);
            return;
        }

        v.setClickable(false);
        Object object = v.getTag();

        List<CategoryMetadata> metadata = mMetadata;
        updateSelectedMetadata(object);

        inflateFilterPanel(this, metadata);

        parseMetaParams();

        String srcPage = "";
        UserTracking userTracking = new UserTracking();
        if (mFrom == FROM_CATEGORY) {
            srcPage = "全部分类页";
        } else {
            srcPage = "hotword";
            userTracking.setSrcPageId(mKeywordId);
        }
        userTracking.setSrcPage(srcPage)
                .setSrcModule("类目搜索")
                .setCategory(mCategoryId).
                setMetaData(mMetadataHttpRequestParam)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_CATEGORY_PAGE_CLICK);
    }

    private void parseMetaParams() {
        mMetadataDisplayNameList.clear();

        StringBuilder outSb = new StringBuilder();
        recursiveParseMetaParams(mMetadata, outSb, mMetadataDisplayNameList);

        if (outSb.length() > 0) {
            mMetadataHttpRequestParam = outSb.substring(0, outSb.length() - 1);  //去除最后的"," 号
        } else {
            mMetadataHttpRequestParam = "";
        }

        notifyMetadataChangeListener();
    }

    /**
     * 解析已选的标签元数据
     */
    private void recursiveParseMetaParams(List<CategoryMetadata> metadata,
                                          StringBuilder httpRequestParam,
                                          List<String> displayNameList) {
        if (metadata == null || httpRequestParam == null || displayNameList == null) {
            return;
        }

        for (CategoryMetadata categoryMetadata : metadata) {
            for (CategoryMetadataValue value : categoryMetadata.getMetadataValues()) {
                if (value.isChosed() && value.getParentMetadata().getId() != 0) {//只筛选单行子标签的数据
                    httpRequestParam.append(value.getParentMetadata().getId());
                    httpRequestParam.append(":");
                    httpRequestParam.append(value.getId());
                    httpRequestParam.append(",");
                    displayNameList.add(value.getDisplayName());
                }
            }

            //操作次级数据
            if (!categoryMetadata.isChosed() && categoryMetadata.getMetadataValues() != null) {
                for (CategoryMetadataValue value : categoryMetadata.getMetadataValues()) {
                    if (value.isChosed()) {
                        recursiveParseMetaParams(value.getMetadatas(), httpRequestParam, displayNameList);
                    }
                }
            }
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {  // 限制该view 的高度最高为屏幕的2/3
        int originHeight = MeasureSpec.getSize(heightMeasureSpec);
        int maxHeight = BaseUtil.getScreenHeight(getContext()) / 3 * 2;
        if (maxHeight < originHeight) {
            heightMeasureSpec = MeasureSpec.makeMeasureSpec(maxHeight, MeasureSpec.AT_MOST);
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    public interface OnMetadataChangeListener {
        /**
         * @param calDimension         筛选维度
         * @param metadataRequestParam 用于筛选专辑的元数据请求参数
         * @param hintStr              展示给用户看的，当前已选的筛选元数据
         */
        void onMetadataChange(String calDimension, String metadataRequestParam, String hintStr);
    }

    public void addMetadataChangeListener(OnMetadataChangeListener listener) {
        if (listener != null) {
            mMetadataChangeListeners.add(listener);
        }
    }

    public void removeMetadataChangeListener(OnMetadataChangeListener listener) {
        if (listener != null) {
            mMetadataChangeListeners.remove(listener);
        }
    }

    private void notifyMetadataChangeListener() {
        String hintStr = getHintStr(mMetadataDisplayNameList);
        for (OnMetadataChangeListener listener : mMetadataChangeListeners) {
            listener.onMetadataChange(mCalDimension, mMetadataHttpRequestParam, hintStr);
        }
    }

    private String getHintStr(List<String> displayNames) {
        StringBuilder sb = new StringBuilder();
        int size = displayNames.size();
        for (int i = 0; i < size; i++) {
            sb.append(displayNames.get(i));
            if (i < size - 1) {
                sb.append(" · ");
            }
        }
        String hintString = sb.toString();

        String sortString = getCalDimenDisplayName(mCalDimension);

        if (sortString == null) {
            sortString = DEFAULT_SORT;
        }

        if (TextUtils.isEmpty(hintString)) {
            return sortString;
        }

        sb = new StringBuilder(hintString);
        sb.append(" · ");
        sb.append(sortString);

        return sb.toString();
    }


    private static final Map<String, String> CAL_DIMEN_DISPLAY_NAMES = new HashMap<String, String>() {
        {
            put(CAL_DIMEN_CLASSIC, "播放最多");
            put(CAL_DIMEN_HOT, "综合排序");
            put(CAL_DIMEN_RECENT, "最近更新");
        }
    };

    public static String getCalDimenDisplayName(String calDimen) {
        return CAL_DIMEN_DISPLAY_NAMES.get(calDimen);
    }

    public void setTextViewBold(TextView textView, boolean isBold) {
        if (textView != null) {
            if (isBold) {
                String familyName = "sans-serif-light";
                Typeface boldTypeface = Typeface.create(familyName, Typeface.BOLD);
                textView.setTypeface(boldTypeface);
            } else {
                textView.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            }
        }
    }
}
