package com.ximalaya.ting.lite.main.download;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.astuetz.PagerSlidingTabStrip;
import com.ximalaya.ting.android.downloadservice.base.BaseDownloadTask;
import com.ximalaya.ting.android.downloadservice.base.IDownloadTaskCallback;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.utils.ShareUtilsInMain;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR> on 17/8/22.
 * <p>
 * 声音排序逻辑：
 * 1.若用户没有手动排过序，则排序以orderNum字段为准
 * 2.若用户手动拍过序，则排序以orderPositionInAlbum为准，手动排序之后新下载的声音按时间排序
 * <p>
 * NOTICE:该页面在无网络时也要能正常显示用户下载的声音
 */
public class DownloadedAlbumDetailFragment extends BaseFragment2 implements
        View.OnClickListener, ViewPager.OnPageChangeListener, IDownloadTaskCallback {
    private long mAlbumId;
    private boolean mIsPaid;

    private boolean mIsLoading = false;

    private boolean mIsFirstOnResume = true;

    private ImageView mIvAlbumCover;
    private TextView mTvTitle;
    private TextView mTvSubTitle;
    private TextView mTvTrackCount;
    private TextView mTvUpdateTime;
    private TextView mTVDownloadMore;
    private View mDivider2;
    private View mDivider1;
    private TextView mTvDownloadTrackCount;

    @Nullable
    private AlbumM mAlbum;  //服务端获取的album model

    @Nullable
    private SubordinatedAlbum mSubordinatedAlbum; // 本地数据库中的album model

    private PagerSlidingTabStrip mPstsTabs;

    private ViewPager mVpContent;

    private DownloadAlbumDetailTabAdapter mAdapter;

    private boolean mPlayFirst;


    public DownloadedAlbumDetailFragment() {
        super(true, null);
    }


    public static DownloadedAlbumDetailFragment newInstance(SubordinatedAlbum album, boolean isPaid, String announcerNickName) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(BundleKeyConstants.KEY_ALBUM, album);
        bundle.putLong(BundleKeyConstants.KEY_ALBUM_ID, album.getAlbumId());
        bundle.putBoolean(BundleKeyConstants.KEY_PAID, isPaid);
        bundle.putString(BundleKeyConstants.KEY_ANCHOR_NICK_NAME, announcerNickName);
        DownloadedAlbumDetailFragment fra = new DownloadedAlbumDetailFragment();
        fra.setArguments(bundle);
        return fra;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_top_layout;
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Bundle args = getArguments();
        if (args != null) {
            mSubordinatedAlbum = args.getParcelable(BundleKeyConstants.KEY_ALBUM);
            mAlbumId = getArguments().getLong(BundleKeyConstants.KEY_ALBUM_ID);
            mIsPaid = getArguments().getBoolean(BundleKeyConstants.KEY_PAID);
            mPlayFirst = getArguments().getBoolean(BundleKeyConstants.KEY_PLAY_FIRST);
        }

        setTitle("详情");

        String tagShare = "tagShare";
        TitleBar.ActionType shareAction = new TitleBar.ActionType(
                tagShare, TitleBar.RIGHT, 0, R.drawable.host_titlebar_share_selector,
                0, ImageView.class);

        titleBar.addAction(shareAction, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mActivity != null && mAlbum != null) {
                    ShareUtilsInMain.shareAlbum(mActivity, mAlbum, ICustomShareContentType.SHARE_TYPE_ALBUM);
                } else {
                    if (mActivity != null && !NetworkUtils.isNetworkAvaliable(mActivity)) {
                        CustomToast.showFailToast(R.string.main_no_net);
                    }
                }
            }
        });
        titleBar.update();

        mPstsTabs = (PagerSlidingTabStrip) findViewById(R.id.main_psts_tabs);
        mVpContent = (ViewPager) findViewById(R.id.main_vp_content);
        mAdapter = new DownloadAlbumDetailTabAdapter(getChildFragmentManager(), mAlbumId, -1, mPlayFirst);  //TODO by easoll 传有效uid
        mVpContent.setAdapter(mAdapter);
        mPstsTabs.setViewPager(mVpContent);

        mDivider1 = findViewById(R.id.main_divider1);
        mTvDownloadTrackCount = (TextView) findViewById(R.id.main_tv_download_track_count);
        mDivider2 = findViewById(R.id.main_divider2);

        mVpContent.addOnPageChangeListener(this);

        initHeaderView();

    }

    private void initHeaderView() {
        View itemAlbum = findViewById(R.id.main_item_album);
        itemAlbum.setOnClickListener(this);
        mIvAlbumCover = itemAlbum.findViewById(R.id.main_iv_album_cover);
        mTvTitle = itemAlbum.findViewById(R.id.main_tv_album_title);
        mTvSubTitle = itemAlbum.findViewById(R.id.main_tv_album_subtitle);
        mTvTrackCount = itemAlbum.findViewById(R.id.main_tv_track_count);
        mTvUpdateTime = itemAlbum.findViewById(R.id.main_tv_update_time);
        mTVDownloadMore = itemAlbum.findViewById(R.id.main_tv_album_manage);
        mTVDownloadMore.setOnClickListener(this);
        AutoTraceHelper.bindData(mTVDownloadMore, "");
    }

    @Override
    protected void loadData() {
        if (mIsLoading) {
            return;
        }
        mIsLoading = true;


        initOfflineAlbumInfo(mSubordinatedAlbum);

        loadDownloadCount();

        if (NetworkUtils.isNetworkAvaliable(getActivity())) {
            Map<String, String> params = new HashMap<>();
            params.put(HttpParamsConstants.PARAM_ALBUM_ID, mAlbumId + "");
            CommonRequestM.getAlbumSimpleInfo(params, new IDataCallBack<AlbumM>() {
                @Override
                public void onSuccess(@Nullable AlbumM object) {
                    if (!canUpdateUi()) return;

                    mAlbum = object;
                    initAlbumInfo(object);
                }

                @Override
                public void onError(int code, String message) {
                    if (ConstantsOpenSdk.isDebug) {
                        ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
                    }
                }
            }, true);
        }

        mIsLoading = false;
    }

    /**
     * 加载下载数
     */
    private void loadDownloadCount() {
        new LoadDownloadCountTask(this).myexec();
    }

    private static class LoadDownloadCountTask extends MyAsyncTask<Object, Object, Object> {
        private WeakReference<DownloadedAlbumDetailFragment> mRef;
        private int mDownloadTrackCount;
        private int mDownloadVideoCount;

        LoadDownloadCountTask(DownloadedAlbumDetailFragment fragment) {
            mRef = new WeakReference<>(fragment);
        }

        @Override
        protected Object doInBackground(Object... objects) {
            DownloadedAlbumDetailFragment fragment = mRef.get();
            if (fragment == null) {
                return null;
            }

            List<BaseDownloadTask> taskList = RouteServiceUtil.getDownloadService().getFinishedTasks();
            for (BaseDownloadTask task : taskList) {
                if (task.getTrack() != null &&
                        task.getTrack().getAlbum() != null &&
                        task.getTrack().getAlbum().getAlbumId() == fragment.mAlbumId) {  //下载任务属于当前专辑
                    if (task.getDownloadFileType() == BaseDownloadTask.DOWNLOAD_FILE_TYPE_VIDEO) { //视频下载任务
                        mDownloadVideoCount++;
                    } else if (task.getDownloadFileType() == BaseDownloadTask.DOWNLOAD_FILE_TYPE_TRACK) { //音频下载任务
                        mDownloadTrackCount++;
                    }
                }
            }


            return null;
        }

        @Override
        protected void onPostExecute(Object o) {
            DownloadedAlbumDetailFragment fragment = mRef.get();
            if (fragment == null) {
                return;
            }

            fragment.updateDownloadCountView(mDownloadTrackCount, mDownloadVideoCount);
        }
    }

    private void initOfflineAlbumInfo(final @Nullable SubordinatedAlbum album) {
        if (album == null) return;
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                ImageManager.from(getActivity()).displayImage(mIvAlbumCover, album.getValidCover(), R.drawable.host_default_album_145);
                mTvTitle.setText(album.getAlbumTitle());
                mTvUpdateTime.setVisibility(View.INVISIBLE);
                mTvTrackCount.setVisibility(View.INVISIBLE);
            }
        });
    }

    private void initAlbumInfo(final @Nullable AlbumM object) {
        if (object == null) return;
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                ImageManager.from(getActivity()).displayImage(mIvAlbumCover, object.getCoverUrlSmall(), R.drawable.host_default_album_145);

                mTvTitle.setText(ToolUtil.getAlbumTitleWithPicAhead(mContext, object.getAlbumTitle(), object.getSerialState() == 2 ? R.drawable.main_tag_complete_top_new : -1));

                String title = "最新：" + object.getLastUptrackTitle();
                mTvSubTitle.setText(title);

                String updateTime = StringUtil.getFriendlyTimeStr(object.getUpdatedAt()) + "更新";
                mTvUpdateTime.setVisibility(View.VISIBLE);
                mTvUpdateTime.setText(updateTime);

                if (object.getIncludeTrackCount() > 0) {
                    String trackCount = StringUtil.getFriendlyNumStr(object.getIncludeTrackCount()) + "集";
                    mTvTrackCount.setText(trackCount);
                    mTvTrackCount.setVisibility(View.VISIBLE);
                } else {
                    mTvTrackCount.setVisibility(View.INVISIBLE);
                }

                mTVDownloadMore.setVisibility(object.isNoCopyright() ? View.GONE : View.VISIBLE);

            }
        });


    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_downloaded_album_detail;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();

        RouteServiceUtil.getDownloadService().registerDownloadCallback(this);

        if (mIsFirstOnResume) {
            mIsFirstOnResume = false;
        } else {
            loadDownloadCount();  //更新下载数
        }
    }

    @Override
    public void onPause() {
        RouteServiceUtil.getDownloadService().unRegisterDownloadCallback(this);

        super.onPause();
    }

    @Override
    public void onClick(final View v) {
        if (OneClickHelper.getInstance().onClick(v)) {
            int i = v.getId();
            if (i == R.id.main_tv_album_manage) {
                if (UserInfoMannage.hasLogined()) {
                    startFragment(
                            BatchDownloadFragment.newInstance(mIsPaid ? BatchDownloadFragment.ACTION_DOWNLOAD_BUY : BatchDownloadFragment.ACTION_DOWNLOAD,
                                    mAlbumId), v);

                } else {
                    new DialogBuilder(getActivity())
                            .setMessage("批量下载功能仅登录用户才能使用哦！")
                            .setCancelBtn("稍后再说")
                            .setOkBtn("去登录", new DialogBuilder.DialogCallback() {

                                @Override
                                public void onExecute() {
                                    UserInfoMannage.gotoLogin(mContext);
                                }
                            }).showConfirm();
                }

            } else if (i == R.id.main_item_album) {
                //NOTICE: 此处必须传albumId 而不能传album， 因为album内的priceType是错的
                AlbumEventManage.startMatchAlbumFragment(mAlbumId, AlbumEventManage.FROM_UNDEFINED, ConstantsOpenSdk.PLAY_FROM_DOWNLOAD_ALBUM, null, null, -1, getActivity());
            }
        }
    }

    /**
     * @param albumId 专辑id
     * @return 下载的专辑在专辑详情页是否是升序
     */
    public static boolean isAlbumAsc(Context context, long albumId) {
        SharedPreferencesUtil spu = SharedPreferencesUtil
                .getInstance(context);
        int orderType = spu.getInt(
                PreferenceConstantsInHost.TINGMAIN_KEY_DOWNLOAD_ALBUM_SOUNDLIST_ORDER
                        + albumId,
                AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER);

        return orderType == AppConstants.DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER;
    }


    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        if (position == 0) {
            setSlideAble(true);
        } else {
            setSlideAble(false);
        }
    }


    @Override
    public void onPageScrollStateChanged(int state) {

    }

    /**
     * 更新声音下载数和视频下载数
     */
    private void updateDownloadCountView(int trackDownloadCount, int videoDownloadCount) {
        mAdapter.updateDownloadCountView(trackDownloadCount, videoDownloadCount);
        if (videoDownloadCount == 0) {   //动态将adapter 的count 数改为1再notifyDataSetChanged viewpager还是会滑动，所以重新设置adapter
            mVpContent.setAdapter(mAdapter);
        } else {
            mAdapter.notifyDataSetChanged();
        }
        mPstsTabs.notifyDataSetChanged();
        if (videoDownloadCount == 0) {  //隐藏视频tab，及psts view，单独显示声音集数
            mPstsTabs.setVisibility(View.GONE);
            mDivider2.setVisibility(View.GONE);
            mDivider1.setVisibility(View.GONE);

            String downloadTrackCount = String.format(Locale.US, "已下载 %d集", trackDownloadCount);
            mTvDownloadTrackCount.setText(downloadTrackCount);
            mTvDownloadTrackCount.setVisibility(View.VISIBLE);
        } else {
            mPstsTabs.setVisibility(View.VISIBLE);
            mDivider2.setVisibility(View.VISIBLE);
            mDivider1.setVisibility(View.VISIBLE);

            mTvDownloadTrackCount.setVisibility(View.GONE);
        }
    }

    @Override
    public void onDownloadProgress(@Nullable BaseDownloadTask downloadTask) {

    }

    @Override
    public void onCancel(@Nullable BaseDownloadTask downloadTask) {

    }

    @Override
    public void onComplete(@Nullable BaseDownloadTask downloadTask) {
        loadDownloadCount();
    }

    @Override
    public void onUpdateTrack(@Nullable BaseDownloadTask downloadTask) {

    }

    @Override
    public void onStartNewTask(@Nullable BaseDownloadTask downloadTask) {

    }

    @Override
    public void onError(@Nullable BaseDownloadTask downloadTask) {

    }

    @Override
    public void onDelete() {
        loadDownloadCount();
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }
}
