package com.ximalaya.ting.lite.main.album.fragment;

import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewStub;
import android.widget.TextView;

import com.google.gson.Gson;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.ShareResultManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.view.ImageViewer;
import com.ximalaya.ting.android.host.view.other.RichWebView;
import com.ximalaya.ting.android.host.view.richtext.OnImageClickListener;
import com.ximalaya.ting.android.host.view.richtext.OnURLClickListener;
import com.ximalaya.ting.android.host.view.richtext.RichText;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.util.List;

import static com.ximalaya.ting.android.framework.fragment.BaseFragment.LoadCompleteType.OK;

/**
 * 正在播放的音频的详细信息或专辑富文本简介
 *
 * <AUTHOR>
 */
public class AlbumIntroDetailFragment extends BaseFragment2 implements View.OnClickListener {

    RichWebView webView = null;
    private TextView tvIntro;
    private ImageViewer mImageViewer;
    private AlbumM albumData;
    private String content = "暂无简介";
    private TextView mTvTrackTitle;
    private TextView mTvNumTime;

    public AlbumIntroDetailFragment() {
        super(AppConstants.isPageCanSlide, null);
    }

    public static AlbumIntroDetailFragment newInstance(AlbumM data) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(BundleKeyConstants.KEY_ALBUM, data);
        AlbumIntroDetailFragment fra = new AlbumIntroDetailFragment();
        fra.setArguments(bundle);
        return fra;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public boolean isShowTruckFloatPlayBar() {
        return false;
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38536;
        super.onMyResume();

        if (webView != null) {
            webView.onResume();
        }
    }

    @Override
    public void onPause() {
        if (webView != null) {
            webView.onPause();
        }
        super.onPause();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (webView != null) {
            webView.destroy();
            webView = null;
        }
        ShareResultManager.getInstance().clearShareFinishListener();
    }

    @Override
    protected String getPageLogicName() {
        return "albumIntroDetail";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Bundle bundle = getArguments();
        if (bundle != null) {
            albumData = getArguments().getParcelable(BundleKeyConstants.KEY_ALBUM);
        }
        tvIntro = (TextView) findViewById(R.id.main_short_intro);
        mTvTrackTitle = (TextView) findViewById(R.id.main_tv_track_title);
        mTvNumTime = (TextView) findViewById(R.id.main_play_num_and_time);

        if (albumData != null) {
            setTitle(R.string.main_album_rich_intro);
            mTvTrackTitle.setText(albumData.getAlbumTitle());
            mTvNumTime.setText(StringUtil.getFriendlyNumStrAndCheckIsZero(albumData.getPlayCount(), getStringSafe(R.string.main_num_read)) + StringUtil.getFriendlyDataStr(albumData.getCreatedAt()));
            doAfterAnimation(new IHandleOk() {
                @Override
                public void onReady() {
                    if (!canUpdateUi()) return;
                    if (!TextUtils.isEmpty(albumData.getIntroRich())) {
                        setSpannContentToView(tvIntro, albumData.getIntroRich());
                    } else {
                        tvIntro.setText("暂无简介");
                    }
                }
            });

        }
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    private void setDataForView(String object) {
        onPageLoadingCompleted(OK);
        if (!TextUtils.isEmpty(object)) {
            try {
                Gson gson = new Gson();
                Model model = gson.fromJson(object, Model.class);
                if (model != null && model.ret == 0) {
                    if (!TextUtils.isEmpty(model.richIntro)) {
                        setSpannContentToView(tvIntro, model.richIntro);
                    } else {
                        setSpannContentToView(tvIntro, content);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                setSpannContentToView(tvIntro, content);
            }
        } else {
            setSpannContentToView(tvIntro, content);
        }
    }

    private void setSpannContentToView(final TextView mRichTextView, String richStr) {
        try {
            if (webView == null) {
                ViewStub stub = (ViewStub) findViewById(R.id.main_view_stub_webview);
                stub.inflate();
                webView = (RichWebView) findViewById(R.id.main_webview);
                //关闭硬件加速
                //web view关闭硬件加速，fix  https://bugly.qq.com/v2/crash-reporting/crashes/4bcf4b3b7d/355414?pid=1
                //这个错误是由于webview在scrollview里设置成了wrap_content，导致webview不确定大小，而且webview启动了硬件加速，由于硬件加速是有个最小的缓存区域的，最终导致超过了缓存范围。
                webView.setLayerType(View.LAYER_TYPE_NONE, null);
                webView.setVerticalScrollBarEnabled(false);
                webView.setURLClickListener(new RichWebView.URLClickListener() {
                    @Override
                    public boolean urlClick(String url) {
                        //极速版禁止专辑详情跳转
//                        ToolUtil.recognizeItingUrl(AlbumIntroDetailFragment.this, url);
                        return true;
                    }
                });
                webView.setOnImageClickListener(new RichWebView.IOnImageClickListener() {
                    @Override
                    public void onClick(List<ImageViewer.ImageUrl> imgs, int index) {
                        mImageViewer = new ImageViewer(getActivity());
                        mImageViewer.setImageUrls(imgs);
                        mImageViewer.show(index, getView());
                    }
                });
            }
            RichWebView.RichWebViewAttr richWebViewAttr = new RichWebView.RichWebViewAttr();
            webView.setData(richStr, richWebViewAttr);
            tvIntro.setVisibility(View.GONE);
        } catch (Throwable e) {
            e.printStackTrace();
            tvIntro.setVisibility(View.VISIBLE);
            mRichTextView.setTextColor(ContextCompat.getColor(mContext, R.color.main_rich_text_color));
            RichText.from(mContext, richStr)
                    .async(true)
                    .imageClick(new OnImageClickListener() {
                        @Override
                        public void imageClicked(List<String> imageUrls, int position) {
                            mImageViewer = new ImageViewer(getActivity());
                            mImageViewer.setData(imageUrls);
                            mImageViewer.show(position, getView());
                        }
                    }).urlClick(new OnURLClickListener() {
                @Override
                public boolean urlClicked(String url) {
                    if (url == null) {
                        return true;
                    }

                    ToolUtil.clickUrlAction(AlbumIntroDetailFragment.this, url, mRichTextView);

                    return true;
                }
            })
                    .setIsloadSmail(NetworkType.isConnectMOBILE(mContext))
                    .into(mRichTextView);
        }
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_album_intro;
    }

    @Override
    protected boolean onPrepareNoContentView() {
        return false;
    }

    @Override
    protected void onNoContentButtonClick(View view) {

    }

    @Override
    public void onClick(View v) {
    }

    public static class Model {
        public String tags;
        public String richIntro;
        public String lyric;
        public int ret;
        public String images;
        public long trackId;
    }
}
