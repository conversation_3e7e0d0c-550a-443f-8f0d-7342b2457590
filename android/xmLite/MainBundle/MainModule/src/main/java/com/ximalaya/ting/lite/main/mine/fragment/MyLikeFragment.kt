package com.ximalaya.ting.lite.main.mine.fragment

import android.graphics.Typeface
import android.os.Bundle
import android.widget.RadioButton
import android.widget.RadioGroup
import androidx.viewpager.widget.ViewPager
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.truck.mine.MyLikeTrackListFragment
import kotlinx.android.synthetic.main.main_fra_my_like.*

/**
 * Created by zk on 2022/1/27
 * 我的-喜欢：新增Tab页 专辑和短剧
 *
 */
class MyLikeFragment : BaseFragment2() {

    private lateinit var mAdapter: TabCommonAdapter

    private val mFragmentList: MutableList<TabCommonAdapter.FragmentHolder> = ArrayList(2)

    override fun getContainerLayoutId(): Int = R.layout.main_fra_my_like

    override fun getTitleBarResourceId(): Int = R.id.main_title_bar

    override fun getPageLogicName(): String {

        return javaClass.simpleName
    }

    override fun initUi(savedInstanceState: Bundle?) {

        setTitle("我的喜欢")

        mFragmentList.add(
            TabCommonAdapter.FragmentHolder(
                MyLikeTrackListFragment::class.java,
                "播放"
            )
        )
        mAdapter = TabCommonAdapter(childFragmentManager, mFragmentList)
        main_vp_like.offscreenPageLimit = 1
        main_vp_like.adapter = mAdapter

        performClickTab(0)

        initListener()
    }

    override fun loadData() {

    }

    private fun initListener() {

        main_rb_like_album.setOnClickListener {
            if (main_vp_like.currentItem != 0) {
                main_vp_like.currentItem = 0
            }
        }

        main_rg_like.setOnCheckedChangeListener(object : RadioGroup.OnCheckedChangeListener {

            override fun onCheckedChanged(group: RadioGroup?, p1: Int) {
                group?.let {
                    val count = group.childCount
                    for (i in 0 until count) {
                        val view = group.getChildAt(i)
                        if (view is RadioButton) {
                            view.typeface =
                                if (view.isChecked) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
                        }
                    }
                }
            }
        })

        main_vp_like.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {

            }

            override fun onPageSelected(position: Int) {
                if (position == 0) {
                    main_rb_like_album.isChecked = true
                }
                performChildModel(true)
            }

            override fun onPageScrollStateChanged(state: Int) {

            }

        })
    }

    private fun performClickTab(position: Int) {
        if (position == 0) {
            main_rb_like_album.performClick()
        }
    }

    private fun performChildModel(isToast: Boolean): Boolean {
        if (ChildProtectManager.isChildProtectOpen(context)) {
            // 青少年模式不允许打开短剧
            if (main_vp_like != null && main_vp_like.currentItem != 0) {
                if (isToast) {
                    CustomToast.showToast("青少年模式下无法使用该功能")
                }
                main_vp_like.currentItem = 0
            }
            return true
        }
        return false
    }

}