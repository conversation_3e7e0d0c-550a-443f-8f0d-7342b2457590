package com.ximalaya.ting.lite.main.manager;

import android.app.Activity;
import android.app.Application;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.view.View;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.fragment.ManageFragment;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.SmsLoginDialogActivity;
import com.ximalaya.ting.android.host.activity.TruckFriendModeActivity;
import com.ximalaya.ting.android.host.activity.manager.AppModeManager;
import com.ximalaya.ting.android.host.adapter.track.base.AbstractTrackAdapter;
import com.ximalaya.ting.android.host.adsdk.platform.common.modelproxy.AbstractThirdAd;
import com.ximalaya.ting.android.host.adsdk.platform.lite.callback.IXmLiteSchemeOpenListener;
import com.ximalaya.ting.android.host.adsdk.platform.lite.callback.IXmLiteWebTaskRewardVideoListener;
import com.ximalaya.ting.android.host.adsdk.platform.lite.manager.XmLiteOpenThirdAppCallBackManager;
import com.ximalaya.ting.android.host.adsdk.platform.lite.manager.XmLiteWebTaskRewardVideoManager;
import com.ximalaya.ting.android.host.adsdk.platform.lite.model.XmLiteWebTaskAdInfo;
import com.ximalaya.ting.android.host.adsdk.platform.lite.thirdad.XmLiteWebTaskRewardVideoThirdAd;
import com.ximalaya.ting.android.host.business.unlock.model.AlbumPaidUnLockHintInfo;
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment;
import com.ximalaya.ting.android.host.hybrid.utils.Constants;
import com.ximalaya.ting.android.host.listener.IInstallApkCallBack;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.listener.IPlayTimeCallBack;
import com.ximalaya.ting.android.host.listener.IShowDialog;
import com.ximalaya.ting.android.host.listenertask.ReadTimeUtils;
import com.ximalaya.ting.android.host.listenertask.SyncTotalListenTimeManagerForMainProcess;
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdStateManage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IBookAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IHomeDialogManager;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.ILifeCycleAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IPlayletPlayRecordAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IReadTimeAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.ISkitsHistoryAction;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.model.AlbumTypeModel;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.BannerModel;
import com.ximalaya.ting.android.host.model.ad.OneKeyTingBannerModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.EbookInfo;
import com.ximalaya.ting.android.host.model.homepage.HomepageM;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.push.PushModel;
import com.ximalaya.ting.android.host.util.ContextUtils;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.PackageUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.util.other.PermissionManage;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.provider.BaseJsSdkAction;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.model.UnlockListenTimeConfigModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.lite.main.album.dialog.FreeUnlockBottomDialog;
import com.ximalaya.ting.lite.main.album.fragment.LiteAlbumFragment;
import com.ximalaya.ting.lite.main.book.BookActionImpl;
import com.ximalaya.ting.lite.main.download.DownloadedAlbumDetailFragment;
import com.ximalaya.ting.lite.main.download.DownloadedTrackAdapter;
import com.ximalaya.ting.lite.main.earn.InspireAdRewardCoinManager;
import com.ximalaya.ting.lite.main.history.SkitsHistoryActionImpl;
import com.ximalaya.ting.lite.main.login.LoginActivity;
import com.ximalaya.ting.lite.main.login.qucikmob.QuickLoginForMobFullPageActivity;
import com.ximalaya.ting.lite.main.login.qucikmob.QuickLoginForMobHalfPageActivity;
import com.ximalaya.ting.lite.main.model.onekey.OneKeyRadioModel;
import com.ximalaya.ting.lite.main.onekey.playpage.OneKeyRadioPlayFragment;
import com.ximalaya.ting.lite.main.play.manager.CalculatePlayTimeManager;
import com.ximalaya.ting.lite.main.play.manager.PlaySubscribeRecommendManager;
import com.ximalaya.ting.lite.main.playlet.PlayletPlayRecordActionImpl;
import com.ximalaya.ting.lite.main.playnew.dialog.UnlockListenTimeDialog;
import com.ximalaya.ting.lite.main.playnew.fragment.PlayFragmentNew;
import com.ximalaya.ting.lite.main.playnew.manager.PlayPageDataManager;
import com.ximalaya.ting.lite.main.playnew.manager.PlayPageTopAdAutoCloseManager;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.rn.RNManager;
import com.ximalaya.ting.lite.main.rn.RNTurboReactPackageUtil;
import com.ximalaya.ting.lite.main.setting.dialog.PlanTerminateFragment;
import com.ximalaya.ting.lite.main.shortplay.manager.CacheDuanJuLengQidongLoadManager;
import com.ximalaya.ting.lite.main.shortplay.utils.CheckAlbumUtil;
import com.ximalaya.ting.lite.main.truck.model.TrackHomeTabModel;
import com.ximalaya.ting.lite.main.truck.tab.TruckHomeFragment;
import com.ximalaya.ting.reactnative.IAccount;
import com.ximalaya.ting.reactnative.IFunctionHandler;
import com.ximalaya.ting.reactnative.IUserStatusChangeListener;
import com.ximalaya.ting.reactnative.UserInfo;
import com.ximalaya.ting.reactnative.interf.ITurboReactPackage;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import okhttp3.OkHttpClient;


/**
 * 主app提供给别的module的操作
 *
 * <AUTHOR> on 2017/2/24.
 */

public class MainActionImpl implements IMainFunctionAction {

    private IBookAction mIBookAction;

    private ISkitsHistoryAction mISkitsHistoryAction;

    private IPlayletPlayRecordAction mIPlayletPlayRecordAction;

    @Override
    public AbstractTrackAdapter newDownloadedTrackAdapter(Context context, List<Track> list, int type) {
        DownloadedTrackAdapter trackAdapter = new DownloadedTrackAdapter(context, list);
        trackAdapter.setTrackType(type);
        return trackAdapter;
    }


    @Override
    public void getHomePage(Map<String, String> homeParams, IDataCallBack<HomepageM> iDataCallBack) {
        LiteCommonRequest.getHomePage(homeParams, iDataCallBack);
    }

    @Override
    public void getPlayPageInfo(HashMap<String, String> params, IDataCallBack<PlayingSoundInfo> iDataCallBack, String addString) {
        LiteCommonRequest.getPlayPageInfo(params, iDataCallBack, addString);
    }


    private void buryingPointForHandleIting(String url) {
        new UserTracking().setItemUrl(url).statIting(XDCSCollectUtil.SERVICE_OPEN_ITING);
    }

    @Override
    public void handleITing(Activity activity, Uri parse) {
        buryingPointForHandleIting(parse.toString());
        ITingHandler mHandler = new ITingHandler();
        mHandler.handleITing(activity, parse);
    }

    @Override
    public boolean handleITing(Activity activity, PushModel model) {
        buryingPointForHandleIting(model.schema);
        ITingHandler mHandler = new ITingHandler();
        return mHandler.handleITing(activity, model);
    }

    @Override
    public void handlerAdClick(Context context, Advertis thirdAd, String logType, String positonName) {
        AdManager.handlerAdClickToXmADSDK(context, thirdAd, positonName);
    }

    @Override
    public void handlerAdClick(Context context, Advertis thirdAd, String logType, String positonName, int categoryId,
                               int index) {
        AdManager.handlerAdClickToXmADSDK(context, thirdAd, AdReportModel.newBuilder(logType, positonName).categoryId(categoryId).position(index).build());
    }

    @Override
    public void batchAdRecord(Context context, List<? extends Advertis> thirdAds, String logType, String positonName) {
        AdManager.batchAdRecordToXmADSDK(context, thirdAds, logType, positonName);
    }

    @Override
    public void batchAdRecord(Context context, List<? extends Advertis> thirdAds, String logType, String positonName,
                              int categoryId) {
        AdManager.batchAdRecordToXmADSDK(context, thirdAds, AdReportModel.newBuilder(logType, positonName).categoryId(categoryId).build());
    }


    @Override
    public boolean handleIting(Activity activity, Uri uri) {
        buryingPointForHandleIting(uri.toString());
        ITingHandler handler = new ITingHandler();
        return handler.handleITing(activity, uri);
    }

    @Override
    public void putAdStateManagerAlearDownloadMap(String url, String filePath) {
        if (AdStateManage.alearDownloadMap == null) {
            AdStateManage.alearDownloadMap = new HashMap<>();
        }
        AdStateManage.alearDownloadMap.put(url, filePath);
    }

    @Override
    public void resetPlanTime(Context context) {
        PlanTerminateFragment.resetPlanTime(context);
    }

    @Override
    public Dialog newAdAppDownloadRemindDialog(Activity activity, String title, IHandleOk handleOk) {
        return null;
    }

    @Override
    public boolean hasPermissionAndRequest(@NonNull Activity activity, @NonNull ISetRequestPermissionCallBack
            requestPermissionCallBack, @NonNull Map<String, Integer> permissions) {
        return PermissionManage.checkPermission(activity, requestPermissionCallBack, permissions, null);
    }

    @Override
    public void checkPermission(@NonNull Activity activity, @NonNull ISetRequestPermissionCallBack
            requestPermissionCallBack, @NonNull Map<String, Integer> permissions, IPermissionListener listener) {
        PermissionManage.checkPermission(activity, requestPermissionCallBack, permissions, listener);
    }


    @Override
    public void getShareAd(Map<String, String> params, IDataCallBack<List<Advertis>> callBackM) {

    }


    @Override
    public boolean isAlbumAsc(Context context, long albumId) {
        return DownloadedAlbumDetailFragment.isAlbumAsc(context, albumId);
    }


    @Override
    public boolean playListCompleteHint(@NonNull Context context, ICallback<Void> hintCompleteCallback) {
//        BaseHintPlayer baseHintPlayer = new ListCompleteHintPlayer(context, hintCompleteCallback);
        return false;
    }

    @Override
    public void playWifiDisconnectHint(@NonNull Context context) {
    }

    @Override
    public boolean playTrackBeforeCheckNeedPlayAdInMain(Track playCompleteTrack, Track playStartTrack, int playMethod) {
        //⚠️NOTE:此方法可能是处于子线程，自动切歌，播放器触发的是子线程，此处禁止更新UI，需要切换到主线程
        //可以在该方法分流不同的前插广告
        Activity topActivity = BaseApplication.getTopActivity();
        //当前Activity不是mainActivity不处理
        if (!(topActivity instanceof MainActivity)) {
            return false;
        }
        MainActivity mainActivity = (MainActivity) topActivity;
        if (mainActivity.isFinishing()) {
            return false;
        }
        //不在前台，不处理
        if (!BaseUtil.isAppForeground(mainActivity)) {
            return false;
        }
        //播放页不可见不处理
        if (!mainActivity.isPlayFragmentVisible()) {
            return false;
        }
        if (mainActivity.getPlayerManager() == null) {
            return false;
        }
        //获取到播放页对象
        BaseFragment currentFragment = mainActivity.getPlayerManager().getCurrentFragment();
        if (!(currentFragment instanceof PlayFragmentNew)) {
            return false;
        }
        //播放页没有真实可见，不发起请求
        PlayFragmentNew playFragment = (PlayFragmentNew) currentFragment;
        if (!playFragment.canUpdateUi() || !playFragment.isRealVisable()) {
            return false;
        }
        //当前播放页可见了，又正在切歌，请求接口
        return playFragment.loadAdForPlayerSoundSwitch(playCompleteTrack, playStartTrack, playMethod);
    }

    @Override
    public void openOneKeyPlayPageForOneKeyBanner(OneKeyTingBannerModel oneKeyTingBannerModel) {
        Activity topActivity = BaseApplication.getTopActivity();
        if (oneKeyTingBannerModel == null || !(topActivity instanceof MainActivity)) {
            CustomToast.showFailToast("听电台打开失败");
            return;
        }
        MainActivity mainActivity = (MainActivity) topActivity;
        //channelType为 0 一键听 1 头条
        //linkType   4是一键听，5是听头条，需要转换下
        int channelType;
        if ("4".equals(oneKeyTingBannerModel.linkType)) {
            channelType = OneKeyRadioModel.TYPE_OTHER_RADIO;
        } else if ("5".equals(oneKeyTingBannerModel.linkType)) {
            channelType = OneKeyRadioModel.TYPE_HEADLINE_RADIO;
        } else {
            channelType = OneKeyRadioModel.TYPE_RADIO_NONE;
        }
        long channelId = oneKeyTingBannerModel.linkId;
        OneKeyRadioModel model = new OneKeyRadioModel();
        model.setId(channelId);
        model.setType(channelType);
        //进入播放页通过channelId来定位，radioId必现设置为0，模拟从肚脐眼进入
        model.setRadioId(0);
        OneKeyRadioPlayFragment oneKeyRadioPlayFragment = OneKeyRadioPlayFragment.newInstance(model);
        mainActivity.startFragment(oneKeyRadioPlayFragment);
    }

    @Override
    public boolean checkPlayAdNeedCanStartAutoCloseFunction(AbstractThirdAd thirdAd) {
        return PlayPageTopAdAutoCloseManager.checkNeedCanStartAutoCloseFunction(thirdAd);
    }

    @Override
    public boolean isInChildProtectFragmentFlow(Fragment fragment) {
        return false;
    }

    @Override
    public void isLivePushSettingOpen(IDataCallBack<Boolean> callBack) {

    }

    @Override
    public void setLivePushSetting(boolean isPush) {

    }

    @Override
    public void getFocusAd(Context appContext, long categoryId, IDataCallBack<List<BannerModel>> callBack) {

    }

    @Override
    public boolean isLoginActivity(Activity activity) {
        if (activity instanceof LoginActivity ||
                activity instanceof SmsLoginDialogActivity ||
                activity instanceof QuickLoginForMobHalfPageActivity ||
                activity instanceof QuickLoginForMobFullPageActivity
        ) {
            return true;
        }
        return false;
    }

    @Override
    public IHomeDialogManager getIHomeDialogManager() {
        return HomeDialogManager.getInstance();
    }

    @Override
    public ILifeCycleAction getILifeCycleAction() {
        return LifeCycleActionImpl.INSTANCE;
    }

    @Override
    public IBookAction getIBookAction() {
        if (mIBookAction == null) {
            mIBookAction = new BookActionImpl();
        }
        return mIBookAction;
    }

    @Override
    public ISkitsHistoryAction getISkitsHistoryAction() {
        if (mISkitsHistoryAction == null) {
            mISkitsHistoryAction = new SkitsHistoryActionImpl();
        }
        return mISkitsHistoryAction;
    }

    @Override
    public IReadTimeAction getIReadTimeAction() {
        return ReadTimeUtils.INSTANCE;
    }

    @Override
    public IPlayletPlayRecordAction getIPlayletRecordAction() {
        if (mIPlayletPlayRecordAction == null) {
            mIPlayletPlayRecordAction = new PlayletPlayRecordActionImpl();
        }
        return mIPlayletPlayRecordAction;
    }

    @Override
    public boolean isAppModeForTruckFriend() {
        return AppModeManager.isAppModeForTruckFriend();
    }

    @Override
    public boolean isCurTruckDianTaiPlayPageShow() {
        Activity topActivity = BaseApplication.getTopActivity();
        if (!(topActivity instanceof MainActivity) || !AppModeManager.isAppModeForTruckFriend()) {
            return false;
        }
        MainActivity mainActivity = (MainActivity) topActivity;
        TruckFriendModeActivity truckFriendModeActivity = mainActivity.getTruckFriendModeActivity();
        if (truckFriendModeActivity == null) {
            return false;
        }
        //播放页是否展示
        boolean playFragmentVisable = false;
        if (truckFriendModeActivity.getPlayerManager() != null) {
            playFragmentVisable = truckFriendModeActivity.getPlayerManager().isPlayFragmentVisable();
        }
        //堆栈是否有页面
        boolean isStackExitPage = mainActivity.getMangeFragmentSize() > 0;
        //当前播放页已经展示了，电台页没有展示
        //当前堆栈有声音，说明不在电台页
        if (playFragmentVisable || isStackExitPage) {
            return false;
        }
        Fragment currTabFragment = null;
        if (mainActivity.getTabFragmentManager() != null) {
            currTabFragment = mainActivity.getTabFragmentManager().getCurrFragment();
        }
        if (!(currTabFragment instanceof TruckHomeFragment)) {
            return false;
        }
        TruckHomeFragment truckHomeFragment = (TruckHomeFragment) currTabFragment;
        TrackHomeTabModel currentSelectTrackHomeTabModel = truckHomeFragment.getCurrentSelectTrackHomeTabModel();
        //当前页面已经展示，并且不选中的不是h5页面
        if (currentSelectTrackHomeTabModel != null && currentSelectTrackHomeTabModel.radioType != TrackHomeTabModel.TYPE_RADIO_H5) {
            return true;
        }
        return false;
    }

    @Override
    public IYaoyiYaoManager getYaoyiYaoManagerInstance(Context context) {
        return null;
    }

    @Override
    public void handleSoundPlayComplete(long trackId, long albumId) {
        try {
            Activity activity = BaseApplication.getMainActivity();
            if (!(activity instanceof MainActivity)) return;
            FragmentManager fragmentManager = ((MainActivity) activity).getSupportFragmentManager();
            List<Fragment> fragments = fragmentManager.getFragments();
            int size = CollectionUtil.isNotEmpty(fragments) ? fragments.size() : 0;
            for (int i = 0; i < size; i++) {
                Fragment fragment = fragments.get(i);
                if (fragment instanceof PlayFragmentNew) {
                    PlayFragmentNew fragmentNew = (PlayFragmentNew) fragment;
                    if (fragmentNew.isVisible()) {
                        fragmentNew.showShareGuideAnimation(trackId, albumId);
                        return;
                    }
                } else if (fragment instanceof ManageFragment) {
                    FragmentManager childFragmentManager = fragment.getChildFragmentManager();
                    List<Fragment> childFragments = childFragmentManager.getFragments();
                    int childSize = CollectionUtil.isNotEmpty(childFragments) ? childFragments.size() : 0;
                    for (int j = 0; j < childSize; j++) {
                        Fragment childFragment = childFragments.get(j);
                        if (childFragment instanceof LiteAlbumFragment) {
                            LiteAlbumFragment albumFragment = (LiteAlbumFragment) childFragment;
                            if (albumFragment.isVisible()) {
                                albumFragment.showShareGuideAnimation();
                                return;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void checkNeedPlaySubscribeRecommendForOnSoundPlayComplete(boolean isNextTimeClose) {
        PlaySubscribeRecommendManager.INSTANCE.checkNeedPlaySubscribeRecommendForOnSoundPlayComplete(isNextTimeClose);
    }

    @Override
    public XmBaseDialog<?> showUnlockBottomDialog(Activity activity, List<Track> trackList, AlbumPaidUnLockHintInfo info) {
        FreeUnlockBottomDialog bottomDialog = new FreeUnlockBottomDialog(activity, PlayPageDataManager.getInstance().getCurrentAlbum());
        bottomDialog.setUnlockHintInfo(trackList, info);
        bottomDialog.show();
        return bottomDialog;
    }

    @Override
    public BaseFullScreenDialogFragment showUnlockListenTimeDialog(FragmentActivity activity, AlbumM albumM, Track track, UnlockListenTimeConfigModel listenTimeConfigModel, UnlockListenTimeManagerNew.IRequestCallBack<Integer> callBack) {
        UnlockListenTimeDialog unlockListenTimeDialog = new UnlockListenTimeDialog();
        unlockListenTimeDialog.setAlbumM(albumM);
        unlockListenTimeDialog.setTrack(track);
        unlockListenTimeDialog.setCurUnlockTimeConfigModel(listenTimeConfigModel);
        unlockListenTimeDialog.setUnlockTimeCallBack(callBack);
        unlockListenTimeDialog.show(activity.getSupportFragmentManager(), "UnlockListenTimeDialog");
        return unlockListenTimeDialog;
    }

    @Override
    public void onCheckedChangedJumpFuLiFragmentPage() {
        Activity topActivity = BaseApplication.getTopActivity();
        if (!(topActivity instanceof MainActivity)) return;
        MainActivity mainActivity = (MainActivity) topActivity;
        mainActivity.switchWelfareTab(null);
    }

    @Override
    public void addPlayTimeListener(IPlayTimeCallBack callBack) {
        CalculatePlayTimeManager.INSTANCE.addLockTrackPlayTimeListener(callBack);
    }

    @Override
    public void removePlayTimeListener(IPlayTimeCallBack callBack) {
        CalculatePlayTimeManager.INSTANCE.removeLockTrackPlayTimeListener(callBack);
    }

    /**
     * 启始版本：3.1.24
     */
    @Override
    public void dealWithThirdExchangeTask(String positionName, JSONObject paramsJson, BaseJsSdkAction.AsyncCallback callback) {
        if (callback == null) {
            return;
        }
        String packageName = paramsJson.optString("packageName");
        String scheme = paramsJson.optString("scheme", "");
        int endTime = paramsJson.optInt("endTime", 0);
        String url = paramsJson.optString("url", "");
        String openurl = paramsJson.optString("openurl", "");
        //value=1 只打开scheme app
        //value=2 只打开落地页
        //value=3 优先打开scheme，没有安装打开落地页，被用户拒绝也打开落地页
        //value=0或者其他 优先打开scheme，没有安装打开落地页，被用户拒绝 不打开落地页，回调失败
        String openType = paramsJson.optString("openType", "0");
        Activity activity = BaseApplication.getTopActivity();
        if (activity == null || !ContextUtils.checkActivity(activity)) {
            callback.callback(NativeResponse.fail());
            CustomToast.showFailToast("app打开失败，奖励发放失败,activity=null");
            return;
        }
        switch (openType) {
            case "1":
                //只打开scheme app
                if (!TextUtils.isEmpty(openurl) && PackageUtil.isAppInstalled(BaseApplication.getMyApplicationContext(), packageName)) {
                    Intent intent = new Intent(Intent.ACTION_VIEW,
                            Uri.parse(openurl));
                    if (intent.resolveActivity(activity.getPackageManager()) != null) {
                        activity.startActivity(intent);
                    }
                } else if (TextUtils.isEmpty(scheme)) {
                    callback.callback(NativeResponse.fail(-4, "app打开异常"));
                    CustomToast.showFailToast("scheme为空，app打开失败");
                } else if (PackageUtil.isAppInstalled(BaseApplication.getMyApplicationContext(), packageName)) {
                    jumpThirdExchangeSchemePage(activity, scheme, callback);
                } else {
                    callback.callback(NativeResponse.fail(-8, "用户未安装app"));
                    CustomToast.showFailToast("app打开失败或未进入app内，奖励发放失败");
                }
                break;
            case "2":
                //只打开落地页
                if (!TextUtils.isEmpty(openurl) && PackageUtil.isAppInstalled(BaseApplication.getMyApplicationContext(), packageName)) {
                    Intent intent = new Intent(Intent.ACTION_VIEW,
                            Uri.parse(openurl));
                    if (intent.resolveActivity(activity.getPackageManager()) != null) {
                        activity.startActivity(intent);
                    }
                } else if (TextUtils.isEmpty(url)) {
                    callback.callback(NativeResponse.fail(-5, "url为空"));
                    CustomToast.showFailToast("网页打开失败，url为空");
                } else {
                    jumpThirdExchangeTaskPage(activity, endTime, url, positionName, callback);
                }
                break;
            case "3":
                //有app则打开app，无app则跳转落地页，如果打开app失败则跳转落地页
                if (PackageUtil.isAppInstalled(BaseApplication.getMyApplicationContext(), packageName) && !TextUtils.isEmpty(scheme)) {
                    XmLiteOpenThirdAppCallBackManager.getInstance().openXmLiteSchemeTask(activity, scheme, new IXmLiteSchemeOpenListener() {
                        @Override
                        public void onXmLiteSchemePageShow() {

                        }

                        @Override
                        public void onXmLiteSchemePageClose() {

                        }

                        @Override
                        public void onXmLiteSchemeLinkOpenReject() {
                            //用户拒绝后直接打开落地页
                            if (!TextUtils.isEmpty(url)) {
                                jumpThirdExchangeTaskPage(activity, endTime, url, positionName, callback);
                            } else {
                                callback.callback(NativeResponse.fail(-2, "用户拒绝打开app"));
                                CustomToast.showFailToast("app打开失败或未进入app内，奖励发放失败");
                            }
                        }

                        @Override
                        public void onXmLiteSchemeLinkOpenSuccess(long remainTimeS) {
                            callback.callback(NativeResponse.success());
                        }

                        @Override
                        public void onXmLiteSchemeLinkOpenError(String errorMsg) {
                            callback.callback(NativeResponse.fail(-3, "app打开异常"));
                            CustomToast.showFailToast("app打开失败，奖励发放失败");
                        }
                    });
                } else if (!TextUtils.isEmpty(url)) {
                    jumpThirdExchangeTaskPage(activity, endTime, url, positionName, callback);
                } else {
                    callback.callback(NativeResponse.fail(-6, "任务完成失败"));
                    CustomToast.showFailToast("任务完成失败，奖励发放失败");
                }
                break;
            default:
                //0：有app则拉起app，无app则跳转落地页，默认配置
                if (!TextUtils.isEmpty(openurl) && PackageUtil.isAppInstalled(BaseApplication.getMyApplicationContext(), packageName)) {
                    Intent intent = new Intent(Intent.ACTION_VIEW,
                            Uri.parse(openurl));
                    if (intent.resolveActivity(activity.getPackageManager()) != null) {
                        activity.startActivity(intent);
                    }
                } else if (PackageUtil.isAppInstalled(BaseApplication.getMyApplicationContext(), packageName) && !TextUtils.isEmpty(scheme)) {
                    jumpThirdExchangeSchemePage(activity, scheme, callback);
                } else if (!TextUtils.isEmpty(url)) {
                    jumpThirdExchangeTaskPage(activity, endTime, url, positionName, callback);
                } else {
                    callback.callback(NativeResponse.fail(-8, "用户未安装app"));
                    CustomToast.showFailToast("app打开失败或未进入app内，奖励发放失败");
                }
                break;
        }
    }

    @Override
    public void dealWithIntoQiJiTask(long bookId, long chapterId, View fromView) {
        Activity activity = BaseApplication.getTopActivity();
        if (activity == null || !ContextUtils.checkActivity(activity) || bookId == 0) {
            CustomToast.showFailToast("奇迹app打开失败");
            return;
        }
        String scheme = "xread://open?msg_type=1&tab_id=102&channelName=ximabookSDKbanner&businessType=1";
        //String scheme = Constants.QI_JI_CHANNEL_READER_SCHEME + "&book_id=" + bookId + "&chapter_id=" + chapterId + "&businessType=0&channelName=jisubanreader";
        String qiJiChannelReaderUrl = UrlConstants.getInstanse().getQiJiChannelReaderUrl();
        if (PackageUtil.isAppInstalled(BaseApplication.getMyApplicationContext(), Constants.QI_JI_CHANNEL_PACKAGE) && !TextUtils.isEmpty(scheme)) {
            XmLiteOpenThirdAppCallBackManager.getInstance().openXmLiteSchemeTask(activity, scheme, new IXmLiteSchemeOpenListener() {
                @Override
                public void onXmLiteSchemePageShow() {

                }

                @Override
                public void onXmLiteSchemePageClose() {

                }

                @Override
                public void onXmLiteSchemeLinkOpenReject() {
                    //用户拒绝后直接打开落地页
                    if (!TextUtils.isEmpty(qiJiChannelReaderUrl) && activity instanceof MainActivity) {
                        ToolUtil.clickUrlAction((MainActivity) activity, qiJiChannelReaderUrl, fromView);
                    } else {
                        CustomToast.showFailToast("奇迹app打开失败或未进入app内");
                    }
                }

                @Override
                public void onXmLiteSchemeLinkOpenSuccess(long remainTimeS) {
                }

                @Override
                public void onXmLiteSchemeLinkOpenError(String errorMsg) {
                    CustomToast.showFailToast("奇迹app打开失败");
                }
            });
        } else if (!TextUtils.isEmpty(qiJiChannelReaderUrl)) {
            if (activity instanceof MainActivity) {
                ToolUtil.clickUrlAction((MainActivity) activity, qiJiChannelReaderUrl, fromView);
            } else {
                CustomToast.showFailToast("奇迹app打开失败或未进入app内");
            }
        } else {
            CustomToast.showFailToast("奇迹app打开失败");
        }
    }


    /**
     * 三方换量--拉起scheme
     */
    public void jumpThirdExchangeSchemePage(Activity activity, String scheme, BaseJsSdkAction.AsyncCallback callback) {
        XmLiteOpenThirdAppCallBackManager.getInstance().openXmLiteSchemeTask(activity, scheme, new IXmLiteSchemeOpenListener() {
            @Override
            public void onXmLiteSchemePageShow() {

            }

            @Override
            public void onXmLiteSchemePageClose() {

            }

            @Override
            public void onXmLiteSchemeLinkOpenReject() {
                callback.callback(NativeResponse.fail(-2, "用户拒绝打开app"));
                CustomToast.showFailToast("app打开失败或未进入app内，奖励发放失败");
            }

            @Override
            public void onXmLiteSchemeLinkOpenSuccess(long remainTimeS) {
                callback.callback(NativeResponse.success());
            }

            @Override
            public void onXmLiteSchemeLinkOpenError(String errorMsg) {
                callback.callback(NativeResponse.fail(-3, "app打开异常"));
                CustomToast.showFailToast("app打开失败，奖励发放失败");
            }
        });
    }


    /**
     * 进入三方换量页面
     *
     * @param activity
     * @param endTime
     * @param url
     * @param positionName
     */
    public void jumpThirdExchangeTaskPage(Activity activity, int endTime, String url, String positionName, BaseJsSdkAction.AsyncCallback callback) {
        if ((activity == null || !ContextUtils.checkActivity(activity))) {
            callback.callback(NativeResponse.fail());
            CustomToast.showFailToast("app打开失败，奖励发放失败,activity=null");
            return;
        }
        if (endTime == 0 || TextUtils.isEmpty(url)) {
            callback.callback(NativeResponse.fail());
            CustomToast.showFailToast("网页打开失败，url为空或时间为0");
            return;
        }
        XmLiteWebTaskAdInfo info = new XmLiteWebTaskAdInfo();
        info.taskWebUrl = url;
        info.videoPlayTimeSecond = endTime;
        Advertis advertis = new Advertis();
        advertis.setPositionName(positionName);
        XmLiteWebTaskRewardVideoThirdAd thirdAd = new XmLiteWebTaskRewardVideoThirdAd(info, advertis, positionName);
        IXmLiteWebTaskRewardVideoListener iXmLiteWebTaskRewardVideoListener = new IXmLiteWebTaskRewardVideoListener() {

            private boolean hasPageClose = false;
            boolean mVideoPlayRewardAdVerifySuccess = false;
            boolean mVideoPlayComplete = false;

            @Override
            public void onWebTaskRewardAdShow() {

            }

            @Override
            public void onWebTaskRewardAdClick() {

            }

            @Override
            public void onWebTaskRewardAdVerify() {
                mVideoPlayRewardAdVerifySuccess = true;
            }

            @Override
            public void onWebTaskRewardAdPlayComplete() {
                mVideoPlayComplete = true;
            }

            @Override
            public void onWebTaskRewardAdPageClose() {
                if (hasPageClose) {
                    return;
                }
                //关闭回调限制只允许调用1次
                hasPageClose = true;
                if (callback != null && (mVideoPlayRewardAdVerifySuccess || mVideoPlayComplete)) {
                    callback.callback(NativeResponse.success());
                } else if (callback != null) {
                    callback.callback(NativeResponse.fail());
                    CustomToast.showFailToast("任务观看时间没有完成，奖励发放失败");
                }
            }

            @Override
            public void onWebTaskRewardAdError(String msg) {
                if (callback != null) {
                    callback.callback(NativeResponse.fail());
                }
            }
        };
        XmLiteWebTaskRewardVideoManager.getInstance().showXmLiteWebTaskRewardVideo(true, activity, thirdAd, iXmLiteWebTaskRewardVideoListener);
    }

    @Override
    public void showFreeModelGuideDialog() {
        HomeFreeModelGuideManager.showGuideDialog();
    }

    @Override
    public void dealWithIntoNovelReader(long bookId) {
        dealWithIntoNovelReader(bookId, 0);
    }

    @Override
    public void dealWithIntoNovelReader(long bookId, long chapterId) {
        Activity activity = BaseApplication.getTopActivity();
        if ((activity == null || !ContextUtils.checkActivity(activity)) || bookId == 0) {
            CustomToast.showFailToast("打开小说阅读器失败");
            return;
        }
        String stringBuilder = "uting://open?msg_type=10026" +
                "&book_id=" +
                bookId +
                "&chapter_id=" +
                chapterId;
        handleITing(activity, Uri.parse(stringBuilder));

    }

    @Override
    public void checkInstallQjApp(IInstallApkCallBack callback) {
        SilentDownQJManager.checkInstallQjApp(callback);
    }

    @Override
    public void startFuLiPage() {
        SilentDownQJManager.checkDownQJApk("fuLiPage");
    }

    @Override
    public void startBookHomePage() {
        SilentDownQJManager.checkDownQJApk("bookHomePage");
    }

    @Override
    public void addShowDialog(IShowDialog showDialog) {
        DialogShowManager.showDialog(showDialog);
    }

    @Override
    public void onPageResume(int tabId) {
        DialogShowManager.onPageResume(tabId);
    }

    @Override
    public void checkShowMemberBenefitsDialog() {
        MemberBenefitsManage.checkShowDialog();
    }

    @Override
    public void interceptDialog() {
        DialogShowManager.interceptDialog();
    }

    @Override
    public void resumeDialog() {
        DialogShowManager.resumeDialog();
    }

    @Override
    public void albumPageCheckQJApp(long bookId) {
        EbookInfo ebookInfo = new EbookInfo();
        ebookInfo.setBookId(bookId);
        DownQjApkManager.albumDetailCheckQJApp(ebookInfo);
    }

    @Override
    public void closeHalfScreenExchangeDialog() {
        HalfScreenExchangeManager.hideHalfScreenExchangeDialog();
    }

    @Override
    public void showExchangeNotifyDialog(String disposableAmount) {
        HalfScreenExchangeManager.showExchangeNotifyDialog(disposableAmount);
    }

    @Override
    public void setHalfScreenIsAutoShowExchangeDialog(boolean isAutoShow) {
        HalfScreenExchangeManager.setHalfScreenIsAutoShowExchangeDialog(isAutoShow);
    }

    @Override
    public boolean getHalfScreenIsAutoShowExchangeDialog() {
        return HalfScreenExchangeManager.getHalfScreenIsAutoShowExchangeDialog();
    }

    @Override
    public void requestTwoMinutesTaskIsCompleteByPlayPage() {
        try {
            Activity activity = BaseApplication.getTopActivity();
            if (!(activity instanceof MainActivity)) {
                return;
            }
            MainActivity mainActivity = ((MainActivity) activity);
            List<Fragment> fragments = mainActivity.getSupportFragmentManager().getFragments();
            int size = fragments.size();
            Fragment fragment = fragments.get(size - 1);
            if (!(fragment instanceof PlayFragmentNew)) {
                HalfScreenExchangeManager.setNeedNextExposeAutoShowExchangeDialog(true);
                return;
            }
            PlayFragmentNew playFragmentNew = (PlayFragmentNew) fragment;
            if (playFragmentNew.isRealVisable() && BaseUtil.isAppForeground(BaseApplication.getMyApplicationContext())) {
                //随机10秒，fix可能导致服务器压力过大
                int random = (int) (Math.random() * 10 + 1);
                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        SyncTotalListenTimeManagerForMainProcess.getInstance().syncTotalListenTime(SyncTotalListenTimeManagerForMainProcess.FROM_SYNC_PLAY_PAGE_EXCHANGE, null);
                        HalfScreenExchangeManager.requestTwoMinutesTaskIsCompleteByPlayPage(playFragmentNew);
                    }
                }, random * 1000L);
            } else {
                HalfScreenExchangeManager.setNeedNextExposeAutoShowExchangeDialog(true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void getAlbumType(long albumId, IDataCallBack<AlbumTypeModel> callBack) {
        CheckAlbumUtil.getAlbumType(albumId, callBack);
    }

    @Override
    public long getTrackId(AlbumM albumM) {
        return CheckAlbumUtil.getTrackId(albumM);
    }

    @Override
    public void preLoadAllDuanJuPageFirsrData(){
        CacheDuanJuLengQidongLoadManager.getInstance().preLoadAllDuanJuPageFirsrData();
    }

    @Override
    public boolean requestInspireAdEnable() {
        return InspireAdRewardCoinManager.requestInspireAdEnable();
    }

    @Override
    public void initRN(Application application) {
        RNManager.INSTANCE.init(application, new IFunctionHandler() {
            @NonNull
            @Override
            public OkHttpClient getOKHttp() {
                return BaseCall.getInstanse().getOkHttpClient();
            }

            @NonNull
            @Override
            public String getDeviceId() {
                return DeviceUtil.getDeviceToken(ToolUtil.getCtx());
            }

            @Override
            public boolean isDebug() {
                return ConstantsOpenSdk.isDebug;
            }

            @NonNull
            @Override
            public IAccount account() {
                return mAccount;
            }

            @Override
            public boolean isDarkMode() {
                return BaseFragmentActivity.sIsDarkMode;
            }

            @NonNull
            @Override
            public ITurboReactPackage turboReactPackage() {
                return RNTurboReactPackageUtil.getRNTurboReactPackage();
            }

            @NonNull
            @Override
            public String preloadBundleName() {
                return "";
            }

            @Override
            public boolean handleRouter(@NonNull String uri) {
                // TODO: 1/23/25 处理路由
                return false;
            }
        });
    }

    private @Nullable IUserStatusChangeListener mUserStatusChangeListener;
    private IAccount mAccount = new IAccount() {
        @Override
        public void setUserStatusChange(@Nullable IUserStatusChangeListener listener) {
            mUserStatusChangeListener = listener;
            if (listener != null) {
                UserInfoMannage.getInstance().addLoginStatusChangeListener(mLoginStatusChangeListener);
            }
        }

        @Override
        public boolean isLogin() {
            return UserInfoMannage.hasLogined();
        }

        @Override
        public UserInfo userInfo() {
            if (UserInfoMannage.hasLogined()) {
                LoginInfoModelNew user = UserInfoMannage.getInstance().getUser();
                return new UserInfo(user.getUid(), user.getToken(), user.getMobileSmallLogo(), user.getNickname(), user.isVip(), user.getMobile(), null);
            }

            return null;
        }
    };

    private ILoginStatusChangeListener mLoginStatusChangeListener = new ILoginStatusChangeListener() {
        @Override
        public void onLogout(LoginInfoModelNew olderUser) {
            if (mUserStatusChangeListener != null) {
                mUserStatusChangeListener.onLogout();
            }
        }

        @Override
        public void onLogin(LoginInfoModelNew model) {
            if (mUserStatusChangeListener != null) {
                mUserStatusChangeListener.onLogin();
            }
        }

        @Override
        public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {
            if (mUserStatusChangeListener != null) {
                mUserStatusChangeListener.onUserChange();
            }
        }
    };

}
