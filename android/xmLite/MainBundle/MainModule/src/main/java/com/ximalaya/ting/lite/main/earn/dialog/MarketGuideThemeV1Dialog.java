package com.ximalaya.ting.lite.main.earn.dialog;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.manager.feedback.CustomerFeedBackManager;
import com.ximalaya.ting.android.host.manager.track.MarkGiveGuideManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * 好评引导弹框
 * <p>
 * 主题1
 */
public class MarketGuideThemeV1Dialog extends XmBaseDialog {
    private static int FROM_SUBSCRIBE_DEF = 0;
    //订阅成功
    public static int FROM_SUBSCRIBE_SUCCESS = 1;
    //提现成功
    public static int FROM_CRASH_SUCCESS = 2;
    private Activity mActivity;
    private int mFrom = FROM_SUBSCRIBE_DEF;

    public MarketGuideThemeV1Dialog(@NonNull Activity context) {
        super(context);
        mActivity = context;
    }

    public MarketGuideThemeV1Dialog(@NonNull Activity context, int themeResId) {
        super(context, themeResId);
        mActivity = context;
    }

    protected MarketGuideThemeV1Dialog(@NonNull Activity context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        mActivity = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //必须放在setContentView之前，如果使用getDecorView,也要放在getDecorView之前
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        if (getWindow() != null) {
            getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            WindowManager.LayoutParams lp = getWindow().getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            lp.gravity = Gravity.CENTER;
            getWindow().setAttributes(lp);
        }
        setCanceledOnTouchOutside(false);
        setCancelable(true);
        setContentView(R.layout.main_dialog_market_theme_v1);
        initUI();
    }

    private void initUI() {
        if (mFrom == FROM_SUBSCRIBE_DEF) {
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException("必须传入from来源");
            }
        }
        //关闭按钮
        RelativeLayout rlMarketGuideClose = findViewById(R.id.main_rl_market_guide_close);
        //引导图
        ImageView ivMarketGuideImage = findViewById(R.id.main_iv_market_guide_image);
        //标题
        TextView tvMarketGuideTitle = findViewById(R.id.main_tv_market_guide_title);
        //描述
        TextView tvMarketGuideDesc = findViewById(R.id.main_tv_market_guide_desc);
        //提建议
        TextView tvMarketGuideFeedback = findViewById(R.id.main_tv_market_guide_feedback);
        //给好评
        TextView tvMarketGuideGiveScore = findViewById(R.id.main_tv_market_guide_give_score);

        if (mFrom == FROM_SUBSCRIBE_SUCCESS) {
            //订阅来源
            ivMarketGuideImage.setImageResource(R.drawable.main_icon_market_guide_subscribe_success);
            tvMarketGuideTitle.setText("订阅成功");
            tvMarketGuideDesc.setText("又找到喜欢的声音了！给个五星好评赞赞我吧");
            tvMarketGuideFeedback.setText("提点建议");
            tvMarketGuideGiveScore.setText("好评鼓励");
        } else if (mFrom == FROM_CRASH_SUCCESS) {
            //提现来源
            ivMarketGuideImage.setImageResource(R.drawable.main_icon_market_guide_crash_success);
            tvMarketGuideTitle.setText("提现成功");
            tvMarketGuideDesc.setText("轻松赚钱到手，发个五星好评让大家羡慕羡慕");
            tvMarketGuideFeedback.setText("下次再说");
            tvMarketGuideGiveScore.setText("好评晒收益");
        }

        //关闭点击
        rlMarketGuideClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                //关闭埋点
                traceClose();
            }
        });

        //提建议点击
        tvMarketGuideFeedback.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //订阅成功为提建议
                if (mFrom == FROM_SUBSCRIBE_SUCCESS) {
                    //打开意见反馈入口
                    CustomerFeedBackManager.jumpToFeedBackChatIssueSubmit();
                    //点击过意见反馈了,必须放在dismiss之前进行保存
                    MarkGiveGuideManager.saveMarketGiveGuideFinishForAll();
                }
                dismiss();
                traceFeedback();
            }
        });

        //给好评点击
        tvMarketGuideGiveScore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    Intent intent = new Intent(Intent.ACTION_VIEW);
                    String uri = "market://details?id=" + mActivity.getPackageName();
                    intent.setData(Uri.parse(uri));
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    mActivity.startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                    CustomToast.showFailToast("打开应用商店失败，请手动前往！");
                }
                //已经跳转过应用市场了,必须放在dismiss之前进行保存
                MarkGiveGuideManager.saveMarketGiveGuideFinishForAll();
                dismiss();
                traceGiveScore();
            }
        });

        AutoTraceHelper.bindData(rlMarketGuideClose, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(tvMarketGuideFeedback, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(tvMarketGuideGiveScore, AutoTraceHelper.MODULE_DEFAULT, "");

        traceShow();
    }

    public void setFrom(int mFrom) {
        this.mFrom = mFrom;
    }

    private void traceFeedback() {
        if (mFrom == FROM_SUBSCRIBE_SUCCESS) {
            new XMTraceApi.Trace()
                    .setMetaId(31538)
                    .setServiceId("dialogClick")
                    .put("item", "提点建议")
                    .createTrace();
        } else if (mFrom == FROM_CRASH_SUCCESS) {
            new XMTraceApi.Trace()
                    .setMetaId(31541)
                    .setServiceId("dialogClick")
                    .put("item", "下次再说")
                    .createTrace();
        }
    }

    private void traceGiveScore() {
        //订阅成功为提建议
        if (mFrom == FROM_SUBSCRIBE_SUCCESS) {
            new XMTraceApi.Trace()
                    .setMetaId(31538)
                    .setServiceId("dialogClick")
                    .put("item", "好评鼓励")
                    .createTrace();
        } else if (mFrom == FROM_CRASH_SUCCESS) {
            new XMTraceApi.Trace()
                    .setMetaId(31541)
                    .setServiceId("dialogClick")
                    .put("item", "好评晒收益")
                    .createTrace();
        }
    }

    private void traceShow() {
        if (mFrom == FROM_SUBSCRIBE_SUCCESS) {
            //订阅成功曝光埋点
            new XMTraceApi.Trace()
                    .setMetaId(31349)
                    .setServiceId("dialogView")
                    .createTrace();
        } else if (mFrom == FROM_CRASH_SUCCESS) {
            //提现成功曝光埋点
            new XMTraceApi.Trace()
                    .setMetaId(31540)
                    .setServiceId("dialogView")
                    .createTrace();
        }
    }

    private void traceClose() {
        if (mFrom == FROM_SUBSCRIBE_SUCCESS) {
            new XMTraceApi.Trace()
                    .setMetaId(31539)
                    .setServiceId("dialogClick")
                    .createTrace();
        } else if (mFrom == FROM_CRASH_SUCCESS) {
            new XMTraceApi.Trace()
                    .setMetaId(31543)
                    .setServiceId("dialogClick")
                    .createTrace();
        }
    }
}
