package com.ximalaya.ting.lite.main.comment

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.*
import android.widget.FrameLayout
import android.widget.RelativeLayout
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.manager.dialog.DialogWeakReferenceManager
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.lite.main.comment.fragment.PlayerCommentListFragment
import com.ximalaya.ting.lite.main.comment.fragment.PlayerCommentReplyListFragment
import com.ximalaya.ting.lite.main.comment.fragment.PlayerCommentTabFragment
import kotlinx.android.synthetic.main.main_fre_lite_test.*


/**
 *  @Author: Junxiang Cheng
 *  @Mail: <EMAIL>
 *  @CreateTime: 12/23/21
 *
 *  @Description: 播放页-评论弹框
 */
class CommentDialogFragmentMain : BaseDialogFragment<CommentDialogFragmentMain>() {
    companion object {
        const val TAG = "CommentDialogFragmentMain"

        const val TAG_COMMENT_LIST = "Comment"
        const val TAG_REPLY_LIST = "Reply"

        fun getInstance(): CommentDialogFragmentMain {
            val bundle = Bundle()
            val fra = CommentDialogFragmentMain()
            fra.arguments = bundle
            return fra
        }

        /**
         * 注入或者更新argument
         */

        @JvmStatic
        fun injectFragmentArgument(fragment: Fragment?, bundle: Bundle?) {
            if (fragment == null || bundle == null) {
                return
            }
            val arguments = fragment.arguments
            if (arguments != null) {
                arguments.putAll(bundle)
            } else {
                if (!fragment.isStateSaved) {
                    fragment.arguments = bundle
                }
            }
        }
    }

    lateinit var commentTabFragment: PlayerCommentTabFragment

    var rootView: RelativeLayout? = null
    var flContainer: FrameLayout? = null
    var outsideView: View? = null

    private var mCurTrack: Track? = null
    private var commentTotalCounts: Long = 0
    private var isAllComment: Boolean = true
    private var commentDes: String = "当前节目暂不支持评论功能，敬请谅解"

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog?.setCanceledOnTouchOutside(true)
        val window = dialog?.window ?: return null

        val mView = inflater.inflate(
            R.layout.main_fra_comment_main,
            window.findViewById(android.R.id.content),
            false
        )
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, BaseUtil.getScreenHeight(activity))
        window.setGravity(Gravity.BOTTOM)
        window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT)) //注意此处

        window.setWindowAnimations(R.style.host_dialog_push_in_out)

        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)

        if (mView == null) {
            return null
        }
        rootView = mView.findViewById(R.id.main_root_view)
        flContainer = mView.findViewById(R.id.main_fl_fragment_container)
        outsideView = mView.findViewById(R.id.main_view_fragment_outside)
        return mView
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        initUI()
    }

    private fun initUI() {
        val bundle = Bundle()
        startCommentList(bundle)

        outsideView?.setOnClickListener {
            dialog?.cancel()
        }
    }

    fun setCurrentTrack(currentTrack: Track, commentTotalCounts: Long) {
        mCurTrack = currentTrack
        this.commentTotalCounts = commentTotalCounts
    }

    fun setIsAllowCommentType(isAllComment: Boolean, commentDes: String) {
        this.isAllComment = isAllComment
        this.commentDes = commentDes
    }

    override fun show(transaction: FragmentTransaction, tag: String?): Int {
        DialogWeakReferenceManager.getInstance().createCommentListDialogRef(this)
        return super.show(transaction, tag)
    }

    override fun show(manager: FragmentManager, tag: String?) {
        DialogWeakReferenceManager.getInstance().createCommentListDialogRef(this)
        super.show(manager, tag)
    }

    fun startCommentList(bundle: Bundle) {
        if (requireActivity().isFinishing || requireActivity().isDestroyed) {
            return
        }
        if (!canUpdateUi()) {
            return
        }
        val transaction = childFragmentManager.beginTransaction()
        bundle.putParcelable(PlayerCommentTabFragment.TRUCK_KEY, mCurTrack)
        bundle.putLong(PlayerCommentTabFragment.COMMENT_TOTAL_COUNTS, commentTotalCounts)
        commentTabFragment = PlayerCommentTabFragment()
        commentTabFragment.arguments = bundle
        transaction.add(R.id.main_fl_fragment_container, commentTabFragment, TAG_COMMENT_LIST)
        transaction.commitNowAllowingStateLoss()

    }

    fun startReplayList(bundle: Bundle?, commentListPresenter: CommentListPresenter) {
        if (requireActivity().isFinishing || requireActivity().isDestroyed) {
            return
        }
        if (!canUpdateUi()) {
            return
        }
        val transaction = childFragmentManager.beginTransaction()

        val replyFragment =
            PlayerCommentReplyListFragment.getInstance(this, commentListPresenter, bundle)
        transaction.add(R.id.main_fl_fragment_container, replyFragment, TAG_REPLY_LIST)

        transaction.commitNowAllowingStateLoss()

    }

    fun returnCommentList(bundle: Bundle?) {
        if (requireActivity().isFinishing || requireActivity().isDestroyed) {
            return
        }
        if (!canUpdateUi()) {
            return
        }
        val transaction = childFragmentManager.beginTransaction()
        var commentFragment = childFragmentManager.findFragmentByTag(TAG_COMMENT_LIST)
        var replyFragment = childFragmentManager.findFragmentByTag(TAG_REPLY_LIST)
        if (replyFragment != null) {
            transaction.hide(replyFragment)
        }
        if (commentFragment is PlayerCommentTabFragment) {
            injectFragmentArgument(commentFragment, bundle)
            transaction.show(commentFragment)
        } else {
            commentFragment = PlayerCommentTabFragment()
            commentFragment.arguments = bundle
            transaction.add(R.id.main_fl_fragment_container, commentFragment, TAG_COMMENT_LIST)
        }
        if (replyFragment != null) {
            transaction.remove(replyFragment)
        }
        transaction.commitNowAllowingStateLoss()
    }

    fun isAllowSendComment(): Boolean {
        if (isAllComment) {
            return true
        }
        CustomToast.showSuccessToast(commentDes)
        return false
    }
}