package com.ximalaya.ting.lite.main.manager;

import android.os.SystemClock;
import android.widget.FrameLayout;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.host.activity.presenter.MainPresenter;
import com.ximalaya.ting.android.host.business.unlock.callback.IInsertScreenAdCallBack;
import com.ximalaya.ting.android.host.dialog.common.DailyAlbumOrTrackDialog;
import com.ximalaya.ting.android.host.dialog.common.EveryDayCoinReportDialog;
import com.ximalaya.ting.android.host.dialog.common.LiteHomeRecommendNovelDialog;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IHomeDialogManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.earn.AppStoreManager;
import com.ximalaya.ting.android.host.model.ad.LoadReawardParams;
import com.ximalaya.ting.android.host.util.ContextUtils;
import com.ximalaya.ting.android.host.view.OpenPushSettingDialog;
import com.ximalaya.ting.android.host.view.UpdateManagerDialog;
import com.ximalaya.ting.lite.main.book.provider.CommonInsertScreenAdProvider;
import com.ximalaya.ting.lite.main.customize.CustomizeBottomStyleDialog;
import com.ximalaya.ting.lite.main.dialog.MemberBenefitsDialog;
import com.ximalaya.ting.lite.main.earn.dialog.CommonPopupDialogFragment;
import com.ximalaya.ting.lite.main.earn.dialog.LoginGuideDialogStyle3Fragment;
import com.ximalaya.ting.lite.main.earn.dialog.OptimizedHasLoginEarnGuideDialogFragmentNew;
import com.ximalaya.ting.lite.main.newuser.dialog.NewUserQuickListenGuideDialogFragment;
import com.ximalaya.ting.lite.main.read.dialog.NovelTopGuideDialog;
import com.ximalaya.ting.lite.main.read.fragment.ProgramNovelTabFragment;
import com.ximalaya.ting.lite.main.tab.HomeFragment;
import com.ximalaya.ting.lite.main.tab.TruckTabNativeH5Fragment;

import java.util.ArrayList;
import java.util.List;

public class HomeDialogManager implements IHomeDialogManager {

    private CommonInsertScreenAdProvider mInsertScreenAdProvider;

    private FragmentActivity mActivity;

    private MainPresenter.IHomePageCallBack mShowADCallBack;
    /**
     * 所有弹窗 用于弹窗管理
     */
    private final List<String> mAllDialogFragment = new ArrayList<>();

    private static final String TAG = "HomeDialogManager";
    // 是否显示了广告
    private volatile boolean isADShow;

    private long mCurRequestTime;

    private boolean isForceCloseAd;

    private HomeDialogManager() {
        // 首页底部兴趣卡片
        mAllDialogFragment.add(CustomizeBottomStyleDialog.class.getSimpleName());
        // 新人极速听
        mAllDialogFragment.add(NewUserQuickListenGuideDialogFragment.class.getSimpleName());
        // 升级弹窗
        mAllDialogFragment.add(UpdateManagerDialog.class.getSimpleName());
        // 通知开关打开弹框
        mAllDialogFragment.add(OpenPushSettingDialog.class.getSimpleName());
        // 1元提现  使用的是公共弹窗
        mAllDialogFragment.add(CommonPopupDialogFragment.class.getSimpleName());
        // 新人奖励弹窗
        mAllDialogFragment.add(OptimizedHasLoginEarnGuideDialogFragmentNew.class.getSimpleName());
        // 未登录引导弹窗
        mAllDialogFragment.add(LoginGuideDialogStyle3Fragment.class.getSimpleName());
        // 每日金币播报弹窗
        mAllDialogFragment.add(EveryDayCoinReportDialog.class.getSimpleName());
        // 极速听模式下的引导弹窗
        mAllDialogFragment.add(NovelTopGuideDialog.class.getSimpleName());
        // 首页推荐小说弹窗
        mAllDialogFragment.add(LiteHomeRecommendNovelDialog.class.getSimpleName());
        // 专辑或声音推荐收听弹窗
        mAllDialogFragment.add(DailyAlbumOrTrackDialog.class.getSimpleName());
        // 会员权益弹窗
        mAllDialogFragment.add(MemberBenefitsDialog.class.getSimpleName());
    }

    public static HomeDialogManager getInstance() {
        return Inner.INSTANCE;
    }

    private static class Inner {
        private static final HomeDialogManager INSTANCE = new HomeDialogManager();
    }

    @Override
    public void init(FragmentActivity activity, FrameLayout adContainer, MainPresenter.IHomePageCallBack showADCallBack) {
        mInsertScreenAdProvider = new CommonInsertScreenAdProvider(activity, adContainer, new IInsertScreenAdCallBack() {
            @Override
            public void onSdkShow() {
                isForceCloseAd = false;
                // 当前不是首页  关闭广告
                if (mShowADCallBack == null || !isHomeFragment(mShowADCallBack.getCurHomePageFragment())) {
                    isForceCloseAd = true;
                    FuliLogger.log(TAG, "当前不是首页,关闭广告");
                    if (mInsertScreenAdProvider != null) {
                        mInsertScreenAdProvider.closeAd();
                    }
                    return;
                }
                isADShow = true;
            }

            @Override
            public void onSdkClose() {
                isADShow = false;
                if (!isForceCloseAd) {
                    mCurRequestTime = SystemClock.elapsedRealtime();
                }
            }

            @Override
            public void onSdkADError() {
                isADShow = false;
            }

            @Override
            public void onNoSdkAd() {
                isADShow = false;
            }
        });
        mActivity = activity;
        mShowADCallBack = showADCallBack;
    }

    @Override
    public void switchTabHomeFragmentVisible() {
        // 首页界面由隐藏变成显示
        requestAD("HomeFragment Visible");
    }

    @Override
    public void appToForegroundTabFragmentVisible(Fragment curFragment, String fromTag) {
        // 说明当前是首页
        if (isHomeFragment(curFragment)) {
            requestAD(fromTag);
        } else {
            FuliLogger.log(TAG, "当前首页tab未选中->拦截");
        }
    }

    @Override
    public void requestAD(String fromTag) {
        FuliLogger.log(TAG, fromTag + "->requestAD()");

        if (AppStoreManager.isAppStoreEnable()) {
            return;
        }

        if (isADShow) {
            FuliLogger.log(TAG, "插屏广告显示中->拦截");
            return;
        }

        //判断弹窗是否存在
        if (!isShowAd()) {
            return;
        }

        int diffTime = ConfigureCenter.getInstance().getInt(CConstants.Group_Ad.GROUP_NAME, CConstants.Group_Ad.ITEM_HOME_POP_UP_REQUEST_INTERVAL, 30);
        if (diffTime < 30) {
            FuliLogger.log(TAG, diffTime + "插屏广告间隔时长过短,强制兜底");
            diffTime = 30;
        }
        if (SystemClock.elapsedRealtime() - mCurRequestTime < diffTime * 1000L) {
            FuliLogger.log(TAG, (SystemClock.elapsedRealtime() - mCurRequestTime) + " 插屏广告请求间隔时间过短->拦截");
            return;
        }

        if (mInsertScreenAdProvider != null) {
            // 插屏广告
            mInsertScreenAdProvider.loadInsertScreenAd("sub_home_popup", "946936738", new LoadReawardParams(), this::isShowAd);
        }
    }

    @Override
    public boolean isADShow() {
        return isADShow;
    }

    @Override
    public void onResume() {
        if (mInsertScreenAdProvider != null) {
            mInsertScreenAdProvider.onResume();
        }
    }

    @Override
    public boolean onBackPressed() {
        if (mInsertScreenAdProvider != null) {
            return mInsertScreenAdProvider.onBackPressed();
        }
        return false;
    }

    @Override
    public void onDestroy() {
        if (mInsertScreenAdProvider != null) {
            mInsertScreenAdProvider.onDestroy();
        }
        mInsertScreenAdProvider = null;
        mActivity = null;
        mAllDialogFragment.clear();
        mShowADCallBack = null;
    }

    private boolean isShowAd() {
        if (isExistOtherDialog()) {
            FuliLogger.log(TAG, "存在其他弹窗");
            return false;
        }

        if (mShowADCallBack != null && isHomeFragment(mShowADCallBack.getCurHomePageFragment())) {
            // app 在前台才请求或显示广告
            if (!BaseApplication.sInstance.isAppForeground()) {
                FuliLogger.log(TAG, "应用不在前台");
                return false;
            }
            return true;
        }

        FuliLogger.log(TAG, "当前界面不是首页");

        return false;
    }

    private boolean isHomeFragment(Fragment curFragment) {
        if (curFragment != null && curFragment.isAdded() && !curFragment.isDetached() && !curFragment.isRemoving()
                && curFragment.isVisible()) {
            String curClassName = curFragment.getClass().getSimpleName();

            if (curFragment instanceof BaseFragment) {
                BaseFragment baseFragment = (BaseFragment) curFragment;
                if (!baseFragment.canUpdateUi() || baseFragment.isHidden() || !baseFragment.getUserVisibleHint() || !baseFragment.isParentFraVisible()) {
                    FuliLogger.log(TAG, curClassName + "不可见");
                    return false;
                }

                if (curFragment instanceof HomeFragment) {
                    HomeFragment homeFragment = (HomeFragment) curFragment;
                    // 是否选中H5页
                    boolean isSelect = homeFragment.isSelectCurTab(NativeHybridFragment.class);
                    FuliLogger.log(TAG, "H5Fragment是否选中:" + isSelect);
                    return !isSelect;
                } else if (curFragment instanceof ProgramNovelTabFragment) {
                    ProgramNovelTabFragment novelTabFragment = (ProgramNovelTabFragment) curFragment;
                    // 是否选中H5页
                    boolean isSelect = novelTabFragment.isSelectCurTab(TruckTabNativeH5Fragment.class);
                    FuliLogger.log(TAG, "H5Fragment是否选中:" + isSelect);
                    return !isSelect;
                }
            }
            FuliLogger.log(TAG, curClassName + "H5界面");
        }
        return false;
    }

    private boolean isExistOtherDialog() {
        if (!ContextUtils.checkActivity(mActivity)) {
            return false;
        }

        FragmentManager fragmentManager = mActivity.getSupportFragmentManager();
        if (fragmentManager == null) {
            return false;
        }

        for (Fragment fragment : fragmentManager.getFragments()) {
            if (fragment != null && mAllDialogFragment.contains(fragment.getClass().getSimpleName())) {
                FuliLogger.log(TAG, fragment.getClass().getSimpleName() + "弹窗已存在,不显示广告");
                return true;
            }
        }

        return false;
    }
}
