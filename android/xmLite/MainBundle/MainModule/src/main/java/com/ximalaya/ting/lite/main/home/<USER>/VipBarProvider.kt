package com.ximalaya.ting.lite.main.home.adapter

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.constants.LoginByConstants
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.model.vip.VipInfoModel
import kotlinx.android.synthetic.main.main_item_vip_other_bar.view.*

/**
 * Created by dumingwei on 2020/5/7.
 *
 * Desc: vip精选之外，其他页面的vip信息条
 */

class VipBarProvider(
        val fragment: BaseFragment2
) : IMulitViewTypeViewAndData<VipBarProvider.Holder, VipInfoModel> {

    private val TAG: String? = "VipBarProvider"

    private val mContext: Context? = fragment.context

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_vip_other_bar, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView)
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<VipInfoModel>, convertView: View, position: Int) {
        Logger.d(TAG, "bindViewDatas")
        val model = t.getObject()
        if (model is VipInfoModel) {
            with(holder.rootView) {
                ImageManager.from(mContext).displayImage(ivAvatar, model.logoPic, R.drawable.main_profile_img_userheah)
                tvSubTitle.text = model.guideText

                if (UserInfoMannage.hasLogined()) {
                    tvMainTitle.text = model.nickName

                    when (model.vipInfo?.vipStatus) {
                        VipInfoModel.VipInfo.NEVER_BE, VipInfoModel.VipInfo.EXPIRED -> {//不是会员，已过期
                            rlVipBg.background = resources.getDrawable(R.drawable.main_vip_top_region_invalid)
                            tvSubTitle.setTextColor(resources.getColor(R.color.main_vip_subtitle_color))
                            tvOpenVipOrRenew.text = resources.getString(R.string.main_open_vip)
                        }

                        VipInfoModel.VipInfo.IS_VIP -> {
                            rlVipBg.background = resources.getDrawable(R.drawable.main_vip_top_region)
                            tvSubTitle.setTextColor(resources.getColor(R.color.main_vip_login_subtitle_color))
                            tvOpenVipOrRenew.text = resources.getString(R.string.main_renewal_vip)
                        }
                    }
                } else {
                    rlVipBg.background = resources.getDrawable(R.drawable.main_vip_top_region_invalid)
                    tvSubTitle.setTextColor(resources.getColor(R.color.main_vip_subtitle_color))
                    tvOpenVipOrRenew.text = resources.getString(R.string.main_open_vip)
                    tvMainTitle.text = resources.getString(R.string.main_login_right_now)
                }

                ivAvatar.setOnClickListener {
                    dealAvatarOrMainTitleClick(model)
                }
                tvMainTitle.setOnClickListener {
                    dealAvatarOrMainTitleClick(model)
                }

                tvOpenVipOrRenew.setOnClickListener {
                    model.buttonUrl?.let { url ->
                        //跳转到开通vip的地方
                        val bundle = Bundle()
                        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url)
                        fragment.startFragment(NativeHybridFragment::class.java, bundle)
                    }
                }
            }
        }
    }

    private fun dealAvatarOrMainTitleClick(model: VipInfoModel) {
        if (UserInfoMannage.hasLogined()) {
            //跳商品页
            model.buttonUrl?.let { url ->
                //跳转到开通vip的地方
                val bundle = Bundle()
                bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url)
                fragment.startFragment(NativeHybridFragment::class.java, bundle)
            }
        } else {
            UserInfoMannage.gotoLogin(mContext, LoginByConstants.LOGIN_BY_DEFUALT)
        }
    }

    class Holder(var rootView: View) : BaseViewHolder()

}