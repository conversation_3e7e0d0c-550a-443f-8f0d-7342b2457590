package com.ximalaya.ting.lite.main.manager;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.DynamicImageProcessor;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.album.AlbumVideoInfoModel;
import com.ximalaya.ting.android.host.util.common.TimeHelper;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmutil.Logger;

import org.jetbrains.annotations.Nullable;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * 短剧激励视频解锁相关逻辑
 */
public class ShortPlayFreeListenManager {
    private final static Set<Long> sSupportFreeListenAlbumIdSet = new HashSet<>();
    private final static Map<Long, String> sAlbumIdIntroMap = new HashMap<>();

    // 是否支持激励视频解锁
    public static boolean supportFreeListen(AlbumVideoInfoModel.AlbumVideoInfo videoInfo) {
        return true;
//        if (isDebug()) {
//            return true;
//        }
//        if (!ShortPlayFreeListenConfigManager.isFreeListenOpen()) {
//            debugToast("非激励视频解锁短剧：开关未打开");
//            return false;
//        }
//        if (playSoundInfo == null) {
//            debugToast("非激励视频解锁短剧：playSoundInfo 为空");
//            return false;
//        }
//        if (playSoundInfo.albumInfo != null && playSoundInfo.albumInfo.type != 32) {
//            debugToast("非激励视频解锁短剧：albumInfoType: " + playSoundInfo.albumInfo.type);
//            return false;
//        }
//        boolean support = playSoundInfo.albumInfo != null && playSoundInfo.albumInfo.supportShortPlayFreeListen();
//        if (support) {
//            saveSupportFreeListenAlbumId(playSoundInfo.albumInfo.albumId);
//        } else {
//            int playletUnlockType = playSoundInfo.albumInfo != null ? playSoundInfo.albumInfo.playletUnlockType : -1;
//            debugToast("非激励视频解锁短剧：playletUnlockType: " + playletUnlockType);
//        }
//        return support;
    }

    private static void debugToast(String s) {
        if (ConstantsOpenSdk.isDebug) {
            ToastManager.showToast(s);
        }
    }

    public static void log(String s) {
        Logger.e("ShortPlayFree", s);
    }

    public static String getCoverUnlockButtonDescription(AlbumVideoInfoModel.AlbumVideoInfo videoInfo) {
        if (!supportFreeListen(videoInfo)) {
            return null;
        }
        return ShortPlayFreeListenConfigManager.getCoverUnlockButtonDescription();
    }

    public static String getCoverTitleHint(AlbumVideoInfoModel.AlbumVideoInfo videoInfo) {
        if (!supportFreeListen(videoInfo)) {
            return null;
        }
        return ShortPlayFreeListenConfigManager.getCoverTitleHint();
    }

    public static String getCoverSubTitleHint(AlbumVideoInfoModel.AlbumVideoInfo videoInfo) {
        if (!supportFreeListen(videoInfo)) {
            return null;
        }
        return ShortPlayFreeListenConfigManager.getCoverSubTitleHint();
    }

    public static void watchAdVideo(AlbumVideoInfoModel.TrackInfo trackInfo) {
        if (!ShortPlayFreeListenConfigManager.isFreeListenOpen()) {
            Logger.e("ShortPlayFree", "watchAdVideo return s1");
            return;
        }
        long trackId = -1;
        if (trackInfo != null) {
            trackId = trackInfo.trackId;
        }
        int adDuration = ShortPlayFreeListenConfigManager.getInstance().getAdDuration();

        Logger.e("ShortPlayFree", "watchAdVideo  trackId: " + trackId + ", adDuration: " + adDuration);

//        SkitsUnlockManager.adUnlock(trackId, adDuration, new IRewardResultCallback() {
//            @Override
//            public void onRewardSuccess() {
//                showSucceedToast();
//
//                ShortPlayFreeListenTimeManager.saveUnlockRecord();
//
//                //重新请求，触发播放
//                Intent intent = new Intent();
//                intent.setAction(ShortPlayFreeListenTimeManager.ACTION_UNLOCK_SUCCEED);
//                Context context = BaseApplication.getMyApplicationContext();
//
//                HandlerManager.postOnUIThreadDelay(new Runnable() {
//                    @Override
//                    public void run() {
//                        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
//                    }
//                }, 200);
//
//                reportUnlockSucceed(track);
//            }
//
//            private void showSucceedToast() {
//                String unlockSucceedToast = ShortPlayFreeListenConfigManager.getUnlockSucceedToast();
//
//                Logger.e("ShortPlayFree", "watchAdVideo onRewardSuccess, " + unlockSucceedToast);
//
//                if (unlockSucceedToast != null && unlockSucceedToast.contains("%s")) {
//                    int rewardDuration = ShortPlayFreeListenConfigManager.getInstance().getRewardDuration();
//                    String minuteHour = TimeHelper.getFriendlyLongTime(rewardDuration);
//
//                    Logger.e("ShortPlayFree", "watchAdVideo onRewardSuccess s2, rewardDuration: "
//                            + rewardDuration + ", minuteHour: " + minuteHour);
//
//                    unlockSucceedToast = String.format(Locale.getDefault(), unlockSucceedToast, minuteHour);
//                }
//                ToastManager.showSuccessToast(unlockSucceedToast);
//            }
//
//            @Override
//            public void onRewardFail() {
//                String toast = ShortPlayFreeListenConfigManager.getUnlockFailedToast();
//                Logger.e("ShortPlayFree", "watchAdVideo onRewardFail, toast: " + toast);
//                ToastManager.showFailToast(toast);
//            }
//
//            @Override
//            public void onRewardError() {
//                Logger.e("ShortPlayFree", "watchAdVideo onRewardError");
//                ToastManager.showToast("广告解锁失败");
//            }
//        });
    }

    public static boolean supportFreeListen(Track track) {
        if (track == null || track.getAlbum() == null) {
            return false;
        }

        return sSupportFreeListenAlbumIdSet.contains(track.getAlbum().getAlbumId());
    }

    public static void saveSupportFreeListenAlbumId(long albumId) {
        sSupportFreeListenAlbumIdSet.add(albumId);
        Logger.w("ShortPlayFree", "saveSupportFreeListenAlbumId: " + albumId + " \n " + sSupportFreeListenAlbumIdSet);
    }

    public static boolean supportFreeListen(long albumId) {
        return sSupportFreeListenAlbumIdSet.contains(albumId);
    }

    //添加短剧解锁提示到播放页
    public static void showCommercialView(AlbumVideoInfoModel.AlbumVideoInfo videoInfo, String wrapCover, ViewGroup containerView) {
        if (videoInfo == null || videoInfo.albumInfo == null || containerView == null) {
            return;
        }
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                showCommercialViewMainThread(videoInfo, wrapCover, containerView);
            }
        });
    }

    public static void showCommercialViewMainThread(AlbumVideoInfoModel.AlbumVideoInfo videoInfo, String wrapCover, ViewGroup containerView) {
        if (videoInfo == null || videoInfo.albumInfo == null || containerView == null) {
            return;
        }
        int childCount = containerView.getChildCount();

        String title = videoInfo.trackInfo != null ? videoInfo.trackInfo.title : videoInfo.albumInfo.title;
        Logger.d("z_short_play", "showCommercialView >>> " + title + ", childCount: " + childCount
                + "\n wrapCover: " + wrapCover);

        if (childCount > 0) {
            return;
        }

        Context context = BaseApplication.getMyApplicationContext();
        View buttonContainer = LayoutInflater.from(context).inflate(R.layout.host_view_over_audition_btn_container_for_short_play, null);
        FrameLayout.LayoutParams flp = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT
        );
        buttonContainer.setLayoutParams(flp);
        containerView.addView(buttonContainer);

        ImageView backgroundIv = buttonContainer.findViewById(R.id.host_over_audition_background);
        TextView titleTv = buttonContainer.findViewById(R.id.host_play_page_over_audition_hint);
        TextView subTitleTv = buttonContainer.findViewById(R.id.host_play_page_over_audition_sub_hint);
        TextView buttonTv = buttonContainer.findViewById(R.id.host_play_page_over_audition_button);

//        ViewStatusUtil.setText(titleTv, ShortPlayFreeListenConfigManager.getCoverTitleHint());

        //二级标题需要传入权益时间
        try {
            String coverSubTitleHint = ShortPlayFreeListenConfigManager.getCoverSubTitleHint();
            if (coverSubTitleHint != null && coverSubTitleHint.contains("%s")) {

                int rewardDuration = ShortPlayFreeListenConfigManager.getInstance().getRewardDuration();
                String minuteHour = TimeHelper.getFriendlyLongTime(rewardDuration);
                coverSubTitleHint = String.format(Locale.getDefault(), coverSubTitleHint, minuteHour);

                Logger.d("z_short_play", "showCommercialView >>> " + coverSubTitleHint + ", rewardDuration:" + rewardDuration);
            }
//            ViewStatusUtil.setText(subTitleTv, coverSubTitleHint);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            Logger.d("z_short_play", "showCommercialView error: " + throwable.getMessage());
        }

//        ViewStatusUtil.setText(buttonTv, ShortPlayFreeListenConfigManager.getCoverUnlockButtonDescription());

        buttonTv.setOnClickListener(v -> {
            if (!OneClickHelper.getInstance().onClick(v)) {
                return;
            }

            try {
                Intent intent = new Intent(Intent.ACTION_VIEW);
                intent.setData(Uri.parse("iting://open?msg_type=13&album_id=" + videoInfo.albumInfo.albumId));
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                BaseApplication.getTopActivity().startActivity(intent);
            } catch (Exception e) {
                ToastManager.showToast("请去应用商店下载喜马拉雅app");
                e.printStackTrace();
            }
//                ShortPlayFreeListenManager.watchAdVideo(videoInfo.trackInfo);
        });


//        String cover = wrapCover;
//        if (TextUtils.isEmpty(cover)) {
//            if (!TextUtils.isEmpty(playingSoundInfo.albumInfo.coverLarge)) {
//                cover = playingSoundInfo.albumInfo.coverLarge;;
//            } else {
//                cover = playingSoundInfo.albumInfo.coverMiddle;
//            }
//        }
//        setCoverForImageView(cover, backgroundIv);
    }

    public static void setCoverForImageView(String albumCover, ImageView backgroundIv) {
        if (TextUtils.isEmpty(albumCover) || backgroundIv == null) {
            return;
        }

        Context context = BaseApplication.getMyApplicationContext();
        Logger.e("z_short_play", "setBackground albumCover: " + albumCover + "\n Imageview width: " + backgroundIv.getWidth() + ", height: " + backgroundIv.getHeight());

        if (albumCover.contains("&columns")) {
            try {
                int index = albumCover.indexOf("&columns");
                albumCover = albumCover.substring(0, index);

                Logger.e("z_short_play", "setBackground substring: " + albumCover);
            } catch (Throwable throwable) {
                Logger.e("z_short_play", "setBackground uri parse error: " + throwable.getMessage());
            }
        }

        Bitmap bitmap = ImageManager.from(context).getFromCacheAndDisk(albumCover);

        Logger.e("z_short_play", "setBackground bitmap from cache: " + bitmap);

        if (bitmap != null) {
            backgroundIv.setImageBitmap(bitmap);
            return;
        }

        String fixCoverUrl = DynamicImageProcessor.getSingleInstance().fitNoWebpImage(albumCover, 0, 0);
        ImageManager.from(context).putWhiteImageMemory(fixCoverUrl);
        ImageManager.from(context).putWhiteImageMemory(albumCover);

        ImageManager.Options options = null;
        ImageManager.from(context).downloadBitmap(albumCover, options, new ImageManager.DisplayCallback() {
            @Override
            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                if (bitmap != null) {
                    backgroundIv.setImageBitmap(bitmap);
                }
            }
        });
    }

    public static String getTrackInfo(@Nullable Long albumId) {
        if (albumId == null) {
            return null;
        }
        return sAlbumIdIntroMap.get(albumId);
    }

    public static void saveTrackInfo(@Nullable Long albumId, String info) {
        if (albumId == null || TextUtils.isEmpty(info)) {
            return;
        }
        sAlbumIdIntroMap.put(albumId, info);
    }
}
