package com.ximalaya.ting.lite.main.download.manager;

import com.ximalaya.ting.android.xmutil.Logger;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> feiwen
 * date   : 2019/4/8
 * desc   :
 */
public class NewTaskExecutor {

    /**
     * 线程工厂
     */
    ThreadFactory mFactory;
    /**
     * 线程表示
     */
    private String mTag;
    /**
     * 线程池服务
     */
    ThreadPoolExecutor mThreadPool;
    /**
     * 任务等待执行队列
     */
    BlockingQueue<Runnable> mWaitingWorkQueue;
    /**
     * 任务正在执行队列
     */
    BlockingQueue<Runnable> mDoingWorkQueue;

    public NewTaskExecutor(ThreadFactory factory, String name, int max) {
        this.mFactory = factory;
        this.mTag = name;
        mWaitingWorkQueue = new LinkedBlockingQueue<>();
        mDoingWorkQueue = new ArrayBlockingQueue<>(max);
        mThreadPool = new ThreadPoolExecutor(max, max, 30L, TimeUnit.MILLISECONDS, mWaitingWorkQueue, factory) {

            @Override
            protected void beforeExecute(Thread t, Runnable r) {
                super.beforeExecute(t, r);
                if (mDoingWorkQueue.offer(r)) {
                    Logger.d(mTag, MessageFormat.format("开始任务前，正在执行{0}，等待{1}", mDoingWorkQueue.size(), mWaitingWorkQueue.size()));
                } else {
                    Logger.e(mTag, "任务无法被添加入到：mDoingWorkQueue");
                }
            }

            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                mDoingWorkQueue.remove(r);
                Logger.d(mTag, MessageFormat.format("有任务结束后，正在执行{0}，等待{1}", mDoingWorkQueue.size(), mWaitingWorkQueue.size()));
            }
        };
    }

    public boolean remove(Runnable task) {
        return mThreadPool.remove(task);
    }

    public void add(Runnable command) {
        try {
            mThreadPool.execute(command);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean contain(Runnable command) {
        return fetchAll().contains(command);
    }

    public boolean isWaiting(Runnable command) {
        return mWaitingWorkQueue.contains(command);
    }

    public boolean isExcuted(Runnable command) {
        return mDoingWorkQueue.contains(command);
    }

    public ArrayList<Runnable> fetchAll() {
        ArrayList<Runnable> list = new ArrayList<Runnable>();
        list.addAll(mWaitingWorkQueue);
        list.addAll(mDoingWorkQueue);
        return list;
    }

    public ArrayList<Runnable> fetchExecutingTask() {
        ArrayList<Runnable> list = new ArrayList<Runnable>();
        list.addAll(mDoingWorkQueue);
        return list;
    }

    public ArrayList<Runnable> fetchWaitingTask() {
        ArrayList<Runnable> list = new ArrayList<Runnable>();
        list.addAll(mWaitingWorkQueue);
        return list;
    }

    public Runnable fetch(Runnable r) {
        ArrayList<Runnable> list = new ArrayList<Runnable>();
        list.addAll(mWaitingWorkQueue);
        list.addAll(mDoingWorkQueue);

        Iterator<Runnable> e = list.iterator();
        if (r == null) {
            return null;
        } else {
            while (e.hasNext()) {
                Runnable next = e.next();
                if (r.equals(next))
                    return next;
            }
        }
        return null;
    }

    public void clear() {
        mDoingWorkQueue.clear();
        mWaitingWorkQueue.clear();
    }

    public void destroy() {
        // shutDown后下一次启用mThreadPool.execute将报RejectedExecutionException
        mThreadPool.shutdownNow();
        mDoingWorkQueue.clear();
        mWaitingWorkQueue.clear();
    }
}
