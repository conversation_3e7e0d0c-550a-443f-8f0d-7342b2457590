package com.ximalaya.ting.lite.main.home.adapter

import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel
import com.ximalaya.ting.lite.main.model.album.HorizontalScrollAlbumModel
import com.ximalaya.ting.lite.main.view.LinearItemDecoration
import kotlinx.android.synthetic.main.main_item_horizontal_scroll_one_line_album.view.*

/**
 * Created by dumingwei on 2020/6/5
 *
 * Desc: 横向滑动单行专辑模块
 *       1. 兴趣推荐区复用此模块
 */
class HorizontalScrollOneLineAlbumProvider @JvmOverloads constructor(
        val mFragment: BaseFragment2,
        private val mExtraModel: HomeRecommendExtraViewModel? = null
) : IMulitViewTypeViewAndData<HorizontalScrollOneLineAlbumProvider.Holder, HorizontalScrollAlbumModel> {

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_horizontal_scroll_one_line_album, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        val holder = Holder(convertView)
        initRvAlbum(holder)
        return holder
    }

    private fun initRvAlbum(holder: Holder) {
        val context = BaseApplication.getMyApplicationContext()
        with(holder.rootView) {
            mainRvAlbums.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            val spacing = BaseUtil.dp2px(context, 12f)
            val margin = BaseUtil.dp2px(context, 12f)
            mainRvAlbums.addItemDecoration(LinearItemDecoration(spacing, margin))
            mainRvAlbums.setDisallowInterceptTouchEventView(mFragment.view as ViewGroup)
            val adapter = RecommendAlbumInModuleAdapter(mFragment)
            holder.adapter = adapter
            mainRvAlbums.adapter = adapter
        }
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<HorizontalScrollAlbumModel>, convertView: View, position: Int) {
        val model = t.getObject()
        if (model is HorizontalScrollAlbumModel) {
            val albumMList = model.mainAlbumMList
            with(holder.rootView) {
                mainRvAlbums.clearOnScrollListeners()
                convertView.post {
                    (mainRvAlbums.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                            model.lastScrollPosition, model.lastScrollOffset)
                    mainRvAlbums.addOnScrollListener(OnScrollListener(model))
                }
                holder.adapter?.let {
                    if (albumMList.isHasMore) {
                        it.setOnMoreBtnClickListener(model.moreClickListener)
                    } else {
                        it.setOnMoreBtnClickListener(null)
                    }
                    it.setAlbumMList(albumMList.list)
                    it.notifyDataSetChanged()
                }
            }
        }
    }

    class OnScrollListener(val mModel: HorizontalScrollAlbumModel) : RecyclerView.OnScrollListener() {

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            val firstView = recyclerView.layoutManager!!.getChildAt(0)
            if (firstView != null) {
                mModel.lastScrollPosition = recyclerView.layoutManager!!.getPosition(firstView)
                mModel.lastScrollOffset = firstView.left - recyclerView.layoutManager!!.getLeftDecorationWidth(firstView)
            }
        }
    }

    class Holder(var rootView: View) : BaseViewHolder() {
        var adapter: RecommendAlbumInModuleAdapter? = null
    }

}