package com.ximalaya.ting.lite.main.home.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.ximalaya.ting.android.framework.adapter.HolderAdapter
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel
import com.ximalaya.ting.lite.main.model.newhome.LiteCloudTag
import com.ximalaya.ting.lite.main.model.newhome.LiteCloudTagProviderModel
import com.ximalaya.ting.lite.main.newhome.adapter.LiteCloudTagAdapter
import com.ximalaya.ting.lite.main.view.AutoScrollRecyclerView

/**
 * Created by dumingwei on 2021/6/11
 *
 * Desc: 云标签模块
 */
class LiteCloudTagProvider @JvmOverloads constructor(
    val mFragment: BaseFragment2,
    val mExtraViewModel: HomeRecommendExtraViewModel
) : IMulitViewTypeViewAndData<LiteCloudTagProvider.Holder, LiteCloudTagProviderModel> {

    private val TAG: String = "LiteCloudTagProvider"

    private var mHolder: Holder? = null

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_cloud_tag_provider, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        mHolder = Holder(convertView)
        return mHolder!!

    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<LiteCloudTagProviderModel>, convertView: View, position: Int) {
        val model = t.getObject()
        val tagList = model.tagList
        val initialSize = model.initialSize
        if (model.title.isNullOrEmpty()) {
            holder.tvTitle.visibility = View.GONE
        } else {
            holder.tvTitle.visibility = View.VISIBLE
            holder.tvTitle.text = model.title
        }

        val adapter = holder.rv.adapter
        if (adapter == null) {
            initAdapter(holder.rv, tagList, initialSize)
        } else {
            if (adapter is LiteCloudTagAdapter) {
                adapter.mDataList = tagList
                adapter.initialSize = initialSize
                adapter.notifyDataSetChanged()
            }
        }
    }

    fun startLoop() {
        mHolder?.rv?.start(false)
    }

    fun stopLoop() {
        mHolder?.rv?.stop()

    }

    private fun initAdapter(rv: RecyclerView, list: List<LiteCloudTag>, initialSize: Int) {
        if (rv.adapter == null) {
            val staggeredGridLayoutManager = StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.HORIZONTAL)
            rv.itemAnimator = null
            rv.layoutManager = staggeredGridLayoutManager
            val adapter = LiteCloudTagAdapter(mFragment, list, initialSize, mExtraViewModel.from == BundleValueConstantsInMain.FROM_NEW_HOME_RECOMMEND,
                mExtraViewModel.pageId)
            rv.adapter = adapter
        }
    }

    class Holder(var rootView: View) : HolderAdapter.BaseViewHolder() {
        val tvTitle: TextView = rootView.findViewById(R.id.main_tv_provider_title)
        val rv: AutoScrollRecyclerView = rootView.findViewById(R.id.main_rv_cloud_tag)

    }

}