package com.ximalaya.ting.lite.main.home.adapter

import android.annotation.SuppressLint
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.business.unlock.manager.AlbumUnlockABManager
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter
import com.ximalaya.ting.lite.main.home.viewmodel.HomeItemAlbumRankModel


/**
 * Created by qinhuifeng on 2020/6/10.
 *
 * 排行榜条目
 */
class HomeItemAlbumRankAdapterNew(
    private val fragment: BaseFragment2,
    private val mAlbumMList: List<HomeItemAlbumRankModel>,
    private val title: String,
    private val mIsRecommendChannel: Boolean
) : AbRecyclerViewAdapter<RecyclerView.ViewHolder>() {

    var isShowTag = false

    init {
        isShowTag = ConfigureCenter.getInstance().getBool(
            CConstants.Group_Base.GROUP_NAME,
            CConstants.Group_Base.ITEM_CAN_SHOW_ALBUM_TAG,
            false
        )
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view: View = if (viewType == 0) {
            LayoutInflater.from(parent.context)
                .inflate(R.layout.main_item_viewpage_item_album_rank_recommend_new, parent, false)
        } else {
            LayoutInflater.from(parent.context)
                .inflate(R.layout.main_item_viewpage_item_album_rank_new, parent, false)
        }
        return AlbumRankViewHolder(view)
    }

    override fun getItemViewType(position: Int): Int {
        if (position != 0) {
            return 2
        }
        return position
    }

    override fun getItemCount(): Int {
        return mAlbumMList.size
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is AlbumRankViewHolder) {
            //绑定排行榜条目
            onBindAlbumRankViewHolder(holder, position)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun onBindAlbumRankViewHolder(holder: AlbumRankViewHolder, position: Int) {
        val album: AlbumM = mAlbumMList[position].albumM ?: return
        //设置专辑标题
        holder.tvAlbumTitle.text = album.albumTitle

        //设置专辑图
        ImageManager.from(fragment.context).displayImage(
            holder.ivAlbumCover,
            album.largeCover,
            R.drawable.host_default_album,
            R.drawable.host_default_album
        )

        //设置专辑评分，

        val showAlbumScoreStr =
            if (TextUtils.isEmpty(album.albumScore) || album.albumScore == "0" ||
                album.albumScore == "0.0" || album.albumScore == "0.00"
            ) {
                ""
            } else {
                "${album.albumScore}分"
            }
        holder.tvAlbumScore.text = showAlbumScoreStr

        if (isShowTag) {
            //设置专辑分类标签
            val albumTagSourceListString = getAlbumTagSourceListString(album)
            if (!TextUtils.isEmpty(albumTagSourceListString)) {
                //有分类标签，展示分类标签
                holder.tvAlbumTagSourceList.text = albumTagSourceListString
                holder.tvAlbumTagSourceList.visibility = View.VISIBLE
            } else {
                //没有分类标签的时候，展示专辑集数
                holder.tvAlbumTagSourceList.visibility = View.GONE
            }
        } else {
            //没有分类标签的时候，展示专辑集数
            holder.tvAlbumTagSourceList.visibility = View.GONE
        }

        //设置排行榜数字
        val rankNumber = position + 1
        when {
            rankNumber == 1 -> {
                holder.ivRankNumber.setImageResource(R.drawable.main_icon_home_rank_1)
                holder.tvRankNumber.text = ""
            }
            rankNumber == 2 -> {
                holder.ivRankNumber.setImageResource(R.drawable.main_icon_home_rank_2)
                holder.tvRankNumber.text = ""
            }
            rankNumber == 3 -> {
                holder.ivRankNumber.setImageResource(R.drawable.main_icon_home_rank_3)
                holder.tvRankNumber.text = ""
            }
            rankNumber <= 9 -> {
                holder.ivRankNumber.setImageResource(R.drawable.main_icon_home_rank_4_9)
                holder.tvRankNumber.text = rankNumber.toString()
            }
            rankNumber <= 99 -> {
                holder.ivRankNumber.setImageResource(R.drawable.main_icon_home_rank_10_99)
                holder.tvRankNumber.text = rankNumber.toString()
            }
            else -> {
                holder.ivRankNumber.setImageResource(R.drawable.main_icon_home_rank_100_more)
                holder.tvRankNumber.text = rankNumber.toString()
            }
        }

        val model = mAlbumMList[position]
        if (mIsRecommendChannel) {
            // 新首页-排行榜-专辑item  控件曝光
            XMTraceApi.Trace()
                .setMetaId(29653)
                .setServiceId("slipPage")
                .put("albumId", album.id.toString())
                .put("moduleId", model.moduleId.toString())
                .put("currPage", "homePageV2")
                .put("type", AlbumUnlockABManager.getHomeLeaderBoardConfig().toString())
                .createTrace()
        } else {
            // 新首页-排行榜-专辑item  控件曝光
            XMTraceApi.Trace()
                .setMetaId(29753)
                .setServiceId("slipPage")
                .put("albumId", album.id.toString())
                .put("moduleId", model.moduleId.toString())
                .put("currPage", "homePageV2")
                .put("type", AlbumUnlockABManager.getHomeLeaderBoardConfig().toString())
                .createTrace()
        }

        // 排行榜-曝光-校验  其他事件
        XMTraceApi.Trace()
            .setMetaId(58186)
            .setServiceId("others")
            .put("albumId", album.id.toString())
            .put("moduleId", model.moduleId.toString())
            .put("currPage", "homePageV2")
            .put("type", AlbumUnlockABManager.getHomeLeaderBoardConfig().toString())
            .createTrace()


        //设置item点击
        holder.itemView.setOnClickListener {
            handleItemClick(album, model)
        }

        holder.tvAlbumLike?.apply {
            text = "${title}第一名"
        }
    }

    private fun handleItemClick(album: AlbumM, model: HomeItemAlbumRankModel) {
        if (mIsRecommendChannel) {
            // 新首页-排行榜-专辑item  点击事件
            XMTraceApi.Trace()
                .click(29652)
                .put("albumId", album.id.toString())
                .put("moduleId", model.moduleId.toString())
                .put("currPage", "homePageV2")
                .put("type", AlbumUnlockABManager.getHomeLeaderBoardConfig().toString())
                .createTrace()
        } else {
            // 新首页-排行榜-专辑item  点击事件
            XMTraceApi.Trace()
                .click(29752)
                .put("albumId", album.id.toString())
                .put("moduleId", model.moduleId.toString())
                .put("currPage", "homePageV2")
                .put("type", AlbumUnlockABManager.getHomeLeaderBoardConfig().toString())
                .createTrace()
        }

        // 排行榜-点击-校验  其他事件
        XMTraceApi.Trace()
            .setMetaId(58187)
            .setServiceId("others")
            .put("albumId", album.id.toString())
            .put("moduleId", model.moduleId.toString())
            .put("currPage", "homePageV2")
            .put("type", AlbumUnlockABManager.getHomeLeaderBoardConfig().toString())
            .createTrace()

        AlbumEventManage.startMatchAlbumFragment(
            album,
            AlbumEventManage.FROM_DISCOVERY_CATEGORY,
            0,
            album.recSrc,
            album.recTrack,
            -1,
            fragment.activity
        )
    }

    override fun getItem(position: Int): Any? {
        if (CollectionUtil.isNotEmpty(mAlbumMList) && position >= 0 && position < mAlbumMList.size) {
            return mAlbumMList[position]
        }
        return null
    }

    /**
     * 获取标签列表的数据
     */
    private fun getAlbumTagSourceListString(albumItem: AlbumM): String? {
        val tagResults = albumItem.tagResults
        if (tagResults == null || tagResults.isEmpty()) {
            return null
        }
        return tagResults[0]?.tagName
    }

    class AlbumRankViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        //专辑图
        val ivAlbumCover: ImageView = itemView.findViewById(R.id.main_iv_album_cover)

        //排行榜数字的背景
        val ivRankNumber: ImageView = itemView.findViewById(R.id.main_iv_rank_number)

        //排行榜数字文本
        val tvRankNumber: TextView = itemView.findViewById(R.id.main_tv_rank_number)

        //专辑评分
        val tvAlbumScore: TextView = itemView.findViewById(R.id.main_album_score)

        //专辑标题
        val tvAlbumTitle: TextView = itemView.findViewById(R.id.main_tv_album_title)

        //专辑tag
        val tvAlbumTagSourceList: TextView = itemView.findViewById(R.id.main_tag_source_list)

        val tvAlbumLike: TextView? = itemView.findViewById(R.id.main_tv_album_like)
    }

}