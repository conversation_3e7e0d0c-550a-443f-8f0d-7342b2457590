package com.ximalaya.ting.lite.main.home.manager;

import android.app.Activity;
import android.graphics.Rect;
import android.view.View;
import android.widget.ListView;


import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.view.CustomTipsView;
import com.ximalaya.ting.android.host.view.ShadowView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendAdapter;
import com.ximalaya.ting.lite.main.home.fragment.HomeRecommendFragment;

import java.util.ArrayList;
import java.util.List;

import static com.ximalaya.ting.android.host.view.CustomTipsView.Tips.TIP_MASK;

/**
 * Created by qinhuifeng on 2020/12/1
 *
 * <AUTHOR>
 */
public class HomeTingUpdateMaskManager {
    //原本听更新引导的key，废弃掉
    //public static String MMKV_TING_UPDATA_GUIDE_HAS_SHOW = "mmkv_ting_updata_guide_has_show";
    private static String MMKV_TING_UPDATA_GUIDE_HAS_SHOW = "mmkv_subscribe_history_guide_has_show";
    //是否需要展示听更新的引导
    public static boolean isNeedShowTingUpdateMask = false;
    public static boolean isDelayDealing = false;

    public static void setNeedShowTingUpdateMask() {
        isNeedShowTingUpdateMask = true;
    }

    public static void checkTingUpdataAndShow(Activity mContext, HomeRecommendAdapter adapter, HomeRecommendFragment recommendFragment, int delayShow) {
        if (!isNeedShowTingUpdateMask) {
            return;
        }
        if (delayShow < 0) {
            delayShow = 0;
        }
        if (isDelayDealing) {
            return;
        }
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                checkTingUpdataItemView(mContext, adapter, recommendFragment);
                //延时处理结束
                isDelayDealing = false;
            }
        }, delayShow);
    }

    public static void checkTingUpdataItemView(Activity mContext, HomeRecommendAdapter adapter, HomeRecommendFragment recommendFragment) {
        //不需要展示，后续不在执行
        if (!isNeedShowTingUpdateMask) {
            return;
        }
        if (mContext == null || adapter == null || recommendFragment == null || recommendFragment.getRefreshLoadMoreListView() == null) {
            return;
        }
        if (mContext.isFinishing() || mContext.getWindowManager() == null) {
            return;
        }
        //页面没有真实可见不处理
        if (!recommendFragment.isRealVisable()) {
            return;
        }
        MmkvCommonUtil mmkvCommonUtil = MmkvCommonUtil.getInstance(mContext);
        boolean hasShow = mmkvCommonUtil.getBoolean(MMKV_TING_UPDATA_GUIDE_HAS_SHOW, false);
        //之前已经show过了，下次不再展示
        if (hasShow) {
            isNeedShowTingUpdateMask = false;
            return;
        }
        List<ItemModel> listData = adapter.getListData();
        if (listData == null || listData.size() == 0) {
            return;
        }
        int tingUpdataIndex = -1;
        for (int i = 0; i < listData.size(); i++) {
            ItemModel itemModel = listData.get(i);
            if (itemModel == null) {
                continue;
            }
            if (itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_MY_SUBSCRIPTION) {
                tingUpdataIndex = i;
                break;
            }
            //最多只取前20个进行判断
            if (i > 20) {
                break;
            }
        }
        if (tingUpdataIndex < 0) {
            return;
        }
        final ListView listView = recommendFragment.getRefreshLoadMoreListView().getRefreshableView();
        if (listView == null) {
            return;
        }
        //获取真实的position
        final int realPosition = tingUpdataIndex + listView.getHeaderViewsCount();
        //选中ListView中对应的item
        int lastVisiblePosition = listView.getLastVisiblePosition();
        int firstVisiblePosition = listView.getFirstVisiblePosition();
        if (realPosition < firstVisiblePosition || realPosition > lastVisiblePosition) {
            return;
        }
        int index = realPosition - firstVisiblePosition;
        //获取听更新itemview
        View tingUpItemView = listView.getChildAt(index);
        if (tingUpItemView == null || tingUpItemView.getWindowToken() == null) {
            return;
        }
        boolean visibleLocal = isVisibleLocal(tingUpItemView);
        if (!visibleLocal) {
            return;
        }
        int[] locations = new int[2];
        tingUpItemView.getLocationInWindow(locations);
        //听更新是可见状态
        final List<CustomTipsView.Tips> tipsList = new ArrayList<>(1);
        String content = "您订阅的节目在这里收听哦";
        int height = BaseUtil.dp2px(mContext, 175);
        CustomTipsView.Tips tips = new CustomTipsView.Tips.Builder(content, tingUpItemView, "")
                .setShape(ShadowView.Focus.SHAPE_ROUND_RECT)
                .setDirection(CustomTipsView.UP)
                .setDismissCallback(new CustomTipsView.DismissCallback() {
                    @Override
                    public void onDismissed() {

                    }
                })
                .setLevel(TIP_MASK)
                .setTvBgDrawable(R.drawable.host_bg_rect_e83f46_radius_100)
                .setMarginX(-BaseUtil.dp2px(mContext, 6))
                .setShowFinger(false)
                .setInterceptShadowClickFocus(true)
                .setArrowDrawable(R.drawable.host_ic_ting_updata_down_arrow)
                .setRadius(BaseUtil.dp2px(mContext, 4))
                .setHeight(height)
                .setY((locations[1] + height / 2))
                .setAutoDismiss(false)
                .setAutoSaveKeyToSp(false)
                .create();
        tipsList.add(tips);
        CustomTipsView tipsView = new CustomTipsView(mContext, R.layout.host_layout_tips_ting_updata_item_guide);
        tipsView.setTipsMap(tipsList);
        //设置正在展示
        MmkvCommonUtil.getInstance(mContext).saveBoolean(MMKV_TING_UPDATA_GUIDE_HAS_SHOW, true);
        tipsView.showAllTips();
    }

    /**
     * View 是否可见
     */
    private static boolean isVisibleLocal(View target) {
        if (target == null) {
            return false;
        }
        int viewHeight = target.getHeight();
        if (viewHeight == 0) {
            return false;
        }
        Rect rect = new Rect();
        boolean isVisibleRect = target.getGlobalVisibleRect(rect);
        int diff = rect.bottom - rect.top;
        //可见区域达到60%认为是可见了
        int checkVisible = (int) (viewHeight * 0.6f);
        return isVisibleRect && diff >= checkVisible;
    }
}
