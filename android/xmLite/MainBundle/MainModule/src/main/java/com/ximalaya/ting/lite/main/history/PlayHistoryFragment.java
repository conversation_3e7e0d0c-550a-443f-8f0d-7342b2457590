package com.ximalaya.ting.lite.main.history;

import android.app.ProgressDialog;
import android.os.Bundle;
import android.os.SystemClock;
import android.util.Log;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.AlbumTypeModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.cloudhistory.CloudHistoryModel;
import com.ximalaya.ting.android.host.model.cloudhistory.CloudHistroyListenModel;
import com.ximalaya.ting.android.host.model.duanju.XmDuanJuItemTransferModel;
import com.ximalaya.ting.android.host.util.common.TimeHelper;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.base.IXmDataChangedCallback;
import com.ximalaya.ting.android.routeservice.service.history.ICloudyHistory;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.manager.LiteNoSupportManager;
import com.ximalaya.ting.lite.main.mylisten.inter.IHistoryCallBack;
import com.ximalaya.ting.lite.main.mylisten.view.AllHistoryFragment;
import com.ximalaya.ting.lite.main.play.dialog.PlayNoCopyRightDialog;
import com.ximalaya.ting.lite.main.shortplay.XmShortPlayDetailFragment;
import com.ximalaya.ting.lite.main.shortplay.utils.CheckAlbumUtil;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 播放历史记录
 *
 * <AUTHOR>
 */

public class PlayHistoryFragment extends BaseFragment2 implements
        OnItemClickListener, IXmDataChangedCallback, IHistoryCallBack {

    private static final String TAG = "PlayHistoryFragment";
    /**
     * 请求在运行中
     */
    private static volatile boolean isRequestRunning;
    private final List<Album> mData = new ArrayList<>();
    private @Nullable
    RefreshLoadMoreListView mListView;
    private final IGotoTop.IGotoTopBtnClickListener mTopBtnListener =
            new IGotoTop.IGotoTopBtnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mListView != null && mListView.getRefreshableView() != null) {
                        mListView.getRefreshableView().setSelection(0);
                    }
                }
            };
    private @Nullable
    HistoryAlbumAdapter mAdapter;
    private @Nullable
    ProgressDialog mDialog;
    private boolean isFirstLoad = true;
    private boolean isChooseType = false;    // 是否是选择模式
    private boolean mPlayFirst = false;
    /**
     * 是否注册数据变更回调
     */
    private volatile boolean isRegisterDataChangeCallback;
    private long mCurTime;

    public PlayHistoryFragment() {
        super(false, null);
    }

    private static void playHistoryForItingFirstPlay(PlayHistoryFragment playHistoryFragment,
                                                     final HistoryModel model) {
        if (model == null || playHistoryFragment == null)
            return;

        final Track t = model.getTrack();
        if (t == null || t.getDataId() <= 0) {
            return;
        }
        //埋点结束
        if (t.isPayTrack() && !UserInfoMannage.hasLogined()) {
            return;
        }
        //设置声音的播放点
        if (t.getDataId() > 0 && t.getLastPlayedMills() > 0) {
            XmPlayerManager.getInstance(playHistoryFragment.mContext).setHistoryPos(t.getDataId()
                    , t.getLastPlayedMills());
        }
        PlayTools.playTrackHistoy(playHistoryFragment.mContext, true, t, null, false);
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Bundle args = getArguments();
        if (args != null) {
            isChooseType = args.getBoolean(BundleKeyConstants.KEY_IS_CHOOSE_TYPE, false);
            mPlayFirst = args.getBoolean(BundleKeyConstants.KEY_PLAY_FIRST, false);
        }

        mListView = findViewById(R.id.main_listview);
        mListView.setMode(Mode.DISABLED);
        mListView.addOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                if (mGlobalFloatView != null) {
                    mGlobalFloatView.startAnimation(scrollState != SCROLL_STATE_IDLE);
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount,
                                 int totalItemCount) {
                if (getiGotoTop() != null) {
                    getiGotoTop().setState(firstVisibleItem > 12);
                }
            }
        });

        mAdapter = new HistoryAlbumAdapter((MainActivity) mActivity, mData, isChooseType);
        mAdapter.mFragment = this;
        mListView.setAdapter(mAdapter);
        mListView.setOnItemClickListener(this);
        mDialog = ToolUtil.createProgressDialog(getActivity(), "正在获取声音列表");
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38324;
        super.onMyResume();
        Log.e("PlayHistoryFragment=", "onMyResume==" );
        Logger.i(TAG, "onMyResume loadData");
        loadData();
        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(mTopBtnListener);
        }
        if (!isRegisterDataChangeCallback) {
            isRegisterDataChangeCallback = true;
            IHistoryManagerForMain historyManager =
                    RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
            if (historyManager != null) {
                historyManager.registerOnHistoryUpdateListener(this);
            }
        }
    }


    @Override
    public void onPause() {
        super.onPause();
        Log.e("PlayHistoryFragment=", "onPause==" );
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
    }

    @Override
    protected void loadData() {
        if (isRealVisable()) {
            if (canUpdateUi() && isFirstLoad) {
                onPageLoadingCompleted(LoadCompleteType.LOADING);
            }
            // 防止重复触发  onDataChanged会回调多次
            if (!isRequestRunning) {
                Logger.i(TAG, "loadData");
                isRequestRunning = true;
                new LoadTask(this).myexec();
            }
        }
    }

    private void requestPlayHistory() {
        if (!UserInfoMannage.hasLogined()) {
            return;
        }

        CommonRequestM.getInstanse().getCloudHistory(new HashMap<>(),
                new IDataCallBack<CloudHistoryModel>() {
                    @Override
                    public void onSuccess(@Nullable CloudHistoryModel object) {
                        if (!canUpdateUi()
                                || object == null
                                || ToolUtil.isEmptyCollects(object.getListenModels())
                                || mAdapter == null
                                || ToolUtil.isEmptyCollects(mAdapter.getListData())) {
                            return;
                        }

                        List<Album> adapterList = mAdapter.getListData();
                        List<CloudHistroyListenModel> netHistoryList = object.getListenModels();
                        for (Album album : adapterList) {
                            AlbumM albumM = (AlbumM) album;
                            for (CloudHistroyListenModel history : netHistoryList) {
                                // 同一专辑
                                if (albumM.getId() == history.getItemId()) {
                                    // 同一专辑下 声音是不是同一个 不是同一个不覆盖刷新数据
                                    HistoryModel historyModel = albumM.getHistoryModel();
                                    if (historyModel != null && historyModel.getTrack() != null
                                            && historyModel.getTrack().getDataId() != history.getChildId()) {
                                        continue;
                                    }

                                    albumM.setIsPaid(history.isPaid());
                                    albumM.setActivityTag(history.getActivityTag());
                                    albumM.setCampGroupId(history.getCampGroupId());
                                    albumM.setVipFree(history.isVipFree());
                                    albumM.setVipFreeType(history.getVipFreeType());
                                    albumM.setType(history.getType());
                                    break;
                                }
                            }
                        }
                        mAdapter.notifyDataSetChanged();
                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                });
    }

    @Override
    public void clearAll() {
        if (mAdapter == null || mAdapter.getCount() == 0) {
            return;
        }

        // 清空播放历史弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(39439)
                .setServiceId("dialogView")
                .put("currPage", "historyPage")
                .createTrace();

        new DialogBuilder<>(getActivity())
                .setMessage(R.string.main_confirm_clean_history)
                .setOkBtn(() -> {
                    // 清空播放历史弹窗-确认点击  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(39440)
                            .setServiceId("dialogClick")
                            .put("currPage", "historyPage")
                            .createTrace();

                    if (mAdapter != null) {
                        mAdapter.clear();
                    }
                    ICloudyHistory historyManager =
                            RouterServiceManager.getInstance().getService(ICloudyHistory.class);
                    if (historyManager != null) {
                        historyManager.clearAllPlayHistory(true);
                    }
                    //清空以后，重新加载一次数据来正确显示空界面
                    loadData();
                })
                .setCancelBtn(() -> {
                    // 清空播放历史弹窗-取消点击  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(39441)
                            .setServiceId("dialogClick")
                            .put("currPage", "historyPage")
                            .createTrace();
                })
                .showConfirm();
    }

    public void checkDelBtnVisible(int visible) {
        Fragment fragment = getParentFragment();
        if (fragment instanceof AllHistoryFragment) {
            AllHistoryFragment allHistoryFragment = (AllHistoryFragment) fragment;
            allHistoryFragment.checkDelBtnVisible(visible, 0);
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_history;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        Log.e("PlayHistoryFragment=", "setUserVisibleHint==isVisibleToUser:"+isVisibleToUser);
        if (isRealVisable()) {
            Logger.i(TAG, "setUserVisibleHint loadData");
            loadData();
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        Log.e("PlayHistoryFragment=", "onHiddenChanged==hidden:"+hidden);
    }

    @Override
    public void onDataChanged() {
        // 触发同步历史数据会导致回调触发  回调有时会同时触发两次
        long sysTime = SystemClock.elapsedRealtime();
        if (sysTime - mCurTime > 1000L) {
            Logger.i(TAG, "onDataChanged loadData");
            mCurTime = sysTime;
            loadData();
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, final View view,
                            int position, long id) {
        if (!OneClickHelper.getInstance().onClick(view) || mAdapter == null || mListView == null ||
                mListView.getRefreshableView() == null) {
            return;
        }
        int index = position
                - mListView.getRefreshableView().getHeaderViewsCount();

        if (index < 0 || index >= mAdapter.getCount()) {
            return;
        }

        final AlbumM albumM = (AlbumM) mAdapter.getItem(index);
        if (albumM == null)
            return;

        final HistoryModel model = albumM.getHistoryModel();
        if (model == null)
            return;

        //检测并且弹出弹出不支持弹框
        if (LiteNoSupportManager.checkAndShowLiteNoSupportDialogForHistory(getChildFragmentManager(), model)) {
            //已经弹出，return
            return;
        }

        final Track t = model.getTrack();
        if (t == null || t.getDataId() <= 0) {
            return;
        }

        if (t.isPayTrack() && !UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(getActivity());
            return;
        }


        //设置声音的播放点
        if (t.getDataId() > 0 && t.getLastPlayedMills() > 0) {
            XmPlayerManager.getInstance(mContext).setHistoryPos(t.getDataId(),
                    t.getLastPlayedMills());
        }

        if (ChildProtectManager.isChildProtectOpen(mContext)) {
            CommonRequestM.getAlbumInfo(albumM.getId() + "", t.getDataId() + "",
                    new IDataCallBack<AlbumM>() {
                        @Override
                        public void onSuccess(@Nullable AlbumM object) {
                            if (object != null && object.getAgeLevel() == 0) {
                                checkStartVideoPlay(model, t);
                            } else {
                                CustomToast.showToast(getString(R.string.host_teenager_protect_cannot_play));
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            CustomToast.showToast(getString(R.string.host_teenager_protect_cannot_play));
                        }
                    });

        } else {
            checkStartVideoPlay(model, t);
        }
    }

    private void checkStartVideoPlay(final HistoryModel model, final Track t) {
        CheckAlbumUtil.getAlbumType(model.getAlbumId(), new IDataCallBack<AlbumTypeModel>() {
            @Override
            public void onSuccess(@Nullable AlbumTypeModel result) {
                if (result != null && result.isStartVideoPage()) {
                    XmDuanJuItemTransferModel xmDuanJuItemTransferModel=XmDuanJuItemTransferModel.createForAlbumTypeModel(result);
                    startFragment(XmShortPlayDetailFragment.newInstance(xmDuanJuItemTransferModel));
                } else {
                    playTrackHistory(model, t);
                }
            }

            @Override
            public void onError(int code, String message) {
                playTrackHistory(model, t);
            }
        });
    }

    private void playTrackHistory(final HistoryModel model, final Track t) {
        if (mDialog != null) {
            mDialog.show();
        }
        PlayTools.playTrackHistoy(mActivity, true, t, new PlayTools.IplayTrackHistoryCallback() {
            @Override
            public void onSuccess() {
                if (!canUpdateUi()) {
                    return;
                }
                if (mDialog != null) {
                    mDialog.dismiss();
                }
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }
                if (code == 702 || code == 924) {
                    PlayNoCopyRightDialog playNoCopyRightDialog = null;
                    if (model != null && model.getTrack() != null) {
                        playNoCopyRightDialog =
                                PlayNoCopyRightDialog.newInstance(model.getTrack().getDataId(),
                                        model.getTrack().getRecSrc(), model.getTrack().getRecTrack());
                    } else {
                        playNoCopyRightDialog = new PlayNoCopyRightDialog();
                    }
                    playNoCopyRightDialog.show(getChildFragmentManager(),
                            PlayNoCopyRightDialog.TAG);
                } else {
                    CustomToast.showFailToast(message);
                }
                if (mDialog != null) {
                    mDialog.dismiss();
                }
            }
        });

    }

    @Override
    protected boolean onPrepareNoContentView() {
        setNoContentImageView(R.drawable.host_no_content);
        setNoContentTitle("没有收听过节目");
        return false;
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.e("PlayHistoryFragment=", "onResume==" );
    }

    @Override
    public void onStop() {
        super.onStop();
        if (isRegisterDataChangeCallback) {
            isRegisterDataChangeCallback = false;
            IHistoryManagerForMain historyManager =
                    RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
            if (historyManager != null) {
                historyManager.unRegisterOnHistoryUpdateListener(this);
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        isRequestRunning = false;
    }

    private static class LoadTask extends MyAsyncTask<Void, Void, List<HistoryModel>> {

        private final WeakReference<PlayHistoryFragment> mHistoryFragmentRef;

        LoadTask(PlayHistoryFragment playHistoryFragment) {
            mHistoryFragmentRef = new WeakReference<>(playHistoryFragment);
        }

        @Override
        protected @Nullable
        List<HistoryModel> doInBackground(Void... params) {
            PlayHistoryFragment playHistoryFragment = mHistoryFragmentRef.get();
            if (playHistoryFragment == null) {
                return null;
            }

            List<HistoryModel> historyModels = new ArrayList<>();
            IHistoryManagerForMain historyManager =
                    RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
            if (historyManager != null) {
                historyModels = historyManager.getTrackList();
            }
            if (historyModels == null) {
                return null;
            }

            CopyOnWriteArrayList<HistoryModel> copyTracks;
            if (playHistoryFragment.isChooseType) {
                copyTracks = new CopyOnWriteArrayList<>();
                for (HistoryModel historyModel : historyModels) {
                    if (historyModel.getTrack() != null) {
                        copyTracks.add(historyModel);
                    }
                }
            } else {
                copyTracks = new CopyOnWriteArrayList<>(historyModels);
            }

            // 加入去除一键听播放历史逻辑 (如果在一键听状态下强杀app，开启app声音会出现在历史记录里面)
            int size = copyTracks.size();
            for (int i = size - 1; i >= 0; i--) {
                HistoryModel historyModel = copyTracks.get(i);
                if (historyModel != null && historyModel.getTrack() != null) {
                    if (historyModel.getTrack().getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY) {
                        copyTracks.remove(i);
                    }
                }
            }

            return copyTracks;
        }

        @Override
        protected void onPostExecute(final List<HistoryModel> result) {
            super.onPostExecute(result);

            final PlayHistoryFragment playHistoryFragment = mHistoryFragmentRef.get();
            if (playHistoryFragment == null) {
                isRequestRunning = false;
                return;
            }

            playHistoryFragment.doAfterAnimation(() -> {
                if (!playHistoryFragment.canUpdateUi()) {
                    isRequestRunning = false;
                    return;
                }
                if (playHistoryFragment.mAdapter != null) {
                    playHistoryFragment.mAdapter.clear();
                }
                if (result != null && !result.isEmpty()) {
                    boolean isToday = false;
                    boolean isYesterDay = false;
                    boolean isOtherDay = false;

                    HistoryModel historyModelFirst = result.get(0);
                    if (playHistoryFragment.mPlayFirst && historyModelFirst != null) {
                        playHistoryFragment.mPlayFirst = false;
                        playHistoryForItingFirstPlay(playHistoryFragment, historyModelFirst);
                    }

                    for (HistoryModel historyModel : result) {
                        if (historyModel != null && !historyModel.isDeleted()) {
                            AlbumM albumM = new AlbumM();
                            albumM.setType(historyModel.getType());
                            //历史页面添加时间title---------start
                            long endedAt = historyModel.getEndedAt() != 0 ?
                                    historyModel.getEndedAt() : historyModel.getUpdateAt();
                            boolean isSetTodayTag =
                                    !isToday && (TimeHelper.isToday(endedAt) || endedAt >= System.currentTimeMillis());
                            if (isSetTodayTag) {
                                albumM.setTimeTag("今天");
                                isToday = true;
                            } else if (!isYesterDay && TimeHelper.isYesterday(endedAt)) {
                                albumM.setTimeTag("昨天");
                                isYesterDay = true;
                            } else if (!isOtherDay && !TimeHelper.isToday(endedAt) && !TimeHelper.isYesterday(endedAt) && endedAt < System.currentTimeMillis()) {
                                albumM.setTimeTag("更早");
                                isOtherDay = true;
                            }
                            //历史页面添加时间title---------end
                            albumM.setHistoryModel(historyModel);
                            if (historyModel.isRadio) {
                                albumM.setAlbumTitle(historyModel.getAlbumTitle());
                                albumM.setCoverUrlMiddle(historyModel.getRadio().getValidCover());
                            } else {
                                albumM.setId(historyModel.getAlbumId());
                                albumM.setAlbumTitle(historyModel.getAlbumTitle());
                                albumM.setCoverUrlMiddle(historyModel.getTrack().getValidCover());
                                albumM.setIsPaid(historyModel.getTrack().isPayTrack() || historyModel.getTrack().vipPriorListenStatus == 1);
                            }

                            // 同步vip状态给专辑对象
                            Track track = historyModel.getTrack();
                            if (track != null) {
                                albumM.setVipFree(track.isVipFree());
                                albumM.setVipFreeType(track.getVipFreeType());
                            }

                            if (playHistoryFragment.mAdapter != null && playHistoryFragment.mAdapter.getListData() != null) {
                                playHistoryFragment.mAdapter.getListData().add(albumM);
                            }
                        }
                    }
                    if (playHistoryFragment.mAdapter != null) {
                        playHistoryFragment.mAdapter.notifyDataSetChanged();
                    }
                    if (playHistoryFragment.mData.isEmpty()) {
                        playHistoryFragment.onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                        playHistoryFragment.checkDelBtnVisible(View.GONE);
                    } else {
                        playHistoryFragment.onPageLoadingCompleted(LoadCompleteType.OK);
                        playHistoryFragment.checkDelBtnVisible(View.VISIBLE);
                    }
                } else {
                    playHistoryFragment.onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                    playHistoryFragment.checkDelBtnVisible(View.GONE);
                }
                playHistoryFragment.requestPlayHistory();


                if (playHistoryFragment.isFirstLoad) {
                    ICloudyHistory historyManager =
                            RouterServiceManager.getInstance().getService(ICloudyHistory.class);
                    if (historyManager != null) {
                        historyManager.syncCloudHistory(true);  //同步云历史
                    }
                }
                playHistoryFragment.isFirstLoad = false;

                isRequestRunning = false;
            });

        }
    }
}
