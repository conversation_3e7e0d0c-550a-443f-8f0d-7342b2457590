package com.ximalaya.ting.lite.main.comment

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.*
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.manager.dialog.DialogWeakReferenceManager
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.comment.fragment.AlbumCommentReplyListFragment
import com.ximalaya.ting.lite.main.comment.fragment.AlbumCommentTabFragment
import com.ximalaya.ting.lite.main.comment.fragment.PlayerCommentTabFragment


/**
 *  @Author: Junxiang Cheng
 *  @Mail: <EMAIL>
 *  @CreateTime: 12/23/21
 *
 *  @Description: 播放页-评论弹框
 */
class CommentDetailDialogFragment : BaseDialogFragment<CommentDetailDialogFragment>() {
    companion object {
        const val TAG = "CommentDetailDialogFragment"

        const val TAG_COMMENT_LIST = "Comment"
        const val TAG_REPLY_LIST = "Reply"

        fun getInstance(bundle: Bundle): CommentDetailDialogFragment {
            val fra = CommentDetailDialogFragment()
            fra.arguments = bundle
            return fra
        }
    }

    lateinit var commentTabFragment: PlayerCommentTabFragment
    var rootView: RelativeLayout? = null
    var flContainer: FrameLayout? = null
    var outsideView: View? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog?.setCanceledOnTouchOutside(true)
        val window = dialog?.window ?: return null

        val mView = inflater.inflate(
            R.layout.main_fra_comment_main,
            window.findViewById(android.R.id.content),
            false
        )
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, BaseUtil.getScreenHeight(activity))
        window.setGravity(Gravity.BOTTOM)
        window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT)) //注意此处

        window.setWindowAnimations(R.style.host_dialog_push_in_out)

        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)

        if (mView == null) {
            return null
        }
        rootView = mView.findViewById(R.id.main_root_view)
        flContainer = mView.findViewById(R.id.main_fl_fragment_container)
        outsideView = mView.findViewById(R.id.main_view_fragment_outside)
        outsideView?.layoutParams?.also {
            it.height = 0
        }
        return mView
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        initUI()
    }

    private fun initUI() {
        startReplayList(arguments)
        outsideView?.setOnClickListener {
            dialog?.cancel()
        }
    }

    override fun show(transaction: FragmentTransaction, tag: String?): Int {
        DialogWeakReferenceManager.getInstance().createCommentListDialogRef(this)
        return super.show(transaction, tag)
    }

    override fun show(manager: FragmentManager, tag: String?) {
        DialogWeakReferenceManager.getInstance().createCommentListDialogRef(this)
        super.show(manager, tag)
    }


    fun startReplayList(bundle: Bundle?) {
        if (requireActivity().isFinishing || requireActivity().isDestroyed) {
            return
        }
        if (!canUpdateUi()) {
            return
        }
        val transaction = childFragmentManager.beginTransaction()
        val commentReplyPresenter = if (parentFragment is AlbumCommentTabFragment) {
            (parentFragment as AlbumCommentTabFragment).getCommentPresent()
        } else {
            CommentListPresenter()
        }
        if (commentReplyPresenter == null) return
        val replyFragment = AlbumCommentReplyListFragment.getInstance(commentReplyPresenter, bundle)
        transaction.add(R.id.main_fl_fragment_container, replyFragment, TAG_REPLY_LIST)
        transaction.commitNowAllowingStateLoss()

    }

    fun returnCommentList() {
        if (requireActivity().isFinishing || requireActivity().isDestroyed) {
            return
        }
        if (!canUpdateUi()) {
            return
        }
        val transaction = childFragmentManager.beginTransaction()
        var replyFragment =
            childFragmentManager.findFragmentByTag(CommentDialogFragmentMain.TAG_REPLY_LIST)
        if (replyFragment != null) {
            transaction.hide(replyFragment)
        }
        if (replyFragment != null) {
            transaction.remove(replyFragment)
        }
        transaction.commitNowAllowingStateLoss()
        dismiss()
    }

    override fun dismiss() {
        super.dismiss()
    }
}