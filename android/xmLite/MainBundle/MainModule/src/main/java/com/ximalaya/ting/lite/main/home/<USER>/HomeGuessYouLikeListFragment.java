package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;

import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.manager.customize.CustomizeManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.user.InterestCardModel;
import com.ximalaya.ting.android.host.util.RequestParamsUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeGuessYouLikeViewModel;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.lite.main.request.HttpParamsConstantsInMain;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 猜你喜欢页面
 *
 * <AUTHOR>
 */
public class HomeGuessYouLikeListFragment extends BaseFragment2 implements IRefreshLoadMoreListener, AdapterView.OnItemClickListener {
    private static final String KEY_GUESS_YOU_LIKE_VIEWMODEL = "key_guess_you_like_viewmodel";

    private RefreshLoadMoreListView mListView;
    private int mPageId = 1;
    private boolean mIsLoading = false;
    private AlbumAdapter mAdapter;
    private ImageView vNocontent;
    private HomeGuessYouLikeViewModel mGuessYouLikeViewModel;
    private List<Album> albumList = new ArrayList<>();

    private int from = -1;

    /**
     * 作为单一页面使用，所需要的参数
     * <p>
     * 设置状态栏为true
     */
    public static Bundle createArgumentFromSinglePage(HomeGuessYouLikeViewModel model, int from) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(KEY_GUESS_YOU_LIKE_VIEWMODEL, model);
        bundle.putInt(BundleKeyConstantsInMain.KEY_FROM, from);

        return bundle;
    }

    @Override
    public void onRefresh() {
        refresh();
    }

    private void refresh() {
        mPageId = 1;
        if (mListView != null) {
            mListView.setFooterViewVisible(View.VISIBLE);
        }
        loadData();
    }

    @Override
    public void onMore() {
        loadData();
    }

    @Override
    public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        List<Album> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (index < 0 || index >= listData.size()) {
            return;
        }
        Album album = listData.get(index);
        if (!(album instanceof AlbumM)) {
            return;
        }
        AlbumM albumM = (AlbumM) album;
        AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mGuessYouLikeViewModel = arguments.getParcelable(KEY_GUESS_YOU_LIKE_VIEWMODEL);
            from = arguments.getInt(BundleKeyConstants.KEY_FROM, -1);
        }
        if (mGuessYouLikeViewModel == null) {
            mGuessYouLikeViewModel = new HomeGuessYouLikeViewModel();
            mGuessYouLikeViewModel.title = "猜你喜欢";
        }
        if (TextUtils.isEmpty(mGuessYouLikeViewModel.title)) {
            mGuessYouLikeViewModel.title = "猜你喜欢";
        }
        //需要标题栏，设置为可滑动返回
        setCanSlided(true);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mListView = findViewById(R.id.main_listview);
        mAdapter = new AlbumAdapter(mActivity, albumList);
        initFooterView();

        mListView.setAdapter(mAdapter);
        mListView.setOnRefreshLoadMoreListener(this);
        mListView.setOnItemClickListener(this);
        mListView.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {

            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (getiGotoTop() != null) {
                    getiGotoTop().setState(firstVisibleItem > 12);
                }
            }
        });
        setTitle(mGuessYouLikeViewModel.title);
    }

    private final IGotoTop.IGotoTopBtnClickListener mTopBtnListener = new IGotoTop.IGotoTopBtnClickListener() {
        @Override
        public void onClick(View v) {
            if (!isRealVisable()) {
                return;
            }
            if (mListView == null) {
                return;
            }
            mListView.getRefreshableView().setSelection(0);
        }
    };

    private void initFooterView() {
        LinearLayout ll = new LinearLayout(getActivity());
        ll.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        ll.setGravity(Gravity.CENTER);
        vNocontent = new ImageView(getActivity());
        vNocontent.setPadding(0, BaseUtil.dp2px(mContext, 30), 0, 0);
        vNocontent.setImageResource(R.drawable.main_bg_meta_nocontent);
        ll.addView(vNocontent);
        vNocontent.setVisibility(View.GONE);
        mListView.getRefreshableView().addFooterView(ll);
    }

    @Override
    protected void loadData() {
        if (mIsLoading) {
            return;
        }
        if (canUpdateUi() && mAdapter != null && mAdapter.getCount() == 0) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        mIsLoading = true;

        loadGuessLikeListRefresh();
    }

    private void loadGuessLikeListRefresh() {
        //使用的是换一换的那个接口
        Map<String, String> p = new HashMap<>();
        p.put(HttpParamsConstantsInMain.PARAM_USE_RECOMMEND_MODEL, "false");
        p.put("pageId", mPageId + "");
        p.put("pageSize", "20");
        p.put("moduleId", mGuessYouLikeViewModel.moduleId);
        p.put("channelId", mGuessYouLikeViewModel.channelId + "");
        int categoryId = mGuessYouLikeViewModel.categoryId;
        //如果是首页没有categoryId，传入-1
        //兼容老的逻辑，默认值的时候必须传-1，兼容老的接口
        if (categoryId == 0) {
            categoryId = -1;
        }
        p.put("categoryId", categoryId + "");
        p = RequestParamsUtil.addVipShowParam(p);
        //本地如果记录了兴趣卡片
        InterestCardModel interestCardModel = CustomizeManager.getInstance().getInterestCardModel();
        if (interestCardModel != null) {
            p.put("ageRange", interestCardModel.ageRange);
            p.put("gender", interestCardModel.gender + "");
        }
        if (from == BundleValueConstantsInMain.FROM_VIP_PAGE) {
            p.put("vipPage", "1");
        }
        p.put("curInterest", mGuessYouLikeViewModel.interestId);

        IDataCallBack<MainAlbumMList> callBack = new IDataCallBack<MainAlbumMList>() {
            @Override
            public void onSuccess(final MainAlbumMList object) {
                mIsLoading = false;
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        boolean isNoContent = object == null || object.getList() == null || object.getList().size() <= 0;
                        if (isNoContent) {
                            mListView.setHasMoreNoFooterView(false);
                            List<Album> listData = mAdapter.getListData();
                            if (listData == null || listData.size() == 0) {
                                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                            }
                            return;
                        }
                        //加载数据
                        onLoadSuccess(object);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                onLoadFailure(message);
            }
        };
        LiteCommonRequest.getGuessYouLikePageLite(p, callBack);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_category_detail_guess_you_like;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    /**
     * 数据加载成功
     */
    private void onLoadSuccess(final MainAlbumMList data) {
        List<AlbumM> list = data.getList();
        if (list == null || list.size() == 0) {
            return;
        }
        List<Album> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (mPageId == 1) {
            listData.clear();
        }
        listData.addAll(data.getList());
        if (data.getMaxPageId() > mPageId) {
            //还有下一页
            mPageId++;
            mListView.onRefreshComplete(true);
        } else {
            //已经没有下一页数据了
            mListView.onRefreshComplete(false);
        }
    }

    /**
     * 数据加载失败
     */
    private void onLoadFailure(String message) {
        mIsLoading = false;
        if (!canUpdateUi()) {
            return;
        }
        if (mPageId == 1) {
            mAdapter.clear();
            mListView.onRefreshComplete(true);
            mListView.setHasMoreNoFooterView(false);
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        } else {
            CustomToast.showFailToast(message);
            mListView.onRefreshComplete(true);
        }
    }

    @Override
    protected void loadDataError() {
        if (mListView != null) {
            mListView.setMode(Mode.DISABLED);
            mListView.setHasMoreNoFooterView(false);
        }
    }

    @Override
    protected void loadDataOk() {
        if (mListView != null) {
            mListView.setMode(Mode.PULL_FROM_START);
        }
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }


    @Override
    protected String getPageLogicName() {
        return "HomeGuessYouLikeListFragment";
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
    }

    @Override
    public void onResume() {
        super.onResume();

        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(mTopBtnListener);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
    }
}
