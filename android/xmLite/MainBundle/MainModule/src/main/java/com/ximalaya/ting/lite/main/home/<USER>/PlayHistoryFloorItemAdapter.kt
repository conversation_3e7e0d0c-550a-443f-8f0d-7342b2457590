package com.ximalaya.ting.lite.main.home.adapter

import android.content.Context
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.AlbumTagUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.server.PlayTools.IplayTrackHistoryCallback
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder
import com.ximalaya.ting.lite.main.home.viewmodel.HomeItemPlayHistoryViewModel
import com.ximalaya.ting.lite.main.mylisten.view.AllHistoryFragment
import com.ximalaya.ting.lite.main.play.dialog.PlayNoCopyRightDialog

/**
 * Created by qinhuifeng on 2021/1/14
 *
 * <AUTHOR>
 */
class PlayHistoryFloorItemAdapter(val baseFragment2: BaseFragment2, context: Context, listData: List<HomeItemPlayHistoryViewModel>) : MultiRecyclerAdapter<HomeItemPlayHistoryViewModel, SuperRecyclerHolder>(context, listData) {


    override fun createMultiViewHolder(mCtx: Context?, itemView: View, viewType: Int): SuperRecyclerHolder {
        return SuperRecyclerHolder.createViewHolder(mCtx, itemView)
    }

    override fun onBindMultiViewHolder(holder: SuperRecyclerHolder?, model: HomeItemPlayHistoryViewModel?, viewType: Int, position: Int) {
        if (holder == null || model == null) {
            return
        }
        if (model.viewType == HomeItemPlayHistoryViewModel.ITEM_MORE) {
            dealMoreItem(holder, model, position)
        } else {
            dealHistoryItem(holder, model, position)
        }
    }

    override fun getMultiItemViewType(model: HomeItemPlayHistoryViewModel?, position: Int): Int {
        if (model == null) {
            return 0
        }
        return model.viewType
    }

    override fun getMultiItemLayoutId(viewType: Int): Int {
        return if (viewType == HomeItemPlayHistoryViewModel.ITEM_MORE) {
            R.layout.main_item_album_history_in_home_floor_rv_item_more
        } else {
            R.layout.main_item_album_history_in_home_floor_rv_item
        }
    }

    private fun dealHistoryItem(holder: SuperRecyclerHolder?, t: HomeItemPlayHistoryViewModel?, position: Int) {
        if (holder == null || t == null || t.historyViewModel == null || t.historyViewModel.track == null) {
            return
        }
        //vip角标相关
        val ivTag: ImageView = holder.getViewById(R.id.main_iv_space_album_tag) as ImageView
        //专辑图
        val ivCover: ImageView = holder.getViewById(R.id.main_iv_album_cover) as ImageView
        //播放进度
        val tvPlayPercent: TextView = holder.getViewById(R.id.main_tv_last_play_percent) as TextView
        //标题
        val tvTitle: TextView = holder.getViewById(R.id.main_album_item_title) as TextView
        val historyViewModel = t.historyViewModel

        tvTitle.text = historyViewModel.albumTitle

        ImageManager.from(context).displayImage(ivCover, historyViewModel.track.validCover, com.ximalaya.ting.android.host.R.drawable.host_default_album_145)

        //设置播放进度
        val lastPos = XmPlayerManager.getInstance(context).getHistoryPos(historyViewModel.track.dataId)
        var playSchedul: String? = ToolUtil.getPlaySchedule(lastPos.toLong(), historyViewModel.track.duration.toLong())
        if (TextUtils.isEmpty(playSchedul)) {
            playSchedul = "已播1%";
        }
        tvPlayPercent.text = playSchedul
        tvPlayPercent.visibility = View.VISIBLE

        val albumM = AlbumM()
        albumM.id = historyViewModel.albumId
        albumM.albumTitle = historyViewModel.albumTitle
        albumM.coverUrlMiddle = historyViewModel.track.validCover
        albumM.setIsPaid(historyViewModel.track.isPayTrack)
        albumM.vipFreeType = if (historyViewModel.track.isVipTrack) {
            1
        } else {
            0
        }
        //设置角标
        ivTag.visibility = if (AlbumTagUtil.getAlbumCoverTag(albumM) != -1) {
            ivTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable(albumM, context, AlbumTagUtil.ZOOM_IN_RATIO_100_percent))
            View.VISIBLE
        } else {
            View.INVISIBLE
        }
        holder.setOnItemClickListenner(object : View.OnClickListener {
            override fun onClick(v: View?) {
                if (t.historyViewModel.track.isPayTrack && !UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(holder.context)
                    return
                }
                PlayTools.playTrackHistoy(baseFragment2.activity, true, t.historyViewModel.track, object : IplayTrackHistoryCallback {
                    override fun onSuccess() {
                        if (!baseFragment2.canUpdateUi()) {
                            return
                        }
                    }

                    override fun onError(code: Int, message: String) {
                        if (!baseFragment2.canUpdateUi()) {
                            return
                        }
                        if (code == 702 || code == 924) {
                            val playNoCopyRightDialog: PlayNoCopyRightDialog = if (t.historyViewModel.track != null && t.historyViewModel.track != null) {
                                PlayNoCopyRightDialog.newInstance(t.historyViewModel.track.dataId, t.historyViewModel.track.recSrc, t.historyViewModel.track.recTrack)
                            } else {
                                PlayNoCopyRightDialog()
                            }
                            playNoCopyRightDialog.show(baseFragment2.childFragmentManager, PlayNoCopyRightDialog.TAG)
                        } else {
                            CustomToast.showFailToast(message)
                        }
                    }
                })
            }
        })
    }

    private fun dealMoreItem(holder: SuperRecyclerHolder?, t: HomeItemPlayHistoryViewModel?, position: Int) {
        if (holder == null) {
            return
        }
        holder.setOnItemClickListenner(object : View.OnClickListener {
            override fun onClick(v: View?) {
                baseFragment2.startFragment(AllHistoryFragment.newInstance(true, false, true))
            }
        })
    }


}