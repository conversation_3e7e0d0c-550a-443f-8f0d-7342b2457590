package com.ximalaya.ting.lite.main.comment.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import com.ximalaya.ting.android.main.R

/**
 *  @Author: <PERSON><PERSON><PERSON> Cheng
 *  @Mail: <EMAIL>
 *  @CreateTime: 1/5/22
 *
 *  @Description: 评论输入控件，显示默认提示语&用户已输入的评论
 */
class CommentEditPreviewTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
): RelativeLayout(context, attrs, defStyleAttr) {

    private var tvPreview: TextView
    private var strPreview: CharSequence? = null


    init {
        View.inflate(context, R.layout.main_lite_comment_edit_preview_text_view, this)

        tvPreview = findViewById(R.id.main_tv_edit_preview)
    }

    fun setHint(cs: CharSequence?) {
        if(TextUtils.isEmpty(cs)) {
            tvPreview.setHint(R.string.main_comment_preview_default_hint)
        } else {
            tvPreview.hint = cs
        }
    }

    fun getHint(): CharSequence? {
        return tvPreview.hint
    }

    fun setCachedText(cs: CharSequence?) {
        tvPreview.text = cs
    }

    fun getCachedText(): CharSequence? {
        return tvPreview.text
    }
}