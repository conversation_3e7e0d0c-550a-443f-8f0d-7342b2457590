package com.ximalaya.ting.lite.main.comment.fragment

import android.graphics.Typeface
import android.os.Bundle
import android.view.View
import android.widget.*
import androidx.core.content.ContextCompat
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction.AbstractHomePageFragment
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction.HomeFragmentItingInterface
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.lite.main.comment.CommentDialogFragmentMain
import com.ximalaya.ting.lite.main.comment.CommentListPresenter
import com.ximalaya.ting.lite.main.comment.manager.ProgramCommentTabFragmentManager
import com.ximalaya.ting.lite.main.comment.view.CommentEditPreviewTextView
import com.ximalaya.ting.lite.main.tab.SimpleHomeFragment

/**
 * Created by dumingwei on 2021/11/18
 *
 * Desc: 极速听模式下 节目&小说tab界面
 */
class PlayerCommentTabFragment : AbstractHomePageFragment(), View.OnClickListener {

    companion object {
        private val TAG = PlayerCommentTabFragment::class.java.name
        const val TRUCK_KEY = "TRUCK_KEY"
        const val COMMENT_TOTAL_COUNTS = "COMMENT_TOTAL_COUNTS"
        fun newInstance(): PlayerCommentTabFragment {
            val args = Bundle()
            val fragment = PlayerCommentTabFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private lateinit var radioGroup: RadioGroup

    //听节目
    private lateinit var rbListenProgram: RadioButton

    //看小说
    private lateinit var rbReadBook: RadioButton

    private lateinit var flContent: FrameLayout

    private lateinit var tvInput: CommentEditPreviewTextView

    private lateinit var tvCommentCount: TextView

    private lateinit var tabFragmentManager: ProgramCommentTabFragmentManager

    private var isWhiteBgWhenReadNovelChecked = false
    private var commentTotalCounts: Long = 0

    private var mCurTrack: Track? = null

    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_play_multi_comments
    }

    override fun initUi(savedInstanceState: Bundle?) {
        arguments?.also {
            mCurTrack = it.getParcelable(TRUCK_KEY)
            commentTotalCounts = it.getLong(COMMENT_TOTAL_COUNTS)
        }
        radioGroup = findViewById(R.id.main_rg)
        rbListenProgram = findViewById(R.id.main_tv_header_sort_hot)
        rbReadBook = findViewById(R.id.main_tv_header_sort_time)
        flContent = findViewById(R.id.main_fl_fragment_container)
        tvInput = findViewById(R.id.main_view_comment_preview_edit)
        tvCommentCount = findViewById(R.id.main_tv_count)
        initListeners()
        tabFragmentManager = ProgramCommentTabFragmentManager(
            mActivity,
            childFragmentManager,
            R.id.main_fl_fragment_container
        )
        showListenProgramTab()
        setCommentCount(0)
    }

    override fun loadData() {}

    override fun onMyResume() {
        setFilterStatusBarSet(true)
        super.onMyResume()
        if (rbReadBook.isChecked && ChildProtectManager.isChildProtectOpen(mContext)) {
            rbListenProgram.isChecked = true
            return
        }
        if (isWhiteBgWhenReadNovelChecked) {
            StatusBarManager.setStatusBarColor(window, true)
        } else {
            StatusBarManager.setStatusBarColor(window, false)
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden);
        if (!isAdded) {
            return
        }
    }

    override fun getPageLogicName() = TAG

    override fun onClick(view: View) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return
        }
        when (view.id) {
            R.id.main_view_comment_preview_edit -> {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(context)
                    return
                }
                if (!isAllowSendComment()) {
                    return
                }
                val fragment = tabFragmentManager.currFragment
                if (fragment is PlayerCommentListFragment) {
                    fragment.startComment(null)
                } else if (fragment is PlayerHotCommentListFragment) {
                    fragment.startComment(null)
                }
            }
        }
    }

    fun isAllowSendComment(): Boolean {
        if (parentFragment is CommentDialogFragmentMain) {
            return (parentFragment as CommentDialogFragmentMain).isAllowSendComment()
        }
        return true
    }

    /**
     * 初始化监听事件
     */
    private fun initListeners() {
        tvInput.setOnClickListener(this)
        radioGroup.setOnCheckedChangeListener { group, checkedId ->
            val tag = group.tag
            when (checkedId) {
                R.id.main_tv_header_sort_hot -> {
                    tabFragmentManager.showFragment(checkedId, tag)
                    setWhiteBg()

                }
                R.id.main_tv_header_sort_time -> {
                    tabFragmentManager.showFragment(checkedId, tag)
                    setGradientBg()
                }
            }
        }
    }

    private fun showListenProgramTab() {
        if (rbListenProgram.isChecked) {
            //这个比较特殊，需要在已经选中状态下处理iting
            val fragment = tabFragmentManager.currFragment
            if (fragment is BaseFragment2) {
                fragment.arguments = radioGroup.tag as? Bundle?
            }
            if (fragment is HomeFragmentItingInterface) {
                fragment.handleIting()
            }
        } else {
            //默认选中听节目
            rbListenProgram.isChecked = true
        }
    }

    private fun showReadBookTab() {
        rbReadBook.isChecked = true
    }

    /**
     * 更新Present
     */
    fun updatePresent(presenter: CommentListPresenter): CommentListPresenter {
        mCurTrack?.also {
            presenter.mCurTrack = it
            presenter.sourceId = it.dataId
        }
        return presenter
    }

    override fun onRefresh() {
    }

    override fun isShowTruckFloatPlayBar(): Boolean {
        if (tabFragmentManager.currFragment is SimpleHomeFragment) {
            return (tabFragmentManager.currFragment as SimpleHomeFragment).isShowTruckFloatPlayBar
        }
        return true
    }

    private fun setWhiteBg() {
        rbListenProgram.setTextColor(ContextCompat.getColor(mContext, R.color.main_color_111111))
        rbListenProgram.textSize = 13f
        rbListenProgram.typeface = Typeface.defaultFromStyle(Typeface.BOLD)

        rbReadBook.setTextColor(ContextCompat.getColor(mContext, R.color.host_color_999999))
        rbReadBook.textSize = 13f
        rbReadBook.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
    }

    /**
     * 设置渐变背景
     */
    private fun setGradientBg() {
        rbListenProgram.setTextColor(ContextCompat.getColor(mContext, R.color.host_color_999999))
        rbListenProgram.textSize = 13f
        rbListenProgram.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)

        rbReadBook.setTextColor(ContextCompat.getColor(mContext, R.color.main_color_111111))
        rbReadBook.textSize = 13f
        rbReadBook.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
    }

    override fun darkStatusBar() = false

    fun setCommentCount(commentCount: Long, isAdd: Boolean = true) {
        if (isAdd) {
            commentTotalCounts += commentCount
        } else {
            commentTotalCounts -= commentCount
        }
        if (commentTotalCounts <= 0L) return
        tvCommentCount.text = "${StringUtil.getFriendlyNumStrEn(commentTotalCounts)}"
    }
}