package com.ximalaya.ting.lite.main.model.album;


import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt;
import com.ximalaya.ting.android.host.model.ad.BannerModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.base.NetOperateModel;
import com.ximalaya.ting.android.host.model.rank.RankM;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.player.cdn.CdnUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.model.onekey.OneKeyRadioModel;
import com.ximalaya.ting.lite.main.model.vip.VipInfoModel;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * ClassName:MainAlbumMList Function: TODO ADD FUNCTION Reason: TODO ADD REASON
 *
 * <AUTHOR>
 * @Date 2015-11-12 下午5:23:02
 * @see
 * @since Ver 1.1
 */
public class MainAlbumMList {
    public static final int MODULE_VIRTUAL_CATEGORY = 1; // 虚拟分类
    public static final int MODULE_GUESSLIKE = 1;

    private static final int MODULE_RANKLIST = 2;
    public static final int MODULE_CALCDIMENSION = 3;
    public static final int MODULE_TINGLIST = 4;
    public static final int MODULE_TAG = 5;
    public static final int MODULE_KEYWORD = 6;
    public static final int MODULE_METADATA = 7;
    public static final int MODULE_RADIO = 8;
    public static final int MOUDLE_MEMBERLIST = 9;
    public static final int MODULE_GROUP_RANK = 10;
    public static final int MODULE_FILTER = 11;
    public static final int MODULE_LISTEN_CALENDAR = 13;
    public static final int MODULE_LIVE = 16;//直播
    public static final int MODULE_OPERATION_PLACE = 17;//运营位(活动)
    public static final int MODULE_RECOMMEND_YOU = 18;//为你推荐
    public static final int MODULE_SOUND_GROUP = 19;//声音聚合
    public static final int MODULE_LISTEN_NOTE = 20;//单独听单
    public static final int MODULE_ANCHOR = 21; //主播
    public static final int MODULE_KEYWORD_METADATA_BOX = 23; //热词和元数据混合的九宫格
    public static final int MODULE_CHANNEL = 24; //一键听频道
    public static final int MODULE_AD = 25; //广告
    public static final int MODULE_PERSONAL_RECOMMEND = 26; // 猜你喜欢
    public static final int MODULE_LISTENER_MORE = 27; // 听完还听（有声小说分类支持）
    public static final int MODULE_HOT_CARD = 28; // 定制热词卡片（有声小说分类支持）
    public static final int MODULE_RANK_TYPE = 29; // 排行榜（单榜）（有声小说分类支持）
    public static final int MODULE_RECOMMEND_FOR_YOU = 33; //为你推荐
    public static final int MODULE_VIP_FREE_ALBUM = 31;//会员畅听专辑
    public static final int MODULE_VIP_FREE_BOOK = 32;//会员畅听有声书
    public static final int MODULE_BOUTIQUE_RECOMMEND_YOU = 33;//精品为你推荐
    public static final int MODULE_VIP_CHANNEL_RECOMMEND = 34;//VIP频道页-为你推荐
    public static final int MODULE_FEED_STREAM = 35; // 信息流
    public static final int MODULE_HOT_WORD_GROUP = 36; // 热词组专辑模块
    public static final int MODULE_NOVEL = 37; // 小说模块
    public static final int MODULE_SPECIAL = 38; // 专题卡片
    public static final int MODULE_PERSONAL_MUSIC_RADIO = 39; // 个性音乐电台
    public static final int MODULE_MUSICIAN = 40; // 音乐人
    public static final int MODULE_NEW_SPECIAL = 41; // 新听单
    public static final int MODULE_VIDEO = 42; // 视频模块
    public static final int MODULE_IMMERSIVE_SPECIAL = 43; // 沉浸式听单，一个听单，同时显示听单中的几个专辑
    public static final int MODULE_ALBUM_RANK_NEW = 44; // 频道专辑排行榜，显示三个榜单，可切换，榜单专辑以横向可横滑形式展示

    public static final int MODULE_CATEGORY_RANK = 10000; // 分类排行榜
    public static final int MODULE_HOT_SEARCH_RANK = 10001; // 热搜排行榜
    public static final int MODULE_ALBUM_OPERATE = 10002; // 专辑运营模块
    public static final int MODULE_NET_OPERATE = 10003; // 网页运营模块
    public static final int MODULE_ONEKEY_NEW = 10007; // 新版一键听

    // personalRecSubType的取值
    public static final String PERSONAL_REC_TYPE_GUESS_YOU_LIKE = "guessYouLike"; // 个性化推荐之猜你喜欢
    public static final String PERSONAL_REC_TYPE_RECOMMEND = "recommend"; // 个性化推荐之为你推荐
    public static final String PERSONAL_REC_TYPE_PAY_BOUTIQUE = "payBoutique"; // 个性化推荐之付费精品

    public static final int WORD_COLOR_BLACK = 0;
    public static final int WORD_COLOR_WHITE = 1;

    //**********************主app已经存在的楼层使用主app的，极速版专属的楼层，新增id，和主app区分开防止重合***********/

    //极速版--糖葫芦，糖葫芦是一个类型，糖葫芦中中的displayClass字段判断展不同的ui样式
    public static final int MODULE_LITE_CATEGORY_TANGHULU = 46; //糖葫芦


    public static final int MODULE_VIP_TOP_REGION = 1004; // VIP顶部的区域类型
    public static final int MODULE_VIP_BAR = 1005; // VIP条状的区域类型
    public static final int MODULE_VIP_ALBUM_CARD = 1006; // VIP专辑卡片
    public static final int MODULE_VIP_FOCUS_IMAGE = 10008; // VIP焦点图

    public static final int MODULE_CONTENT_POOL = 10010; // 内容池卡片

    public static final int MODULE_INTEREST_RECOMMEND = 10011; // 兴趣推荐区

    public static final int MODULE_REPLENISH_SEARCH = 10012; // 补充搜索区

    public static final int MODULE_TANGHULU_HOT_WORD = 10013; // 糖葫芦热词
    //听更新楼层
    public static final int MODULE_TING_UPDATE = 10014;
    //订阅历史楼层
    public static final int MODULE_SUBSCRIBE_HISTORY = 10015;

    //信息流，有限信息流
    public static final int MODULE_FEED_STREAM_V2 = 10016;

    //专辑排行榜楼层
    public static final int MODULE_ALBUM_RANK = 10017;

    public static final int MODULE_FLEX_BOX = 10100; // 动态布局


    //具体分类页面--排行榜糖一键听糖葫芦类型
    public static final String TANGHUYLU_DISPLAY_CLASS_CATEGORY_RANKONEKEY = "tanghulu_rankonekey";
    //首页糖葫芦类型
    public static final String TANGHUYLU_DISPLAY_CLASS_HOME_NORMAL = "tanghulu_normal";
    //首页楼层---单独一个一键听入口
    public static final String TANGHUYLU_DISPLAY_CLASS_ONLY_ONE_KEY = "tanghulu_onekey";

    //糖葫芦，横滑样式
    public static final String TANGHUYLU_DISPLAY_CLASS_SIDE_SLIP = "tanghulu_sideslip";

    public static final String TANGHUYLU_HOT_WORD_FLOAT = "floating";
    public static final String TANGHUYLU_HOT_WORD_NORMAL = "normal";

    //专辑运营位有配图C
    public static final String ALBUM_OPERATE_HAS_CUSTOM_COVER = "hasCustomCover";

    //是否展示全部播放按钮
    private boolean playAllBtn;
    private boolean hasMore;
    private String hasMoreUrl;
    private boolean hasInterest; // 是否填写过兴趣，猜你喜欢列表页用
    private int maxPageId;
    private int ret;
    private int pageSize;
    private int categoryId;
    private int moduleType;
    private int moduleId;
    private String moduleName;
    private int keywordId;
    private String personalRecSubType;  // 模块类型为26时，会有这个字段


    private String title;
    private String subtitle;
    private String tagName;
    private String coverPath;
    private String contentType;
    private String keywordName;

    private int loopCount;

    private int displayCount;

    private JSONObject jsonObject;

    private long channelId;
    private boolean musicSongList; // 是否支持一键播放
    private int wordColor; // 字体颜色
    private String recData;

    private List<AlbumM> list;
    private List<RankM> rankList;
    private List<RecommendDiscoveryM> tanghuluList;
    private List<OneKeyRadioModel> oneKeyRadioList;
    public List<RecommendItemNew> feedStreamItemList; // 信息流数据
    /**
     * vipTopInfoModelList 和 vipBarInfoModelList 数据类型一样，区分不同的使用类型
     */
    public List<VipInfoModel> vipInfoModelList;//VIP顶部区域信息
    public List<VipInfoModel> vipBarInfoModelList;//VIP条信息

    public List<TanghuluHotWord> tanghuluHotWordList;//优化的糖葫芦糖葫芦热词，悬浮型和常规性都用这个列表
    private List<TanghuluHotWord> tempTanghuluHotWordList;//优化的糖葫芦糖葫芦热词

    public List<BannerModel> bannerModelList;//vip焦点图

    public List<NetOperateModel> albumOperateModelList;//专辑运营模块
    public List<NetOperateModel> netOperateModelList;//网络运营模块

    public List<String> hotSearchRankList;//热搜排行榜

    public List<ReplenishSearchModel> replenishSearchList;//补充搜索词

    public HomeTingUpdateModel homeTingUpdateModel;//听更新楼层

    //专辑排行榜
    private List<HomeAlbumRankItem> homeAlbumRankItemList;

    //信息流使用的额外数据，目前10016，有限流在使用
    private FeedStreamOtherData feedStreamOtherData;

    //通用otherData
    private MainAlbumOtherData mainAlbumOtherData;

    private String displayClass;
    private int allCount;
    private int status; // 一键听电台兴趣设置入口控制字段 0表示未设置 1表示已设置

    public String interestId;//兴趣id

    //订阅历史模块使用的，返回用户当前订阅的个数
    public int totalCount;

    private int currentHotWordIndex; // 热词组模块中，当前选中的热词的索引，不来自服务端，本地处理用的

    /////////////////////////////////////////////////20171027 new add

    public static final String ITEM_DIRECTION_HORI = "horizontal";
    public static final String ITEM_DIRECTION_SIDE_SLIP = "sideslip";
    public static final String ITEM_DIRECTION_VERT = "vertical";
    public static final String ITEM_DIRECTION_NEW_LISTENER = "newlisten";
    private String cardClass;//条目排列方向：horizontal/vertical

    /////////////////////////////////////////////////20171027 new add

    private boolean circle;
    private long rankClusterId;//跳转至排行榜界面需要用到这个字段

    public MainAlbumMList() {

    }

    public MainAlbumMList(String json) {
        try {
            JSONObject object = new JSONObject(json);
            jsonObject = object;
            moduleType = object.optInt("moduleType", 5);
            // download & build
            Gson gson = new Gson();
            hasMore = object.optBoolean("hasMore", false);
            hasMoreUrl = object.optString("hasMoreUrl");
            hasInterest = object.optBoolean("hasInterest", false);
            maxPageId = object.optInt("maxPageId");
            ret = object.optInt("ret", -1);
            title = object.optString("title", "");
            pageSize = object.optInt("pageSize", 0);
            categoryId = object.optInt("categoryId", 0);
            tagName = object.optString("tagName", "");
            moduleId = object.optInt("moduleId", 0);
            moduleName = object.optString("moduleName", "");
            keywordId = object.optInt("keywordId");
            keywordName = object.optString("keywordName");
            subtitle = object.optString("subtitle");
            coverPath = object.optString("coverPath");
            cardClass = object.optString("cardClass", ITEM_DIRECTION_VERT);
            loopCount = object.optInt("loopCount");
            displayCount = object.optInt("displayCount", 0);
            personalRecSubType = object.optString("personalRecSubType");
            channelId = object.optLong("channelId");
            musicSongList = object.optBoolean("musicSongList");
            wordColor = object.optInt("wordColor");
            recData = object.optString("recData");
            displayClass = object.optString("displayClass");
            totalCount = object.optInt("totalCount", 0);
            circle = object.optBoolean("circle");

            rankClusterId = object.optLong("rankClusterId");


            JSONObject otherObj = object.optJSONObject("otherData");
            if (otherObj != null) {
                allCount = otherObj.optInt("allCount", 0);
                status = otherObj.optInt("staus", 0);
                interestId = otherObj.optString("interestId", "");
                playAllBtn = otherObj.optBoolean("playAllBtn", false);

                if (moduleType == MODULE_TING_UPDATE) {
                    int tingUpdate_unreadNum = otherObj.optInt("unreadNum", 0);
                    int tingUpdate_todayUpdateCnt = otherObj.optInt("todayUpdateCnt", 0);
                    long tingUpdate_lastUpdateAt = otherObj.optLong("lastUpdateAt", 0);
                    int tingUpdate_status = otherObj.optInt("status", 0);
                    long tingUpdate_updateAt = otherObj.optInt("updateAt", 0);
                    //创建听更新楼层数据
                    if (homeTingUpdateModel == null) {
                        homeTingUpdateModel = new HomeTingUpdateModel();
                    }
                    homeTingUpdateModel.allCount = allCount;
                    homeTingUpdateModel.status = tingUpdate_status;
                    homeTingUpdateModel.unreadNum = tingUpdate_unreadNum;
                    homeTingUpdateModel.todayUpdateCnt = tingUpdate_todayUpdateCnt;
                    homeTingUpdateModel.lastUpdateAt = tingUpdate_lastUpdateAt;
                    homeTingUpdateModel.updateAt = tingUpdate_updateAt;
                } else if (moduleType == MODULE_FEED_STREAM_V2) {
                    //有限信息流需要解析newestTimeline和newestTimeline
                    if (feedStreamOtherData == null) {
                        feedStreamOtherData = new FeedStreamOtherData();
                    }
                    feedStreamOtherData.newestTimeline = otherObj.optLong("newestTimeline", 0L);
                    feedStreamOtherData.oldestTimeline = otherObj.optLong("oldestTimeline", 0L);
                } else if (moduleType == MODULE_CONTENT_POOL) {
                    if (mainAlbumOtherData == null) {
                        mainAlbumOtherData = new MainAlbumOtherData();
                    }
                    //内容
                    mainAlbumOtherData.showScore = otherObj.optBoolean("showScore", false);
                    mainAlbumOtherData.poolId = otherObj.optInt("poolId", 0);
                } else if (moduleType == MODULE_ALBUM_RANK) {
                    //排行榜楼层
                    if (mainAlbumOtherData == null) {
                        mainAlbumOtherData = new MainAlbumOtherData();
                    }
                    mainAlbumOtherData.hasMoreLink = otherObj.optString("hasMoreLink", "");
                }
            }

            JSONArray jArray = object.optJSONArray("list");
            if (jArray == null) {
                jArray = object.optJSONArray("data");
            }
            if (jArray != null) {
                for (int i = 0; i < jArray.length(); i++) {
                    if (moduleType == MODULE_RANKLIST) {
                        if (rankList == null) rankList = new ArrayList<>();
                        RankM rankM = new RankM(jArray.getString(i));
                        rankList.add(rankM);
                    } else if (moduleType == MODULE_FILTER) {
                        if (rankList == null) rankList = new ArrayList<>();
                        RankM rankM = new RankM(jArray.getString(i));
                        rankList.add(rankM);
                    } else if (moduleType == MODULE_GROUP_RANK) {
                        if (rankList == null) rankList = new ArrayList<>();
                        RankM rankM = new RankM(jArray.getString(i));
                        rankList.add(rankM);
                    } else if (moduleType == MODULE_LITE_CATEGORY_TANGHULU) {
                        if (tanghuluList == null) tanghuluList = new ArrayList<>();
                        tanghuluList.add(new RecommendDiscoveryM(jArray.getJSONObject(i)));
                    } else if (moduleType == MODULE_FEED_STREAM) {
                        if (feedStreamItemList == null) {
                            feedStreamItemList = new ArrayList<>();
                        }
                        RecommendItemNew recommendItem = RecommendItemNew.parseJson(jArray.optJSONObject(i), gson);
                        if (recommendItem != null) {
                            feedStreamItemList.add(recommendItem);
                        }
                    } else if (moduleType == MODULE_FEED_STREAM_V2) {
                        if (feedStreamItemList == null) {
                            feedStreamItemList = new ArrayList<>();
                        }
                        RecommendItemNew recommendItem = RecommendItemNew.parseJson(jArray.optJSONObject(i), gson);
                        if (recommendItem != null) {
                            //在解析的时候，判断是否为完播数据，完播的话不再添加
                            if (recommendItem.getItem() instanceof RecommendTrackItem) {
                                RecommendTrackItem trackItem = (RecommendTrackItem) recommendItem.getItem();
                                int lastPos = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getHistoryPos(trackItem.getDataId());
                                int percent = ToolUtil.getPlayPercent(lastPos, trackItem.getDuration());
                                //过滤完播数据
                                if (percent <= 97) {
                                    feedStreamItemList.add(recommendItem);
                                }
                            } else {
                                feedStreamItemList.add(recommendItem);
                            }
                        }
                    } else if (moduleType == MODULE_ONEKEY_NEW) {
                        if (oneKeyRadioList == null) {
                            oneKeyRadioList = new ArrayList<>();
                        }
                        try {
                            OneKeyRadioModel radioModel = gson.fromJson(jArray.optString(i), OneKeyRadioModel.class);
                            if (radioModel != null) {
                                oneKeyRadioList.add(radioModel);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else if (moduleType == MODULE_VIP_TOP_REGION) {
                        if (vipInfoModelList == null) {
                            vipInfoModelList = new ArrayList<>();
                        }
                        VipInfoModel vipInfoModel = JsonUtilKt.getInstance().toObject(jArray.optString(i), VipInfoModel.class);
                        if (vipInfoModel != null) {
                            vipInfoModelList.add(vipInfoModel);
                        }
                    } else if (moduleType == MODULE_VIP_BAR) {
                        if (vipBarInfoModelList == null) {
                            vipBarInfoModelList = new ArrayList<>();
                        }
                        VipInfoModel vipInfoModel = JsonUtilKt.getInstance().toObject(jArray.optString(i), VipInfoModel.class);
                        if (vipInfoModel != null) {
                            vipBarInfoModelList.add(vipInfoModel);
                        }
                    } else if (moduleType == MODULE_VIP_FOCUS_IMAGE) {
                        if (bannerModelList == null) {
                            JSONArray jsonArray = new JSONArray(jsonObject.optString("list"));
                            JSONObject focusImageJsonObject = jsonArray.getJSONObject(0);
                            bannerModelList = JsonUtilKt.getInstance().toList(
                                    focusImageJsonObject.optString("data"),
                                    new TypeToken<List<BannerModel>>() {
                                    }.getType());
                        }
                    } else if (moduleType == MODULE_NET_OPERATE) {
                        if (netOperateModelList == null) {
                            netOperateModelList = new ArrayList<>();
                        }
                        NetOperateModel netOperateModel = JsonUtilKt.getInstance().toObject(jArray.optString(i), NetOperateModel.class);
                        if (netOperateModel != null) {
                            netOperateModelList.add(netOperateModel);
                        }
                    } else if (moduleType == MODULE_VIP_ALBUM_CARD) {
                        if (list == null) list = new ArrayList<>();
                        AlbumM album = new AlbumMInMain(jArray.getString(i));
                        album.setCategoryId(getCategoryId());
                        list.add(album);
                    } else if (moduleType == MODULE_HOT_SEARCH_RANK) {
                        if (hotSearchRankList == null) {
                            hotSearchRankList = new ArrayList<>();
                        }
                        hotSearchRankList.add(jArray.optString(i));
                    } else if (moduleType == MODULE_REPLENISH_SEARCH) {
                        if (replenishSearchList == null) {
                            replenishSearchList = new ArrayList<>();
                        }
                        ReplenishSearchModel replenishSearchModel = JsonUtilKt.getInstance().toObject(
                                jArray.optString(i), ReplenishSearchModel.class);
                        if (replenishSearchModel != null) {
                            replenishSearchList.add(replenishSearchModel);
                        }
                    } else if (moduleType == MODULE_TANGHULU_HOT_WORD) {
                        if (tempTanghuluHotWordList == null) {
                            tempTanghuluHotWordList = new ArrayList<>();
                        }
                        TanghuluHotWord tanghuluHotWord = JsonUtilKt.getInstance().toObject(jArray.optString(i), TanghuluHotWord.class);
                        //cardClass 为normal 的要过滤掉信息流
                        if (MainAlbumMList.TANGHUYLU_HOT_WORD_NORMAL.equals(cardClass)) {
                            if (tanghuluHotWord == null || TanghuluHotWord.ITEM_FEED.equals(tanghuluHotWord.itemType)) {
                                continue;
                            }
                        }
                        if (tanghuluHotWord != null) {
                            tempTanghuluHotWordList.add(tanghuluHotWord);
                        }
                    } else if (moduleType == MainAlbumMList.MODULE_ALBUM_RANK) {
                        //专辑排行榜
                        if (homeAlbumRankItemList == null) {
                            homeAlbumRankItemList = new ArrayList<>();
                        }
                        HomeAlbumRankItem homeAlbumRankItem = HomeAlbumRankItem.parseHomeAlbumRankItem(jArray.optJSONObject(i));
                        if (homeAlbumRankItem != null) {
                            homeAlbumRankItemList.add(homeAlbumRankItem);
                        }
                    } else {
                        // MODULE_CATEGORY_RANK 类型也使用list字段
                        if (list == null) list = new ArrayList<>();
                        AlbumM album = new AlbumMInMain(jArray.getString(i));
                        album.setCategoryId(getCategoryId());
                        list.add(album);
                    }
                }
            }
            //添加全部分类
            tanghuluHotWordList = transformHotWordList(tempTanghuluHotWordList);
        } catch (Exception e) {
            e.printStackTrace();
            Logger.e("mark", CdnUtil.exception2String(e));
        }
    }

    private List<TanghuluHotWord> transformHotWordList(List<TanghuluHotWord> list) {
        List<TanghuluHotWord> finalList = new ArrayList<>();
        if (CollectionUtil.isNullOrEmpty(list)) {
            return finalList;
        }

        int showSize = Math.min(list.size(), 7);
        for (int i = 0; i < showSize; i++) {
            finalList.add(list.get(i));
        }
        //默认选中第1个
        if (finalList.get(0) != null) {
            finalList.get(0).selected = true;
        }
        TanghuluHotWord allHotWord = new TanghuluHotWord();
        allHotWord.itemType = TanghuluHotWord.ITEM_ALL;
        TanghuluHotWord.ItemBean item = new TanghuluHotWord.ItemBean();
        item.name = TanghuluHotWord.NAME_ALL;
        allHotWord.item = item;

        finalList.add(allHotWord);

        return finalList;
    }

    public String getDisplayClass() {
        return displayClass;
    }

    public void setDisplayClass(String displayClass) {
        this.displayClass = displayClass;
    }

    public int getLoopCount() {
        return loopCount;
    }


    public void setLoopCount(int loopCount) {
        this.loopCount = loopCount;
    }

    public String getKeywordName() {
        return keywordName;
    }


    public int getKeywordId() {
        return keywordId;
    }


    public boolean isHasMore() {
        return hasMore;
    }

    public void setHasMore(boolean hasMore) {
        this.hasMore = hasMore;
    }

    public List<AlbumM> getList() {
        return list;
    }

    public void setList(List<AlbumM> list) {
        this.list = list;
    }

    public int getRet() {
        return ret;
    }


    public String getTitle() {
        return title;
    }


    public int getPageSize() {
        return pageSize;
    }

    public int getCategoryId() {
        return categoryId;
    }


    public List<RecommendDiscoveryM> getTanghuluList() {
        return tanghuluList;
    }

    public String getTagName() {
        return tagName;
    }


    public int getModuleType() {
        return moduleType;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public int getMaxPageId() {
        return maxPageId;
    }

    public List<RankM> getRankList() {
        return rankList;
    }

    public String getContentType() {
        return contentType;
    }


    public JSONObject getJsonObject() {
        return jsonObject;
    }


    public void setCardClass(String cardClass) {
        this.cardClass = cardClass;
    }

    public String getCardClass() {
        return cardClass;
    }

    public String getHasMoreUrl() {
        return hasMoreUrl;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public boolean isHasInterest() {
        return hasInterest;
    }

    public void setHasInterest(boolean hasInterest) {
        this.hasInterest = hasInterest;
    }

    public String getPersonalRecSubType() {
        return personalRecSubType;
    }

    public void setPersonalRecSubType(String personalRecSubType) {
        this.personalRecSubType = personalRecSubType;
    }

    public long getChannelId() {
        return channelId;
    }

    public void setChannelId(long channelId) {
        this.channelId = channelId;
    }

    public int getCurrentHotWordIndex() {
        return currentHotWordIndex;
    }

    public void setCurrentHotWordIndex(int currentHotWordIndex) {
        this.currentHotWordIndex = currentHotWordIndex;
    }


    public boolean isMusicSongList() {
        return musicSongList;
    }

    public void setMusicSongList(boolean musicSongList) {
        this.musicSongList = musicSongList;
    }

    public String getRecData() {
        return recData;
    }

    public void setRecData(String recData) {
        this.recData = recData;
    }

    public int getWordColor() {
        return wordColor;
    }

    public void setWordColor(int wordColor) {
        this.wordColor = wordColor;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public List<OneKeyRadioModel> getOneKeyRadioList() {
        return oneKeyRadioList;
    }

    public void setOneKeyRadioList(List<OneKeyRadioModel> oneKeyRadioList) {
        this.oneKeyRadioList = oneKeyRadioList;
    }

    public int getAllCount() {
        return allCount;
    }

    public void setAllCount(int allCount) {
        this.allCount = allCount;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getModuleId() {
        return moduleId;
    }

    public void setModuleId(int moduleId) {
        this.moduleId = moduleId;
    }

    public boolean isCircle() {
        return circle;
    }

    public void setCircle(boolean circle) {
        this.circle = circle;
    }


    public long getRankClusterId() {
        return rankClusterId;
    }

    public void setRankClusterId(long rankClusterId) {
        this.rankClusterId = rankClusterId;
    }

    public HomeTingUpdateModel getHomeTingUpdateModel() {
        return homeTingUpdateModel;
    }

    public void setHomeTingUpdateModel(HomeTingUpdateModel homeTingUpdateModel) {
        this.homeTingUpdateModel = homeTingUpdateModel;
    }

    public boolean isPlayAllBtn() {
        return playAllBtn;
    }

    public void setPlayAllBtn(boolean playAllBtn) {
        this.playAllBtn = playAllBtn;
    }

    public List<HomeAlbumRankItem> getHomeAlbumRankItemList() {
        return homeAlbumRankItemList;
    }

    public int getDisplayCount() {
        return displayCount;
    }

    public FeedStreamOtherData getFeedStreamOtherData() {
        return feedStreamOtherData;
    }

    public MainAlbumOtherData getMainAlbumOtherData() {
        return mainAlbumOtherData;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof MainAlbumMList) {
            MainAlbumMList mainAlbumMList = (MainAlbumMList) obj;
            if (this.getCategoryId() == mainAlbumMList.getCategoryId() && this.getTitle().equals(mainAlbumMList.getTitle())) {
                return true;
            }
        }
        return super.equals(obj);
    }
}
