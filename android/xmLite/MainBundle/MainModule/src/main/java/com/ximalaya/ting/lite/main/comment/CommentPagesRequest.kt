package com.ximalaya.ting.lite.main.comment

import android.text.TextUtils
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.lite.main.comment.entities.AlbumCommentModel
import com.ximalaya.ting.lite.main.comment.entities.CommentListBean
import com.ximalaya.ting.lite.main.comment.entities.CommentModel
import com.ximalaya.ting.lite.main.comment.entities.ListModeBase
import com.ximalaya.ting.lite.main.request.LiteUrlConstants
import org.json.JSONException
import org.json.JSONObject

/**
 *  @Author: Junxiang Cheng
 *  @Mail: <EMAIL>
 *  @CreateTime: 12/29/21
 *
 *  @Description:
 */

class CommentPagesRequest : CommonRequestM() {
    companion object {
        const val TAG = "CommentPagesRequest"

        /**
         * 获取评论列表
         */
        fun getCommentList(
            specificParams: Map<String, String>?,
            callback: IDataCallBack<CommentListBean>
        ) {
            val url = LiteUrlConstants.getCommentListUrl() + System.currentTimeMillis()

            baseGetRequest(
                url,
                specificParams,
                callback,
                IRequestCallBack<CommentListBean> { content ->
                    try {
                        val json = JSONObject(content)
                        val data = json.optString("data")

                        return@IRequestCallBack JsonUtilKt.instance.toObject(
                            data,
                            CommentListBean::class.java
                        )

                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                    return@IRequestCallBack null
                })
        }

        /**
         * 获取评论列表
         */
        fun getHotCommentList(
            specificParams: Map<String, String>?,
            callback: IDataCallBack<CommentListBean>
        ) {
            val url = LiteUrlConstants.getHotCommentUrl() + "/" + System.currentTimeMillis()
            baseGetRequest(
                url,
                specificParams,
                callback,
                IRequestCallBack<CommentListBean> { content ->
                    try {
                        val json = JSONObject(content)
                        val hotComment = json.optString("comment")
                        val data = ListModeBase(hotComment, CommentModel::class.java, "list")
                        data.extraData = "hot"
                        return@IRequestCallBack data.commentTo2CommentListBean(data)

                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                    return@IRequestCallBack null
                })
        }

        /**
         * 获取评论列表
         */
        fun getAlbumHotCommentList(
            specificParams: Map<String, String>?,
            callback: IDataCallBack<CommentListBean>
        ) {
            val url = LiteUrlConstants.getAlbumRateListUrl() + "/" + System.currentTimeMillis()
            baseGetRequest(
                url,
                specificParams,
                callback,
                IRequestCallBack<CommentListBean> { content ->
                    try {
                        val json = JSONObject(content)
                        val data = json.optJSONObject("data")
                        val hotComment = data.optString("comments")
                        val allCommentTotalCount = data.optLong("allCommentsCount")
                        val baseModel = ListModeBase<AlbumCommentModel>(
                            hotComment, AlbumCommentModel::class.java, "list"
                        )
                        return@IRequestCallBack baseModel.albumCommentTo2CommentListBean(
                            baseModel,
                            allCommentTotalCount
                        )

                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                    return@IRequestCallBack null
                })
        }

        /**
         * 获取回复列表
         */
        fun getCommentReplyList(
            specificParams: Map<String, String>?,
            callback: IDataCallBack<CommentListBean>
        ) {
            val url = LiteUrlConstants.getTrackCommentDetail() + System.currentTimeMillis()

            baseGetRequest(
                url,
                specificParams,
                callback,
                IRequestCallBack<CommentListBean> { content ->
                    try {
                        val data = ListModeBase(content, CommentModel::class.java, "list")
                        return@IRequestCallBack data.commentTo2CommentListBean(data)

                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                    return@IRequestCallBack null
                })
        }

        /**
         * 获取回复列表
         */
        fun getAlbumCommentReplyList(
            specificParams: Map<String, String>?,
            callback: IDataCallBack<CommentListBean>
        ) {
            val url = LiteUrlConstants.getAlbumRateDetailUrl() + "/" + System.currentTimeMillis()
            baseGetRequest(
                url,
                specificParams,
                callback,
                IRequestCallBack<CommentListBean> { content ->
                    try {
                        val json = JSONObject(content)
                        if (json.optInt("ret") == 0) {
                            val data = json.optString("data")
                            if (!TextUtils.isEmpty(data)) {
                                val jsonData = JSONObject(data)
                                val commentReplies = jsonData.optString("commentReplys")
                                val comments: ListModeBase<AlbumCommentModel> =
                                    ListModeBase(
                                        commentReplies,
                                        AlbumCommentModel::class.java,
                                        "list"
                                    )
                                return@IRequestCallBack comments.albumCommentTo2CommentListBean(
                                    comments,
                                    comments.totalCount.toLong()
                                )
                            }
                        }
                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                    return@IRequestCallBack null
                })
        }

        /**
         * 添加评论
         */
        fun postAddComment(
            specificParams: Map<String, String>,
            callback: IDataCallBack<String>
        ) {
            val url = LiteUrlConstants.getSendCommentUrl()
            basePostRequest(url, specificParams, callback, IRequestCallBack<String> { content ->
                try {
                    return@IRequestCallBack content

                } catch (e: JSONException) {
                    e.printStackTrace()
                }
                return@IRequestCallBack null
            })
        }

        /**
         * 添加评论
         */
        fun postReplyAlbumComment(
            specificParams: Map<String, String>,
            callback: IDataCallBack<String>
        ) {
            val url = LiteUrlConstants.replyAlbumRateUrl() + "/" + System.currentTimeMillis()
            basePostRequest(url, specificParams, callback, IRequestCallBack<String> { content ->
                try {
                    val json = JSONObject(content)
                    if (json.optInt("ret") == 0) {
                        val data = json.optString("data")
                        if (!TextUtils.isEmpty(data)) {
                            return@IRequestCallBack JSONObject(data).optString("replyResult")
                        }
                    }
                } catch (e: JSONException) {
                    e.printStackTrace()
                }
                return@IRequestCallBack null
            })
        }

        /**
         * 删除评论
         */
        fun postDeleteComment(
            specificParams: Map<String, String>,
            callback: IDataCallBack<String>
        ) {
            val url = LiteUrlConstants.commentDel() + "/" + System.currentTimeMillis()
            basePostRequest(
                url,
                specificParams,
                callback,
                IRequestCallBack<String> { content ->
                    return@IRequestCallBack content
                })
        }

        /**
         * 删除评论
         */
        fun postAlbumDeleteComment(
            specificParams: Map<String, String>,
            callback: IDataCallBack<String>
        ) {
            val url =
                LiteUrlConstants.deleteAlbumRateReplyUrl() + "/" + System.currentTimeMillis()
            basePostRequest(
                url,
                specificParams,
                callback,
                IRequestCallBack<String> { content ->
                    return@IRequestCallBack content
                })
        }

        /**
         * 获取评论数
         */
        fun getCommentCount(sourceId: Long, callback: IDataCallBack<Long>) {
            val params = HashMap<String, String>()
            params["trackId"] = sourceId.toString()
            params["pageId"] = "1"
            params["hotPageId"] = 1.toString()
            params["order"] = "0"
            params["source"] = "2"
            params["pageSize"] = "0"
            params["hotPageSize"] = "0"
            params["showVersion"] = "1"
            val url = LiteUrlConstants.getHotCommentUrl() + "/" + System.currentTimeMillis()
            baseGetRequest(url, params, callback, IRequestCallBack<Long> { content ->
                try {
                    val json = JSONObject(content)
                    if (json.optInt("ret") == 0) {
                        val data = json.optString("comment")
                        if (!TextUtils.isEmpty(data)) {
                            val totalCount = JSONObject(data).optString("totalCount")
                            if (!TextUtils.isEmpty(totalCount)) {
                                return@IRequestCallBack totalCount?.toLong()
                            }
                        }
                    }
                } catch (e: JSONException) {
                    e.printStackTrace()
                }
                return@IRequestCallBack null
            })
        }

        /**
         * 获取专辑评论数
         */
        fun getAlbumCommentCount(sourceId: Long, callback: IDataCallBack<Long>) {
            val params = HashMap<String, String>()
            params["albumId"] = sourceId.toString()
            params["pageId"] = "1"
            params["pageSize"] = "5"
            params["order"] = "time-desc"
            val url = LiteUrlConstants.getAlbumRateListUrl() + "/" + System.currentTimeMillis()
            baseGetRequest(url, params, callback, IRequestCallBack<Long> { content ->
                try {
                    val json = JSONObject(content)
                    if (json.optInt("ret") == 0) {
                        val data = json.optString("data")
                        if (!TextUtils.isEmpty(data)) {
                            return@IRequestCallBack JSONObject(data).optLong("allCommentsCount")
                        }
                    }
                } catch (e: JSONException) {
                    e.printStackTrace()
                }
                return@IRequestCallBack null
            })
        }

        fun commentLikeOrUnLike(
            params: Map<String, String>,
            callback: IDataCallBack<Boolean>
        ) {
            requestCommentLikeOrUnLike(
                params = params,
                callback = callback,
                url = LiteUrlConstants.commentLike()
            )
        }

        fun albumLikeComment(
            params: Map<String, String>,
            callback: IDataCallBack<Boolean>
        ) {
            requestCommentLikeOrUnLike(
                params = params,
                callback = callback,
                url = LiteUrlConstants.likeAlbumRateUrl() + "/" + System.currentTimeMillis()
            )
        }

        fun albumDisLikeComment(
            params: Map<String, String>,
            callback: IDataCallBack<Boolean>
        ) {
            requestCommentLikeOrUnLike(
                params = params,
                callback = callback,
                url = LiteUrlConstants.unlikeAlbumRateUrl() + "/" + System.currentTimeMillis()
            )
        }

        fun albumReplyLikeComment(
            params: Map<String, String>,
            callback: IDataCallBack<Boolean>
        ) {
            requestCommentLikeOrUnLike(
                params = params,
                callback = callback,
                url = LiteUrlConstants.likeAlbumReplyRateUrl() + "/" + System.currentTimeMillis()
            )
        }

        fun albumReplyDisLikeComment(
            params: Map<String, String>,
            callback: IDataCallBack<Boolean>
        ) {
            requestCommentLikeOrUnLike(
                params = params,
                callback = callback,
                url = LiteUrlConstants.unlikeAlbumReplyRateUrl() + "/" + System.currentTimeMillis()
            )
        }

        private fun requestCommentLikeOrUnLike(
            params: Map<String, String>,
            callback: IDataCallBack<Boolean>,
            url: String
        ) {
            basePostRequest(url, params, callback,
                IRequestCallBack<Boolean> { content ->
                    try {
                        val json = JSONObject(content)
                        return@IRequestCallBack json.optInt("ret") == 0
                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                    return@IRequestCallBack false
                })
        }
    }
}