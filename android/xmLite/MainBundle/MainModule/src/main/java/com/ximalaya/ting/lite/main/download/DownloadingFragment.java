package com.ximalaya.ting.lite.main.download;

import android.app.ProgressDialog;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.ximalaya.ting.android.downloadservice.base.BaseDownloadTask;
import com.ximalaya.ting.android.downloadservice.base.IDownloadStatus;
import com.ximalaya.ting.android.downloadservice.base.IDownloadTaskCallback;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder.DialogCallback;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.FileSizeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.util.IDbDataCallBack;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.lang.ref.WeakReference;
import java.util.List;


/**
 * 下载听-下载中
 *
 * <AUTHOR>
 */
public class DownloadingFragment extends BaseFragment2 implements
        OnItemClickListener, OnClickListener {
    private TextView mTvTitle;
    private ListView mListView;
    private DownloadingTaskAdapter mAdapter;
    private View mTvClearAll;
    private TextView mTvResume;
    private TextView mTvPause;
    private View mLvHeader;
    private ProgressBar mProgressBar;
    private TextView mTvProgress;
    private ProgressDialog mDeleteDialog;
    private boolean mIsLoading;
    private long mAvailableMemorySize;
    private long mAllocateMemorySize;
    private final IDownloadTaskCallback mDownloadCallback = new IDownloadTaskCallback() {

        @Override
        public void onUpdateTrack(@Nullable BaseDownloadTask track) {
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
            updateControlView();
        }

        @Override
        public void onDownloadProgress(@Nullable BaseDownloadTask downloadTask) {
            if (!canUpdateUi() || downloadTask == null)
                return;
            mAdapter.updateDownloadInfo(mListView, downloadTask);
        }

        @Override
        public void onComplete(@Nullable BaseDownloadTask downloadTask) {
            if (downloadTask == null) {
                return;
            }
            mAdapter.deleteListData(downloadTask);
            if (RouteServiceUtil.getDownloadService().getUnfinishedTasks().size() == 0) {
                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            } else {
                updateControlView();
            }
            mAdapter.notifyDataSetChanged();
            updateDownloadingCountView();
        }

        @Override
        public void onCancel(@Nullable BaseDownloadTask downloadTask) {
            if (downloadTask == null) {
                return;
            }
            mAdapter.deleteListData(downloadTask);
            if (RouteServiceUtil.getDownloadService().getUnfinishedTasks().size() == 0) {
                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            } else {
                updateControlView();
            }
            mAdapter.notifyDataSetChanged();
            updateDownloadingCountView();
        }

        @Override
        public void onStartNewTask(@Nullable BaseDownloadTask downloadTask) {
            if (downloadTask == null) {
                return;
            }
            if (mAdapter.getListData() != null) {
                mAdapter.getListData().add(downloadTask);
                if (RouteServiceUtil.getDownloadService().getUnfinishedTasks().size() > 0) {
                    updateControlView();
                }
                mAdapter.notifyDataSetChanged();

                updateDownloadingCountView();
            }
        }

        @Override
        public void onError(@Nullable BaseDownloadTask downloadTask) {
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
            updateControlView();
        }

        @Override
        public void onDelete() {
            if (ConstantsOpenSdk.isDebug) {
                Log.d("DownloadingFragment", "onDelete");
            }
        }
    };

    public DownloadingFragment() {
        super(true, null);
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null){
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mTvTitle = (TextView) findViewById(R.id.main_title);
        mTvTitle.setText(getStringSafe(R.string.main_downloading, 0));
        mProgressBar = (ProgressBar) findViewById(R.id.main_load_progress);
        mTvProgress = (TextView) findViewById(R.id.main_progress_tv);
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                refreshProgress();
            }
        });
        findViewById(R.id.main_back_btn).setOnClickListener(this);
        mListView = (ListView) findViewById(R.id.main_listview);
        mListView.setDividerHeight(0);
        mLvHeader = LayoutInflater.from(getActivity()).inflate(R.layout.main_view_downloading_head, null, false);
        mLvHeader.setLayoutParams(new AbsListView.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, BaseUtil.dp2px(getActivity(), 55)
        ));
        mTvPause = mLvHeader.findViewById(R.id.main_batch_pause);
        mTvResume = mLvHeader.findViewById(R.id.main_batch_resume);
        mTvClearAll = mLvHeader.findViewById(R.id.main_clear_all);

        mTvPause.setOnClickListener(this);
        mTvResume.setOnClickListener(this);
        mTvClearAll.setOnClickListener(this);
        AutoTraceHelper.bindData(mTvPause,"");
        AutoTraceHelper.bindData(mTvResume,"");
        AutoTraceHelper.bindData(mTvClearAll,"");

        mListView.addHeaderView(mLvHeader);
        mAdapter = new DownloadingTaskAdapter(mActivity, null);
        mListView.setAdapter(mAdapter);
        mListView.setOnItemClickListener(this);
        mDeleteDialog = ToolUtil.createProgressDialog(getActivity(),
                "正在刪除所有未完成的任务");
    }

    @Override
    protected void loadData() {
        updateData();
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38251;
        super.onMyResume();


        refreshProgress();
        updateData();
        RouteServiceUtil.getDownloadService().registerDownloadCallback(mDownloadCallback);
    }

    public void onPause() {
        super.onPause();
        RouteServiceUtil.getDownloadService().unRegisterDownloadCallback(mDownloadCallback);
    }

    private static class UpdateTask extends MyAsyncTask<Void, Void, List<BaseDownloadTask>> {
        private WeakReference<DownloadingFragment> mFragmentRef;

        UpdateTask(DownloadingFragment fragment) {
            mFragmentRef = new WeakReference<>(fragment);
        }

        @Override
        protected List<BaseDownloadTask> doInBackground(Void... params) {
            return RouteServiceUtil.getDownloadService().getUnfinishedTasks();
        }

        @Override
        protected void onPostExecute(final List<BaseDownloadTask> list) {
            DownloadingFragment fragment = mFragmentRef.get();
            if (fragment == null) {
                return;
            }
            fragment.mIsLoading = false;

            if (fragment.canUpdateUi()) {
                fragment.doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        DownloadingFragment f = mFragmentRef.get();
                        if (f == null) {
                            return;
                        }
                        if (list == null || list.size() == 0) {
                            f.mAdapter.clear();
                            f.mAdapter.notifyDataSetChanged();

                            f.onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                        } else {
                            f.mAdapter.clear();
                            f.mAdapter.addListData(list);
                            f.mAdapter.notifyDataSetChanged();

                            f.onPageLoadingCompleted(LoadCompleteType.OK);
                        }
                    }
                });

            }
        }
    }

    public void updateData() {
        if (mIsLoading) {
            return;
        }
        mIsLoading = true;
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        new UpdateTask(this).myexec();
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position,
                            long id) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int index = position - mListView.getHeaderViewsCount();
        if (mAdapter.getCount() == 0 || index < 0
                || index >= mAdapter.getCount())
            return;

        BaseDownloadTask task = (BaseDownloadTask) mAdapter.getItem(index);

        if (task.getDownloadStatus() == IDownloadStatus.DOWNLOADING) {
            RouteServiceUtil.getDownloadService().pauseTask(task);
        } else if (task.getDownloadStatus() == IDownloadStatus.DOWNLOAD_PAUSE) {
            RouteServiceUtil.getDownloadService().resumeTask(task);
            task.setStartTime("" + System.currentTimeMillis());
        } else if (task.getDownloadStatus() == IDownloadStatus.DOWNLOAD_WAITING) {
            RouteServiceUtil.getDownloadService().priorityTask(task);
        } else if (task.getDownloadStatus() == IDownloadStatus.DOWNLOAD_FAILED) {
            RouteServiceUtil.getDownloadService().resumeTask(task);
        }
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_downloading;
    }

    private void updateControlView() {
        if (getActivity() == null) {
            return;
        }
        if (RouteServiceUtil.getDownloadService().hasUnFinishDownload()) {
            mTvPause.setVisibility(View.VISIBLE);
            mTvResume.setVisibility(View.GONE);
        } else {
            mTvPause.setVisibility(View.GONE);
            mTvResume.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onPageLoadingCompleted(LoadCompleteType loadCompleteType) {
        if (loadCompleteType == LoadCompleteType.OK) {
            mLvHeader.setVisibility(View.VISIBLE);
            updateControlView();
        } else {
            mLvHeader.setVisibility(View.GONE);
        }

        updateDownloadingCountView();
        super.onPageLoadingCompleted(loadCompleteType);
    }

    private void updateDownloadingCountView() {
        if (mAdapter == null) {
            return;
        }

        mTvTitle.setText(getStringSafe(R.string.main_downloading, mAdapter.getCount()));
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.main_back_btn) {
            finishFragment();
        } else if (i == R.id.main_batch_pause) {
            RouteServiceUtil.getDownloadService().pauseAllTask(true, false);
            mAdapter.notifyDataSetChanged();
            updateControlView();

        } else if (i == R.id.main_batch_resume) {
            RouteServiceUtil.getDownloadService().resumeAllTask();
            mAdapter.notifyDataSetChanged();
            updateControlView();
        } else if (i == R.id.main_clear_all) {
            new DialogBuilder(getActivity()).setMessage("确定清空所有正在下载的任务？")
                    .setOkBtn(new DialogCallback() {
                        @Override
                        public void onExecute() {
                            mDeleteDialog.show();
                            RouteServiceUtil.getDownloadService().deleteAllDownloadingTask(new IDbDataCallBack<Integer>() {
                                @Override
                                public void onResult(Integer result) {
                                    if (result == null || result <= 0) {
                                        return;
                                    }
                                    HandlerManager.obtainMainHandler().post(new Runnable() {
                                        @Override
                                        public void run() {
                                            mDeleteDialog.dismiss();
                                            mAdapter.clear();
                                            mAdapter.notifyDataSetChanged();
                                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                                        }
                                    });
                                }
                            });
                        }
                    })
                    .setCancelBtn(new DialogCallback() {
                        @Override
                        public void onExecute() {
                        }
                    })
                    .showConfirm();
        }
    }

    @Override
    protected boolean onPrepareNoContentView() {
        setNoContentSubtitle("下载节目到本地，随时随地离线收听");
        return false;
    }


    public void refreshProgress() {
        new MyAsyncTask<Void, Void, Void>() {
            @Override
            protected Void doInBackground(Void... params) {
                mAllocateMemorySize = RouteServiceUtil.getDownloadService().getDownloadedFileSize();
                String path = RouteServiceUtil.getStoragePathManager().getCurSavePath();
                mAvailableMemorySize = FileSizeUtil.getAvailableMemorySize(path);
                return null;
            }

            protected void onPostExecute(Void result) {
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        updateProgress();
                    }
                });

            }
        }.myexec();
    }

    /**
     * 每次下载完成后sd卡使用情况
     */
    private void updateProgress() {
        String string = "已占用"
                + StringUtil.getFriendlyFileSize(mAllocateMemorySize) + "/可用空间"
                + StringUtil.getFriendlyFileSize(mAvailableMemorySize);
        int progress = (int) (mAllocateMemorySize * 100f / mAvailableMemorySize);
        mProgressBar.setProgress(progress);
        mTvProgress.setText(string);
    }

}
