package com.ximalaya.ting.lite.main.album.fragment

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.Adapter
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi


class AlbumUnlockStepFragment : BaseFragment2() {

    private val mStatusBar: View? by lazy {
        findViewById<View>(R.id.main_view_status_bar)
    }
    private val mTitleBar: RelativeLayout? by lazy {
        findViewById<RelativeLayout>(R.id.main_rl_title_bar)
    }
    private val mTvTitle: TextView? by lazy {
        findViewById<TextView>(R.id.main_tv_title)
    }
    private val mIvBack: ImageView? by lazy {
        findViewById<ImageView>(R.id.main_iv_back)
    }
    private val mRecyclerView: RecyclerView? by lazy {
        findViewById<RecyclerView>(R.id.main_lite_rv_unlock_step)
    }

    private val mList = listOf(R.drawable.main_unlock_step1, R.drawable.main_unlock_step2,
        R.drawable.main_unlock_step3, R.drawable.main_unlock_step4, R.drawable.main_unlock_step5)

    private val mListImgHeight = listOf(189, 193, 188, 235, 139)

    override fun getPageLogicName(): String {
        return "AlbumUnlockStepFragment"
    }

    override fun initUi(savedInstanceState: Bundle?) {
        mRecyclerView?.apply {
            val lM = LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
            layoutManager = lM
            adapter = mAdapter

            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    changeTitleBar(canScrollVertically(-1))
                }
            })
        }

        mStatusBar?.apply {
            val params: RelativeLayout.LayoutParams = layoutParams as RelativeLayout.LayoutParams
            params.height = BaseUtil.getStatusBarHeight(activity)
            layoutParams = params
        }

        mIvBack?.setOnClickListener {
            finish()
        }

        // 解锁VIP章节操作页  页面展示
        XMTraceApi.Trace()
            .pageView(45436, "unlockVipChapterOperationPage")
            .put("currPage", "unlockVipChapterOperationPage")
            .createTrace()
    }

    private var curStatus = false

    fun changeTitleBar(isWhiteBg: Boolean) {
        if (curStatus == isWhiteBg) {
            return
        }
        curStatus = isWhiteBg
        if (isWhiteBg) {
            mStatusBar?.setBackgroundResource(R.color.white)
            mTitleBar?.setBackgroundResource(R.color.white)
            mTvTitle?.setTextColor(resources.getColor(R.color.black))
            mIvBack?.setColorFilter(resources.getColor(R.color.black))
        } else {
            mStatusBar?.setBackgroundResource(R.color.host_transparent)
            mTitleBar?.setBackgroundResource(R.color.host_transparent)
            mTvTitle?.setTextColor(resources.getColor(R.color.white))
            mIvBack?.setColorFilter(resources.getColor(R.color.white))
        }
    }


    override fun loadData() {
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_lite_fra_album_unlock_step_layout
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // 解锁VIP章节操作页  页面离开
        XMTraceApi.Trace()
            .pageExit2(45437)
            .createTrace()
    }

    val mAdapter = object : Adapter<Holder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            return Holder(layoutInflater.inflate(R.layout.main_lite_item_album_unlock_step_layout, parent, false))
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            ImageManager.from(mContext).displayImageOfResourceId(holder.image, mList[position])

            val params = holder.image.layoutParams
            params.height = (BaseUtil.getScreenWidth(activity) / 375f * mListImgHeight[position]).toInt()
            holder.image.layoutParams = params
        }

        override fun getItemCount(): Int {
            return mList.size
        }
    }

    class Holder(item: View) : RecyclerView.ViewHolder(item) {
        val image: ImageView = item.findViewById(R.id.main_lite_iv_unlock_step)
    }
}