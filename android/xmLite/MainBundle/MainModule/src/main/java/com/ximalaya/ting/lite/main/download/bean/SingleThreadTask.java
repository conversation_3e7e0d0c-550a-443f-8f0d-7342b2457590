package com.ximalaya.ting.lite.main.download.bean;

import android.text.TextUtils;

import com.ximalaya.ting.android.opensdk.httputil.Config;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.download.connection.DownloadOkClientUrlConnection;
import com.ximalaya.ting.lite.main.download.connection.DownloadUrlConnection;
import com.ximalaya.ting.lite.main.download.engine.CoreSteamWriter;
import com.ximalaya.ting.lite.main.download.engine.Transport;
import com.ximalaya.ting.lite.main.download.inter.IDownloadConnection;
import com.ximalaya.ting.lite.main.download.inter.ITask;
import com.ximalaya.ting.lite.main.download.inter.ITaskCallback;
import com.ximalaya.ting.lite.main.download.utils.BufferDataMgr;
import com.ximalaya.ting.lite.main.download.utils.DownloadConst;
import com.ximalaya.ting.lite.main.download.utils.TaskCode;
import com.ximalaya.ting.lite.main.download.utils.TaskStateConst;
import com.ximalaya.ting.lite.main.download.utils.TaskUtil;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.text.MessageFormat;
import java.util.concurrent.BlockingQueue;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   : 单个下载任务线程
 */
public class SingleThreadTask implements Runnable, ITask, CoreSteamWriter.ITransmitCallback {

    private static final int CHECK_DONE = 0;
    private static final int CHECK__CONTINUE = 1;
    private static final int CHECK_ERROR = -1;
    private String tag;
    private int taskId;
    // 下载地址
    private final String downloadUrl;
    // 起始位置
    private final long beginPos;
    // 结束位置
    private final long endPos;
    // 初始化任务前已下载量
    private final long preDone;
    // 预期下载大小
    private final long expectSize;
    private long preCurSize;
    // 当前下载的起始位置（即时）
    private long curBeginPos;
    // 当前任务的完成量（即时）
    private long curDone = 0;
    // 网络预请求的字符串范围（即时）
    private String range = "";
    private long rangeBegin;
    private long rangeEnd;
    // 状态
    private int state = TaskStateConst.STATE_PENDING;
    // 错误码
    private int errorCode;
    // 取消控制器
    private Transport control = new Transport();
    // 任务回调
    private ITaskCallback callback;
    private BlockingQueue<BufferData> bufferQueue;
    private BufferDataMgr memoryBufferMgr;
    private IDownloadConnection downloadConnection;
    private Config config;
    private int downloadConnectionType;

    public SingleThreadTask(SingleTaskInfo info, String url, BlockingQueue<BufferData> queue, String tagName, BufferDataMgr memoryMgr,
                            int connectionType, Config configuration) {
        this(info, url, queue, tagName, memoryMgr, connectionType, configuration, null);
    }

    public SingleThreadTask(SingleTaskInfo info, String url, BlockingQueue<BufferData> queue, String tagName, BufferDataMgr memoryMgr,
                            int connectionType, Config configuration, IDownloadConnection connection) {
        tag = MessageFormat.format("Single \t{0}", tagName);
        taskId = info.taskId;
        downloadUrl = url;
        beginPos = info.beginPos;
        endPos = info.endPos;
        preDone = info.haveDoneSize;
        // 预期下载大小=前后位置之差
        expectSize = endPos - beginPos;
        curBeginPos = beginPos + preDone;
        bufferQueue = queue;
        memoryBufferMgr = memoryMgr;
        config = configuration;
        downloadConnection = connection;
        downloadConnectionType = connectionType;
    }

    @Override
    public void run() {
        try {
            start();
        } catch (Throwable error) {
            error.printStackTrace();
            errorCode = TaskCode.ERROR_UNCAPTURED_EXCEPTION;
            notifyStateChanged(TaskStateConst.STATE_ERROR);
        }
    }

    private void initRange() {
        rangeBegin = curBeginPos + curDone;
        rangeEnd = endPos - 1;
        range = "bytes=" + rangeBegin + "-" + rangeEnd;
    }

    public void stop() {
        if (state != TaskStateConst.STATE_RUNNING) {
            return;
        }
        control.requestCancel();
        Logger.d(tag, MessageFormat.format("请求任务：ID({0})", taskId));
        // 释放网络资源
        if (downloadConnection != null) {
            downloadConnection.disconnect();
        }
    }

    private boolean needContinueDownload() {
        if (preCurSize == curDone) {
            return false;
        }
        preCurSize = curDone;
        long done = curDone + preDone;
        long left = expectSize - done;
        return left > 0;
    }

    /**
     * 检查范围
     */
    private int checkRange() {
        long re = rangeEnd - rangeBegin;
        if (re == 0) {
            return CHECK_DONE;
        } else if (re > 0) {
            return CHECK__CONTINUE;
        } else {
            return CHECK_ERROR;
        }
    }

    public void download() throws IOException {

        switch (checkRange()) {
            case CHECK_DONE:
                return;
            case CHECK_ERROR:
                // 范围不对，传输异常，可能计算错误
                errorCode = TaskCode.ERROR_TRANSPORT_BREAK;
                throw new IOException("download process with wrong range");
            default:
                break;
        }

        Logger.d(tag, MessageFormat.format("开始下载：ID({0})", taskId));
        if (downloadConnection == null) {
            // 说明是断点续传，需要重新设置range范围
            if (config != null && config.property != null) {
                config.property.put("Range", range);
            }
            if (downloadConnectionType == DownloadConst.DOWNLOAD_URL_CONNECTION_OKHTTP) {
                downloadConnection = new DownloadOkClientUrlConnection.Factory().create(downloadUrl, config);
            } else {
                downloadConnection = new DownloadUrlConnection.Factory().create(downloadUrl, config);
            }
        }
        try {
            // 向服务端请求数据
            downloadConnection.execute();
        } catch (IOException ex) {
            // 请求的时候可能被人为的阻断
            if (control.cancel()) {
                return;
            } else {
                throw ex;
            }
        }
        int responseCode = downloadConnection.getResponseCode();
        if ((responseCode == 416 || responseCode == HttpURLConnection.HTTP_BAD_REQUEST) && !needContinueDownload()) {
            return;
        }
        String lenStr = downloadConnection.getResponseHeaderField("Content-Length");
        if (TextUtils.isEmpty(lenStr)) {
            lenStr = downloadConnection.getResponseHeaderField2("Content-Length");
        }
        if (!TextUtils.isEmpty(lenStr)) {
            Logger.d(tag, MessageFormat.format("Content-Range({0})", lenStr));
        } else {
            Logger.e(tag, "没有返回对应的Content-Range");
            notifyStateChanged(TaskStateConst.STATE_ERROR);
            return;
        }

        // 执行下载操作
        if (downloadConnection != null) {
            InputStream inputStream = downloadConnection.getInputStream();
            try {
                transformData(inputStream);
            } catch (Exception e) {
                Logger.e(tag, e.toString());
            } finally {
                if (inputStream != null) {
                    inputStream.close();
                }
            }
        }

    }

    private void transformData(InputStream inputStream) throws IOException {
        CoreSteamWriter writer = new CoreSteamWriter();
        writer.transmit(inputStream, this, control);
    }

    public boolean isError() {
        return state == TaskStateConst.STATE_ERROR;
    }

    public int getErrorCode() {
        return errorCode;
    }

    private void notifyStateChanged(int state) {
        this.state = state;
        callback.onStateChanged(state);

        switch (state) {
            case TaskStateConst.STATE_FINISH:
            case TaskStateConst.STATE_ERROR:
                long done = curDone + preDone;
                long left = expectSize - done;
                Logger.d(tag, MessageFormat.format("REQUEST RANGE({0}),DONE({1}),LEFT({2})", range, done, left));
                break;
            default:
                break;
        }
    }

    @Override
    public void start() {
        notifyStateChanged(TaskStateConst.STATE_RUNNING);
        boolean error = false;
        try {
            initRange();
            download();
        } catch (Exception e) {
            error = true;
            errorCode = TaskUtil.getErrorCode(e);
            Logger.e(tag, e.toString());
            notifyStateChanged(TaskStateConst.STATE_ERROR);
        } finally {
            if (!error) {
                notifyStateChanged(TaskStateConst.STATE_FINISH);
            }
            // 释放网络资源
            if (downloadConnection != null) {
                downloadConnection.disconnect();
            }
        }
    }

    @Override
    public void setCallback(ITaskCallback back) {
        callback = back;
    }

    @Override
    public void progress(byte[] buf, int done, int len) {
        if (control.cancel()) {
            return;
        }
        long beginP = curBeginPos + curDone;
        BufferData data = this.memoryBufferMgr.getBuffer();
        data.taskId = taskId;
        data.beginPos = beginP;
        System.arraycopy(buf, 0, data.buffer, 0, Math.min(buf.length, len));
        data.len = len;
        curDone += len;
        try {
            bufferQueue.put(data);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        callback.onProgressUpdate(len, done, expectSize);
    }
}
