package com.ximalaya.ting.lite.main.home.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Bundle;

import androidx.core.content.ContextCompat;

import android.text.Html;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.model.album.MainAlbumOtherData;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Lennon on 2017/11/9.
 * <p>
 * desc:首页推荐---水平布局的adapter
 * <p>
 * 一行3个item
 *
 * <AUTHOR>
 */
public class HomeRecommendAlbumHorizontalProvider implements IMulitViewTypeViewAndData<HomeRecommendAlbumHorizontalProvider.AlbumRowHolder, List<AlbumM>> {
    private Context context;
    private BaseFragment2 fragment;
    private IRecommendFeedItemActionListener recommendFeedItemActionListener;

    public HomeRecommendAlbumHorizontalProvider(BaseFragment2 fragment, IRecommendFeedItemActionListener recommendFeedItemActionListener) {
        this.fragment = fragment;
        this.context = fragment.getActivity();
        this.recommendFeedItemActionListener = recommendFeedItemActionListener;
    }

    @Override
    public void bindViewDatas(AlbumRowHolder holder, ItemModel<List<AlbumM>> t, View convertView, int position) {
        if (holder == null || t == null || ToolUtil.isEmptyCollects(t.getObject()) || !(t.getTag() instanceof MainAlbumMList))
            return;

        final MainAlbumMList mainAlbumMList = (MainAlbumMList) t.getTag();
        List<AlbumM> albumMList = t.getObject();

        Bundle bundle = t.getBundle();

        if (bundle != null && bundle.getBoolean(BundleKeyConstantsInMain.KEY_SHOW_BOTTOM_MARGIN)) {
            holder.mainViewBottomSplit.setVisibility(View.VISIBLE);
        } else {
            holder.mainViewBottomSplit.setVisibility(View.GONE);
        }

        for (int i = 0; i < albumMList.size(); i++) {
            final AlbumM albumM = albumMList.get(i);
            HomeRecommendAlbumHorizontalProvider.AlbumRowItemHolder itemHolder = holder.itemHolderList.get(i);

            StringBuilder titleBuilder = new StringBuilder();
            titleBuilder.append(albumM.getAlbumTitle());
            itemHolder.titleMainText.setText(Html.fromHtml(titleBuilder.toString(), getImageGetter(context), null));

            //设置图片
            String imageUrl = albumM.getLargeCover();
            ImageManager.from(context).displayImage(itemHolder.coverImage, imageUrl, R.drawable.host_default_album_145);
            itemHolder.coverImage.setTag(R.id.main_cate_recommend_row_album_item, albumM);
            itemHolder.coverImage.setOnClickListener(new AlbumItemClickListener(mainAlbumMList.getTitle(), i));
            AutoTraceHelper.bindData(itemHolder.coverImage, mainAlbumMList.getModuleType() + "", mainAlbumMList, albumM);

            //是否展示专辑评分
            MainAlbumOtherData mainAlbumOtherData = mainAlbumMList.getMainAlbumOtherData();
            //是否展示评分，默认不展示
            boolean isShowScore = false;
            //是否展示播放量，默认展示
            boolean isShowPlayCount = true;
            if (mainAlbumOtherData != null) {
                isShowScore = mainAlbumOtherData.showScore;
                //如果展示评分的话，就不展示播放量
                isShowPlayCount = false;
            }
            String albumScore = albumM.getAlbumScore();
            if (isShowScore &&
                    !TextUtils.isEmpty(albumScore) &&
                    !"0".equals(albumScore) &&
                    !"0.0".equals(albumScore) &&
                    !"0.00".equals(albumScore)
            ) {
                itemHolder.tvAlbumScore.setText(albumScore + "分");
                itemHolder.tvAlbumScore.setVisibility(View.VISIBLE);
                itemHolder.tvAlbumScoreShadow.setVisibility(View.VISIBLE);
            } else {
                itemHolder.tvAlbumScore.setText("");
                itemHolder.tvAlbumScore.setVisibility(View.GONE);
                itemHolder.tvAlbumScoreShadow.setVisibility(View.GONE);
            }

            //展示播放量
            if (isShowPlayCount) {
                itemHolder.tvPlayCount.setVisibility(View.VISIBLE);
                itemHolder.tvPlayCount.setText(StringUtil.getFriendlyNumStr(albumM.getPlayCount()));
            } else {
                itemHolder.tvPlayCount.setVisibility(View.GONE);
            }

            //是否支持一键播放
            itemHolder.ivPlayBtn.setVisibility(mainAlbumMList.isMusicSongList() ? View.VISIBLE : View.INVISIBLE);
            itemHolder.ivPlayBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    handlePlayBtnClicked(albumM, v, mainAlbumMList);
                }
            });

            if (AlbumTagUtil.getAlbumCoverTag((albumM)) != -1) {
                itemHolder.completeIcon.setImageResource(AlbumTagUtil.getAlbumCoverTag(albumM));
                itemHolder.completeIcon.setVisibility(View.VISIBLE);
            } else {
                itemHolder.completeIcon.setVisibility(View.INVISIBLE);
            }
        }
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_home_item_cate_recommend_row_item, parent, false);
    }

    @Override
    public AlbumRowHolder buildHolder(View convertView) {
        return new AlbumRowHolder(convertView);
    }

    private void handlePlayBtnClicked(AlbumM albumM, final View view, MainAlbumMList mainAlbumMList) {
        if (albumM != null) {
            Map<String, String> params = new HashMap<>();
            params.put(DTransferConstants.PAGE, "1");
            params.put(DTransferConstants.PRE_PAGE, "0");
            params.put(HttpParamsConstants.PARAM_URL_FROM, AlbumEventManage.URL_FROM_ALBUM_TRACKLIST);
            params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
            params.put(HttpParamsConstants.PARAM_ALBUM_ID, albumM.getId() + "");
            params.put(HttpParamsConstants.PARAM_IS_ASC, String.valueOf(true));
            params.put(HttpParamsConstants.PARAM_DEVICE, "android");
            CommonRequestM.getAlbumInfo(params, new IDataCallBack<AlbumM>() {
                @Override
                public void onSuccess(final AlbumM loadAlbumInfo) {
                    if (loadAlbumInfo != null && loadAlbumInfo.getCommonTrackList() != null) {
                        PlayTools.playCommonList(fragment.getActivity(), loadAlbumInfo.getCommonTrackList(), 0, true, view);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (fragment.canUpdateUi()) {
                        ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
                    }
                }
            });
        }
    }

    public static Html.ImageGetter getImageGetter(final Context context) {
        Html.ImageGetter imageGetter = new Html.ImageGetter() {
            public Drawable getDrawable(String source) {
                int id = Integer.parseInt(source);
                Drawable d = ContextCompat.getDrawable(context, id);
                d.setBounds(0, 0, d.getIntrinsicWidth(), d.getIntrinsicHeight());
                return d;
            }
        };
        return imageGetter;
    }

    class AlbumItemClickListener implements View.OnClickListener {

        private String mModuleTitle;
        private int mPosition;

        AlbumItemClickListener(String moduleTitle, int position) {
            mModuleTitle = moduleTitle;
            mPosition = position;
        }

        @Override
        public void onClick(View v) {
            AlbumM album = (AlbumM) v.getTag(R.id.main_cate_recommend_row_album_item);
            if (!TextUtils.isEmpty(album.getContentType())) {
                fragment.startFragment(NativeHybridFragment.newInstance(UrlConstants.getInstanse().getSubjectDetailPageUrl(album.getSpecialId() + ""), true), fragment.getContainerView());
            } else {
                AlbumEventManage.startMatchAlbumFragment(album, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, album.getRecSrc(), album.getRecTrack(), -1, fragment.getActivity());
            }
            // RecommendItemNew 是为null....吧？
            // 只有推荐流的点击才有请求实时推荐
//            notifyItemAction(album, IRecommendFeedItemActionListener.ActionType.CLICK, null, null);
        }
    }

    private void notifyItemAction(AlbumM album, IRecommendFeedItemActionListener.ActionType actionType, RecommendItemNew itemData, ItemModel itemModel) {
        if (recommendFeedItemActionListener == null) {
            return;
        }
        if (album != null) {
            recommendFeedItemActionListener.onItemAction(IRecommendFeedItemActionListener.FeedItemType.ALBUM, album.getId(), actionType, album.getCategoryId(), itemData, itemModel);
        }
    }

    public static class AlbumRowHolder extends HolderAdapter.BaseViewHolder {

        View mainViewBottomSplit;

        List<HomeRecommendAlbumHorizontalProvider.AlbumRowItemHolder> itemHolderList;

        public AlbumRowHolder(View convertView) {

            mainViewBottomSplit = convertView.findViewById(R.id.mainViewBottomSplit);

            itemHolderList = new ArrayList<>(3);
            itemHolderList.add(new HomeRecommendAlbumHorizontalProvider.AlbumRowItemHolder(convertView.findViewById(R.id.main_sect_1)));
            itemHolderList.add(new HomeRecommendAlbumHorizontalProvider.AlbumRowItemHolder(convertView.findViewById(R.id.main_sect_2)));
            itemHolderList.add(new HomeRecommendAlbumHorizontalProvider.AlbumRowItemHolder(convertView.findViewById(R.id.main_sect_3)));
        }
    }

    public static class AlbumRowItemHolder {
        ImageView coverImage;
        ImageView completeIcon;
        TextView titleMainText;
        ImageView ivPlayBtn;
        TextView tvPlayCount;
        TextView tvAlbumScore;
        View tvAlbumScoreShadow;

        public AlbumRowItemHolder(View view) {
            coverImage = (ImageView) view.findViewById(R.id.main_tiv_cover);
            completeIcon = (ImageView) view.findViewById(R.id.main_iv_album_complete);
            titleMainText = (TextView) view.findViewById(R.id.main_tv_name);
            ivPlayBtn = view.findViewById(R.id.main_iv_play_btn);
            tvPlayCount = view.findViewById(R.id.main_tv_play_count);
            tvAlbumScore = view.findViewById(R.id.main_album_score);
            tvAlbumScoreShadow = view.findViewById(R.id.main_album_score_shadow);
        }
    }
}
