package com.ximalaya.ting.lite.main.home.viewmodel;

/**
 * Created by q<PERSON><PERSON>feng on 2021/1/15
 *
 * <AUTHOR>
 */
public class HomeSubscribeHistoryViewModel {

    //订阅页面使用的数据
    public HomeSubscribePageViewModel subscribePageViewModel;

    //播放历史页面使用的数据
    public HomeHistoryPageViewModel historyPageViewModel;

    //初始化请求完成，可以隐藏占位图了
    public boolean initRequestFinish = false;

    public HomeSubscribeHistoryViewModel() {

    }

    public void fillViewModel(HomeSubscribeHistoryViewModel viewModel) {
        if (viewModel == null) {
            return;
        }
        if (viewModel.subscribePageViewModel != null) {
            if (this.subscribePageViewModel == null) {
                this.subscribePageViewModel = new HomeSubscribePageViewModel();
            }
            this.subscribePageViewModel.fillViewModel(viewModel.subscribePageViewModel);
        }
        if (viewModel.historyPageViewModel != null) {
            if (this.historyPageViewModel == null) {
                this.historyPageViewModel = new HomeHistoryPageViewModel();
            }
            this.historyPageViewModel.fillViewModel(viewModel.historyPageViewModel);
        }
        if (viewModel.initRequestFinish) {
            this.initRequestFinish = true;
        }
    }
}
