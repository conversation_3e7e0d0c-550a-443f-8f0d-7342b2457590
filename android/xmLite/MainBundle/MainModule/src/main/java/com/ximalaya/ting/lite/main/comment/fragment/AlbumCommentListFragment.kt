package com.ximalaya.ting.lite.main.comment.fragment

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.AdapterView
import android.widget.RelativeLayout
import android.widget.TextView
import com.handmark.pulltorefresh.library.PullToRefreshBase
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.model.dialog.BaseDialogModel
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.host.view.BaseBottomDialog
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.comment.*
import com.ximalaya.ting.lite.main.comment.CommentListPresenter.Companion.FETCH_MODE_HOT
import com.ximalaya.ting.lite.main.comment.CommentListPresenter.Companion.FETCH_MODE_NEW
import com.ximalaya.ting.lite.main.comment.CommentListPresenter.Companion.FRAGMENT_PAGE_TYPE_COMMENT
import com.ximalaya.ting.lite.main.comment.adapter.AlbumCommentListAdapter
import com.ximalaya.ting.lite.main.comment.adapter.CommentListAdapter
import com.ximalaya.ting.lite.main.comment.adapter.CommentListAdapter.Companion.TYPE_COMMENT
import com.ximalaya.ting.lite.main.comment.entities.CommentListItemBean
import com.ximalaya.ting.lite.main.comment.fragment.AlbumCommentReplyListFragment.Companion.ARGS_CURRENT_PARENT_COMMENT_DATA
import com.ximalaya.ting.lite.main.comment.view.CommentEditPreviewTextView
import com.ximalaya.ting.lite.main.comment.view.UserEditCommentDialog
import java.util.*


/**
 *  @Author: Junxiang Cheng
 *  @Mail: <EMAIL>
 *  @CreateTime: 12/24/21
 *
 *  @Description:评论主列表页面
 */
class AlbumCommentListFragment : BaseFragment2(),
    View.OnClickListener,
    CommentListListener,
    ICommentCountListener,
    UserEditCommentDialog.OnTextConfirmListener,
    AlbumCommentListAdapter.OnCommentItemClickListener {

    private var mTvTitle: TextView? = null
    private var mTvHot: TextView? = null
    private var mTvNew: TextView? = null
    private var mViewEditPreview: CommentEditPreviewTextView? = null
    private var mEmptyView: RelativeLayout? = null

    lateinit var mList: RefreshLoadMoreListView
    private var mAdapter: AlbumCommentListAdapter? = null

    private var mCurrentReplyTargetData: CommentListItemBean? = null
    lateinit var presenter: CommentListPresenter

    private var mTraced = false

    companion object {
        const val TAG: String = "AlbumCommentListFragment"
    }

    override fun getPageLogicName() = TAG
    override fun getContainerLayoutId() = R.layout.main_fra_comment_list
    override fun getTitleBarResourceId() = -1

    override fun initUi(savedInstanceState: Bundle?) {
        presenter = getCommentListPresenter()
        mTvTitle = findViewById(R.id.main_tv_comment_title)
        mTvNew = findViewById(R.id.main_tv_new)
        mTvHot = findViewById(R.id.main_tv_hot)
        mList = findViewById(R.id.main_rv_comment_list)
        mViewEditPreview = findViewById(R.id.main_view_comment_preview_edit)
        mEmptyView = findViewById(R.id.main_rl_empty_view)

        mTvHot?.setOnClickListener(this)
        mTvNew?.setOnClickListener(this)
        mViewEditPreview?.setOnClickListener(this)
        mViewEditPreview?.setHint(getString(R.string.main_comment_preview_default_hint))

        presenter.commentListListener = this
        presenter.setCommentCountListener(this)
        presenter.commentList = ArrayList()
        mList.apply {
            isSendScrollListener = false
            preLoadMoreItemCount = -1

            refreshableView.divider = null

            mode = PullToRefreshBase.Mode.DISABLED

            setOnRefreshLoadMoreListener(object : IRefreshLoadMoreListener {
                override fun onRefresh() {
                    presenter.requestAlbumCommentList(refresh = true)
                }

                override fun onMore() {
                    presenter.requestAlbumCommentList(refresh = false)
                }
            })

            mAdapter = AlbumCommentListAdapter(
                TYPE_COMMENT,
                context,
                presenter,
                presenter.commentList,
                this@AlbumCommentListFragment
            )
            setAdapter(mAdapter)

            setScrollViewListener(this)
        }
    }

    private fun setCommentCount(count: Long) {
        mTvTitle?.text = if (count == 0L) {
            "评论"
        } else {
            "评论（${StringUtil.getFriendlyNumStrEn(count)}）"
        }
    }

    override fun loadData() {
        presenter.requestAlbumCommentList(refresh = true)
        onPageLoadingCompleted(LoadCompleteType.LOADING)
    }


    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.main_tv_new -> {
                // 声音评论-父评论浮层-最新  弹框控件点击
                XMTraceApi.Trace()
                    .setMetaId(41442)
                    .setServiceId("dialogClick")
                    .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
                    .put("trackId", presenter.mCurTrack?.dataId.toString())
                    .createTrace()

                presenter.switchFetchMode(FETCH_MODE_NEW)
                mTvNew?.setTextColor(resources.getColor(R.color.host_color_111111))
                mTvHot?.setTextColor(resources.getColor(R.color.host_color_999999))
            }
            R.id.main_tv_hot -> {
                // 声音评论-父评论浮层-最热  弹框控件点击
                XMTraceApi.Trace()
                    .setMetaId(41441)
                    .setServiceId("dialogClick")
                    .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
                    .put("trackId", presenter.mCurTrack?.dataId.toString())
                    .createTrace()

                presenter.switchFetchMode(FETCH_MODE_HOT)
                mTvHot?.setTextColor(resources.getColor(R.color.host_color_111111))
                mTvNew?.setTextColor(resources.getColor(R.color.host_color_999999))
            }
            R.id.main_view_comment_preview_edit -> {

            }
        }
    }

    // 拉起评论输入
    override fun startComment(targetParentComment: CommentListItemBean?) {

        // 声音评论-父评论浮层-回复入口
        XMTraceApi.Trace()
            .setMetaId(if (targetParentComment == null) 41443 else 41444)
            .setServiceId("dialogClick")
            .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
            .put("trackId", presenter.mCurTrack?.dataId.toString())
            .createTrace()

        val hint = if (targetParentComment == null) {
            mCurrentReplyTargetData = null
            mViewEditPreview?.getHint().toString()
        } else {
            mCurrentReplyTargetData = targetParentComment
            "@${targetParentComment.user?.nickname}："
        }

        val cachedText = mViewEditPreview?.getCachedText()

        val userEditCommentDialog = UserEditCommentDialog(mActivity, hint, cachedText)
        userEditCommentDialog.setOnTextConfirmListener(this)
        userEditCommentDialog.show()
    }

    // 跳转回复列表
    override fun startReplyList(targetParentComment: CommentListItemBean) {
        targetParentComment.let { data ->
            val bundle = Bundle()
            bundle.putParcelable(ARGS_CURRENT_PARENT_COMMENT_DATA, data)
            parentFragment?.also {
                if (it is AlbumCommentTabFragment) {
                    it.showCommentDetailFragment(bundle)
                }
            }
        }
    }

    //长按删除
    override fun onLongClickDelete(position: Int, data: CommentListItemBean) {
        val models: MutableList<BaseDialogModel> = ArrayList()
        models.add(BaseDialogModel(R.drawable.host_ic_comment_delete, "删除", 0))
        object : BaseBottomDialog(mActivity, models) {
            override fun onItemClick(parent: AdapterView<*>?, view: View, position: Int, id: Long) {
                dismiss()

                // 声音评论-父评论浮层-删除按钮  弹框控件点击
                XMTraceApi.Trace()
                    .setMetaId(41449)
                    .setServiceId("dialogClick")
                    .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
                    .put("trackId", presenter.mCurTrack?.dataId.toString())
                    .createTrace()

                if (data.replyCount > 0 || CollectionUtil.isNotEmpty(data.replys)) {
                    val dialog = DialogBuilder<DialogBuilder<*>>(activity)
                        .setTitleVisibility(false)
                        .setMessage(R.string.main_delete_parent_confirm)
                        .setOkBtn {
                            confirmDeleteParentComment(data)
                        }
                    dialog.showConfirm()
                } else {
                    confirmDeleteParentComment(data)
                }
            }
        }.show()
        // 声音评论-父评论浮层-删除按钮  控件曝光
        XMTraceApi.Trace()
            .setMetaId(41448)
            .setServiceId("slipPage")
            .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
            .put("trackId", presenter.mCurTrack?.dataId.toString())
            .createTrace()
    }

    fun confirmDeleteParentComment(data: CommentListItemBean) {
        presenter.deleteAlbumComment(
            pageType = FRAGMENT_PAGE_TYPE_COMMENT,
            data = data,
            commentRequestListener = object : ICommentRequestListener {
                override fun onSuccess() {
                    if (!canUpdateUi()) {
                        return
                    }
                    CustomToast.showSuccessToast("评论删除成功")
                }

                override fun onError(message: String?) {
                    CustomToast.showFailToast(message ?: "评论删除失败")
                }

            }
        )
    }

    fun updateData() {
        mAdapter?.notifyDataSetChanged()
        checkEmpty()
    }

    private fun checkEmpty() {
        if (CollectionUtil.isNullOrEmpty(presenter.commentList)) {
            mEmptyView?.visibility = View.VISIBLE
        } else {
            mEmptyView?.visibility = View.GONE
        }
    }

    override fun onSuccessUpdateCommentList(
        data: List<CommentListItemBean>?,
        refresh: Boolean,
        hasMore: Boolean
    ) {
        if (!canUpdateUi()) {
            return
        }
        updateData()
        onPageLoadingCompleted(LoadCompleteType.OK)

        if (!mTraced) {
            mTraced = true
            // 声音评论-父评论浮层  弹框展示

            //状态：1-有评论；2-无评论；
            val status = if (CollectionUtil.isNullOrEmpty(data)) "2" else "1"

            XMTraceApi.Trace()
                .setMetaId(41440)
                .setServiceId("dialogView")
                .put("status", status)
                .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
                .put("trackId", presenter.mCurTrack?.dataId.toString())
                .createTrace()
        }

        if (refresh) {
            mList?.refreshableView?.smoothScrollToPosition(0)
            mList?.onRefreshComplete(hasMore)
        }
        mList?.hasMore = hasMore

    }

    override fun onUpdateList(force: Boolean) {
        if (!canUpdateUi()) {
            return
        }
        updateData()
    }

    override fun onRequestFailed() {
        if (!canUpdateUi()) {
            return
        }
        updateData()
        mList?.onRefreshComplete(true)
        onPageLoadingCompleted(LoadCompleteType.OK)
    }

    override fun onConfirm(cs: CharSequence) {
    }

    override fun onCancel(cs: CharSequence?) {
        mViewEditPreview?.setCachedText(cs)
        mCurrentReplyTargetData = null
    }

    override fun onCommentCountUpdated(commentCount: Long, isAdd: Boolean) {
        if (parentFragment is AlbumCommentTabFragment) {
            (parentFragment as AlbumCommentTabFragment).setCommentCount(commentCount, isAdd)
        }
    }

    fun getCommentListPresenter(): CommentListPresenter {
        if (parentFragment is AlbumCommentTabFragment) {
            return (parentFragment as AlbumCommentTabFragment).updatePresent(CommentListPresenter())
        }
        return CommentListPresenter()
    }

}