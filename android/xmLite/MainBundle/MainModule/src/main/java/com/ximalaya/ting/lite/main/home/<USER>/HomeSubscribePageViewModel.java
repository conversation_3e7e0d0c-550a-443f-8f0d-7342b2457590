package com.ximalaya.ting.lite.main.home.viewmodel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qinhuifeng on 2021/1/18
 * <p>
 * 订阅页面对应的数据model
 *
 * <AUTHOR>
 */
public class HomeSubscribePageViewModel {

    //添加专辑model，缓存使用
    private HomeItemSubscriptionViewModel addItemModel;
    //更多按钮model，缓存使用
    private HomeItemSubscriptionViewModel moreItemModel;
    //中间线model，缓存使用
    private HomeItemSubscriptionViewModel lineItemModel;


    //订阅数记录，更新的时候使用
    private HomeSubscribeCountRecond subscribeCountRecond;
    //订阅相关model
    public List<HomeItemSubscriptionViewModel> subscriptionItemModelList;

    //推荐相关model
    public List<HomeItemSubscriptionViewModel> recommendItemModelList;

    //是否是加载推荐下一页的数据
    public boolean isRecommendLoadMore = false;

    public void fillViewModel(HomeSubscribePageViewModel viewModel) {
        if (viewModel == null) {
            return;
        }
        //更新订阅数量
        if (viewModel.subscribeCountRecond != null) {
            this.updateSubscribeCount(viewModel.subscribeCountRecond.hasSubscribeCount);
        }
        //禁止判断.size=0,等于0的时候清除数据
        if (viewModel.subscriptionItemModelList != null) {
            if (this.subscriptionItemModelList == null) {
                this.subscriptionItemModelList = new ArrayList<>();
            }
            this.subscriptionItemModelList.clear();
            this.subscriptionItemModelList.addAll(viewModel.subscriptionItemModelList);
        }
        //禁止判断.size=0,等于0的时候清除数据
        if (viewModel.recommendItemModelList != null) {
            if (this.recommendItemModelList == null) {
                this.recommendItemModelList = new ArrayList<>();
            }
            if (!viewModel.isRecommendLoadMore) {
                this.recommendItemModelList.clear();
            }
            this.recommendItemModelList.addAll(viewModel.recommendItemModelList);
        }
    }

    public List<HomeItemSubscriptionViewModel> build() {
        List<HomeItemSubscriptionViewModel> allData = new ArrayList<>();
        if (subscriptionItemModelList != null) {
            allData.addAll(subscriptionItemModelList);
            //订阅数小于20，在最后添加订阅按钮
            if (getSubscribeCount() < 20) {
                allData.add(createAddItemModel());
            } else {
                //订阅数量大于等于20的时候需要添加更多
                allData.add(createMoreItemModel());

                //如果更多展示了，清除掉推荐内容
                if (recommendItemModelList != null) {
                    recommendItemModelList.clear();
                }
            }
        } else {
            //没有订阅的时候也需要添加订阅按钮
            allData.add(createAddItemModel());
        }

        if (recommendItemModelList != null && recommendItemModelList.size() > 0) {
            //如果推荐前有数据，添加订阅推荐的线
            if (allData.size() > 0) {
                allData.add(createLineItemModel());
            }
            //添加推荐数据
            allData.addAll(recommendItemModelList);
        }

        //如果全部都为空了，也展示添加订阅
        if (allData.size() == 0) {
            allData.add(createAddItemModel());
        }
        return allData;
    }

    private HomeItemSubscriptionViewModel createAddItemModel() {
        if (addItemModel == null) {
            addItemModel = new HomeItemSubscriptionViewModel();
            //订阅数量赋值
            addItemModel.viewType = HomeItemSubscriptionViewModel.ITEM_ADD;
        }
        //更新添加更多的数量
        addItemModel.hasSubscribeCount = getSubscribeCount();
        return addItemModel;
    }


    private HomeItemSubscriptionViewModel createMoreItemModel() {
        if (moreItemModel == null) {
            moreItemModel = new HomeItemSubscriptionViewModel();
            moreItemModel.viewType = HomeItemSubscriptionViewModel.ITEM_MORE;
        }
        return moreItemModel;
    }

    private HomeItemSubscriptionViewModel createLineItemModel() {
        if (lineItemModel == null) {
            lineItemModel = new HomeItemSubscriptionViewModel();
            lineItemModel.viewType = HomeItemSubscriptionViewModel.ITEM_LINE;
        }
        return lineItemModel;
    }


    //更新订阅数
    public void updateSubscribeCount(int count) {
        if (subscribeCountRecond == null) {
            subscribeCountRecond = new HomeSubscribeCountRecond();
        }
        subscribeCountRecond.hasSubscribeCount = count;
    }

    //获取订阅数
    public int getSubscribeCount() {
        if (subscribeCountRecond != null) {
            return subscribeCountRecond.hasSubscribeCount;
        }
        return 0;
    }
}
