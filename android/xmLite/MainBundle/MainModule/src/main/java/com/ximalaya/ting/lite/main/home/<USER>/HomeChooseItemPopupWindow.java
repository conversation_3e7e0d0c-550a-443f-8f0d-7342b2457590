package com.ximalaya.ting.lite.main.home.view;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Build;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.GridView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.data.model.category.Tag;
import com.ximalaya.ting.android.host.manager.download.DownloadXmlyFullManager;
import com.ximalaya.ting.android.host.util.common.PackageUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.util.List;

/**
 * <AUTHOR> on 2017/11/30.
 */
public class HomeChooseItemPopupWindow extends PopupWindow implements View.OnClickListener, View.OnKeyListener {

    //一行展示3个
    private static final int NUM_COLUMNS = 3;

    private OnChosenChangeListener mListener;
    private int mChosenPosition;
    private BaseAdapter mAdapter;
    private View mContainer;
    private Activity mActivity;
    private View mContentView;
    private TextView mTvDownloadXmfull;
    private RelativeLayout nRlDownInfo;
    private GridView mGvItems;
    private List<Tag> mTagList;

    public HomeChooseItemPopupWindow(Activity context, List<Tag> items, int chosenPosition) {
        super(context);
        mActivity = context;
        mChosenPosition = chosenPosition;
        mContentView = LayoutInflater.from(context).inflate(R.layout.main_home_choose_choose_category, null);
        mTagList = items;

        mTvDownloadXmfull = mContentView.findViewById(R.id.main_tv_download_xmfull);
        nRlDownInfo = mContentView.findViewById(R.id.main_rl_down_info);
        mContentView.findViewById(R.id.main_pull_down_btn_up).setOnClickListener(this);
        mContentView.findViewById(R.id.main_ll_container).setOnClickListener(this);
        mTvDownloadXmfull.setOnClickListener(this);

        mContainer = mContentView.findViewById(R.id.main_category_layout);
        mGvItems = mContentView.findViewById(R.id.main_gv_items);

        int screenWidth = BaseUtil.getScreenWidth(context);
        int dp16 = BaseUtil.dp2px(context, 16);
        int dp100 = BaseUtil.dp2px(context, 100);
        //屏幕宽度减去左右间距16*2，每个item的宽度是100，减去所有的宽度，计算HorizontalSpacing
        int horizontalSpacing = (screenWidth - dp16 * 2 - dp100 * NUM_COLUMNS) / 2;
        mGvItems.setNumColumns(3);
        mGvItems.setHorizontalSpacing(horizontalSpacing);

        mAdapter = new ChooseItemAdapter(context, mTagList);
        mGvItems.setAdapter(mAdapter);

        if (PackageUtil.isXimalyaFullInstalled(context)) {
            //已经安装了喜马拉雅完整版
            nRlDownInfo.setVisibility(View.GONE);
        } else {
            nRlDownInfo.setVisibility(View.VISIBLE);
        }

        mContentView.setFocusable(true);
        mContentView.setFocusableInTouchMode(true);
        mContentView.setOnKeyListener(this);
        setAnimationStyle(0);
        setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        setBackgroundDrawable(null);
        setTouchable(true);
        setFocusable(true);
        setContentView(mContentView);
    }

    public void setChosenPosition(int position) {
        mChosenPosition = position;
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void showAsDropDown(View anchor) {
        if (isShowing()) {
            return;
        }
        //每次show的时候，判断一次，解决安装完整或者卸载了主app，此处没有更新
        if (PackageUtil.isXimalyaFullInstalled(mActivity)) {
            //已经安装了喜马拉雅完整版
            nRlDownInfo.setVisibility(View.GONE);
        } else {
            nRlDownInfo.setVisibility(View.VISIBLE);
            trackXimalyaFullVisible();
        }
        //fix 解决7.0以上popupwindow显示位置不正确
        if (Build.VERSION.SDK_INT >= 24) {
            Rect rect = new Rect();
            anchor.getGlobalVisibleRect(rect);
            int screenBottom = 0;
            //测量MainActivity底部tab的可见区域，
            //主要fix用getDisplayMetrics().heightPixels获取到的屏幕高度在刘海屏，全面屏上没有包含状态栏高度问题
            //还有底部出现或者隐藏虚拟按键问题处理
            if (mActivity instanceof MainActivity) {
                Rect bottomTabVisibleRect = ((MainActivity) mActivity).getBottomTabVisibleRect();
                if (bottomTabVisibleRect != null) {
                    screenBottom = bottomTabVisibleRect.bottom;
                }
            }
            if (screenBottom == 0) {
                //没有获取到底部的测量高度
                screenBottom = anchor.getResources().getDisplayMetrics().heightPixels;
            }
            int popuHeight = screenBottom - rect.bottom;
            setHeight(popuHeight);
        }
        super.showAsDropDown(anchor);
        int measuredHeight = mContainer.getMeasuredHeight();
        if (measuredHeight <= 0) {  //第一次show时，mContainer还未进行measure操作
            mContentView.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
                @Override
                public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                    v.removeOnLayoutChangeListener(this);
                    showAnimation();
                }
            });
        } else {
            showAnimation();
        }
    }

    private void showAnimation() {
        //去掉动画，fix一直快速点击位置错误
//        int duration = 350;
//        isAnimationRuning = true;
//        HandlerManager.postOnUIThreadDelay(new Runnable() {
//            @Override
//            public void run() {
//                isAnimationRuning = false;
//            }
//        }, duration);
//        float curTranslationY = mContainer.getTranslationY();
//        int measuredHeight = mContainer.getMeasuredHeight();
//        ObjectAnimator animator = ObjectAnimator.ofFloat(mContainer, "translationY", -measuredHeight, curTranslationY);
//        animator.setDuration(duration);
//        ObjectAnimator animatorAlpha = ObjectAnimator.ofFloat(nRlDownInfo, "alpha", 0.0f, 1.0f);
//        animatorAlpha.setDuration(duration);
//        animatorAlpha.start();
//        animator.start();
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        if (id == R.id.main_pull_down_btn_up || id == R.id.main_ll_container) {
            dismiss();
            return;
        }
        if (id == R.id.main_tv_download_xmfull) {
            dismiss();
            //下载喜马完整版
            dealWithDownXmlyFullClick(v);
            return;
        }
    }

    /**
     * 下载喜马拉雅完整版
     */
    private void dealWithDownXmlyFullClick(View v) {
        trackXimalyaFullDownloadClick();
        try {
            Uri uri = Uri.parse("market://details?id=com.ximalaya.ting.android");
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mActivity.startActivity(intent);
        } catch (Throwable e) {
            e.printStackTrace();
            CustomToast.showFailToast("应用市场打开失败");
        }
    }


    /**
     * 下载按钮露出埋点
     */
    private void trackXimalyaFullVisible() {
        new XMTraceApi.Trace()
                .setMetaId(6024)
                .setServiceId("dialogView")
                .put("currPage", "listeningToBook")
                .put("dialogTitle", "请选择分类")
                .createTrace();
    }

    /**
     * 下载按钮点击埋点
     */
    private void trackXimalyaFullDownloadClick() {
        new XMTraceApi.Trace()
                .setMetaId(6025)
                .setServiceId("dialogClick")
                .put("currPage", "listeningToBook")
                .put("dialogTitle", "请选择分类")
                .put("item", "download")
                .createTrace();
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            dismiss();
            return true;
        }
        return false;
    }

    private class ChooseItemAdapter extends HolderAdapter<Tag> {
        @Override
        public void onClick(View view, Tag t, int position, BaseViewHolder holder) {
            if (!OneClickHelper.getInstance().onClick(view)) {
                return;
            }
            mChosenPosition = position;
            notifyDataSetChanged();
            dismiss();
            if (mListener != null) {
                mListener.onChosenChange(mChosenPosition, position);
            }
        }

        public ChooseItemAdapter(Context context, List<Tag> listData) {
            super(context, listData);
        }

        @Override
        public int getConvertViewId() {
            return R.layout.main_item_home_choose_item_grid;
        }

        @Override
        public BaseViewHolder buildHolder(View convertView) {
            ChooseItemAdapter.ViewHolder holder = new ChooseItemAdapter.ViewHolder(convertView);
            return holder;
        }

        @Override
        public void bindViewDatas(BaseViewHolder holder, Tag t, int position) {
            ChooseItemAdapter.ViewHolder h = (ChooseItemAdapter.ViewHolder) holder;
            h.tvName.setText(t.getKeywordName());
            h.tvName.setTextColor(Color.parseColor(position == mChosenPosition ? "#ffffff" : mActivity.getString(R.string.main_color_black)));
            h.tvName.setBackgroundResource(position == mChosenPosition ? R.drawable.main_round_bg_red_radius_100 : R.drawable.main_round_bg_radius_f3f4f5_dp100);
            setClickListener(h.tvName, t, position, holder);
        }

        class ViewHolder extends BaseViewHolder {
            TextView tvName;

            public ViewHolder(View root) {
                this.tvName = root.findViewById(R.id.main_tv_item_title);
            }
        }
    }

    public void setOnChosenChangeListener(OnChosenChangeListener listener) {
        this.mListener = listener;
    }

    public interface OnChosenChangeListener {
        void onChosenChange(int chosenId, int chosenIdPosition);
    }
}
