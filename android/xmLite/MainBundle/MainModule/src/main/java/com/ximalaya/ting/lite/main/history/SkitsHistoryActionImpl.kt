package com.ximalaya.ting.lite.main.history

import com.ximalaya.ting.android.host.db.model.SkitsHistoryInfo
import com.ximalaya.ting.android.host.db.utils.BookUtils
import com.ximalaya.ting.android.host.db.utils.SkitsHistoryUtils
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.ISkitsHistoryAction
import com.ximalaya.ting.lite.main.history.presenter.ISkitsHistoryPresenter
import com.ximalaya.ting.lite.main.history.presenter.SkitsHistoryPresenter

class SkitsHistoryActionImpl : ISkitsHistoryAction {

    private var mPresenter: ISkitsHistoryPresenter = SkitsHistoryPresenter(null)

    override fun addHistory(skitsHistoryInfo: SkitsHistoryInfo, needSync: Boolean) {
        skitsHistoryInfo.lastUpdatedTime = 0L
        SkitsHistoryUtils.operatingData(skitsHistoryInfo, BookUtils.TYPE_ADD)
        if (needSync) {
            mPresenter.syncHistoryModifyRecord()
        }
    }

    override fun delHistory(albumId: Long, skitsId: Long) {
        val info = SkitsHistoryInfo()
        info.albumId = albumId
        info.skitsId = skitsId
        SkitsHistoryUtils.operatingData(info, BookUtils.TYPE_DEL)
        mPresenter.syncHistoryModifyRecord()
    }


    override fun onLogin() {
        mPresenter.syncHistoryModifyRecord()
    }

    override fun syncHistory() {
        mPresenter.syncHistoryModifyRecord()
    }

    override fun queryHistoryAmount(): Int {
        return mPresenter.queryHistoryAmount()
    }
}