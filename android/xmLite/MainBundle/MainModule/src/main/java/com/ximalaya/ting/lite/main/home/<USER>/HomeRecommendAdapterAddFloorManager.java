package com.ximalaya.ting.lite.main.home.manager;

import android.app.Activity;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.text.TextUtils;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.model.ad.BannerModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.AlbumTrackInfo;
import com.ximalaya.ting.android.host.model.album.NewUserMustListenerModel;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.lite.main.home.HomeSubscribeFloorPresenter;
import com.ximalaya.ting.lite.main.home.HomeTingUpdatePresenter;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendAdapter;
import com.ximalaya.ting.lite.main.home.listener.NormalTitleClickListener;
import com.ximalaya.ting.lite.main.home.presenter.HomeRecommendContact;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeAlbumRankFloorViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeSubscribeHistoryViewModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeSubscribePageViewModel;
import com.ximalaya.ting.lite.main.model.ListViewNoContentModel;
import com.ximalaya.ting.lite.main.model.album.CategoryRecommendMList;
import com.ximalaya.ting.lite.main.model.album.CategoryRecommendRefresh;
import com.ximalaya.ting.lite.main.model.album.FeedStreamOtherData;
import com.ximalaya.ting.lite.main.model.album.HomeAlbumRankItem;
import com.ximalaya.ting.lite.main.model.album.HomeTingUpdateModel;
import com.ximalaya.ting.lite.main.model.album.HorizontalScrollAlbumModel;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.lite.main.model.album.RecommendAlbumItem;
import com.ximalaya.ting.lite.main.model.album.RecommendDiscoveryM;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;
import com.ximalaya.ting.lite.main.model.album.RecommendTrackItem;
import com.ximalaya.ting.lite.main.model.album.TanghuluHotWord;
import com.ximalaya.ting.lite.main.model.album.TitleModule;
import com.ximalaya.ting.android.host.model.track.EverydayUpdateTrack;
import com.ximalaya.ting.lite.main.model.vip.VipInfoModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qinhuifeng on 2019-08-05
 *
 * <AUTHOR>
 */
public class HomeRecommendAdapterAddFloorManager {

    public static final String FEED_STREAM = "feedStream";
    private HomeRecommendAdapter mAdapter;
    private HomeRecommendContact.IFragmentView mIFragment;
    private Activity mActivity;
    //信息流id
    private long mRecommendFeedChannelId = -1;
    //有限信息流请求使用的参数
    private FeedStreamOtherData mFeedStreamOtherData;
    private int from = -1;
    //听更新楼层管理
    private HomeTingUpdatePresenter mTingUpdateFloor;
    //订阅历史楼层管理
    private HomeSubscribeFloorPresenter mSubscribeFloorPresenter;

    //排行榜楼层支持多个楼层展示，解决同一个页面展示多个viewpager问题
    private HomeAlbumRankFloorTypeManager mAlbumRankFloorTypeManager;

    //信息流播放状态，上次播放的数据缓存处理
    private HomeFeedTrackStatusManager mFeedTrackStatusManager;

    public HomeRecommendAdapterAddFloorManager(HomeRecommendAdapter mAdapter, HomeRecommendContact.IFragmentView fragment2) {
        this.mAdapter = mAdapter;
        mIFragment = fragment2;
        mActivity = mIFragment.getActivity();
        mTingUpdateFloor = new HomeTingUpdatePresenter(fragment2, mAdapter);
    }

    /**
     * @param mAdapter
     * @param mIFragment
     * @param from       是否来自vip界面 1：来自vip专区界面
     */
    public HomeRecommendAdapterAddFloorManager(HomeRecommendAdapter mAdapter,
                                               HomeRecommendContact.IFragmentView mIFragment, int from) {
        this.mAdapter = mAdapter;
        this.mIFragment = mIFragment;
        this.from = from;
    }

    /**
     * 将信息流增加到楼层中
     */
    public void setRecommendFeedListForView(List<RecommendItemNew> recommendFeedItemsList) {
        if (mAdapter == null) {
            return;
        }
        if (recommendFeedItemsList == null || recommendFeedItemsList.size() == 0) {
            return;
        }
        for (RecommendItemNew recommendItem : recommendFeedItemsList) {
            if (recommendItem == null) {
                continue;
            }
            if (RecommendItemNew.RECOMMEND_ITEM_TRACK.equals(recommendItem.getItemType())) {
                if (recommendItem.getItem() instanceof RecommendTrackItem) {
                    RecommendTrackItem trackItem = (RecommendTrackItem) recommendItem.getItem();
                    if (trackItem.getUiType() == RecommendTrackItem.TRACK_ITEM_UI_TYPE_STYLE_V2) {
                        mAdapter.add(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_TRACK_STYLE_V2);
                    } else {
                        mAdapter.add(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_TRACK);
                    }
                }
            } else if (RecommendItemNew.RECOMMEND_ITEM_ALBUM.equals(recommendItem.getItemType())) {
                if (recommendItem.getItem() instanceof RecommendAlbumItem) {
                    RecommendAlbumItem albumItem = (RecommendAlbumItem) recommendItem.getItem();
                    if (albumItem.getUiType() == RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V2) {
                        mAdapter.add(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V2);
                    } else if (albumItem.getUiType() == RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V3) {
                        mAdapter.add(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V3);
                    } else if (albumItem.getUiType() == RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V4) {
                        mAdapter.add(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V4);
                    } else {
                        mAdapter.add(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM);
                    }
                }
            } else if (RecommendItemNew.RECOMMEND_TYPE_LOCAL_AD_CUSTOM.equals(recommendItem.getItemType())) {
                mAdapter.add(recommendItem, HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION);
            } else if (RecommendItemNew.RECOMMEND_TYPE_LOCAL_AD_CUSTOM_STYLE3.equals(recommendItem.getItemType())) {
                mAdapter.add(recommendItem, HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3);
            } else if (RecommendItemNew.RECOMMEND_TYPE_LOCAL_AD_CUSTOM_STYLE4.equals(recommendItem.getItemType())) {
                mAdapter.add(recommendItem, HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4);
            }
        }
    }

    /**
     * @param data 主体数据
     */
    public void setDataForView(CategoryRecommendMList data) {
        if (data == null) {
            return;
        }
        setDataForView(data.getList(), true);
        //第一页没有返回信息流的条目
        if (mRecommendFeedChannelId < 0) {
            mIFragment.setHasMore(false);
        } else {
            mIFragment.setHasMore(true);
        }
    }

    /**
     * 不清除老的数据，增加数据，然后 notifyDataSetChanged
     */
    public void setDataForView(List<MainAlbumMList> dataList, boolean clearAdapter) {
        if (CollectionUtil.isNullOrEmpty(dataList)) {
            return;
        }
        mRecommendFeedChannelId = -1;
        if (clearAdapter) {
            mAdapter.clear();
        }
        for (int i = 0; i < dataList.size(); i++) {
            MainAlbumMList group = dataList.get(i);
            if (group == null) {
                continue;
            }
            String itemDirection = group.getCardClass();
            switch (group.getModuleType()) {
                case MainAlbumMList.MODULE_GUESSLIKE:
                case MainAlbumMList.MODULE_CALCDIMENSION:
                case MainAlbumMList.MODULE_KEYWORD:
                case MainAlbumMList.MODULE_METADATA:
                case MainAlbumMList.MODULE_RECOMMEND_YOU:
                case MainAlbumMList.MODULE_TAG:
                case MainAlbumMList.MODULE_VIP_ALBUM_CARD:
                case MainAlbumMList.MODULE_CONTENT_POOL:
                    if (group.getModuleType() == MainAlbumMList.MODULE_RECOMMEND_YOU) {
                        if (TextUtils.isEmpty(itemDirection)) {
                            group.setCardClass(MainAlbumMList.ITEM_DIRECTION_HORI);
                            itemDirection = group.getCardClass();
                        }
                    }
                    List<AlbumM> albumMList = group.getList();
                    boolean isNoAddAlbum = ToolUtil.isEmptyCollects(albumMList) || (MainAlbumMList.ITEM_DIRECTION_HORI.equals(itemDirection) && albumMList.size() < 3);
                    if (isNoAddAlbum) {// 水平排列时专辑数少于三则不显示
                        break;
                    }

                    NormalTitleClickListener normalTitleClickListener = new NormalTitleClickListener(group, mIFragment.getBaseFragment2());
                    //设置点击来源
                    normalTitleClickListener.setFrom(from);
                    if (!TextUtils.isEmpty(group.getTitle())) {
                        TitleModule titleModule = new TitleModule(group.getCategoryId(), group, normalTitleClickListener);
                        mAdapter.add(titleModule, HomeRecommendAdapter.VIEW_TYPE_TITLE_NORMAL);
                    }

                    if (MainAlbumMList.ITEM_DIRECTION_HORI.equals(itemDirection)) {
                        int lines = albumMList.size() / 3;
                        if (lines > 0) {
                            for (int j = 0; j < lines; j++) {
                                ItemModel itemModel = mAdapter.add(albumMList.subList(j * 3, j * 3 + 3), HomeRecommendAdapter.VIEW_TYPE_ALBUM_HORIZONT, group);
                                if (j == lines - 1) {
                                    //最后一行
                                    Bundle bundle = new Bundle();
                                    bundle.putBoolean(BundleKeyConstantsInMain.KEY_SHOW_BOTTOM_MARGIN, true);
                                    itemModel.setBundle(bundle);
                                }
                            }
                        }
                    } else if (MainAlbumMList.ITEM_DIRECTION_VERT.equals(itemDirection)) {
                        for (AlbumM albumM : albumMList) {
                            ItemModel itemModel = mAdapter.add(albumM, HomeRecommendAdapter.VIEW_TYPE_ALBUM_VERTICAL);
                            itemModel.setTag(group);
                        }
                    } else if (MainAlbumMList.ITEM_DIRECTION_SIDE_SLIP.equals(itemDirection)) {
                        HorizontalScrollAlbumModel scrollOneLineModel = new HorizontalScrollAlbumModel(group, normalTitleClickListener);
                        mAdapter.add(scrollOneLineModel, HomeRecommendAdapter.VIEW_TYPE_HORIZONTAL_SCROLL_ONE_LINE_ALBUM);

                        //额外的可视化埋点信息
                        AlbumTrackInfo trackInfo = new AlbumTrackInfo();
                        trackInfo.setTitle(group.getTitle());

                        for (AlbumM albumM : albumMList) {
                            if (albumM == null) {
                                continue;
                            }
                            albumM.setTrackInfo(trackInfo);
                        }
                    }

                    //新人必听新方案
                    if (MainAlbumMList.MODULE_CONTENT_POOL == group.getModuleType()
                            && MainAlbumMList.ITEM_DIRECTION_NEW_LISTENER.equals(itemDirection)) {
                        HorizontalScrollAlbumModel scrollAlbumModel = new HorizontalScrollAlbumModel(group, normalTitleClickListener);
                        mAdapter.add(scrollAlbumModel, HomeRecommendAdapter.VIEW_TYPE_HORIZONTAL_SCROLL_THREE_LINE_ALBUM);
                    }

                    if (group.getModuleType() == MainAlbumMList.MODULE_TAG && group.getLoopCount() > 1) {
                        mAdapter.add(createRecommendRefreshAndMore(group), HomeRecommendAdapter.VIEW_TYPE_REFRESH);
                    }
                    break;
                case MainAlbumMList.MODULE_PERSONAL_RECOMMEND:
                    //个性化推荐,猜你喜欢
                    List<AlbumM> albumMListPersonalRecommend = group.getList();
                    if (ToolUtil.isEmptyCollects(albumMListPersonalRecommend) || albumMListPersonalRecommend.size() < 3) {
                        break;
                    }
                    //第一个item，或者标题为空，不展示标题
                    if (i != 0 && !TextUtils.isEmpty(group.getTitle())) {
                        NormalTitleClickListener normalClickListener2 = new NormalTitleClickListener(group, mIFragment.getBaseFragment2());
                        normalClickListener2.setFrom(from);
                        TitleModule titleModule2 = new TitleModule(group.getCategoryId(), group, normalClickListener2);
                        mAdapter.add(titleModule2, HomeRecommendAdapter.VIEW_TYPE_TITLE_NORMAL, group);
                    }
                    if (MainAlbumMList.ITEM_DIRECTION_HORI.equals(itemDirection)) {
                        int lines = albumMListPersonalRecommend.size() / 3;
                        if (lines > 0) {
                            for (int j = 0; j < lines; j++) {
                                ItemModel itemModel = mAdapter.add(albumMListPersonalRecommend.subList(j * 3, j * 3 + 3), HomeRecommendAdapter.VIEW_TYPE_ALBUM_HORIZONT, group);
                                if (j == lines - 1) {
                                    //最后一行
                                    Bundle bundle = new Bundle();
                                    bundle.putBoolean(BundleKeyConstantsInMain.KEY_SHOW_BOTTOM_MARGIN, true);
                                    itemModel.setBundle(bundle);
                                }
                            }
                        }
                    } else {
                        for (AlbumM albumM : albumMListPersonalRecommend) {
                            ItemModel itemModel = mAdapter.add(albumM, HomeRecommendAdapter.VIEW_TYPE_ALBUM_VERTICAL);
                            itemModel.setTag(group);
                        }
                    }
                    if (group.getLoopCount() > 1) {
                        mAdapter.add(createRecommendRefreshAndMore(group), HomeRecommendAdapter.VIEW_TYPE_REFRESH);
                    }
                    break;
                case MainAlbumMList.MODULE_LITE_CATEGORY_TANGHULU:
                    List<RecommendDiscoveryM> tanghuluList = group.getTanghuluList();
                    if (tanghuluList == null || tanghuluList.size() == 0) {
                        break;
                    }
                    //首页--糖葫芦类型首页正常类型
                    if (MainAlbumMList.TANGHUYLU_DISPLAY_CLASS_HOME_NORMAL.equals(group.getDisplayClass())) {
                        mAdapter.add(tanghuluList, HomeRecommendAdapter.VIEW_TYPE_CALABASH_LINE);
                    }
                    //分类页--糖葫芦类型分类页面排行榜和一键听的糖葫芦
                    if (MainAlbumMList.TANGHUYLU_DISPLAY_CLASS_CATEGORY_RANKONEKEY.equals(group.getDisplayClass())) {
                        mAdapter.add(tanghuluList, HomeRecommendAdapter.VIEW_TYPE_CATEGORY_RANK_ONEKEY);
                    }
                    //单独的一键听楼层样式
                    if (MainAlbumMList.TANGHUYLU_DISPLAY_CLASS_ONLY_ONE_KEY.equals(group.getDisplayClass())) {
                        mAdapter.add("一键听", HomeRecommendAdapter.VIEW_TYPE_ONEKEY_LISTENER);
                    }

                    //新版听书/听节目使用的横滑糖葫芦
                    if (MainAlbumMList.TANGHUYLU_DISPLAY_CLASS_SIDE_SLIP.equals(group.getDisplayClass())) {
                        mAdapter.add(tanghuluList, HomeRecommendAdapter.VIEW_TYPE_TANGHULU_HOME_V2);
                    }
                    break;
                case MainAlbumMList.MODULE_FEED_STREAM:
                case MainAlbumMList.MODULE_FEED_STREAM_V2:
                    //无线信息流需要使用的id
                    if (group.getModuleType() == MainAlbumMList.MODULE_FEED_STREAM) {
                        //分类页和首页--设置信息流的channelId
                        mRecommendFeedChannelId = group.getChannelId();
                    }
                    //有限信息流需要使用的请求参数
                    if (group.getModuleType() == MainAlbumMList.MODULE_FEED_STREAM_V2) {
                        mFeedStreamOtherData = group.getFeedStreamOtherData();
                    }

                    //无数据，不添加楼层，必须放在mRecommendFeedChannelId和mFeedStreamOtherData赋值之后
                    List<RecommendItemNew> feedStreamItemList = group.feedStreamItemList;
                    if (feedStreamItemList == null || feedStreamItemList.size() == 0) {
                        break;
                    }
                    //展示了全部播放按钮，需要设置监听，继续播放处理
                    if (group.isPlayAllBtn()) {
                        if (mFeedTrackStatusManager == null) {
                            mFeedTrackStatusManager = new HomeFeedTrackStatusManager(mAdapter);
                        }
                        mFeedTrackStatusManager.register();
                    }
                    //每次下拉刷新后，清除下数据
                    if (clearAdapter && mFeedTrackStatusManager != null) {
                        mFeedTrackStatusManager.reset();
                    }
                    if (!TextUtils.isEmpty(group.getTitle())) {
                        TitleModule recommendFeedTitleModule = new TitleModule(group.getCategoryId(), group, null);
                        recommendFeedTitleModule.setShowBottomDivider(true);
                        mAdapter.add(recommendFeedTitleModule, HomeRecommendAdapter.VIEW_TYPE_TITLE_NORMAL, FEED_STREAM);
                    }
                    setRecommendFeedListForView(feedStreamItemList);
                    break;
                case MainAlbumMList.MODULE_ONEKEY_NEW:
                    boolean show = ConfigureCenter.getInstance().getBool("ximalaya_lite", "speed_fm_open", false);
                    if (show) {
                       /* if (group.getStatus() == 0
                                && !SharedPreferencesUtil.getInstance(mActivity).getBoolean(KEY_ONEKEY_CATEGORY_SETTING_WRITED)) {
                            mAdapter.add("一键听兴趣设置入口", HomeRecommendAdapter.VIEW_TYPE_ONE_KEY_RADIO_SETTING);
                        } else {
                            mAdapter.add(group.getOneKeyRadioList(), HomeRecommendAdapter.VIEW_TYPE_ONE_KEY_RADIO, group);
                        }*/
                        mAdapter.add(group.getOneKeyRadioList(), HomeRecommendAdapter.VIEW_TYPE_ONE_KEY_RADIO, group);
                    }
                    break;
                case MainAlbumMList.MODULE_VIP_TOP_REGION:
                    List<VipInfoModel> vipInfoModelList = group.vipInfoModelList;
                    if (CollectionUtil.isNotEmpty(vipInfoModelList)) {
                        mAdapter.add(vipInfoModelList.get(0), HomeRecommendAdapter.VIEW_TYPE_VIP_TOP_REGION);
                    }
                    break;
                case MainAlbumMList.MODULE_VIP_BAR:
                    List<VipInfoModel> vipBarInfoModelList = group.vipBarInfoModelList;
                    if (CollectionUtil.isNotEmpty(vipBarInfoModelList)) {
                        mAdapter.add(vipBarInfoModelList.get(0), HomeRecommendAdapter.VIEW_TYPE_VIP_BAR);
                    }
                    break;
                case MainAlbumMList.MODULE_VIP_FOCUS_IMAGE:
                    List<BannerModel> bannerModelList = group.bannerModelList;
                    if (CollectionUtil.isNotEmpty(bannerModelList)) {
                        mAdapter.add(bannerModelList, HomeRecommendAdapter.VIEW_TYPE_VIP_FOCUS_IMAGE);
                    }
                    break;
                case MainAlbumMList.MODULE_TANGHULU_HOT_WORD:
                    if (!TextUtils.isEmpty(group.getTitle())) {
                        TitleModule titleModule = new TitleModule(group.getCategoryId(), group, null);
                        mAdapter.add(titleModule, HomeRecommendAdapter.VIEW_TYPE_TITLE_NORMAL);
                    }
                    List<TanghuluHotWord> tanghuluHotWordList = group.tanghuluHotWordList;
                    if (MainAlbumMList.TANGHUYLU_HOT_WORD_FLOAT.equals(group.getCardClass())) {
                        if (CollectionUtil.isNotEmpty(tanghuluHotWordList)) {
                            mAdapter.add(tanghuluHotWordList, HomeRecommendAdapter.VIEW_TYPE_TANGHULU_FLOAT_HOT_WORD);
                        }
                    } else if (MainAlbumMList.TANGHUYLU_HOT_WORD_NORMAL.equals(group.getCardClass())) {
                        if (CollectionUtil.isNotEmpty(tanghuluHotWordList)) {
                            mAdapter.add(tanghuluHotWordList, HomeRecommendAdapter.VIEW_TYPE_TANGHULU_NORMAL_HOT_WORD);
                        }
                    }
                    break;
                case MainAlbumMList.MODULE_CATEGORY_RANK:
                    List<AlbumM> rankList = group.getList();
                    AlbumTrackInfo trackInfo = new AlbumTrackInfo();
                    trackInfo.setTitle(group.getTitle());
                    trackInfo.setRankClusterId(group.getRankClusterId());
                    //最多取9个
                    List<AlbumM> max9List = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(rankList)) {
                        if (rankList.size() > 9) {
                            for (int j = 0; j < 9; j++) {
                                max9List.add(rankList.get(j));
                            }
                            group.setList(max9List);
                        }
                    }
                    //分类排行榜模块，产品要的额外的埋点信息
                    if (CollectionUtil.isNotEmpty(rankList)) {
                        for (AlbumM albumM : rankList) {
                            if (albumM == null) {
                                continue;
                            }
                            albumM.setTrackInfo(trackInfo);
                        }
                    }

                    NormalTitleClickListener titleClickListener = new NormalTitleClickListener(
                            group, mIFragment.getBaseFragment2());
                    titleClickListener.setFrom(from);
                    if (!TextUtils.isEmpty(group.getTitle())) {
                        TitleModule titleModule = new TitleModule(group.getCategoryId(), group, titleClickListener);
                        mAdapter.add(titleModule, HomeRecommendAdapter.VIEW_TYPE_TITLE_NORMAL);
                    }
                    HorizontalScrollAlbumModel scrollAlbumModel = new HorizontalScrollAlbumModel(group, titleClickListener);
                    mAdapter.add(scrollAlbumModel, HomeRecommendAdapter.VIEW_TYPE_HORIZONTAL_SCROLL_THREE_LINE_ALBUM);
                    break;
                case MainAlbumMList.MODULE_NET_OPERATE:
                    mAdapter.add(group, HomeRecommendAdapter.VIEW_TYPE_NET_OPERATE);
                    break;
                case MainAlbumMList.MODULE_HOT_SEARCH_RANK:
                    mAdapter.add(group, HomeRecommendAdapter.VIEW_TYPE_HOT_SEARCH_RANK);
                    break;
                case MainAlbumMList.MODULE_INTEREST_RECOMMEND:
                    if (group.getList() == null || group.getList().size() < 3) {
                        break;
                    }
                    NormalTitleClickListener titleClickListener1 = new NormalTitleClickListener(group, mIFragment.getBaseFragment2());
                    //设置点击来源
                    titleClickListener1.setFrom(from);
                    TitleModule titleModule = new TitleModule(group.getCategoryId(), group, titleClickListener1);
                    mAdapter.add(titleModule, HomeRecommendAdapter.VIEW_TYPE_TITLE_NORMAL_V2);
                    HorizontalScrollAlbumModel scrollOneLineModel = new HorizontalScrollAlbumModel(group, titleClickListener1);
                    mAdapter.add(scrollOneLineModel, HomeRecommendAdapter.VIEW_TYPE_INTEREST_RECOMMEND_REGION);
                    break;
                case MainAlbumMList.MODULE_REPLENISH_SEARCH:
                    mAdapter.add(group, HomeRecommendAdapter.VIEW_TYPE_REPLENISH_SEARCH_REGION);
                    break;
                case MainAlbumMList.MODULE_FLEX_BOX:
                    mAdapter.add(group, HomeRecommendAdapter.VIEW_TYPE_FLEX_BOX);
                    break;
                case MainAlbumMList.MODULE_TING_UPDATE:
                    //听更新楼层
                    if (!UserInfoMannage.hasLogined()) {
                        break;
                    }
                    HomeTingUpdateModel homeTingUpdateModel = group.getHomeTingUpdateModel();
                    if (homeTingUpdateModel != null && mTingUpdateFloor != null) {
                        List<EverydayUpdateTrack> everydayUpdateTrackList = mTingUpdateFloor.getEverydayUpdateTrackList();
                        if (homeTingUpdateModel.allTracks == null) {
                            homeTingUpdateModel.allTracks = new ArrayList<>();
                        }
                        if (everydayUpdateTrackList != null && everydayUpdateTrackList.size() > 0) {
                            homeTingUpdateModel.allTracks.clear();
                            homeTingUpdateModel.allTracks.addAll(everydayUpdateTrackList);
                        }
                        mAdapter.add(group, HomeRecommendAdapter.VIEW_TYPE_TING_UPDATE);
                        if (clearAdapter && mTingUpdateFloor != null) {
                            mTingUpdateFloor.requestEveryDayUpdateTrack();
                        }
                    }
                    break;
                case MainAlbumMList.MODULE_SUBSCRIBE_HISTORY:
                    if (mSubscribeFloorPresenter == null) {
                        mSubscribeFloorPresenter = new HomeSubscribeFloorPresenter(mIFragment, mAdapter);
                    }
                    HomeSubscribeHistoryViewModel homeSubscribeHistoryViewModel = new HomeSubscribeHistoryViewModel();
                    homeSubscribeHistoryViewModel.fillViewModel(mSubscribeFloorPresenter.getCacheSubscribeHistoryViewModel());
                    if (homeSubscribeHistoryViewModel.subscribePageViewModel == null) {
                        homeSubscribeHistoryViewModel.subscribePageViewModel = new HomeSubscribePageViewModel();
                    }
                    //需要后设置设置全部订阅数，服务端参数覆盖读取的缓存值
                    homeSubscribeHistoryViewModel.subscribePageViewModel.updateSubscribeCount(group.totalCount);
                    //加载上次可能存在的缓存
                    mAdapter.add(homeSubscribeHistoryViewModel, HomeRecommendAdapter.VIEW_TYPE_MY_SUBSCRIPTION);
                    if (clearAdapter) {
                        //更新一次数据
                        mSubscribeFloorPresenter.requestDataAll(group.totalCount);
                    }
                    break;
                case MainAlbumMList.MODULE_ALBUM_RANK:
                    //专辑排行榜
                    List<HomeAlbumRankItem> homeAlbumRankItemList = group.getHomeAlbumRankItemList();
                    if (homeAlbumRankItemList != null && homeAlbumRankItemList.size() > 0) {
                        HomeAlbumRankFloorViewModel floorViewModel = new HomeAlbumRankFloorViewModel();

                        floorViewModel.title = group.getTitle();
                        floorViewModel.rankNeedRequestNumber = group.getDisplayCount();
                        if (group.getMainAlbumOtherData() != null) {
                            floorViewModel.moreClickUrl = group.getMainAlbumOtherData().hasMoreLink;
                        }
                        floorViewModel.homeAlbumRankItemList = new ArrayList<>();
                        floorViewModel.homeAlbumRankItemList.addAll(homeAlbumRankItemList);

                        if (mAlbumRankFloorTypeManager == null) {
                            mAlbumRankFloorTypeManager = new HomeAlbumRankFloorTypeManager();
                        }
                        int canUseAlbumRankFloorType = mAlbumRankFloorTypeManager.getCanUseAlbumRankFloorType(floorViewModel);
                        if (canUseAlbumRankFloorType > 0) {
                            mAdapter.add(floorViewModel, canUseAlbumRankFloorType);
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        mAdapter.notifyDataSetChanged();
    }

    public void setDataForViewWithRealTimeRecommendFeedItem(List<ItemModel> data) {
        if (mAdapter == null || data == null) {
            return;
        }
        mAdapter.clear();
        for (ItemModel itemModel : data) {
            mAdapter.add(itemModel);
        }
        mAdapter.notifyDataSetChanged();
    }

    /**
     * 将vip热词分类下的数据加入到适配器中
     *
     * @param albumList vip热词分类下的数据
     */
    public void setAlbumLists(List<AlbumM> albumList) {
        if (mAdapter == null || CollectionUtil.isNullOrEmpty(albumList)) {
            return;
        }
        for (AlbumM album : albumList) {
            if (album == null) {
                continue;
            }
            mAdapter.add(album, HomeRecommendAdapter.VIEW_TYPE_VIP_HOT_WORD_ALBUM);
        }
    }

    /**
     * 将vip热词分类下的数据加入到适配器中
     */
    public void setFeedLists(List<NewUserMustListenerModel> modelList) {
        if (mAdapter == null || CollectionUtil.isNullOrEmpty(modelList)) {
            return;
        }
        for (NewUserMustListenerModel model : modelList) {
            if (model == null) {
                continue;
            }
            mAdapter.add(model, HomeRecommendAdapter.VIEW_TYPE_TANGHULU_FEED_ALBUM);
        }
    }

    /**
     * 添加没有数据的适配器
     */
    public void setNoContentModel(ListViewNoContentModel model) {
        if (mAdapter == null) {
            return;
        }
        mAdapter.add(model, HomeRecommendAdapter.VIEW_TYPE_LIST_VIEW_NO_CONTENT);
    }

    @NonNull
    private CategoryRecommendRefresh createRecommendRefreshAndMore(MainAlbumMList albumMList) {
        return new CategoryRecommendRefresh(mActivity, albumMList);
    }

    public void notifyDataSetChanged() {
        mAdapter.notifyDataSetChanged();
    }

    public long getRecommendFeedChannelId() {
        return mRecommendFeedChannelId;
    }

    //有限信息流使用的参数
    public FeedStreamOtherData getFeedStreamOtherData() {
        return mFeedStreamOtherData;
    }

    //有限流参数每次使用过后，都需要清除掉，每次请求只能给下次请求使用
    public void resetFeedStreamOtherData() {
        if (mFeedStreamOtherData != null) {
            mFeedStreamOtherData.newestTimeline = 0;
            mFeedStreamOtherData.oldestTimeline = 0;
        }
        mFeedStreamOtherData = null;
    }

    /**
     * 生命周期
     */
    public void onDestroy() {
        if (mSubscribeFloorPresenter != null) {
            mSubscribeFloorPresenter.onDestroy();
        }
        if (mFeedTrackStatusManager != null) {
            mFeedTrackStatusManager.onDestroy();
        }
    }

    @Nullable
    public HomeSubscribeFloorPresenter getSubscribeFloorPresenter() {
        return mSubscribeFloorPresenter;
    }

    public HomeFeedTrackStatusManager getFeedTrackStatusManager() {
        return mFeedTrackStatusManager;
    }
}
