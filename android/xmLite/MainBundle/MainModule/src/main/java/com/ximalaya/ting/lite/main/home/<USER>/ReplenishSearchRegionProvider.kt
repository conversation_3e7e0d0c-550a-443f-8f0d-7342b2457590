package com.ximalaya.ting.lite.main.home.adapter

import androidx.core.content.ContextCompat
import android.text.TextUtils
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter
import com.ximalaya.ting.android.host.manager.customize.CustomizeManager
import com.ximalaya.ting.android.host.util.ui.AnimationUtil
import com.ximalaya.ting.android.host.view.layout.FlowLayout
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.home.fragment.HomeCategoryContentTabFragment
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList
import com.ximalaya.ting.lite.main.model.album.ReplenishSearchModel
import com.ximalaya.ting.lite.main.request.LiteCommonRequest
import kotlinx.android.synthetic.main.main_item_replenish_search_region.view.*

/**
 * Created by dumingwei on 2020/7/1
 *
 * Desc: 补充搜索区
 */
class ReplenishSearchRegionProvider @JvmOverloads constructor(
        val mFragment: BaseFragment2,
        private val mExtraModel: HomeRecommendExtraViewModel? = null,
        private val iRefreshContext: HomeRecommendRefreshProvider.IRefreshContext? = null
) : IMulitViewTypeViewAndData<ReplenishSearchRegionProvider.Holder, MainAlbumMList> {


    private val TAG: String = "ReplenishSearchRegionPr"

    private var mModel: MainAlbumMList? = null

    private val textViewHorizontalPadding: Int = BaseUtil.dp2px(mFragment.context, 12f)
    private val textViewVerticalPadding: Int = BaseUtil.dp2px(mFragment.context, 8f)

    private val textViewRightMargin: Int = BaseUtil.dp2px(mFragment.context, 12f)
    private val textViewBottomMargin: Int = BaseUtil.dp2px(mFragment.context, 16f)

    private val textViewColor: Int = mFragment.context?.let { ContextCompat.getColor(it, R.color.main_color_ae8559) }
            ?: 0

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_replenish_search_region, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView)
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<MainAlbumMList>, convertView: View, position: Int) {
        val model = t.getObject()
        mModel = model
        if (model is MainAlbumMList) {
            with(holder.rootView) {
                //mainTvTitle.text = model.title
                setWords(model, mainTagContainer)
                mainLlRefresh.setOnClickListener {
                    refreshWords(mainIvRefresh)
                }
            }
        }
    }

    private fun setWords(model: MainAlbumMList, mainTagContainer: FlowLayout) {
        val searchModelList = model.replenishSearchList
        mainTagContainer.removeAllViews()

        AutoTraceHelper.setLabelForCTWithMultiSameSubView(mainTagContainer);

        searchModelList?.forEach { searchModel ->
            val tv = TextView(mainTagContainer.context)
            tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
            tv.text = searchModel.word
            tv.gravity = Gravity.CENTER
            tv.setPadding(textViewHorizontalPadding, textViewVerticalPadding, textViewHorizontalPadding, textViewVerticalPadding)
            tv.maxLines = 1
            tv.ellipsize = TextUtils.TruncateAt.END
            tv.setTextColor(textViewColor)
            tv.setBackgroundResource(R.drawable.main_bg_rect_f6f7f8_corner_18)
            tv.setOnClickListener {
                handleItemClick(searchModel)
            }
            //绑定整个列表
            AutoTraceHelper.bindData(tv, searchModelList)

            val lp = FlowLayout.LayoutParams(
                    FlowLayout.LayoutParams.WRAP_CONTENT,
                    FlowLayout.LayoutParams.WRAP_CONTENT)
            lp.setMargins(0, 0, textViewRightMargin, textViewBottomMargin)
            mainTagContainer.addView(tv, lp)
        }
    }

    private fun refreshWords(view: View) {
        AnimationUtil.rotateView(mFragment.activity, view)
        val params = hashMapOf<String, String>()

        mModel?.moduleId?.let {
            params.put("moduleId", it.toString())
        }
        //本地记录了兴趣卡片
        val interestCardModel = CustomizeManager.getInstance().interestCardModel
        if (interestCardModel != null) {
            params["ageRange"] = interestCardModel.ageRange
            params["gender"] = interestCardModel.gender.toString() + ""
        }
        LiteCommonRequest.refreshReplenishSearchWord(params, object : IDataCallBack<List<ReplenishSearchModel>> {
            override fun onSuccess(obj: List<ReplenishSearchModel>?) {
                AnimationUtil.stopAnimation(view)
                Logger.d(TAG, "onSuccess obj = $obj")
                obj?.let {
                    mModel?.replenishSearchList = it
                    iRefreshContext?.onRefreshSuccess()
                }
            }

            override fun onError(code: Int, message: String?) {
                AnimationUtil.stopAnimation(view)
                Logger.d(TAG, "code = $code , message = $message")
            }
        })
    }

    private fun handleItemClick(model: ReplenishSearchModel) {
        if (ReplenishSearchModel.TYPE_SEARCH == model.type) {
            model.word?.let { searchWord ->
                val fragmentAction = SearchActionRouter.getInstance().fragmentAction
                fragmentAction?.let {
                    val fragment = fragmentAction.newSearchFragmentByWordAndSearchNow(searchWord)
                    mFragment.startFragment(fragment)
                }
            }
        } else if (ReplenishSearchModel.TYPE_CATEGORY == model.type) {
            /*model.categoryId?.let {
                //打开分类--相关推荐页面
                val categoryFragment = HomeCategoryRecommendFragment.newInstance(it, model.word)
                mFragment.startFragment(categoryFragment)
            }*/
            handleCategoryContentTabPage(model)
        }
    }

    /**
     * 打开具体分类--tab页面，默认选中的是全部
     */
    private fun handleCategoryContentTabPage(model: ReplenishSearchModel) {
        //跳转到分类页面
        val tabFragment = HomeCategoryContentTabFragment()
        val title = model.word
        val argumentDef = HomeCategoryContentTabFragment.createArgumentSelectByKeyWordName(
                model.categoryId ?: -1, title)
        tabFragment.arguments = argumentDef
        mFragment.startFragment(tabFragment)
    }

    class Holder(var rootView: View) : BaseViewHolder()

}