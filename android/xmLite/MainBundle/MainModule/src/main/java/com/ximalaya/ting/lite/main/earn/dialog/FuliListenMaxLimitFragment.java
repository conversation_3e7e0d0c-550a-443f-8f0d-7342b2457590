package com.ximalaya.ting.lite.main.earn.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.listenertask.ListenEarnCoinDialogManager;
import com.ximalaya.ting.android.host.listenertask.callback.JssdkFuliRewardCallback;
import com.ximalaya.ting.android.host.model.earn.FuliBallDialogDataModel;
import com.ximalaya.ting.android.host.model.earn.FuliBallType;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 已登录领取金币的弹框
 *
 * <AUTHOR>
 */
public class FuliListenMaxLimitFragment extends BaseDialogFragment {

    private static final String ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL = "listen_earn_dialog_data_model";
    private boolean mMaskIsShow = false; //解决fragment重复添加crash问题

    //奖励数据载体
    private FuliBallDialogDataModel mDataModel;

    private JssdkFuliRewardCallback mJssdkFuliRewardCallback;

    public static Bundle newArgument(FuliBallDialogDataModel dialogDataModel) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL, dialogDataModel);
        return bundle;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mDataModel = arguments.getParcelable(ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL);
        }
        if (mDataModel == null) {
            mDataModel = new FuliBallDialogDataModel(FuliBallType.BALL_TYPE_DEF, FuliBallType.AWARD_TYPE_GET);
        }
        View inflate = inflater.inflate(R.layout.main_fra_dialog_fuli_listen_max_limit, container, false);
        View viewClose = inflate.findViewById(R.id.main_iv_close);

        TextView tvEarnMore = inflate.findViewById(R.id.main_tv_coin_earn_more);
        TextView tvMyBalanceExchange = inflate.findViewById(R.id.main_tv_my_balance_exchange);
        TextView tvCancelExchange = inflate.findViewById(R.id.main_cancel_exchange);
        TextView tvListenTime = inflate.findViewById(R.id.main_tv_earn_listen_time);

        tvListenTime.setText(mDataModel.awardDesc);
        viewClose.setAlpha(0.4f);
        String totalCoinBalance = mDataModel.myCoinBalance + "";


        String balanceTransform = StringUtil.balanceTransform(totalCoinBalance);

        BigDecimal decimal = new BigDecimal(totalCoinBalance);
        BigDecimal divide = decimal.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
        String myCoinExchangeInfo = balanceTransform + "≈" + divide.toString() + "元";

        SpannableString spannableExchangeInfo = new SpannableString(myCoinExchangeInfo);
        spannableExchangeInfo.setSpan(new ForegroundColorSpan(Color.parseColor("#66ffffff")), 0, balanceTransform.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvMyBalanceExchange.setText(spannableExchangeInfo);

        viewClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismissAllowingStateLoss();
                new XMTraceApi.Trace()
                        .setMetaId(10043)
                        .setServiceId("dialogClick")
                        .put("item", "关闭")
                        .put("dialogType", "listenOverruned")
                        .createTrace();
            }
        });
        tvCancelExchange.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismissAllowingStateLoss();
                new XMTraceApi.Trace()
                        .setMetaId(10042)
                        .setServiceId("dialogClick")
                        .put("item", "放弃兑换")
                        .put("dialogType", "listenOverruned")
                        .createTrace();
            }
        });
        tvEarnMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!OneClickHelper.getInstance().onClick(view)) {
                    return;
                }
                //只有幸运气泡可以翻倍操作，加载幸运气泡的广告
                ListenEarnCoinDialogManager.getInstance().loadVideoAdForListenMaxLimitReward(mDataModel.fulliCoinRewardReqModel, mJssdkFuliRewardCallback);
                dismissAllowingStateLoss();

                new XMTraceApi.Trace()
                        .setMetaId(10041)
                        .setServiceId("dialogClick")
                        .put("item", "看视频继续兑换")
                        .put("dialogType", "listenOverruned")
                        .createTrace();
            }
        });
        AutoTraceHelper.bindData(tvCancelExchange, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(viewClose, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(tvEarnMore, AutoTraceHelper.MODULE_DEFAULT, "");

        new XMTraceApi.Trace()
                .setMetaId(10040)
                .setServiceId("dialogView")
                .put("dialogType", "listenOverruned")
                .createTrace();

        return inflate;
    }


    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        //去掉标题
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setCanceledOnTouchOutside(false);
        Window window = dialog.getWindow();
        if (window != null) {
            //dialog背景设置透明，解决shape不生效问题
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setPadding(0, 0, 0, 0); //消除边距
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;   //设置宽度充满屏幕
            lp.height = WindowManager.LayoutParams.MATCH_PARENT;
            window.setAttributes(lp);
        }
        return dialog;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mMaskIsShow = false;
    }


    public void setJssdkFuliRewardCallback(JssdkFuliRewardCallback jssdkFuliRewardCallback) {
        this.mJssdkFuliRewardCallback = jssdkFuliRewardCallback;
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        if (mMaskIsShow) {
            return;
        }
        mMaskIsShow = true;
        super.show(manager, tag);
    }

}