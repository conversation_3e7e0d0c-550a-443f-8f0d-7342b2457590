package com.ximalaya.ting.lite.main.base.album

import android.content.Context
import android.text.*
import android.text.style.RelativeSizeSpan
import android.view.View
import android.widget.TextView
import androidx.annotation.DrawableRes
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.framework.view.image.FlexibleRoundImageView
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.AlbumTagUtil
import com.ximalaya.ting.android.host.view.SpaceSpan
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.model.album.Album
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.lite.main.view.VerticalAlignTextSpan

/**
 * Created by du<PERSON><PERSON> on 2021/1/8
 *
 *
 * Desc:专辑评分
 *
 */
class AlbumScoreAdapter(
        val category: Int,//category等于3，表示是有声书样式，
        context: Context,
        listData: List<Album>?
) : BaseAlbumAdapter(context, listData) {

    companion object {

        //表示是有声书样式
        const val CATEGORY_TYPE = 3
    }

    private val albumScoreSpanWidth = BaseUtil.dp2px(context, 1f)
    private val dp24: Int = BaseUtil.dp2px(context, 24f)

    override fun getConvertViewId(): Int {
        return R.layout.main_item_album_score
    }

    override fun bindViewDatas(holder: BaseViewHolder, t: Album, position: Int) {
        super.bindViewDatas(holder, t, position)
        val viewHolder = holder as ViewHolder
        hideAllViews(viewHolder)
        if (t !is AlbumM) {
            return
        }
        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, t)
        //添加专辑Item的ContentDescription
        if (viewHolder.root != null) {
            if (!TextUtils.isEmpty(t.albumTitle)) {
                viewHolder.root.contentDescription = t.albumTitle
            } else {
                viewHolder.root.contentDescription = ""
            }
        }
        ImageManager.from(context).displayImage(viewHolder.cover,
                t.largeCover, R.drawable.host_default_album_145, R.drawable.host_default_album_145)

        @DrawableRes val coverTagResId: Int = AlbumTagUtil.getAlbumCoverTag(t)
        if (coverTagResId != -1) {
            holder.ivCoverTag.setImageDrawable(AlbumTagUtil.getAlbumCoverTagDrawable(t,
                    context, 0.7f))
            holder.ivCoverTag.visibility = View.VISIBLE
        } else {
            holder.ivCoverTag.visibility = View.INVISIBLE
        }

        val textSize = viewHolder.title.textSize.toInt()
        val titleWithTag = AlbumAdapter.getRichTitle(t, context, textSize)
        viewHolder.title.text = titleWithTag

        val subTitle = t.albumIntro
        if (!TextUtils.isEmpty(subTitle)) {
            viewHolder.subtitle.text = subTitle
        } else {
            viewHolder.subtitle.text = ""
        }

        val playCountStr = StringUtil.getFriendlyNumStr(t.getPlayCount()) + "播放"
        viewHolder.tvPlayCount.text = playCountStr

        if (category == CATEGORY_TYPE) {//有声书类型

            holder.tvTrackCount.text = ""
            holder.tvTrackCount.visibility = View.INVISIBLE

            viewHolder.tvAlbumScore.visibility = View.VISIBLE

            //专辑评分
            val score = t.score
            if (score > 0.5) {
                val albumScore = context.getString(R.string.main_album_score_span_format, score)
                viewHolder.tvAlbumScore.text = getScoreSpan(albumScore)
                viewHolder.tvAlbumScore.visibility = View.VISIBLE
            } else {
                viewHolder.tvAlbumScore.text = ""
                viewHolder.tvAlbumScore.visibility = View.INVISIBLE
            }
            viewHolder.tvAlbumTags.text = getAlbumTags(t)

        } else {
            holder.tvTrackCount.visibility = View.VISIBLE
            holder.tvTrackCount.text = StringUtil.getFriendlyNumStr(t.getIncludeTrackCount()) + " 集"

            viewHolder.tvAlbumScore.text = ""
            viewHolder.tvAlbumScore.visibility = View.GONE

            viewHolder.tvAlbumTags.text = ""
        }

    }

    private fun getScoreSpan(result: String): SpannableStringBuilder? {
        //注意，这个i是需要被空白替换掉的，模拟1dp的宽度
        //val suffix = "i分"
        //val result = albumScore + suffix
        val builder = SpannableStringBuilder(result)
        val spaceSpan = SpaceSpan(albumScoreSpanWidth)
        val sizeSpan = RelativeSizeSpan(0.68f)
        builder.setSpan(spaceSpan, result.length - 2, result.length - 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        builder.setSpan(sizeSpan, result.length - 1, result.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        return builder
    }

    private fun getAlbumTags(albumM: AlbumM): CharSequence? {
        val list = albumM.tagResults
        val stringBuilder = SpannableStringBuilder()
        list?.forEachIndexed { index, tagResult ->
            if (tagResult != null && !tagResult.tagName.isNullOrEmpty()) {
                if (index > 0) {
                    val spannableString = SpannableString(" · ")
                    val verticalAlignTextSpan = VerticalAlignTextSpan(dp24)
                    spannableString.setSpan(verticalAlignTextSpan, 1, 2, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
                    stringBuilder.append(spannableString)
                }
                stringBuilder.append(tagResult.tagName)
            }
        }
        return stringBuilder
    }

    override fun buildHolder(convertView: View): BaseViewHolder {
        return ViewHolder(convertView)
    }

    override fun onClick(view: View, album: Album, position: Int, holder: BaseViewHolder) {
        //do nothing
    }

    class ViewHolder(convertView: View) : BaseAlbumAdapter.ViewHolder(convertView) {
        val tvAlbumScore: TextView
        val tvAlbumTags: TextView
        val tvPlayCount: TextView
        val tvTrackCount: TextView
        val ivCoverTag: FlexibleRoundImageView

        init {
            cover = convertView.findViewById(R.id.main_iv_album_cover)
            border = convertView.findViewById(R.id.main_album_border)
            title = convertView.findViewById(R.id.main_tv_album_title)
            subtitle = convertView.findViewById(R.id.main_tv_sub_title)
            tvAlbumScore = convertView.findViewById(R.id.main_tv_album_score)
            tvAlbumTags = convertView.findViewById(R.id.main_tv_tags)
            tvPlayCount = convertView.findViewById(R.id.main_tv_play_count)
            tvTrackCount = convertView.findViewById(R.id.main_tv_track_count)
            ivCoverTag = convertView.findViewById(R.id.main_iv_album_cover_tag)
        }
    }

}