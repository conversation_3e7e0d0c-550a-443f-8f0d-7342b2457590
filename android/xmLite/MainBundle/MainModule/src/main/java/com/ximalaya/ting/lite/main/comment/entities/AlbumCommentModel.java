package com.ximalaya.ting.lite.main.comment.entities;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> feiwen
 * date   : 2019-08-23
 * desc   :
 */
public class AlbumCommentModel {
    // 审核中
    public static final int STATUS_VERIFYING = 1;
    // 审核失败
    public static final int STATUS_VERIFY_FAIL = 3;

    public static final long ITEM_MORE = -99;

    @SerializedName(value = "newAlbumScore", alternate = {"new_album_score"})
    private double score = -1;
    @SerializedName(value = "albumId", alternate = {"album_id"})
    private long albumId;
    @SerializedName("albumCoverPath")
    private String albumCoverPath;
    @SerializedName("albumUid")
    private long albumUid;
    @SerializedName("auditStatus")
    private int auditStatus;
    @SerializedName("commentId")
    private long commentId;
    @SerializedName("content")
    private String content;
    @SerializedName(value = "createdAt", alternate = {"created_at"})
    private long createdAt;
    @SerializedName("liked")
    private boolean liked;
    @SerializedName("likes")
    private long likes;
    @SerializedName("nickname")
    private String nickname;
    @SerializedName("replyCount")
    private long replyCount;
    @SerializedName("smallHeader")
    private String smallHeader;
    @SerializedName("uid")
    private long uid;
    @SerializedName("updatedAt")
    private long updatedAt;
    @SerializedName("vLogoType")
    private int vLogoType;//-1无微标，1橙v，2蓝v，3红金v，4蓝金v
    @SerializedName("talentColor")
    private String talentColor;
    @SerializedName("talentContent")
    private String talentContent;
    @SerializedName("talentIcon")
    private String talentIcon;
    @SerializedName("talentIconWidth")
    private int talentIconWidth;
    @SerializedName("talentIconHeight")
    private int talentIconHeight;
    @SerializedName("commentUid")
    private long commentUid;
    @SerializedName("replyId")
    private long replyId;
    @SerializedName("albumTitle")
    private String albumTitle;
    @SerializedName("playProgress")
    private int playProgress;
    @SerializedName("isHighQuality")
    private boolean isHighQuality;
    private boolean hasReplied;
    // 专辑评论回复的根评论
    @SerializedName(value = "album_comment_id")
    private long parentAlbumId;
    private String albumUidNickName;
    private boolean isLookAlled;
    @SerializedName("vipIconUrl")
    private String vipIconUrl;
    @SerializedName("vipJumpUrl")
    private String vipJumpUrl;
    @SerializedName("ximiIconUrl")
    private String ximiIconUrl;
    @SerializedName("ximiJumpUrl")
    private String ximiJumpUrl;
    @SerializedName("albumPaid")
    private boolean albumPaid;
    @SerializedName("replies")
    public List<AlbumCommentModel> replies;
    public List<UserLevelTag> userTags;
    @SerializedName("parentReplyId")
    public long parentReplyId;
    @SerializedName("parentReplyNickName")
    public String parentReplyNickName;
    @SerializedName("parentReplyUid")
    public long parentReplyUid;
    @SerializedName("region")
    public String region;

    public boolean getAlbumPaid() {
        return albumPaid;
    }

    public String getVipIconUrl() {
        return vipIconUrl;
    }

    public void setVipIconUrl(String vipIconUrl) {
        this.vipIconUrl = vipIconUrl;
    }

    public String getVipJumpUrl() {
        return vipJumpUrl;
    }

    public void setVipJumpUrl(String vipJumpUrl) {
        this.vipJumpUrl = vipJumpUrl;
    }

    public String getXimiIconUrl() {
        return ximiIconUrl;
    }

    public void setXimiIconUrl(String ximiIconUrl) {
        this.ximiIconUrl = ximiIconUrl;
    }

    public String getXimiJumpUrl() {
        return ximiJumpUrl;
    }

    public void setXimiJumpUrl(String ximiJumpUrl) {
        this.ximiJumpUrl = ximiJumpUrl;
    }

    public boolean showTalentLogo() {
        return !TextUtils.isEmpty(talentIcon);
    }

    public String getTalentColor() {
        return talentColor;
    }

    public void setTalentColor(String talentColor) {
        this.talentColor = talentColor;
    }

    public String getTalentContent() {
        return talentContent;
    }

    public void setTalentContent(String talentContent) {
        this.talentContent = talentContent;
    }

    public String getTalentIcon() {
        return talentIcon;
    }

    public void setTalentIcon(String talentIcon) {
        this.talentIcon = talentIcon;
    }

    public int getTalentIconWidth() {
        return talentIconWidth;
    }

    public void setTalentIconWidth(int talentIconWidth) {
        this.talentIconWidth = talentIconWidth;
    }

    public int getTalentIconHeight() {
        return talentIconHeight;
    }

    public void setTalentIconHeight(int talentIconHeight) {
        this.talentIconHeight = talentIconHeight;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean isHasReplied() {
        return hasReplied;
    }

    public void setHasReplied(boolean hasReplied) {
        this.hasReplied = hasReplied;
    }

    public long getParentAlbumId() {
        return parentAlbumId;
    }

    public void setParentAlbumId(long parentAlbumId) {
        this.parentAlbumId = parentAlbumId;
    }

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public int getvLogoType() {
        return vLogoType;
    }

    public void setvLogoType(int vLogoType) {
        this.vLogoType = vLogoType;
    }

    public String getAlbumCoverPath() {
        return albumCoverPath;
    }

    public void setAlbumCoverPath(String albumCoverPath) {
        this.albumCoverPath = albumCoverPath;
    }

    public String getAlbumUidNickName() {
        return albumUidNickName;
    }

    public void setAlbumUidNickName(String albumUidNickName) {
        this.albumUidNickName = albumUidNickName;
    }

    public int getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(int auditStatus) {
        this.auditStatus = auditStatus;
    }

    public long getReplyId() {
        return replyId;
    }

    public void setReplyId(long replyId) {
        this.replyId = replyId;
    }

    public long getAlbumUid() {
        return albumUid;
    }

    public void setAlbumUid(long albumUid) {
        this.albumUid = albumUid;
    }

    public long getCommentId() {
        // 重构接口中
        // 一级评论：commentId为id，replyId为0
        // 回复：commentId为父id，replyId为id
        if (replyId > 0) {
            return replyId;
        }
        return commentId;
    }

    public void setCommentId(long commentId) {
        this.commentId = commentId;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public boolean isLiked() {
        return liked;
    }

    public void setLiked(boolean liked) {
        this.liked = liked;
    }

    public long getLikes() {
        return likes;
    }

    public void setLikes(long likes) {
        this.likes = likes;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public long getReplyCount() {
        return replyCount;
    }

    public void setReplyCount(long replyCount) {
        this.replyCount = replyCount;
    }

    public String getSmallHeader() {
        return smallHeader;
    }

    public void setSmallHeader(String smallHeader) {
        this.smallHeader = smallHeader;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public long getCommentUid() {
        return commentUid;
    }

    public void setCommentUid(long commentUid) {
        this.commentUid = commentUid;
    }

    public String getAlbumTitle() {
        return albumTitle;
    }

    public void setAlbumTitle(String albumTitle) {
        this.albumTitle = albumTitle;
    }

    public boolean isLookAlled() {
        return isLookAlled;
    }

    public void setLookAlled(boolean lookAlled) {
        isLookAlled = lookAlled;
    }

    public int getPlayProgress() {
        return playProgress;
    }

    public void setplayProgress(int playProgress) {
        this.playProgress = playProgress;
    }

    public boolean isHighQuality() {
        return isHighQuality;
    }

    public void setHighQuality(boolean highQuality) {
        isHighQuality = highQuality;
    }

    public List<AlbumCommentModel> getReplies() {
        return replies;
    }

    public void setReplies(List<AlbumCommentModel> replies) {
        this.replies = replies;
    }

    public List<UserLevelTag> getUserTags() {
        return userTags;
    }

    public void setUserTags(List<UserLevelTag> userTags) {
        this.userTags = userTags;
    }

    public long getParentReplyId() {
        return parentReplyId;
    }

    public void setParentReplyId(long parentReplyId) {
        this.parentReplyId = parentReplyId;
    }

    public String getParentReplyNickName() {
        return parentReplyNickName;
    }

    public void setParentReplyNickName(String parentReplyNickName) {
        this.parentReplyNickName = parentReplyNickName;
    }

    public long getParentReplyUid() {
        return parentReplyUid;
    }

    public void setParentReplyUid(long parentReplyUid) {
        this.parentReplyUid = parentReplyUid;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public CommentListItemBean commentTo2CommentListItemBean() {
        CommentListItemBean commentListItemBean = new CommentListItemBean();
        commentListItemBean.setCreateTime(this.createdAt);
        commentListItemBean.setLikeCount(this.likes);
        commentListItemBean.setLikeStatus(this.liked);
        commentListItemBean.setCanDelete(this.uid == UserInfoMannage.getUid());
        commentListItemBean.setReplyCount(this.replyCount);
        CommentUserBean commentUserBean = new CommentUserBean();
        commentUserBean.setAvatar(this.smallHeader);
        commentUserBean.setNickname(this.nickname);
        commentUserBean.setUid(this.uid);
        List<UserLevelTag> userTags = this.userTags;
        int userTagSize = userTags != null ? userTags.size() : 0;
        boolean isVipTip = false;
        for (int i = 0; i < userTagSize; i++) {
            UserLevelTag userLevelTag = userTags.get(0);
            if ("vip".equals(userLevelTag.businessName)) {
                isVipTip = true;
                break;
            }
        }
        if (!TextUtils.isEmpty(this.vipIconUrl) || isVipTip) {
            commentUserBean.setVip(true);
        }
        commentListItemBean.setUser(commentUserBean);
        commentListItemBean.setContent(this.content);
        commentListItemBean.setCommentId(this.commentId);
        commentListItemBean.setParentCommentId(this.parentAlbumId);
        commentListItemBean.setScore(this.score);
        int size = this.replies != null ? this.replies.size() : 0;
        ArrayList<CommentListItemBean> itemReplies = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            AlbumCommentModel albumCommentModelTemp = this.replies.get(i);
            itemReplies.add(albumCommentModelTemp.commentTo2CommentListItemBean());
        }
        commentListItemBean.setReplys(itemReplies);
        commentListItemBean.setReplyId(this.replyId);
        commentListItemBean.setCommentUid(this.commentUid);
        commentListItemBean.setParentReplyId(this.parentReplyId);
        commentListItemBean.setParentReplyNickName(this.parentReplyNickName);
        commentListItemBean.setParentReplyUid(this.parentReplyUid);
        commentListItemBean.setRegion(this.region);
        return commentListItemBean;
    }
}
