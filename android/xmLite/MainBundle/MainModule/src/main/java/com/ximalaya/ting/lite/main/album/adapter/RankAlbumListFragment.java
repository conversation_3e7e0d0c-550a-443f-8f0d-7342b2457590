package com.ximalaya.ting.lite.main.album.adapter;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.lite.main.model.rank.GroupRankAlbumList;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.HashMap;
import java.util.List;

/**
 * 专辑的排行榜
 *
 * <AUTHOR> on 2016/12/5.
 */
public class RankAlbumListFragment extends BaseFragment2 implements IDataCallBack<GroupRankAlbumList>, IRefreshLoadMoreListener, AdapterView.OnItemClickListener {
    private static String KEY_RANK_LIST_ID = "key_rank_list_id";
    private static String KEY_TAB_NAME = "key_tab_name";
    private static String KEY_RANK_TITLE = "key_rank_title";

    private RankAlbumAdapter mAdapter;
    private RefreshLoadMoreListView mListView;

    private int mPageId = 1;
    private long mTotalCount = -1;

    private long mRankingListId;
    private boolean mIsLoading = false;
    private boolean mIsFirst = true;//是否是第一次加载
    private String mRankTitle;

    //当前tab的名字
    private String mTabName = "RankAlbumListFragment";  //调试使用

    /**
     * @param rankingListId
     * @param tabName       getPageLogicName来使用的
     * @param rankTitle     整个排行榜的标题
     */
    public static Bundle getArgumentBundle(long rankingListId, String tabName, String rankTitle) {
        Bundle bundle = new Bundle();
        bundle.putLong(KEY_RANK_LIST_ID, rankingListId);
        bundle.putString(KEY_TAB_NAME, tabName);
        bundle.putString(KEY_RANK_TITLE, rankTitle);
        return bundle;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mRankingListId = bundle.getLong(KEY_RANK_LIST_ID);
            mRankTitle = bundle.getString(KEY_RANK_TITLE, "");
            mTabName = bundle.getString(KEY_TAB_NAME, "RankAlbumListFragment");
        }
        mListView = (RefreshLoadMoreListView) findViewById(R.id.main_listview);
        mListView.setOnRefreshLoadMoreListener(this);
        mListView.getRefreshableView().setPadding(0, 0, 0, getResourcesSafe().getDimensionPixelSize(R.dimen.host_bottom_bar_height));
        mListView.getRefreshableView().setClipToPadding(false);
        mListView.setOnItemClickListener(this);
        mListView.addOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {

            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (getiGotoTop() != null) {
                    getiGotoTop().setState(firstVisibleItem > 12);
                }
            }
        });
    }

    @Override
    protected void loadData() {
        if (mIsLoading) {
            return;
        }
        mIsLoading = true;
        HashMap<String, String> p = new HashMap<>();
        p.put("device", "android");
        p.put("version", DeviceUtil.getVersion(mContext));
        p.put("pageId", String.valueOf(mPageId));
        p.put("pageSize", "20");
        p.put("deviceId", DeviceUtil.getDeviceToken(getActivity()));
        p.put("rankingListId", String.valueOf(mRankingListId));
        if (mPageId == 1 && mIsFirst) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        //该接口增加极速版标识
        p.put("speed", "1");
        LiteCommonRequest.getRankGroupAlbumList(p, this);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_rank_album;
    }


    @Override
    public void onSuccess(final GroupRankAlbumList object) {
        if (!canUpdateUi()) {
            return;
        }
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (!canUpdateUi()) {
                    return;
                }
                mIsFirst = false;
                onPageLoadingCompleted(LoadCompleteType.OK);
                if (object != null) {
                    mTotalCount = object.totalCount;
                }
                if (object != null && object.list != null && !object.list.isEmpty()) {
                    if (mAdapter == null) {
                        mAdapter = new RankAlbumAdapter((MainActivity) mActivity, null);
                        if (TextUtils.equals(mRankTitle, "总榜")) {
                            mAdapter.setType(RankAlbumAdapter.RANK_TYPE_ALL);
                        }
                        mListView.setAdapter(mAdapter);
                        mAdapter.addListData(object.list);
                    } else {
                        if (mPageId == 1) {
                            mAdapter.clear();
                        }
                        mAdapter.addListData(object.list);
                    }
                } else {
                    if (mPageId == 1) {
                        if (mAdapter != null) {
                            mAdapter.clear();
                        }
                        onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                    }
                }
                mIsLoading = false;
                int count = 0;
                if (mAdapter != null) {
                    count = mAdapter.getCount();
                }
                if (mTotalCount > count) {
                    mListView.onRefreshComplete(true);
                } else {
                    mListView.onRefreshComplete(false);
                }
            }
        });
    }

    @Override
    public void onError(int code, String message) {
        mIsLoading = false;
        mIsFirst = false;
        if (!canUpdateUi()) {
            return;
        }
        if (mPageId == 1) {
            if (mAdapter != null) {
                mAdapter.clear();
            }
            mListView.onRefreshComplete(true);
            mListView.setHasMoreNoFooterView(false);
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        } else {
            ApiErrorToastManager.showToast(code,message);
            mListView.onRefreshComplete(true);
        }
    }

    @Override
    public void onRefresh() {
        mPageId = 1;
        if (mListView != null) {
            mListView.setFooterViewVisible(View.VISIBLE);
        }
        loadData();
    }

    @Override
    public void onMore() {
        mPageId++;
        loadData();
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long itemId) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        List<Album> listData = mAdapter.getListData();
        if (listData == null || listData.size() == 0) {
            return;
        }
        if (index >= 0 && index < mAdapter.getCount()) {
            Album album = mAdapter.getListData().get(index);
            if (!(album instanceof AlbumM)) {
                return;
            }
            AlbumM albumM = (AlbumM) album;
            AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_OTHER, ConstantsOpenSdk.PLAY_FROM_RANK, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
        }
    }


    @Override
    public void onMyResume() {
        tabIdInBugly = 38547;
        super.onMyResume();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(mTopBtnListener);
        }
    }

    private IGotoTop.IGotoTopBtnClickListener mTopBtnListener = new IGotoTop.IGotoTopBtnClickListener() {
        @Override
        public void onClick(View v) {
            if (mListView == null) {
                return;
            }
            mListView.getRefreshableView().setSelection(0);
        }
    };

    @Override
    public void onPause() {
        super.onPause();
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
    }

    @Override
    protected String getPageLogicName() {
        return mTabName;
    }
}
