package com.ximalaya.ting.lite.main.newhome.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.trace.TraceFreeTrackManager;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.fragment.TrackContentListFragment;
import com.ximalaya.ting.lite.main.model.album.MainAlbumOtherData;
import com.ximalaya.ting.lite.main.model.newhome.LiteFloorModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Desc: 参考LiteHomeFreeRecommendTrackHorizontalProvider
 */
public class LiteHomeFreeRecommendTrackHorizontalProvider implements
        IMulitViewTypeViewAndData<LiteHomeFreeRecommendTrackHorizontalProvider.TrackRowHolder, List<TrackM>> {
    private Activity mActivity;
    private BaseFragment2 mFragment;
    private IRecommendFeedItemActionListener recommendFeedItemActionListener;

    public LiteHomeFreeRecommendTrackHorizontalProvider(BaseFragment2 fragment, IRecommendFeedItemActionListener recommendFeedItemActionListener) {
        this.mFragment = fragment;
        this.mActivity = fragment.getActivity();
        this.recommendFeedItemActionListener = recommendFeedItemActionListener;

    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_free_recommend_track_row_item, parent, false);
    }

    @Override
    public void bindViewDatas(TrackRowHolder viewHolder, ItemModel<List<TrackM>> t, View convertView, final int position) {
        if (viewHolder == null || t == null || ToolUtil.isEmptyCollects(t.getObject()) || !(t.getTag() instanceof LiteFloorModel))
            return;

        final LiteFloorModel floorModel = (LiteFloorModel) t.getTag();
        MainAlbumOtherData mainAlbumOtherData = floorModel.getOtherData();
        if (viewHolder.tvFreeTitle != null) {
            viewHolder.tvFreeTitle.setText(floorModel.getTitle());
        }
        if (viewHolder.tvFreeContentTitle != null && mainAlbumOtherData != null) {
            viewHolder.tvFreeContentTitle.setText(mainAlbumOtherData.buttonDisplayName);
        }
        if (viewHolder.tvFreeMore != null) {
            viewHolder.tvFreeMore.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    int poolId = mainAlbumOtherData != null ? mainAlbumOtherData.poolId : 0;
                    mFragment.startFragment(TrackContentListFragment.newInstance(floorModel.getTitle(), poolId));
                    TraceFreeTrackManager.INSTANCE.homeTrackMoreClickView();
                }
            });
        }
        //处理rv声音列表
        bindAlbumTrackRecyclerView(t.getObject(), viewHolder);
    }

    /**
     * @param trackList
     * @param viewHolder
     */
    private void bindAlbumTrackRecyclerView(List<TrackM> trackList, LiteHomeFreeRecommendTrackHorizontalProvider.TrackRowHolder viewHolder) {
        if (viewHolder.mRvTrackList.getAdapter() == null) {
            viewHolder.mRvTrackList.setAdapter(new HomeFreeTrackRvAdapter(mActivity,mFragment, new ArrayList<>(),false));
            //禁止滑动
            viewHolder.mRvTrackList.setNestedScrollingEnabled(false);
            viewHolder.mRvTrackList.setLayoutManager(new LinearLayoutManager(mActivity));
            viewHolder.mRvTrackList.setItemViewCacheSize(3);
        }
        if (!(viewHolder.mRvTrackList.getAdapter() instanceof HomeFreeTrackRvAdapter)) {
            return;
        }
        HomeFreeTrackRvAdapter albumTrackListAdapter = (HomeFreeTrackRvAdapter) viewHolder.mRvTrackList.getAdapter();
        List<TrackM> valueList = albumTrackListAdapter.getValueList();
        //无数据的时候也需要清除，防止上次的的声音view被复用
        valueList.clear();
        if (trackList != null) {
            valueList.addAll(trackList);
        }
        albumTrackListAdapter.notifyDataSetChanged();
    }

    @Override
    public TrackRowHolder buildHolder(View convertView) {

        TrackRowHolder albumRowHolder = new TrackRowHolder(convertView);
        return albumRowHolder;
    }

    public static class TrackRowHolder extends HolderAdapter.BaseViewHolder {

        RecyclerView mRvTrackList;
        TextView tvFreeTitle;
        LinearLayout llFreePage;
        TextView tvFreeMore;
        TextView tvFreeContentTitle;
        View freeContentContainer;


        public TrackRowHolder(View convertView) {
            mRvTrackList = convertView.findViewById(R.id.free_item_layout);
            tvFreeTitle = convertView.findViewById(R.id.tv_free_title);
            llFreePage = convertView.findViewById(R.id.ll_free_page);
            tvFreeMore = convertView.findViewById(R.id.tv_free_title_more);
            tvFreeContentTitle = convertView.findViewById(R.id.tv_free_content_title);
            freeContentContainer = convertView.findViewById(R.id.main_layout_section_content);
        }
    }
}
