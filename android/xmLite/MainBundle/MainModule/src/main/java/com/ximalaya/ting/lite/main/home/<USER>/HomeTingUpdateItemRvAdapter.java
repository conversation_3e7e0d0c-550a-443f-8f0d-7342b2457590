package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter;
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder;
import com.ximalaya.ting.lite.main.model.album.HomeTingUpdateModel;
import com.ximalaya.ting.android.host.model.track.EverydayUpdateTrack;

import java.util.List;

/**
 * Created by qinhuifeng on 2020/11/24
 *
 * <AUTHOR>
 */
public class HomeTingUpdateItemRvAdapter extends MultiRecyclerAdapter<EverydayUpdateTrack, SuperRecyclerHolder> {

    public HomeTingUpdateModel homeTingUpdateModel;
    public BaseFragment2 baseFragment2;
    public Activity mActivity;

    public HomeTingUpdateItemRvAdapter(Activity activity, List<EverydayUpdateTrack> mValueList, BaseFragment2 fragment) {
        super(activity, mValueList);
        mActivity = activity;
        baseFragment2 = fragment;
    }

    @Override
    public SuperRecyclerHolder createMultiViewHolder(Context mCtx, @NonNull View itemView, int viewType) {
        return SuperRecyclerHolder.createViewHolder(mCtx, itemView);
    }

    @Override
    public void onBindMultiViewHolder(SuperRecyclerHolder holder, EverydayUpdateTrack trackM, int viewType, int position) {
        if (trackM == null) {
            return;
        }
        ImageView playAnimationFlag = (ImageView) holder.getViewById(R.id.main_playing_flag);
        TextView trackTitle = (TextView) holder.getViewById(R.id.main_sound_name);
        TextView hasPlayPercent = (TextView) holder.getViewById(R.id.main_has_play_percent);
        trackTitle.setText(trackM.getTrackTitle());
        //处理播放中的动画
        boolean isCurrentTrack = (PlayTools.isPlayCurrTrackById(getContext(), trackM.getDataId()));
        XmPlayerManager xManager = XmPlayerManager.getInstance(getContext());
        //是当前声音
        if (isCurrentTrack) {
            trackTitle.setSingleLine(false);
            trackTitle.setMaxLines(2);
            trackTitle.setEllipsize(TextUtils.TruncateAt.END);
            trackTitle.setTextColor(0xFFE83F46);
            hasPlayPercent.setVisibility(View.INVISIBLE);
            //当前声音正在播放
            if (xManager.isPlaying()) {
                playAnimationFlag.setImageResource(R.drawable.host_anim_play_flag_v2);
                if (playAnimationFlag.getDrawable() instanceof AnimationDrawable) {
                    final AnimationDrawable animationDrawable = (AnimationDrawable) playAnimationFlag.getDrawable();
                    playAnimationFlag.post(new Runnable() {
                        @Override
                        public void run() {
                            if (animationDrawable == null) {
                                return;
                            }
                            if (animationDrawable.isRunning()) {
                                return;
                            }
                            animationDrawable.start();
                        }
                    });
                }
            } else {
                playAnimationFlag.setImageResource(R.drawable.host_play_flag_wave_v2_11);
            }
        } else {
            if (trackM.isNeedTopPosition) {
                int lastPos = XmPlayerManager.getInstance(mActivity).getHistoryPos(trackM.getDataId());
                int playPercent = ToolUtil.getPlayPercent(lastPos, trackM.getDuration());
                if (playPercent > 0 && playPercent < 90) {
                    hasPlayPercent.setText("已播" + playPercent + "%");
                    hasPlayPercent.setVisibility(View.VISIBLE);
                } else {
                    hasPlayPercent.setVisibility(View.INVISIBLE);
                }
            } else {
                hasPlayPercent.setVisibility(View.INVISIBLE);
            }
            trackTitle.setMaxLines(1);
            trackTitle.setSingleLine(true);
            trackTitle.setEllipsize(TextUtils.TruncateAt.END);
            trackTitle.setTextColor(0xFF666666);
            playAnimationFlag.setImageResource(R.drawable.main_icon_album_track_item_play);
        }
        holder.setOnItemClickListenner(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HomeRecommendTingUpdataAdapterProvider.playTingUpdateTrack(mActivity, baseFragment2, homeTingUpdateModel, trackM.getDataId(), true);
            }
        });
    }

    @Override
    public int getMultiItemViewType(EverydayUpdateTrack model, int position) {
        return 0;
    }

    @Override
    public int getMultiItemLayoutId(int viewType) {
        return R.layout.main_item_home_ting_updata_item;
    }

    public void updateHomeTingUpdateModel(HomeTingUpdateModel model) {
        homeTingUpdateModel = model;
    }
}
