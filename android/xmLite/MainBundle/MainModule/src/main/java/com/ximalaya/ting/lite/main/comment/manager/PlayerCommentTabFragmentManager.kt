package com.ximalaya.ting.lite.main.comment.manager

import android.app.Activity
import android.os.Bundle
import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.comment.CommentListPresenter
import com.ximalaya.ting.lite.main.comment.fragment.PlayerCommentListFragment
import com.ximalaya.ting.lite.main.comment.fragment.PlayerHotCommentListFragment

/**
 * Created by duming<PERSON> on 2021/11/18
 *
 *
 * Desc:
 */
class ProgramCommentTabFragmentManager(
    private val activity: Activity,
    private val fragmentManager: FragmentManager,
    @IdRes private val containerId: Int
) {

    companion object {

        const val TAB_SHALL_REPLACE = "tab_show_replace"
        val TAB_LISTEN_PROGRAM: Int = R.id.main_tv_header_sort_hot
        val TAB_INNER_NOVEL: Int = R.id.main_tv_header_sort_time

        /**
         * 注入或者更新argument
         */

        @JvmStatic
        fun injectFragmentArgument(fragment: Fragment?, bundle: Bundle?) {
            if (fragment == null || bundle == null) {
                return
            }
            val arguments = fragment.arguments
            if (arguments != null) {
                arguments.putAll(bundle)
            } else {
                if (!fragment.isStateSaved) {
                    fragment.arguments = bundle
                }
            }
        }
    }

    private var isAddListenProgram = false
    private var isAddNovel = false

    private var changeTabFragment = false //是否切换了底部的tab = false
    var currFragment: Fragment? = null
        private set
    var currentTab = -1
        private set
    private val TAG = javaClass.name


    /**
     * 展示或者隐藏fragment，低内存的情况下replace
     */
    fun showFragment(checkedId: Int, any: Any?) {
        if (checkedId == -1 || activity.isFinishing || activity.isDestroyed) {
            return
        }
        val bundle: Bundle = if (any is Bundle) {
            any
        } else {
            Bundle()
        }
        //如果是低内存的话，替换fragment
        var shallReplace = MainActivity.isLowMemoryDevice
        if (bundle.containsKey(TAB_SHALL_REPLACE)) {
            //传递的参数最终决定是否要替换
            shallReplace = bundle.getBoolean(TAB_SHALL_REPLACE)
        }
        val fm = fragmentManager
        var tabTingProgram = fm.findFragmentByTag(TAB_LISTEN_PROGRAM.toString())
        var tabNovel = fm.findFragmentByTag(TAB_INNER_NOVEL.toString())
        val transaction = fm.beginTransaction()
        if (!shallReplace) {
            //隐藏目前所有的tab页面
            if (tabTingProgram != null) {
                transaction.hide(tabTingProgram)
            }
            if (tabNovel != null) {
                transaction.hide(tabNovel)
            }
        } else {
            resetTabFragment()
        }
        changeTabFragment = true
        if (checkedId == TAB_LISTEN_PROGRAM) {
            //经典模式我听tab
            if (shallReplace) {
                tabTingProgram = PlayerHotCommentListFragment()
                injectFragmentArgument(tabTingProgram, bundle)
                transaction.replace(containerId, tabTingProgram, TAB_LISTEN_PROGRAM.toString())
            } else {
                if (tabTingProgram == null && !isAddListenProgram) {
                    tabTingProgram = PlayerHotCommentListFragment()
                    isAddListenProgram = true
                    injectFragmentArgument(tabTingProgram, bundle)
                    transaction.add(containerId, tabTingProgram, TAB_LISTEN_PROGRAM.toString())
                } else {
                    if (tabTingProgram != null) {
                        injectFragmentArgument(tabTingProgram, bundle)
                        transaction.show(tabTingProgram)
                    }
                }
            }
            currFragment = tabTingProgram
        } else if (checkedId == TAB_INNER_NOVEL) {
            //经典模式小说tab
            if (shallReplace) {
                tabNovel = PlayerCommentListFragment()
                injectFragmentArgument(tabNovel, bundle)
                transaction.replace(containerId, tabNovel, TAB_INNER_NOVEL.toString())
            } else {
                if (tabNovel == null && !isAddNovel) {
                    tabNovel = PlayerCommentListFragment()
                    isAddNovel = true
                    injectFragmentArgument(tabNovel, bundle)
                    transaction.add(containerId, tabNovel, TAB_INNER_NOVEL.toString())
                } else {
                    if (tabNovel != null) {
                        injectFragmentArgument(tabNovel, bundle)
                        transaction.show(tabNovel)
                    }
                }
            }
            currFragment = tabNovel
        }
        currentTab = checkedId
        transaction?.commitNowAllowingStateLoss()
    }

    fun findFragmentByTag(tag: Int): Fragment? {
        return fragmentManager.findFragmentByTag(tag.toString())
    }


    private fun resetTabFragment() {
        //经典模式
        resetTabFragmentForNormalMode()
    }

    private fun resetTabFragmentForNormalMode() {
        isAddListenProgram = false
        isAddNovel = false
    }
}