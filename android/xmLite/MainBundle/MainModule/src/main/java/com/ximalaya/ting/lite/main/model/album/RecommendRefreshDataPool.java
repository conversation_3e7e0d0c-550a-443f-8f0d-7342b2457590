package com.ximalaya.ting.lite.main.model.album;

import android.content.Context;
import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.model.album.Album;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by le.xin on 2017/3/23.
 *
 * <AUTHOR>
 * 首页刷新
 */

public class RecommendRefreshDataPool {
    private final Context mContext;
    private final Set<Long> mRequestedDataIds = new HashSet<>();
    private final Set<Long> mRequestedSpecialIds = new HashSet<>();
    private final Set<Long> mRequestedRoomIds = new HashSet<>();
    private final RecommendItem mRecommendItem;
    private RecommendRefreshModel<Object> mFirstData;    // 首次请求到的数据列表
    private int itemCount;
    private int loopCount;
    private int tempLoopCount;
    private int curPageId = 1;
    private int mKeywordsStartIndex = CATEGORY_KEY_WORDS_SIZE;
    private final static String REQUESTED_AD_ID = "excludedAdAlbumIds";
    private final Set<Long> mRequestedAdAlbumId = new HashSet<>(); // 已经请求的广告的id
    private final Set<Long> mCurrPageAdAlbumId = new HashSet<>();    // 当前页面已经有的广告的id
    public static Set<Long> mAllPageAdAlbumId = new HashSet<>();    // 推荐页所有的位置的广告id

    public RecommendRefreshDataPool(Context context, @NonNull RecommendItem itemData) {
        mContext = context;
        mRecommendItem = itemData;
        itemData.setRefreshDataPool(this);
        if (mRecommendItem.getList() != null) {
            if (mRecommendItem.getCurrentLoopCount() >= 0) {
                loopCount = mRecommendItem.getCurrentLoopCount();
            } else {
                loopCount = mRecommendItem.getLoopCount();
            }
            tempLoopCount = mRecommendItem.getLoopCount();
            mFirstData = new RecommendRefreshModel<>(mRecommendItem.getList(), null);
            itemCount = mRecommendItem.getList().size();

            setRequestParam();
        }
    }

    private void setRequestParam() {
        if (mRecommendItem.getCurrentLoopCount() >= 0) {
            loopCount = mRecommendItem.getCurrentLoopCount();
        } else {
            loopCount = mRecommendItem.getLoopCount();
        }
        if (!ToolUtil.isEmptyCollects(mRecommendItem.getRequestDataIds())) {
            mRequestedDataIds.addAll(mRecommendItem.getRequestDataIds());
        }
        for (Object object : mRecommendItem.getList()) {
            addId(object);
        }
    }

    private static final int CATEGORY_KEY_WORDS_SIZE = 4;

    private void addId(Object object) {
        mAllPageAdAlbumId.removeAll(mCurrPageAdAlbumId);
        mCurrPageAdAlbumId.clear();
        if (object instanceof Album) {
            if (((Album) object).getId() > 0) {
                mRequestedDataIds.add(((Album) object).getId());
            }

            if (object instanceof AlbumM) {
                if (((AlbumM) object).getSpecialId() > 0) {
                    mRequestedSpecialIds.add(((AlbumM) object).getSpecialId());
                }
            }

            if (object instanceof AlbumM) {
                if (((AlbumM) object).getRoomId() > 0) {
                    mRequestedRoomIds.add(((AlbumM) object).getRoomId());
                }
            }

            if (object instanceof AlbumM && ((AlbumM) object).getAdInfo() != null) {
                mRequestedAdAlbumId.add(((AlbumM) object).getId());
                mCurrPageAdAlbumId.add(((AlbumM) object).getId());
                mAllPageAdAlbumId.add(((AlbumM) object).getId());
            }
        }
        if (mRecommendItem != null) {
            mRecommendItem.setRequestDataIds(mRequestedDataIds);
        }
    }
}
