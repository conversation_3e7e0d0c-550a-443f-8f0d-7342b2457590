package com.ximalaya.ting.lite.main.download.callback;


import com.ximalaya.ting.lite.main.download.inter.ITaskCallback;
import com.ximalaya.ting.lite.main.download.utils.TaskStateConst;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   : 抽象类，可以支持CountDownLatch操作
 */
public class CountDownCallback implements ITaskCallback {
    CountDownLatch countDown;

    public void setCountDownLatch(CountDownLatch latch) {
        this.countDown = latch;
    }

    @Override
    public void onStateChanged(int state) {
        switch (state) {
            case TaskStateConst.STATE_FINISH:
            case TaskStateConst.STATE_ERROR:
            case TaskStateConst.STATE_PAUSED:
                // 任务完成时，通知外部线程。
                if (this.countDown != null) {
                    this.countDown.countDown();
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onProgressUpdate(long once, long done, long fileSize) {
    }
}
