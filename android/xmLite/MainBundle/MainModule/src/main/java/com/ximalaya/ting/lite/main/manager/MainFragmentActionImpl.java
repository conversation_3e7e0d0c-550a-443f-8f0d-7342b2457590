package com.ximalaya.ting.lite.main.manager;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ximalaya.reactnative.XMReactNativeApi;
import com.ximalaya.reactnative.debug.DebugBundleManager;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment;
import com.ximalaya.ting.android.host.fragment.child.ChildProtectGuideDialog;
import com.ximalaya.ting.android.host.fragment.child.ChildProtectHintDialog;
import com.ximalaya.ting.android.host.fragment.earn.BigRedPacketDialogFragment;
import com.ximalaya.ting.android.host.fragment.earn.EarnByListenDialogFragment;
import com.ximalaya.ting.android.host.fragment.earn.NewSuperMultiplyDialogFragment;
import com.ximalaya.ting.android.host.fragment.earn.ReadStageRedPacketDialogFragment;
import com.ximalaya.ting.android.host.fragment.earn.SuperMultiplyCompleteDialogFragment;
import com.ximalaya.ting.android.host.fragment.earn.SuperMultiplyDialogFragment;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.fragment.web.js.JSPayModule;
import com.ximalaya.ting.android.host.listener.CommonPopupDialogCallBack;
import com.ximalaya.ting.android.host.listenertask.callback.JssdkFuliRewardCallback;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleException;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveAppFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.NewShortPlayFragmentModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.configurecenter.ConfigureNeedCacheLocalManager;
import com.ximalaya.ting.android.host.manager.earn.FuliPageManager;
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.ad.AdWrapper;
import com.ximalaya.ting.android.host.model.childprotect.ChildProtectInfo;
import com.ximalaya.ting.android.host.model.duanju.XmDuanJuItemTransferModel;
import com.ximalaya.ting.android.host.model.earn.CommonPopupDialogDataModel;
import com.ximalaya.ting.android.host.model.earn.FuliBallDialogDataModel;
import com.ximalaya.ting.android.host.model.earn.HasLoginEarnGuideDataModel;
import com.ximalaya.ting.android.host.model.earn.SignInDialogDataModel;
import com.ximalaya.ting.android.host.model.newuser.QuickListenModel;
import com.ximalaya.ting.android.host.model.user.InterestCardSetting;
import com.ximalaya.ting.android.host.model.user.NewInterestCardResp;
import com.ximalaya.ting.android.host.model.user.NewUserRewardGiftList;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.album.fragment.LiteAlbumFragment;
import com.ximalaya.ting.lite.main.child.ChildProtectionPassWordFragment;
import com.ximalaya.ting.lite.main.child.ChildProtectionSettingFragment;
import com.ximalaya.ting.lite.main.customize.CustomizeBottomStyleDialog;
import com.ximalaya.ting.lite.main.customize.CustomizeFragment;
import com.ximalaya.ting.lite.main.earn.FullCoinBallDialogManager;
import com.ximalaya.ting.lite.main.earn.LoginGuideDialogManager;
import com.ximalaya.ting.lite.main.earn.dialog.BaseLoginGuideDialogFragment;
import com.ximalaya.ting.lite.main.earn.dialog.CommonPopupDialogFragment;
import com.ximalaya.ting.lite.main.earn.dialog.FuliCoinBallNoAdDialogFragment;
import com.ximalaya.ting.lite.main.earn.dialog.FuliListenMaxLimitFragment;
import com.ximalaya.ting.lite.main.earn.dialog.FuliSignInDialogFragment;
import com.ximalaya.ting.lite.main.earn.dialog.MarketGuideThemeV1Dialog;
import com.ximalaya.ting.lite.main.earn.dialog.MarketGuideThemeV2Dialog;
import com.ximalaya.ting.lite.main.earn.dialog.OptimizedHasLoginEarnGuideDialogFragment;
import com.ximalaya.ting.lite.main.earn.dialog.OptimizedHasLoginEarnGuideDialogFragmentNew;
import com.ximalaya.ting.lite.main.earn.dialog.OptimizedLoginNewUserRewardDialogFragment;
import com.ximalaya.ting.lite.main.home.fragment.VipAlbumContentListFragment;
import com.ximalaya.ting.lite.main.login.SmsLoginProxyFragment;
import com.ximalaya.ting.lite.main.mylisten.view.AllHistoryFragment;
import com.ximalaya.ting.lite.main.newuser.NewListenPlayFragment;
import com.ximalaya.ting.lite.main.newuser.dialog.NewUserQuickListenGuideDialogFragment;
import com.ximalaya.ting.lite.main.onekey.playpage.OneKeyRadioPlayFragment;
import com.ximalaya.ting.lite.main.play.dialog.FreeAlbumDownloadLimitDialog;
import com.ximalaya.ting.lite.main.playlet.fragment.PlayletDetailFragment;
import com.ximalaya.ting.lite.main.playnew.fragment.PlayFragmentNew;
import com.ximalaya.ting.lite.main.read.fragment.LoveNovelTabFragment;
import com.ximalaya.ting.lite.main.read.fragment.ProgramNovelTabFragment;
import com.ximalaya.ting.lite.main.record.fragment.UnLockRecordListFragment;
import com.ximalaya.ting.lite.main.setting.AccountSettingFragment;
import com.ximalaya.ting.lite.main.setting.DownloadCacheFragment;
import com.ximalaya.ting.lite.main.setting.SettingFragment;
import com.ximalaya.ting.lite.main.shortplay.ShortPlayDetailFragment;
import com.ximalaya.ting.lite.main.shortplay.XmShortPlayDetailFragment;
import com.ximalaya.ting.lite.main.tab.ChildModeHomeFragment;
import com.ximalaya.ting.lite.main.tab.HomeFragment;
import com.ximalaya.ting.lite.main.tab.IListenTabFragment;
import com.ximalaya.ting.lite.main.tab.MineTabFragment;
import com.ximalaya.ting.lite.main.tab.TruckWelfareFragment;
import com.ximalaya.ting.lite.main.truck.tab.TruckHomeFragment;
import com.ximalaya.ting.reactnative.container.ReactDebugFragment;
import com.ximalaya.ting.reactnative.container.ReactFragment;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR> on 2017/1/18.
 */

public class MainFragmentActionImpl implements IMainFragmentAction {

    private Map<Integer, Class<? extends BaseFragment>> fragmentMap = new HashMap<Integer, Class<? extends BaseFragment>>() {
        {
            put(Configure.MainFragmentFid.PLAY_FRAGMENT, PlayFragmentNew.class);
            put(Configure.MainFragmentFid.DOWNLOAD_CACHE_FRAGMENT, DownloadCacheFragment.class);
        }
    };

    @Override
    public BaseFragment newFragmentByFid(int fid) throws BundleException {
        BaseFragment fragment;
        Class clazz = fragmentMap.get(fid);
        if (clazz != null) {
            try {
                fragment = (BaseFragment) clazz.newInstance();
            } catch (InstantiationException e) {
                e.printStackTrace();
                throw new BundleException(Configure.mainBundleModel.bundleName, "new a fragment by fid" + fid + " failure!,Execption:" + e.toString());
            } catch (IllegalAccessException e) {
                e.printStackTrace();
                throw new BundleException(Configure.mainBundleModel.bundleName, "new a fragment by fid" + fid + "  failure!,Execption:" + e.toString());
            }
        } else {
            throw new BundleException(Configure.mainBundleModel.bundleName, "fid:" + fid + " --> can not find the Class, maybe fragment is not registered");
        }
        if (fragment != null) {
            fragment.fid = fid;
        }
        return fragment;
    }

    @Override
    public BaseFragment newAnchorSpaceFragment(long uid, int playSource) {
        return null;
    }


    @Override
    public BaseFragment newAlbumFragment(String title, long albumId, int from, int playSource,
                                         @Nullable String recSrc, @Nullable String recTrack,
                                         int unreadTrackNumber, AlbumEventManage.AlbumFragmentOption option) {
        LiteAlbumFragment fragment = LiteAlbumFragment.newInstance(title, recSrc, recTrack, albumId, from, playSource, unreadTrackNumber, option);
        return fragment;
    }

    @Override
    public BaseFragment newFeedBackMainFragment() {
        return null;
    }

    @Override
    public Class findClassByFid(int fid) {
        if (fragmentMap != null) {
            return fragmentMap.get(fid);
        }
        return null;
    }


    @Override
    public BaseDialogFragment newPayResultSimpleDialog(boolean b) {
        return null;
    }


    @Override
    public Class getPlayFragmentClass() {
        return PlayFragmentNew.class;
    }


    @Override
    public BaseFragment2 newRechargeFragment(int toRechargeChoose, double amount) {
        return null;
    }


    @Override
    public Class<?> getQrCodeScanFragment() {
        return null;
    }

    @Override
    public BaseFragment newOneKeyPlayFragment() {
        return null;
    }

    @Override
    public BaseFragment2 newCustomizeFragment(InterestCardSetting setting) {
        CustomizeFragment customizeFragment = CustomizeFragment.newInstance(setting);
        return customizeFragment;
    }

    @Override
    public BaseDialogFragment newSmsLoginProxyFragment() {
        return new SmsLoginProxyFragment();
    }

    @Override
    public BaseFragment newCategoryMetadataFragment(String category, String categoryTitle) {
        return null;
    }

    /**
     * Lite version 使用的接口
     */

    @Override
    public BaseFragment2 newTabFragmentByType(int tabType) {
        if (tabType == TabFragmentManager.TAB_HOME) {
            if (ChildProtectManager.isChildProtectOpen(BaseApplication.getMyApplicationContext())) {
                return new ChildModeHomeFragment();
            } else {
                return new HomeFragment();
            }
        } else if (tabType == TabFragmentManager.TAB_I_LISTEN) {
            //我听
            return IListenTabFragment.newInstance();
//        } else if (tabType == TabFragmentManager.TAB_NOVEL) {
//            //小说
//            return new LoveNovelTabFragment();
        } else if (tabType == TabFragmentManager.TAB_WELFARE) {
            BaseFragment2 web = FuliPageManager.getFuLiPageFragment();
            return web;
        } else if (tabType == TabFragmentManager.TAB_TRUCK_MODE_WELFARE) {
            String configUrl = ConfigureCenter.getInstance().getString(CConstants.Group_Base.GROUP_NAME, CConstants.Group_Base.ITEM_TASKPAGEURL, FuliPageManager.ERROR_DEF_URL);
            if (TextUtils.isEmpty(configUrl)) {
                configUrl = "";
            }
            String useUrl = "";
            if (FuliPageManager.ERROR_DEF_URL.equals(configUrl) || !configUrl.startsWith("http")) {
                Logger.d("福利页2==", "配置中心没有获取到url或者获取到的是错误的=" + configUrl);
                String mmkvSaveUrl = ConfigureNeedCacheLocalManager.getCacheFuliUrl();
                //配置中心没有加载成功，优先加载上次缓存的url
                if (!TextUtils.isEmpty(mmkvSaveUrl) && mmkvSaveUrl.startsWith("http") && !FuliPageManager.ERROR_DEF_URL.equals(mmkvSaveUrl)) {
                    useUrl = mmkvSaveUrl;

                    Logger.d("福利页2==", "使用最mmkv缓存方案=" + useUrl);
                } else {
                    //配置中心没有加载成功，mmkv也没有加载成功，使用最终客户端内置的url
                    useUrl = "https://m.ximalaya.com/growth-ssr-speed-welfare-center/page/welfare";

                    Logger.d("福利页2==", "使用最终客户端兜底方案=" + useUrl);
                }
            } else {
                //配置中心正确，使用配置中心的值
                useUrl = configUrl;
                //更新本地缓存的url
                ConfigureNeedCacheLocalManager.saveFuliUrl(configUrl);
                Logger.d("福利页2==", "使用配置中心方案，更新本地缓存=" + useUrl);
            }
            //如果是Debug并且使用UAT环境，url手动设置为uat地址，配置中心目前不支持uat
            if (ConstantsOpenSdk.isDebug && AppConstants.environmentId == AppConstants.ENVIRONMENT_UAT) {
                useUrl = "http://m.uat.ximalaya.com/growth-ssr-speed-welfare-center/page/welfare";
            }
            Logger.d("福利页2==", "配置url=" + useUrl);
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, useUrl);
            //去掉标标题栏
            bundle.putBoolean("embedded", true);
            BaseFragment2 web = new TruckWelfareFragment();
            web.setArguments(bundle);
            return web;
        } else if (tabType == TabFragmentManager.TAB_MINE) {
            return new MineTabFragment();
        } else if (tabType == TabFragmentManager.TAB_TRUCK_MODE_DIANTAI) {
            //卡友模式首页
            return new TruckHomeFragment();
        } else if (tabType == TabFragmentManager.TAB_TRUCK_MODE_SELECTED) {
            //卡友模式精选页面
            return new ProgramNovelTabFragment();
        } else if (tabType == TabFragmentManager.TAB_TRUCK_MODE_MINE) {
            //卡友模式我页
            return new MineTabFragment();
        }
        return null;
    }

    @Override
    public BaseDialogFragment newCommonPopupDialogFragment(CommonPopupDialogDataModel dataModel, CommonPopupDialogCallBack callBack) {
        Bundle bundle = CommonPopupDialogFragment.newArgument(dataModel);
        CommonPopupDialogFragment fragment = new CommonPopupDialogFragment();
        fragment.setCommonPopupDialogCallBack(callBack);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public BaseDialogFragment newOptimizedHasLoginEarnGuideDialogFragment(HasLoginEarnGuideDataModel dataModel) {
        OptimizedHasLoginEarnGuideDialogFragmentNew fragment = new OptimizedHasLoginEarnGuideDialogFragmentNew();
        Bundle bundle = OptimizedHasLoginEarnGuideDialogFragment.newArgument(dataModel);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public BaseDialogFragment newNoLoginEarnGuideDialogFragment(BaseDialogFragment.OnDialogCloseListener onDialogCloseListener) {
        BaseLoginGuideDialogFragment fragment = LoginGuideDialogManager.getGuideDialog();
        fragment.setOnDialogCloseListener(onDialogCloseListener);
        return fragment;
    }

    @Override
    public BaseDialogFragment newUserQuickListenGuideDialogFragment() {
        NewUserQuickListenGuideDialogFragment fragment = new NewUserQuickListenGuideDialogFragment();
        return fragment;
    }

    @Override
    public BaseFragment newListenFragment(QuickListenModel quickListenModel) {
        NewListenPlayFragment fragment = NewListenPlayFragment.newInstance(quickListenModel);
        return fragment;
    }

    @Override
    public BaseDialogFragment newFuliCoinBallDialogFragment(FuliBallDialogDataModel dialogDataModel, AdWrapper ttFeedAd, JssdkFuliRewardCallback callback) {
        return FullCoinBallDialogManager.newFuliCoinBallDialogFragment(dialogDataModel, ttFeedAd, callback);
    }

    @Override
    public BaseDialogFragment newFuliCoinBallNoAdDialogFragment(FuliBallDialogDataModel dialogDataModel) {
        Bundle argument = FuliCoinBallNoAdDialogFragment.newArgument(dialogDataModel);
        FuliCoinBallNoAdDialogFragment fragment = new FuliCoinBallNoAdDialogFragment();
        fragment.setArguments(argument);
        return fragment;
    }

    @Override
    public BaseDialogFragment newFuliListenMaxLimitDialogFragment(FuliBallDialogDataModel dialogDataModel, JssdkFuliRewardCallback callback) {
        Bundle argument = FuliListenMaxLimitFragment.newArgument(dialogDataModel);
        FuliListenMaxLimitFragment fragment = new FuliListenMaxLimitFragment();
        fragment.setJssdkFuliRewardCallback(callback);
        fragment.setArguments(argument);
        return fragment;
    }

    @Override
    public BaseDialogFragment newFuliSignInDialogFragment(SignInDialogDataModel dialogDataModel, AdWrapper ttFeedAd, JssdkFuliRewardCallback callback) {
        Bundle argument = FuliSignInDialogFragment.newArgument(dialogDataModel);
        FuliSignInDialogFragment fragment = new FuliSignInDialogFragment();
        fragment.updateAdFeedAdBottom(ttFeedAd);
        fragment.setJssdkFuliRewardCallback(callback);
        fragment.setArguments(argument);
        return fragment;
    }

    @Override
    public BaseDialogFragment newH5PayDialog(String info, double price, double difference, JSPayModule.IPayInH5 iPayInH5) {
        return null;
    }

    @Override
    public BaseFullScreenDialogFragment newStageRedPacketDialogFragment(int position) {
        EarnByListenDialogFragment fragment = new EarnByListenDialogFragment(position);
        return fragment;
    }

    @Override
    public BaseFullScreenDialogFragment readStageRedPacketDialogFragment() {
        ReadStageRedPacketDialogFragment fragment = new ReadStageRedPacketDialogFragment();
        return fragment;
    }

    @Override
    public BaseDialogFragment newSuperMultiplyDialogFragment(
            FuliBallDialogDataModel dialogDataModel,
            BaseDialogFragment.OnDialogConfirmListener dialogConfirmListener,
            BaseDialogFragment.OnDialogCloseListener dialogCloseListener) {
        SuperMultiplyDialogFragment fragment = SuperMultiplyDialogFragment.newInstance(dialogDataModel);
        fragment.setDialogConfirmListener(dialogConfirmListener);
        fragment.setDialogCloseListener(dialogCloseListener);
        return fragment;
    }

    @Override
    public BaseDialogFragment newMultiplyDialogFragment(FuliBallDialogDataModel dialogDataModel,
                                                        BaseDialogFragment.OnDialogConfirmListener dialogConfirmListener,
                                                        BaseDialogFragment.OnDialogCloseListener dialogCloseListener) {

        NewSuperMultiplyDialogFragment fragment = NewSuperMultiplyDialogFragment.newInstance(dialogDataModel);
        fragment.setDialogConfirmListener(dialogConfirmListener);
        fragment.setDialogCloseListener(dialogCloseListener);
        return fragment;
    }

    @Override
    public BaseDialogFragment newSuperMultiplyCompleteDialogFragment(FuliBallDialogDataModel dataModel,
                                                                     AdWrapper ttFeedAd) {
        SuperMultiplyCompleteDialogFragment fragment = SuperMultiplyCompleteDialogFragment.newInstance(dataModel);
        fragment.setAdWrapper(ttFeedAd);
        return fragment;
    }

    @Override
    public BaseDialogFragment newBigRedPacketDialogFragment(
            FuliBallDialogDataModel dataModel,
            BaseDialogFragment.OnDialogConfirmListener dialogConfirmListener,
            BaseDialogFragment.OnDialogCloseListener dialogCloseListener) {
        BigRedPacketDialogFragment fragment = BigRedPacketDialogFragment.newInstance(dataModel);
        fragment.setOnDialogConfirmListener(dialogConfirmListener);
        fragment.setOnDialogCloseListener(dialogCloseListener);
        return fragment;
    }

    @Override
    public Fragment newOneKeyRadioPlayFragment(long channelId, int channelType) {
        return OneKeyRadioPlayFragment.newInstance(channelId, channelType);
        //return NewUserQuickListenFragment.newInstance(channelId, channelType);
    }

    @Override
    public BaseDialogFragment newFreeAlbumDownloadListDialog(
            BaseDialogFragment.OnDialogConfirmListener dialogConfirmListener,
            BaseDialogFragment.OnDialogCloseListener dialogCloseListener) {

        FreeAlbumDownloadLimitDialog dialogFragment = FreeAlbumDownloadLimitDialog.newInstance();
        dialogFragment.setOnDialogConfirmListener(dialogConfirmListener);
        dialogFragment.setOnDialogCloseListener(dialogCloseListener);
        return dialogFragment;
    }

    @Override
    public BaseFragment newPlayHistoryFragment() {
        AllHistoryFragment fragment = AllHistoryFragment.newInstance(true, false, true);
        return fragment;
    }

    @Override
    public BaseFragment newRechargeDiamondFragment(int fragmentId, double difference) throws BundleException {
        try {
            ILiveFragmentAction action = LiveActionRouter.getInstance().getFragmentAction();
            if (action instanceof ILiveFragmentAction) {
                return ((ILiveFragmentAction) action).newRechargeDiamondFragment(fragmentId, difference);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public BaseDialogFragment newBottomInterestDialogFragment(InterestCardSetting setting, NewInterestCardResp resp) {
        CustomizeBottomStyleDialog dialog = CustomizeBottomStyleDialog.newInstance(setting);
        dialog.setData(resp);
        return dialog;
    }

    @Override
    public boolean isInChildProtectFragmentFlow(Fragment fragment) {
        if (fragment != null) {
            //增加未成年人保护相关页面的话，需要在此处增加判断
            return fragment instanceof ChildProtectionPassWordFragment ||
                    fragment instanceof ChildProtectionSettingFragment;
        }
        return false;
    }

    @Override
    public BaseFragment2 newChildProtectionSettingFragment() {
        return ChildProtectionSettingFragment.newInstance();
    }

    @Override
    public XmBaseDialog newChildProtectGuideDialog(Activity activity) {
        ChildProtectGuideDialog dialog = new ChildProtectGuideDialog(activity);
        return dialog;
    }

    @Override
    public XmBaseDialog newChildProtectHintDialog(Activity activity, ChildProtectInfo childProtectInfo) {
        ChildProtectHintDialog dialog = new ChildProtectHintDialog(activity);
        dialog.setChildProtectInfo(childProtectInfo);
        return dialog;
    }

    @Override
    public XmBaseDialog newMarketGuideThemeV1DialogForCrashSuccess(Activity activity) {
        MarketGuideThemeV1Dialog dialog = new MarketGuideThemeV1Dialog(activity);
        dialog.setFrom(MarketGuideThemeV1Dialog.FROM_CRASH_SUCCESS);
        return dialog;
    }

    @Override
    public XmBaseDialog newMarketGuideThemeV1DialogForSubscribeSuccess(Activity activity) {
        MarketGuideThemeV1Dialog dialog = new MarketGuideThemeV1Dialog(activity);
        dialog.setFrom(MarketGuideThemeV1Dialog.FROM_SUBSCRIBE_SUCCESS);
        return dialog;
    }

    @Override
    public XmBaseDialog newMarketGuideThemeV2Dialog(Activity activity) {
        MarketGuideThemeV2Dialog dialog = new MarketGuideThemeV2Dialog(activity);
        return dialog;
    }

    @Override
    public BaseFragment2 newSettingFragment() {
        return new SettingFragment();
    }

    @Override
    public BaseFragment newReportFragmentByLiveId(long liveId, long targetUid) {
        try {
            ILiveFragmentAction action = LiveActionRouter.getInstance().getFragmentAction();
            return action.newReportFragmentByLiveId(liveId, targetUid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public BaseFragment newReportFragmentByLiveId(long liveId, long targetUid, String content, int mediaType) {
        try {
            ILiveFragmentAction action = LiveActionRouter.getInstance().getFragmentAction();
            return action.newReportFragmentByLiveId(liveId, targetUid, content, mediaType);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Override
    public BaseFragment newReportFragmentByVideoLive(long liveId, long targetUid) {
        try {
            ILiveFragmentAction action = Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFragmentAction();
            return action.newReportFragmentByVideoLive(liveId, targetUid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public BaseFragment2 newPushSettingFragment() {
        return null;
    }

    @Override
    public boolean switchChildTabInFindingFragment(Fragment fragment, String tabType) {
        return false;
    }


    @Override
    public BaseFragment newReportFragmentByPGCUid(long pgcRoomId, long targetUid) {
        try {
            ILiveFragmentAction action = Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFragmentAction();
            return action.newReportFragmentByPGCUid(pgcRoomId, targetUid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public BaseFragment newReportFragmentByUGCUid(long ugcRoomId, long ugcRecordId, long targetUid) {
        try {
            ILiveFragmentAction action = Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFragmentAction();
            if (action instanceof ILiveAppFragmentAction) {
                return ((ILiveAppFragmentAction) action).newReportFragmentByUGCUid(ugcRoomId, ugcRecordId, targetUid);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public BaseFragment newPlayletDetailFragment(long trackId, long albumId, int episodeNum) {
        return PlayletDetailFragment.newInstance(trackId, albumId, episodeNum <= 1);
    }

    @Override
    public BaseFragment2 newLoveNovelTabFragment(int pageId) {
        return LoveNovelTabFragment.newInstance(pageId);
    }

    @Override
    public BaseFragment2 newVipAlbumContentListFragment(String title, int poolId) {
        return VipAlbumContentListFragment.newInstance(title, poolId);
    }

    @Override
    public BaseFragment2 newVipAlbumContentListFragment(String title, int poolId, String schema) {
        return VipAlbumContentListFragment.newInstance(title, poolId, schema);
    }

    @Override
    public BaseFragment2 jumpAlbumUnlockRecordFragment() {
        return UnLockRecordListFragment.newInstance();
    }

    @Override
    public BaseFragment jumpAlbumFragment(String title, long albumId) {
        LiteAlbumFragment fragment = LiteAlbumFragment.newInstance(title, albumId,
                AlbumEventManage.FROM_ASSIST_SHARE_PAGE, ConstantsOpenSdk.PLAY_ASSIST_SHARE_PAGE);
        return fragment;
    }

    @Override
    public BaseFragment getAccountFragment() {
        return new AccountSettingFragment();
    }

    @Override
    public BaseFullScreenDialogFragment newUserRewardGiftGuideDialogFragment(NewUserRewardGiftList newUserRewardGiftList,boolean isCanUseCache) {
        OptimizedLoginNewUserRewardDialogFragment fragment = new OptimizedLoginNewUserRewardDialogFragment();
        Bundle bundle = OptimizedLoginNewUserRewardDialogFragment.newArgument(newUserRewardGiftList,isCanUseCache);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public BaseFragment getShortPlayDetailFragment(NewShortPlayFragmentModel model) {
        ShortPlayDetailFragment fragment = new ShortPlayDetailFragment();
        fragment.setVideoModel(model);
        return fragment;
    }

    @Override
    public BaseFragment getXmShortPlayDetailFragment(XmDuanJuItemTransferModel model) {
        return XmShortPlayDetailFragment.newInstance(model);
    }

    @Override
    public BaseFragment2 newRNFragment(boolean isDebug, Bundle data) {
        BaseFragment2 rnFragment;
        if (isDebug) {
            rnFragment = new ReactDebugFragment();
        } else {
            String bundleName = data == null ? null : data.getString("bundle");
            if (TextUtils.isEmpty(bundleName)) {
                return null;
            }
            DebugBundleManager.getInstance().setDebugBundleFromParams(data);
            XMReactNativeApi.preloadReactInstance(bundleName);
            rnFragment = new ReactFragment();
        }
        rnFragment.setArguments(data);
        return rnFragment;
    }
}
