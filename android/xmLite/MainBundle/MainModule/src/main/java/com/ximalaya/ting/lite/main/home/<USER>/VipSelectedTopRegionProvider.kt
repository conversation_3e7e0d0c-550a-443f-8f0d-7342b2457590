package com.ximalaya.ting.lite.main.home.adapter

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.constants.LoginByConstants
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder
import com.ximalaya.ting.lite.main.model.vip.VipInfoModel
import com.ximalaya.ting.lite.main.model.vip.VipTabModel
import kotlinx.android.synthetic.main.main_item_vip_top_region.view.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by dumingwei on 2020/5/7.
 *
 * Desc: vip精选页面顶部的item
 */
class VipSelectedTopRegionProvider @JvmOverloads constructor(
        val fragment: BaseFragment2,
        val vipTabModel: VipTabModel? = null
) : IMulitViewTypeViewAndData<VipSelectedTopRegionProvider.Holder, VipInfoModel> {

    private val TAG: String? = "VIPTopRegionProvider"

    private val mContext: Context? = fragment.context

    private val dateFormat = SimpleDateFormat("yyyy.MM.dd", Locale.getDefault())

    companion object {
        //一天的毫秒数
        //val daySeconds: Long = 24 * 3600L
        const val daySeconds: Long = 86400000L
    }

    //颜色格式6位或者8位
    private val colorRegex = "^#([0-9a-fA-F]{6}|[0-9a-fA-F]{8})$"

    //vip权益
    private val benefitList = arrayListOf<VipInfoModel.Entrance>()

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_vip_top_region, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView)
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<VipInfoModel>, convertView: View, position: Int) {
        Logger.d(TAG, "bindViewDatas")
        val model = t.getObject()
        if (model is VipInfoModel) {

            with(holder.rootView) {
                //处理底部颜色
                vipTabModel?.backColor?.let {
                    if (it.matches(colorRegex.toRegex())) {
                        lgvTopVipRegion.setSameColor(Color.parseColor(it))
                    }
                }

                ImageManager.from(mContext).displayImage(ivAvatar, model.logoPic, R.drawable.main_profile_img_userheah)

                if (UserInfoMannage.hasLogined()) {

                    tvMainTitle.text = model.nickName

                    when (model.vipInfo?.vipStatus) {
                        VipInfoModel.VipInfo.NEVER_BE -> {//不是会员
                            rlVipBg.background = resources.getDrawable(R.drawable.main_vip_top_region_invalid)
                            ivVipTag.visibility = View.GONE
                            tvExpireDays.visibility = View.GONE

                            tvSubTitle.setTextColor(resources.getColor(R.color.main_vip_subtitle_color))
                            tvSubTitle.text = model.guideText
                            tvOpenVipOrRenew.text = fragment.getString(R.string.main_open_vip)
                        }

                        VipInfoModel.VipInfo.IS_VIP -> {
                            ivVipTag.visibility = View.VISIBLE
                            ivVipTag.background = resources.getDrawable(R.drawable.host_ic_vip)
                            rlVipBg.background = resources.getDrawable(R.drawable.main_vip_top_region)

                            tvSubTitle.setTextColor(resources.getColor(R.color.main_vip_login_subtitle_color))
                            tvOpenVipOrRenew.text = resources.getString(R.string.main_renewal_vip)
                            val expireDateTimeStamp = model.vipInfo?.expireDate ?: 0
                            if (expireDateTimeStamp <= 0) {
                                tvSubTitle.text = model.guideText
                            } else {
                                //vip生效状态，剩余天数小于等于7天的时候，要显示剩余天数
                                tvExpireDays.visibility = View.VISIBLE
                                val date = dateFormat.format(Date(expireDateTimeStamp))
                                tvSubTitle.text = resources.getString(R.string.main_vip_date_format, date)
                                //当前时间戳
                                val currentTimeStamp = System.currentTimeMillis()
                                val leftDays = (expireDateTimeStamp - currentTimeStamp) / daySeconds
                                when (leftDays) {
                                    0L -> {
                                        tvExpireDays.text = resources.getString(R.string.main_vip_today_expire)
                                    }
                                    in 1..7 -> {
                                        tvExpireDays.text = resources.getString(R.string.main_vip_left_date_format, leftDays)
                                    }
                                    else -> {
                                        tvExpireDays.text = ""
                                        tvExpireDays.visibility = View.GONE
                                    }
                                }
                            }
                        }
                        VipInfoModel.VipInfo.EXPIRED -> {
                            ivVipTag.visibility = View.VISIBLE
                            ivVipTag.background = resources.getDrawable(R.drawable.main_ic_vip_expired)
                            rlVipBg.background = resources.getDrawable(R.drawable.main_vip_top_region_invalid)
                            tvSubTitle.setTextColor(resources.getColor(R.color.main_vip_subtitle_color))
                            tvSubTitle.text = model.guideText
                            tvOpenVipOrRenew.text = resources.getString(R.string.main_open_vip)

                            val expireDays = model.vipInfo?.expireDays ?: 0
                            if (expireDays > 15) {
                                tvExpireDays.text = ""
                                tvExpireDays.visibility = View.GONE
                            } else {
                                tvExpireDays.visibility = View.VISIBLE
                                tvExpireDays.text = fragment.getString(R.string.main_vip_expire_format, expireDays)
                            }
                        }
                    }
                } else {
                    rlVipBg.background = resources.getDrawable(R.drawable.main_vip_top_region_invalid)
                    ivVipTag.visibility = View.GONE
                    tvExpireDays.visibility = View.GONE
                    tvSubTitle.setTextColor(resources.getColor(R.color.main_vip_subtitle_color))

                    tvMainTitle.text = fragment.getString(R.string.main_login_right_now)
                    tvSubTitle.text = model.guideText
                    tvOpenVipOrRenew.text = fragment.getString(R.string.main_open_vip)
                }

                rvVipBenefits.setDisallowInterceptTouchEventView(rvVipBenefits.parent as ViewGroup)
                initAdapter(rvVipBenefits, model.vipInfo?.vipStatus)

                model.entrances?.let {
                    if (rvVipBenefits.adapter != null) {
                        benefitList.clear()
                        benefitList.addAll(it)
                        rvVipBenefits.adapter!!.notifyDataSetChanged()
                    }
                }

                ivAvatar.setOnClickListener {
                    if (!UserInfoMannage.hasLogined()) {
                        UserInfoMannage.gotoLogin(mContext, LoginByConstants.LOGIN_BY_DEFUALT)
                        return@setOnClickListener
                    }
                    model.buttonUrl?.let { url ->
                        //跳转到开通vip的地方
                        val bundle = Bundle()
                        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url)
                        fragment.startFragment(NativeHybridFragment::class.java, bundle)
                    }
                }
                tvMainTitle.setOnClickListener {
                    dealAvatarOrMainTitleClick(model)
                }

                tvOpenVipOrRenew.setOnClickListener {
                    //跳商品页
                    model.buttonUrl?.let { url ->
                        //跳转到开通vip的地方
                        val bundle = Bundle()
                        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url)
                        fragment.startFragment(NativeHybridFragment::class.java, bundle)
                    }
                }
            }
        }
    }

    private fun dealAvatarOrMainTitleClick(model: VipInfoModel) {
        if (UserInfoMannage.hasLogined()) {
            //跳商品页
            model.buttonUrl?.let { url ->
                //跳转到开通vip的地方
                val bundle = Bundle()
                bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url)
                fragment.startFragment(NativeHybridFragment::class.java, bundle)
            }
        } else {
            UserInfoMannage.gotoLogin(mContext, LoginByConstants.LOGIN_BY_DEFUALT)
        }
    }

    private fun initAdapter(rv: RecyclerView, vipStatus: Int?) {
        if (rv.adapter != null) {
            return
        }
        rv.layoutManager = LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false)
        rv.adapter = object : MultiRecyclerAdapter<VipInfoModel.Entrance, SuperRecyclerHolder>(mContext, benefitList) {

            override fun createMultiViewHolder(mCtx: Context?, itemView: View, viewType: Int): SuperRecyclerHolder {
                return SuperRecyclerHolder.createViewHolder(mCtx, itemView)
            }

            override fun onBindMultiViewHolder(holder: SuperRecyclerHolder, t: VipInfoModel.Entrance, viewType: Int, position: Int) {
                if (t.flag == 1) {
                    holder.setVisibility(R.id.ivNewTag, View.VISIBLE)
                } else {
                    holder.setVisibility(R.id.ivNewTag, View.INVISIBLE)
                }

                if (UserInfoMannage.hasLogined()) {
                    when (vipStatus) {
                        VipInfoModel.VipInfo.NEVER_BE, VipInfoModel.VipInfo.EXPIRED -> {
                            holder.setTextColorResource(R.id.tvBenefitTitle, R.color.main_vip_login_benefit_title_color)
                            holder.setTextColorResource(R.id.tvBenefitSubtitle, R.color.main_vip_login_benefit_subtitle_color)
                        }
                        VipInfoModel.VipInfo.IS_VIP -> {
                            holder.setTextColorResource(R.id.tvBenefitTitle, R.color.main_vip_benefit_title_color)
                            holder.setTextColorResource(R.id.tvBenefitSubtitle, R.color.main_vip_benefit_subtitle_color)
                        }
                    }
                } else {
                    holder.setTextColorResource(R.id.tvBenefitTitle, R.color.main_vip_login_benefit_title_color)
                    holder.setTextColorResource(R.id.tvBenefitSubtitle, R.color.main_vip_login_benefit_subtitle_color)
                }
                holder.setText(R.id.tvBenefitTitle, t.title)
                holder.setText(R.id.tvBenefitSubtitle, t.subTitle)

                if (position == benefitList.size - 1) {
                    holder.setVisibility(R.id.spaceEnd, View.VISIBLE)
                } else {
                    holder.setVisibility(R.id.spaceEnd, View.GONE)
                }
                holder.setOnClickListenner(R.id.cardViewBenefit, object : View.OnClickListener {
                    override fun onClick(v: View?) {
                        t.url?.let {
                            val bundle = Bundle()
                            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, it)
                            fragment.startFragment(NativeHybridFragment::class.java, bundle)
                        }
                    }
                })
            }

            override fun getMultiItemViewType(model: VipInfoModel.Entrance?, position: Int): Int {
                return 0
            }

            override fun getMultiItemLayoutId(viewType: Int): Int {
                return R.layout.main_item_vip_benefit
            }
        }
    }

    class Holder(var rootView: View) : BaseViewHolder()

}