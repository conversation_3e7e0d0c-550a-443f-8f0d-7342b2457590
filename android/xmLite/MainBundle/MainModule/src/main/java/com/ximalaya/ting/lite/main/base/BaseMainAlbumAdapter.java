package com.ximalaya.ting.lite.main.base;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.adapter.album.IAlbumAdapter;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.album.Album;

import java.util.List;

/**
 * Created by easoll on 17/6/24.
 *
 * <AUTHOR>
 */

public abstract class BaseMainAlbumAdapter extends HolderAdapter<Album> implements IAlbumAdapter {
    public BaseMainAlbumAdapter(MainActivity activity, List<Album> listData) {
        super(activity, listData);
    }


    @Override
    public void startAlbumFragment(final Album album) {
    }

    @Override
    public void startFragment(Fragment fra) {
        if (context instanceof MainActivity) {
            ((MainActivity) context).startFragment(fra);
        }
    }


    @Override
    public void bindViewDatas(BaseViewHolder holder, Album t, int position) {
        if (t == null)
            return;

        BaseMainAlbumHolder viewHolder = (BaseMainAlbumHolder) holder;

        Spanned richTitle = getRichTitle(t);
        if (!TextUtils.isEmpty(richTitle)) {
            viewHolder.title.setText(richTitle);
        } else {
            viewHolder.title.setText(t.getAlbumTitle());
        }

        ImageManager.from(context).displayImage(viewHolder.cover, t.getValidCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145);
    }

    @Override
    public void onClick(View view, Album album, int position, BaseViewHolder holder) {

    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        BaseViewHolder holder;
        Album album = (Album) getItem(position);
        if (convertView == null) {
            int layoutId = getConvertViewIdByPosition(position);
            convertView = layoutInflater.inflate(layoutId, parent, false);
            holder = buildHolderByPosition(convertView, position);
            convertView.setTag(holder);
        } else {
            holder = (BaseViewHolder) convertView.getTag();
        }
        if (position < getCount()) {
            bindViewDatas(holder, album, position);
        } else {
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException(getClass().getName() + " error:getView listData:" + listData + "position:" + position);
            }
        }
        return convertView;
    }


    @Override
    public final int getConvertViewId() {
        throw new UnsupportedOperationException("请使用getCovertViewIdByPosition(int)");
    }

    // 如果要覆盖请使用  getOtherViewTypeCount
    @Override
    public final int getViewTypeCount() {
        // 这里+1 表示是广告的类型
        return getOtherViewTypeCount() + 1;
    }

    /**
     * 如果要覆盖请使用  getOtherViewType
     *
     * @param position item view 的位置
     * @return item view 的类型
     */
    @Override
    public final int getItemViewType(int position) {
        Object item = getItem(position);
        int otherViewType = getOtherViewType(position, item);
        if (otherViewType == 0) {
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException("getOtherViewType 返回值必须从1 开始");
            }
        }
        return otherViewType;
    }

    protected int getOtherViewType(int position, Object object) {
        return 1;
    }

    protected int getOtherViewTypeCount() {
        return 1;
    }

    @Override
    public final BaseViewHolder buildHolder(View convertView) {
        throw new UnsupportedOperationException("请使用buildHolderByPosition(View, int)");
    }

    /**
     * 产品无特殊需求可直接使用该方法获取专辑的副标题
     */
    protected String getDefaultSubTitle(@Nullable Album album) {
        if (album == null) {
            return "";
        }
        String subTitle = album.getAlbumIntro();
        if (album instanceof AlbumM) {
            AlbumM albumM = (AlbumM) album;

            if (TextUtils.isEmpty(subTitle)) {
                subTitle = albumM.getSubTitle();
            }

            if (TextUtils.isEmpty(subTitle))
                subTitle = albumM.getTracks() != null && albumM.getTracks().get(0) != null
                        ? albumM.getTracks().get(0).getTrackTitle() : "";

        }

        return subTitle;
    }

    abstract public int getConvertViewIdByPosition(int position);

    abstract public BaseViewHolder buildHolderByPosition(View convertView, int position);

    public static class BaseMainAlbumHolder extends BaseViewHolder {
        public View root;  //专辑条根布局
        public View border;  //专辑条分割线
        public ImageView cover;  //专辑条封面
        public TextView title;   //专辑条标题
        public TextView adFlag1;
        public TextView adFlag2;
        public ImageView vActivity123Image;

        public BaseMainAlbumHolder(View convertView) {
            root = convertView;
            cover = (ImageView) convertView.findViewById(R.id.main_iv_album_cover);
            border = convertView.findViewById(R.id.main_album_border);
            title = (TextView) convertView.findViewById(R.id.main_tv_album_title);
            adFlag1 = (TextView) convertView.findViewById(R.id.main_ad_tag_iv_1);
            adFlag2 = (TextView) convertView.findViewById(R.id.main_ad_tag_iv_2);
            vActivity123Image = convertView.findViewById(R.id.main_album_activity_123_2018);
        }
    }

    protected @Nullable
    Spanned getRichTitle(@Nullable Album data) {
        if (data == null || !(data instanceof AlbumM))
            return null;

        AlbumM album = (AlbumM) data;
        StringBuilder result = new StringBuilder();

        boolean isCompleteTag = album.getSerialState() == 2 || album.isCompleted() ||
                (album.getAttentionModel() != null && album.getAttentionModel().getSerialState() == 2);
        if (isCompleteTag) {
            // 完本标签
            result.append("<img src=\"").append(R.drawable.main_tag_complete_top_new).append("\">  ");
        }

        if (TextUtils.isEmpty(result))
            return null;

        result.append(album.getAlbumTitle());
        return Html.fromHtml(result.toString(), ToolUtil.getImageGetter(context), null);
    }

    // 广告holder
    public static class BaseMainAdHolder extends BaseViewHolder {
        public View root;  //专辑条根布局
        public View border;  //专辑条分割线
        public ImageView cover;  //专辑条封面
        public TextView title;   //专辑条标题
        public TextView subtitle;
        public TextView adFlag1;
        public TextView adFlag2;

        public BaseMainAdHolder(View convertView) {
            root = convertView;
            cover = (ImageView) convertView.findViewById(R.id.main_iv_album_cover);
            border = convertView.findViewById(R.id.main_album_border);
            title = (TextView) convertView.findViewById(R.id.main_tv_album_title);
            subtitle = (TextView) convertView.findViewById(R.id.main_tv_album_subtitle);
            adFlag1 = (TextView) convertView.findViewById(R.id.main_ad_tag_iv_1);
            adFlag2 = (TextView) convertView.findViewById(R.id.main_ad_tag_iv_2);
        }
    }
}
