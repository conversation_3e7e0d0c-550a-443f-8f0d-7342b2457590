package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;
import com.ximalaya.ting.lite.main.view.LinearItemDecoration;
import com.ximalaya.ting.lite.main.view.RecyclerViewCanDisallowIntercept;

import java.util.List;

/**
 * Created by WolfXu on 2019/9/17.
 * 实时推荐强露出模块
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class HomeRecommendRealTimeRecommendStrongModuleAdapterProvider implements IMulitViewTypeViewAndData {

    BaseFragment2 mFragment;
    Activity mActivity;

    public HomeRecommendRealTimeRecommendStrongModuleAdapterProvider(BaseFragment2 fragment) {
        mFragment = fragment;
        if (mFragment != null) {
            mActivity = mFragment.getActivity();
        }
        if (mActivity == null) {
            mActivity = BaseApplication.getOptActivity();
        }
    }

    @Override
    public void bindViewDatas(HolderAdapter.BaseViewHolder h, ItemModel t, View convertView, int position) {
        if ((!(h instanceof RealTimeRecommendViewHolder)) || t == null || t.getObject() == null) {
            return;
        }
        RealTimeRecommendViewHolder holder = (RealTimeRecommendViewHolder) h;
        if (t.getObject() instanceof RecommendItemNew) {
            RecommendItemNew recommendItemNew = (RecommendItemNew) t.getObject();
            List<RecommendItemNew> recommendItemNews = (List<RecommendItemNew>) recommendItemNew.getItem();
            if(null != recommendItemNews) {
                if (RecommendItemNew.RECOMMEND_TYPE_LOCAL_REAL_TIME_RECOMMEND.equals(recommendItemNew.getItemType())) {
                    holder.adapter.setData(recommendItemNews);
                    holder.adapter.notifyDataSetChanged();
                }
            }
        }
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_recommend_real_time_recommend_strong_module, parent, false);
    }

    @Override
    public HolderAdapter.BaseViewHolder buildHolder(View convertView) {
        RealTimeRecommendViewHolder holder = new RealTimeRecommendViewHolder(convertView);
        initRv(holder);
        return holder;
    }

    private void initRv(RealTimeRecommendViewHolder holder) {
        holder.rvItems.setLayoutManager(new LinearLayoutManager(mActivity
                , LinearLayoutManager.HORIZONTAL, false));
        holder.adapter = new HomeRecommendRealTimeRecommendModuleItemAdapter(mFragment);
        holder.rvItems.setAdapter(holder.adapter);
        int spacing = BaseUtil.dp2px(mActivity, 10);
        int margin = BaseUtil.dp2px(mActivity, 14);
        holder.rvItems.addItemDecoration(new LinearItemDecoration(spacing, margin));
        if (mFragment != null) {
            holder.rvItems.setDisallowInterceptTouchEventView((ViewGroup) mFragment.getView());
        }
    }

    private static class RealTimeRecommendViewHolder extends HolderAdapter.BaseViewHolder {

        private RecyclerViewCanDisallowIntercept rvItems;
        private HomeRecommendRealTimeRecommendModuleItemAdapter adapter;

        RealTimeRecommendViewHolder(View convertView) {
            rvItems = convertView.findViewById(R.id.main_rv_items);
        }
    }
}
