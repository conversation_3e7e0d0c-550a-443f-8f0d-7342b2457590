package com.ximalaya.ting.lite.main.home.adapter;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;

import com.bytedance.sdk.openadsdk.TTFeedAd;
import com.qq.e.ads.nativ.NativeUnifiedADData;
import com.qq.e.ads.nativ.widget.NativeAdContainer;
import com.qq.e.comm.constants.AdPatternType;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.adsdk.callback.IFeedVideoAdCallBack;
import com.ximalaya.ting.android.host.adsdk.platform.common.modelproxy.AbstractThirdAd;
import com.ximalaya.ting.android.host.adsdk.platform.csj.manager.CsjAdSdkManager;
import com.ximalaya.ting.android.host.adsdk.platform.csj.modelproxy.CsjNativeThirdAd;
import com.ximalaya.ting.android.host.adsdk.platform.gdt.modelproxy.GdtThirdNativeAd;
import com.ximalaya.ting.android.host.adsdk.platform.gdt.view.GdtMediaViewContainer;
import com.ximalaya.ting.android.host.adsdk.provider.CommonNativeDaTuAdProvider;
import com.ximalaya.ting.android.host.adsdk.provider.callback.OnCommonDaTuAdBindCallBack;
import com.ximalaya.ting.android.host.adsdk.provider.viewmodel.SimpleDaTuViewModel;
import com.ximalaya.ting.android.host.adsdk.provider.viewmodel.XMVideoParamModel;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.play.manager.PlayForwardVideoAdReportManger;
import com.ximalaya.ting.lite.main.manager.CoinStyleManager;
import com.ximalaya.ting.lite.main.play.manager.PlayForwardVideoAdReportManger;
import com.ximalaya.ting.lite.main.playnew.manager.TrackPlayTopAdUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by dumingwei on 2022/3/1
 * <p>
 * Desc: 首页大图广告或者视频广告，参考：MineNativeDaTuAdProvider
 */
public class LiteNativeDaTuAdProvider {

    private static final String TAG = "LiteNativeDaTuAdProvide";

    protected Context mContext;
    protected Holder mAdLayoutHolder;

    private final CommonNativeDaTuAdProvider mDatuAdProvider;
    private final FrameLayout mAdContainer;

    private IHiddenAdCallback mIHiddenAdCallback;


    //播放视频前贴播放完成上报管理
    private PlayForwardVideoAdReportManger mPlayForwardVideoAdReportManger;


    public LiteNativeDaTuAdProvider(Context context, FrameLayout adContainer) {
        mContext = context;
        mDatuAdProvider = new CommonNativeDaTuAdProvider(context);
        mAdContainer = adContainer;
    }

    public void setIHiddenAdCallback(IHiddenAdCallback mIHiddenAdCallback) {
        this.mIHiddenAdCallback = mIHiddenAdCallback;
    }

    /******************生命周期方法，广点通广告使用*******************************/

    public void onMyResume() {
        if (mDatuAdProvider != null) {
            mDatuAdProvider.onMyResume();
        }
    }


    public void onDestroy() {
        //隐藏广告，停止定时器
        hideAd();
        if (mDatuAdProvider != null) {
            mDatuAdProvider.onDestroy();
        }
    }


    /******************生命周期方法，广点通广告使用*******************************/

    /**
     * 播放页和我页单独拆分出来
     */
    public boolean bindViewDatas(AbstractThirdAd<?> thirdAd, final String positionName) {
        resetPlayForwardVideoAdReportManger();
        if (mAdContainer == null) {
            return false;
        }
        //不存在广告数据
        if (thirdAd.getAdData() == null) {
            return false;
        }
        if (mAdLayoutHolder == null) {
            mAdLayoutHolder = new Holder(mAdContainer);
        }

        mAdContainer.removeAllViews();
        mAdContainer.addView(mAdLayoutHolder.convertView);

        //处理自渲染通用操作
        //图片的宽度
        int imageWith = BaseUtil.getScreenWidth(mContext) - BaseUtil.dp2px(mContext, 12 * 2);
        //设置点击的view
        List<View> clickList = new ArrayList<>();
        clickList.add(mAdLayoutHolder.layout);
        //创建广告绑定相关View
        SimpleDaTuViewModel simpleDaTuViewModel = new SimpleDaTuViewModel(imageWith, clickList, mAdLayoutHolder.cover);
        //设置通用标题
        simpleDaTuViewModel.descView = mAdLayoutHolder.title;
        //设置穿山甲相关
        simpleDaTuViewModel.adContentRootView = mAdLayoutHolder.convertView;
        simpleDaTuViewModel.adTagView = mAdLayoutHolder.adTag;
        simpleDaTuViewModel.csjVideoLayout = mAdLayoutHolder.csjAdVideoLayout;
        //设置广点通相关
        simpleDaTuViewModel.gdtNativeAdContainer = mAdLayoutHolder.nativeAdContainer;
        simpleDaTuViewModel.gdtVideoContainer = mAdLayoutHolder.gdtAdVideoLayout;

        //设置广点通tag logo的位置，设置高度为10，自适应宽度
        FrameLayout.LayoutParams gdtTagParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, BaseUtil.dp2px(mContext, 10));
        gdtTagParams.gravity = Gravity.END | Gravity.BOTTOM;
        gdtTagParams.rightMargin = BaseUtil.dp2px(mContext, 17);
        gdtTagParams.bottomMargin = BaseUtil.dp2px(mContext, 72);
        simpleDaTuViewModel.gtdAdTagLayoutParams = gdtTagParams;

        //喜马视频相关
        if (AdManager.isXmHomePageVideo(thirdAd.getAdvertis())) {
            simpleDaTuViewModel.descView = null;
            simpleDaTuViewModel.titleView = mAdLayoutHolder.title;
            XMVideoParamModel xmVideoParamModel = new XMVideoParamModel(mAdLayoutHolder.xmAdVideoLayout, true);
            //xmVideoParamModel.isOnlyRelayShowToPlay() = true;
            xmVideoParamModel.setOnlyRelayShowToPlay(true);
            xmVideoParamModel.setListenScrollAndCheckViewState(true);
            simpleDaTuViewModel.xmVideoParamModel = xmVideoParamModel;
        }

        if (isShowForwardSdkVideoAd(thirdAd)) {
            Logger.i(TAG, "isShowForwardSdkVideoAd == true");

            mPlayForwardVideoAdReportManger = new PlayForwardVideoAdReportManger(thirdAd);

            simpleDaTuViewModel.feedVideoAdCallBack = new IFeedVideoAdCallBack() {

                @Override
                public void onFeedVideoStart() {
                    Logger.i(TAG, "onFeedVideoStart");

                }

                @Override
                public void onFeedVideoAdError() {
                    Logger.i(TAG, "onFeedVideoAdError");
                    if (mPlayForwardVideoAdReportManger != null) {
                        //非关闭按钮关闭，发生错误，上报完成
                        mPlayForwardVideoAdReportManger.adPlayCompleteForPlayPageForwardVideoAd(false, positionName);
                    }
                }

                @Override
                public void onSurfaceDestroyed(int what) {
                    Logger.i(TAG, "onSurfaceDestroyed");
                    if (mPlayForwardVideoAdReportManger != null) {
                        //非关闭按钮关闭，发生错误，上报完成
                        mPlayForwardVideoAdReportManger.adPlayCompleteForPlayPageForwardVideoAd(false, positionName);
                    }
                }

                @Override
                public void onFeedVideoAdPaused() {
                    Logger.i(TAG, "onFeedVideoAdPaused");
                }

                @Override
                public void onFeedVideoAdComplete() {
                    Logger.i(TAG, "onFeedVideoAdComplete");
                    //设置已经播放完成
                    if (mPlayForwardVideoAdReportManger != null) {
                        //设置播放完成,必须在adPlayCompleteForPlayPageForwardVideoAd上报前进行设置
                        mPlayForwardVideoAdReportManger.setHasVideoCompleted();
                        //非关闭按钮关闭，播放完成，进行上报完成
                        mPlayForwardVideoAdReportManger.adPlayCompleteForPlayPageForwardVideoAd(false, positionName);
                    }
                }

                @Override
                public void onFeedVideoAdClick() {
                    Logger.i(TAG, "onFeedVideoAdClick");
                }

                @Override
                public void onCsjFeedVideoProgressUpdate(long current, long duration) {
                    Logger.i(TAG, "onCsjFeedVideoProgressUpdate");
                    //记录播放的时长
                    if (mPlayForwardVideoAdReportManger != null) {
                        mPlayForwardVideoAdReportManger.setCsjVideoPlayTime(current);
                    }
                }
            };

        }

        //绑定广告相关事件，埋点等
        boolean isBindSuccess = mDatuAdProvider.bindViewDatas(thirdAd, simpleDaTuViewModel, positionName, new OnCommonDaTuAdBindCallBack() {
            @Override
            public void onAdViewClick() {
                CoinStyleManager.INSTANCE.clickCoinStyle(thirdAd.getAdvertis());
            }
        });
        //绑定失败
        if (!isBindSuccess) {
            mAdLayoutHolder.convertView.setVisibility(View.GONE);
            return false;
        }
        if (CoinStyleManager.INSTANCE.showCoinStyle(mAdLayoutHolder.coinLayout, thirdAd.getAdvertis())) {
            mAdLayoutHolder.coinLayout.setVisibility(View.VISIBLE);
            mAdLayoutHolder.commonAdButtonLayout.setVisibility(View.GONE);
        } else {
            mAdLayoutHolder.coinLayout.setVisibility(View.GONE);
            mAdLayoutHolder.commonAdButtonLayout.setVisibility(View.VISIBLE);
            //设置按钮标题
            mAdLayoutHolder.commonAdButton.setText(TrackPlayTopAdUtils.getAdButtonTextWithLogic(thirdAd));
        }
        //广告绑定成功
        mAdLayoutHolder.layout.setBackground(null);
        //展示自渲染布局
        mAdLayoutHolder.nativeAdContainer.setVisibility(View.VISIBLE);
        //展示总广告item
        mAdLayoutHolder.convertView.setVisibility(View.VISIBLE);

        mAdLayoutHolder.commonIvClose.setOnClickListener((v) -> {
            hideAd();
            if (mIHiddenAdCallback != null) {
                mIHiddenAdCallback.onAdClose();
            }
        });

        return true;
    }

    public boolean isShowForwardSdkVideoAd(AbstractThirdAd thirdAd) {
        if (thirdAd == null || thirdAd.getAdvertis() == null || thirdAd.getAdData() == null) {
            return false;
        }
        //当前是穿山甲信息流广告
        if (thirdAd instanceof CsjNativeThirdAd) {
            TTFeedAd csjFeedAd = ((CsjNativeThirdAd) thirdAd).getAdData();
            //是穿山甲信息流视频
            if (CsjAdSdkManager.isCsjFeedVideoAd(csjFeedAd)) {
                return true;
            }
        }
        //当前是穿山甲信息流广告
        if (thirdAd instanceof GdtThirdNativeAd) {
            NativeUnifiedADData gdtFeedAd = ((GdtThirdNativeAd) thirdAd).getAdData();
            //是穿山甲信息流视频
            if (gdtFeedAd != null && gdtFeedAd.getAdPatternType() == AdPatternType.NATIVE_VIDEO) {
                return true;
            }
        }
        if (AdManager.isXmHomePageVideo(thirdAd.getAdvertis())) {
            return true;
        }
        return false;
    }


    public void hideAd() {
        if (mAdContainer == null) {
            return;
        }
        if (mAdContainer.getChildCount() > 0) {
            mAdContainer.removeAllViews();
        }
    }

    /**
     * 清除视频前插完播上报
     */
    public void resetPlayForwardVideoAdReportManger() {
        if (mPlayForwardVideoAdReportManger != null) {
            mPlayForwardVideoAdReportManger.reset();
            mPlayForwardVideoAdReportManger = null;
        }
    }

    public static class Holder extends HolderAdapter.BaseViewHolder {
        //广点通需要包装一层
        NativeAdContainer nativeAdContainer;
        GdtMediaViewContainer gdtAdVideoLayout;

        ImageView cover;
        TextView title;
        ViewGroup layout;
        ViewGroup csjAdVideoLayout;
        ViewGroup xmAdVideoLayout;
        ViewGroup convertView;
        TextView vipClick;
        ImageView adTag;

        ImageView commonIvClose;
        // 金币布局
        ViewGroup coinLayout;
        // 按钮父布局
        ViewGroup commonAdButtonLayout;
        // 按钮文案
        TextView commonAdButton;

        private Holder(FrameLayout adLayout) {
            this.convertView = (ViewGroup) LayoutInflater.from(adLayout.getContext()).inflate(R.layout.main_lite_native_ad_picture_or_video_item, adLayout, false);
            nativeAdContainer = convertView.findViewById(R.id.main_ad_native_container);
            gdtAdVideoLayout = convertView.findViewById(R.id.main_gdt_ad_video);
            layout = convertView.findViewById(R.id.main_ad_layout_content);
            cover = convertView.findViewById(R.id.main_iv_track_cover);
            title = convertView.findViewById(R.id.main_tv_title);
            csjAdVideoLayout = convertView.findViewById(R.id.main_ad_video);
            xmAdVideoLayout = convertView.findViewById(R.id.main_ad_xm_home_video);

            vipClick = convertView.findViewById(R.id.main_iv_vip_click);
            adTag = convertView.findViewById(R.id.main_iv_ad_tag);

            commonIvClose = convertView.findViewById(R.id.main_ad_top_home_iv_close);
            coinLayout = convertView.findViewById(R.id.main_rl_coin_layout);
            commonAdButtonLayout = convertView.findViewById(R.id.main_ad_ad_button_parent);
            commonAdButton = convertView.findViewById(R.id.main_ad_ad_button);
        }
    }

    public interface IHiddenAdCallback {
        void onAdClose();
    }
}