package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import android.graphics.Color;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;

/**
 * Created by dumingwei on 2020/5/12
 * <p>
 * Desc: vip热词下的专辑数据
 */
public class VipHotWordAlbumProvider implements IMulitViewTypeViewAndData<VipHotWordAlbumProvider.ViewHolder, AlbumM> {

    private Activity mActivity;
    protected BaseFragment2 mFragment;

    public VipHotWordAlbumProvider(BaseFragment2 baseFragment2) {
        mFragment = baseFragment2;
        mActivity = baseFragment2.getActivity();
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_vip_hot_word_album, null);
    }

    @Override
    public ViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(ViewHolder viewHolder, final ItemModel<AlbumM> t, View convertView, final int position) {
        if (t == null || t.object == null) {
            return;
        }
        final AlbumM albumItem = t.getObject();

        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, albumItem);
        //添加专辑Item的ContentDescription
        if (viewHolder.root != null) {
            if (!TextUtils.isEmpty(albumItem.getAlbumTitle())) {
                viewHolder.root.setContentDescription(albumItem.getAlbumTitle());
            } else {
                viewHolder.root.setContentDescription("");
            }
        }
        ImageManager.from(mActivity).displayImage(viewHolder.cover, albumItem.getLargeCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145, com.ximalaya.ting.android.host.R.drawable.host_default_album_145);

        if (AlbumTagUtil.getAlbumCoverTag(albumItem) != -1) {
            viewHolder.ivTag.setImageResource(AlbumTagUtil.getAlbumCoverTag(albumItem));
            viewHolder.ivTag.setVisibility(View.VISIBLE);
        } else {
            viewHolder.ivTag.setVisibility(View.INVISIBLE);
        }

        int textSize = (int) viewHolder.title.getTextSize();
        Spanned richTitle = AlbumAdapter.getRichTitle(albumItem, mActivity, textSize);
        viewHolder.title.setText(richTitle);

        String subTitle = albumItem.getIntro();
        if (!TextUtils.isEmpty(subTitle)) {
            viewHolder.subtitle.setText(Html.fromHtml(subTitle));
        } else {
            viewHolder.subtitle.setText("");
        }

        String playCountStr = StringUtil.getFriendlyNumStr(albumItem.getPlayCount()) + "播放";
        int drawable = R.drawable.main_ic_common_play_count;
        BaseAlbumAdapter.addAlbumInfo(mActivity, viewHolder.layoutAlbumInfo, drawable, playCountStr, Color.parseColor("#999999"), false, false);
        BaseAlbumAdapter.addAlbumInfo(mActivity, viewHolder.layoutAlbumInfo, R.drawable.main_ic_common_track_count, StringUtil.getFriendlyNumStr(albumItem.getIncludeTrackCount()) + " 集", Color.parseColor("#999999"));

        //设置item点击
        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //todo from应该传递哪个字段
                AlbumEventManage.startMatchAlbumFragment(albumItem, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0,
                        albumItem.getRecSrc(), albumItem.getRecTrack(), -1, mActivity);
            }
        });
    }

    public class ViewHolder extends BaseAlbumAdapter.ViewHolder {

        private ImageView ivTag;

        public ViewHolder(View convertView) {
            super(convertView);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
            cover = convertView.findViewById(R.id.main_iv_album_cover);
            border = convertView.findViewById(R.id.main_album_border);
            title = convertView.findViewById(R.id.main_tv_album_title);
            subtitle = convertView.findViewById(R.id.main_tv_album_subtitle);
            layoutAlbumInfo = convertView.findViewById(R.id.main_layout_album_info);
        }
    }
}
