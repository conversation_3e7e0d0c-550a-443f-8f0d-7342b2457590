package com.ximalaya.ting.lite.main.home.adapter

import android.annotation.SuppressLint
import android.graphics.Typeface
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.model.newhome.LiteChildTab

/**
 * Created by du<PERSON><PERSON> on 2021/6/16
 *
 * Desc: 子tab需要滑动的样式
 */
class LiteChildTabScrollStyleAdapter(
    val mFragment: BaseFragment2,
    var mDataList: List<LiteChildTab>? = null
) : AbRecyclerViewAdapter<RecyclerView.ViewHolder>() {

    private val TAG: String = "LiteChildTabScrollStyle"


    private var selectedColor: Int = 0
    private var unSelectedColor: Int = 0


    private var selectedTextSize: Float = 19.0f
    private var unSelectedTextSize: Float = 16.0f

    private var isTabChange = false

    private var dp8: Int = 0
    var itemClickAction: ((tab: LiteChildTab) -> Unit)? = null


    init {
        selectedColor = mFragment.context?.let { ContextCompat.getColor(it, R.color.main_color_272727) }
                        ?: 0
        unSelectedColor = mFragment.context?.let { ContextCompat.getColor(it, R.color.main_color_666666) }
                          ?: 0

        dp8 = BaseUtil.dp2px(mFragment.context, 8f)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = inflater.inflate(R.layout.main_item_scroll_style_single_child_tab, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount(): Int {
        return mDataList?.size ?: 0
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is ViewHolder) {
            val tab = mDataList?.get(position) ?: return
            var title = tab.title ?: ""

            if (title.length >= 6) {
                title = title.substring(0, 6)
            }

            holder.tvChildTabTitle.text = title
            if (tab.selectd) {
                holder.tvChildTabTitle.setTextColor(selectedColor)
                holder.tvChildTabTitle.typeface = Typeface.DEFAULT_BOLD
                holder.tvChildTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, selectedTextSize)
                holder.bgChildTabView.visibility = View.VISIBLE

                val measureTextWidth = holder.tvChildTabTitle.paint.measureText(title)

                val lp = holder.bgChildTabView.layoutParams
                lp.width = (measureTextWidth + dp8).toInt()
                holder.bgChildTabView.layoutParams = lp
                Logger.i(TAG, "lp.width = ${lp.width} lp.height = ${lp.height}")

                //val bounds = holder.bgChildTabView.background?.bounds ?: Rect()
                //holder.bgChildTabView.background.setBounds(bounds.left, bounds.top, (measureTextWidth + dp8).toInt(), bounds.bottom)

            } else {
                holder.tvChildTabTitle.setTextColor(unSelectedColor)
                holder.tvChildTabTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, unSelectedTextSize)
                holder.tvChildTabTitle.typeface = Typeface.DEFAULT
                //holder.bgChildTabView.background = null
                holder.bgChildTabView.visibility = View.GONE
            }
            holder.tvChildTabTitle.setOnClickListener {
                val size = mDataList?.size ?: return@setOnClickListener
                if (position in 0 until size) {
                    mDataList?.forEach {
                        it.selectd = false
                    }
                }

                val selectedTab = mDataList?.get(position)
                selectedTab?.let {
                    isTabChange = true
                    it.selectd = true
                    itemClickAction?.invoke(it)

                    // 分类页-子Tab  点击事件
                    XMTraceApi.Trace()
                        .click(32937)
                        .put("moduleId", selectedTab.moduleId)
                        .put("tabId", selectedTab.tabId.toString())
                        .put("currModule", selectedTab.title)
                        .put("currPage", "categoryPageV2")
                        .createTrace()
                }

                notifyDataSetChanged()
            }

            if (isTabChange) {
                isTabChange = false
            } else {
                // 分类页-子Tab  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(32938)
                    .setServiceId("slipPage")
                    .put("moduleId", tab.moduleId)
                    .put("tabId", tab.tabId.toString()) // 子tab所属的pageid
                    .put("currModule", tab.title)
                    .put("currPage", "categoryPageV2")
                    .createTrace()
            }
        }
    }

    override fun getItem(position: Int): Any? {
        if (CollectionUtil.isNotEmpty(mDataList)) {
            return mDataList?.get(position)
        }
        return null
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        val tvChildTabTitle: TextView = itemView.findViewById(R.id.main_tv_child_tab_title)
        val bgChildTabView: View = itemView.findViewById(R.id.main_view_tab_bg)

    }

}