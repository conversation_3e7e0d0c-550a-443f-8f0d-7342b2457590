package com.ximalaya.ting.lite.main.earn.dialog.adapter;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter;
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder;
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment;
import com.ximalaya.ting.android.host.model.user.NewUserReward;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.earn.dialog.OptimizedHasLoginEarnGuideDialogFragmentNew;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OptimizedHasLoginEarnGuideRvAdapter extends MultiRecyclerAdapter<NewUserReward, SuperRecyclerHolder> {
    public Activity mActivity;
    private OptimizedHasLoginEarnGuideDialogFragmentNew.NewUserOpenRewardCallBack newUserOpenRewardCallBack;
    public boolean isOpenNewUserReward = false; //是否在开红包
    private BaseFullScreenDialogFragment mFragment;

    public OptimizedHasLoginEarnGuideRvAdapter(Activity mCtx, BaseFullScreenDialogFragment fragment, List<NewUserReward> mValueList, OptimizedHasLoginEarnGuideDialogFragmentNew.NewUserOpenRewardCallBack newUserOpenRewardCallBack) {
        super(mCtx, mValueList);
        this.mActivity = mCtx;
        this.mFragment = fragment;
        this.newUserOpenRewardCallBack = newUserOpenRewardCallBack;
    }

    @Override
    public SuperRecyclerHolder createMultiViewHolder(Context mCtx, @NonNull View itemView, int viewType) {
        return SuperRecyclerHolder.createViewHolder(mCtx, itemView);
    }

    @Override
    public void onBindMultiViewHolder(SuperRecyclerHolder holder, NewUserReward newUserReward, int viewType, int position) {
        if (newUserReward == null) {
            return;
        }
        LinearLayout newUserItemContainer = (LinearLayout) holder.getViewById(R.id.ll_new_user_item_container);
        ImageView ivGoldCoin = (ImageView) holder.getViewById(R.id.iv_redPacket_gold_coin);
        ImageView ivQiJI = (ImageView) holder.getViewById(R.id.iv_redPacket_qiji);
        TextView tvReaPacketContent = (TextView) holder.getViewById(R.id.tv_redPacket_item_content);
        View redPacketBg = holder.getViewById(R.id.view_redPacket_item_background);
        View selectRedPacketBg = holder.getViewById(R.id.view_redPacket_select_item_background);

        if (newUserReward.isOpen()) {
            Object tag = redPacketBg.getTag();
            if (tag instanceof AnimatorSet) {
                AnimatorSet animatorSet = (AnimatorSet) tag;
                animatorSet.cancel();
                redPacketBg.setTag(null);
            }

            // 点击后选中的那个的动画
            if (newUserReward.getRewardType() == 3) {
                newUserItemContainer.setVisibility(View.VISIBLE);
                selectRedPacketBg.setVisibility(View.VISIBLE);
                redPacketBg.setVisibility(View.GONE);
            } else {
                redPacketBg.setCameraDistance(BaseUtil.dp2px(getContext(), 12490));
                redPacketBg.setRotationY(360);
                redPacketBg.animate().rotationY(0).setDuration(1000).setListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        redPacketBg.setVisibility(View.GONE);
                        selectRedPacketBg.setVisibility(View.GONE);
                        newUserItemContainer.setVisibility(View.VISIBLE);
                    }
                }).setStartDelay(300);
            }
        } else {
            newUserItemContainer.setVisibility(View.GONE);
            redPacketBg.setVisibility(View.VISIBLE);

            Object tag = redPacketBg.getTag();
            if (tag instanceof AnimatorSet) {
                AnimatorSet animatorSet = (AnimatorSet) tag;
                animatorSet.start();
            } else {
                AnimatorSet animatorSet = new AnimatorSet();
                ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(redPacketBg, "scaleX", 1f, 0.9f, 1f);
                scaleXAnimator.setRepeatCount(ValueAnimator.INFINITE);

                ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(redPacketBg, "scaleY", 1f, 0.9f, 1f);
                scaleYAnimator.setRepeatCount(ValueAnimator.INFINITE);

                animatorSet.setDuration(2500);
                animatorSet.playTogether(scaleXAnimator, scaleYAnimator);
                animatorSet.start();

                redPacketBg.setTag(animatorSet);
            }
        }
        if (newUserReward.getRewardType() == 3) {
            ivGoldCoin.setVisibility(View.VISIBLE);
            ivQiJI.setVisibility(View.VISIBLE);
        } else if (newUserReward.getRewardType() == 2) {
            ivQiJI.setVisibility(View.GONE);
            ivGoldCoin.setVisibility(View.VISIBLE);
        } else if (newUserReward.getRewardType() == 1) {
            ivQiJI.setVisibility(View.VISIBLE);
            ivGoldCoin.setVisibility(View.GONE);
        }
        if (!TextUtils.isEmpty(newUserReward.getRewardContent())) {
            tvReaPacketContent.setText(newUserReward.getRewardContent());
        }
        if (isOpenNewUserReward && !newUserReward.isOpen() && newUserReward.getRewardType() == 3) {
            //12490这个数值随便写的。但是一定要是一个比较大的值。
            redPacketBg.setCameraDistance(BaseUtil.dp2px(getContext(), 12490));
            redPacketBg.setRotationY(360);
            redPacketBg.animate().rotationY(0).setDuration(1000).setListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    if (newUserOpenRewardCallBack != null) {
                        newUserOpenRewardCallBack.openRewardBack();
                    }
                    updateNewUserRewardData();
                }
            }).start();
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!newUserReward.isOpen() && !isOpenNewUserReward && !isAutoOpenRewardRedPacket()) {
                    isOpenNewUserReward = true;
                    if (newUserOpenRewardCallBack != null) {
                        newUserOpenRewardCallBack.stopRunnableBack();
                    }
                    updateNewUserRewardDataByPosition(position);
                }
                // 新人礼包放送弹窗-红包  弹框控件点击
                new XMTraceApi.Trace()
                        .setMetaId(54913)
                        .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                        .put("currPage", "newUserPage")
                        .createTrace();
            }
        });
    }

    public boolean isAutoOpenRewardRedPacket() {
        if (mFragment instanceof OptimizedHasLoginEarnGuideDialogFragmentNew) {
            return ((OptimizedHasLoginEarnGuideDialogFragmentNew) mFragment).isAutoOpenReward;
        }
        return false;
    }

    @SuppressLint("NotifyDataSetChanged")
    private void updateNewUserRewardData() {
        List<NewUserReward> newUserRewards = getValueList();
        for (NewUserReward newUserReward : newUserRewards) {
            newUserReward.setOpen(true);
        }
        notifyDataSetChanged();
    }

    /**
     * 通过 position 交换数据
     *
     * @param position
     */
    @SuppressLint("NotifyDataSetChanged")
    private void updateNewUserRewardDataByPosition(int position) {
        List<NewUserReward> newUserRewards = getValueList();
        NewUserReward newUserReward = newUserRewards.get(position);
        boolean isSelectVariousType = newUserReward != null && newUserReward.getRewardType() == 3;
        if (isSelectVariousType) {
            notifyDataSetChanged();
            return;
        }
        int variousUserRewardPosition = 1;
        for (int i = 0; i < newUserRewards.size(); i++) {
            NewUserReward userReward = newUserRewards.get(i);
            if (userReward != null && userReward.getRewardType() == 3) {
                variousUserRewardPosition = i;
                break;
            }
        }
        Collections.swap(newUserRewards, position, variousUserRewardPosition);
        notifyDataSetChanged();
    }

    /**
     * 是否能开红包
     *
     * @return
     */
    public boolean isCanOpenNewUserReward() {
        List<NewUserReward> newUserRewards = getValueList();
        for (NewUserReward newUserReward : newUserRewards) {
            if (!newUserReward.isOpen()) {
                return true;
            }
        }
        return false;
    }


    @Override
    public int getMultiItemViewType(NewUserReward model, int position) {
        return 0;
    }

    @Override
    public int getMultiItemLayoutId(int viewType) {
        return R.layout.main_fra_dialog_optimized_has_login_item;
    }
}
