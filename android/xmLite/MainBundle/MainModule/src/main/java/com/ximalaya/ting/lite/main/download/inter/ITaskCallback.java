package com.ximalaya.ting.lite.main.download.inter;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   : 下载任务的回调接口
 */
public interface ITaskCallback {

    /**
     * 下载进度回调
     *
     * @param once
     *            一次通知完成量（每次通知下载的粒度）
     * @param done
     *            已完成量,指任务开始到某一时刻的大小。
     * @param fileSize
     *            任务的总量
     */
    void onProgressUpdate(long once, long done, long fileSize);

    /**
     * 下载状态发生了改变
     *
     */
    void onStateChanged(int state);
}
