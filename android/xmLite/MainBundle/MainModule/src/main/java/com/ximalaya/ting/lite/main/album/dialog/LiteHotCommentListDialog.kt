package com.ximalaya.ting.lite.main.album.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.album.adapter.LiteHotCommentAdapterTwo
import com.ximalaya.ting.lite.main.model.album.AlbumCommentListModel
import com.ximalaya.ting.lite.main.model.album.LiteHotComment
import com.ximalaya.ting.lite.main.request.LiteAlbumPageRequest
import com.ximalaya.ting.lite.main.view.LitePtrRecyclerView


/**
 * Created by dumingwei on 2021/5/26
 *
 * Desc: 评论列表弹窗
 */
class LiteHotCommentListDialog(val albumId: Long) : BaseFullScreenDialogFragment(), View.OnClickListener {

    private val TAG: String = "LiteHotCommentListDialo"

    private lateinit var ptrRvComment: LitePtrRecyclerView

    private lateinit var rvComment: RecyclerView

    private lateinit var rlNoNet: RelativeLayout
    private lateinit var ivNoNet: ImageView
    private lateinit var tvHintText: TextView
    private lateinit var tvNoNet: TextView

    private lateinit var rlRootContainer: RelativeLayout

    private var isLoading = false
    private var pageId = 1

    var adapter: LiteHotCommentAdapterTwo? = null

    var commentList: MutableList<LiteHotComment> = arrayListOf()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val inflaterView = inflater.inflate(R.layout.main_dialog_hot_comment_list, container, false)

        rlRootContainer = inflaterView.findViewById(R.id.main_rl_root_container)
        rlRootContainer.setOnClickListener(this)

        val clContainer = inflaterView.findViewById<ConstraintLayout>(R.id.main_cl_container)


        val lp = clContainer.layoutParams as RelativeLayout.LayoutParams
        val screenHeight = BaseUtil.getScreenHeight(context)
        lp.height = (screenHeight / 4f * 3f).toInt()

        clContainer.layoutParams = lp

        ptrRvComment = inflaterView.findViewById(R.id.main_ptr_rv_hot_comment)
        rvComment = ptrRvComment.refreshableView

        ptrRvComment.setOnRefreshLoadMoreListener(object : LitePtrRecyclerView.IRefreshLoadMoreListener {

            override fun onRefresh() {
                pageId = 1
                loadData()
            }

            override fun onMore() {
                pageId++
                loadData()
            }
        })

        rvComment.layoutManager = LinearLayoutManager(context)

        adapter = context?.let { LiteHotCommentAdapterTwo(it, commentList) }
        adapter?.onLikeAction = { position, comment ->
            if (UserInfoMannage.hasLogined()) {
                val currentLiked = comment.liked ?: false
                comment.liked = !currentLiked

                XMTraceApi.Trace()
                        .setMetaId(32269)
                        .setServiceId("dialogClick")
                        .createTrace()

                if (currentLiked) {
                    //取消点赞
                    val likeCount = (comment.likeCount ?: 0) - 1
                    comment.likeCount = if (likeCount < 0) 0 else likeCount
                    unLikeComment(position, comment)

                } else {
                    //点赞
                    val likeCount = (comment.likeCount ?: 0) + 1
                    comment.likeCount = likeCount
                    likeComment(position, comment)
                }
                adapter?.notifyItemChanged(position)
            } else {
                UserInfoMannage.gotoLogin(context)
            }
        }

        //注意!!!，这里调用的是LitePtrRecyclerView的setAdapter方法。
        ptrRvComment.setAdapter(adapter)

        rlNoNet = inflaterView.findViewById(R.id.main_rl_no_net)
        ivNoNet = inflaterView.findViewById(R.id.main_iv_no_net)
        tvHintText = inflaterView.findViewById(R.id.main_tv_hint_text)
        tvNoNet = inflaterView.findViewById(R.id.main_tv_no_net)

        tvNoNet.setOnClickListener(this)

        val tvClose: TextView = inflaterView.findViewById(R.id.main_tv_close)
        tvClose.setOnClickListener(this)
        AutoTraceHelper.bindData(tvClose, AutoTraceHelper.MODULE_DEFAULT, "")

        XMTraceApi.Trace()
                .setMetaId(32268)
                .setServiceId("dialogView")
                .createTrace()

        return inflaterView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        HandlerManager.postOnUIThreadDelay({
            ptrRvComment.setRefreshing()
        }, 500)
    }

    /**
     * 点赞
     */
    private fun likeComment(position: Int, comment: LiteHotComment) {
        LiteAlbumPageRequest.likeAlbumComment(comment.albumId ?: -1, comment.commentId ?: -1,
                comment.uid ?: -1, object : IDataCallBack<Boolean> {
            override fun onSuccess(success: Boolean?) {
                if (success == true) {
                    //不需要任何操作，因为在请求接口前已经将状态置为已点赞了。
                } else {
                    //点赞失败，将状态重新置为未点赞状态
                    likeFailed(comment, position)
                }
            }

            override fun onError(code: Int, message: String?) {
                Logger.i(TAG, "likeComment onError code $code")
                likeFailed(comment, position)
            }
        })

    }

    private fun likeFailed(comment: LiteHotComment, position: Int) {
        comment.liked = false
        comment.likeCount = (comment.likeCount ?: 1) - 1
        adapter?.notifyItemChanged(position)
    }

    /**
     * 取消点赞
     */
    private fun unLikeComment(position: Int, comment: LiteHotComment) {
        LiteAlbumPageRequest.unlikeAlbumComment(comment.albumId ?: -1,
                comment.commentId ?: -1, comment.uid ?: -1, object : IDataCallBack<Boolean> {
            override fun onSuccess(success: Boolean?) {
                if (success == true) {
                    //不需要任何操作，因为在请求接口前已经将状态置为已点赞了。
                } else {
                    //取消点赞失败，将状态重新置为已点赞状态
                    unlikeFailed(comment, position)
                }
            }

            override fun onError(code: Int, message: String?) {
                Logger.i(TAG, "unLikeComment onError code $code")
                unlikeFailed(comment, position)
            }
        })
    }

    private fun unlikeFailed(comment: LiteHotComment, position: Int) {
        comment.liked = true
        comment.likeCount = (comment.likeCount ?: 0) + 1
        adapter?.notifyItemChanged(position)
    }

    override fun isShowFromBottomEnable() = true

    private fun loadData() {
        if (isLoading) {
            return
        }
        isLoading = true
        val params = hashMapOf<String, String?>()
        params["albumId"] = albumId.toString()
        params["pageId"] = pageId.toString()
        params["pageSize"] = "10"

        LiteAlbumPageRequest.getCommentList(params, object : IDataCallBack<AlbumCommentListModel?> {
            override fun onSuccess(model: AlbumCommentListModel?) {
                isLoading = false
                if (!canUpdateUi()) {
                    return
                }
                ptrRvComment.onRefreshComplete()
                if ((model == null || model.list.isNullOrEmpty()) && pageId == 1) {
                    showNoNetLayout(true)
                    return
                }

                hideNoNetLayout()

                if (model == null) {
                    return
                }

                val maxPageId = model.maxPageId ?: 0
                if (maxPageId > pageId) {
                    ptrRvComment.onRefreshComplete(true)
                } else {
                    ptrRvComment.onRefreshComplete(false)
                }
                if (pageId == 1) {
                    commentList.clear()
                }

                model.list?.let {
                    commentList.addAll(it)
                }
                adapter?.notifyDataSetChanged()

            }

            override fun onError(code: Int, message: String?) {
                isLoading = false
                Logger.i(TAG, "onError code = $code")
                if (!canUpdateUi()) {
                    return
                }
                ptrRvComment.onRefreshComplete()
                if (pageId == 1) {
                    showNoNetLayout(false)
                }
            }
        })

    }

    override fun onClick(view: View) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return
        }
        when (view.id) {
            R.id.main_tv_close,
            R.id.main_rl_root_container -> dismiss()

            R.id.main_tv_no_net -> {
                hideNoNetLayout()
                ptrRvComment.setRefreshing()
            }
        }
    }

    private fun showNoNetLayout(isEmpty: Boolean) {
        if (canUpdateUi()) {
            rlNoNet.visibility = View.VISIBLE
            //展示空界面
            if (isEmpty) {
                ivNoNet.setImageResource(R.drawable.host_no_content)
                tvHintText.text = context?.getString(R.string.host_no_content_now)
                tvNoNet.visibility = View.INVISIBLE
            } else {
                //展示错误界面
                ivNoNet.setImageResource(R.drawable.host_no_net)
                tvHintText.text = context?.getString(R.string.host_network_error)
                tvNoNet.visibility = View.VISIBLE
            }
        }
    }

    private fun hideNoNetLayout() {
        if (canUpdateUi()) {
            rlNoNet.visibility = View.GONE
        }
    }

}