package com.ximalaya.ting.lite.main.manager

import androidx.fragment.app.FragmentActivity
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.listener.IShowDialog
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.lite.main.dialog.MemberBenefitsDialog
import com.ximalaya.ting.lite.main.request.LiteUrlConstants
import org.json.JSONObject

/**
 * 会员权益弹窗管理
 */
object MemberBenefitsManage {

    private const val TAG = "MemberBenefitsManage"

    private const val KEY_SHOW_MEMBER_BENEFITS_DIALOG = "key_show_member_benefits_dialog"

    @JvmStatic
    fun checkShowDialog() {
        val context = BaseApplication.getMyApplicationContext()

        if (ChildProtectManager.isChildProtectOpen(context)) {
            return
        }

        val isShow =
            MmkvCommonUtil.getInstance(context).getBoolean(KEY_SHOW_MEMBER_BENEFITS_DIALOG, false)

        if (isShow) {
            FuliLogger.log(TAG, "权益弹窗已显示过")
            return
        }

        requestShowDialog(object : IDataCallBack<Int> {
            override fun onSuccess(isShow: Int?) {
                FuliLogger.log(TAG, "requestShowDialog onSuccess:${isShow}")

                // 已经展示过 不处理
                if (isShow == -1) {
                    MmkvCommonUtil.getInstance(context)
                        .saveBoolean(KEY_SHOW_MEMBER_BENEFITS_DIALOG, true)
                } else if (isShow == 1) {
                    DialogShowManager.showDialog(object : IShowDialog() {
                        override fun getDialogName(): String {
                            return MemberBenefitsDialog::class.java.simpleName
                        }

                        override fun show() {
                            val dialog = MemberBenefitsDialog()
                            val activity = BaseApplication.getMainActivity()
                            if (activity is FragmentActivity) {
                                try {
                                    dialog.show(
                                        activity.supportFragmentManager,
                                        "MemberBenefitsDialog"
                                    )
                                    MmkvCommonUtil.getInstance(context)
                                        .saveBoolean(KEY_SHOW_MEMBER_BENEFITS_DIALOG, true)
                                    sendShowDialogSuccess()
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }
                        }

                    })
                }
            }

            override fun onError(code: Int, message: String?) {
                FuliLogger.log(TAG, "requestShowDialog onError code:${code} message:${message}")
            }
        })
    }

    private fun requestShowDialog(callBack: IDataCallBack<Int>) {
        val url = LiteUrlConstants.getMemberBenefitsDialogUrl()
        CommonRequestM.baseGetRequest(
            url, null, callBack, object : CommonRequestM.IRequestCallBack<Int> {
                override fun success(content: String?): Int {
                    if (content.isNullOrEmpty()) {
                        return 0
                    }
                    try {
                        val json = JSONObject(content)
                        val data = json.optJSONObject("data")
                        if (data != null) {
                            return if (data.optBoolean("showPopup")) {
                                1
                            } else {
                                -1
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    return 0
                }
            })
    }

    private fun sendShowDialogSuccess() {
        val url = LiteUrlConstants.setMemberBenefitsDialogUrl()
        CommonRequestM.baseGetRequest(
            url, null, object : IDataCallBack<Boolean> {
                override fun onSuccess(result: Boolean?) {
                    FuliLogger.log(TAG, "sendShowDialogSuccess onSuccess:$result")
                }

                override fun onError(code: Int, message: String?) {
                    FuliLogger.log(TAG, "sendShowDialogSuccess onError code:$code message:$message")
                }

            }, object : CommonRequestM.IRequestCallBack<Boolean> {
                override fun success(content: String?): Boolean {
                    if (content.isNullOrEmpty()) {
                        return false
                    }
                    try {
                        val json = JSONObject(content)
                        return json.optInt("code") == 0
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    return false
                }
            })
    }
}