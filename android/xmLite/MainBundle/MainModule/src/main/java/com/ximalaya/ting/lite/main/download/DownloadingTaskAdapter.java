package com.ximalaya.ting.lite.main.download;

import android.app.Activity;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import com.ximalaya.ting.android.downloadservice.base.BaseDownloadTask;
import com.ximalaya.ting.android.downloadservice.base.IDownloadStatus;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.view.bar.RoundProgressBar;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> on 17/8/17.
 */

public class DownloadingTaskAdapter extends HolderAdapter<BaseDownloadTask> {

    public DownloadingTaskAdapter(Activity context, List<BaseDownloadTask> listData) {
        super(context, listData);
    }

    @Override
    public void onClick(View view, BaseDownloadTask track, int position, BaseViewHolder holder) {
        int id = view.getId();
        if (id == R.id.main_iv_del) {
            delete(track);
        }
    }

    private static class DeleteTask extends MyAsyncTask<Object, Object, Object> {
        private MyProgressDialog pd = null;
        private WeakReference<DownloadingTaskAdapter> mAdapterRef;
        private BaseDownloadTask mTrackToDelete;

        DeleteTask(DownloadingTaskAdapter adapter, BaseDownloadTask trackToDelete) {
            mAdapterRef = new WeakReference<>(adapter);
            mTrackToDelete = trackToDelete;
        }

        @Override
        protected Void doInBackground(Object... params) {
            RouteServiceUtil.getDownloadService().deleteDownloadTask(mTrackToDelete);

            return null;
        }

        @Override
        protected void onPostExecute(Object result) {
            if (null != pd) {
                pd.cancel();
            }
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            DownloadingTaskAdapter adapter = mAdapterRef.get();
            if (adapter != null) {
                pd = new MyProgressDialog(adapter.context);
                pd.setMessage("正在清除下载列表，请等待...");
                pd.delayShow();
            }
        }
    }


    private void delete(final BaseDownloadTask task) {
        if (task.getDownloadStatus() < IDownloadStatus.DOWNLOAD_FINISH) {
            new DialogBuilder(context).setMessage("是否确定删除该节目?")
                    .setOkBtn(new DialogBuilder.DialogCallback() {
                        @Override
                        public void onExecute() {
                            new DeleteTask(DownloadingTaskAdapter.this, task).myexec();
                        }
                    }).showConfirm();
        }
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_downloading_track;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, BaseDownloadTask downloadTask, int position) {
        Track track = downloadTask.getTrack();
        ViewHolder vh = (ViewHolder) holder;
        String coverUrl = TextUtils.isEmpty(track.getCoverUrlMiddle()) ? track
                .getCoverUrlSmall() : track.getCoverUrlMiddle();
        ImageManager.from(context).displayImage(vh.cover, coverUrl,
                com.ximalaya.ting.android.host.R.drawable.host_default_album_145);

        vh.duration.setText(StringUtil.toTime(track.getDuration()));
        List<Integer> resIds = new ArrayList<>();
//        if (downloadTask.getDownloadFileType() == BaseDownloadTask.DOWNLOAD_FILE_TYPE_VIDEO) {
//            resIds.add(R.drawable.host_album_ic_video);
//        }
        vh.title.setText(ToolUtil.getTrackTitleWithPicAheadCenterAlign(vh.title.getContext(), track.getTrackTitle(), resIds, 1));
        if (track.getAlbum() != null) {
            vh.subtitle.setText(track.getAlbum().getAlbumTitle());
        }

        if (downloadTask.getDownloadStatus() == IDownloadStatus.DOWNLOADING) {
            vh.progressBar.setVisibility(View.VISIBLE);
            vh.progressBar.setCricleProgressColor(0xFFF86442);
            vh.tvStatus.setText("下载中");
            vh.tvStatus.setTextColor(0xFFF86442);
            vh.playFlag.setImageResource(R.drawable.main_ic_download_downloading);
        } else if (downloadTask.getDownloadStatus() == IDownloadStatus.DOWNLOAD_PAUSE) {
            vh.progressBar.setVisibility(View.VISIBLE);
            vh.progressBar.setCricleProgressColor(0xFF999999);
            vh.tvStatus.setText("已暂停");
            vh.tvStatus.setTextColor(0xFF999999);
            vh.playFlag.setImageResource(R.drawable.main_ic_download_pause);
        } else if (downloadTask.getDownloadStatus() == IDownloadStatus.DOWNLOAD_WAITING) {
            vh.progressBar.setVisibility(View.INVISIBLE);
            vh.tvStatus.setText("待下载");
            vh.tvStatus.setTextColor(0xFF999999);
            vh.playFlag.setImageResource(R.drawable.main_ic_download_waiting);
        } else if (downloadTask.getDownloadStatus() == IDownloadStatus.DOWNLOAD_FAILED) {
            vh.progressBar.setVisibility(View.INVISIBLE);
            vh.tvStatus.setText("失败");
            vh.tvStatus.setTextColor(0xFF999999);
            vh.playFlag.setImageResource(R.drawable.main_ic_download_wrong);
        }




        String fileSize = StringUtil.toMBFormatString(downloadTask.getDownloadedSize()) + "M/" + StringUtil.toMBFormatString(downloadTask.getDownloadTotalSize()) + "M";
        vh.tvDownloadSize.setText(fileSize);
        vh.progressBar.setProgress((int) (downloadTask.getDownloadPercentage() * 100));

        setClickListener(vh.ivDel, downloadTask, position, vh);
        AutoTraceHelper.bindData(vh.ivDel,track);
    }


    public void updateDownloadInfo(ListView listView, BaseDownloadTask task) {
        if (listView == null) {
            return;
        }

        View convertView = getItemView(listView, task);
        if (convertView == null) {
            return;
        }
        ViewHolder viewHolder = (ViewHolder) convertView.getTag();

        long downloadedSize = task.getDownloadedSize();
        long fileSize = task.getDownloadTotalSize();
        //以下是处理需要处理的控件
        if (fileSize > 0 && downloadedSize > 0) {
            viewHolder.progressBar.setVisibility(View.VISIBLE);
            viewHolder.progressBar.setProgress((int) (task.getDownloadPercentage() * 100));

            viewHolder.tvDownloadSize.setVisibility(View.VISIBLE);
            String fileSizeStr = StringUtil.toMBFormatString(downloadedSize) + "M/" + StringUtil.toMBFormatString(fileSize) + "M";
            viewHolder.tvDownloadSize.setText(fileSizeStr);
        }
    }

    private View getItemView(ListView listView, BaseDownloadTask downloadTask) {
        int wantedPosition = -1;

        if (listView != null && getCount() > 0) {
            for (int i = listView.getFirstVisiblePosition() - 1; i < getCount(); i++) {
                if (i < 0 || getListData() == null)
                    continue;
                if (downloadTask.equals(getListData().get(i))) {  //TODO by easoll: 使用task id来判断同一task
                    wantedPosition = i;
                    break;
                }
            }
        }

        if (wantedPosition < 0) {
            return null;
        }

        int firstPosition = listView.getFirstVisiblePosition() - listView.getHeaderViewsCount();

        int wantedChild = wantedPosition - firstPosition;
        if (wantedChild < 0 || wantedChild >= listView.getChildCount()) {
            return null;
        }

        return listView.getChildAt(wantedChild);
    }

    private static class ViewHolder extends BaseViewHolder {
        final ImageView ivDel;
        final RoundProgressBar progressBar;
        final TextView tvStatus;
        final TextView tvDownloadSize;
        final View root;
        final ImageView cover;
        final TextView title;
        final TextView duration;
        final ImageView playFlag;
        final View border;
        final TextView subtitle;

        ViewHolder(View convertView) {
            root = convertView;
            cover = convertView.findViewById(R.id.main_iv_cover);
            border = convertView.findViewById(R.id.main_border_bottom);
            playFlag = convertView.findViewById(R.id.main_play_icon);
            title = convertView.findViewById(R.id.main_down_track_title);
            subtitle = convertView.findViewById(R.id.main_tv_subtitle);
            duration = convertView.findViewById(R.id.main_tv_total_time);
            ivDel = convertView.findViewById(R.id.main_iv_del);
            progressBar = convertView.findViewById(R.id.main_pb_download_progress);
            tvStatus = convertView.findViewById(R.id.main_tv_status);
            tvDownloadSize = convertView.findViewById(R.id.main_tv_file_size);
        }

    }
}
