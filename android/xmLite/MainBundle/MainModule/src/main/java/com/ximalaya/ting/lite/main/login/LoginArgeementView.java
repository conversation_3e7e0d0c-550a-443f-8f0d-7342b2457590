package com.ximalaya.ting.lite.main.login;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.IntDef;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.host.activity.web.WebActivity;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.earn.AppStoreManager;
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.login.XiMaUseRuleManager;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.view.UnderlineClickableSpan;

/**
 * Created by qinhuifeng on 2021-6-23
 * <p>
 * 隐私协议同意相关View
 * <p>
 * 内部自带居上10dp，居下10dp的间距，扩大点击范围
 * <p>
 * 在不同的登录页面使用
 */
public class LoginArgeementView extends RelativeLayout {

    private Activity mActivity;
    private TextView mTvRegisterHint;
    private ViewGroup mLayoutAgreementCheck;
    private ImageView mIvAgreementCheck;

    //运营商协议名称
    private String quickOperatorProtocolName = "";
    //运营商协议跳转地址
    private String quickOperatorProtocolUrl = "";
    private Bundle mLoginParams;

    public LoginArgeementView(Context context) {
        super(context);
    }

    public LoginArgeementView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public LoginArgeementView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * 初始化ui
     */
    public void initUi(Activity activity, Bundle loginParams) {
        mLoginParams = loginParams;
        mActivity = activity;
        initView();
    }

    private void initView() {
        View agreementView = LayoutInflater.from(getContext()).inflate(R.layout.main_view_login_agreement_custom_view, null);
        mTvRegisterHint = agreementView.findViewById(R.id.main_regiset_hint);
        mLayoutAgreementCheck = agreementView.findViewById(R.id.main_layout_agreement_check);
        mIvAgreementCheck = agreementView.findViewById(R.id.main_iv_agreement_check);
        //初始化选中按钮
        initAgreementSelectShow();
        //初始化协议文案
        initAgreementTextShow();
        //将view添加到当前容器
        removeAllViews();
        RelativeLayout.LayoutParams agreementParams = new RelativeLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        addView(agreementView, agreementParams);
    }

    private void initAgreementTextShow() {
        //同意《中国联通认证服务协议》和《用户协议》、《隐私协议》并授权喜马拉雅极速版获取本机号码
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder("");
        String hintText = "我已阅读并同意喜马拉雅";
        String registerRule = "《用户协议》";
        String comma = " ";
        String privacyRule = "《隐私协议》";
        //运营商协议
        String operatorProtocolNameMark = "和";
        String operatorProtocolName = "";
        if (!TextUtils.isEmpty(quickOperatorProtocolName)) {
            operatorProtocolName = "《" + quickOperatorProtocolName + "》";
        }
        spannableStringBuilder.append(hintText);
        spannableStringBuilder.append(registerRule);
        spannableStringBuilder.append(comma);
        spannableStringBuilder.append(privacyRule);
        if (!TextUtils.isEmpty(operatorProtocolName)) {
            spannableStringBuilder.append(operatorProtocolNameMark);
            spannableStringBuilder.append(operatorProtocolName);
        }
        //用户协议点击
        int agreementColor = ContextCompat.getColor(mActivity, R.color.host_color_666666);
        UnderlineClickableSpan registerRuleClickSpan = new UnderlineClickableSpan(agreementColor) {
            @Override
            public void onClick(View widget) {
                Intent intent = new Intent(mActivity, WebActivity.class);
                intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, XiMaUseRuleManager.getRegisterRule());
                mActivity.startActivity(intent);
            }
        };
        int registerRuleStartIndex = hintText.length();
        int registerRuleEndIndex = registerRuleStartIndex + registerRule.length();
        spannableStringBuilder.setSpan(registerRuleClickSpan, registerRuleStartIndex, registerRuleEndIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        //隐私协议点击
        UnderlineClickableSpan privacyRuleClickSpan = new UnderlineClickableSpan(agreementColor) {
            @Override
            public void onClick(View widget) {
                Intent intent = new Intent(mActivity, WebActivity.class);
                intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, XiMaUseRuleManager.getPrivacyRule());
                mActivity.startActivity(intent);
            }
        };
        int privacyRuleStartIndex = hintText.length() + registerRule.length() + comma.length();
        int privacyRuleEndIndex = privacyRuleStartIndex + privacyRule.length();
        spannableStringBuilder.setSpan(privacyRuleClickSpan, privacyRuleStartIndex, privacyRuleEndIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        //运营商协议点击
        if (!TextUtils.isEmpty(operatorProtocolName)) {
            //隐私协议点击
            UnderlineClickableSpan operatorProtocolClickSpan = new UnderlineClickableSpan(agreementColor) {
                @Override
                public void onClick(View widget) {
                    if (!TextUtils.isEmpty(quickOperatorProtocolUrl)) {
                        Intent intent = new Intent(mActivity, WebActivity.class);
                        intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, quickOperatorProtocolUrl);
                        mActivity.startActivity(intent);
                    }
                }
            };
            int operatorProtocolStartIndex = hintText.length() + registerRule.length() + comma.length() + privacyRule.length() + operatorProtocolNameMark.length();
            int operatorProtocolEndIndex = operatorProtocolStartIndex + operatorProtocolName.length();
            spannableStringBuilder.setSpan(operatorProtocolClickSpan, operatorProtocolStartIndex, operatorProtocolEndIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        mTvRegisterHint.setText(spannableStringBuilder);
        mTvRegisterHint.setMovementMethod(LinkMovementMethod.getInstance());
        mTvRegisterHint.setHighlightColor(Color.TRANSPARENT);
    }

    /**
     * 选中按钮初始化
     */
    private void initAgreementSelectShow() {
//       Boolean agreementSelected = LoginBundleParamsManager.getAgreementSelected(mLoginParams);
//       if (agreementSelected == null) {
//           //默认为不勾选
//           changeAgreementSelect(false);
//       } else {
//           //之前已经选中过了，直接进行选中
//           changeAgreementSelect(agreementSelected);
//       }
        //默认为不勾选，
        changeAgreementSelect(false);
        //设置选中监听
        mLayoutAgreementCheck.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //不用做OneClickHelper.getInstance().onClick(v)判断
                changeAgreementSelect(!mLayoutAgreementCheck.isSelected());
            }
        });
    }

    /**
     * 协议选中状态改变
     */
    public void changeAgreementSelect(boolean isNeedSelect) {
        if (mIvAgreementCheck == null || mLayoutAgreementCheck == null) {
            return;
        }
        mLayoutAgreementCheck.setSelected(isNeedSelect);
        mIvAgreementCheck.setImageResource(isNeedSelect ? R.drawable.host_icon_agreement_selected : R.drawable.host_icon_agreement_unselected);
        //选中状态发生变化的时候，登录参数中的状态，切换其他登录方式的时候使用
        //因为登录的参数会在整个登录链中进行传递，此处做更新操作
        //不合规，不再保存状态
        //LoginBundleParamsManager.setAgreementSelected(mLoginParams, isNeedSelect);
    }

    /**
     * 检查是否勾选了隐私同意条款，没有勾选进行弹框
     */
    public boolean checkAgreementSelectedAndShowHint() {
        if (isAgreementSelected()) {
            return true;
        }
        LoginAgreementSelectGuideDialog dialog = new LoginAgreementSelectGuideDialog(mActivity);
        //设置运营商协议
        dialog.setQuickProtocolConfig(quickOperatorProtocolName, quickOperatorProtocolUrl);
        dialog.setAgreementOkClick(new LoginAgreementSelectGuideDialog.IDialogAgreementOkClick() {
            @Override
            public void onAgreementBtnClick() {
                changeAgreementSelect(true);
            }
        });
        dialog.show();
        //没有勾选
        return false;
    }

    /**
     * 是否选中协议
     */
    public boolean isAgreementSelected() {
        if (mIvAgreementCheck == null || mLayoutAgreementCheck == null) {
            return false;
        }
        return mLayoutAgreementCheck.isSelected();
    }

    /**
     * 一键登录运营商协议
     */
    public void setQuickProtocolConfig(String quickOperatorProtocolName, String quickOperatorProtocolUrl) {
        if (TextUtils.isEmpty(quickOperatorProtocolName) || TextUtils.isEmpty(quickOperatorProtocolUrl)) {
            return;
        }
        //跳转链接不合法
        if (!quickOperatorProtocolUrl.trim().startsWith("http")) {
            return;
        }
        this.quickOperatorProtocolName = quickOperatorProtocolName;
        this.quickOperatorProtocolUrl = quickOperatorProtocolUrl;
    }
}
