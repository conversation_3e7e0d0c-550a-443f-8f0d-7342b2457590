package com.ximalaya.ting.lite.main.newuser.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.SpannableString;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.NewUserMustListenerModel;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.List;

import static com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk.PLAY_FROM_OTHER;

/**
 * Created by dumingwei on 2020/6/1
 * <p>
 * Desc:
 */
public class NewUserAlbumAdapter extends HolderAdapter<NewUserMustListenerModel> {

    private final Context mContext;

    public NewUserAlbumAdapter(Context context, List<NewUserMustListenerModel> listData) {
        super(context, listData);
        mContext = context;
    }

    @Override
    public void onClick(View view, NewUserMustListenerModel listenerModel, int position, BaseViewHolder holder) {
        int id = view.getId();
        if (id == R.id.main_iv_play) {
            if (OneClickHelper.getInstance().onClick(view)) {
                NewUserMustListenerModel.Item album = listenerModel.getAlbum();
                if (album == null) {
                    return;
                }
                if (PlayTools.isAlbumPlaying(mContext, album.getAlbumId())) {
                    PlayTools.pause(mContext);
                    if (holder instanceof ViewHolder) {
                        ((ViewHolder) holder).ivPlay.setImageResource(R.drawable.main_ic_new_user_album_play);
                    }
                } else {
                    if (holder instanceof ViewHolder) {
                        //((ViewHolder) holder).ivPlay.setImageResource(R.drawable.main_ic_new_user_album_pause);
                        toAlbumPage(listenerModel.getAlbum(), true);
                    }
                }
            }
        } else if (id == R.id.main_v_item) {
            if (OneClickHelper.getInstance().onClick(view)) {
                toAlbumPage(listenerModel.getAlbum(), false);
            }
        }
    }

    private void toAlbumPage(NewUserMustListenerModel.Item album, boolean autoPlay) {
        if (album == null) {
            return;
        }
        Activity activity = BaseApplication.getTopActivity();
        if (activity == null) {
            return;
        }
        AlbumEventManage.AlbumFragmentOption option = new AlbumEventManage.AlbumFragmentOption();
        option.isAutoPlay = autoPlay;
        AlbumEventManage.startMatchAlbumFragment(album.getAlbumId(),
                AlbumEventManage.FROM_OTHER, PLAY_FROM_OTHER, null, null,
                -1, activity, option);
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_new_user_album;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, NewUserMustListenerModel listenerModel, int position) {
        ViewHolder vh = (ViewHolder) holder;
        NewUserMustListenerModel.Item album = listenerModel.getAlbum();
        if (album != null) {
            if (position == 0) {
                vh.vDivider.setVisibility(View.INVISIBLE);
            } else {
                vh.vDivider.setVisibility(View.VISIBLE);
            }
            ImageManager.from(mContext).displayImage(vh.ivCover, album.getCoverPath(), R.drawable.host_default_album_145);
            if (!TextUtils.isEmpty(album.getTitle())) {
                if (album.getSerialState() != null && album.getSerialState() == 2) {
                    SpannableString title = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(mContext, album.getTitle(),
                            R.drawable.host_tag_complete, 2);
                    vh.tvTitle.setText(title);
                } else {
                    vh.tvTitle.setText(album.getTitle());
                }
            }
            if (!TextUtils.isEmpty(album.getIntro())) {
                vh.tvIntro.setText(album.getIntro());
            }
            int coverTagResId = AlbumTagUtil.getAlbumCoverTag(album);
            if (coverTagResId != -1) {
                vh.ivVipTag.setImageResource(coverTagResId);
                vh.ivVipTag.setVisibility(View.VISIBLE);
            } else {
                vh.ivVipTag.setVisibility(View.INVISIBLE);
            }
            vh.tvPlayCount.setText(StringUtil.getFriendlyNumStr(album.getPlaysCounts()));
            vh.tvIncludeTracks.setText(String.valueOf(album.getTracks()));
            if (PlayTools.isAlbumPlaying(mContext, album.getAlbumId())) {
                vh.ivPlay.setImageResource(R.drawable.main_ic_new_user_album_pause);
            } else {
                vh.ivPlay.setImageResource(R.drawable.main_ic_new_user_album_play);
            }

            setClickListener(vh.ivPlay, listenerModel, position, vh);
            setClickListener(vh.vItem, listenerModel, position, vh);
            AutoTraceHelper.bindData(vh.ivPlay, listenerModel);
            AutoTraceHelper.bindData(vh.vItem, listenerModel);
        }
    }


    class ViewHolder extends BaseViewHolder {

        private final ImageView ivCover;
        private final TextView tvTitle;
        private final TextView tvIntro;
        private final ImageView ivVipTag;
        private final TextView tvPlayCount;
        private final TextView tvIncludeTracks;
        private final ImageView ivPlay;
        private final View vItem;
        private final View vDivider;

        ViewHolder(View view) {
            vItem = view.findViewById(R.id.main_v_item);
            ivCover = view.findViewById(R.id.main_iv_cover);
            tvTitle = view.findViewById(R.id.main_tv_title);
            tvIntro = view.findViewById(R.id.main_tv_intro);
            ivVipTag = view.findViewById(R.id.main_iv_vip_tag);
            tvPlayCount = view.findViewById(R.id.main_tv_play_count);
            tvIncludeTracks = view.findViewById(R.id.main_tv_include_tracks);
            ivPlay = view.findViewById(R.id.main_iv_play);
            vDivider = view.findViewById(R.id.main_v_divider);
        }
    }
}
