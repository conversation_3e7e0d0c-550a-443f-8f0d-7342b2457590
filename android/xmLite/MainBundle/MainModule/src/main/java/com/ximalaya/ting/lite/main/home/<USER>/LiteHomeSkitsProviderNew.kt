//package com.ximalaya.ting.lite.main.home.adapter
//
//import android.content.Context
//import android.net.Uri
//import android.text.TextUtils
//import android.view.LayoutInflater
//import android.view.View
//import android.view.ViewGroup
//import android.widget.TextView
//import androidx.recyclerview.widget.GridLayoutManager
//import androidx.recyclerview.widget.RecyclerView
//import com.ximalaya.ting.android.framework.adapter.HolderAdapter
//import com.ximalaya.ting.android.framework.manager.ImageManager
//import com.ximalaya.ting.android.framework.view.image.RoundImageView
//import com.ximalaya.ting.android.host.fragment.BaseFragment2
//import com.ximalaya.ting.android.main.R
//import com.ximalaya.ting.android.xmtrace.XMTraceApi
//import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter
//import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
//import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
//import com.ximalaya.ting.lite.main.home.fragment.LiteHomeSkitsFragment
//import com.ximalaya.ting.lite.main.manager.ITingHandler
//import com.ximalaya.ting.lite.main.model.newhome.LiteFloorModel
//import com.ximalaya.ting.lite.main.view.RecyclerViewCanDisallowIntercept
//
//
///**
// *  @Author: Junxiang Cheng
// *  @Mail: <EMAIL>
// *  @CreateTime: 1/28/22
// *
// *  @Description: 短剧页Adapter
// */
//class LiteHomeSkitsProviderNew @JvmOverloads constructor(
//    val mFragment: BaseFragment2
//) : IMulitViewTypeViewAndData<LiteHomeSkitsProviderNew.Holder, LiteFloorModel> {
//
//    companion object {
//        const val TYPE_TRIPLE_ROW = 4
//    }
//
//    private var mHolder: Holder? = null
//
//    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup?): View {
//        return layoutInflater.inflate(R.layout.main_item_home_skits_floor, parent, false)
//    }
//
//    override fun buildHolder(convertView: View): Holder {
//        mHolder = Holder(convertView)
//        return mHolder!!
//    }
//
//    override fun bindViewDatas(
//        holder: Holder,
//        t: ItemModel<LiteFloorModel>,
//        convertView: View,
//        position: Int
//    ) {
//        val model = t.getObject()
//        holder.viewType = TYPE_TRIPLE_ROW
//        holder.rvSkitsContainer.apply {
//            layoutManager = GridLayoutManager(context, holder.viewType)
//        }
//        model?.let {
//            holder.tvTitle.text = it.title
//            var isHasMore = false
//            if (it.otherData != null && !TextUtils.isEmpty(it.otherData.hasMoreLink)) {
//                isHasMore = true
//            }
//            holder.tvViewAll.apply {
//                visibility = if (isHasMore) View.VISIBLE else View.GONE
//                setOnClickListener { _ ->
//                    try {
//                        ITingHandler().handleITing(
//                            mFragment.activity,
//                            Uri.parse(it.otherData.hasMoreLink)
//                        )
//                        // 新首页-短剧模块-更多  点击事件
//                        XMTraceApi.Trace()
//                            .click(58292) // 用户点击时上报
//                            .put("currPage", "homePageV2")
//                            .createTrace()
//                    } catch (e: Exception) {
//                        e.printStackTrace();
//                    }
//                }
//            }
//            //mFragment.view?.setBackgroundColor(mFragment.resourcesSafe.getColor(R.color.host_read_color_f7f7f7))
//            holder.rvSkitsContainer.apply {
//                setDisallowInterceptTouchEventView(mFragment.view as ViewGroup)
//                isNestedScrollingEnabled = false
//                adapter = LiteSkitsFloorGridAdapter(
//                    context,
//                    mFragment,
//                    it.skitsList as ArrayList,
//                    holder.viewType
//                )
//            }
//            // 新首页-短剧模块  控件曝光
//            XMTraceApi.Trace()
//                .setMetaId(58291)
//                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
//                .put("currPage", "homePageV2")
//                .put("exploreType", "homePageV2") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
//                .createTrace()
//        }
//    }
//
//    class Holder(val view: View) : HolderAdapter.BaseViewHolder() {
//        var viewType: Int = 0
//
//        var rootView: View = view.findViewById(R.id.main_cl_floor_root)
//        var tvTitle: TextView = view.findViewById(R.id.main_tv_floor_title)
//        var rvSkitsContainer: RecyclerViewCanDisallowIntercept =
//            view.findViewById(R.id.main_rv_skits_container)
//        var tvViewAll: View = view.findViewById(R.id.main_rl_view_all_container)
//    }
//
//}
//
//class LiteSkitsFloorGridAdapter(
//    var mContext: Context,
//    var mFragment: BaseFragment2,
//    var mList: ArrayList<DPDrama>?, var mFloorType: Int
//) : AbRecyclerViewAdapter<RecyclerView.ViewHolder>() {
//
//    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
//        val view = LayoutInflater.from(parent.context)
//            .inflate(R.layout.main_item_home_skits_grid_triple_new, parent, false)
//        return SkitsGridRowViewHolder(view)
//    }
//
//    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
//        if (!(holder is SkitsGridRowViewHolder)) return
//        val data = getItem(position)
//        data?.let {
//            holder.tvTitle.text = it.title
//
//            ImageManager.from(mContext)
//                .displayImage(
//                    holder.ivCover,
//                    it.coverImage,
//                    R.drawable.host_bg_book_default,
//                    R.drawable.host_bg_book_default
//                )
//            holder.itemView.setOnClickListener { _ ->
//                mFragment.startFragment(LiteHomeSkitsFragment.newInstance(it.id))
//                // 新首页-短剧模块  点击事件
//                // 新首页-短剧模块  点击事件
//                XMTraceApi.Trace()
//                    .click(58290) // 用户点击时上报
//                    .put("currPage", "homePageV2")
//                    .createTrace()
//            }
//        }
//    }
//
//    override fun getItemCount(): Int {
//        return mList?.size ?: 0
//    }
//
//    override fun getItem(position: Int): DPDrama? {
//        if (position >= mList?.size ?: 0) return null
//        return mList?.get(position)
//    }
//
//    class SkitsGridRowViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
//        var rootView: View = view.findViewById(R.id.main_cl_root)
//        var tvTitle: TextView = view.findViewById(R.id.main_tv_title)
//        var ivCover: RoundImageView = view.findViewById(R.id.main_iv_cover)
//    }
//}