package com.ximalaya.ting.lite.main.history.presenter

import com.ximalaya.ting.android.host.db.model.BookHistoryInfo
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack

interface IBookHistoryPresenter {
    /**
     * 加载数据
     */
    fun loadData();

    /**
     * 加载更多
     */
    fun loadMore();

    /**
     * 清除所有历史记录
     */
    fun clearAllBookHistoryRecord(callback: IDataCallBack<Boolean>?)

    /**
     * 同步记录
     */
    fun syncBookHistoryModifyRecord()

    /**
     * 查询历史数量
     */
    fun queryBookHistoryAmount():Int

    /**
     * 获取最近一条记录
     */
    fun getRecentHistory() : BookHistoryInfo?
}