package com.ximalaya.ting.lite.main.download;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import java.util.List;

/**
 * Created by <PERSON> on 2018/1/23.
 * <AUTHOR>
 */

public class BatchDownloadAdapter extends HolderAdapter<Track> {
    public static final int CHOOSE_ALL_DOWNLOAD_ALL = 11;
    public static final int CHOOSE_ALL_DOWNLOAD_PART = 22;
    public static final int CHOOSE_PART_DOWNLOAD_PART = 33;
    private AlbumM albumM;

    public BatchDownloadAdapter(Context context, List<Track> listData) {
        super(context, listData);
    }

    public void setAlbumM(AlbumM albumM) {
        this.albumM = albumM;
    }

    @Override
    public void onClick(View view, Track track, int position, BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_album_download_batch1;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, Track track, int position) {

        if(track == null){
            return;
        }

        ViewHolder vh = (ViewHolder) holder;
        boolean isChecked = track.getExtra();
        vh.tvTitle.setText(track.getTrackTitle());
        vh.cbTrack.setSelected(isChecked);
        if (track instanceof TrackM) {
            TrackM tm = (TrackM) track;
            vh.tvNumber.setText(String.valueOf(tm.getOrderNo()));
        }
        vh.tvSize.setText(StringUtil.getFriendlyFileSize(track.getDownloadSize()));
        if (RouteServiceUtil.getDownloadService().isAddToDownload(track) || isTrackLock(track)) {
            vh.tvTitle.setTextColor(Color.parseColor("#999999"));
            vh.cbTrack.setBackgroundResource(R.drawable.host_checkbox_disable);
        } else {
            vh.tvTitle.setTextColor(Color.parseColor("#333333"));
            vh.cbTrack.setBackgroundResource(R.drawable.main_batch_down_selector);
        }
    }

    /**
     * 判断该声音是不是锁定状态，和PaidTrackAdapter一样的判断条件
     */
    public boolean isTrackLock(Track track) {
        if(albumM != null && track != null) {
            // VIP专辑: 试听声音为非锁定状态，非试听声音为锁定状态
            if(albumM.isVipFree() || albumM.getVipFreeType() == 1) {
                return !(track.isFree() ||
                        track.isAuthorized());
            }
            // 精品专辑: 整张已购买、单集已购买、限时免费听的声音展示非锁定状态
            else if(albumM.isPaid()) {
                return !(track.isAuthorized() ||
                        track.isFree());
            }
            // 免费专辑：对非会员：抢先听的声音加标签且加锁
            else {
                return !UserInfoMannage.isVipUser() && track.vipPriorListenStatus == 1;
            }
        }
        return false;
    }



    public int getChooseStatus() {
        int choose = 0;
        int download = 0;
        if (!ToolUtil.isEmptyCollects(listData)) {
            for (Track track : listData) {
                if (track.getExtra()) {
                    choose++;
                }
                if (RouteServiceUtil.getDownloadService().isAddToDownload(track)) {
                    download++;
                }
            }
            if (choose == listData.size() && choose == download) {
                return CHOOSE_ALL_DOWNLOAD_ALL;
            } else if (choose == listData.size() && choose != download) {
                return CHOOSE_ALL_DOWNLOAD_PART;
            } else {
                return CHOOSE_PART_DOWNLOAD_PART;
            }
        } else {
            return CHOOSE_PART_DOWNLOAD_PART;
        }
    }

    public void checkAll() {
        if (getCount() > 0) {
            for (Track track : listData) {
                if (!RouteServiceUtil.getDownloadService().isAddToDownload(track) && !isTrackLock(track)) {
                    track.setExtra(true);
                }
            }
            notifyDataSetChanged();
        }
    }

    public void unSelectNoPermissionItems() {
        if (getCount() > 0) {
            for (Track track : listData) {
                if (!UserInfoMannage.isVipUser()
                        && track.vipPriorListenStatus == 1) {
                    track.setExtra(false);
                }
            }
        }
    }

    public void checkNone() {
        if (getCount() > 0) {
            for (Track track : listData) {
                if (!RouteServiceUtil.getDownloadService().isAddToDownload(track) && !isTrackLock(track)) {
                    track.setExtra(false);
                }
            }
            notifyDataSetChanged();
        }
    }

    class ViewHolder extends BaseViewHolder {
        TextView tvNumber;
        TextView tvTitle;
        TextView tvSize;
        View cbTrack;

        public ViewHolder(View convertView) {
            tvNumber = (TextView) convertView.findViewById(R.id.main_number);
            tvSize = (TextView) convertView.findViewById(R.id.main_size);
            tvTitle = (TextView) convertView.findViewById(R.id.main_item_album_down_title);
            cbTrack = convertView.findViewById(R.id.main_checkbox);
        }
    }
}
