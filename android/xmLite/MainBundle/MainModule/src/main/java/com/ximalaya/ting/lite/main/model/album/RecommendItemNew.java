package com.ximalaya.ting.lite.main.model.album;

import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/5/28.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class RecommendItemNew {

    public static final String RECOMMEND_ITEM_MODULE = "MODULE"; // 模块
    public static final String RECOMMEND_ITEM_TRACK = "TRACK"; // 声音
    public static final String RECOMMEND_ITEM_CUSTOMIZE_SELF_RENDERING_AD_SMALL = "customize_self_rendering_ad_small"; // 自己传入的广告小图
    public static final String RECOMMEND_ITEM_CUSTOMIZE_SELF_RENDERING_AD_BIG = "customize_self_rendering_ad_big"; // 自己传入的广告大图
    public static final String RECOMMEND_ITEM_ALBUM = "ALBUM"; // 专辑
    public static final String RECOMMEND_ITEM_LIVE = "LIVE"; // 直播
    public static final String RECOMMEND_SPECIAL = "SPECIAL"; // 听单
    public static final String RECOMMEND_NEW_SPECIAL = "NEW_SPECIAL"; // 新听单
    public static final String RECOMMEND_INTEREST_CARD = "INTEREST_CARD"; // 兴趣卡片
    public static final String RECOMMEND_VIDEO = "VIDEO"; // 视频大图类型
    public static final String RECOMMEND_LAST_SEEN_HERE = "LAST_SEEN_HERE"; // 上次听到这的提示条，客户端使用，服务端没有这个类型
    public static final String RECOMMEND_ONE_KEY_LISTEN = "ONE_KEY_LISTEN_SCENE"; // 一键听
    public static final String RECOMMEND_ITEM_COLLECTION = "COLLECTION"; // 专辑系列
    public static final String RECOMMEND_HOT_COMMENT = "HOT_COMMENT"; // 热评

    public static final String RECOMMEND_TYPE_LOCAL_REAL_TIME_RECOMMEND = "localRealTimeRecommend"; // 本地添加的类型（不是来自服务端），实时推荐强露出模块
    public static final String RECOMMEND_TYPE_LOCAL_AD_CUSTOM = "localAdCustom"; // 本地添加的类型（不是来自服务端），激励视频引导
    public static final String RECOMMEND_TYPE_LOCAL_AD_CUSTOM_STYLE3 = "localAdCustomStyle3"; // 本地添加的类型（不是来自服务端），激励视频引导样式3
    public static final String RECOMMEND_TYPE_LOCAL_AD_CUSTOM_STYLE4 = "localAdCustomStyle4"; // 本地添加的类型（不是来自服务端），激励视频引导样式3

    private String itemType;
    private Object item;

    private String srcTitle; // 来源tab，埋点用
    private long tabId; // 所在子tab的id，埋点用
    private int pageId; // 这个item所在页码，埋点用

    private int moduleId;
    private String moduleTitle;

    public static RecommendItemNew parseJson(JSONObject jsonObject, Gson gson) {
        try {
            RecommendItemNew recommendItemNew = new RecommendItemNew();
            if (jsonObject != null) {
                recommendItemNew.setItemType(jsonObject.optString("itemType"));
                switch (recommendItemNew.getItemType()) {
                    case RECOMMEND_ITEM_TRACK:
                        recommendItemNew.setItem(new RecommendTrackItem(jsonObject.optString("item")));
                        break;
                    case RECOMMEND_ITEM_ALBUM:
                        recommendItemNew.setItem(new RecommendAlbumItem(jsonObject.optString("item")));
                        break;
                    default:
                        // 不是上面的类型的，目前不用，返回null。
                        return null;
                }
                return recommendItemNew;
            }
        } catch (Exception e) {
            // catch住所有异常，避免一个数据解析时出错，导致所有数据无法加载
            e.printStackTrace();
        }
        return null;
    }

    public static List<RecommendItemNew> parseRecommendItemList(JSONArray jsonArray) {
        return parseRecommendItemList(jsonArray, null);
    }

    public static List<RecommendItemNew> parseRecommendItemList(JSONArray jsonArray, String newUserTopBannerGroup) {
        Gson gson = new Gson();
        List<RecommendItemNew> list = new ArrayList<>();
        if (jsonArray != null && jsonArray.length() > 0) {
            for (int i = 0; i < jsonArray.length(); i++) {
                RecommendItemNew recommendItem = RecommendItemNew.parseJson(jsonArray.optJSONObject(i), gson);
                if (recommendItem != null) {
                    list.add(recommendItem);
                }
            }
        }
        return list;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public Object getItem() {
        return item;
    }

    public void setItem(Object item) {
        this.item = item;
    }

    public String getSrcTitle() {
        return srcTitle;
    }

    public void setSrcTitle(String srcTitle) {
        this.srcTitle = srcTitle;
    }

    public int getPageId() {
        return pageId;
    }

    public void setPageId(int pageId) {
        this.pageId = pageId;
    }

    public long getTabId() {
        return tabId;
    }

    public void setTabId(long tabId) {
        this.tabId = tabId;
    }

    public int getModuleId() {
        return moduleId;
    }

    public void setModuleId(int moduleId) {
        this.moduleId = moduleId;
    }

    public String getModuleTitle() {
        return moduleTitle;
    }

    public void setModuleTitle(String moduleTitle) {
        this.moduleTitle = moduleTitle;
    }

    public RecommendItemNew() {

    }
}
