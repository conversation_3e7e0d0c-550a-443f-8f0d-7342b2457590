package com.ximalaya.ting.lite.main.earn.dialog;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.manager.feedback.CustomerFeedBackManager;
import com.ximalaya.ting.android.host.manager.track.MarkGiveGuideManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * 好评引导弹框
 * <p>
 * 主题2
 */
public class MarketGuideThemeV2Dialog extends XmBaseDialog {

    private Activity mActivity;

    public MarketGuideThemeV2Dialog(@NonNull Activity context) {
        super(context);
        mActivity = context;
    }

    public MarketGuideThemeV2Dialog(@NonNull Activity context, int themeResId) {
        super(context, themeResId);
        mActivity = context;
    }

    protected MarketGuideThemeV2Dialog(@NonNull Activity context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        mActivity = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //必须放在setContentView之前，如果使用getDecorView,也要放在getDecorView之前
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        if (getWindow() != null) {
            getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            WindowManager.LayoutParams lp = getWindow().getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            lp.gravity = Gravity.CENTER;
            getWindow().setAttributes(lp);
        }
        setCanceledOnTouchOutside(false);
        setCancelable(true);
        setContentView(R.layout.main_dialog_market_theme_v2);
        initUI();
    }

    private void initUI() {
        //关闭按钮
        RelativeLayout rlMarketGuideClose = findViewById(R.id.main_rl_market_guide_close);
        //引导图
        ImageView ivMarketGuideImage = findViewById(R.id.main_iv_market_guide_image);
        //标题
        TextView tvMarketGuideTitle = findViewById(R.id.main_tv_market_guide_title);
        //描述
        TextView tvMarketGuideDesc = findViewById(R.id.main_tv_market_guide_desc);
        //提建议
        TextView tvMarketGuideFeedback = findViewById(R.id.main_tv_market_guide_feedback);
        //给好评
        TextView tvMarketGuideGiveScore = findViewById(R.id.main_tv_market_guide_give_score);


        ivMarketGuideImage.setImageResource(R.drawable.main_icon_market_guide_cute);
        tvMarketGuideTitle.setText("卖萌求鼓励");
        tvMarketGuideDesc.setText("喜欢“喜马拉雅极速版”吗？给个好评鼓励一下吧~");
        tvMarketGuideFeedback.setText("提点建议");
        tvMarketGuideGiveScore.setText("好评鼓励");

        //关闭点击
        rlMarketGuideClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                //关闭埋点
                traceClose();
            }
        });

        //提建议点击
        tvMarketGuideFeedback.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //打开意见反馈入口
                CustomerFeedBackManager.jumpToFeedBackChatIssueSubmit();
                //已经打开过意见反馈了，必须放在dismiss之前进行保存
                MarkGiveGuideManager.saveMarketGiveGuideFinishForAll();
                dismiss();
                //提点建议埋点
                traceFeedback();
            }
        });

        //给好评点击
        tvMarketGuideGiveScore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    Intent intent = new Intent(Intent.ACTION_VIEW);
                    String uri = "market://details?id=" + mActivity.getPackageName();
                    intent.setData(Uri.parse(uri));
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    mActivity.startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                    CustomToast.showFailToast("打开应用商店失败，请手动前往！");
                }
                dismiss();
                //已经跳转过应用市场了
                MarkGiveGuideManager.saveMarketGiveGuideFinishForAll();

                //好评鼓励埋点
                traceGiveScore();
            }
        });

        AutoTraceHelper.bindData(rlMarketGuideClose, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(tvMarketGuideFeedback, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(tvMarketGuideGiveScore, AutoTraceHelper.MODULE_DEFAULT, "");

        //曝光埋点
        traceShow();
    }

    private void traceFeedback() {
        new XMTraceApi.Trace()
                .setMetaId(31545)
                .setServiceId("dialogClick")
                .put("item", "提点建议")
                .createTrace();
    }

    private void traceGiveScore() {
        new XMTraceApi.Trace()
                .setMetaId(31545)
                .setServiceId("dialogClick")
                .put("item", "好评鼓励")
                .createTrace();
    }

    private void traceShow() {
        new XMTraceApi.Trace()
                .setMetaId(31544)
                .setServiceId("dialogView")
                .createTrace();
    }

    private void traceClose() {
        new XMTraceApi.Trace()
                .setMetaId(31546)
                .setServiceId("dialogClick")
                .createTrace();
    }
}
