package com.ximalaya.ting.lite.main.home.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.adapter.HolderAdapter
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.model.album.HomeAllCategoryModel

/**
 * Created by qinhuifeng on 2021/1/11
 *
 */
class HomeAllCategoryListProvider(val baseFragment2: BaseFragment2) : IMulitViewTypeViewAndData<HomeAllCategoryListProvider.Holder, HomeAllCategoryModel> {

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup?): View {
        return layoutInflater.inflate(R.layout.main_item_home_all_category_list, null)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView)
    }


    override fun bindViewDatas(holder: Holder?, dataList: ItemModel<HomeAllCategoryModel>?, convertView: View?, position: Int) {
        if (holder == null || dataList == null || dataList.getObject() == null) {
            return
        }
        val itemDataList = dataList.getObject()
        if (itemDataList.categories == null || itemDataList.categories.size == 0) {
            return
        }
        //设置标题
        holder.tvModelTile.text = itemDataList.moduleName

        //没有设置过adapter，进行设置
        if (holder.rvList.adapter == null) {
            val rvAdapter = HomeAllCategoryRvItemAdapter(baseFragment2, ArrayList())
            holder.rvList.isNestedScrollingEnabled = false
            holder.rvList.layoutManager = GridLayoutManager(baseFragment2.context, 4)
            holder.rvList.setItemViewCacheSize(10)
            holder.rvList.adapter = rvAdapter
        }
        val adapter = holder.rvList.adapter
        if (adapter !is HomeAllCategoryRvItemAdapter) {
            return
        }
        val valueList = adapter.valueList
        valueList.clear()
        valueList.addAll(itemDataList.categories)
    }

    class Holder(val convertView: View) : HolderAdapter.BaseViewHolder() {
        var tvModelTile: TextView
        var rvList: RecyclerView

        init {
            tvModelTile = convertView.findViewById(R.id.main_module_title)
            rvList = convertView.findViewById(R.id.main_rv_list)
        }
    }
}