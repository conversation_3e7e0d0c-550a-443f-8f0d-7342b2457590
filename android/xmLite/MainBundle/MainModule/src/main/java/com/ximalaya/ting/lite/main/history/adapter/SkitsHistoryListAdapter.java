package com.ximalaya.ting.lite.main.history.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.db.model.SkitsHistoryInfo;
import com.ximalaya.ting.android.host.util.common.TimeHelper;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;

import org.jetbrains.annotations.NotNull;

import java.util.List;

public class SkitsHistoryListAdapter extends AbRecyclerViewAdapter<RecyclerView.ViewHolder> {

    private final Context mContext;
    private final List<SkitsHistoryInfo> mList;

    private onSkitsClickListener mSkitsListener;

    public SkitsHistoryListAdapter(Context context, List<SkitsHistoryInfo> list) {
        mList = list;
        mContext = context;
    }

    public void setOnSkitsClickListener(onSkitsClickListener listener) {
        this.mSkitsListener = listener;
    }


    @NotNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_item_skits_history_layout, parent, false);

        return new SkitsHistoryListAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (mList == null || position < 0 || position >= mList.size()) {
            return;
        }
        final SkitsHistoryInfo skitsHistoryInfo = mList.get(position);
        if (skitsHistoryInfo == null) {
            return;
        }

        if (holder instanceof ViewHolder) {
            ViewHolder viewHolder = (ViewHolder) holder;

            viewHolder.itemView.setOnClickListener(v -> {
                if (mSkitsListener != null) {
                    mSkitsListener.onClickItems(position);
                }
            });

            viewHolder.ivDel.setOnClickListener(v -> {
                if (mSkitsListener != null) {
                    mSkitsListener.onDelItem(position);
                }
            });

            viewHolder.tvTitle.setText(skitsHistoryInfo.getVideoTitle());
            viewHolder.tvSubTitle.setText(skitsHistoryInfo.getTrackTitle());
            viewHolder.tvTime.setText(String.format("%s/%s", TimeHelper.toTime(skitsHistoryInfo.getCurTime()), TimeHelper.toTime(skitsHistoryInfo.getTotalTime())));
            ImageManager.from(mContext).displayImage(viewHolder.ivCover, skitsHistoryInfo.getCover(), R.drawable.main_skits_cove_bg);

            boolean isOffShelf = skitsHistoryInfo.isOffShelf();
            if (isOffShelf) {
                viewHolder.tvOffShelfCover.setVisibility(View.VISIBLE);
            } else {
                viewHolder.tvOffShelfCover.setVisibility(View.GONE);
            }

            setOffShelf(viewHolder.tvTitle, isOffShelf);
            setOffShelf(viewHolder.tvSubTitle, isOffShelf);
            setOffShelf(viewHolder.tvTime, isOffShelf);
            setOffShelf(viewHolder.ivDel, isOffShelf);
        }
    }

    private void setOffShelf(View view, boolean isOffShelf) {
        if (view != null) {
            view.setAlpha(isOffShelf ? 0.3f : 1f);
        }
    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }

    @Override
    public Object getItem(int position) {
        if (mList != null && position >= 0 && mList.size() > position) {
            return mList.get(position);
        }
        return null;
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {
        View itemView;
        TextView tvTitle;
        TextView tvSubTitle;
        ImageView ivCover;
        TextView tvOffShelfCover;
        TextView tvCount;
        TextView tvTime;
        ImageView ivDel;

        public ViewHolder(@NonNull View view) {
            super(view);
            itemView = view.findViewById(R.id.main_skits_root);
            tvTitle = view.findViewById(R.id.main_skits_tv_title);
            tvSubTitle = view.findViewById(R.id.main_skits_tv_sub_title);
            ivCover = view.findViewById(R.id.main_skits_iv_cover);
            tvOffShelfCover = view.findViewById(R.id.main_skits_tv_img_cover);
            tvCount = view.findViewById(R.id.main_skits_tv_count);
            tvTime = view.findViewById(R.id.main_skits_tv_time);
            ivDel = view.findViewById(R.id.main_skits_iv_del);
        }
    }

    public interface onSkitsClickListener {
        void onClickItems(int position);

        void onDelItem(int position);
    }
}
