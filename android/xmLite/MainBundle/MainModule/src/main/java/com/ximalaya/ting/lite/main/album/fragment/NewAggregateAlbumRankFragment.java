package com.ximalaya.ting.lite.main.album.fragment;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.album.adapter.NewAggregateAlbumRankAdapter;
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter;
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder;
import com.ximalaya.ting.lite.main.model.rank.GroupRankAlbumList;
import com.ximalaya.ting.lite.main.model.rank.NewRankGroup;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by dumingwei on 2021/1/8
 * <p>
 * Desc:新的榜单界面
 */
public class NewAggregateAlbumRankFragment extends BaseFragment2 implements AdapterView.OnItemClickListener, ILoginStatusChangeListener, AlbumEventManage.CollectListener {

    private static final String TAG = "NewAggregateAlbumRankFr";

    public static final String ARGS_CLUSTER_TYPE = "args_cluster_type";
    public static final String ARGS_SELECT_RANK_LIST_ID = "args_select_rank_list_Id";

    //分类列表
    private RecyclerView mRvCategory;
    private List<NewRankGroup.SingleCategory> mCategoryList = new ArrayList<>();
    private MultiRecyclerAdapter<NewRankGroup.SingleCategory, SuperRecyclerHolder> mRvRankCateGoryAdapter;
    private long mClusterType = 0;
    private long mSelectRankListId = 0;

    private boolean mIsLoading = false;
    private RefreshLoadMoreListView mListView;
    private NewAggregateAlbumRankAdapter mAdapter;
    private int mPageId = 1;
    private long mTotalCount = -1;

    private boolean mIsFirstVisible = true;
    private boolean mIsFirstResume = true;

    public static Bundle newArgument(long clusterType, long selectRankListId) {
        Bundle bundle = new Bundle();
        bundle.putLong(ARGS_CLUSTER_TYPE, clusterType);
        bundle.putLong(ARGS_SELECT_RANK_LIST_ID, selectRankListId);
        return bundle;
    }

    @Override
    public int getTitleBarResourceId() {
        return -1;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_new_aggregate_album_rank;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mClusterType = arguments.getLong(ARGS_CLUSTER_TYPE, 0);
            mSelectRankListId = arguments.getLong(ARGS_SELECT_RANK_LIST_ID, 0);
        }
        mRvCategory = findViewById(R.id.main_rv_category);

        initRvCategoryAdapter();

        mListView = findViewById(R.id.main_list_rank);
        mListView.setOnRefreshLoadMoreListener(new IRefreshLoadMoreListener() {
            @Override
            public void onRefresh() {
                mPageId = 1;
                requestAlbumList();
            }

            @Override
            public void onMore() {
                requestAlbumList();
            }
        });
        mListView.getRefreshableView().setPadding(0, 0, 0, getResourcesSafe().getDimensionPixelSize(R.dimen.host_bottom_bar_height));
        mListView.getRefreshableView().setClipToPadding(false);
        mListView.setOnItemClickListener(this);
        mAdapter = new NewAggregateAlbumRankAdapter(this, (MainActivity) mActivity, null);
        mListView.setAdapter(mAdapter);

        //添加登录监听
        UserInfoMannage.getInstance().addLoginStatusChangeListener(this);
        //注册专辑订阅监听
        AlbumEventManage.addListener(this);

    }

    private void initRvCategoryAdapter() {
        mRvRankCateGoryAdapter = new MultiRecyclerAdapter<NewRankGroup.SingleCategory, SuperRecyclerHolder>(mActivity, mCategoryList) {

            @Override
            public SuperRecyclerHolder createMultiViewHolder(Context mCtx, @NonNull View itemView, int viewType) {
                return SuperRecyclerHolder.createViewHolder(mCtx, itemView);
            }

            @Override
            public void onBindMultiViewHolder(SuperRecyclerHolder holder, NewRankGroup.SingleCategory categoryModel, int viewType, int position) {
                holder.setText(R.id.main_rank_category_title_item, categoryModel.getRankingListName());
                if (mSelectRankListId == categoryModel.getRankingListId()) {
                    holder.setVisibility(R.id.main_rank_category_title_item_selected, View.VISIBLE);
                    holder.setTextColor(R.id.main_rank_category_title_item, Color.parseColor("#ff4646"));
                    holder.setTextViewTypeface(R.id.main_rank_category_title_item, Typeface.DEFAULT_BOLD);
                    holder.setBackgroundResource(R.id.main_rank_category_title_item_layout, R.drawable.main_bg_ffffff_f0f0f0);
                } else {
                    holder.setVisibility(R.id.main_rank_category_title_item_selected, View.INVISIBLE);
                    holder.setTextColor(R.id.main_rank_category_title_item, Color.parseColor("#666666"));
                    holder.setTextViewTypeface(R.id.main_rank_category_title_item, Typeface.DEFAULT);
                    holder.setBackgroundColor(R.id.main_rank_category_title_item_layout, Color.parseColor("#ffffff"));
                }
                holder.setOnItemClickListenner(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (!OneClickHelper.getInstance().onClick(view)) {
                            return;
                        }
                        if (mSelectRankListId == categoryModel.getRankingListId()) {
                            return;
                        }

                        // 听书排行榜（代码）-左侧榜单  点击事件
                        new XMTraceApi.Trace()
                                .click(45523)
                                .put("tabName", categoryModel.getRankingListName())
                                .put("currPage", "BookLeaderboards")
                                .createTrace();

                        mSelectRankListId = categoryModel.getRankingListId();
                        mRvRankCateGoryAdapter.notifyDataSetChanged();
                        //选中的item发生变化，重新请求
                        mPageId = 1;
                        requestAlbumList();
                    }
                });
            }

            @Override
            public int getMultiItemViewType(NewRankGroup.SingleCategory model, int position) {
                return 0;
            }

            @Override
            public int getMultiItemLayoutId(int viewType) {
                return R.layout.main_item_aggregate_rank_category_title;
            }

        };
        mRvCategory.setAdapter(mRvRankCateGoryAdapter);
        LinearLayoutManager layoutManager = new LinearLayoutManager(mActivity);
        mRvCategory.setLayoutManager(layoutManager);

        mCategoryList.clear();
        Fragment parentFragment = getParentFragment();
        //添加分类
        if (parentFragment instanceof NewAggregateRankFragment) {
            NewRankGroup listTabsDataItemRsp = ((NewAggregateRankFragment) parentFragment).getAggregateRankCategoryModelByClusterType(mClusterType);
            if (listTabsDataItemRsp != null && CollectionUtil.isNotEmpty(listTabsDataItemRsp.getRankingLists())) {
                mCategoryList.addAll(listTabsDataItemRsp.getRankingLists());
                mRvRankCateGoryAdapter.notifyDataSetChanged();
            }
        }
        int selectPosition = -1;
        for (int i = 0; i < mCategoryList.size(); i++) {
            NewRankGroup.SingleCategory categoryModel = mCategoryList.get(i);
            if (categoryModel == null) {
                continue;
            }
            if (mSelectRankListId == categoryModel.getRankingListId()) {
                selectPosition = i;
                break;
            }
        }
        //没有查到选中的item，默认选中0,mSelectRankClusterId选中0号元素
        if (selectPosition == -1) {
            selectPosition = 0;
            if (mCategoryList.size() > 0) {
                NewRankGroup.SingleCategory model = mCategoryList.get(0);
                if (model != null) {
                    mSelectRankListId = model.getRankingListId();
                }
            }
        }
        layoutManager.scrollToPosition(selectPosition);
    }

    @Override
    protected void loadData() {
        if (mCategoryList.size() == 0) {
            mRvCategory.setVisibility(View.INVISIBLE);
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
            return;
        }
        //请求排行榜专辑内容
        requestAlbumList();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            if (mIsFirstVisible) {
                mIsFirstVisible = false;
            } else {
                if (mAdapter != null) {
                    mAdapter.notifyDataSetChanged();
                }
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mIsFirstResume) {
            mIsFirstResume = false;
        }
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        if (!mIsFirstResume) {
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
    }

    private void requestAlbumList() {
        if (mIsLoading) {
            return;
        }
        if (mPageId == 1) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        mIsLoading = true;
        HashMap<String, String> p = new HashMap<>();
        p.put("pageId", String.valueOf(mPageId));
        p.put("pageSize", "20");
        p.put("rankingListId", String.valueOf(mSelectRankListId));

        LiteCommonRequest.getNewRankGroupAlbumList(p, new IDataCallBack<GroupRankAlbumList>() {
            @Override
            public void onSuccess(@Nullable final GroupRankAlbumList object) {
                mIsLoading = false;
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        if (object == null) {
                            checkAndShowNetError();
                            return;
                        }
                        mTotalCount = object.totalCount;
                        if (object.list == null) {
                            checkAndShowNetError();
                            return;
                        }
                        if (mPageId == 1) {
                            //这个是检查切换不同的Category，分页加载的时候返回的列表是否为空列表
                            if (object.list.isEmpty()) {
                                if (mAdapter != null) {
                                    mAdapter.clear();
                                }
                                mListView.onRefreshComplete();
                                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                                return;
                            }
                        } else {
                            //这个是检查同一个Category，分页加载的时候返回的列表是否为空列表
                            checkAndShowNoContent();
                        }
                        if (mPageId == 1) {
                            mAdapter.clear();
                        }
                        mAdapter.addListData(object.list);
                        if (mPageId == 1) {
                            mListView.getRefreshableView().setSelection(0);
                        }
                        List<Album> listData = mAdapter.getListData();
                        if (listData != null) {
                            if (listData.size() < mTotalCount) {
                                //还有下一页
                                mPageId++;
                                mListView.onRefreshComplete(true);
                            } else {
                                //没有下一页了
                                mListView.onRefreshComplete(false);
                            }
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                mIsLoading = false;
                if (!canUpdateUi()) {
                    return;
                }
                checkAndShowNetError();
            }
        });
    }

    private void checkAndShowNetError() {
        if (mAdapter == null) {
            return;
        }
        List<Album> listData = mAdapter.getListData();
        if (listData != null && listData.size() == 0) {
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        }
    }


    private void checkAndShowNoContent() {
        if (mAdapter == null) {
            return;
        }
        List<Album> listData = mAdapter.getListData();
        if (listData != null && listData.size() == 0) {
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
        }
    }

    @Override
    protected void addLoadStateView(ViewGroup parent, View addView, ViewGroup.LayoutParams lp, LoadCompleteType type) {
        if (type == LoadCompleteType.LOADING || type == LoadCompleteType.NETWOEKERROR) {
            if (lp instanceof FrameLayout.LayoutParams) {
                //大概让view居中
                ((FrameLayout.LayoutParams) lp).leftMargin = BaseUtil.dp2px(getContext(), 35);
                ((FrameLayout.LayoutParams) lp).topMargin = BaseUtil.dp2px(getContext(), -70);
            }
        }
        super.addLoadStateView(parent, addView, lp, type);
    }

    @Override
    protected String getPageLogicName() {
        return "NewAggregateAlbumRankFragment";
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        List<Album> listData = mAdapter.getListData();
        if (listData == null || listData.size() == 0) {
            return;
        }
        if (index >= 0 && index < mAdapter.getCount()) {
            Album album = mAdapter.getListData().get(index);
            if (!(album instanceof AlbumM)) {
                return;
            }

            // 听书排行榜（代码）-专辑item  点击事件
            new XMTraceApi.Trace()
                    .click(45524)
                    .put("albumId", String.valueOf(album.getId()))
                    .put("currPage", "BookLeaderboards")
                    .createTrace();

            AlbumM albumM = (AlbumM) album;
            AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_OTHER, ConstantsOpenSdk.PLAY_FROM_RANK, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        //移除登录监听
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(this);
        //注册专辑订阅监听
        AlbumEventManage.removeListener(this);
    }

    /**********************登录监听start***************************************/

    @Override
    public void onLogout(LoginInfoModelNew olderUser) {
        if (mListView != null) {
            mListView.setRefreshing(true);
        }
    }

    @Override
    public void onLogin(LoginInfoModelNew model) {
        if (mListView != null) {
            mListView.setRefreshing(true);
        }
    }

    @Override
    public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {

    }


    /**********************登录监听end***************************************/

    /**
     * 收藏监听
     *
     * @param collect
     * @param id
     */
    @Override
    public void onCollectChanged(boolean collect, long id) {
        //可见的时候，不处理，adapter中已经处理了订阅
        if (isRealVisable()) {
            return;
        }
        if (mAdapter == null) {
            return;
        }
        List<Album> listData = mAdapter.getListData();
        if (listData == null || listData.size() == 0) {
            return;
        }
        for (int i = 0; i < listData.size(); i++) {
            Album album = listData.get(i);
            if (album == null) {
                continue;
            }
            //查到了当前变化,刷新一次数据
            if (album.getId() == id && album.getId() > 0) {
                if (album instanceof AlbumM) {
                    ((AlbumM) album).setFavorite(collect);
                    mAdapter.notifyDataSetChanged();
                }
                break;
            }
        }
    }
}
