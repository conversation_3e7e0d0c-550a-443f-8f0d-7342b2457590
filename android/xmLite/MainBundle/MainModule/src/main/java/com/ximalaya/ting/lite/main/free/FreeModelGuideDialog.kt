package com.ximalaya.ting.lite.main.free

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew.unlockConfigModel
import com.ximalaya.ting.android.host.util.ContextUtils
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi


class FreeModelGuideDialog : BaseFullScreenDialogFragment() {

    private var tvTitle: TextView? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.main_dia_free_model_layout, container, false)
        initUi(view)
        return view
    }

    private fun initUi(rootView: View?) {
        rootView?.findViewById<View?>(R.id.main_iv_close)?.setOnClickListener {
            dismiss()
            // 首次领取畅听时长弹窗-点击关闭  弹框控件点击
            XMTraceApi.Trace()
                .setMetaId(51195)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("currPage", "homePageV2")
                .createTrace()
        }
        rootView?.findViewById<View?>(R.id.main_tv_confirm)?.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            dismiss()
            val activity = BaseApplication.getMainActivity()
            if (ContextUtils.checkActivity(activity) && activity is FragmentActivity) {
                FreeModelReceiveDialog().show(activity.supportFragmentManager, "FreeModelReceiveDialog")
            }
            // 首次领取畅听时长弹窗-点击领取  弹框控件点击
            XMTraceApi.Trace()
                .setMetaId(51193)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("currPage", "homePageV2")
                .createTrace()
        }
        tvTitle = rootView?.findViewById(R.id.main_tv_title)

        showTimeUi()

        // 首次领取畅听时长弹窗  弹框展示
        XMTraceApi.Trace()
            .setMetaId(51192)
            .setServiceId("dialogView") // 弹窗展示时上报
            .put("currPage", "homePageV2")
            .createTrace()
    }

    private fun showTimeUi() {
        val config = unlockConfigModel
        if (config != null) {
            val text = formatAvailableListeningTime(config.bufferDailyComplimentaryListenDuration!!)
            tvTitle?.text = text
        }
    }

    /**
     * 格式化可用收听时长
     *
     * @param time 可用收听时长 单位秒
     */
    private fun formatAvailableListeningTime(time: Long): String {
        if (time <= 0L) {
            return "全场免费畅听0分钟"
        }
        // 计算出分钟
        var minutes = time / 60f
        // 计算多余的秒数
        val second = time % 60f
        // 秒数不为空  分钟数+1
        minutes = if (second == 0f) minutes else minutes + 1

        return "全场免费畅听${minutes.toInt()}分钟"
    }
}