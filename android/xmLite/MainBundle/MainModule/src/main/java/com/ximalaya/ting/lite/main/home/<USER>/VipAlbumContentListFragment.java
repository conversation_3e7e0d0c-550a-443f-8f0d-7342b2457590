package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.FreeListenVipAlbumDetailFragment;
import com.ximalaya.ting.android.host.fragment.FreeListenVipAlbumRuleFragment;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.trace.TraceFreeAlbumManager;
import com.ximalaya.ting.android.host.util.StringCodeManager;
import com.ximalaya.ting.android.host.util.StringCodeUtils;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBackNew;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.UiUtil;
import com.ximalaya.ting.lite.main.base.album.UnLockVipAlbumAdapter;
import com.ximalaya.ting.lite.main.base.album.VipAlbumAdapter;
import com.ximalaya.ting.lite.main.model.album.AlbumShareModel;
import com.ximalaya.ting.lite.main.model.album.AlbumShareModelList;
import com.ximalaya.ting.lite.main.model.newhome.LiteFloorModel;
import com.ximalaya.ting.lite.main.newhome.fragment.LiteHomeRecommendFragment;
import com.ximalaya.ting.lite.main.record.fragment.UnLockRecordListFragment;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.request.LiteUrlConstants;
import com.ximalaya.ting.lite.main.tab.HomeFragment;
import com.ximalaya.ting.lite.main.utils.AlbumUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Vip专辑免费畅听
 *
 * <AUTHOR>
 */
public class VipAlbumContentListFragment extends BaseFragment2 implements ILoginStatusChangeListener, IRefreshLoadMoreListener, AdapterView.OnItemClickListener, View.OnClickListener {

    private RefreshLoadMoreListView mListView;
    private int mPageId = 1;
    private boolean mIsLoading = false;
    private VipAlbumAdapter mAdapter;
    private UnLockVipAlbumAdapter shareAdapter;
    private ImageView vNoContent;
    private View vHeadView;
    private List<List<AlbumM>> albumList = new ArrayList<>();
    private List<AlbumShareModel> shareList = new ArrayList<>();
    private ArrayList<AlbumM> homeToVipAlbum;
    private String title;
    private String schema;
    private AlbumM selectRecommendAlbum;
    private ListView loadMoreListView;
    private String mRuleContent;
    public static final String KEY_EXTRA_SCHEMA = "extra_schema";
    private int pooId;

    public static VipAlbumContentListFragment newInstance(String title, int poolId) {
        Bundle args = new Bundle();
        args.putString(BundleKeyConstants.KEY_TITLE, title);
        args.putInt(BundleKeyConstants.CONTENT_POOL_ID, poolId);
        args.putSerializable(BundleKeyConstants.KEY_EXTRA_DATA, poolId);
        VipAlbumContentListFragment fragment = new VipAlbumContentListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    public static VipAlbumContentListFragment newInstance(String title, int poolId, String schema) {
        Bundle args = new Bundle();
        args.putString(BundleKeyConstants.KEY_TITLE, title);
        args.putInt(BundleKeyConstants.CONTENT_POOL_ID, poolId);
        args.putSerializable(BundleKeyConstants.KEY_EXTRA_DATA, poolId);
        args.putSerializable(KEY_EXTRA_SCHEMA, schema);
        VipAlbumContentListFragment fragment = new VipAlbumContentListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    public static VipAlbumContentListFragment newInstance(String title, int poolId, List<AlbumM> albumMs) {
        Bundle args = new Bundle();
        args.putString(BundleKeyConstants.KEY_TITLE, title);
        args.putInt(BundleKeyConstants.CONTENT_POOL_ID, poolId);
        args.putParcelableArrayList(BundleKeyConstants.KEY_EXTRA_DATA, AlbumUtils.INSTANCE.toArrayList(albumMs));
        VipAlbumContentListFragment fragment = new VipAlbumContentListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    // 隐藏状态栏
    private void setVisibleTitleBar() {
        if (mContainerView != null && mContainerView instanceof ViewGroup) {
            View headView = ((ViewGroup) mContainerView).getChildAt(0);
            if (headView != null) {
                headView.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public int getTitleBarResourceId() {
        return -1;
    }


    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_vip_album_list;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            title = arguments.getString(BundleKeyConstants.KEY_TITLE);
            homeToVipAlbum = arguments.getParcelableArrayList(BundleKeyConstants.KEY_EXTRA_DATA);
            pooId = arguments.getInt(BundleKeyConstants.CONTENT_POOL_ID);
            schema = arguments.getString(KEY_EXTRA_SCHEMA);
        }
        if (CollectionUtil.isNullOrEmpty(homeToVipAlbum)) {
            homeToVipAlbum = getHomeRecommendAlbums();
        }
        //需要标题栏，设置为可滑动返回
        setCanSlided(true);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setVisibleTitleBar();
        mListView = findViewById(R.id.main_listview);
        LinearLayout mActivityRule = findViewById(R.id.main_ll_activity_rule);
        LinearLayout mActivityRecord = findViewById(R.id.main_ll_activity_record);
        ImageView mIvBack = findViewById(R.id.main_iv_vip_back);
        View ivBackContainer = findViewById(R.id.main_title_bar);
        TextView mTvTitle = findViewById(R.id.main_iv_vip_title);
        TextView mTvRule = findViewById(R.id.main_iv_vip_rule);
        mListView.setOnRefreshLoadMoreListener(this);
        mIvBack.setOnClickListener(this);
        mActivityRule.setOnClickListener(this);
        mActivityRecord.setOnClickListener(this);
        mTvRule.setOnClickListener(this);
        UserInfoMannage.getInstance().addLoginStatusChangeListener(this);
        mAdapter = new VipAlbumAdapter(mActivity, albumList, new IDataCallBackNew<AlbumM>() {

            @Override
            public void onSuccess(@Nullable AlbumM albumM, int status, String content) {
                if (!UserInfoMannage.hasLogined()) {
                    selectRecommendAlbum = albumM;
                    UserInfoMannage.gotoLogin(mActivity);
                    return;
                }
                if (albumM == null) return;
                LoginInfoModelNew infoModelNew = UserInfoMannage.getInstance().getUser();
                if (infoModelNew != null) {
                    jumpAlbumOrShareDetail(infoModelNew, albumM);
                }
                TraceFreeAlbumManager.INSTANCE.clickVipAlbumListItem(albumM.getId());
            }
        });
        initHeadView();
        initFooterView();
        mListView.setAdapter(mAdapter);
        mListView.setOnItemClickListener(this);
        mListView.addOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView absListView, int i) {

            }

            @Override
            public void onScroll(AbsListView absListView, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (firstVisibleItem >= 1) {
                    ivBackContainer.setBackgroundColor(ContextCompat.getColor(mActivity, R.color.white));
                    mIvBack.setImageResource(R.drawable.host_arrow_orange_normal_left_new);
                    mTvRule.setVisibility(View.VISIBLE);
                    mTvTitle.setVisibility(View.VISIBLE);
                    mActivityRule.setVisibility(View.INVISIBLE);
                    mActivityRecord.setVisibility(View.INVISIBLE);
                } else {
                    mActivityRecord.setVisibility(View.VISIBLE);
                    mActivityRule.setVisibility(View.VISIBLE);
                    mTvRule.setVisibility(View.INVISIBLE);
                    mTvTitle.setVisibility(View.INVISIBLE);
                    mIvBack.setImageResource(R.drawable.main_arrow_white_normal_left);
                    ivBackContainer.setBackgroundColor(ContextCompat.getColor(mActivity, R.color.host_transparent));
                }
            }
        });
        if (mTvTitle != null && title != null) {
            mTvTitle.setText(title);
        }
        refresh();
        try {
            if (!TextUtils.isEmpty(schema) && schema.contains(StringCodeUtils.FREE_LISTENER_VIP_ALBUM_I_TING)) {
                LoginInfoModelNew infoModelNew = UserInfoMannage.getInstance().getUser();
                boolean autoAssist = false;
                if (infoModelNew != null && infoModelNew.getUid() != 0) {
                    autoAssist = true;
                }
                if (getActivity() instanceof MainActivity) {
                    StringCodeManager.INSTANCE.openActivity(
                            (MainActivity) getActivity(), schema.replace(StringCodeUtils.UNLOCK_TYPE_PAGE_KEY_AND_VALUE, ""), autoAssist);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 进入专辑或者解锁详情页
     *
     * @param loginInfo
     * @param albumM
     */
    private void jumpAlbumOrShareDetail(LoginInfoModelNew loginInfo, AlbumM albumM) {
        startFragment(FreeListenVipAlbumDetailFragment.Companion.newInstance(albumM.getId(),
                albumM.getAlbumTitle(), albumM.getLargeCover()));
    }

    private void initHeadView() {
        vHeadView = View.inflate(mContext, R.layout.main_fra_home_vip_album_head_view, null);
        loadMoreListView = vHeadView.findViewById(R.id.list_head_view);
        shareAdapter = new UnLockVipAlbumAdapter(mActivity, shareList, new IDataCallBackNew<AlbumM>() {
            @Override
            public void onSuccess(@Nullable AlbumM albumM, int status, String content) {
                if (albumM == null) return;
                LoginInfoModelNew loginInfo = UserInfoMannage.getInstance().getUser();
                startFragment(FreeListenVipAlbumDetailFragment.Companion.newInstance(albumM.getId(),
                        albumM.getAlbumTitle(), albumM.getLargeCover()));
                TraceFreeAlbumManager.INSTANCE.clickVipAlbumLockItem(albumM.getId(), content);
            }

            @Override
            public void updateContainerHeight(@Nullable AlbumM albumM, boolean isHasMore, int num) {
                updateHeadListViewHeight(isHasMore, num);
            }
        });
        loadMoreListView.setAdapter(shareAdapter);
        mListView.getRefreshableView().addHeaderView(vHeadView);

    }

    private void updateHeadListViewHeight(boolean isHasMore, int num) {
        if (loadMoreListView == null) return;
        ViewGroup.LayoutParams params = loadMoreListView.getLayoutParams();
        float height = UiUtil.dp2px(157) + (num - 1) * UiUtil.dp2px(179) + UiUtil.dp2px(52);
        if (isHasMore) {
            height += UiUtil.dp2px(50);
        }
        params.height = (int) height;
        loadMoreListView.setLayoutParams(params);
    }

    private void initFooterView() {
        LinearLayout ll = new LinearLayout(getActivity());
        ll.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        ll.setGravity(Gravity.CENTER);
        vNoContent = new ImageView(getActivity());
        vNoContent.setPadding(0, BaseUtil.dp2px(mContext, 30), 0, 0);
        vNoContent.setImageResource(R.drawable.main_bg_meta_nocontent);
        ll.addView(vNoContent);
        vNoContent.setVisibility(View.GONE);
        mListView.getRefreshableView().addFooterView(ll);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        List<List<AlbumM>> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (index < 0 || index >= listData.size()) {
            return;
        }
        List<AlbumM> album = listData.get(index);
        if (!(album instanceof AlbumM)) {
            return;
        }
        AlbumM albumM = (AlbumM) album;
        AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
        TraceFreeAlbumManager.INSTANCE.freeVipAlbumPageIntoAlbum("" + albumM.getId());
    }

    private void refresh() {
        mPageId = 1;
        if (mListView != null) {
            mListView.setFooterViewVisible(View.VISIBLE);
        }
        loadData();
    }

    @Override
    protected void loadData() {
        if (mIsLoading) {
            return;
        }
        if (canUpdateUi() && mAdapter != null) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        mIsLoading = true;
        loadMemberSelectionList();
    }

    @Override
    public void onMore() {
        mPageId++;
        loadData();
    }

    /**
     * 数据加载成功
     */
    private void onLoadSuccess(final List<AlbumM> list) {
        List<List<AlbumM>> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (vHeadView != null) {
            vHeadView.setVisibility(View.VISIBLE);
        }
        listData.addAll(AlbumUtils.INSTANCE.groupList(2, list));
        onPageLoadingCompleted(LoadCompleteType.OK);
        mListView.onRefreshComplete(true);
    }

    /**
     * 数据加载失败
     */
    private void onLoadFailure(String message) {
        mIsLoading = false;
        if (!canUpdateUi()) {
            return;
        }
        if (mPageId == 1) {
            mAdapter.clear();
            mListView.onRefreshComplete(true);
            mListView.setHasMoreNoFooterView(false);
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        } else {
            CustomToast.showFailToast(message);
            mListView.onRefreshComplete(true);
        }
    }

    @Override
    protected void loadDataError() {
        if (mListView != null) {
            mListView.setMode(Mode.DISABLED);
            mListView.setHasMoreNoFooterView(false);
        }
    }

    @Override
    protected void loadDataOk() {
        if (mListView != null) {
            mListView.setMode(Mode.DISABLED);
        }
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }


    @Override
    protected String getPageLogicName() {
        return "VipAlbumContentListFragment";
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        loadShareData();
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
        LoginInfoModelNew infoModelNew = UserInfoMannage.getInstance().getUser();
        TraceFreeAlbumManager.INSTANCE.vipAlbumListPageView(infoModelNew != null && infoModelNew.getUid() != 0);
    }

    @Override
    public void onPause() {
        super.onPause();
        try {
            LoginInfoModelNew loginInfoModelNew = UserInfoMannage.getInstance().getUser();
            TraceFreeAlbumManager.INSTANCE.exitVipAlbumListPage(loginInfoModelNew != null && loginInfoModelNew.getUid() != 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(this);
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.main_ll_activity_rule) {
            startFragment(FreeListenVipAlbumRuleFragment.Companion.newInstance(mRuleContent));
            TraceFreeAlbumManager.INSTANCE.clickVipAlbumListRulePage();
            return;
        }
        if (view.getId() == R.id.main_ll_activity_record) {
            startFragment(UnLockRecordListFragment.newInstance());
            // 免费听VIP优选专辑-助力记录  点击事件
            new XMTraceApi.Trace()
                    .click(47818) // 用户点击时上报
                    .put("currPage", "VipforFree")
                    .createTrace();
            return;
        }
        if (view.getId() == R.id.main_iv_vip_back) {
            finishFragment();
            TraceFreeAlbumManager.INSTANCE.exitVipAlbumListPage();
        }
    }

    /**
     * 会员精选列表
     */
    private void loadMemberSelectionList() {
        Map<String, String> params = new HashMap<>();
        params.put("pageNum", String.valueOf(mPageId));
        if (!CollectionUtil.isNullOrEmpty(homeToVipAlbum) && mPageId == 1) {
            int pageSize = 20 - homeToVipAlbum.size();
            params.put(HttpParamsConstants.PARAM_PAGE_SIZE, String.valueOf(pageSize));
        } else {
            params.put(HttpParamsConstants.PARAM_PAGE_SIZE, "20");
        }
        IDataCallBack<List<AlbumM>> callBack = new IDataCallBack<List<AlbumM>>() {
            @Override
            public void onSuccess(final List<AlbumM> list) {
                mIsLoading = false;
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        try {
                            if (!canUpdateUi()) {
                                return;
                            }
                            addHomeAlbumList(list);
                            if (CollectionUtil.isNullOrEmpty(list)) {
                                mListView.setHasMoreNoFooterView(false);
                                List<List<AlbumM>> listData = mAdapter.getListData();
                                if (CollectionUtil.isNullOrEmpty(listData)) {
                                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                                } else {
                                    onPageLoadingCompleted(LoadCompleteType.OK);
                                }
                                return;
                            }
                            onLoadSuccess(list);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                onLoadFailure(message);
            }
        };
        LiteCommonRequest.getRecommendPoolData(LiteUrlConstants.newRecommendContentPollUrl(), params, callBack);
    }

    /**
     * 添加首页的数据
     */
    public void addHomeAlbumList(List<AlbumM> list) {
        List<List<AlbumM>> listData = mAdapter.getListData();
        if (mPageId == 1 && CollectionUtil.isNotEmpty(homeToVipAlbum)) {
            if (CollectionUtil.isNotEmpty(listData)) {
                listData.clear();
            }
            for (int i = 0; i < homeToVipAlbum.size(); i++) {
                list.add(i, homeToVipAlbum.get(i));
            }
        }
    }

    /**
     * 获取解锁数据
     */
    protected void loadShareData() {
        Map<String, String> params = new HashMap<>();
        params.put("pageNum", "1");
        params.put("pageSize", "30");
        params.put("activityCode", "10");
        LiteCommonRequest.getShareContentData(params, new IDataCallBack<AlbumShareModelList>() {
            @Override
            public void onSuccess(@Nullable AlbumShareModelList albumShareModelList) {
                if (albumShareModelList == null) return;
                mRuleContent = albumShareModelList.getShareRule();
                List<AlbumShareModel> list = albumShareModelList.getShareList();
                if (CollectionUtil.isNullOrEmpty(list)) {
                    return;
                }
                List<AlbumShareModel> listData = shareAdapter.getListData();
                if (listData == null) {
                    listData = new ArrayList<>();
                } else {
                    listData.clear();
                }
                if (shareAdapter.isHasExpand()) {
                    listData.addAll(list);
                } else {
                    listData.add(0, list.get(0));
                }
                shareAdapter.updateTotalData(list);
                shareAdapter.notifyDataSetChanged();
            }

            @Override
            public void onError(int code, String message) {
            }
        });
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    protected boolean isShowCoinGuide() {
        return false;
    }

    @Override
    public void onLogout(LoginInfoModelNew olderUser) {

    }

    @Override
    public void onLogin(LoginInfoModelNew model) {
        if (selectRecommendAlbum != null && model != null) {
            jumpAlbumOrShareDetail(model, selectRecommendAlbum);
            TraceFreeAlbumManager.INSTANCE.clickVipAlbumListItem(selectRecommendAlbum.getId());
            selectRecommendAlbum = null;
        }
    }

    @Override
    public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {

    }

    private ArrayList<AlbumM> getHomeRecommendAlbums() {
        try {
            if (getActivity() == null) return null;
            FragmentManager fragmentManager = getActivity().getSupportFragmentManager();
            List<Fragment> fragments = fragmentManager.getFragments();
            if (CollectionUtil.isNullOrEmpty(fragments)) return null;
            Fragment homeFragment = null;
            for (int i = 0; i < fragments.size(); i++) {
                if (fragments.get(i) instanceof HomeFragment) {
                    homeFragment = fragments.get(i);
                    break;
                }
            }
            if (homeFragment == null) return null;
            FragmentManager childFragmentManager = homeFragment.getChildFragmentManager();
            List<Fragment> childFragments = childFragmentManager.getFragments();
            if (CollectionUtil.isNullOrEmpty(childFragments)) return null;
            LiteHomeRecommendFragment homeRecommendFragment = null;
            for (int i = 0; i < childFragments.size(); i++) {
                if (childFragments.get(i) instanceof LiteHomeRecommendFragment) {
                    homeRecommendFragment = (LiteHomeRecommendFragment) childFragments.get(i);
                    break;
                }
            }
            if (homeRecommendFragment == null) return null;
            List<LiteFloorModel> liteFloorModels = homeRecommendFragment.getLiteFloorModeList();
            if (CollectionUtil.isNullOrEmpty(liteFloorModels)) return null;
            List<AlbumM> homeAlbums = null;
            for (int i = 0; i < liteFloorModels.size(); i++) {
                LiteFloorModel liteFloorModel = liteFloorModels.get(i);
                if (liteFloorModel.getModuleType() == LiteFloorModel.MODULE_FREE_LISTENER_CONTENT_POOL) {
                    homeAlbums = liteFloorModel.getList();
                    break;
                }
            }
            if (CollectionUtil.isNullOrEmpty(homeAlbums)) return null;
            return new ArrayList<>(homeAlbums);
        } catch (Exception e) {
            return null;
        }
    }
}
