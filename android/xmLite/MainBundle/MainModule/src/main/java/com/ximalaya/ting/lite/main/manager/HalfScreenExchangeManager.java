package com.ximalaya.ting.lite.main.manager;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.earn.AppStoreManager;
import com.ximalaya.ting.android.host.manager.earn.FuliPageManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.util.DateUtils;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.UiUtil;
import com.ximalaya.ting.lite.main.playlet.fragment.PlayletExchangeFragment;
import com.ximalaya.ting.lite.main.playnew.fragment.PlayFragmentNew;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.List;

public class HalfScreenExchangeManager {
    private static String MMKV_CASH_EXCHANGE_AUTO_SHOW_DIALOG_TS = "mmkv_cash_exchange_auto_show_dialog_ts";
    private static String MMKV_NEED_AUTO_SHOW_EXCHANGE_DIALOG_KEY = "mmkv_need_auto_show_exchange_dialog_key";
    private static String MMKV_CASH_EXCHANGE_EXPOSE_NEED_SHOW_DIALOG = "mmkv_cash_exchange_expose_need_show_dialog";
    private static WeakReference<BaseFragment2> dialogWeakReference;

    /**
     * 显示半屏幕弹窗
     */
    public static void showHalfScreenExchangeDialog(PlayFragmentNew baseFragment) {
        try {
            if (baseFragment == null) {
                return;
            }
            String url = getExchangeSwitchInfo().optString("url");
            if (TextUtils.isEmpty(url)) {
                return;
            }
            if (dialogWeakReference == null || dialogWeakReference.get() == null) {
                dialogWeakReference = new WeakReference<>(PlayletExchangeFragment.newInstance(url));
            }
            baseFragment.hideOrShowHalfScreenExchange(true);
            FragmentTransaction ft = baseFragment.getChildFragmentManager().beginTransaction();
            ft.setCustomAnimations(R.anim.host_dialog_push_in, R.anim.host_dialog_push_out);
            ft.replace(R.id.main_half_screen_exchange, dialogWeakReference.get(), PlayletExchangeFragment.class.getSimpleName());
            ft.commitAllowingStateLoss();
        }catch (Throwable throwable){
            throwable.printStackTrace();
        }
    }

    /**
     * 获取配置中心换量开关
     *
     * @return
     */
    public static JSONObject getExchangeSwitchInfo() {
        try {
            final String isConfigWithdrawal = ConfigureCenter.getInstance().getString(CConstants.Group_Cash.GROUP_NAME, CConstants.Group_Cash.ITEM_PLAYERWITHDRAW, "");
            if (isConfigWithdrawal == null || isConfigWithdrawal.isEmpty()) return new JSONObject();
            return new JSONObject(isConfigWithdrawal);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new JSONObject();
    }

    /**
     * 是否正在显示换量半浮层弹窗
     *
     * @return
     */
    public static boolean isShowingHalfScreenExchangeFragment(PlayFragmentNew baseFragment) {
        FragmentManager fragmentManager = baseFragment.getChildFragmentManager();
        List<Fragment> fragmentList = fragmentManager.getFragments();
        PlayletExchangeFragment playletExchangeFragment = null;
        for (int i = 0; i < fragmentList.size(); i++) {
            if (fragmentList.get(i) instanceof PlayletExchangeFragment) {
                playletExchangeFragment = (PlayletExchangeFragment) fragmentList.get(i);
                break;
            }
        }
        if (playletExchangeFragment != null) {
            FragmentTransaction ft = fragmentManager.beginTransaction();
            ft.remove(playletExchangeFragment);
            ft.commitAllowingStateLoss();
            baseFragment.hideOrShowHalfScreenExchange(false);
            dialogWeakReference = null;
            return true;
        }
        return false;
    }

    /**
     * 关闭半屏幕弹窗
     */
    public static void hideHalfScreenExchangeDialog() {
        if (dialogWeakReference == null || dialogWeakReference.get() == null) {
            return;
        }
        if (dialogWeakReference.get().getParentFragment() instanceof PlayFragmentNew) {
            PlayFragmentNew baseFragment2 = (PlayFragmentNew) dialogWeakReference.get().getParentFragment();
            if (baseFragment2 == null) {
                return;
            }
            FragmentTransaction ft = baseFragment2.getChildFragmentManager().beginTransaction();
            ft.remove(dialogWeakReference.get());
            ft.commitAllowingStateLoss();
            baseFragment2.hideOrShowHalfScreenExchange(false);
            dialogWeakReference = null;
        }
    }

    /**
     * 弹出H5给到的提现通知
     *
     * @param disposableAmount
     */
    public static void showExchangeNotifyDialog(String disposableAmount) {
        try {
            Activity topActivity = BaseApplication.getTopActivity();
            if (!(topActivity instanceof MainActivity)) {
                return;
            }
            MainActivity mainActivity = (MainActivity) topActivity;
            ViewGroup exchangeRootView = mainActivity.getExchangeNotifyRootView();

            if (exchangeRootView == null || TextUtils.isEmpty(disposableAmount)) {
                return;
            }
            if (!UserInfoMannage.hasLogined() || UserInfoMannage.getUid() == 0 || ChildProtectManager.isChildProtectOpen(BaseApplication.mAppInstance)) {
                return;
            }
            Fragment currentFragment = mainActivity.getNormalModeActivity() != null ?
                    mainActivity.getNormalModeActivity().getMainPageCurFragment() : null;

            if ((dialogWeakReference != null && dialogWeakReference.get() instanceof PlayletExchangeFragment) ||
                    FuliPageManager.isFuLiPageFragment(currentFragment)) {
                exchangeRootView.setVisibility(View.VISIBLE);
                dealWithHalfScreenExchangeNotifyView(disposableAmount, exchangeRootView);
                // 新播放页(节目)-换量额外奖励提现通知  控件曝光
                new XMTraceApi.Trace()
                        .setMetaId(58256)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currPage", "playPageTrackTab")
                        .put("exploreType", "playPageTrackTab") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                        .createTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置换量弹窗是否自动弹出
     *
     * @param isAutoShow
     */
    public static void setHalfScreenIsAutoShowExchangeDialog(boolean isAutoShow) {
        MMKVUtil.getInstance().saveBoolean(MMKV_NEED_AUTO_SHOW_EXCHANGE_DIALOG_KEY, isAutoShow);
    }

    /**
     * 获取设置换量弹窗是否自动弹出状态
     */
    public static boolean getHalfScreenIsAutoShowExchangeDialog() {
        return MMKVUtil.getInstance().getBoolean(MMKV_NEED_AUTO_SHOW_EXCHANGE_DIALOG_KEY, true);
    }


    /**
     * 设置获取播放页曝光换量弹窗是否自动弹出
     *
     * @param isAutoShow
     */
    public static void setNeedNextExposeAutoShowExchangeDialog(boolean isAutoShow) {
        MMKVUtil.getInstance().saveBoolean(MMKV_CASH_EXCHANGE_EXPOSE_NEED_SHOW_DIALOG, isAutoShow);
    }

    /**
     * 获取播放页曝光换量弹窗是否自动弹出
     */
    public static boolean getNeedNextExposeAutoShowExchangeDialogStatus() {
        return MMKVUtil.getInstance().getBoolean(MMKV_CASH_EXCHANGE_EXPOSE_NEED_SHOW_DIALOG, false);
    }

    public static void dealWithHalfScreenExchangeNotifyView(String disposableAmount, ViewGroup rootView) {
        View exchangeNotifyView = rootView.findViewById(R.id.exchange_notify_root_view);
        if (exchangeNotifyView == null) {
            exchangeNotifyView = LayoutInflater.from(rootView.getContext()).inflate(
                    R.layout.main_exchange_nofity_layout, rootView);
        }
        TextView notifyCashNumber = exchangeNotifyView.findViewById(R.id.tv_notify_cash_number);
        String withdrawContent = "已成功发" + disposableAmount + "元提现";
        SpannableString spannable = new SpannableString(withdrawContent);
        spannable.setSpan(new ForegroundColorSpan(Color.parseColor("#FE5631")), withdrawContent.indexOf("发") + 1,
                withdrawContent.indexOf("提"), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        notifyCashNumber.setText(spannable);
        ObjectAnimator translationY =
                ObjectAnimator.ofFloat(exchangeNotifyView, "translationY", 0F, UiUtil.dp2px(45f));
        ObjectAnimator alpha = ObjectAnimator.ofFloat(exchangeNotifyView, "alpha", 0F, 1.0F);
        ObjectAnimator translationYUp =
                ObjectAnimator.ofFloat(exchangeNotifyView, "translationY", (UiUtil.dp2px(45f)), 0F);
        AnimatorSet mExchangeNotifyDownAnimal = new AnimatorSet();
        mExchangeNotifyDownAnimal.playTogether(translationY, alpha);
        mExchangeNotifyDownAnimal.setDuration(400);
        mExchangeNotifyDownAnimal.start();
        AnimatorSet mExchangeNotifyUpAnimal = new AnimatorSet();
        mExchangeNotifyUpAnimal.play(translationYUp);
        mExchangeNotifyUpAnimal.setDuration(300);
        mExchangeNotifyDownAnimal.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {

            }

            @Override
            public void onAnimationEnd(Animator animator) {
                try {
                    mExchangeNotifyDownAnimal.cancel();
                }catch (Throwable throwable){
                    throwable.printStackTrace();
                    return;
                }
                rootView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mExchangeNotifyUpAnimal.start();
                    }
                }, 1200);
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        mExchangeNotifyUpAnimal.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {

            }

            @Override
            public void onAnimationEnd(Animator animator) {
                try {
                    mExchangeNotifyUpAnimal.cancel();
                    rootView.setVisibility(View.GONE);
                }catch (Throwable throwable){
                    throwable.printStackTrace();
                }
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
    }

    /**
     * 请求是否需求展示换量半浮层弹窗
     *
     * @return
     */
    public static void requestTwoMinutesTaskIsCompleteByPlayPage(PlayFragmentNew playFragmentNew) {
        try {
            long lastTimes = MMKVUtil.getInstance().getLong(MMKV_CASH_EXCHANGE_AUTO_SHOW_DIALOG_TS, -1);
            if (DateUtils.isSameDay(lastTimes, System.currentTimeMillis())) {
                return;
            }
            final boolean isConfigWithdrawal = getExchangeSwitchInfo().optBoolean("switch", false);
            final boolean isCanShowWithdrawal = isConfigWithdrawal && !AppStoreManager.isAppStoreEnable();
            if (!isCanShowWithdrawal) {
                return;
            }
            if (!getHalfScreenIsAutoShowExchangeDialog()) {
                return;
            }
            FragmentManager fragmentManager = playFragmentNew.getChildFragmentManager();
            Fragment fragment = fragmentManager.findFragmentByTag(PlayletExchangeFragment.class.getSimpleName());
            PlayletExchangeFragment playletExchangeFragment = null;
            if (fragment instanceof PlayletExchangeFragment) {
                playletExchangeFragment = (PlayletExchangeFragment) fragment;
            }
            if (playletExchangeFragment != null && playletExchangeFragment.isRealVisable()) {
                return;
            }
            HalfScreenExchangeManager.checkTwoMinutesTaskComplete(new IDataCallBack<Boolean>() {
                @Override
                public void onSuccess(@Nullable Boolean object) {
                    if (object != null && object.equals(false)) {
                        HalfScreenExchangeManager.showHalfScreenExchangeDialog(playFragmentNew);
                        MMKVUtil.getInstance().saveLong(MMKV_CASH_EXCHANGE_AUTO_SHOW_DIALOG_TS, System.currentTimeMillis());
                    }
                }

                @Override
                public void onError(int code, String message) {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void checkTwoMinutesTaskComplete(IDataCallBack<Boolean> callBack) {
        CommonRequestM.baseGetRequest(UrlConstants.getInstanse().getListenTaskRecordUrl(), null, callBack, new CommonRequestM.IRequestCallBack<Boolean>() {
            @Override
            public Boolean success(String content) throws Exception {
                try {
                    JSONObject jsonObject = new JSONObject(content);
                    int code = jsonObject.optInt("code");
                    if (code != 0) {
                        return false;
                    }
                    JSONObject data = jsonObject.optJSONObject("data");
                    if (data == null) {
                        return false;
                    }
                    String id = data.optString("id");
                    if (!TextUtils.isEmpty(id) && !id.equals("null")) {
                        return true;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return false;
            }
        });
    }

    public static void onDestroy() {
        if (dialogWeakReference != null && dialogWeakReference.get() != null) {
            dialogWeakReference = null;
        }
    }
}
