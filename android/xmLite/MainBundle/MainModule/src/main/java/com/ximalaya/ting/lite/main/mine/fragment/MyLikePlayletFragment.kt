package com.ximalaya.ting.lite.main.mine.fragment

import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import com.handmark.pulltorefresh.library.PullToRefreshBase
import com.xiaomi.push.it
import com.ximalaya.ting.android.framework.fragment.BaseFragment
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.mine.adapter.MyLikePlayletListAdapter
import com.ximalaya.ting.lite.main.request.LiteCommonRequest
import com.ximalaya.ting.lite.main.truck.mine.MyLikeTrackListFragment
import com.ximalaya.ting.lite.main.truck.mine.adapter.MyLikeTrackListAdapter
import com.ximalaya.ting.lite.main.truck.mine.model.MyLikeTrackListResp
import java.util.HashMap

/**
 * Created by zk on 2022/1/27
 * 我的-喜欢-短剧
 */
class MyLikePlayletFragment : BaseFragment2() {

    private val TAG: String = "MyLikePlayletFragment"

    private lateinit var mRefreshLoadMoreListView: RefreshLoadMoreListView
    private val mDataList: MutableList<TrackM> = arrayListOf()
    private lateinit var mAdapter: MyLikePlayletListAdapter
    private var mPageId = 1
    private var mTotalCount = Int.MAX_VALUE

    companion object {

        private const val PAGE_SIZE = 20

        @JvmStatic
        fun newInstance(): MyLikePlayletFragment {
            val args = Bundle()
            val fragment = MyLikePlayletFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun getPageLogicName(): String = javaClass.simpleName

    override fun initUi(savedInstanceState: Bundle?) {
        mRefreshLoadMoreListView = findViewById(R.id.main_refresh_load_more_listview)
        mRefreshLoadMoreListView.mode = PullToRefreshBase.Mode.PULL_FROM_START
        mRefreshLoadMoreListView.setIsShowLoadingLabel(true)
        mAdapter = MyLikePlayletListAdapter(context, mDataList)
        mRefreshLoadMoreListView.setAdapter(mAdapter)

        mRefreshLoadMoreListView.setOnRefreshLoadMoreListener(object : IRefreshLoadMoreListener {
            override fun onRefresh() {
                mPageId = 1
                loadData()
            }

            override fun onMore() {
                mPageId++
                loadData()
            }
        })

        mAdapter.setOnItemClickListener(object : MyLikePlayletListAdapter.OnItemClickListener {
            override fun onItemClick(position: Int) {
                try {
                    val item = mDataList[position]
                    val trackId = item.dataId
                    val album = item.album
                    album?.let {
                        val fragment: BaseFragment = Router.getMainActionRouter()
                                .fragmentAction
                                .newPlayletDetailFragment(trackId, album.albumId, 0)
                        startFragment(fragment)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        })
    }

    override fun getContainerLayoutId(): Int = R.layout.main_fra_my_like_playlet_list

    override fun onMyResume() {
        super.onMyResume()

        mPageId = 1
        loadData()
    }

    override fun loadData() {

        requestDataList(mPageId, PAGE_SIZE)
    }

    /**
     * 请求数据
     */
    private fun requestDataList(pageId: Int, pageSize: Int) {

        val params: MutableMap<String, String> = HashMap()
        params["businessType"] = "11"
        params["pageSize"] = pageSize.toString()
        params["pageId"] = pageId.toString()
        LiteCommonRequest.getMyLikeTrackList(params, object : IDataCallBack<MyLikeTrackListResp> {

            override fun onSuccess(resp: MyLikeTrackListResp?) {
                if (!canUpdateUi()) {
                    return
                }
                if (mPageId == 1) {
                    mDataList.clear()
                }
                if (resp?.list == null && mPageId == 1) {
                    //第一页就没有数据
                    mAdapter.notifyDataSetChanged()
                    onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR)
                    mRefreshLoadMoreListView.onRefreshComplete(false)

                    return
                }
                if (resp?.list.isNullOrEmpty() && mPageId == 1) {
                    //第一页就没有数据
                    mAdapter.notifyDataSetChanged()
                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT)
                    mRefreshLoadMoreListView.onRefreshComplete(false)
                    return
                }
                onPageLoadingCompleted(LoadCompleteType.OK)

                resp?.let {
                    mTotalCount = it.totalCount ?: Int.MAX_VALUE
                    it.list?.let { list ->
                        mDataList.addAll(list)
                    }
                    val totalCount = it.totalCount ?: 0
                    if (mDataList.size >= totalCount) {//是否有更多
                        mRefreshLoadMoreListView.onRefreshComplete(false)
                    } else {
                        mRefreshLoadMoreListView.onRefreshComplete(true)
                    }
                }

                mAdapter.notifyDataSetChanged()
            }

            override fun onError(code: Int, message: String?) {
                Logger.i(TAG, "onError code = $code  msg = $message")
                if (mPageId == 1 && mDataList.isEmpty()) {
                    //第一页就没有数据
                    onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR)
                    mRefreshLoadMoreListView.onRefreshComplete(false)
                }
            }
        })
    }

}