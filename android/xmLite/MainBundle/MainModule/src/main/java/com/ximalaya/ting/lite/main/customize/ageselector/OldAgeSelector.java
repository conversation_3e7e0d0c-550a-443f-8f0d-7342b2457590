package com.ximalaya.ting.lite.main.customize.ageselector;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

/**
 * 年龄选择器
 *
 * <AUTHOR>
 */

public class OldAgeSelector extends FrameLayout implements IAgeSelector, View.OnClickListener {

    String[] ages = {"60", "70", "80", "-1", "90", "95", "00", "10", "其他"};
    TextView[] tvAges = new TextView[9];
    private int mSelected = 3;
    private OnValueChangeListener mListener;
    private int mStyle;
    private ViewGroup vAge;

    public OldAgeSelector(Context context) {
        this(context, null);
    }

    public OldAgeSelector(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OldAgeSelector(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        if (attrs != null) {
            TypedArray ta = getContext().obtainStyledAttributes(attrs, R.styleable.OldAgeSelector);
            mStyle = ta.getInt(R.styleable.OldAgeSelector_oasStyle, 0);
        }

        View view = LayoutInflater.from(getContext()).inflate(mStyle == 0 ? R.layout.main_customize_persion_age_simple :
                R.layout.main_customize_persion_age, null);
        vAge = view.findViewById(R.id.main_v_age);
        tvAges[0] = view.findViewById(R.id.main_tv_age_60);
        tvAges[1] = view.findViewById(R.id.main_tv_age_70);
        tvAges[2] = view.findViewById(R.id.main_tv_age_80);
        tvAges[4] = view.findViewById(R.id.main_tv_age_90);
        tvAges[5] = view.findViewById(R.id.main_tv_age_95);
        tvAges[6] = view.findViewById(R.id.main_tv_age_00);
        tvAges[7] = view.findViewById(R.id.main_tv_age_10);
        tvAges[8] = view.findViewById(R.id.main_tv_age_other);


        //10后配置ab，默认展示05后
//        boolean portraitAgeNickname = ConfigureCenter.getInstance().getBool("ximalaya_lite", "portraitAgeNickname", true);
//        tvAges[7].setText(portraitAgeNickname ? "05后" : "10后");
//        ages[7] = portraitAgeNickname ? "05" : "10";
        tvAges[7].setText("10后");
        ages[7] = "10";

        for (int i = 0; i < ages.length; i++) {
            if (tvAges[i] != null) {
                tvAges[i].setOnClickListener(this);
                AutoTraceHelper.bindData(tvAges[i], ages[i]);
            }
        }
//        AutoTraceHelper.setLabelForCTAndMultiSameView(vAge, tvAges[0], tvAges[1], tvAges[2],
//                tvAges[3], tvAges[4], tvAges[5], tvAges[6], tvAges[7], tvAges[8]);
        addView(view);
    }

    private void toggleAgeSelect(View view) {
        boolean isSelect = false;
        if (view != null) {
            isSelect = view.isSelected();
        }
        for (int i = 0; i < ages.length; i++) {
            if (tvAges[i] != null) {
                tvAges[i].setSelected(false);
            }
        }
        if (view != null) {
            view.setSelected(!isSelect);
        }
    }

    @Override
    public String getSelectedAge() {
        if (mSelected == 3) {
            return null;
        }
        return ages[mSelected];
    }

    @Override
    public void setSelectedAge(String age) {
        if (age == null) {
            return;
        }
        for (int i = 0; i < ages.length; i++) {
            if (ages[i].equals(age)) {
                mSelected = i;
                toggleAgeSelect(tvAges[i]);
                return;
            }
        }
    }

    @Override
    public void setOnValueChangeListener(OnValueChangeListener listener) {
        mListener = listener;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        toggleAgeSelect(v);

        if (!v.isSelected()) {
            //取消选中
            mSelected = 3;
        } else {
            //选中状态
            if (id == R.id.main_tv_age_60) {
                mSelected = 0;
            } else if (id == R.id.main_tv_age_70) {
                mSelected = 1;
            } else if (id == R.id.main_tv_age_80) {
                mSelected = 2;
            } else if (id == R.id.main_tv_age_90) {
                mSelected = 4;
            } else if (id == R.id.main_tv_age_95) {
                mSelected = 5;
            } else if (id == R.id.main_tv_age_00) {
                mSelected = 6;
            } else if (id == R.id.main_tv_age_10) {
                mSelected = 7;
            } else if (id == R.id.main_tv_age_other) {
                mSelected = 8;
            }
        }

        if (mListener != null) {
            mListener.onValueChanged(ages[mSelected]);
        }
    }
}
