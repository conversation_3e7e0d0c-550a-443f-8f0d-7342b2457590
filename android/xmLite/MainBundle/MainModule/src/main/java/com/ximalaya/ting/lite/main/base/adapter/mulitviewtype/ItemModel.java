package com.ximalaya.ting.lite.main.base.adapter.mulitviewtype;

import android.os.Bundle;

/**
 * @param <Model>
 * <AUTHOR>
 */
public class ItemModel<Model> {
    public Model object;   // 类型
    public Object tag;      // 额外参数
    public int dataType = MulitViewTypeAdapter.DEFAULT_DATA_TYPE; // 数据的类型
    public int viewType;           // 布局的类型
    public String viewTypeString;          //类型字符串
    private boolean mCurrentAdStatue; // 当前广告的状态

    private Bundle bundle;//用来存储一些特殊变量

    public ItemModel(Model object, int viewType) {
        this.object = object;
        this.viewType = viewType;
    }

    public ItemModel(Model object, int viewType, int dataType) {
        this.object = object;
        this.dataType = dataType;
        this.viewType = viewType;
    }

    public Model getObject() {
        return object;
    }

    public void setObject(Model object) {
        this.object = object;
    }

    public Object getTag() {
        return tag;
    }

    public void setTag(Object tag) {
        this.tag = tag;
    }

    public int getViewType() {
        return viewType;
    }

    public int getDataType() {
        return dataType;
    }

    public void setDataType(int dataType) {
        this.dataType = dataType;
    }

    public boolean isCurrentAdStatue() {
        return mCurrentAdStatue;
    }

    public void setCurrentAdStatue(boolean currentAdStatue) {
        mCurrentAdStatue = currentAdStatue;
    }

    public Bundle getBundle() {
        return bundle;
    }

    public void setBundle(Bundle bundle) {
        this.bundle = bundle;
    }

    public void setViewTypeString(String viewTypeString) {
        this.viewTypeString = viewTypeString;
    }

    public String getViewTypeString() {
        return viewTypeString;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null) return false;
        //noinspection SimplifiableIfStatement
        if (!(obj instanceof ItemModel)) return false;

        return getObject() == ((ItemModel) obj).getObject()
                && getTag() == ((ItemModel) obj).getTag()
                && getDataType() == getDataType()
                && getViewType() == getViewType();
    }
}