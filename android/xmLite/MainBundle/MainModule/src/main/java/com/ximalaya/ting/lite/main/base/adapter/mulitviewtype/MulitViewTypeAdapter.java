package com.ximalaya.ting.lite.main.base.adapter.mulitviewtype;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.model.album.RecommendItem;
import com.ximalaya.ting.lite.main.model.album.RecommendRefreshModel;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2016/11/24.
 * <AUTHOR>
 */

public final class MulitViewTypeAdapter extends BaseAdapter {

    public interface IDataAction {
        void remove(int postion);
    }

    // 默认的dataType
    public static final int DEFAULT_DATA_TYPE = -1;

    protected Context context;

    protected LayoutInflater layoutInflater;

    public Map<Integer, IMulitViewTypeViewAndData> getViewTypes() {
        return viewTypes;
    }

    private Map<Integer, IMulitViewTypeViewAndData> viewTypes;

    private List<ItemModel> listData;
    private boolean mNotifyOnChange = true;
    private int mFromForCalabashLineAdapter = -1;

    public MulitViewTypeAdapter(Context context, Map<Integer, IMulitViewTypeViewAndData> viewTypes) {
        // 这里要检查是否是合法的viewType
        if (context == null) {
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException(getClass().getName() + " : context 不能为null");
            }
            context = MainApplication.getMyApplicationContext();
        }

        this.context = context;
        if (viewTypes == null) {
            viewTypes = new HashMap<>();
        }
        this.viewTypes = new HashMap<>(viewTypes);
        layoutInflater = LayoutInflater.from(context);
        listData = new ArrayList<>();

        if (ConstantsOpenSdk.isDebug && viewTypes.size() > 0) {
            List<Integer> integers = new ArrayList<>(viewTypes.keySet());
            Collections.sort(integers);
            for (int i = 0; i < integers.size(); i++) {
                if (integers.get(0) != null && integers.get(0) != 0) {
                    throw new RuntimeException(getClass().getName() + " viewType 必须以0开头");
                }

                if (i + 1 < viewTypes.size() && integers.get(i) != null && integers.get(i + 1) != null && integers.get(i).intValue() != (integers.get(i + 1).intValue() - 1)) {
                    throw new RuntimeException(getClass().getName() + " viewType 必须连续");
                }
            }
        }
    }

    public void setLayoutInflater(LayoutInflater layoutInflater) {
        this.layoutInflater = layoutInflater;
    }

    @Override
    public int getItemViewType(int position) {
        ItemModel itemModel = getItem(position);
        if (itemModel != null) {
            return itemModel.viewType;
        }
        if (ConstantsOpenSdk.isDebug) {
            throw new RuntimeException(getClass().getName() + " : 相关的viewType 没有注册");
        }
        return 0;
    }

    @Override
    public int getViewTypeCount() {
        if (viewTypes != null) {
            int size = viewTypes.size();
            if (size < 1) {
                return 1;
            }
            return size;
        }
        return 1;
    }

    @Override
    public int getCount() {
        if (listData != null) {
            return listData.size();
        }
        return 0;
    }

    @Override
    public synchronized ItemModel getItem(int position) {
        if (listData != null && listData.size() > 0 && position < listData.size() && position >= 0) {
            return listData.get(position);
        }
        return null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        HolderAdapter.BaseViewHolder holder = null;

        IMulitViewTypeViewAndData mulitViewTypeAdapter = viewTypes.get(getItemViewType(position));
        if (mulitViewTypeAdapter == null) {
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException(getClass().getName() + "没有注册到相应的 ViewType " + getItemViewType(position) + "    " + position);
            }
            return null;
        }
        if (convertView == null) {
            convertView = mulitViewTypeAdapter.getView(layoutInflater, position, parent);

            holder = mulitViewTypeAdapter.buildHolder(convertView);

            convertView.setTag(holder);
        } else {
            holder = (HolderAdapter.BaseViewHolder) convertView.getTag();
        }

        convertView.setTag(R.id.main_mult_view_type_position ,position);
        if (listData != null && position < listData.size()) {
            try {
                mulitViewTypeAdapter.bindViewDatas(holder, listData.get(position), convertView, position);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException(getClass().getName() + " error:getView listData:" + listData + "position:" + position);
            }
        }

        return convertView;
    }

    /**
     * 注册ViewType的实现
     *
     * @param mulitViewTypeAdapter
     * @return 返回的是viewType在viewTypes中的位置
     */
    public int registerViewTypeImp(IMulitViewTypeViewAndData mulitViewTypeAdapter) {
        if (mulitViewTypeAdapter == null) {
            return -1;
        }

        int viewType = 0;
        if (viewTypes.size() > 0) {
            List<Integer> keys = new ArrayList<>(viewTypes.keySet());
            Collections.sort(keys);
            if (keys.get(keys.size() - 1) != null) {
                viewType = keys.get(keys.size() - 1).intValue() + 1;
            }
        }

        viewTypes.put(viewType, mulitViewTypeAdapter);
        return viewType;
    }

    public void onResume(){
        for(Map.Entry<Integer, IMulitViewTypeViewAndData> entry : viewTypes.entrySet()){
            IMulitViewTypeViewAndData viewTypeViewAndData = entry.getValue();
            if(viewTypeViewAndData instanceof IMulitViewTypeViewAndDataWithLifecircle){
                ((IMulitViewTypeViewAndDataWithLifecircle) viewTypeViewAndData).onResume();
            }
        }
    }

    public void onPause(){
        for(Map.Entry<Integer, IMulitViewTypeViewAndData> entry : viewTypes.entrySet()){
            IMulitViewTypeViewAndData viewTypeViewAndData = entry.getValue();
            if(viewTypeViewAndData instanceof  IMulitViewTypeViewAndDataWithLifecircle){
                ((IMulitViewTypeViewAndDataWithLifecircle) viewTypeViewAndData).onPause();
            }
        }
    }

    private void checkViewType(int viewType) {
        if (viewTypes == null || !viewTypes.containsKey(viewType)) {
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException("设置ViewType时要先进行配置");
            }
        }
    }

    public ItemModel add(Object object, int viewType) {
        return add(object, viewType, DEFAULT_DATA_TYPE);
    }

    public void updateViewItem(View itemView, int position) {
        if (itemView == null || position < 0) {
            return;
        }

        HolderAdapter.BaseViewHolder holder = (HolderAdapter.BaseViewHolder) itemView.getTag();

        IMulitViewTypeViewAndData mulitViewTypeAdapter = viewTypes.get(getItemViewType(position));

        try {
            if (mulitViewTypeAdapter != null) {
                mulitViewTypeAdapter.bindViewDatas(holder, listData.get(position) ,itemView , position);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public ItemModel add(Object object, int viewType, int dataType) {
        if (object == null) {
            return null;
        }

        checkViewType(viewType);

        ItemModel itemModel = new ItemModel(object, viewType, dataType);

        listData.add(itemModel);

        if (mNotifyOnChange) notifyDataSetChanged();

        return itemModel;
    }

    public void addAll(List list, int viewType) {
        if (ToolUtil.isEmptyCollects(list)) {
            return;
        }

        checkViewType(viewType);

        Object object = list.get(0);
        if (object == null) {
            return;
        }

        Iterator iterator = list.iterator();

        while (iterator.hasNext()) {
            Object item = iterator.next();
            if (item == null || item.getClass() != object.getClass()) {
                Logger.e(getClass().getSimpleName(), "Item 不能为null 并且同一Type的List中元素必须一致");
                iterator.remove();
            } else {
                ItemModel itemModel = new ItemModel(item, viewType);
                listData.add(itemModel);
            }
        }

        if (mNotifyOnChange) notifyDataSetChanged();
    }

    public void insert(Object object, int index, int viewType) {
        if (object == null) {
            return;
        }

        checkViewType(viewType);

        ItemModel itemModel = new ItemModel(object, viewType);
        insertToList(listData, index, itemModel);

        if (mNotifyOnChange) notifyDataSetChanged();
    }

    public void addAll(List list, int index, int viewType) {
        if (ToolUtil.isEmptyCollects(list)) {
            return;
        }

        checkViewType(viewType);

        Object object = list.get(0);
        if (object == null) {
            return;
        }

        Iterator iterator = list.iterator();

        while (iterator.hasNext()) {
            Object item = iterator.next();
            if (item == null || item.getClass() != object.getClass()) {
                Logger.e(getClass().getSimpleName(), "Item 不能为null 并且同一Type的List中元素必须一致");
                iterator.remove();
            } else {
                ItemModel itemModel = new ItemModel(item, viewType);
                insertToList(listData, index, itemModel);
                index++;
            }
        }

        if (mNotifyOnChange) notifyDataSetChanged();
    }

    private void insertToList(List list, int index, ItemModel itemModel) {
        if (list == null) {
            return;
        }

        if (list.size() <= index) {
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException(getClass().getSimpleName() + "   :  数组越界 ");
            }
            index = list.size() - 1;
            if (index < 0) {
                index = 0;
            }
        }
        list.add(index, itemModel);
    }

    public void remove(int index) {
        if (index >= listData.size()) {
            return;
        }

        listData.remove(index);

        if (mNotifyOnChange) notifyDataSetChanged();
    }

    public void remove(Object object) {
        if (object == null) {
            return;
        }
        Iterator<ItemModel> iterator = listData.iterator();

        while (iterator.hasNext()) {
            ItemModel item = iterator.next();
            if (item != null && item.object == object) {
                iterator.remove();
                if (mNotifyOnChange) notifyDataSetChanged();
                break;
            }
        }
    }

    public void clear() {
        listData.clear();

        if (mNotifyOnChange) notifyDataSetChanged();
    }

    @Override
    public void notifyDataSetChanged() {
        super.notifyDataSetChanged();
        mNotifyOnChange = true;
    }

    public void setNotifyOnChange(boolean notifyOnChange) {
        mNotifyOnChange = notifyOnChange;
    }

    public int getDataTypeIndex(int dataType) {
        for (int i = 0; i < listData.size(); i++) {
            ItemModel itemModel = listData.get(i);
            if (itemModel != null && itemModel.dataType == dataType) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 更新数据根据偏移量
     *
     * @param end
     * @param count
     */
    public void updateDataByOffset(int end, int count, RecommendRefreshModel data, String direction) {
        if (listData != null && listData.size() > count && listData.size() > end) {
            for (int i = 0; i < (RecommendItem.RECOMMEND_DIRECTION_ROW.equals(direction) ? count / 3 : count); i++) {
                if (RecommendItem.RECOMMEND_DIRECTION_ROW.equals(direction)) {
                    int index = end - count / 3 + i;
                    if (listData.size() > index) {
                        ItemModel remove = listData.get(index);
                        if ((data.getList().size() >= i * 3 + 3) && remove.getObject() instanceof RecommendItem) {
                            RecommendItem tempRecommendItem = new RecommendItem((RecommendItem) remove.getObject());
                            tempRecommendItem.setList(new ArrayList(data.getList().subList(i * 3, i * 3 + 3)));
                            ItemModel element = new ItemModel(tempRecommendItem, remove.getViewType(), remove
                                    .getDataType());
                            element.setTag(remove.getTag());
                            listData.set(index, element);
                        }
                    }
                } else if (RecommendItem.RECOMMEND_DIRECTION_COLUMN.equals(direction)) {
                    int index = end - count + i;
                    if (listData.size() > index) {
                        ItemModel remove = listData.get(index);
                        if (data.getList().size() > i) {
                            ItemModel element = new ItemModel(data.getList().get(i), remove.getViewType(), remove.getDataType());
                            element.setTag(remove.getTag());
                            listData.set(index, element);
                        }
                    }
                }
            }

            //刷新分类标签
            if (!ToolUtil.isEmptyCollects(data.keywords)) {
                int index;
                if (RecommendItem.RECOMMEND_DIRECTION_ROW.equals(direction)) {
                    index = end - count / 3 - 1;
                } else {
                    index = end - count - 1;
                }

                if (listData.size() > index) {
                    ItemModel remove = listData.get(index);
                    ItemModel element = new ItemModel(data.keywords, remove.getViewType(), remove.getDataType());
                    element.setTag(remove.getTag());
                    listData.set(index, element);
                }
            }

            if (mNotifyOnChange) {
                notifyDataSetChanged();
            }
        }
    }

    public int getIndexOfData(Object object) {
        if (listData == null || listData.size() <= 0)
            return 0;
        else
            return listData.indexOf(object);
    }

    public List<ItemModel> getListData() {
        return listData;
    }

    public void setFromForCalabashLineAdapter(int fromForCalabashLineAdapter) {
        mFromForCalabashLineAdapter = fromForCalabashLineAdapter;
    }

    public interface IListViewItemUpdater {
        void updateItemView(ItemModel itemModel);
    }
}
