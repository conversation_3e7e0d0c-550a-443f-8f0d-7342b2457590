package com.ximalaya.ting.lite.main.download.utils;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   : 任务状态常量定义
 */
public class TaskStateConst {
    public static final int STATE_PENDING = 0;// 表示任务未开始
    public static final int STATE_RUNNING = 1;// 任务在执行
    public static final int STATE_PAUSED = 2;// 任务已经开始，此时被暂停了
    public static final int STATE_ERROR = 3;// 任务发生了错误，此时任务已经停止了运行
    public static final int STATE_FINISH = 4;// 任务已经完成，且成功执行
}
