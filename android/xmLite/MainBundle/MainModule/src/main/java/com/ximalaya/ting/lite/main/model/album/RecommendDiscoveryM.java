package com.ximalaya.ting.lite.main.model.album;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;

import org.json.JSONObject;

/**
 * 发现-推荐 发现新奇的数据Model
 *
 * <AUTHOR>
 */
public class RecommendDiscoveryM implements Parcelable {

    @SerializedName("id")
    private int id;
    @SerializedName("title")
    private String title;
    @SerializedName("subtitle")
    private String subtitle;
    @SerializedName("coverPath")
    private String coverPath;
    @SerializedName("contentType")
    private String contentType; // xzone,activity,html5
    @SerializedName("url")
    private String url;
    @SerializedName("sharePic")
    private String sharePic;
    @SerializedName("enableShare")
    private boolean enableShare;
    @SerializedName("isHot")
    private boolean isHot;
    @SerializedName("isExternalUrl")
    private boolean isExternalUrl;
    @SerializedName("contentUpdatedAt")
    private long contentUpdatedAt;
    @SerializedName("bubbleText")
    private String bubbleText;
    @SerializedName("properties")
    private RecommendDiscoveryProps properties;
    @SerializedName("channelId")
    private long channelId;

    @SerializedName("displayClass")
    private String displayClass;

    @SerializedName("backColor")
    private String backColor;

    public RecommendDiscoveryM() {

    }

    public RecommendDiscoveryM(JSONObject object) {
        if (object != null) {
            try {
                setId(object.optInt("id", 0));
                setTitle(object.optString("title", ""));
                setSubtitle(object.optString("subtitle", ""));
                setContentUpdatedAt(object.optLong("contentUpdatedAt"));
                setCoverPath(object.optString("coverPath", ""));
                setContentType(object.optString("contentType", ""));
                setUrl(object.optString("url"));
                setEnableShare(object.optBoolean("enableShare"));
                setIsExternalUrl(object.optBoolean("isExternalUrl"));
                setSharePic(object.optString("sharePic"));
                setBubbleText(object.optString("bubbleText"));
                setProperties(new Gson().fromJson(
                        object.optString("properties"),
                        RecommendDiscoveryProps.class));
                setChannelId(object.optLong("channelId"));
                setDisplayClass(object.optString("displayClass"));
                setBackColor(object.optString("backColor"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return url;
    }

    private void setUrl(String url) {
        this.url = url;
    }

    public String getSubtitle() {
        return subtitle;
    }

    private void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public boolean isHot() {
        return isHot;
    }

    public String getBubbleText() {
        return bubbleText;
    }

    private void setBubbleText(String bubbleText) {
        this.bubbleText = bubbleText;
    }

    public void setIsHot(boolean isHot) {
        this.isHot = isHot;
    }

    public boolean isEnableShare() {
        return enableShare;
    }

    private void setEnableShare(boolean enableShare) {
        this.enableShare = enableShare;
    }

    public String getSharePic() {
        return sharePic;
    }

    private void setSharePic(String sharePic) {
        this.sharePic = sharePic;
    }

    public boolean isExternalUrl() {
        return isExternalUrl;
    }

    private void setIsExternalUrl(boolean isExternalUrl) {
        this.isExternalUrl = isExternalUrl;
    }

    public long getContentUpdatedAt() {
        return contentUpdatedAt;
    }

    private void setContentUpdatedAt(long contentUpdatedAt) {
        this.contentUpdatedAt = contentUpdatedAt;
    }

    public RecommendDiscoveryProps getProperties() {
        return properties;
    }

    public void setProperties(RecommendDiscoveryProps properties) {
        this.properties = properties;
    }

    public long getChannelId() {
        return channelId;
    }

    public RecommendDiscoveryM setChannelId(long channelId) {
        this.channelId = channelId;
        return this;
    }

    public String getDisplayClass() {
        return displayClass;
    }

    public void setDisplayClass(String displayClass) {
        this.displayClass = displayClass;
    }

    public String getBackColor() {
        return backColor;
    }

    public void setBackColor(String backColor) {
        this.backColor = backColor;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.id);
        dest.writeString(this.title);
        dest.writeString(this.subtitle);
        dest.writeString(this.coverPath);
        dest.writeString(this.contentType);
        dest.writeString(this.url);
        dest.writeString(this.sharePic);
        dest.writeByte(this.enableShare ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isHot ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isExternalUrl ? (byte) 1 : (byte) 0);
        dest.writeLong(this.contentUpdatedAt);
        dest.writeString(this.bubbleText);
        dest.writeParcelable(this.properties, flags);
        dest.writeLong(this.channelId);
        dest.writeString(this.displayClass);
        dest.writeString(this.backColor);
    }

    protected RecommendDiscoveryM(Parcel in) {
        this.id = in.readInt();
        this.title = in.readString();
        this.subtitle = in.readString();
        this.coverPath = in.readString();
        this.contentType = in.readString();
        this.url = in.readString();
        this.sharePic = in.readString();
        this.enableShare = in.readByte() != 0;
        this.isHot = in.readByte() != 0;
        this.isExternalUrl = in.readByte() != 0;
        this.contentUpdatedAt = in.readLong();
        this.bubbleText = in.readString();
        this.properties = in.readParcelable(RecommendDiscoveryProps.class.getClassLoader());
        this.channelId = in.readLong();
        this.displayClass = in.readString();
        this.backColor = in.readString();
    }

    public static final Creator<RecommendDiscoveryM> CREATOR = new Creator<RecommendDiscoveryM>() {
        @Override
        public RecommendDiscoveryM createFromParcel(Parcel source) {
            return new RecommendDiscoveryM(source);
        }

        @Override
        public RecommendDiscoveryM[] newArray(int size) {
            return new RecommendDiscoveryM[size];
        }
    };
}
