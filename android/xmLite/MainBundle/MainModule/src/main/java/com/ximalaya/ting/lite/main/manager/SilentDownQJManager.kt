package com.ximalaya.ting.lite.main.manager

import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.hybrid.utils.MD5Tool
import com.ximalaya.ting.android.host.listener.IInstallApkCallBack
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.server.NetworkUtils
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.android.xmsysteminvoke.XmSystemInvokeManager
import com.ximalaya.ting.lite.main.download.bean.TaskInfo
import com.ximalaya.ting.lite.main.download.inter.DownloadListener
import com.ximalaya.ting.lite.main.model.QJAppInfoModel
import java.io.File

object SilentDownQJManager {

    private const val TAG = "SilentDownQJManager"

    private const val KEY_DOWN_APK_DATE = "key_down_apk_date"

    private const val KEY_DOWN_APK_PATH = "key_down_apk_path"

    private const val KEY_DOWN_APK_SIGN = "key_down_apk_sign"

    private var mDownloadListener = object : DownloadListener {
        override fun onTaskStart(task: TaskInfo?) {
            FuliLogger.log(TAG, "开始下载")
        }

        override fun onTaskSuccess(task: TaskInfo?) {
            FuliLogger.log(TAG, "下载成功")
        }

        override fun onTaskFailed(task: TaskInfo?) {
            FuliLogger.log(TAG, "下载失败,请重试")
        }

        override fun onTaskProgress(task: TaskInfo?, progress: Int) {
        }
    }

    fun checkSaveData(file: File?) {
        if (file == null || !file.exists()) {
            return
        }
        val context = BaseApplication.getMyApplicationContext()
        MmkvCommonUtil.getInstance(context).saveString(KEY_DOWN_APK_PATH, file.path)

        MmkvCommonUtil.getInstance(context)
            .saveString(KEY_DOWN_APK_DATE, DateTimeUtil.getCurrentDate4yyyyMMdd())

        // 存储签名的md5
        MmkvCommonUtil.getInstance(context)
            .saveString(KEY_DOWN_APK_SIGN, DownFileManager.encrypt(MD5Tool.md5(file)))
    }

    /**
     * 检查并安装app  不触发下载
     */
    @JvmStatic
    fun checkInstallQjApp(callback: IInstallApkCallBack?) {
        var forceTheLatestApk = false
        val config = ConfigureCenter.getInstance()
            .getJson(CConstants.Group_Base.GROUP_NAME, CConstants.Group_Base.ITEM_QIJI_DOWN_CONFIG)
        forceTheLatestApk =
            config?.optBoolean("forceTheLatestApk", forceTheLatestApk) ?: forceTheLatestApk

        // 不强制使用最新版本apk
        if (!forceTheLatestApk) {
            if (!checkInstallQjApp(true)) {
                callback?.onResult(false)
            } else {
                callback?.onResult(true)
            }
            return
        } else {
            if (!checkInstallQjApp(false)) {
                callback?.onResult(false)
                return
            }
        }

        val context = BaseApplication.getMyApplicationContext()
        DownQjApkManager.getDownloadInfo(object : IDataCallBack<QJAppInfoModel> {
            override fun onSuccess(model: QJAppInfoModel?) {
                if (model != null) {
                    val localPath = MmkvCommonUtil.getInstance(context).getString(KEY_DOWN_APK_PATH)
                    // 文件发生了变化  清除本地文件
                    if (!localPath.isNullOrEmpty()) {
                        val remoteFile = DownFileManager.getDownFile(model)
                        if (remoteFile.path != localPath) {
                            FuliLogger.log(TAG, "本地文件和线上配置不一致,删除")
                            MmkvCommonUtil.getInstance(context).saveString(KEY_DOWN_APK_PATH, "")
                            MmkvCommonUtil.getInstance(context).saveString(KEY_DOWN_APK_SIGN, "")
                            val localFile = File(localPath)
                            if (localFile.exists()) {
                                localFile.delete()
                            }
                        } else {
                            DownFileManager.installApp(context, remoteFile)
                            callback?.onResult(true)
                            return
                        }
                    }
                }
                callback?.onResult(false)
            }

            override fun onError(code: Int, message: String?) {
                callback?.onResult(false)
            }
        })

    }

    /**
     * 检查并安装app  不触发下载
     */
    @JvmStatic
    fun checkInstallQjApp(isInstall: Boolean): Boolean {
        val context = BaseApplication.getMyApplicationContext()
        val path = MmkvCommonUtil.getInstance(context).getString(KEY_DOWN_APK_PATH)
        if (path.isNullOrEmpty()) {
            FuliLogger.log(TAG, "未成功缓存过安装包")
            return false
        }

        val file = File(path)
        if (!file.exists()) {
            MmkvCommonUtil.getInstance(context).saveString(KEY_DOWN_APK_PATH, "")
            FuliLogger.log(TAG, "缓存安装包被删除")
            return false
        }

        val sign = MmkvCommonUtil.getInstance(context).getString(KEY_DOWN_APK_SIGN)
        if (!DownFileManager.checkApk(file, sign)) {
            FuliLogger.log(TAG, "签名不一致")
            file.delete()
            MmkvCommonUtil.getInstance(context).saveString(KEY_DOWN_APK_SIGN, "")
            return false
        }

        if (isInstall) {
            DownFileManager.installApp(context, file)
        }
        return true
    }

    /**
     * 触发静默下载app 不安装
     */
    @JvmStatic
    fun checkDownQJApk(from: String) {
        FuliLogger.log(TAG, "检查静默下载$from")

        val isOpen = ConfigureCenter.getInstance().getBool(
            CConstants.Group_Base.GROUP_NAME, CConstants.Group_Base.ITEM_QIJI_DOWN_SWITCH, false
        )

        if (!isOpen) {
            FuliLogger.log(TAG, "开关关闭,不允许下载")
            return
        }

        var diffDate = 15
        val config = ConfigureCenter.getInstance()
            .getJson(CConstants.Group_Base.GROUP_NAME, CConstants.Group_Base.ITEM_QIJI_DOWN_CONFIG)
        diffDate = config?.optInt("diffDate", diffDate) ?: diffDate

        val context = BaseApplication.getMyApplicationContext()

        if (diffDate > 0) {
            var downDate = MmkvCommonUtil.getInstance(context).getString(KEY_DOWN_APK_DATE)
            val curDate = DateTimeUtil.getCurrentDate4yyyyMMdd()
            if (downDate > curDate) {
                downDate = curDate
                MmkvCommonUtil.getInstance(context).saveString(KEY_DOWN_APK_DATE, curDate)
            }
            if (downDate.isNotEmpty() && downDate <= curDate && DateTimeUtil.getDiffDay(
                    downDate,
                    curDate
                ) < diffDate
            ) {
                FuliLogger.log(
                    TAG,
                    "距离上次下载时间:${DateTimeUtil.getDiffDay(downDate, curDate)}小于${diffDate}天"
                )
                return
            }
        }

        val netType = NetworkUtils.getNetType(context)
        if (netType != NetworkUtils.NETWORK_TYPE_WIFI) {
            FuliLogger.log(TAG, "非wifi网络:$netType")
            return
        }

        val qjAppId = "reader.com.xmly.xmlyreader"
        if (XmSystemInvokeManager.isAppInstalled(context, qjAppId)) {
            FuliLogger.log(TAG, "奇迹已安装")
            return
        }

        val localPath = MmkvCommonUtil.getInstance(context).getString(KEY_DOWN_APK_PATH)
        if (!localPath.isNullOrEmpty()) {
            DownQjApkManager.getDownloadInfo(object : IDataCallBack<QJAppInfoModel> {
                override fun onSuccess(model: QJAppInfoModel?) {
                    if (model != null) {
                        val remoteFile = DownFileManager.getDownFile(model)
                        // 文件发生了变化  清除本地文件
                        if (remoteFile.path != localPath) {
                            FuliLogger.log(TAG, "本地文件和线上不一致,删除")
                            MmkvCommonUtil.getInstance(context).saveString(KEY_DOWN_APK_PATH, "")
                            MmkvCommonUtil.getInstance(context).saveString(KEY_DOWN_APK_SIGN, "")
                            val localFile = File(localPath)
                            if (localFile.exists()) {
                                localFile.delete()
                            }
                        }
                    }

                    performQJDownApk(model)
                }

                override fun onError(code: Int, message: String?) {
                    performQJDownApk(null)
                }
            })
        } else {
            performQJDownApk(null)
        }
    }

    @JvmStatic
    private fun performQJDownApk(model: QJAppInfoModel?) {
        if (checkInstallQjApp(false)) {
            FuliLogger.log(TAG, "奇迹已下载完成,不处理")
            return
        }

        DownQjApkManager.addDownListener(mDownloadListener)
        DownQjApkManager.checkSilentDownQJApp(model)
    }

}