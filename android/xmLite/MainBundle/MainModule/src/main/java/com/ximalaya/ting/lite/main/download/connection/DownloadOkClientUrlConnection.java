package com.ximalaya.ting.lite.main.download.connection;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.httputil.Config;
import com.ximalaya.ting.lite.main.download.inter.IDownloadConnection;

import java.io.IOException;
import java.io.InputStream;
import java.net.ProtocolException;
import java.util.List;
import java.util.Map;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * <AUTHOR> feiwen
 * date   : 2019-07-29
 * desc   :
 */
public class DownloadOkClientUrlConnection implements IDownloadConnection {
    @NonNull final OkHttpClient client;
    @NonNull private final Request.Builder requestBuilder;
    private Request request;
    Response response;

    public DownloadOkClientUrlConnection(String originUrl, Config configuration) {
        client = BaseCall.getInstanse().getOkHttpClient();
        requestBuilder = new Request.Builder().url(originUrl);
        if (configuration != null && configuration.property != null) {
            for (Map.Entry<String, String> entry : configuration.property.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    @Override
    public void addHeader(String name, String value) {
        this.requestBuilder.addHeader(name, value);
    }

    @Override
    public boolean setRequestMethod(@NonNull String method) throws ProtocolException {
        this.requestBuilder.method(method, null);
        return true;
    }

    @Override
    public IDownloadConnection execute() throws IOException {
        request = requestBuilder.build();
        response = client.newCall(request).execute();
        return this;
    }

    @Override
    public void disconnect() {
        request = null;
        if (response != null) response.close();
        response = null;
    }

    @Override
    public Map<String, List<String>> getRequestProperties() {
        if (request != null) {
            return request.headers().toMultimap();
        } else {
            return requestBuilder.build().headers().toMultimap();
        }
    }

    @Override
    public String getRequestProperty(String key) {
        if (request != null) {
            return request.header(key);
        } else {
            return requestBuilder.build().header(key);
        }
    }

    @Override
    public int getResponseCode() throws IOException {
        if (response == null) throw new IOException("Please invoke execute first!");
        return response.code();
    }

    @Override
    public InputStream getInputStream() throws IOException {
        if (response == null) throw new IOException("Please invoke execute first!");
        final ResponseBody body = response.body();
        if (body == null) throw new IOException("no body found on response!");
        return body.byteStream();
    }

    @Nullable
    @Override
    public Map<String, List<String>> getResponseHeaderFields() {
        return response == null ? null : response.headers().toMultimap();
    }

    @Nullable
    @Override
    public String getResponseHeaderField(String name) {
        return response == null ? null : response.header(name);
    }

    @Nullable
    @Override
    public String getResponseHeaderField2(String name) {
        return null;
    }

    /**
     * TODO
     */
    @Override
    public String getRedirectLocation() {
        return null;
    }

    public static class Factory implements IDownloadConnection.Factory {

        @Override
        public IDownloadConnection create(String originUrl, Config configuration) throws IOException {
            return new DownloadOkClientUrlConnection(originUrl, configuration);
        }

    }
}
