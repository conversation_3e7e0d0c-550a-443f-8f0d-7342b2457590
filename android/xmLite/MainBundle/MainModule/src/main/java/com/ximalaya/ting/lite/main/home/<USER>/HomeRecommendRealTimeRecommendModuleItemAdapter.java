package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;

import java.util.List;

/**
 * Created by WolfXu on 2019/9/17.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class HomeRecommendRealTimeRecommendModuleItemAdapter extends AbRecyclerViewAdapter<HomeRecommendRealTimeRecommendModuleItemAdapter.ItemViewHolder> {

    private Activity mActivity;
    private List<RecommendItemNew> mData;
    private BaseFragment2 mFragment;

    public HomeRecommendRealTimeRecommendModuleItemAdapter(BaseFragment2 fragment) {
        mFragment = fragment;
        if (mFragment != null) {
            mActivity = mFragment.getActivity();
        }
        if (mActivity == null) {
            mActivity = BaseApplication.getOptActivity();
        }
    }

    @Override
    public Object getItem(int i) {
        if (mData != null && i >= 0 && i < mData.size()) {
            return mData.get(i);
        }
        return null;
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_item_real_time_recommend, parent,
                false);
        return new ItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ItemViewHolder holder, int position) {
        if (mData != null && position >= 0 && position < mData.size()) {
            RecommendItemNew recommendItem = mData.get(position);
            if (recommendItem.getItemType() != null) {
                switch (recommendItem.getItemType()) {
                case RecommendItemNew.RECOMMEND_ITEM_ALBUM:
                    if (recommendItem.getItem() instanceof AlbumM) {
                        bindAlbumItemData((AlbumM) recommendItem.getItem(), holder);
                    }
                    break;
                case RecommendItemNew.RECOMMEND_ITEM_TRACK:
                    if (recommendItem.getItem() instanceof TrackM) {
                        bindTrackItemData((TrackM) recommendItem.getItem(), holder);
                    }
                    break;
                default:
                    break;
                }
            }
        }
    }

    private void bindAlbumItemData(final AlbumM albumM, ItemViewHolder holder) {
        holder.tvTitle.setText(albumM.getAlbumTitle());
        ImageManager.from(mActivity).displayImage(holder.ivCover, albumM.getValidCover(), R.drawable.host_default_album_145);
        holder.tvPlayCount.setText(StringUtil.getFriendlyNumStr(albumM.getPlayCount()));
        holder.tvPlayCount.setVisibility(View.VISIBLE);
        if(AlbumTagUtil.getAlbumCoverTag(albumM) != -1) {
            holder.ivAlbumCoverTag.setImageResource(AlbumTagUtil.getAlbumCoverTag(albumM));
            holder.ivAlbumCoverTag.setVisibility(View.VISIBLE);
        } else {
            holder.ivAlbumCoverTag.setVisibility(View.INVISIBLE);
        }
        holder.ivPlayBtn.setVisibility(View.INVISIBLE);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                handleAlbumItemClick(albumM);
            }
        });
    }

    private void handleAlbumItemClick(AlbumM albumM) {
        AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_OTHER
                , ConstantsOpenSdk.PLAY_FROM_OTHER, albumM.getRecSrc(), albumM
                        .getRecTrack()
                , -1, BaseApplication.getOptActivity());
    }

    private void bindTrackItemData(final TrackM trackM, final ItemViewHolder holder) {
        holder.tvTitle.setText(trackM.getTrackTitle());
        ImageManager.from(mActivity).displayImage(holder.ivCover, trackM.getValidCover(), R.drawable.host_default_album_145);
        holder.tvPlayCount.setVisibility(View.INVISIBLE);
        holder.ivPlayBtn.setVisibility(View.VISIBLE);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PlayTools.playTrack(mActivity, trackM, true, holder.itemView);
            }
        });
    }

    @Override
    public int getItemCount() {
        if (mData != null) {
            return mData.size();
        }
        return 0;
    }

    public void setData(List<RecommendItemNew> data) {
        mData = data;
    }

    public static final class ItemViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivCover;
        private TextView tvTitle;
        private TextView tvPlayCount;
        private ImageView ivAlbumCoverTag;
        private ImageView ivPlayBtn;

        ItemViewHolder(View view) {
            super(view);
            ivCover = itemView.findViewById(R.id.main_iv_cover);
            tvTitle = itemView.findViewById(R.id.main_tv_title);
            tvPlayCount = itemView.findViewById(R.id.main_tv_play_count);
            ivAlbumCoverTag = itemView.findViewById(R.id.main_iv_album_cover_tag);
            ivPlayBtn = itemView.findViewById(R.id.main_iv_play_btn);
        }
    }
}
