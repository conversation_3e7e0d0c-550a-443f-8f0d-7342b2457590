package com.ximalaya.ting.lite.main.model.mine;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;

/**
 * Created by qin<PERSON>feng on 2019-11-18
 *
 * <AUTHOR>
 */
public class TaskCenterPopResourceModel {
    @SerializedName("image")
    public String image;

    @SerializedName("targetUrl")
    public String targetUrl;

    @SerializedName("name")
    public String name;

    //每天最多展示几次
    @SerializedName("showTimesOneDay")
    public int showTimesOneDay;

    public boolean checkParams() {
        if (TextUtils.isEmpty(targetUrl)) {
            return false;
        }
        if (TextUtils.isEmpty(image)) {
            return false;
        }
        return true;
    }
}
