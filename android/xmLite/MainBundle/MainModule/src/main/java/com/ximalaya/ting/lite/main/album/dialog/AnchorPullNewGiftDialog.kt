package com.ximalaya.ting.lite.main.album.dialog

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.text.Html
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt
import com.ximalaya.ting.android.host.manager.LiteEncryptManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.constant.MMKV_ANCHOR_RECEIVE_GIFT
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.view.ComProgressDialog
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.manager.AnchorPullNewManager
import com.ximalaya.ting.lite.main.model.AnchorPullNewQueryModel
import com.ximalaya.ting.lite.main.model.AnchorPullNewRewardModel
import org.json.JSONObject


class AnchorPullNewGiftDialog : BaseFullScreenDialogFragment() {

    val mTAG = "AnchorPullNewGiftDialog"

    private var mGroupNormal: Group? = null
    private var mGroupRule: Group? = null
    private var mClAlbum: ConstraintLayout? = null
    private var mIvAlbumCover: RoundImageView? = null
    private var mTvAlbumTitle: TextView? = null
    private var mTvAlbumSubTitle: TextView? = null
    private var mTvConfirm: TextView? = null
    private var mTvRuleContent: TextView? = null
    private var mTvErrTips: TextView? = null
    private var mEtCode: EditText? = null

    private var mClInput: ConstraintLayout? = null
    private var mClResult: ConstraintLayout? = null

    // 活动入口view
    var activityEnterView: ViewGroup? = null

    private var mAnchorPullNewQueryModel: AnchorPullNewQueryModel? = null

    private var mIsRequestReceiver = false

    private var mLoadingDialog: ComProgressDialog? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.main_dialog_anchor_pull_new_gift_layout, container, false)

        initView(view)

        return view
    }

    private fun initView(view: View?) {
        mGroupNormal = view?.findViewById(R.id.main_group_normal)
        mGroupRule = view?.findViewById(R.id.main_group_rule)
        mEtCode = view?.findViewById<EditText?>(R.id.main_et_code)
        mClAlbum = view?.findViewById(R.id.main_cl_album)
        mIvAlbumCover = view?.findViewById(R.id.main_iv_cover)
        mTvAlbumTitle = view?.findViewById(R.id.main_tv_album_title)
        mTvAlbumSubTitle = view?.findViewById(R.id.main_tv_album_sub_title)
        mTvConfirm = view?.findViewById(R.id.main_tv_confirm)
        mTvRuleContent = view?.findViewById(R.id.main_tv_rule_content)
        mTvErrTips = view?.findViewById(R.id.main_tv_error_tips)

        mClInput = view?.findViewById(R.id.main_cl_input)
        mClResult = view?.findViewById(R.id.main_cl_result)

        var defaultRule = "1. 仅部分主播及专辑参与本次活动，请正确填写邀请码才可获得奖励；<br>2. 活动奖品类型包含金币和体验会员，系统将根据的您的设备情况随机发放，请以实际获得的奖励为准；<br>3. 若系统判断您存在违规行为，将有权扣除您违规获得的金币或者冻结您的账号。"
        defaultRule = ConfigureCenter.getInstance().getString(CConstants.Group_Base.GROUP_NAME, CConstants.Group_Base.ITEM_ANCHOR_ULL_NEW_RULE, defaultRule)
        if (Build.VERSION.SDK_INT >= 24) {
            mTvRuleContent?.text = Html.fromHtml(defaultRule, Html.FROM_HTML_MODE_LEGACY)
        } else {
            mTvRuleContent?.text = Html.fromHtml(defaultRule)
        }


        view?.findViewById<ImageView>(R.id.main_iv_close)?.setOnClickListener {
            dismiss()
        }

        view?.findViewById<ImageView>(R.id.main_iv_back)?.setOnClickListener {
            mGroupNormal?.visibility = View.VISIBLE
            mGroupRule?.visibility = View.GONE
        }

        view?.findViewById<TextView?>(R.id.main_tv_rule)?.setOnClickListener {
            mGroupNormal?.visibility = View.GONE
            mGroupRule?.visibility = View.VISIBLE

            // 专辑页-主播拉新-查看规则  点击事件
            XMTraceApi.Trace()
                .click(49192) // 用户点击时上报
                .put("currPage", "albumPage")
                .createTrace()
        }

        mTvConfirm?.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }

            val inviteCode = mEtCode?.text.toString().trim()
            if (inviteCode.isEmpty()) {
                ToastManager.showToast("请输入主播邀请码")
                return@setOnClickListener
            }
            // 先去查询礼包
            if (mClAlbum?.visibility != View.VISIBLE) {
                // 专辑页-主播拉新-查询信息  点击事件
                XMTraceApi.Trace()
                    .click(49190) // 用户点击时上报
                    .put("currPage", "albumPage")
                    .createTrace()

                performQueryAlbum(inviteCode)
            } else {
                // 专辑页-主播拉新-领取主播好礼  点击事件
                XMTraceApi.Trace()
                    .click(49193) // 用户点击时上报
                    .put("currAlbumId", mAnchorPullNewQueryModel?.albumId.toString())
                    .put("currPage", "albumPage")
                    .createTrace()

                hiddenInputMethod(mEtCode)
                // 领取礼包
                performReceiverAlbumGift(inviteCode)
            }
        }

        // 专辑页-主播拉新-查询信息  控件曝光
        XMTraceApi.Trace()
            .setMetaId(49191)
            .setServiceId("slipPage")
            .put("currPage", "albumPage")
            .createTrace()
    }

    private fun performReceiverAlbumGift(inviteCode: String) {
        receiverAlbumGift(inviteCode, object : AnchorPullNewManager.IRequestCallBack<AnchorPullNewRewardModel> {
            override fun onResult(model: AnchorPullNewRewardModel) {
                // 保存礼包领取成功状态
                MmkvCommonUtil.getInstance(context).saveBoolean(MMKV_ANCHOR_RECEIVE_GIFT, true)

                if (!canUpdateUi()) {
                    return
                }
                // 隐藏活动入口
                activityEnterView?.visibility = View.GONE

                mClInput?.visibility = View.GONE
                mClResult?.visibility = View.VISIBLE

                val lottieView = mClResult?.findViewById<XmLottieAnimationView>(R.id.main_lv_gift_animal)

                lottieView?.visibility = View.VISIBLE
                lottieView?.removeAllAnimatorListeners()
                lottieView?.addAnimatorListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationCancel(animation: Animator) {
                        super.onAnimationCancel(animation)
                        showReceiverResult(lottieView, model)
                    }

                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        showReceiverResult(lottieView, model)
                    }
                })
                lottieView?.playAnimation()

                // 主播拉新-奖励弹窗  弹框展示
                XMTraceApi.Trace()
                    .setMetaId(49195)
                    .setServiceId("dialogView") // 弹窗展示时上报
                    .put("currAlbumId", mAnchorPullNewQueryModel?.albumId.toString())
                    // 1:金币，2:现金，3:会员
                    .put("prizeType", if (model.rewardType == 3) {
                        "vip"
                    } else {
                        "coin"
                    })
                    .put("currPage", "albumPage")
                    .createTrace()
            }
        })
    }

    private fun showReceiverResult(lottieView: XmLottieAnimationView?, model: AnchorPullNewRewardModel) {
        if (canUpdateUi()) {
            lottieView?.visibility = View.GONE

            val tvTitle = mClResult?.findViewById<TextView?>(R.id.main_tv_title_result)
            val tvSubTitle = mClResult?.findViewById<TextView?>(R.id.main_tv_sub_title_result)
            val ivContent = mClResult?.findViewById<ImageView?>(R.id.main_iv_content_result)
            var color = Color.parseColor("#99FFE690")
            tvTitle?.setShadowLayer(10f, 0f, 0f, color)
            tvSubTitle?.setShadowLayer(10f, 0f, 0f, color)

            mClResult?.findViewById<TextView?>(R.id.main_tv_confirm_result)?.setOnClickListener {
                dismiss()
            }
            color = Color.parseColor("#FFE784")

            //1:金币，2:现金，3:会员
            when (model.rewardType) {
                3 -> {
                    // 七天会员礼包
                    tvTitle?.text = "恭喜你获得"
                    val span = SpannableString("喜马拉雅极速版${model.reward}天会员")
                    span.setSpan(ForegroundColorSpan(color), 7, span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    tvSubTitle?.text = span
                    ImageManager.from(ivContent?.context).displayImageOfResourceId(ivContent, R.drawable.main_ic_anchor_pull_new_member)
                }
                1 -> {
                    // 金币礼包
                    tvTitle?.text = "您已参与过其他活动"
                    val span = SpannableString("送您${model.reward}金币")
                    span.setSpan(ForegroundColorSpan(color), 2, span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    tvSubTitle?.text = span
                    ImageManager.from(ivContent?.context).displayImageOfResourceId(ivContent, R.drawable.main_ic_anchor_pull_new_coin)
                }
            }

            val groupResult = mClResult?.findViewById<ConstraintLayout?>(R.id.main_cl_result_end)
            groupResult?.run {
                visibility = View.VISIBLE
                val scaleX = ObjectAnimator.ofFloat(this, "scaleX", 0f, 1f)
                val scaleY = ObjectAnimator.ofFloat(this, "scaleY", 0f, 1f)
                val animatorSet = AnimatorSet()
                animatorSet.playTogether(scaleX, scaleY)
                animatorSet.duration = 300
                animatorSet.start()
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun performQueryAlbum(inviteCode: String) {
        queryCodeAlbum(inviteCode, object : AnchorPullNewManager.IRequestCallBack<AnchorPullNewQueryModel> {

            override fun onResult(result: AnchorPullNewQueryModel) {
                FuliLogger.log(mTAG, "礼包查询成功 result:$result")
                mAnchorPullNewQueryModel = result

                if (result.isShowError || result.albumName.isNullOrEmpty() || result.anchorName.isNullOrEmpty() || result.coverPath.isNullOrEmpty()) {
                    mTvErrTips?.visibility = View.VISIBLE
                } else {
                    mTvErrTips?.visibility = View.GONE
                    mTvAlbumTitle?.text = result.albumName
                    mTvAlbumSubTitle?.text = "主播：${result.anchorName}"
                    ImageManager.from(context).displayImage(mIvAlbumCover, result.coverPath, R.drawable.host_default_album)

                    mClAlbum?.visibility = View.VISIBLE
                    mTvConfirm?.text = "领取主播好礼"

                    hiddenInputMethod(mEtCode)
                }

                // 专辑页-主播拉新-领取主播好礼  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(49194)
                    .setServiceId("slipPage")
                    .put("currAlbumId", result.albumId.toString())
                    .put("currPage", "albumPage")
                    .createTrace()
            }
        })
    }

    private fun queryCodeAlbum(inviteCode: String, callback: AnchorPullNewManager.IRequestCallBack<AnchorPullNewQueryModel>?) {
        if (mLoadingDialog?.isShowing == true) {
            mLoadingDialog?.dismiss()
        }

        mLoadingDialog = ComProgressDialog(activity)
        mLoadingDialog?.show()

        val map = mutableMapOf<String, String?>()
        val url = UrlConstants.getInstanse().serverNetAddressHost + "lite-mobile/anchor/pullnew/query/$inviteCode"

        CommonRequestM.baseGetRequest(url, map, object : IDataCallBack<AnchorPullNewQueryModel?> {
            override fun onSuccess(result: AnchorPullNewQueryModel?) {
                mLoadingDialog?.dismiss()
                if (result != null) {
                    callback?.onResult(result)
                } else {
                    FuliLogger.log(mTAG, "礼包查询失败 result is null")
                    ToastManager.showToast("礼包查询失败")
                }
            }

            override fun onError(code: Int, message: String?) {
                mLoadingDialog?.dismiss()
                ToastManager.showToast(message ?: "礼包查询失败")
                FuliLogger.log(mTAG, "礼包查询失败 code:$code message:$message")
            }
        }) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            if (ret == 0) {
                val data = json.optString("data")
                if (data.isNullOrEmpty() || data == "{}") {
                    AnchorPullNewQueryModel(0, null, null, 0, null, isShowError = true)
                } else {
                    JsonUtilKt.instance.toObjectOfType<AnchorPullNewQueryModel>(json.optString("data"),
                        object : TypeToken<AnchorPullNewQueryModel>() {}.type)
                }
            } else null
        }
    }

    private fun receiverAlbumGift(inviteCode: String, callback: AnchorPullNewManager.IRequestCallBack<AnchorPullNewRewardModel>?) {
        if (mIsRequestReceiver) {
            return
        }
        mIsRequestReceiver = true

        if (mLoadingDialog?.isShowing == true) {
            mLoadingDialog?.dismiss()
        }

        mLoadingDialog = ComProgressDialog(activity)
        mLoadingDialog?.show()

        val map = mutableMapOf<String, String?>()
        map["albumId"] = mAnchorPullNewQueryModel?.albumId.toString()
        map["anchorUid"] = mAnchorPullNewQueryModel?.anchorId.toString()
        map["inviteCode"] = inviteCode
        map["timestamp"] = System.currentTimeMillis().toString()
        map["uid"] = UserInfoMannage.getUid().toString()
        map["signature"] = LiteEncryptManager.getActivitySignature(activity, map)
        map.remove("uid")

        val url = UrlConstants.getInstanse().serverNetAddressHost + "lite-mobile/anchor/pullnew/reward"

        CommonRequestM.basePostRequestParmasToJson(url, map, object : IDataCallBack<AnchorPullNewRewardModel?> {
            override fun onSuccess(result: AnchorPullNewRewardModel?) {
                mLoadingDialog?.dismiss()
                mIsRequestReceiver = false
                if (result != null) {
                    callback?.onResult(result)
                } else {
                    FuliLogger.log(mTAG, "礼包领取失败 result is null")
                    ToastManager.showToast("礼包领取失败")
                }
            }

            override fun onError(code: Int, message: String?) {
                mLoadingDialog?.dismiss()
                mIsRequestReceiver = false
                ToastManager.showToast(message ?: "礼包领取失败")
                FuliLogger.log(mTAG, "礼包领取失败 code:$code message:$message")
            }
        }) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            if (ret == 0) {
                JsonUtilKt.instance.toObjectOfType<AnchorPullNewRewardModel>(json.optString("data"),
                    object : TypeToken<AnchorPullNewRewardModel>() {}.type)
            } else null
        }
    }

    private fun hiddenInputMethod(view: View?) {
        val imm = context?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager?
        imm?.hideSoftInputFromWindow(view?.windowToken, 0) //强制隐藏键盘
    }

    override fun isShowFromBottomEnable(): Boolean {
        return true
    }
}