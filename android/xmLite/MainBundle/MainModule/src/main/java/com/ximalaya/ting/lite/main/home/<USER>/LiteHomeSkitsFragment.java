//package com.ximalaya.ting.lite.main.home.fragment;
//
//import android.os.Bundle;
//import android.util.Log;
//import android.view.View;
//import android.widget.LinearLayout;
//import android.widget.TextView;
//
//import androidx.fragment.app.Fragment;
//import androidx.fragment.app.FragmentManager;
//
//import com.bytedance.sdk.dp.DPDramaDetailConfig;
//import com.bytedance.sdk.dp.DPSdk;
//import com.bytedance.sdk.dp.DPWidgetDramaDetailParams;
//import com.ximalaya.ting.android.host.PluginStrategyManger;
//import com.ximalaya.ting.android.host.fragment.BaseFragment2;
//import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
//import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
//import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
//import com.ximalaya.ting.android.host.util.BundleRouterIntercept;
//import com.ximalaya.ting.android.main.R;
//import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
//import com.ximalaya.ting.android.xmtrace.XMTraceApi;
//
///**
// * 短视频页面
// *
// * <AUTHOR>
// */
//public class LiteHomeSkitsFragment extends BaseFragment2 {
//    private long skitsId;
//
//
//    public static LiteHomeSkitsFragment newInstance(long skitsId) {
//        Bundle args = new Bundle();
//        args.putLong("id", skitsId);
//        LiteHomeSkitsFragment fragment = new LiteHomeSkitsFragment();
//        fragment.setArguments(args);
//        return fragment;
//    }
//
//    @Override
//    protected String getPageLogicName() {
//        return "LiteHomeSkitsFragment";
//    }
//
//    @Override
//    protected void initUi(Bundle savedInstanceState) {
//        if (getArguments() != null) {
//            skitsId = getArguments().getLong("id");
//        }
//        if (skitsId == 0) {
//            return;
//        }
//        DPWidgetDramaDetailParams dramaDetailParams = DPWidgetDramaDetailParams.obtain();
//        dramaDetailParams.id = skitsId;
//        DPDramaDetailConfig dpDramaDetailConfig = DPDramaDetailConfig.obtain(DPDramaDetailConfig.COMMON_DETAIL);
//        dpDramaDetailConfig.id = skitsId;
//        dpDramaDetailConfig.mCloseListener = new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                finishFragment();
//            }
//        };
//        dramaDetailParams.detailConfig(dpDramaDetailConfig);
//        Fragment fragment = DPSdk.factory().createDramaDetail(dramaDetailParams).getFragment();
//        if (fragment == null) {
//            return;
//        }
//        FragmentManager manager = getChildFragmentManager();
//        manager.beginTransaction().replace(R.id.fl_skits_container, fragment).commitNowAllowingStateLoss();
//    }
//
//    @Override
//    protected void loadData() {
//
//    }
//
//    @Override
//    protected boolean isShowPlayButton() {
//        return false;
//    }
//
//    @Override
//    protected boolean isShowCoinGuide() {
//        return false;
//    }
//
//
//    @Override
//    public int getContainerLayoutId() {
//        return R.layout.main_home_skits_fragment;
//    }
//}
//
//
//
