package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.adapter.album.BaseAlbumAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.NewUserMustListenerModel;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;

import java.util.ArrayList;
import java.util.List;

import static com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk.PLAY_FROM_OTHER;

/**
 * Created by dumingwei on 2020/8/18
 * <p>
 * Desc: 糖葫芦下的信息流数据
 */
public class TanghuluHotWordFeedProvider implements IMulitViewTypeViewAndData<TanghuluHotWordFeedProvider.ViewHolder, NewUserMustListenerModel> {

    private Activity mActivity;
    protected BaseFragment2 mFragment;

    public TanghuluHotWordFeedProvider(BaseFragment2 baseFragment2) {
        mFragment = baseFragment2;
        mActivity = baseFragment2.getActivity();
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_vip_hot_word_album, null);
    }

    @Override
    public ViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(ViewHolder viewHolder, final ItemModel<NewUserMustListenerModel> t, View convertView, final int position) {
        if (t == null || t.object == null) {
            return;
        }
        NewUserMustListenerModel model = t.getObject();
        NewUserMustListenerModel.Item album = model.getAlbum();
        AutoTraceHelper.bindData(viewHolder.root, AutoTraceHelper.MODULE_DEFAULT, model);
        if (album != null) {

            //添加专辑Item的ContentDescription
            if (viewHolder.root != null) {
                if (!TextUtils.isEmpty(album.getTitle())) {
                    viewHolder.root.setContentDescription(album.getTitle());
                } else {
                    viewHolder.root.setContentDescription("");
                }
            }
            ImageManager.from(mActivity).displayImage(viewHolder.cover, album.getCoverPath(),
                    R.drawable.host_default_album_145, R.drawable.host_default_album_145);

            int albumCoverTag = AlbumTagUtil.getAlbumCoverTag(album);
            if (albumCoverTag != -1) {
                viewHolder.ivTag.setImageResource(albumCoverTag);
                viewHolder.ivTag.setVisibility(View.VISIBLE);
            } else {
                viewHolder.ivTag.setVisibility(View.INVISIBLE);
            }

            int textSize = (int) viewHolder.title.getTextSize();

            Integer serialState = album.getSerialState();
            if (serialState != null) {
                Spanned richTitle = getRichTitle(album.getTitle(), serialState, mActivity, textSize);
                viewHolder.title.setText(richTitle);
            } else {
                viewHolder.title.setText(album.getTitle());
            }

            String subTitle = album.getIntro();
            if (!TextUtils.isEmpty(subTitle)) {
                viewHolder.subtitle.setText(Html.fromHtml(subTitle));
            } else {
                viewHolder.subtitle.setText("");
            }

            String playCountStr = StringUtil.getFriendlyNumStr(album.getPlaysCounts()) + "播放";
            int drawable = R.drawable.main_ic_common_play_count;
            BaseAlbumAdapter.addAlbumInfo(mActivity, viewHolder.layoutAlbumInfo, drawable, playCountStr, Color.parseColor("#999999"), false, false);
            BaseAlbumAdapter.addAlbumInfo(mActivity, viewHolder.layoutAlbumInfo, R.drawable.main_ic_common_track_count, StringUtil.getFriendlyNumStr(album.getTracks()) + " 集", Color.parseColor("#999999"));

            //设置item点击
            convertView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    AlbumEventManage.AlbumFragmentOption option = new AlbumEventManage.AlbumFragmentOption();
                    AlbumEventManage.startMatchAlbumFragment(album.getAlbumId(),
                            AlbumEventManage.FROM_OTHER, PLAY_FROM_OTHER, null, null,
                            -1, mActivity, option);
                }
            });
        }
    }

    private Spanned getRichTitle(String title, int status, Context context, int maxHeight) {
        if (context == null) {
            return null;
        }
        boolean isCompleteTag = status == 2;
        List<Integer> resList = new ArrayList<>();
        if (isCompleteTag) {
            // 完本标签
            resList.add(R.drawable.main_tag_complete_top_new);
        }
        String intro;
        if (resList.size() > 0) {
            intro = " " + title;
        } else {
            intro = title;
        }
        Spanned titleWithTag = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(context, intro, resList, resList.size(), maxHeight);
        return titleWithTag;
    }

    public class ViewHolder extends BaseAlbumAdapter.ViewHolder {

        private ImageView ivTag;

        public ViewHolder(View convertView) {
            super(convertView);
            ivTag = convertView.findViewById(R.id.main_iv_space_album_tag);
            cover = convertView.findViewById(R.id.main_iv_album_cover);
            border = convertView.findViewById(R.id.main_album_border);
            title = convertView.findViewById(R.id.main_tv_album_title);
            subtitle = convertView.findViewById(R.id.main_tv_album_subtitle);
            layoutAlbumInfo = convertView.findViewById(R.id.main_layout_album_info);
        }
    }
}
