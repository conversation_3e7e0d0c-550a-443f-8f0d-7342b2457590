package com.ximalaya.ting.lite.main.home.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;

import com.qq.e.ads.nativ.widget.NativeAdContainer;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.adsdk.platform.common.modelproxy.AbstractThirdAd;
import com.ximalaya.ting.android.host.adsdk.platform.gdt.view.GdtMediaViewContainer;
import com.ximalaya.ting.android.host.adsdk.provider.CommonNativeDaTuAdProvider;
import com.ximalaya.ting.android.host.adsdk.provider.viewmodel.SimpleDaTuViewModel;
import com.ximalaya.ting.android.host.business.unlock.callback.IInsertScreenAdCallBack;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.util.ContextUtils;
import com.ximalaya.ting.android.main.R;

import java.util.ArrayList;
import java.util.List;

/**
 * 首页自定义广告管理
 *
 * <AUTHOR>
 */
public class HomeCustomAdProvider {

    protected Context mContext;
    protected Holder mAdHolder;

    private final CommonNativeDaTuAdProvider mAdProvider;
    private final FrameLayout mAdContainer;

    private final IInsertScreenAdCallBack mAdCallBack;

    public HomeCustomAdProvider(Context context, FrameLayout adContainer, IInsertScreenAdCallBack callBack) {
        mContext = context;
        mAdProvider = new CommonNativeDaTuAdProvider(context);
        mAdContainer = adContainer;
        mAdCallBack = callBack;
    }

    public void onMyResume() {
        mAdProvider.onMyResume();
    }

    public void onDestroy() {
        //隐藏广告
        hideAd();
        mAdProvider.onDestroy();
    }

    /**
     * 绑定广告视图
     */
    public boolean bindViewData(AbstractThirdAd<?> thirdAd, final String positionName) {
        if (mAdContainer == null) {
            FuliLogger.log("插屏广告:mAdContainer null");
            if (mAdCallBack != null) {
                mAdCallBack.onSdkADError();
            }
            return false;
        }
        //不存在广告数据
        if (thirdAd.getAdData() == null) {
            FuliLogger.log("插屏广告:thirdAd.getAdData() null");
            if (mAdCallBack != null) {
                mAdCallBack.onNoSdkAd();
            }
            return false;
        }
        if (mAdHolder == null) {
            mAdHolder = new Holder(mAdContainer);
        }
        if (mAdContainer.getChildCount() == 0) {
            mAdContainer.addView(mAdHolder.convertView);
        }

        //图片的宽度
        int imageWith = (int) (BaseUtil.getScreenWidth(mContext) * 0.75f);
        //图片的高度
        int imageHeight = (int) (imageWith * 0.564f);
        //设置点击的view
        List<View> clickList = new ArrayList<>();
        clickList.add(mAdHolder.layout);
        //创建广告绑定相关View
        SimpleDaTuViewModel simpleDaTuViewModel = new SimpleDaTuViewModel(imageWith, clickList, mAdHolder.cover);
        simpleDaTuViewModel.height = imageHeight;
        //设置通用标题
        simpleDaTuViewModel.descView = mAdHolder.title;
        //设置穿山甲相关
        simpleDaTuViewModel.adContentRootView = mAdHolder.convertView;
        simpleDaTuViewModel.adTagView = mAdHolder.adTag;
        simpleDaTuViewModel.csjVideoLayout = mAdHolder.csjAdVideoLayout;
        //设置广点通相关
        simpleDaTuViewModel.gdtNativeAdContainer = mAdHolder.nativeAdContainer;
        simpleDaTuViewModel.gdtVideoContainer = mAdHolder.gdtAdVideoLayout;

        //绑定广告相关事件，埋点等
        boolean isBindSuccess = mAdProvider.bindViewDatas(thirdAd, simpleDaTuViewModel, positionName);
        //绑定失败
        if (!isBindSuccess) {
            FuliLogger.log("插屏广告:bindAdFailed");
            mAdHolder.convertView.setVisibility(View.GONE);
            mAdContainer.setVisibility(View.GONE);
            if (mAdCallBack != null) {
                mAdCallBack.onSdkADError();
            }
            return false;
        } else {
            FuliLogger.log("插屏广告:adShow");
            if (mAdCallBack != null) {
                mAdCallBack.onSdkShow();
            }
        }
        //展示总广告item
        mAdHolder.convertView.setVisibility(View.VISIBLE);
        // 因为图片渲染慢 标题先出来 所以延时处理下
        mAdHolder.convertView.postDelayed(mRunnable, 300);

        mAdHolder.IvClose.setOnClickListener(view -> hideAd());

        return true;
    }

    private final Runnable mRunnable = new Runnable() {
        @Override
        public void run() {
            if (mAdHolder != null && mAdHolder.convertView != null
                    && ContextUtils.checkContext(mAdHolder.convertView.getContext())) {
                //广告绑定成功
                mAdHolder.layout.setBackground(null);
                //展示自渲染布局
                mAdHolder.nativeAdContainer.setVisibility(View.VISIBLE);
                //展示容器
                mAdContainer.setVisibility(View.VISIBLE);
            }
        }
    };

    public void hideAd() {
        FuliLogger.log("插屏广告:hideAd");
        if (mAdContainer == null) {
            return;
        }
        if (mAdHolder != null && mAdHolder.convertView != null) {
            mAdHolder.convertView.removeCallbacks(mRunnable);
        }
        if (mAdContainer.getChildCount() > 0) {
            mAdContainer.removeAllViews();
        }
        mAdContainer.setVisibility(View.GONE);

        if (mAdCallBack != null) {
            mAdCallBack.onSdkClose();
        }
    }

    public boolean onBackPressed() {
        if (mAdContainer != null && mAdContainer.getVisibility() == View.VISIBLE
                && mAdHolder != null && mAdHolder.convertView.getVisibility() == View.VISIBLE) {
            hideAd();
            return true;
        }
        return false;
    }

    public static class Holder extends HolderAdapter.BaseViewHolder {
        //广点通需要包装一层
        NativeAdContainer nativeAdContainer;
        GdtMediaViewContainer gdtAdVideoLayout;

        ImageView cover;
        TextView title;
        ViewGroup layout;
        CardView csjAdVideoLayout;
        ViewGroup convertView;
        ImageView adTag;
        ImageView IvClose;

        private Holder(FrameLayout adLayout) {
            this.convertView = (ViewGroup) LayoutInflater.from(adLayout.getContext()).inflate(R.layout.main_home_custom_insert_screen_ad_layout, adLayout, false);
            nativeAdContainer = convertView.findViewById(R.id.main_ad_native_container);
            gdtAdVideoLayout = convertView.findViewById(R.id.main_gdt_ad_video);
            layout = convertView.findViewById(R.id.main_ad_layout_content);
            cover = convertView.findViewById(R.id.main_iv_track_cover);
            title = convertView.findViewById(R.id.main_tv_title);
            csjAdVideoLayout = convertView.findViewById(R.id.main_ad_video);
            adTag = convertView.findViewById(R.id.main_iv_ad_tag);
            IvClose = convertView.findViewById(R.id.main_ad_iv_close);
        }
    }
}
