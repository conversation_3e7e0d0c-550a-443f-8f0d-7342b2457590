package com.ximalaya.ting.lite.main.model.album;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.lite.main.album.manage.CategoryRefreshDataPool;

/**
 * <AUTHOR> on 2018/3/16.
 */

public class CategoryRecommendRefresh {
    @NonNull
    private Context mContext;

    public MainAlbumMList albumMList;

    @Nullable
    public CategoryRefreshDataPool dataPool;

    public boolean isRefreshing;

    public CategoryRecommendRefresh(@NonNull Context context, @NonNull MainAlbumMList albumMList){
        mContext = context;

        this.albumMList = albumMList;

        dataPool = new CategoryRefreshDataPool(context, albumMList);
    }
}
