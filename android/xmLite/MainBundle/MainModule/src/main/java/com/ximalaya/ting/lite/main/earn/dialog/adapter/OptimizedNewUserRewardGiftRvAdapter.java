package com.ximalaya.ting.lite.main.earn.dialog.adapter;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter;
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder;
import com.ximalaya.ting.android.host.model.user.NewUserRewardGiftList;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.earn.dialog.OptimizedLoginNewUserRewardDialogFragment;

import java.util.List;

/**
 * <AUTHOR>
 */
public class OptimizedNewUserRewardGiftRvAdapter extends MultiRecyclerAdapter<NewUserRewardGiftList.NewUserRewardGift, SuperRecyclerHolder> {
    public Activity mActivity;
    private OptimizedLoginNewUserRewardDialogFragment.NewUserJumpRewardCallBack newUserOpenRewardCallBack;

    public OptimizedNewUserRewardGiftRvAdapter(Activity mCtx, List<NewUserRewardGiftList.NewUserRewardGift> mValueList, OptimizedLoginNewUserRewardDialogFragment.NewUserJumpRewardCallBack newUserOpenRewardCallBack) {
        super(mCtx, mValueList);
        this.mActivity = mCtx;
        this.newUserOpenRewardCallBack = newUserOpenRewardCallBack;
    }

    @Override
    public SuperRecyclerHolder createMultiViewHolder(Context mCtx, @NonNull View itemView, int viewType) {
        return SuperRecyclerHolder.createViewHolder(mCtx, itemView);
    }

    @Override
    public void onBindMultiViewHolder(SuperRecyclerHolder holder, NewUserRewardGiftList.NewUserRewardGift model, int viewType, int position) {
        if (model == null) {
            return;
        }
        TextView tvRewardTitle = (TextView) holder.getViewById(R.id.tv_reward_title);
        ImageView tvRewardItemIcon = (ImageView) holder.getViewById(R.id.iv_reward_item_icon);
        TextView tvRewardItemContent = (TextView) holder.getViewById(R.id.tv_reward_item_content);
        TextView tvRewardBtn = (TextView) holder.getViewById(R.id.tv_reward_btn);
        ImageManager.from(mActivity).displayImage(tvRewardItemIcon, model.getCover(), R.drawable.host_default_album_145);
        if (!TextUtils.isEmpty(model.getTitle())) {
            tvRewardTitle.setText(model.getTitle());
        }
        if (!TextUtils.isEmpty(model.getName())) {
            tvRewardItemContent.setText(model.getName());
        }
        if (!TextUtils.isEmpty(model.getJumpText())) {
            tvRewardBtn.setText(model.getJumpText());
        }
        if (model.getStatus() != 1) {
            tvRewardBtn.setBackgroundResource(R.drawable.main_bg_gradient_ff754b_ff2500_radius_24);
        } else {
            tvRewardBtn.setBackgroundResource(R.drawable.main_bg_gradient_aaaaaa_6a6a6a_radius_24);
        }
        tvRewardBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (model.getStatus() == 1) return;
                if (newUserOpenRewardCallBack != null) {
                    newUserOpenRewardCallBack.jumpRewardBack(model.getJumpText(),model.getJumpUrl());
                }
            }
        });
    }


    @Override
    public int getMultiItemViewType(NewUserRewardGiftList.NewUserRewardGift model, int position) {
        return 0;
    }

    @Override
    public int getMultiItemLayoutId(int viewType) {
        return R.layout.main_fra_dialog_optimized_new_user_reward_item;
    }
}
