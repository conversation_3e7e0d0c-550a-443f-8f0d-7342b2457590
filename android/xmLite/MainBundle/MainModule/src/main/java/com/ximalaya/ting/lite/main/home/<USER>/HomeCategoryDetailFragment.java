package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.Gravity;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.RequestParamsUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.utils.MainSearchUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class HomeCategoryDetailFragment extends BaseFragment2 implements IRefreshLoadMoreListener, AdapterView.OnItemClickListener {
    private static final String KEY_TAB_KEYWORD_ID = "key_tab_keyword_id";
    private static final String KEY_TAB_KEYWORD_NAME = "key_tab_keyword_name";
    private static final String KEY_NEED_TITLE_BAR = "key_need_title_bar";

    private RefreshLoadMoreListView mListView;
    private int mPageId = 1;
    private boolean mIsLoading = false;
    private AlbumAdapter mAdapter;
    private String mCalDimension = "hot";
    private ImageView vNocontent;
    private int mTabKeywordId = 0;
    private RelativeLayout mRlTitleBar; //状态栏
    private String mTabKeyWordName = "";
    private boolean mNeedTitleBar = false; //是否需要标题栏，默认是不需要，默认在首页tab使用
    //分类id
    private int mCategoryId = -1;

    private int from = -1;

    /**
     * 首页tab使用，所需要的argument
     * <p>
     * 设置状态栏为false
     */
    public static Bundle createArgumentFromHomeTab(int categoryId, int keywordId, String keywordName) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstants.KEY_CATEGORY_ID, categoryId);
        bundle.putInt(KEY_TAB_KEYWORD_ID, keywordId);
        bundle.putString(KEY_TAB_KEYWORD_NAME, keywordName);
        bundle.putBoolean(KEY_NEED_TITLE_BAR, false);
        return bundle;
    }

    /**
     * 作为单一页面使用，所需要的参数
     * <p>
     * 设置状态栏为true
     */
    public static Bundle createArgumentFromSinglePage(int categoryId, int keywordId, String keywordName, int from) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstants.KEY_CATEGORY_ID, categoryId);
        bundle.putInt(KEY_TAB_KEYWORD_ID, keywordId);
        bundle.putString(KEY_TAB_KEYWORD_NAME, keywordName);
        bundle.putBoolean(KEY_NEED_TITLE_BAR, true);
        bundle.putInt(BundleKeyConstantsInMain.KEY_FROM, from);
        return bundle;
    }

    @Override
    public void onRefresh() {
        refresh();
        MainSearchUtils.updateSearchHintWithParentCategoryId(HomeCategoryDetailFragment.this);
    }

    private void refresh() {
        mPageId = 1;
        if (mListView != null) {
            mListView.setFooterViewVisible(View.VISIBLE);
        }
        loadData();
    }

    @Override
    public void onMore() {
        loadData();
    }

    @Override
    public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int index = position - mListView.getRefreshableView().getHeaderViewsCount();
        List<Album> listData = mAdapter.getListData();
        if (listData == null) {
            return;
        }
        if (index < 0 || index >= listData.size()) {
            return;
        }
        Album album = listData.get(index);
        if (!(album instanceof AlbumM)) {
            return;
        }
        AlbumM albumM = (AlbumM) album;
        AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mCategoryId = arguments.getInt(BundleKeyConstants.KEY_CATEGORY_ID, -1);
            mTabKeywordId = arguments.getInt(KEY_TAB_KEYWORD_ID, 0);
            mTabKeyWordName = arguments.getString(KEY_TAB_KEYWORD_NAME, "");
            mNeedTitleBar = arguments.getBoolean(KEY_NEED_TITLE_BAR, false);
            from = arguments.getInt(BundleKeyConstantsInMain.KEY_FROM, -1);
        }
        //需要标题栏，设置为可滑动返回
        setCanSlided(mNeedTitleBar);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        //状态栏
        mRlTitleBar = findViewById(R.id.main_title_bar);

        mListView = (RefreshLoadMoreListView) findViewById(R.id.main_listview);
        mAdapter = new AlbumAdapter(mActivity, new ArrayList<Album>());
        initFooterView();

        mListView.setAdapter(mAdapter);
        mListView.setOnRefreshLoadMoreListener(this);
        mListView.setOnItemClickListener(this);
        mListView.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {

            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (getiGotoTop() != null) {
                    getiGotoTop().setState(firstVisibleItem > 12);
                }
            }
        });

        //设置状态栏展示
        if (mNeedTitleBar) {
            mRlTitleBar.setVisibility(View.VISIBLE);
            setTitle(mTabKeyWordName);
        }
    }


    private final IGotoTop.IGotoTopBtnClickListener mTopBtnListener = new IGotoTop.IGotoTopBtnClickListener() {
        @Override
        public void onClick(View v) {
            if (!isRealVisable()) {
                return;
            }
            if (mListView == null) {
                return;
            }
            mListView.getRefreshableView().setSelection(0);
        }
    };

    private void initFooterView() {
        LinearLayout ll = new LinearLayout(getActivity());
        ll.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        ll.setGravity(Gravity.CENTER);
        vNocontent = new ImageView(getActivity());
        vNocontent.setPadding(0, BaseUtil.dp2px(mContext, 30), 0, 0);
        vNocontent.setImageResource(R.drawable.main_bg_meta_nocontent);
        ll.addView(vNocontent);
        vNocontent.setVisibility(View.GONE);
        mListView.getRefreshableView().addFooterView(ll);
    }

    @Override
    protected void loadData() {
        if (mIsLoading) {
            return;
        }
        if (canUpdateUi() && mAdapter != null && mAdapter.getCount() == 0) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        mIsLoading = true;

        loadCommonDatas();
    }

    private void loadCommonDatas() {
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_CALC_DIMENSION, mCalDimension);
        params.put(HttpParamsConstants.PARAM_PAGE_ID, mPageId + "");
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        params.put(HttpParamsConstants.PARAM_CATEGORY_ID, mCategoryId + "");
        if (mTabKeywordId > 0) {
            params.put("keywordId", mTabKeywordId + "");
        }
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        params.put(HttpParamsConstants.PARAM_VERSION, DeviceUtil.getVersion(mContext));
        params.put("scale", "1");
        params.put("network", CommonRequestM.getInstanse().getNetWorkType());
        params.put("operator", NetworkType.getOperator(mContext) + "");
        params.put("deviceId", DeviceUtil.getDeviceToken(mContext));
        params.put("appid", "0");
        if (UserInfoMannage.hasLogined()) {
            params.put("uid", UserInfoMannage.getUid() + "");
        }
        if (from == BundleValueConstantsInMain.FROM_VIP_PAGE) {
            params.put("vipPage", "1");
        }
        RequestParamsUtil.addVipShowParam(params);
        params = RequestParamsUtil.addVipShowParam(params);
        IDataCallBack<MainAlbumMList> callBack = new IDataCallBack<MainAlbumMList>() {
            @Override
            public void onSuccess(final MainAlbumMList object) {
                mIsLoading = false;
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        boolean isNoContent = object == null || object.getList() == null || object.getList().size() <= 0;
                        if (isNoContent) {
                            mListView.setHasMoreNoFooterView(false);
                            if (mPageId == 1) {
                                onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                            }
                        } else {
                            onLoadSuccess(object);
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                onLoadFailure(message);
            }
        };
        LiteCommonRequest.getMainCategoryAlbums(params, callBack);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_category_detail;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    /**
     * 数据加载成功
     */
    private void onLoadSuccess(final MainAlbumMList data) {
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (mPageId == 1) {
                    mAdapter.clear();
                }
                onPageLoadingCompleted(LoadCompleteType.OK);
                if (data.getList() != null) {
                    List<Album> listData = mAdapter.getListData();
                    if (mPageId == 1 && listData != null) {
                        listData.addAll(data.getList());
                    } else if (listData != null) {
                        listData.addAll(data.getList());
                        if (listData.size() <= 0) {
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                            mListView.setHasMoreNoFooterView(false);
                            return;
                        }
                    }
                } else {
                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                    mListView.setHasMoreNoFooterView(false);
                }
                if (data.getMaxPageId() > mPageId) {
                    mPageId++;
                    mListView.onRefreshComplete(true);
                    return;
                }
                mListView.onRefreshComplete(false);
            }

        });
    }

    /**
     * 数据加载失败
     */
    private void onLoadFailure(String message) {
        mIsLoading = false;
        if (!canUpdateUi()) {
            return;
        }
        if (mPageId == 1) {
            mAdapter.clear();
            mListView.onRefreshComplete(true);
            mListView.setHasMoreNoFooterView(false);
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        } else {
            CustomToast.showFailToast(message);
            mListView.onRefreshComplete(true);
        }
    }

    @Override
    protected void loadDataError() {
        if (mListView != null) {
            mListView.setMode(Mode.DISABLED);
            mListView.setHasMoreNoFooterView(false);
        }
    }

    @Override
    protected void loadDataOk() {
        if (mListView != null) {
            mListView.setMode(Mode.PULL_FROM_START);
        }
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }


    @Override
    protected String getPageLogicName() {
        return mTabKeyWordName;
    }

    @Override
    public void onMyResume() {
        if (!mNeedTitleBar) {
            //设置super.onMyResume()中不做状态栏改变操作,在首页tab内使用
            setFilterStatusBarSet(true);
        }
        super.onMyResume();
    }

    @Override
    public void onResume() {
        super.onResume();

        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(mTopBtnListener);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
    }
}
