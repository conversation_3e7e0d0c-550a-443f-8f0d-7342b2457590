package com.ximalaya.ting.lite.main.album.fragment

import android.graphics.Paint
import android.os.Bundle
import android.text.TextPaint
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.server.NetworkUtils
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.album.adapter.LiteHotCommentAdapterOne
import com.ximalaya.ting.lite.main.album.adapter.LiteRecommendAlbumAdapter
import com.ximalaya.ting.lite.main.album.dialog.LiteFullIntroDialog.Companion.newInstance
import com.ximalaya.ting.lite.main.album.dialog.LiteHotCommentListDialog
import com.ximalaya.ting.lite.main.model.album.AlbumExtraModel
import com.ximalaya.ting.lite.main.model.album.LiteHotComment
import com.ximalaya.ting.lite.main.request.LiteAlbumPageRequest
import com.ximalaya.ting.lite.main.view.LinearItemDecoration
import com.ximalaya.ting.lite.main.view.RecyclerViewCanDisallowInterceptTwo
import com.ximalaya.ting.lite.main.view.text.StaticLayoutManager
import com.ximalaya.ting.lite.main.view.text.StaticLayoutView

/**
 * Created by dumingwei on 2021/5/25
 *
 *
 * Desc: 专辑简介界面
 */
class LiteAlbumIntroFragment : BaseFragment2(), View.OnClickListener {

    companion object {
        private const val TAG = "LiteAlbumIntroductionFr"
    }

    private lateinit var slvAlbumIntro: StaticLayoutView
    private lateinit var rlHotCommend: RelativeLayout
    private lateinit var tvCommentMore: TextView
    private lateinit var rvHotComment: RecyclerViewCanDisallowInterceptTwo
    private var hotCommentAdapter: LiteHotCommentAdapterOne? = null


    private lateinit var rlRecommendAlbum: RelativeLayout
    private lateinit var tvRecommendMore: TextView
    private lateinit var rvRecommendAlbum: RecyclerViewCanDisallowInterceptTwo

    private var mAlbumM: AlbumM? = null

    private var isFirstIn = true

    private var isLoading = false

    override fun getContainerLayoutId(): Int {
        return R.layout.main_lite_fra_album_introduction
    }

    override fun initUi(savedInstanceState: Bundle?) {
        mAlbumM = arguments?.getParcelable(BundleKeyConstants.KEY_ALBUM)
        //显示专辑简介的特殊控件，限制最多三行
        slvAlbumIntro = findViewById(R.id.main_slv_album_intro)

        rlHotCommend = findViewById(R.id.main_rl_hot_comment)
        tvCommentMore = findViewById(R.id.main_tv_comment_more)
        tvCommentMore.setOnClickListener(this)
        AutoTraceHelper.bindData(tvCommentMore, AutoTraceHelper.MODULE_DEFAULT, "")

        rvHotComment = findViewById(R.id.main_rv_hot_comment)
        //解决手指从左向右滑动，会退出当前界面的问题
        rvHotComment.setDisallowInterceptTouchEventView(view as ViewGroup)

        rlRecommendAlbum = findViewById(R.id.main_rl_recommend_album)
        tvRecommendMore = findViewById(R.id.main_tv_recommend_more)
        tvRecommendMore.setOnClickListener(this)
        AutoTraceHelper.bindData(tvCommentMore, AutoTraceHelper.MODULE_DEFAULT, "")

        rvRecommendAlbum = findViewById(R.id.main_rv_recommend_album)
        rvRecommendAlbum.setDisallowInterceptTouchEventView(view as ViewGroup)

        val albumIntro = mAlbumM?.intro ?: "感谢您收听，喜欢记得订阅哦，第一时间获取最新节目动态！"

        Logger.i(TAG, "albumIntro = $albumIntro")
        val textPaint = TextPaint(Paint.ANTI_ALIAS_FLAG)
        textPaint.textSize = BaseUtil.sp2px(mContext, 15f).toFloat()
        textPaint.color = ContextCompat.getColor(mContext, R.color.main_color_666666)
        val width = BaseUtil.getScreenWidth(mContext) - BaseUtil.dp2px(mContext, 24f)

        var maxLines = 3

        val screenHeightDp = BaseUtil.getScreenHeight(mContext) / BaseUtil.getScreenDensity(mContext)
        //当屏幕高度>667时，简介展示4行简介
        if (screenHeightDp > 667) {
            maxLines = 4
        }

        val layout = StaticLayoutManager.getInstance().getLimitLayout(albumIntro, maxLines, width, textPaint, 1.5f) {
            mAlbumM?.let {
                //添加点击埋点
                XMTraceApi.Trace()
                        .click(32271)
                        .createTrace()
                val completeIntroductionDialog = newInstance(it)
                completeIntroductionDialog.show(childFragmentManager, "")
            }
        }
        slvAlbumIntro.setLayout(layout)
        slvAlbumIntro.invalidate()

        if (mAlbumM == null) {
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT)
        }
    }

    override fun loadData() {
        if (isFirstIn) {
            isFirstIn = false
            onPageLoadingCompleted(LoadCompleteType.LOADING)
        }
        if (isLoading) {
            return
        }
        isLoading = true

        val params = hashMapOf<String, String>()
        params["albumId"] = mAlbumM?.id.toString()
        LiteAlbumPageRequest.getAlbumExtra(params, object : IDataCallBack<AlbumExtraModel?> {
            override fun onSuccess(extraModel: AlbumExtraModel?) {
                if (!canUpdateUi()) {
                    return
                }
                isLoading = false
                if (extraModel == null) {
                    onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR)
                    return
                }
                //1.下架专辑；2. 热评和推荐专辑都没有；显示空界面
                if (mAlbumM?.isOfflineHidden == true || (extraModel.hotComments.isNullOrEmpty() && extraModel.recAlbums.isNullOrEmpty())) {
                    rlHotCommend.visibility = View.GONE
                    rlRecommendAlbum.visibility = View.GONE
                    onPageLoadingCompleted(LoadCompleteType.OK)
                    return
                }
                onPageLoadingCompleted(LoadCompleteType.OK)

                setHotCommendAdapter(extraModel)

                if (ChildProtectManager.isChildProtectOpen(mContext)) {
                    rlRecommendAlbum.visibility = View.GONE
                } else {
                    setRecAlbumAdapter(extraModel)
                }
            }

            override fun onError(code: Int, message: String?) {
                isLoading = false
                if (canUpdateUi()) {
                    hideSomeViews()
                    onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR)
                }
            }
        })
    }

    /**
     * 设置评论适配器
     */
    private fun setHotCommendAdapter(extraModel: AlbumExtraModel) {
        //纯付费节目不展示评论模块
        if (mAlbumM != null && isPaidAlbum(mAlbumM)) {
            rlHotCommend.visibility = View.GONE
            return
        }
        val hotComments = extraModel.hotComments
        if (hotComments.isNullOrEmpty()) {
            rlHotCommend.visibility = View.GONE
            return
        }
        rlHotCommend.visibility = View.VISIBLE

        rvHotComment.layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)

        val spacing = BaseUtil.dp2px(context, 8f)
        val margin = BaseUtil.dp2px(context, 12f)
        rvHotComment.addItemDecoration(LinearItemDecoration(spacing, margin))

        hotCommentAdapter = LiteHotCommentAdapterOne(this, hotComments)
        hotCommentAdapter?.onLikeAction = { position, comment ->
            XMTraceApi.Trace()
                .setMetaId(32286)
                .setServiceId("click")
                .createTrace()

            if (UserInfoMannage.hasLogined()) {
                val currentLiked = comment.liked ?: false

                comment.liked = !currentLiked

                if (currentLiked) {
                    //取消点赞
                    val likeCount = (comment.likeCount ?: 0) - 1
                    comment.likeCount = if (likeCount < 0) 0 else likeCount
                    unLikeComment(position, comment)
                } else {
                    //点赞
                    val likeCount = (comment.likeCount ?: 0) + 1
                    comment.likeCount = likeCount
                    likeComment(position, comment)
                }
                hotCommentAdapter?.notifyItemChanged(position)
            } else {
                UserInfoMannage.gotoLogin(mContext)
            }
        }
        hotCommentAdapter?.onItemClickAction = {
            seeMoreComment()
        }
        rvHotComment.adapter = hotCommentAdapter
    }

    /**
     * 判断是否为精品（付费）专辑
     * 如果同是vip专辑，则优先认为是vip类型
     */
    private fun isPaidAlbum(albumM: AlbumM?): Boolean {
        return if (albumM != null) {
            if (albumM.vipFreeType == 1 || albumM.isVipFree) {
                false
            } else albumM.isPaid
        } else false
    }

    /**
     * 点赞
     */
    private fun likeComment(position: Int, comment: LiteHotComment) {
        LiteAlbumPageRequest.likeAlbumComment(comment.albumId ?: -1,
            comment.commentId ?: -1, comment.uid ?: -1, object : IDataCallBack<Boolean> {
            override fun onSuccess(success: Boolean?) {
                if (success == true) {
                    //不需要任何操作，因为在请求接口前已经将状态置为已点赞了。
                } else {
                    //点赞失败，将状态重新置为未点赞状态
                    likeFailed(comment, position)
                }
            }

            override fun onError(code: Int, message: String?) {
                Logger.i(TAG, "likeComment onError code $code")
                likeFailed(comment, position)
            }
        })

    }

    private fun likeFailed(comment: LiteHotComment, position: Int) {
        comment.liked = false
        comment.likeCount = (comment.likeCount ?: 1) - 1
        hotCommentAdapter?.notifyItemChanged(position)
    }

    /**
     * 取消点赞
     */
    private fun unLikeComment(position: Int, comment: LiteHotComment) {
        LiteAlbumPageRequest.unlikeAlbumComment(comment.albumId ?: -1, comment.commentId
                                                                       ?: -1, comment.uid
                                                                              ?: -1, object : IDataCallBack<Boolean> {
            override fun onSuccess(success: Boolean?) {
                if (success == true) {
                    //不需要任何操作，因为在请求接口前已经将状态置为已点赞了。
                } else {
                    //取消点赞失败，将状态重新置为已点赞状态
                    unlikeFailed(comment, position)
                }
            }

            override fun onError(code: Int, message: String?) {
                Logger.i(TAG, "unLikeComment onError code $code")
                unlikeFailed(comment, position)
            }
        })
    }

    private fun unlikeFailed(comment: LiteHotComment, position: Int) {
        comment.liked = true
        comment.likeCount = (comment.likeCount ?: 0) + 1
        hotCommentAdapter?.notifyItemChanged(position)
    }

    /**
     * 设置推荐专辑适配器
     */
    private fun setRecAlbumAdapter(extraModel: AlbumExtraModel) {
        val recAlbums = extraModel.recAlbums
        if (recAlbums.isNullOrEmpty()) {
            rlRecommendAlbum.visibility = View.GONE
            return
        }
        rlRecommendAlbum.visibility = View.VISIBLE
        rvRecommendAlbum.layoutManager = GridLayoutManager(mContext, 3,
            LinearLayoutManager.HORIZONTAL, false)
        val adapter = LiteRecommendAlbumAdapter(mAlbumM?.uid ?: -1, this, recAlbums)
        rvRecommendAlbum.adapter = adapter
    }

    private fun hideSomeViews() {
        rlHotCommend.visibility = View.GONE
        rlRecommendAlbum.visibility = View.GONE
    }

    override fun onClick(v: View) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }
        when (v.id) {
            R.id.main_tv_comment_more -> {
                XMTraceApi.Trace()
                    .setMetaId(32287)
                    .setServiceId("click")
                    .createTrace()

                seeMoreComment()
            }
            R.id.main_tv_recommend_more -> {
                mAlbumM?.let {
                    XMTraceApi.Trace()
                        .setMetaId(32290)
                        .setServiceId("click")
                        .createTrace()

                    val argumentFromDef = AlbumRecListFragment.createArgumentUseAlbumIdFromDef(it.id,
                        AlbumRecListFragment.FROM_PLAY_PAGE_RECOMMEND)
                    //添加专辑主播uid
                    AlbumRecListFragment.addArgumentAlbumAnchorUid(argumentFromDef, it.uid)
                    val albumRecListFragment = AlbumRecListFragment()
                    albumRecListFragment.arguments = argumentFromDef
                    startFragment(albumRecListFragment)
                }
            }
        }
    }

    fun seeMoreComment() {
        if (!canUpdateUi()) {
            return
        }
        if (!NetworkUtils.isNetworkAvaliable(BaseApplication.getMyApplicationContext())) {
            CustomToast.showToast("当前网络不可用，请检查你的网络设置")
            return
        }

        XMTraceApi.Trace()
            .setMetaId(32272)
            .setServiceId("click")
            .createTrace()

        val commentListDialog = LiteHotCommentListDialog(mAlbumM?.id ?: -1)
        commentListDialog.show(childFragmentManager, "")
    }

    override fun getPageLogicName(): String {
        return "LiteAlbumIntroFragment"
    }

}