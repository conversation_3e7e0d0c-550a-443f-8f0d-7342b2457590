package com.ximalaya.ting.lite.main.album.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.AlbumTagUtil
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter
import java.lang.String


/**
 * Created by dumingwei on 2021/5/25
 *
 * Desc: 专辑简介界面的推荐专辑适配器
 */
class LiteRecommendAlbumAdapter(
    private val uid: Long,
    private val fragment: BaseFragment2,
    private val mAlbumMList: List<AlbumM>
) : AbRecyclerViewAdapter<LiteRecommendAlbumAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = inflater.inflate(R.layout.main_item_recommend_album, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount() = mAlbumMList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        onBindAlbumRankViewHolder(holder, position)
    }

    @SuppressLint("SetTextI18n")
    private fun onBindAlbumRankViewHolder(holder: ViewHolder, position: Int) {
        val album: AlbumM = mAlbumMList[position]

        if (album == null) {
            return
        }

        FuliLogger.log("排行榜模块:onBindViewHolder=start=${position}==111")
        //设置专辑标题
        holder.tvAlbumTitle.text = album.albumTitle

        FuliLogger.log("排行榜模块:onBindViewHolder=start=${position}==222")

        //设置专辑图
        ImageManager.from(fragment.context).displayImage(holder.ivAlbumCover, album.largeCover, R.drawable.host_default_album, R.drawable.host_default_album)

        FuliLogger.log("排行榜模块:onBindViewHolder=start=${position}==3333")

        //展示专辑tag
        if (AlbumTagUtil.getAlbumCoverTag(album) != -1) {
            val albumCoverTagDrawable = AlbumTagUtil.getAlbumCoverTagDrawable(album, fragment.context, AlbumTagUtil.ZOOM_IN_RATIO_78_percent)
            holder.ivAlbumCoverTag.setImageDrawable(albumCoverTagDrawable)
            holder.ivAlbumCoverTag.visibility = View.VISIBLE
        } else {
            holder.ivAlbumCoverTag.visibility = View.INVISIBLE
        }

        //设置专辑评分
        val playCountStr = StringUtil.getFriendlyNumStr(album.playCount) + "播放"
        holder.tvPlayCount.text = playCountStr

        //是否展示相同主播的标
        if (uid == album.uid) {
            holder.viewAlbumTagSplitLine.visibility = View.VISIBLE
            holder.tvAnchor.visibility = View.VISIBLE
        } else {
            holder.viewAlbumTagSplitLine.visibility = View.GONE
            holder.tvAnchor.visibility = View.GONE
        }
        //设置item点击
        holder.itemView.setOnClickListener { handleItemClick(album) }
    }

    private fun handleItemClick(album: AlbumM) {
        XMTraceApi.Trace()
            .setMetaId(32273)
            .setServiceId("click")
            .createTrace()
        AlbumEventManage.startMatchAlbumFragment(album, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, album.recSrc, album.recTrack, -1, fragment.activity)
    }

    override fun getItem(position: Int): Any? {
        val albumSize = mAlbumMList.size
        if (CollectionUtil.isNotEmpty(mAlbumMList) && position >= 0 && position < albumSize) {
            return mAlbumMList[position]
        }
        return null
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        //专辑图
        val ivAlbumCover: ImageView = itemView.findViewById(R.id.main_iv_album_cover)

        //专辑标签
        val ivAlbumCoverTag: ImageView = itemView.findViewById(R.id.main_iv_album_cover_tag)

        //专辑播放量
        val tvPlayCount: TextView = itemView.findViewById(R.id.main_tv_play_count)

        //专辑标题
        val tvAlbumTitle: TextView = itemView.findViewById(R.id.main_tv_album_title)

        val viewAlbumTagSplitLine: View = itemView.findViewById(R.id.main_view_album_tag_split_line)

        //是否是相同主播
        val tvAnchor: TextView = itemView.findViewById(R.id.main_tv_anchor)
    }

}