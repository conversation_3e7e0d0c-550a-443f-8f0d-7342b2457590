package com.ximalaya.ting.lite.main.model.newhome.view

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.model.category.CategoryMetadata
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.constant.DPConstants
import com.ximalaya.ting.lite.main.model.newhome.LiteChannelModel
import com.ximalaya.ting.lite.main.model.newhome.LiteFilterPanelStatusModel
import com.ximalaya.ting.lite.main.vip.listener.FilterPanelSyncListener
import java.util.*

/**
 * Created by dumingwei on 2021/3/26.
 *
 * Desc:包裹LiteChooseMetadataView
 */
class LiteChooseMetaDataViewWrapper @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr), View.OnClickListener {


    companion object {

        const val ALL = "全部"

        const val CAL_DIMEN_RECENT = "recent" //最近更新

        const val CAL_DIMEN_HOT = "hot" //综合排序

        const val CAL_DIMEN_CLASSIC = "classic" //播放最多

    }

    var pageId: Int = 0
    private var dp2 = 0
    private var dp10 = 0

    private val TAG: String = "LiteChooseMetaDataViewW"

    private var llFirstLevelContainer: LinearLayout

    //所有的频道的子View
    private var firstLevelChannelChildren: MutableList<TextView> = arrayListOf()

    //中间的筛选面板
    private var innerChooseView: LiteChooseMetadataView

    //综合排序
    private var tvHot: TextView

    //播放最多
    private var tvClassic: TextView

    //最近播放
    private var tvRecent: TextView

    //展开或者收起
    private var tvFold: TextView

    private var mMetadataList: List<CategoryMetadata>? = null

    private var mChannelList: List<LiteChannelModel>? = null

    private var selectedColor: Int = resources.getColor(R.color.main_color_e83f46)
    private var unSelectedColor: Int = resources.getColor(R.color.main_color_999999)


    private var mMetadataRequestParam: String? = null
    private var mDisplayName: String? = null
    private var sortType: String = "hot"

    //内部的筛选面板是否处于展开状态
    private var mExpand: Boolean = false

    //筛选展开的按钮是否可见，选中全部的时候要不可见
    private var mTvFoldVisible: Boolean = false

    var moduleId: Int = 0//买点用

    var filterPanelSyncListener: FilterPanelSyncListener? = null
        set(value) {
            field = value
            innerChooseView.setFilterPanelSyncListener(value)
        }

    init {

        orientation = VERTICAL

        dp2 = DPConstants.getInstance(getContext()).DP_2
        dp10 = DPConstants.getInstance(getContext()).DP_10

        View.inflate(context, R.layout.main_lite_choose_meta_data_view_wrap, this)

        llFirstLevelContainer = findViewById(R.id.main_ll_first_level)

        innerChooseView = findViewById(R.id.main_expand_choose_view)

        tvHot = findViewById(R.id.main_tv_hot)
        setTextViewBold(tvHot, true)
        tvClassic = findViewById(R.id.main_tv_classic)
        tvRecent = findViewById(R.id.main_tv_recent)
        tvFold = findViewById(R.id.main_tv_fold)

        tvHot.setOnClickListener(this)
        tvClassic.setOnClickListener(this)
        tvRecent.setOnClickListener(this)
        tvFold.setOnClickListener(this)

        innerChooseView.setFilterPanelSyncListener(filterPanelSyncListener)
        innerChooseView.addMetadataChangeListener { calDimension, metadataRequestParam, displayName ->
            mMetadataRequestParam = metadataRequestParam
            mDisplayName = displayName
            //syncChannelStatusOnClick()
            syncMetaDataStatusOnClick()

            // 分类页-筛选流-筛选项item  点击事件
            XMTraceApi.Trace()
                .click(29776) // 用户点击时上报
                .put("currPageId", pageId.toString())
                .put("moduleId", moduleId.toString())
                .put("item", displayName)
                .put("currPage", "categoryPageV2")
                .createTrace()
        }
    }


    fun setChannelList(channelList: List<LiteChannelModel>) {
        if (mChannelList == null) {
            mChannelList = channelList
            addChannelView()
        } else {
            val mChannelListSize = mChannelList?.size ?: 0
            //如果长度发生了变化，指向的对象不一样了，认为是数据发生了变化
            if (mChannelListSize != channelList.size || mChannelList != channelList) {
                mChannelList = channelList
                addChannelView()
            }
        }
    }

    private fun addChannelView() {
        if (mChannelList == null) {
            return
        }
        llFirstLevelContainer.removeAllViews()
        firstLevelChannelChildren.clear()

        val childPs = LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        childPs.leftMargin = BaseUtil.dp2px(context, 2f)
        childPs.rightMargin = BaseUtil.dp2px(context, 2f)

        mChannelList?.forEach {
            val textView = getView(it)
            llFirstLevelContainer.addView(textView, childPs)
            firstLevelChannelChildren.add(textView)
        }
    }

    private fun getView(data: LiteChannelModel): TextView {
        val tv = TextView(context)
        tv.text = data.title
        tv.textSize = 14f
        tv.setPadding(dp10, dp2, dp10, dp2)
        if (data.selected) {
            setTextViewBold(tv, true)
            tv.setTextColor(selectedColor)
            tv.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12)
        } else {
            setTextViewBold(tv, false)
            tv.setTextColor(unSelectedColor)
            tv.background = null
        }
        tv.tag = data

        Logger.i(TAG, "tv.tag = data = $data")
        //val bindData: MutableMap<String, Long> = HashMap()
        //bindData["rowID"] = data.id
        AutoTraceHelper.bindData(tv, AutoTraceHelper.MODULE_DEFAULT, data)
        tv.setOnClickListener {
            val isLoading = filterPanelSyncListener?.isLoading() ?: false
            if (!isLoading) {
                val tag = tv.tag
                Logger.i(TAG, "tv.tag = $tag")
                if (tag is LiteChannelModel) {
                    Logger.i(TAG, "tv.tag is LiteChannelModel = $tag")
                    mChannelList?.forEach { liteChannelModel: LiteChannelModel ->
                        liteChannelModel.selected = false
                    }
                    tag.selected = true
                }
                changeChannelSelectedStatus()

                // 分类页-筛选流-筛选项item  点击事件
                XMTraceApi.Trace()
                    .click(29776) // 用户点击时上报
                    .put("currPageId", pageId.toString())
                    .put("moduleId", moduleId.toString())
                    .put("item", tv.text.toString())
                    .put("currPage", "categoryPageV2")
                    .createTrace()
            }
        }
        return tv
    }

    private fun changeChannelSelectedStatus() {
        firstLevelChannelChildren.forEach { tv ->
            val tag = tv.tag
            if (tag is LiteChannelModel) {
                if (tag.selected) {
                    setTextViewBold(tv, true)
                    tv.setTextColor(selectedColor)
                    tv.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12)
                } else {
                    setTextViewBold(tv, false)
                    tv.setTextColor(unSelectedColor)
                    tv.background = null
                }
            }
        }
        if (firstLevelChannelChildren.isNotEmpty()) {
            val tv = firstLevelChannelChildren[0]
            val tag = tv.tag
            if (tag is LiteChannelModel) {
                if (ALL == tag.title && tag.selected) {
                    //选中全部的时候，筛选按钮不可见,将展开状态置为false
                    tvFold.visibility = View.GONE
                    mTvFoldVisible = false
                    mExpand = false
                } else {
                    tvFold.visibility = View.VISIBLE
                    tvFold.text = context.getString(R.string.main_select_expand)
                    mTvFoldVisible = true
                    mExpand = false
                }
            }
        }
        syncChannelStatusOnClick()
    }

    fun setMetadataList(metadataList: List<CategoryMetadata>?) {
        Logger.i(TAG, "setMetadataList")
        if (mMetadataList != null && metadataList != null) {
            var dataChanged = false
            val size = mMetadataList?.size ?: 0
            if (size != metadataList.size) {
                dataChanged = true
            }
            if (dataChanged) {
                realSetMetaDataList(metadataList)
                return
            }
            for (i in mMetadataList!!.indices) {
                val categoryMetadata = metadataList[i]
                val oldCategoryMetadata = mMetadataList!![i]
                if (categoryMetadata.id != oldCategoryMetadata.id || categoryMetadata.isChosed != oldCategoryMetadata.isChosed) {
                    dataChanged = true
                    break
                }
                val metaValueList = categoryMetadata.metadataValues
                val oldMetaValueList = oldCategoryMetadata.metadataValues
                if (metaValueList != null && oldCategoryMetadata == null) {
                    dataChanged = true
                    break
                }
                if (metaValueList == null && oldCategoryMetadata != null) {
                    dataChanged = true
                    break
                }
                if (metaValueList != null && oldMetaValueList != null) {
                    if (metaValueList.size != oldMetaValueList.size) {
                        dataChanged = true
                        break
                    }
                    for (j in metaValueList.indices) {
                        val categoryMetadataValue = metaValueList[j]
                        val oldCategoryMetadataValue = oldMetaValueList[j]
                        if (categoryMetadataValue.id != oldCategoryMetadataValue.id || categoryMetadataValue.isChosed != oldCategoryMetadataValue.isChosed) {
                            dataChanged = true
                            break
                        }
                    }
                    if (dataChanged) {
                        break
                    }
                }
            }
            if (dataChanged) {
                realSetMetaDataList(metadataList)
                return
            }
        } else {
            if (mMetadataList == null && metadataList == null) {
                //都是null，就不用同步
                return
            }
            //其中一个不为null，就要发生改变
            realSetMetaDataList(metadataList)
        }
    }

    private fun realSetMetaDataList(metadataList: List<CategoryMetadata>?) {
        Logger.i(TAG, "realSetMetaDataList 真正改变筛选面板的筛选项")
        mMetadataList = metadataList
        innerChooseView.visibility = View.GONE
        innerChooseView.setMetadataList(metadataList, moduleId)
    }

    fun getMetadataList(): List<CategoryMetadata>? {
        return mMetadataList
    }

    override fun onClick(v: View) {
        val isLoading = filterPanelSyncListener?.isLoading() ?: false
        if (isLoading) {
            return
        }

        if (v.id == R.id.main_tv_hot) {
            tvHot.setTextColor(selectedColor)
            tvHot.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12)
            setTextViewBold(tvHot, true)

            tvClassic.setTextColor(unSelectedColor)
            tvClassic.background = null
            setTextViewBold(tvClassic, false)

            tvRecent.setTextColor(unSelectedColor)
            tvRecent.background = null
            setTextViewBold(tvRecent, false)

            sortType = "hot"
            syncSortTypeOnClick()
            return
        }
        if (v.id == R.id.main_tv_classic) {

            tvHot.setTextColor(unSelectedColor)
            tvHot.background = null
            setTextViewBold(tvHot, false)

            tvClassic.setTextColor(selectedColor)
            tvClassic.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12)
            setTextViewBold(tvClassic, true)

            tvRecent.setTextColor(unSelectedColor)
            tvRecent.background = null
            setTextViewBold(tvRecent, false)

            sortType = "classic"
            syncSortTypeOnClick()
            return
        }
        if (v.id == R.id.main_tv_recent) {
            tvHot.setTextColor(unSelectedColor)
            tvHot.background = null
            setTextViewBold(tvHot, false)

            tvClassic.setTextColor(unSelectedColor)
            tvClassic.background = null
            setTextViewBold(tvClassic, false)


            tvRecent.setTextColor(selectedColor)
            tvRecent.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12)
            setTextViewBold(tvRecent, true)

            sortType = "recent"
            syncSortTypeOnClick()
            return
        }
        if (v.id == R.id.main_tv_fold) {
            if (innerChooseView.visibility == View.GONE) {
                innerChooseView.visibility = View.VISIBLE
                tvFold.text = context.getString(R.string.main_select_fold)
                mExpand = true
            } else {
                innerChooseView.visibility = View.GONE
                tvFold.text = context.getString(R.string.main_select_expand)
                mExpand = false
            }
            //syncFoldStatusOnClick()
            return
        }
    }

    fun setTvFoldVisibility(visible: Boolean) {
        if (visible) {
            tvFold.visibility = View.VISIBLE
        } else {
            tvFold.visibility = View.GONE
        }
    }

    /**
     * 频道发生了改变，调用这个方法
     */
    private fun syncChannelStatusOnClick() {
        if (filterPanelSyncListener == null) {
            return
        }

        val filterPanelStatusModel = getSyncData()

        filterPanelSyncListener?.syncChannelDataOnClick(filterPanelStatusModel)
    }

    private fun getSyncData(): LiteFilterPanelStatusModel {
        val selectedChannel = mChannelList?.firstOrNull {
            it.selected
        }
        var metadataParams: String? = ""
        if (mExpand) {
            metadataParams = mMetadataRequestParam
        }
        val displayName = getDisplayName(selectedChannel)

        return LiteFilterPanelStatusModel(selectedChannel, metadataParams, displayName, mExpand, sortType)
    }


    /**
     * 排序方式发生了变化，调用这个方法
     */
    private fun syncSortTypeOnClick() {
        if (filterPanelSyncListener == null) {
            return
        }

        val filterPanelStatusModel = getSyncData()

        filterPanelSyncListener?.syncMetaDataOrSorTypeOrFoldOnClick(filterPanelStatusModel)
    }

    /**
     * 筛选展开或者收起，调用这个方法
     */
    private fun syncFoldStatusOnClick() {
        if (filterPanelSyncListener == null) {
            return
        }

        val filterPanelStatusModel = getSyncData()

        filterPanelSyncListener?.syncMetaDataOrSorTypeOrFoldOnClick(filterPanelStatusModel)
    }

    /**
     * 频道没有发生变化，筛选项发生了改变，调用这个方法
     */
    private fun syncMetaDataStatusOnClick() {
        if (filterPanelSyncListener == null) {
            return
        }

        val filterPanelStatusModel = getSyncData()

        filterPanelSyncListener?.syncMetaDataOrSorTypeOrFoldOnClick(filterPanelStatusModel)
    }

    private fun getDisplayName(selectedChannel: LiteChannelModel?): String {
        val sb = StringBuilder()

        if (selectedChannel != null && ALL != selectedChannel.title) {
            sb.append(selectedChannel.title)
            sb.append(" · ")
        }
        if (mExpand) {
            val displayName = mDisplayName ?: ""
            if (displayName.isEmpty()) {
                sb.append(getSortTypeName())
            } else {
                sb.append(displayName)
                sb.append(" · ")
                sb.append(getSortTypeName())
            }
        } else {
            sb.append(getSortTypeName())
        }

        return sb.toString()
    }

    private fun getSortTypeName(): String {
        return when (sortType) {
            CAL_DIMEN_HOT -> "综合排序"
            CAL_DIMEN_CLASSIC -> "播放最多"
            CAL_DIMEN_RECENT -> "最近更新"
            else -> "综合排序"
        }
    }

    fun selectDefaultSortType() {
        if (tvHot != null && tvClassic != null && tvRecent != null) {
            tvHot.setTextColor(selectedColor)
            tvHot.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12)
            setTextViewBold(tvHot, true)

            tvClassic.setTextColor(unSelectedColor)
            tvClassic.background = null
            setTextViewBold(tvClassic, false)

            tvRecent.setTextColor(unSelectedColor)
            tvRecent.background = null
            setTextViewBold(tvRecent, false)

            sortType = "hot"
        }
    }

    fun setTextViewBold(textView: TextView?, isBold: Boolean) {
        if (textView != null) {
            if (isBold) {
                val familyName = "sans-serif-light"
                val boldTypeface = Typeface.create(familyName, Typeface.BOLD)
                textView.typeface = boldTypeface
            } else {
                textView.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            }
        }
    }

}