package com.ximalaya.ting.lite.main.home.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.model.album.TanghuluHotWord
import com.ximalaya.ting.lite.main.vip.listener.AdapterDataSyncListener
import kotlinx.android.synthetic.main.main_item_vip_hot_word.view.*

/**
 * Created by duming<PERSON> on 2020/5/7.
 *
 * Desc: 糖葫芦分类热词 cardClass = float
 */

class TanghuluFloatHotWordProvider @JvmOverloads constructor(
        val mFragment: BaseFragment2,
        private val adapterDataSyncListener: AdapterDataSyncListener<TanghuluHotWord>? = null
) : IMulitViewTypeViewAndData<TanghuluFloatHotWordProvider.Holder, ArrayList<TanghuluHotWord>> {

    private val TAG: String? = "VipHotWordProvider"
    private val SPAN_COUNT = 4

    private val mContext: Context? = mFragment.context

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_vip_hot_word, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView)
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<ArrayList<TanghuluHotWord>>, convertView: View, position: Int) {
        val hotWordList = t.getObject()
        Logger.d(TAG, "bindViewDatas" + hotWordList.hashCode())
        val adapter = holder.rootView.rvAdapterVipHotWord.adapter
        if (adapter == null) {
            initAdapter(holder.rootView.rvAdapterVipHotWord, hotWordList)
        } else {
            if (adapter is TanghuluHotWordAdapter) {
                adapter.setValueList(hotWordList)
                adapter.notifyDataSetChanged()
            }
        }
    }

    private fun initAdapter(rv: RecyclerView, list: MutableList<TanghuluHotWord>) {
        if (rv.adapter == null) {
            rv.layoutManager = GridLayoutManager(mContext, SPAN_COUNT)
            mContext?.let {
                val superRecyclerAdapter = TanghuluHotWordAdapter(mContext, list)
                superRecyclerAdapter.syncDataListener = adapterDataSyncListener
                rv.adapter = superRecyclerAdapter
            }
        }
    }

    class Holder(var rootView: View) : BaseViewHolder()

}