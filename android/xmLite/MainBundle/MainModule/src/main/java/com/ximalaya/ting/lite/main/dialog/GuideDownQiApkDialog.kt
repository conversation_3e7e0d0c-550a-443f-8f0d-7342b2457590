package com.ximalaya.ting.lite.main.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmsysteminvoke.XmSystemInvokeManager
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.download.bean.TaskInfo
import com.ximalaya.ting.lite.main.download.inter.DownloadListener
import com.ximalaya.ting.lite.main.manager.DownQjApkManager
import com.ximalaya.ting.lite.main.model.DownQjDialogConfig
import com.ximalaya.ting.lite.main.utils.checkActivity


class GuideDownQiApkDialog(val curPage: String, val mDownQjDialogConfig: DownQjDialogConfig) :
    BaseFullScreenDialogFragment() {

    private var mTvContent: TextView? = null
    private var mTvConfirm: TextView? = null

    var mIClickCallBack: IClickCallBack? = null

    private var oldText = ""

    private var mDownloadListener = object : DownloadListener {
        override fun onTaskStart(task: TaskInfo?) {
            CustomToast.showToast("开始下载")
        }

        override fun onTaskSuccess(task: TaskInfo?) {
            CustomToast.showToast("下载成功,开始安装")
            if (mTvConfirm?.context.checkActivity()) {
                mTvConfirm?.post {
                    mTvConfirm?.text = oldText
                }
            }
        }

        override fun onTaskFailed(task: TaskInfo?) {
            CustomToast.showToast("下载失败,请重试")
            if (mTvConfirm?.context.checkActivity()) {
                mTvConfirm?.post {
                    mTvConfirm?.text = oldText
                }
            }
        }

        override fun onTaskProgress(task: TaskInfo?, progress: Int) {
            if (mTvConfirm?.context.checkActivity()) {
                mTvConfirm?.post {
                    mTvConfirm?.text = "当前进度${progress}%"
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.main_guide_down_qj_layout, container, false)
    }

    override fun onResume() {
        super.onResume()
        initView(view)
        // 导流确认弹窗  弹框展示
        XMTraceApi.Trace()
            .setMetaId(53487)
            .setServiceId("dialogView") // 弹窗展示时上报
            .put("currPage", curPage)
            .createTrace()
    }

    private fun initView(view: View?) {
        view?.findViewById<ImageView?>(R.id.main_iv_close)?.setOnClickListener {
            dismiss()
        }
        mTvContent = view?.findViewById<TextView?>(R.id.main_tv_content)
        mTvConfirm = view?.findViewById<TextView?>(R.id.main_tv_confirm)

        if (!mDownQjDialogConfig.button.isNullOrEmpty()) {
            mTvConfirm?.text = mDownQjDialogConfig.button
        }

        if (!mDownQjDialogConfig.text.isNullOrEmpty()) {
            mTvContent?.text = mDownQjDialogConfig.text
        }

        val qjAppId = "reader.com.xmly.xmlyreader"

        if (!XmSystemInvokeManager.isAppInstalled(context, qjAppId)) {
            oldText = mTvConfirm?.text.toString()
            DownQjApkManager.addDownListener(mDownloadListener)
        }

        mTvConfirm?.setOnClickListener {
            // 导流确认弹窗-btn  弹框控件点击
            XMTraceApi.Trace()
                .setMetaId(53488)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("dialogItem", mTvConfirm!!.text.toString())
                .put("currPage", curPage)
                .createTrace()
            mIClickCallBack?.onClick()
        }
    }

    override fun dismiss() {
        super.dismiss()
        DownQjApkManager.removeDownListener(mDownloadListener)
    }

    interface IClickCallBack {
        fun onClick()
    }
}