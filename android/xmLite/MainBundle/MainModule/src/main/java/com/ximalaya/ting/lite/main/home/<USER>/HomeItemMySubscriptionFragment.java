package com.ximalaya.ting.lite.main.home.fragment;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.home.adapter.SubscriptionFloorItemAdapter;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeItemSubscriptionViewModel;
import com.ximalaya.ting.lite.main.newhome.fragment.LiteHomeRecommendFragment;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by qinhuifeng on 2019-07-16
 * <p>
 * 我听楼层中的--我的订阅页面
 *
 * <AUTHOR>
 */
public class HomeItemMySubscriptionFragment extends BaseFragment2 implements View.OnClickListener {

    //是否来自于听书tab
    public static final String KEY_FROM_TAB = "key_from_tab";

    private RecyclerView mRvList;
    private SubscriptionFloorItemAdapter mAdapter;
    private List<HomeItemSubscriptionViewModel> mDataList = new CopyOnWriteArrayList<>();
    private int mFrom = 0;
    private LinearLayoutManager mLayoutManager;
    private long mLastScollLastTs = 0;

    public HomeItemMySubscriptionFragment() {
        super(false, null);
    }

    public static HomeItemMySubscriptionFragment newInstance(int fromTab) {
        Bundle args = new Bundle();
        args.putInt(KEY_FROM_TAB, fromTab);
        HomeItemMySubscriptionFragment fragment = new HomeItemMySubscriptionFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        return "HomeItemAllMySubscriptionFragment";
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        FuliLogger.log("订阅历史模块==:订阅==onCreate");
        Bundle arguments = getArguments();
        if (arguments != null) {
            mFrom = arguments.getInt(KEY_FROM_TAB, 0);
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        FuliLogger.log("订阅历史模块==:订阅==initUi--start-" + mDataList.size());
        mRvList = findViewById(R.id.main_rv_list);
        mLayoutManager = new LinearLayoutManager(mActivity, LinearLayoutManager.HORIZONTAL, false);
        mRvList.setLayoutManager(mLayoutManager);
        mAdapter = new SubscriptionFloorItemAdapter(this, mActivity, mDataList, mFrom);
        mRvList.setAdapter(mAdapter);

        mRvList.addOnScrollListener(new RecyclerView.OnScrollListener() {

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                if (mLayoutManager == null) {
                    return;
                }
                //推荐内最少5条数据，小于等于5条就不触发刷新了
                int totalItemCount = mLayoutManager.getItemCount();
                if (totalItemCount <= 5) {
                    return;
                }
                int lastVisibleItemPosition = mLayoutManager.findLastVisibleItemPosition();
                if (lastVisibleItemPosition > totalItemCount - 5) {
                    //滑动到了列表最后位置

                    //500秒内只允许回调刷新1次
                    if (System.currentTimeMillis() - mLastScollLastTs <= 500) {
                        return;
                    }
                    mLastScollLastTs = System.currentTimeMillis();

                    if (onScrollListLastListener != null) {
                        onScrollListLastListener.onScrollLastLast();
                    }
                }
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (mLayoutManager == null) {
                    return;
                }
            }
        });
        mAdapter.notifyDataSetChanged();
        FuliLogger.log("订阅历史模块==:订阅==onCreate--end-" + mDataList.size());
    }


    @Override
    protected void loadData() {

    }

    public void updateMySubscriptionFragmentList(List<HomeItemSubscriptionViewModel> list) {
        if (list == null || mDataList == null) {
            FuliLogger.log("订阅历史模块==:updateMySubscriptionFragmentList=mDataList=null");
            return;
        }
        FuliLogger.log("订阅历史模块==:更新数量=updateMySubscriptionFragmentList=" + list.size());
        //先添加数据，可能mRvList还没创建
        mDataList.clear();
        mDataList.addAll(list);
        if (mAdapter == null) {
            FuliLogger.log("订阅历史模块==:mAdapter=updateMySubscriptionFragmentList=null");
            return;
        }
        if (mRvList == null) {
            FuliLogger.log("订阅历史模块==:mRvList=updateMySubscriptionFragmentList=null");
            return;
        }
//      mRvList.scrollToPosition(0);
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void onMyResume() {
        //不做状态颜色处理
        setFilterStatusBarSet(true);
        super.onMyResume();
    }

    @Override
    public int getTitleBarResourceId() {
        return -1;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_item_fragment_my_subscription;
    }

    @Override
    public void onClick(View v) {

    }

    @Override
    protected boolean isShowSevenDay() {
        return true;
    }

    public OnScrollListLastListener onScrollListLastListener;

    public void setOnScrollListLastListener(OnScrollListLastListener onScrollListLastListener) {
        this.onScrollListLastListener = onScrollListLastListener;
    }

    public interface OnScrollListLastListener {
        void onScrollLastLast();
    }

    @Override
    public boolean isGlobalFloatViewGray() {
        if (getParentFragment() instanceof LiteHomeRecommendFragment) {
            return ((LiteHomeRecommendFragment) getParentFragment()).isGlobalFloatViewGray();
        }
        return false;
    }
}
