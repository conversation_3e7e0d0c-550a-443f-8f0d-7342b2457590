package com.ximalaya.ting.lite.main.book.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.db.model.BookInfo;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmriskdatacollector.util.ScreenUtils;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;
import com.ximalaya.ting.lite.main.book.bean.BookShelfAddBean;
import com.ximalaya.ting.lite.main.book.bean.BookWrapperBean;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

public class BookshelfDialogListAdapter extends AbRecyclerViewAdapter<RecyclerView.ViewHolder> {

    private final Context mContext;
    private final List<BookWrapperBean<?>> mList;

    private final int VIEW_TYPE_BOOK = 1;
    private final int VIEW_TYPE_ADD = 2;

    private static float mItemWidth;
    private static float mImgWidth;
    private static float mImgHeight;

    // 是否编辑模式
    private boolean isEditModel;

    private onBookEditListener mBookEditListener;

    public BookshelfDialogListAdapter(Context context, List<BookWrapperBean<?>> list) {
        mList = list;
        mContext = context;
    }

    public void setBookEditListener(onBookEditListener mBookEditListener) {
        this.mBookEditListener = mBookEditListener;
    }

    public void changeEditMode(boolean isEdit) {
        if (isEditModel == isEdit) {
            return;
        }
        isEditModel = isEdit;
        notifyDataSetChanged();
    }

    public void performSelectAll(boolean isSelect) {
        if (mList == null) {
            return;
        }

        for (BookWrapperBean<?> wrapperBean : mList) {
            if (wrapperBean != null) {
                if (wrapperBean.getData() instanceof BookInfo) {
                    BookInfo eBook = (BookInfo) wrapperBean.getData();
                    if (eBook != null) {
                        eBook.setSelect(isSelect);
                    }
                }
            }
        }

        notifyDataSetChanged();
    }

    @NotNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view;

        if (mItemWidth == 0) {
            mItemWidth = (ScreenUtils.getScreenWidth() - BaseUtil.dp2px(mContext, 16)) / 3f;
            // 图片距离item左右两边留白12dp
            mImgWidth = mItemWidth - BaseUtil.dp2px(mContext, 24);
            mImgHeight = mImgWidth * 1.42f;
        }

        switch (viewType) {
            case VIEW_TYPE_BOOK:
                view = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_item_book_shelf_book_layout, parent, false);
                break;
            case VIEW_TYPE_ADD:
                view = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_item_bookshelf_dialog_more_layout, parent, false);
                return new AddViewHolder(view);
            default:
                view = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_item_book_shelf_book_layout, parent, false);
                break;
        }
        return new BookshelfDialogListAdapter.BookViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (mList == null || position < 0 || position >= mList.size()) {
            return;
        }
        final BookWrapperBean<?> wrapperBean = mList.get(position);
        if (wrapperBean == null) {
            return;
        }

        if (wrapperBean.getData() instanceof BookInfo) {
            final BookInfo bookInfo = (BookInfo) wrapperBean.getData();
            if (bookInfo == null) {
                return;
            }

            if (holder instanceof BookViewHolder) {
                BookViewHolder viewHolder = (BookViewHolder) holder;
                viewHolder.cbSelect.setVisibility(isEditModel ? View.VISIBLE : View.GONE);
                viewHolder.cbSelect.setChecked(bookInfo.isSelect());

                View.OnClickListener listener = v -> {
                    if (isEditModel) {
                        // 切换选中状态
                        bookInfo.setSelect(!bookInfo.isSelect());
                        viewHolder.cbSelect.setChecked(bookInfo.isSelect());

                        if (mBookEditListener != null) {
                            mBookEditListener.onSelectBook(position);
                        }
                    } else {
                        if (mBookEditListener != null) {
                            mBookEditListener.onClickBook(position);
                        }
                    }
                };
                viewHolder.cbSelect.setOnClickListener(listener);
                viewHolder.itemView.setOnClickListener(listener);

                viewHolder.itemView.setOnLongClickListener(v -> {
                    if (mBookEditListener != null) {
                        mBookEditListener.onLongClickBook(position);
                    }
                    return true;
                });

                viewHolder.tvName.setText(bookInfo.getBookName());
                ImageManager.from(mContext).displayImage(((BookViewHolder) holder).ivCover, bookInfo.getBookCover(), R.drawable.host_bg_book_default);

                if (bookInfo.isOffShelf()) {
                    viewHolder.tvOffShelfCover.setVisibility(View.VISIBLE);
                    viewHolder.tvName.setTextColor(mContext.getResources().getColor(R.color.main_color_4c333333));
                } else {
                    viewHolder.tvOffShelfCover.setVisibility(View.GONE);
                    viewHolder.tvName.setTextColor(mContext.getResources().getColor(R.color.main_color_333333));
                }

            }
        } else if (wrapperBean.getData() instanceof BookShelfAddBean) {

            if (holder instanceof AddViewHolder) {
                AddViewHolder viewHolder = (AddViewHolder) holder;
                viewHolder.itemView.setOnClickListener(v -> {
                    if (mBookEditListener != null) {
                        mBookEditListener.onClickAddBook(position);
                    }
                });
            }
        }

    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }

    @Override
    public int getItemViewType(int position) {
        final BookWrapperBean<?> wrapperBean = mList.get(position);
        if (wrapperBean != null && wrapperBean.getData() instanceof BookShelfAddBean) {
            return VIEW_TYPE_ADD;
        }

        return VIEW_TYPE_BOOK;
    }

    private static void setItemViewWidth(View itemView) {
        RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) itemView.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new RecyclerView.LayoutParams((int) mItemWidth, ConstraintLayout.LayoutParams.WRAP_CONTENT);
        } else {
            layoutParams.width = (int) mItemWidth;
        }
        itemView.setLayoutParams(layoutParams);
    }

    private static void setCoverViewWidth(View coverView) {
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) coverView.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new ConstraintLayout.LayoutParams((int) mImgWidth, (int) mImgHeight);
        } else {
            layoutParams.width = (int) mImgWidth;
            layoutParams.height = (int) mImgHeight;
        }
        coverView.setLayoutParams(layoutParams);
    }

    @Override
    public Object getItem(int position) {
        if (mList != null && position >= 0 && mList.size() > position) {
            return mList.get(position).getData();
        }
        return null;
    }


    public static class BookViewHolder extends RecyclerView.ViewHolder {
        View itemView;
        RoundImageView ivCover;
        TextView tvName;
        CheckBox cbSelect;
        TextView tvOffShelfCover;

        public BookViewHolder(@NonNull View view) {
            super(view);
            itemView = view.findViewById(R.id.main_book_shelf_root);
            ivCover = view.findViewById(R.id.main_book_shelf_iv_cover);
            tvName = view.findViewById(R.id.main_book_shelf_tv_name);
            cbSelect = view.findViewById(R.id.main_book_shelf_cb_select);
            tvOffShelfCover = view.findViewById(R.id.main_book_shelf_tv_img_cover);

            setItemViewWidth(itemView);
            setCoverViewWidth(ivCover);
        }
    }

    public static class AddViewHolder extends RecyclerView.ViewHolder {
        View itemView;
        ConstraintLayout ivCover;

        public AddViewHolder(@NonNull View view) {
            super(view);
            itemView = view.findViewById(R.id.main_book_shelf_root);
            ivCover = view.findViewById(R.id.main_book_shelf_iv_cover);

            setItemViewWidth(itemView);
            setCoverViewWidth(ivCover);
        }
    }

    public interface onBookEditListener {
        void onClickBook(int position);

        void onLongClickBook(int position);

        void onClickAddBook(int position);

        void onSelectBook(int position);
    }
}
