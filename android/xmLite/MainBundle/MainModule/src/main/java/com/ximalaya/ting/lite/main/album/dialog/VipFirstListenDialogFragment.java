package com.ximalaya.ting.lite.main.album.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.model.homepage.VipResourceInfo;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * <AUTHOR>
 */
public class VipFirstListenDialogFragment extends BaseDialogFragment {

    private LinearLayout mLlClose;
    private boolean mMaskIsShow = false; //解决fragment重复添加crash问题
    private TextView mTvDownloadFullXmly;

    private VipResourceInfo mVipResourceInfo;
    private IDialogViewClickListener mListener;
    private long mAlbumId;

    public void setVipResourceInfo(VipResourceInfo vipResourceInfo) {
        mVipResourceInfo = vipResourceInfo;
    }

    public void setListener(IDialogViewClickListener listener) {
        mListener = listener;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.main_dialog_vip_first_listen, container, false);
        mLlClose = view.findViewById(R.id.main_ll_close);
        mTvDownloadFullXmly = view.findViewById(R.id.main_tv_buy_vip);
        if(mVipResourceInfo != null && !TextUtils.isEmpty(mVipResourceInfo.buttonContent)) {
            mTvDownloadFullXmly.setText(mVipResourceInfo.buttonContent);
        }
        mTvDownloadFullXmly.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                dismissAllowingStateLoss();
                if(mListener != null && mVipResourceInfo != null && !TextUtils.isEmpty(mVipResourceInfo.url)) {
                    mListener.onOkButtonClick(mVipResourceInfo.url);
                    new XMTraceApi.Trace()
                            .setMetaId(12740)
                            .setServiceId("dialogClick")
                            .put("item", "购买")
                            .put("albumId", String.valueOf(mAlbumId))
                            .createTrace();
                }

            }
        });
        AutoTraceHelper.bindData(mTvDownloadFullXmly, AutoTraceHelper.MODULE_DEFAULT, "");

        //点击任意区域关闭
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissAllowingStateLoss();
            }
        });
        AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, "");
        mLlClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissAllowingStateLoss();
            }
        });
        AutoTraceHelper.bindData(mLlClose, AutoTraceHelper.MODULE_DEFAULT, "");

        new XMTraceApi.Trace()
                .setMetaId(12739)
                .setServiceId("dialogView")
                .put("albumId", String.valueOf(mAlbumId))
                .createTrace();
        return view;
    }


    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        //去掉标题
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setCanceledOnTouchOutside(false);
        Window window = dialog.getWindow();
        if (window != null) {
            //dialog背景设置透明，解决shape不生效问题
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setPadding(0, 0, 0, 0); //消除边距
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;   //设置宽度充满屏幕
            lp.height = WindowManager.LayoutParams.MATCH_PARENT;
            window.setAttributes(lp);
        }
        return dialog;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mMaskIsShow = false;
    }

    public boolean isShowing() {
        return mMaskIsShow;
    }

    @Override
    public int show(FragmentTransaction transaction, String tag) {
        if (mMaskIsShow) {
            return 0;
        }
        mMaskIsShow = true;
        return super.show(transaction, tag);
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        if (mMaskIsShow) {
            return;
        }
        mMaskIsShow = true;
        super.show(manager, tag);
    }


    @Override
    public void dismiss() {
        super.dismiss();
    }

    public void setAlbumId(long albumId) {
        mAlbumId = albumId;
    }


    public interface IDialogViewClickListener {
        void onOkButtonClick(String url);
    }

}
