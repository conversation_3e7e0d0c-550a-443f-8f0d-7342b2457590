package com.ximalaya.ting.lite.main.download.bean;

import com.ximalaya.ting.lite.main.download.utils.SpeedHelper;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   : 统计类，用于统计已完成大小，速度等等
 */
public class Statistics {
    // 速度与激情
    public double speed;
    // 以完成量
    public long done;
    // 本次累计量
    public long read;
    // 总量
    public long total;
    private final SpeedHelper calculator = new SpeedHelper();

    public void init(long done, long total) {
        this.done = done;
        this.total = total;
        this.speed = 0;
        this.read = 0;
    }

    public void reset() {
        speed = 0;
        done = 0;
        read = 0;
    }

    public void increment(long len) {
        read += len;
        done += len;
        speed = calculator.calculate(read);
    }

    /**
     * 开始统计
     */
    public void start() {
        calculator.start();
    }

}
