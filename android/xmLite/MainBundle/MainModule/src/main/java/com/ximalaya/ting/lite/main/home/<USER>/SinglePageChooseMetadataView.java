package com.ximalaya.ting.lite.main.home.view;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.HorizontalScrollViewInSlideView;
import com.ximalaya.ting.android.host.model.category.CategoryMetadata;
import com.ximalaya.ting.android.host.model.category.CategoryMetadataValue;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.constant.DPConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by dumingwei on 2021/1/15
 * <p>
 * Desc: 通用筛选页用的筛选View
 */
public class SinglePageChooseMetadataView extends LinearLayout implements View.OnClickListener {

    private static final String TAG = "SinglePageChooseMetadat";

    public static final String CAL_DIMEN_RECENT = "recent";
    public static final String CAL_DIMEN_HOT = "hot";
    public static final String CAL_DIMEN_CLASSIC = "classic";
    public static final String CAL_DIMEN_DEFAULT = CAL_DIMEN_HOT;

    public static final int FROM_CATEGORY = 1;
    public static final int FROM_KEYWORD = 2;
    public static final String DEFAULT_SORT = "综合排序";
    private int mFrom = FROM_CATEGORY;

    private String mCategoryId;
    private String mKeywordId;

    /**
     * 综合排序
     */
    private LinearLayout llSort;

    public void setCategoryId(String categoryId) {
        this.mCategoryId = categoryId;
    }

    public void setFrom(int from) {
        mFrom = from;
    }

    public void setKeywordId(String keywordId) {
        mKeywordId = keywordId;
    }

    private boolean mIsFold = false; //是否为收起状态


    private List<CategoryMetadata> mMetadata;
    private View mSlideView;
    private final List<String> mMetadataDisplayNameList = new ArrayList<>();
    private String mCalDimension = CAL_DIMEN_DEFAULT;
    private String mMetadataHttpRequestParam;
    private List<OnMetadataChangeListener> mMetadataChangeListeners = new ArrayList<>();

    private FrameLayout mFlFoldContainer;
    private TextView mTvFold;
    private String chooseMetaData;

    private int selectedColor;
    private int unSelectedColor;

    private boolean sortVisible = true;


    private int dp2;
    private int dp10;


    public void setSortVisible(boolean sortVisible) {
        this.sortVisible = sortVisible;
    }

    public SinglePageChooseMetadataView(Context context) {
        super(context);
        init();
    }

    public SinglePageChooseMetadataView(Context context, String calDimension, String metadata) {
        super(context);
        mCalDimension = calDimension;
        chooseMetaData = metadata;

        init();
    }

    private void setCategoryMetaDataValue(List<CategoryMetadata> object, String chooseMetadata) {
        if (!TextUtils.isEmpty(chooseMetadata)) {
            if (!TextUtils.isEmpty(chooseMetadata)) {
                String[] selectedMetaDataStrArray = chooseMetadata.split(":");
                if (selectedMetaDataStrArray.length >= 2) {
                    int selectedMetadataId = Integer.parseInt(selectedMetaDataStrArray[0]);
                    int selectedMetadataValueId = Integer.parseInt(selectedMetaDataStrArray[1]);
                    for (CategoryMetadata categoryMetadata : object) {
                        if (categoryMetadata.getId() == selectedMetadataId) {
                            if (categoryMetadata.getMetadataValues() != null) {
                                for (CategoryMetadataValue categoryMetadataValue : categoryMetadata.getMetadataValues()) {
                                    if (categoryMetadataValue.getId() == selectedMetadataValueId) {
                                        categoryMetadata.setChosed(false);
                                        categoryMetadataValue.setChosed(true);
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    public SinglePageChooseMetadataView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public SinglePageChooseMetadataView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public void setSlideView(View slideView) {
        mSlideView = slideView;
    }

    private void init() {
        dp2 = DPConstants.getInstance(getContext()).DP_2;
        dp10 = DPConstants.getInstance(getContext()).DP_10;

        selectedColor = getResources().getColor(R.color.main_color_e83f46);
        unSelectedColor = getResources().getColor(R.color.main_color_999999);

        setOrientation(LinearLayout.VERTICAL);

        setFocusable(false);
        setFocusableInTouchMode(false);

        mTvFold = new TextView(getContext());
        if (mIsFold) {
            mTvFold.setText("筛选");
        } else {
            mTvFold.setText("收起");
        }
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.RIGHT;
        params.rightMargin = BaseUtil.dp2px(getContext(), 15);
        mTvFold.setLayoutParams(params);
        mTvFold.setTextSize(16);
        ColorStateList stateList = getResources().
                getColorStateList(R.color.main_text_gray_clickable);
        mTvFold.setTextColor(stateList);
        mTvFold.setCompoundDrawablePadding(BaseUtil.dp2px(getContext(), 2));
        Drawable leftDrawable = LocalImageUtil.getDrawable(getContext(), R.drawable.main_ic_category_filter_new);
        mTvFold.setCompoundDrawables(leftDrawable, null, null, null);
        mTvFold.setBackgroundResource(R.drawable.main_bg_corner_ccf3f4f5);
        mTvFold.setPadding(DPConstants.getInstance(getContext()).DP_8,
                DPConstants.getInstance(getContext()).DP_2,
                DPConstants.getInstance(getContext()).DP_8,
                DPConstants.getInstance(getContext()).DP_2);
        AutoTraceHelper.bindData(mTvFold, "");

        mFlFoldContainer = new FrameLayout(getContext());
        LayoutParams llParams = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        mFlFoldContainer.setLayoutParams(llParams);
        mFlFoldContainer.addView(mTvFold);
    }

    public void setMetadata(List<CategoryMetadata> metadata) {
        setMetadata(metadata, -1, -1);
    }

    /**
     * 递归查找出指定标签所在的节点
     */
    private CategoryMetadataValue getSelectMetadataOrMetadataValue(List<CategoryMetadata> metadata, int metadataValueId) {
        CategoryMetadataValue result = null;
        for (CategoryMetadata categoryMetadata : metadata) {
            List<CategoryMetadataValue> metadataValues = categoryMetadata.getMetadataValues();
            if (!ToolUtil.isEmptyCollects(metadataValues)) {
                for (CategoryMetadataValue categoryMetadataValue : metadataValues) {
                    if (categoryMetadataValue.getId() == metadataValueId) {
                        return categoryMetadataValue;
                    } else if (!ToolUtil.isEmptyCollects(categoryMetadataValue.getMetadatas())) {
                        result = getSelectMetadataOrMetadataValue(categoryMetadataValue.getMetadatas(), metadataValueId);
                        if (result != null) {
                            return result;
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 由指定节点递归向上回溯父节点，改变状态
     */
    private void requestUpdateMetadataValue(CategoryMetadataValue selectedMetadataValue) {
        updateSelectedMetadata(selectedMetadataValue);
        CategoryMetadata categoryMetadata = selectedMetadataValue.getParentMetadata();
        if (categoryMetadata != null) {
            CategoryMetadataValue categoryMetadataValue = categoryMetadata.getParentMetadataValue();
            if (categoryMetadataValue != null) {
                requestUpdateMetadataValue(categoryMetadataValue);
            }
        }
    }

    public void setMetadata(List<CategoryMetadata> metadata, int metadataId, int metadataValueId) {
        // 跳转过来有指定某一个标签（如专辑页标签跳转）
        if (metadataValueId != -1 && !ToolUtil.isEmptyCollects(metadata)) {
            CategoryMetadataValue selectedMetadataValue = getSelectMetadataOrMetadataValue(metadata, metadataValueId);
            if (null != selectedMetadataValue) {
                requestUpdateMetadataValue(selectedMetadataValue);
            }
        }

        mMetadata = metadata;
        inflateFilterPanel(this, metadata);
        parseMetaParams();
    }

    private Map<CategoryMetadata, HorizontalScrollView> mHorizontalScrollViews = new HashMap<>();

    @NonNull
    private HorizontalScrollView getChild(CategoryMetadata categoryMetadata) {
        HorizontalScrollView scrollView = mHorizontalScrollViews.get(categoryMetadata);
        if (scrollView == null) {
            scrollView = new HorizontalScrollViewInSlideView(getContext());
            if (mHorizontalScrollViews.isEmpty()) {
                //第一行上边距大一点
                scrollView.setPadding(BaseUtil.dp2px(getContext(), 15), BaseUtil.dp2px(getContext(), 16), 0, BaseUtil.dp2px(getContext(), 6));
            } else {
                scrollView.setPadding(BaseUtil.dp2px(getContext(), 15), BaseUtil.dp2px(getContext(), 6), 0, BaseUtil.dp2px(getContext(), 6));
            }

            if (mSlideView != null) {
                ((HorizontalScrollViewInSlideView) scrollView).
                        setDisallowInterceptTouchEventView((ViewGroup) mSlideView);
            }

            scrollView.setTag(categoryMetadata);
            scrollView.requestDisallowInterceptTouchEvent(true);
            scrollView.setHorizontalScrollBarEnabled(false);

            LinearLayout layout = new LinearLayout(getContext());
            LayoutParams lp = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layout.setLayoutParams(lp);
            scrollView.addView(layout);

            mHorizontalScrollViews.put(categoryMetadata, scrollView);
        }

        return scrollView;
    }

    private void doRecursiveInflate(ViewGroup viewGroup, List<CategoryMetadata> metadata) {
        if (metadata != null && metadata.size() > 0) {
            for (CategoryMetadata categoryMetadata : metadata) {
                Logger.log("Metadata___" + categoryMetadata.getDisplayName());
                final HorizontalScrollView scrollView = getChild(categoryMetadata);
                viewGroup.addView(scrollView);
                AutoTraceHelper.setLabelForCTWithMultiSameSubView(viewGroup);
                LinearLayout layout = (LinearLayout) scrollView.getChildAt(0);
                AutoTraceHelper.setLabelForCTWithMultiSameSubView(layout);

                LayoutParams metaViewPs = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                metaViewPs.leftMargin = BaseUtil.dp2px(getContext(), 4);
                metaViewPs.rightMargin = BaseUtil.dp2px(getContext(), 4);

                int index = 0;
                TextView tv;
                if (layout.getChildAt(index) == null) {
                    tv = getView(categoryMetadata);
                    layout.addView(tv, metaViewPs);
                } else {
                    tv = (TextView) layout.getChildAt(index);
                    if (categoryMetadata.isChosed()) {
                        setTextViewBold(tv, true);
                        tv.setTextColor(selectedColor);
                        tv.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12);
                        tv.setOnClickListener(null);
                    } else {
                        setTextViewBold(tv, false);
                        tv.setTextColor(unSelectedColor);
                        tv.setBackground(null);
                        tv.setOnClickListener(this);
                        Map<String, Object> bindData = new HashMap<>();
                        bindData.put("rowID", categoryMetadata.getId());
                        bindData.put("displayName", categoryMetadata.getDisplayName());
                        AutoTraceHelper.bindData(tv, AutoTraceHelper.MODULE_DEFAULT, bindData);
                    }
                }
                LayoutParams childPs = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                childPs.leftMargin = BaseUtil.dp2px(getContext(), 4);
                childPs.rightMargin = BaseUtil.dp2px(getContext(), 4);
                for (CategoryMetadataValue value : categoryMetadata.getMetadataValues()) {
                    index++;
                    final TextView view;
                    if (index < layout.getChildCount() && layout.getChildAt(index) != null) {
                        view = (TextView) layout.getChildAt(index);
                        if (value.isChosed()) {
                            setTextViewBold(view, true);
                            view.setTextColor(selectedColor);
                            view.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12);
                            view.setOnClickListener(null);
                        } else {
                            setTextViewBold(view, false);
                            view.setTextColor(unSelectedColor);
                            view.setBackground(null);
                            view.setOnClickListener(this);
                            Map<String, Object> bindData = new HashMap<>();
                            bindData.put("rowID", categoryMetadata.getId());
                            bindData.put("columnID", value.getId());
                            bindData.put("displayName", categoryMetadata.getDisplayName());
                            AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, bindData);
                        }
                        view.setLayoutParams(childPs);
                    } else {
                        view = getView(value, categoryMetadata);
                        layout.addView(view, childPs);
                    }
                    if (value.isChosed()) {
                        //搜索直达中需要HorizontalScrollView直接跳到被选中的TextView上
                        scrollToLocation(scrollView, view);
                        scrollView.addOnLayoutChangeListener(new OnLayoutChangeListener() {
                            @Override
                            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                                removeOnLayoutChangeListener(this);
                                if (view != null && view.getTag() != null) {
                                    Object tag = view.getTag();
                                    if (tag instanceof CategoryMetadata) {
                                        if (((CategoryMetadata) tag).isChosed()) {
                                            scrollToLocation(scrollView, view);
                                        }
                                    } else if (tag instanceof CategoryMetadataValue) {
                                        if (((CategoryMetadataValue) tag).isChosed()) {
                                            scrollToLocation(scrollView, view);
                                        }
                                    }
                                }
                            }
                        });
                    }
                }

                //添加次级数据
                if (!categoryMetadata.isChosed() && categoryMetadata.getMetadataValues() != null) {
                    for (CategoryMetadataValue value : categoryMetadata
                            .getMetadataValues()) {
                        if (value.isChosed()) {
                            doRecursiveInflate(viewGroup, value.getMetadatas());
                        }
                    }
                }
            }
        }
    }

    private void scrollToLocation(final HorizontalScrollView scrollView, final TextView view) {
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        if (location[0] > BaseUtil.getScreenWidth(getContext())) {
            scrollView.scrollTo(location[0] - view.getWidth() / 2, location[1]);
        }
    }

    private TextView getView(CategoryMetadata data) {
        TextView tv = new TextView(getContext());
        tv.setText(data.getDisplayName());
        tv.setTextSize(16);
        tv.setPadding(dp10, dp2, dp10, dp2);
        if (data.isChosed()) {
            Logger.i(TAG, "getView CategoryMetadata.isChosed() = true ");
            setTextViewBold(tv, true);
            tv.setTextColor(selectedColor);
            tv.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12);
            tv.setOnClickListener(null);
        } else {
            Logger.i(TAG, "getView CategoryMetadata.isChosed() = false ");
            setTextViewBold(tv, false);
            tv.setTextColor(unSelectedColor);
            tv.setBackground(null);
            tv.setOnClickListener(this);
            Map<String, Object> bindData = new HashMap<>();
            bindData.put("rowID", data.getId());
            bindData.put("displayName", data.getDisplayName());
            AutoTraceHelper.bindData(tv, AutoTraceHelper.MODULE_DEFAULT, bindData);
        }
        tv.setTag(data);

        return tv;
    }

    private TextView getView(CategoryMetadataValue data, CategoryMetadata categoryMetadata) {
        TextView tv = new TextView(getContext());
        tv.setText(data.getDisplayName());
        tv.setTextSize(16);
        tv.setPadding(dp10, dp2, dp10, dp2);
        if (data.isChosed()) {
            Logger.i(TAG, "getView CategoryMetadataValue.isChosed() = true ");
            setTextViewBold(tv, true);
            tv.setTextColor(selectedColor);
            tv.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12);
            tv.setOnClickListener(null);
        } else {
            Logger.i(TAG, "getView CategoryMetadataValue.isChosed() =false ");
            setTextViewBold(tv, false);
            tv.setTextColor(unSelectedColor);
            tv.setBackground(null);
            tv.setOnClickListener(this);
            Map<String, Object> bindData = new HashMap<>();
            bindData.put("rowID", categoryMetadata.getId());
            bindData.put("columnID", data.getId());
            bindData.put("displayName", data.getDisplayName());
            AutoTraceHelper.bindData(tv, AutoTraceHelper.MODULE_DEFAULT, bindData);
        }
        tv.setTag(data);

        return tv;
    }

    /**
     * @param selectedItem 被选中的model 可能为CategoryMetadata 或 CategoryMetadataValue
     */
    private void updateSelectedMetadata(Object selectedItem) {
        if (selectedItem instanceof CategoryMetadata) {
            CategoryMetadata metadata = (CategoryMetadata) selectedItem;
            metadata.setChosed(true);

            for (CategoryMetadataValue value : metadata.getMetadataValues()) {
                value.setChosed(false);
            }
        } else if (selectedItem instanceof CategoryMetadataValue) {
            CategoryMetadataValue metadataValue = (CategoryMetadataValue) selectedItem;
            metadataValue.getParentMetadata().setChosed(false);

            for (CategoryMetadataValue value : metadataValue.getParentMetadata().getMetadataValues()) {
                if (value.getId() == metadataValue.getId()) {
                    value.setChosed(true);
                } else {
                    value.setChosed(false);
                }
            }
        }
    }

    /**
     * 分类过滤元数据布局初始化
     */
    private void inflateFilterPanel(ViewGroup viewGroup, List<CategoryMetadata> metadata) {
        viewGroup.removeAllViews();
        if (mShowDivider) {
            addDivider(viewGroup);
        }
        doRecursiveInflate(viewGroup, metadata);

        addCalDimension(viewGroup);

        addBottomDivider(viewGroup);
    }

    private void addDivider(ViewGroup viewGroup) {
        View divider = new View(getContext());
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 1);
        params.bottomMargin = BaseUtil.dp2px(getContext(), 10);
        divider.setLayoutParams(params);
        divider.setBackgroundColor(Color.parseColor("#E8E8E8"));
        viewGroup.addView(divider);
    }

    private View mBottomDivider;
    private boolean mShowBottomDivider = true;
    private boolean mShowDivider = false;

    private void addBottomDivider(ViewGroup viewGroup) {
        mBottomDivider = new View(getContext());
        LayoutParams lp = new LayoutParams(
                AbsListView.LayoutParams.MATCH_PARENT, BaseUtil.dp2px(
                getContext(), 1));
        lp.bottomMargin = BaseUtil.dp2px(getContext(), 10);
        lp.topMargin = BaseUtil.dp2px(getContext(), 10);
        mBottomDivider.setLayoutParams(lp);
        mBottomDivider.setBackgroundColor(Color.parseColor("#f3f4f5"));
        viewGroup.addView(mBottomDivider);
    }

    public void showBottomDivider(boolean show) {
        mShowBottomDivider = show;
        if (mBottomDivider == null) {
            return;
        }
        if (show) {
            mBottomDivider.setVisibility(VISIBLE);
        } else {
            mBottomDivider.setVisibility(INVISIBLE);
        }
    }

    private void addCalDimension(ViewGroup viewGroup) {
        if (!sortVisible) {
            return;
        }
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                LayoutParams.WRAP_CONTENT);

        params.leftMargin = BaseUtil.dp2px(getContext(), 15);
        if (CollectionUtil.isNullOrEmpty(mMetadata)) {
            params.topMargin = BaseUtil.dp2px(getContext(), 16);
        } else {
            params.topMargin = BaseUtil.dp2px(getContext(), 6);
        }
        params.bottomMargin = BaseUtil.dp2px(getContext(), 6);

        llSort = new LinearLayout(getContext());
        llSort.setGravity(Gravity.CENTER_VERTICAL);

        llSort.setLayoutParams(params);
        String[] calDimensions = {CAL_DIMEN_HOT,
                CAL_DIMEN_CLASSIC,
                CAL_DIMEN_RECENT};

        LinearLayout.LayoutParams childParams = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
                LayoutParams.WRAP_CONTENT);

        childParams.leftMargin = BaseUtil.dp2px(getContext(), 4);
        childParams.rightMargin = BaseUtil.dp2px(getContext(), 4);

        for (final String str : calDimensions) {
            final String displayName = getCalDimenDisplayName(str);
            TextView subTv = new TextView(getContext());
            subTv.setText(displayName);
            subTv.setTextSize(16);

            subTv.setPadding(dp10, dp2, dp10, dp2);

            if (getCalDimenDisplayName(mCalDimension).equals(displayName)) {
                setTextViewBold(subTv, true);
                subTv.setTextColor(selectedColor);
                subTv.setBackgroundResource(R.drawable.main_bg_eeeeee_radius_12);
            } else {
                setTextViewBold(subTv, false);
                subTv.setTextColor(unSelectedColor);
                subTv.setBackground(null);
                subTv.setOnClickListener(new OnClickListener() {

                    @Override
                    public void onClick(View v) {
                        UserTracking userTracking = new UserTracking();
                        String srcPage = "";
                        if (mFrom == FROM_KEYWORD) {
                            srcPage = "hotword";
                            userTracking.setSrcPageId(mKeywordId);
                        } else {
                            srcPage = "全部分类页";
                        }

                        userTracking.setSrcPage(srcPage).
                                setSrcModule("排序").
                                setItem("button").
                                setItemId(displayName).
                                setCategory(mCategoryId).
                                statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_CATEGORY_PAGE_CLICK);

                        if (!NetworkUtils.isNetworkAvaliable(getContext().getApplicationContext())) {
                            CustomToast.showFailToast(R.string.main_network_exeption_toast);
                            return;
                        }

                        mCalDimension = str;

                        //移除掉综合排序一栏
                        removeView(llSort);
                        addCalDimension(SinglePageChooseMetadataView.this);

                        //重新移除和添加底部分割线，否则综合排序会添加到mBottomDivider下面
                        removeView(mBottomDivider);
                        addBottomDivider(SinglePageChooseMetadataView.this);

                        notifyMetadataChangeListener();
                    }
                });
                AutoTraceHelper.setLabelForCTWithMultiSameSubView(llSort);
                AutoTraceHelper.bindData(subTv, AutoTraceHelper.MODULE_DEFAULT, "");
            }
            llSort.addView(subTv, childParams);
        }
        viewGroup.addView(llSort);
    }


    @Override
    public void onClick(View v) {
        if (!NetworkUtils.isNetworkAvaliable(getContext().getApplicationContext())) {
            CustomToast.showFailToast(R.string.main_network_exeption_toast);
            return;
        }

        TextView tv = (TextView) v;
        tv.setClickable(false);
        Object object = tv.getTag();

        List<CategoryMetadata> metadata = mMetadata;
        updateSelectedMetadata(object);

        inflateFilterPanel(this, metadata);

        parseMetaParams();

        String srcPage = "";
        UserTracking userTracking = new UserTracking();
        if (mFrom == FROM_CATEGORY) {
            srcPage = "全部分类页";
        } else {
            srcPage = "hotword";
            userTracking.setSrcPageId(mKeywordId);
        }
        userTracking.
                setSrcPage(srcPage).
                setSrcModule("类目搜索").
                setCategory(mCategoryId).
                setMetaData(mMetadataHttpRequestParam).
                statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_CATEGORY_PAGE_CLICK);
    }

    private void parseMetaParams() {
        mMetadataDisplayNameList.clear();

        StringBuilder outSb = new StringBuilder();
        recursiveParseMetaParams(mMetadata, outSb, mMetadataDisplayNameList);
        if (outSb.length() > 0) {
            mMetadataHttpRequestParam = outSb.substring(0, outSb.length() - 1);  //去除最后的"," 号
        } else {
            mMetadataHttpRequestParam = "";
        }

        notifyMetadataChangeListener();
    }

    /**
     * 解析已选的标签元数据
     */
    private void recursiveParseMetaParams(List<CategoryMetadata> metadata,
                                          StringBuilder httpRequestParam,
                                          List<String> displayNameList) {
        if (metadata == null || httpRequestParam == null || displayNameList == null) {
            return;
        }

        for (CategoryMetadata categoryMetadata : metadata) {

            for (CategoryMetadataValue value : categoryMetadata
                    .getMetadataValues()) {
                if (value.isChosed() && value.getParentMetadata().getId() != 0) {//只筛选单行子标签的数据
                    httpRequestParam.append(value.getParentMetadata().getId());
                    httpRequestParam.append(":");
                    httpRequestParam.append(value.getId());
                    httpRequestParam.append(",");
                    displayNameList.add(value.getDisplayName());
                }
            }

            //操作次级数据
            if (!categoryMetadata.isChosed() && categoryMetadata.getMetadataValues() != null) {
                for (CategoryMetadataValue value : categoryMetadata.getMetadataValues()) {
                    if (value.isChosed()) {
                        recursiveParseMetaParams(value.getMetadatas(), httpRequestParam, displayNameList);
                    }
                }
            }

        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {  // 限制该view 的高度最高为屏幕的2/3
        int originHeight = MeasureSpec.getSize(heightMeasureSpec);
        int maxHeight = BaseUtil.getScreenHeight(getContext()) / 3 * 2;
        if (maxHeight < originHeight) {
            heightMeasureSpec = MeasureSpec.makeMeasureSpec(maxHeight, MeasureSpec.AT_MOST);
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    public interface OnMetadataChangeListener {
        /**
         * @param calDimension         筛选维度
         * @param metadataRequestParam 用于筛选专辑的元数据请求参数
         * @param hintStr              展示给用户看的，当前已选的筛选元数据
         */
        void onMetadataChange(String calDimension, String metadataRequestParam, String hintStr);
    }

    public void addMetadataChangeListener(OnMetadataChangeListener listener) {
        if (listener != null) {
            mMetadataChangeListeners.add(listener);
        }
    }

    public void removeMetadataChangeListener(OnMetadataChangeListener listener) {
        if (listener != null) {
            mMetadataChangeListeners.remove(listener);
        }
    }

    private void notifyMetadataChangeListener() {
        String hintStr = getHintStr(mMetadataDisplayNameList);
        for (OnMetadataChangeListener listener : mMetadataChangeListeners) {
            listener.onMetadataChange(mCalDimension, mMetadataHttpRequestParam, hintStr);
        }
    }

    private String getHintStr(List<String> displayNames) {
        StringBuilder sb = new StringBuilder();
        int size = displayNames.size();
        for (int i = 0; i < size; i++) {
            sb.append(displayNames.get(i));
            if (i < size - 1) {
                sb.append(" · ");
            }
        }
        String hintString = sb.toString();

        String sortString = getCalDimenDisplayName(mCalDimension);

        if (sortString == null) {
            sortString = DEFAULT_SORT;
        }

        if (TextUtils.isEmpty(hintString)) {
            return sortString;
        }

        sb = new StringBuilder(hintString);
        sb.append(" · ");
        sb.append(sortString);

        return sb.toString();
    }

    private static final Map<String, String> CAL_DIMEN_DISPLAY_NAMES = new HashMap<String, String>() {
        {
            put(CAL_DIMEN_CLASSIC, "播放最多");
            put(CAL_DIMEN_HOT, DEFAULT_SORT);
            put(CAL_DIMEN_RECENT, "最近更新");
        }
    };

    public static String getCalDimenDisplayName(String calDimen) {
        return CAL_DIMEN_DISPLAY_NAMES.get(calDimen);
    }

    public void setTextViewBold(TextView textView, boolean isBold) {
        if (textView != null) {
            if (isBold) {
                String familyName = "sans-serif-light";
                Typeface boldTypeface = Typeface.create(familyName, Typeface.BOLD);
                textView.setTypeface(boldTypeface);
            } else {
                textView.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            }
        }
    }

}
