package com.ximalaya.ting.lite.main.base.album;

import android.content.Context;
import android.text.Spanned;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.trace.TraceFreeAlbumManager;
import com.ximalaya.ting.android.host.util.AlbumTagUtil;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBackNew;
import com.ximalaya.ting.android.opensdk.model.album.Album;

import java.util.ArrayList;
import java.util.List;

public class VipAlbumAdapter extends HolderAdapter<List<AlbumM>> {

    private Context mContext;
    private int columnYu = 2;
    private IDataCallBackNew<AlbumM> albumIDataCallback;


    public VipAlbumAdapter(Context context, List<List<AlbumM>> listData, IDataCallBackNew<AlbumM> callback) {
        super(context, listData);
        mContext = context;
        this.albumIDataCallback = callback;
    }

    @Override
    public void onClick(View view, List<AlbumM> albums, int position, BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_vip_album_home_lite;
    }

    /**
     * 建立视图Holder
     */
    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, List<AlbumM> albums, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        if (CollectionUtil.isNullOrEmpty(albums)) {
            return;
        }
        try {
            viewHolder.vSelectHead.setVisibility(position == 0 ? View.VISIBLE : View.GONE);
            viewHolder.llSelectTip.setVisibility(position == 0 ? View.VISIBLE : View.GONE);
            viewHolder.vSelectBottom.setVisibility(position == listData.size() - 1 ? View.VISIBLE : View.GONE);
            if (albums.size() % columnYu == 0) {
                AlbumM album = albums.get(1);
                updateCover(viewHolder.ivCoverRight, album);
                updateTags(viewHolder.ivTagRight, album);
                updateTitle(viewHolder.tvTitleRight, album);
                MyOnClickListener listener = new MyOnClickListener(album);
                viewHolder.ivCoverRight.setOnClickListener(listener);
                viewHolder.freeListenerRightBtn.setVisibility(View.VISIBLE);
                viewHolder.freeListenerRightBtn.setOnClickListener(listener);
                viewHolder.freeListenerRightBtn.setBackgroundResource(R.drawable.main_bg_fffe5501_ffff762a_dp22);
                TraceFreeAlbumManager.INSTANCE.exposeVipAlbumListItem(album.getId());
            }
            AlbumM album = albums.get(0);
            updateCover(viewHolder.ivCover, album);
            updateTags(viewHolder.ivTag, album);
            updateTitle(viewHolder.tvTitle, album);
            MyOnClickListener listener = new MyOnClickListener(album);
            viewHolder.ivCover.setOnClickListener(listener);
            viewHolder.freeListenerBtn.setVisibility(View.VISIBLE);
            viewHolder.freeListenerBtn.setOnClickListener(listener);
            viewHolder.freeListenerBtn.setBackgroundResource(R.drawable.main_bg_fffe5501_ffff762a_dp22);
            TraceFreeAlbumManager.INSTANCE.exposeVipAlbumListItem(album.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class MyOnClickListener implements View.OnClickListener {
        private AlbumM album;

        MyOnClickListener(AlbumM album) {
            this.album = album;

        }

        @Override
        public void onClick(View view) {
            albumIDataCallback.onSuccess(album, 0, null);
        }
    }

    private void updateCover(ImageView ivCover, Album albumM) {
        if (ivCover != null) {
            ivCover.setVisibility(View.VISIBLE);
            ImageManager.from(mContext).displayImage(ivCover, albumM.getLargeCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album_145, com.ximalaya.ting.android.host.R.drawable.host_default_album_145);
        }
    }

    private void updateTags(ImageView ivTag, Album albumM) {
        if (albumM instanceof AlbumM) {
            if (AlbumTagUtil.getAlbumCoverTag((AlbumM) albumM) != -1) {
                ivTag.setImageResource(AlbumTagUtil.getAlbumCoverTag((AlbumM) albumM));
                ivTag.setVisibility(View.VISIBLE);
            } else {
                ivTag.setVisibility(View.INVISIBLE);
            }
        } else {
            ivTag.setVisibility(View.INVISIBLE);
        }

    }

    private void updateTitle(TextView tvTitle, Album albumM) {
        int textSize = (int) tvTitle.getTextSize();
        Spanned richTitle = getRichTitle(albumM, mContext, textSize);
        tvTitle.setText(richTitle);
    }

    @Nullable
    public static Spanned getRichTitle(Album data, Context context, int maxHeight) {
        if (!(data instanceof AlbumM)) {
            return null;
        }
        if (context == null) {
            return null;
        }
        AlbumM album = (AlbumM) data;
        boolean isCompleteTag = album.getSerialState() == 2 || album.isCompleted() || (album.getAttentionModel() != null && album.getAttentionModel().getSerialState() == 2);
        List<Integer> resList = new ArrayList<>();
        if (isCompleteTag) {
            // 完本标签
            resList.add(R.drawable.main_tag_complete_top_new);
        }
        String intro;
        if (resList.size() > 0) {
            intro = " " + data.getAlbumTitle();
        } else {
            intro = data.getAlbumTitle();
        }
        Spanned titleWithTag = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(context, intro, resList, resList.size(), maxHeight);
        return titleWithTag;
    }

    public static class ViewHolder extends BaseViewHolder {
        private ImageView ivCover;
        private ImageView ivCoverRight;
        private ImageView ivTag;
        private ImageView ivTagRight;
        private TextView tvTitle;
        private TextView tvTitleRight;
        private View vSelectHead;
        private View vSelectBottom;
        private LinearLayout llSelectTip;
        private LinearLayout freeListenerBtn;
        private LinearLayout freeListenerRightBtn;


        public ViewHolder(View convertView) {
            View vipAlbumItem = convertView.findViewById(R.id.vipAlbumItem);
            View vipAlbumItemRight = convertView.findViewById(R.id.vipAlbumItemRight);
            ivCover = vipAlbumItem.findViewById(R.id.main_iv_album_cover);
            ivTag = vipAlbumItem.findViewById(R.id.main_iv_space_album_tag);
            tvTitle = vipAlbumItem.findViewById(R.id.main_tv_album_title);
            freeListenerBtn = vipAlbumItem.findViewById(R.id.ll_free_listener);
            ivCoverRight = vipAlbumItemRight.findViewById(R.id.main_iv_album_cover);
            ivTagRight = vipAlbumItemRight.findViewById(R.id.main_iv_space_album_tag);
            tvTitleRight = vipAlbumItemRight.findViewById(R.id.main_tv_album_title);
            freeListenerRightBtn = vipAlbumItemRight.findViewById(R.id.ll_free_listener);
            vSelectHead = convertView.findViewById(R.id.vSelectHead);
            llSelectTip = convertView.findViewById(R.id.llSelectTip);
            vSelectBottom = convertView.findViewById(R.id.vSelectBottom);
        }
    }
}
