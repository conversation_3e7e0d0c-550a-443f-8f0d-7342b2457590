package com.ximalaya.ting.lite.main.login;


import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.Toast;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.fixtoast.ToastCompat;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.framework.view.SlideView.IOnFinishListener;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.host.activity.SmsLoginDialogActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.feedback.CustomerFeedBackManager;
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.login.LoginPageTraceManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.loginservice.BaseResponse;
import com.ximalaya.ting.android.loginservice.IHandleRequestCode;
import com.ximalaya.ting.android.loginservice.ILoginResultCode;
import com.ximalaya.ting.android.loginservice.LoginRequest;
import com.ximalaya.ting.android.loginservice.LoginService;
import com.ximalaya.ting.android.loginservice.XMLoginCallBack;
import com.ximalaya.ting.android.loginservice.XmLoginInfo;
import com.ximalaya.ting.android.loginservice.base.IDataCallBackUseLogin;
import com.ximalaya.ting.android.loginservice.base.ILogin;
import com.ximalaya.ting.android.loginservice.base.ILogin.LoginStrategies;
import com.ximalaya.ting.android.loginservice.base.IRequestData;
import com.ximalaya.ting.android.loginservice.base.LoginFailMsg;
import com.ximalaya.ting.android.loginservice.callback.IRequestCallBack;
import com.ximalaya.ting.lite.main.constant.PreferenceConstantsInMain;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.ref.SoftReference;
import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Nullable;

import static com.ximalaya.ting.android.loginservice.base.ILogin.LOGIN_FLAG_PHONE;

/**
 * <AUTHOR> on 2017/6/29.
 */
public class BaseLoginFragment extends BaseFragment2 {

    private static final String TAG = BaseLoginFragment.class.getSimpleName();
    private LoginInfoModelNew loginInfoModel;
    private Activity activity;

    protected String mCountryCode = "86";
    protected Bundle loginParamsBundle; // 记录用户登录源传递的参数
    @LoginStrategies
    private int loginStrategy;

    private String name, passWd, countryCode;

    public BaseLoginFragment() {
    }

    @SuppressLint("ValidFragment")
    @SuppressWarnings("NullAway")
    public BaseLoginFragment(boolean canSiled, IOnFinishListener onFinishListener) {
        super(canSiled, onFinishListener);
    }

    @Override
    protected String getPageLogicName() {
        return "baseLogin";
    }

    @Nullable
    private LoginService getLoginService() {
        return LoginService.getInstance();
    }

    private void initCallback() {
        xmLoginCallBack = new XMLoginCallBack() {
            @Override
            public void onXMLoginSuccess(LoginInfoModelNew loginInfoModel, XmLoginInfo xmLoginInfo) {

                dismissLoginProgress();
                //登录成功后处理
                handleLoginSuccess(loginInfoModel);

                LoginPageTraceManager.traceLoginResult(false, LoginBundleParamsManager.getLoginBy(getArguments()) == LoginByConstants.LOGIN_BY_FULL_SCREEN, "登录成功");
            }

            @Override
            public void onLoginBegin() {
                showLoginProgress(getmActivity());
            }

            @Override
            public void onLoginSuccess(XmLoginInfo xmLoginInfo) {
            }

            @Override
            public void onLoginFailed(LoginFailMsg msg) {
                dismissLoginProgress();

                handleLoginFail(msg);

                if (msg != null) {
                    LoginPageTraceManager.traceLoginResult(false, LoginBundleParamsManager.getLoginBy(getArguments()) == LoginByConstants.LOGIN_BY_FULL_SCREEN, msg.getErrorCode() + "");
                } else {
                    LoginPageTraceManager.traceLoginResult(false, LoginBundleParamsManager.getLoginBy(getArguments()) == LoginByConstants.LOGIN_BY_FULL_SCREEN, "-1");
                }
            }
        };
    }

    private MyProgressDialog loginProgressDialog;

    public void showLoginProgress(Activity activity) {
        if (activity == null || activity.isFinishing())
            return;
        if (loginProgressDialog == null) {
            loginProgressDialog = new MyProgressDialog(activity);
        } else {
            loginProgressDialog.cancel();
        }
        loginProgressDialog.setTitle("登录");
        loginProgressDialog.setMessage("正在登录...");
        loginProgressDialog.show();
    }

    public void dismissLoginProgress() {
        if (loginProgressDialog != null) {
            loginProgressDialog.dismiss();
            loginProgressDialog = null;
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    public interface ILoginStatueCallbackAndFailMsg extends ILoginStatueCallBack {
        void onFail(LoginFailMsg failMsg);
    }

    public interface ILoginStatueCallBack {
        void onSuccess();

        void onFail();
    }

    private static String getCountryCodePhoneNum(String countryCode, String phoneNum) {
        // 非国内手机号 需要加上国家识别码
        return TextUtils.equals("86", countryCode) ? phoneNum : countryCode + "-" + phoneNum;
    }

    protected void doLoginWithXMLY(String name, String passwd, String countryCode, @Nullable ILoginStatueCallBack loginStatueCallBack) {
        this.loginParamsBundle = getArguments();
        this.loginStrategy = ILogin.LOGIN_FLAG_XIMALAYA;
        this.name = name;
        this.passWd = passwd;
        this.countryCode = countryCode;
        SharedPreferencesUtil.getInstance(getmActivity()).saveBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_LOGIN_FROM_XMLY, true);
        if (!TextUtils.isEmpty(countryCode)) {
            SharedPreferencesUtil.getInstance(getmActivity().getApplicationContext())
                    .saveString(PreferenceConstantsInMain.TINGMAIN_KEY_SHARED_PRE_COUNTRY_CODE, countryCode);
            name = getCountryCodePhoneNum(countryCode, name);
        }

        Map<String, String> map = new HashMap<>();
        map.put("account", name);
        map.put("password", passwd);
        map.put("deviceToken", DeviceUtil.getDeviceToken(BaseApplication.getTopActivity()));
        map.put("rememberMe", "true");
        map.put("device", "android");
        String xum = DeviceUtil.getLocalMacAddress(getmActivity());
        if (!TextUtils.isEmpty(xum)) {
            map.put("xum", xum);
        }

        if (getLoginService() != null) {
            getLoginService().loginWithPswd(getActivity(), name, passwd, new
                    XMLoginCallBackWrapper(xmLoginCallBack, loginStatueCallBack));
        }
    }

    private class XMLoginCallBackWrapper extends XMLoginCallBack {
        private XMLoginCallBack mLoginCallBack;
        private ILoginStatueCallBack mLoginStatueCallBack;

        public XMLoginCallBackWrapper(XMLoginCallBack loginCallBack, ILoginStatueCallBack loginStatueCallBack) {
            mLoginCallBack = loginCallBack;
            mLoginStatueCallBack = loginStatueCallBack;
        }

        @Override
        public void onXMLoginSuccess(LoginInfoModelNew loginInfoModel, XmLoginInfo xmLoginInfo) {
            if (mLoginCallBack != null) {
                mLoginCallBack.onXMLoginSuccess(loginInfoModel, xmLoginInfo);
            }
            if (mLoginStatueCallBack != null) {
                mLoginStatueCallBack.onSuccess();
            }
        }

        @Override
        public void onLoginBegin() {
            if (mLoginCallBack != null) {
                mLoginCallBack.onLoginBegin();
            }
        }

        @Override
        public void onLoginSuccess(XmLoginInfo xmLoginInfo) {

        }

        @Override
        public void onLoginFailed(LoginFailMsg msg) {
            if (mLoginCallBack != null) {
                mLoginCallBack.onLoginFailed(msg);
            }
            if (mLoginStatueCallBack instanceof ILoginStatueCallbackAndFailMsg) {
                ((ILoginStatueCallbackAndFailMsg) mLoginStatueCallBack).onFail(msg);
            } else {
                if (mLoginStatueCallBack != null) {
                    mLoginStatueCallBack.onFail();
                }
            }

        }
    }

    /**
     * 免密登陆
     *
     * @param name
     * @param checkCode
     */
    protected void doLoginWithoutPwd(String name, String checkCode) {
        doLoginWithoutPwd(name, checkCode, "");
    }

    /**
     * 免密登陆
     *
     * @param name
     * @param checkCode
     */
    protected void doLoginWithoutPwd(String name, String checkCode, String countryCode) {
        this.loginParamsBundle = getArguments();
        this.loginStrategy = LOGIN_FLAG_PHONE;

        this.name = name;
        this.passWd = checkCode;
        this.countryCode = countryCode;

        SharedPreferencesUtil.getInstance(getmActivity()).saveBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_LOGIN_FROM_XMLY, false);
        if (!TextUtils.isEmpty(countryCode)) {
            SharedPreferencesUtil.getInstance(getmActivity().getApplicationContext())
                    .saveString(PreferenceConstantsInMain.TINGMAIN_KEY_SHARED_PRE_COUNTRY_CODE, countryCode);
            name = getCountryCodePhoneNum(countryCode, name);
        }
        if (getLoginService() != null) {
            getLoginService().loginQuick(getActivity(), name, checkCode, xmLoginCallBack);
        }
    }


    XMLoginCallBack xmLoginCallBack;


    private void handleLoginSuccess(LoginInfoModelNew loginInfoModel) {
        handleLoginSuccess(loginInfoModel, false);
    }

    protected void handleLoginSuccess(LoginInfoModelNew loginInfoModel, boolean showSuccessToast) {
        requestBack(loginInfoModel, showSuccessToast);
        if (loginInfoModel == null) { // 如果登录成功，但是返回数据异常，则拦击之后的操作；
            return;
        }
        String srcPage = "引导用户登录全屏";
        if (mActivity instanceof SmsLoginDialogActivity) {
            srcPage = "引导用户登录半屏";
        }
        new UserTracking().setItem("user")
                .setItemId(loginInfoModel.getUid())
                .setThirdParty(loginInfoModel.getLoginType())
                .setThirdPartyId(loginStrategy + "")
                .setSrcPage(srcPage)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, "login");
    }

    /**
     * 登录失败后的信息封装
     */
    private void handleLoginFail(LoginFailMsg msg) {
        LoginInfoModelNew mLoginInfo = new LoginInfoModelNew();
        mLoginInfo.setRet(msg.getErrorCode());
        mLoginInfo.setMsg("" + msg.getErrorMsg());
        requestBack(mLoginInfo, true);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return 0;

    }


    @Override
    protected boolean onPrepareNoContentView() {
        return false;
    }

    @Override
    protected void onNoContentButtonClick(View view) {

    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        this.activity = activity;
        if (xmLoginCallBack == null) {
            initCallback();
        }

        LoginRequest.setHandleRequestCode(new WeakReference<IHandleRequestCode>(mHandleRequestCode));
    }

    private IHandleRequestCode mHandleRequestCode = new IHandleRequestCode() {
        @Override
        public void noSetPswd() {

        }

        @Override
        public void accountFroze(String msg, long l) {
            new DialogBuilder(getActivity())
                    .setTitleVisibility(false)
                    .setMessage(TextUtils.isEmpty(msg) ? "很抱歉，您的账号存在违规操作，已被冻结，请联系客服解封" : msg)
                    .setMsgGravity(Gravity.CENTER)
                    .setCancelBtn("知道了")
                    .setOkBtn("去解封", new DialogBuilder.DialogCallback() {
                        @Override
                        public void onExecute() {
                            CustomerFeedBackManager.jumpToXmCustomerFeedBack();
                        }
                    })
                    .showConfirm();
        }


        @Override
        public void noBindPhone(LoginInfoModelNew result) {
//            gotoBind(result);
        }

        @Override
        public void gotoVerficate(LoginInfoModelNew result) {
            //极速版本身是手机号登录，不需要再次手机号验证
//            goto2Verficate(result);
        }

        @Override
        public void alreadyBinded(final LoginInfoModelNew result, final IRequestData requestData, final String url,
                                  final Map<String, String> specificParams,
                                  final IDataCallBackUseLogin callBack, final IRequestCallBack successCallBack,
                                  final String requestType) {
            String message = "该手机已绑定,是否换绑";
            if (result != null && !TextUtils.isEmpty(result.getMsg())) {
                message = result.getMsg();
            }

            DialogBuilder dialogBuilder = new DialogBuilder(getmActivity()).setMessage(message).setCancelBtn("否",
                    new DialogBuilder.DialogCallback() {
                        @Override
                        public void onExecute() {

                        }
                    }).setOkBtn("是", new DialogBuilder.DialogCallback() {
                @Override
                public void onExecute() {
                    specificParams.put("forceBind", "true");

                    if (result != null) {
                        specificParams.put("bizKey", result.getBizKey());
                        specificParams.put("smsKey", result.getSmsKey());
                    }
                    LoginRequest.bindPhone(requestData, specificParams, callBack);
                }
            });
            dialogBuilder.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    dismissLoginProgress();
                }
            });
            dialogBuilder.showConfirm();
        }

        @Override
        public void resetPsw(LoginInfoModelNew result) {
            String message = "账户存在风险，请修改密码";
            if (result != null && !TextUtils.isEmpty(result.getMsg())) {
                message = result.getMsg();
            }

            DialogBuilder dialogBuilder = new DialogBuilder(getmActivity()).setMessage(message).setCancelBtn("取消",
                    new DialogBuilder.DialogCallback() {
                        @Override
                        public void onExecute() {

                        }
                    }).setOkBtn("去修改", new DialogBuilder.DialogCallback() {
                @Override
                public void onExecute() {
                    String url = "iting://open?msg_type=94&bundle=account&pageName=forgetPassword";
                    Intent intent = new Intent(Intent.ACTION_VIEW);
                    intent.setData(Uri.parse(url));
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(intent);
                }
            });
            dialogBuilder.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    dismissLoginProgress();
                }
            });
            dialogBuilder.showConfirm();
        }
    };

    public Activity getmActivity() {
        return activity;
    }

    private void requestBack(LoginInfoModelNew result, boolean showSuccessToast) {
        this.loginInfoModel = result;
        if (getmActivity() == null || getmActivity().isFinishing()) {
            return;
        }
        if (result != null) {
            if (result.getRet() == 0) {
                ToolUtil.removeLastBindPhoneInfo();
                UserInfoMannage.getInstance().setUser(result);
                if (showSuccessToast) {
                    showSuccessToast("登录成功");
                }
                if (mActivity != null) {
                    mActivity.finish();
                }
            } else if (ILoginResultCode.ACCOUNT_FROZE == result.getRet()) {
                //账号被封
                Logger.e("login", result.getMsg());
                new DialogBuilder(getActivity())
                        .setTitleVisibility(false)
                        .setMessage(TextUtils.isEmpty(result.getMsg()) ? "很抱歉，您的账号存在违规操作，已被冻结，请联系客服解封" : result.getMsg())
                        .setMsgGravity(Gravity.CENTER)
                        .setCancelBtn("知道了")
                        .setOkBtn("去解封", new DialogBuilder.DialogCallback() {
                            @Override
                            public void onExecute() {
                                CustomerFeedBackManager.jumpToXmCustomerFeedBack();
                            }
                        })
                        .showConfirm();
            } else {
                Logger.e("login", result.getMsg());
                // 如果是在登录页并且是手机登录不弹出该toast,因为会再外面弹出dialog提示用户使用验证码登录
                if (!(getClass() == LoginFragment.class && loginStrategy == ILogin.LOGIN_FLAG_XIMALAYA && !TextUtils.isEmpty(countryCode))) {
                    showFailToast(TextUtils.isEmpty(result.getMsg()) ? "请稍后再试……" : result.getMsg());
                }
            }
        } else {
            showFailToast("网络超时，请稍候再试！");
        }
    }

    public void startFragment(FragmentActivity activity, Fragment fragment) {
        if (activity == null || activity.isFinishing())
            return;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            if (activity.isDestroyed())
                return;
        }
        if (fragment == null) return;
        FragmentTransaction ft = activity.getSupportFragmentManager().beginTransaction();
        ft.add(android.R.id.content, fragment);
        ft.addToBackStack(null);
        ft.commitAllowingStateLoss();
    }


    // 因为SmsLoginDialogActivity 里面弹出了一个Dialog ,所以里面显示toast的会被dialog背景覆盖
    protected void showFailToast(int toastId) {
        if (getActivity() instanceof SmsLoginDialogActivity) {
            CustomToast.showFailToast(getActivity().getString(toastId));
        } else {
            CustomToast.showFailToast(toastId);
        }
    }

    protected void showFailToast(String toast) {
        if (getActivity() instanceof SmsLoginDialogActivity) {
            try {
                ToastCompat.makeText(getActivity(), toast, Toast.LENGTH_SHORT).show();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            CustomToast.showFailToast(toast);
        }
    }

    public static void showFailToast(Activity activity, int stringId) {
        if (activity instanceof SmsLoginDialogActivity) {
            try {
                ToastCompat.makeText(activity, stringId, Toast.LENGTH_SHORT).show();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            CustomToast.showFailToast(stringId);
        }
    }

    public static void showFailToast(Activity activity, String string) {
        if (activity instanceof SmsLoginDialogActivity) {
            try {
                ToastCompat.makeText(activity, string, Toast.LENGTH_SHORT).show();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            CustomToast.showFailToast(string);
        }
    }

    protected void showSuccessToast(String string) {
        try {
            ToastManager.showToast(string);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private MyProgressDialog progressDialog;

    protected void getPhoneCheckCode(final String phoneNumber, final String countryCode, final SoftReference<? extends BaseLoginFragment> softReference, final IHandleOk onDataCallBack) {
        final BaseLoginFragment fragment = softReference.get();
        if (fragment == null)
            return;

        this.loginStrategy = LOGIN_FLAG_PHONE;
        this.name = phoneNumber;
        if (!TextUtils.isEmpty(countryCode)) {
            SharedPreferencesUtil.getInstance(getmActivity().getApplicationContext())
                    .saveString(PreferenceConstantsInMain.TINGMAIN_KEY_SHARED_PRE_COUNTRY_CODE, countryCode);
        }

        if (fragment.progressDialog == null) {
            fragment.progressDialog = new MyProgressDialog(fragment.getActivity());
        } else {
            fragment.progressDialog.cancel();
        }
        fragment.progressDialog.setMessage("请稍候");
        fragment.progressDialog.show();

        sendSms(phoneNumber, countryCode, onDataCallBack, fragment);
    }


    private void sendSms(final String phoneNumber, final String countryCode, final IHandleOk onDataCallBack, final BaseLoginFragment fragment) {

        Map<String, String> maps = new HashMap<>();
        maps.put("mobile", getCountryCodePhoneNum(countryCode, phoneNumber));
        maps.put("sendType", "1");

        //极速版只支持验证码登录，此处设置为sms
        int sendSmsType = LoginRequest.SEND_SMS_TYPE_FOR_LOGIN;

        LoginRequest.sendSms(getActivity(), sendSmsType, LoginService.getInstance().getRquestData(), maps,
                new IDataCallBackUseLogin<BaseResponse>() {
                    @Override
                    public void onSuccess(@androidx.annotation.Nullable BaseResponse object) {
                        if (fragment.progressDialog != null)
                            fragment.progressDialog.cancel();

                        if (fragment.canUpdateUi() && object != null) {
                            // 点击登录到验证码接收页面
                            if (onDataCallBack != null) {
                                onDataCallBack.onReady();
                            }
                        }
                        if (object == null) {
                            CustomToast.showFailToast("数据发生异常，请稍后重试");
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (fragment.progressDialog != null)
                            fragment.progressDialog.cancel();

                        if (fragment.canUpdateUi()) {
                            ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
                        }
                    }
                });
    }

    public void setLoginStrategy(int loginStrategy) {
        this.loginStrategy = loginStrategy;
    }
}
