package com.ximalaya.ting.lite.main.manager

import android.annotation.SuppressLint
import android.graphics.Color
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt
import com.ximalaya.ting.android.host.manager.LiteEncryptManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.ContextUtils
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.SpannableStringUtils
import com.ximalaya.ting.android.host.util.constant.MMKV_SIGN_CLOSE_DATE
import com.ximalaya.ting.android.host.util.constant.MMKV_SIGN_COMPLETE_DATE
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.model.CheckInConfigModel
import com.ximalaya.ting.lite.main.model.CheckInDataModel
import com.ximalaya.ting.lite.main.model.CheckInDetails
import org.json.JSONObject


class SignManager {

    private val mTAG = "SignManager"

    private var mSignView: View? = null
    private var mSignParentLayout: ViewGroup? = null

    // 正在请求签到
    private var mIsRequestSign = false

    // 正在请求签到数据
    private var mIsRequestSignData = false

    private val mLoginListener = object : ILoginStatusChangeListener {
        override fun onLogout(olderUser: LoginInfoModelNew?) {
            removeSignView()
        }

        override fun onLogin(model: LoginInfoModelNew?) {
            checkSignView(mSignParentLayout)
        }

        override fun onUserChange(oldModel: LoginInfoModelNew?, newModel: LoginInfoModelNew?) {

        }
    }

    fun checkSignView(signParentLayout: ViewGroup?) {
        if (signParentLayout == null) {
            FuliLogger.log(mTAG, "signParentLayout为空,不处理")
            return
        }

        mSignParentLayout = signParentLayout
        // 不管是否登录  都需要监听登录状态  要处理切换账户的情况
        UserInfoMannage.getInstance().addLoginStatusChangeListener(mLoginListener)
        // 未登录不往下走
        if (!UserInfoMannage.hasLogined()) {
            return
        }

        val context = BaseApplication.getMyApplicationContext()
        val curDate = DateTimeUtil.getCurrentDate4yyyyMMdd()
        val closeDate = MmkvCommonUtil.getInstance(context).getString(MMKV_SIGN_CLOSE_DATE)
        // 签到完成和用户绑定
        val completeDate = MmkvCommonUtil.getInstance(context).getString(MMKV_SIGN_COMPLETE_DATE + UserInfoMannage.getUid())
        if (curDate == closeDate || curDate == completeDate) {
            FuliLogger.log(mTAG, "已主动关闭或者完成签到不处理 curDate:$curDate closeDate:$closeDate completeDate:$completeDate")
            removeSignView()
            return
        }

        if (mIsRequestSignData) {
            FuliLogger.log(mTAG, "签到数据请求中,本次不处理")
            return
        }
        mIsRequestSignData = true

        try {
            requestCheckInConfig(object : IRequestCallback<CheckInConfigModel> {
                override fun onResult(t: CheckInConfigModel) {
                    if (t.activitySwitch) {
                        requestCheckInData(object : IRequestCallback<CheckInDataModel> {
                            override fun onResult(t: CheckInDataModel) {
                                if (ContextUtils.checkActivity(mSignParentLayout?.context)) {
                                    addCheckInView(t)
                                }
                                mIsRequestSignData = false
                            }

                            override fun onEnd() {
                                mIsRequestSignData = false
                            }
                        })
                    } else {
                        FuliLogger.log(mTAG, "签到开关关闭")
                        removeSignView()
                    }
                }

                override fun onEnd() {
                    mIsRequestSignData = false
                }
            })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun removeSignView() {
        if (ContextUtils.checkActivity(mSignView?.context)) {
            if (mSignView != null) {
                mSignParentLayout?.removeAllViews()
                mSignView = null
            }
        }
    }

    private fun addCheckInView(model: CheckInDataModel) {
        var startIndex: Int
        model.checkInDetails?.run {
            // thatDay 从1开始
            if (size < model.thatDay) {
                FuliLogger.log(mTAG, "签到数据异常 连续签到天数大于总天数")
                return@run
            }

            if (get(model.thatDay - 1)?.checkInStatus ?: 0 >= 1) {
                FuliLogger.log(mTAG, "今日已签到,不显示签到")
                MmkvCommonUtil.getInstance(BaseApplication.mAppInstance).saveString(MMKV_SIGN_COMPLETE_DATE + UserInfoMannage.getUid(), DateTimeUtil.getCurrentDate4yyyyMMdd())
                removeSignView()
                return@run
            }

            val showData: List<CheckInDetails?>?
            // 签到天数为26天时显示24-30
            if (model.thatDay > 25) {
                if (size < 7) {
                    FuliLogger.log(mTAG, "签到数据异常 thatDay${model.thatDay}")
                    return@run
                }
                // 取最后七天
                startIndex = size - 7
                showData = subList(startIndex, size)
            } else {
                var index = model.thatDay / 7
                index += if (model.thatDay % 7 == 0) {
                    0
                } else {
                    1
                }

                // 处理特殊情况 导致数据不显示
                if (index == 0) {
                    index = 1
                }

                if (size < index * 7) {
                    FuliLogger.log(mTAG, "签到数据异常")
                    return@run
                }

                // 取周期第一天  index * 7代表周期最后一天
                startIndex = (index - 1) * 7
                showData = subList(startIndex, index * 7)
            }

            if (showData.isNotEmpty()) {
                val context = mSignParentLayout?.context
                // 因为每次界面可见时会刷新  所以只有第一次才添加
                if (mSignView == null) {
                    mSignView = LayoutInflater.from(context).inflate(R.layout.main_home_sign_module_layout, mSignParentLayout, false)
                }

                val clSign = mSignView?.findViewById<ConstraintLayout>(R.id.main_cl_sign)
                val clSignResult = mSignView?.findViewById<ConstraintLayout>(R.id.main_cl_sign_result)
                val ivGift = mSignView?.findViewById<ImageView>(R.id.main_iv_gift)
                val mRecycleView = mSignView?.findViewById<RecyclerView>(R.id.main_rv_sign)
                val tvTitle = mSignView?.findViewById<TextView>(R.id.main_tv_title_result)
                val tvSubTitle = mSignView?.findViewById<TextView>(R.id.main_tv_sub_title_result)

                // 设置签到奖励数据  -- start
                val remindText = "${model.checkInDetails[model.thatDay - 1]?.checkInAward} 金币"
                var text = "获得 $remindText"
                val start = text.indexOf(remindText)
                val spannableString = SpannableString(text)
                val color = Color.parseColor("#FF6110")
                spannableString.setSpan(ForegroundColorSpan(color), start, start + remindText.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                tvTitle?.text = spannableString

                var nextIndex = model.thatDay
                if (nextIndex >= model.checkInDetails.size) {
                    nextIndex = 0
                }

                text = "明日可领 ${model.checkInDetails[nextIndex]?.checkInAward.toString()} 金币"
                tvSubTitle?.text = SpannableStringUtils.transformForAddColor(text, color, "\\d+")
                // 设置签到奖励数据  -- end

                mSignView?.findViewById<ImageView>(R.id.main_iv_close)?.setOnClickListener {
                    // 保存关闭时间
                    MmkvCommonUtil.getInstance(context).saveString(MMKV_SIGN_CLOSE_DATE, DateTimeUtil.getCurrentDate4yyyyMMdd())
                    removeSignView()

                    // 新首页-签到模块-关闭  点击事件
                    XMTraceApi.Trace()
                        .click(49214) // 用户点击时上报
                        .put("currPage", "homePageV2")
                        .createTrace()
                }

                mSignView?.findViewById<TextView>(R.id.main_tv_more)?.setOnClickListener {
                    val activity = BaseApplication.getMainActivity()
                    if (activity is MainActivity) {
                        activity.switchWelfareTab(null)
                    }

                    // 新首页-签到模块-赚更多钱  点击事件
                    XMTraceApi.Trace()
                        .click(49215) // 用户点击时上报
                        .put("currPage", "homePageV2")
                        .createTrace()
                }

                // 按照效果图等比缩放
                val scale = 124 / 374f
                val params = clSign?.layoutParams
                params?.apply {
                    params.width = (BaseUtil.getScreenWidth(context) * 370 / 375f).toInt()
                    params.height = (params.width * scale).toInt()
                    clSign.layoutParams = params
                }

                val params1 = clSignResult?.layoutParams
                params1?.width = params?.width
                params1?.height = params?.height
                clSignResult?.layoutParams = params1

                mRecycleView?.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                mRecycleView?.isNestedScrollingEnabled = false //禁止滑动
                // startNum = startIndex + 1  startIndex 从0开始  startNum 从1开始
                mRecycleView?.adapter = SignAdapter(showData, startIndex + 1, model.thatDay, this@SignManager)

                if (mSignParentLayout?.indexOfChild(mSignView) ?: 0 < 0) {
                    FuliLogger.log(mTAG, "添加签到视图")
                    clSign?.visibility = View.VISIBLE
                    clSignResult?.visibility = View.GONE
                    mSignParentLayout?.removeAllViews()
                    mSignParentLayout?.addView(mSignView)
                }

                // 新首页-签到模块  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(49211)
                    .setServiceId("slipPage")
                    .put("currPage", "homePageV2")
                    .createTrace()

                // 新首页-签到模块-领取  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(49213)
                    .setServiceId("slipPage")
                    .put("signDays", "${model.thatDay}")
                    .put("currPage", "homePageV2")
                    .createTrace()

            } else {
                FuliLogger.log(mTAG, "签到数据处理后为空,不显示")
                removeSignView()
            }
        } ?: removeSignView()
    }

    fun onDestroy() {
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(mLoginListener)
    }

    private fun performCheckIn(thatDay: Int) {
        MainActionRouter.getInstanse()?.functionAction?.onCheckedChangedJumpFuLiFragmentPage()
        // 新首页-签到模块-领取  点击事件
        XMTraceApi.Trace()
            .click(49212)
            .put("signDays", "$thatDay")
            .put("currPage", "homePageV2")
            .createTrace()


    }

    /**
     * 签到配置 总开关
     */
    private fun requestCheckInConfig(callBack: IRequestCallback<CheckInConfigModel>?) {
        val url = UrlConstants.getInstanse().mNetAddressHost + "speed/web-earn/check-in/config"
        val map = mutableMapOf<String, String?>()

        CommonRequestM.baseGetRequest(url, map, object : IDataCallBack<CheckInConfigModel> {
            override fun onSuccess(model: CheckInConfigModel?) {
                FuliLogger.log(mTAG, "签到配置获取成功:$model")
                if (model != null) {
                    callBack?.onResult(model)
                } else {
                    callBack?.onEnd()
                }
            }

            override fun onError(code: Int, message: String?) {
                FuliLogger.log(mTAG, "签到配置获取失败 code:$code message:$message")
                callBack?.onEnd()
            }

        }) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("code", -1)
            if (ret == 0) {
                JsonUtilKt.instance.toObjectOfType<CheckInConfigModel>(json.optString("data"),
                    object : TypeToken<CheckInConfigModel>() {}.type)
            } else null
        }
    }

    /**
     * 签到数据
     */
    private fun requestCheckInData(callBack: IRequestCallback<CheckInDataModel>?) {
        val url = UrlConstants.getInstanse().mNetAddressHost + "speed/web-earn/check-in/record"
        val map = mutableMapOf<String, String?>()
        CommonRequestM.baseGetRequest(url, map, object : IDataCallBack<CheckInDataModel> {
            override fun onSuccess(model: CheckInDataModel?) {
                FuliLogger.log(mTAG, "签到数据获取成功:$model")
                if (model != null) {
                    callBack?.onResult(model)
                } else {
                    callBack?.onEnd()
                }
            }

            override fun onError(code: Int, message: String?) {
                FuliLogger.log(mTAG, "签到数据获取失败 code:$code message:$message")
                callBack?.onEnd()
            }

        }) { content ->
            val json = JSONObject(content)
            // 因为这和是老接口  没有ret和code 使用thatDay判断
            val ret = json.optInt("thatDay", -1)
            if (ret != -1) {
                JsonUtilKt.instance.toObjectOfType<CheckInDataModel>(content,
                    object : TypeToken<CheckInDataModel>() {}.type)
            } else null
        }
    }

    private class SignAdapter(val list: List<CheckInDetails?>, val startNum: Int, val thatDay: Int, val mSignManager: SignManager?) : RecyclerView.Adapter<SignAdapter.Holder>() {

        private var itemWidth = 0
        private var dp5 = 0

        init {
            val context = BaseApplication.getMyApplicationContext()
            dp5 = BaseUtil.dp2px(context, 5f)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            itemWidth = (parent.measuredWidth - BaseUtil.dp2px(parent.context, 30f)) / 7
            return Holder(LayoutInflater.from(parent.context).inflate(R.layout.main_home_item_sign_layout, parent, false), itemWidth)
        }

        @SuppressLint("SetTextI18n")
        override fun onBindViewHolder(holder: Holder, position: Int) {
            val model = list[position]
            model?.run {
                // 已签到
                if (checkInStatus > 0) {
                    holder.tvCoinNum?.setTextColor(Color.parseColor("#FF4444"))
                    holder.tvCoinNum?.alpha = 0.5f

                    holder.ivCoin?.setImageResource(R.drawable.main_ic_sign_coin)
                    holder.ivCoin?.alpha = 0.5f

                    holder.tvSignNum?.text = "${startNum + position}天"
                    holder.tvSignNum?.setTextColor(Color.parseColor("#999999"))
                    holder.tvSignNum?.alpha = 0.5f
                    holder.tvSignNum?.background = null

                } else {
                    when {
                        startNum + position == thatDay -> {
                            // 当天 待签到
                            holder.tvCoinNum?.setTextColor(Color.parseColor("#FF4444"))
                            holder.tvCoinNum?.alpha = 1f

                            holder.ivCoin?.setImageResource(R.drawable.main_ic_sign_coin)
                            holder.ivCoin?.alpha = 1f

                            holder.tvSignNum?.text = "领取"
                            holder.tvSignNum?.setTextColor(Color.parseColor("#F5F6F7"))
                            holder.tvSignNum?.alpha = 1f
                            holder.tvSignNum?.setBackgroundResource(R.drawable.main_bg_sign_module_sign_shape)

                        }
                        startNum + position < thatDay -> {
                            // 漏签 显示未签到
                            holder.tvCoinNum?.setTextColor(Color.parseColor("#999999"))
                            holder.tvCoinNum?.alpha = 1f

                            holder.ivCoin?.setImageResource(R.drawable.main_ic_not_sign_coin)
                            holder.ivCoin?.alpha = 1f

                            holder.tvSignNum?.text = "未签到"
                            holder.tvSignNum?.setTextColor(Color.parseColor("#999999"))
                            holder.tvSignNum?.alpha = 1f
                            holder.tvSignNum?.background = null
                        }
                        else -> {
                            // 今天之后的未签到
                            holder.tvCoinNum?.setTextColor(Color.parseColor("#FF4444"))
                            holder.tvCoinNum?.alpha = 1f

                            holder.ivCoin?.setImageResource(R.drawable.main_ic_sign_coin)
                            holder.ivCoin?.alpha = 1f

                            // 当天之后显示天数
                            holder.tvSignNum?.text = "${startNum + position}天"
                            holder.tvSignNum?.setTextColor(Color.parseColor("#999999"))
                            holder.tvSignNum?.alpha = 1f
                            holder.tvSignNum?.background = null
                        }
                    }
                }

                holder.tvCoinNum?.text = "+$checkInAward"

                holder.itemView.setOnClickListener {
                    // 触发签到
                    if (startNum + position == thatDay && checkInStatus <= 0) {
                        mSignManager?.performCheckIn(thatDay)
                    }
                }

                val params: RecyclerView.LayoutParams? = holder.rootView?.layoutParams as RecyclerView.LayoutParams?
                if (position == itemCount - 1) {
                    params?.rightMargin = 0
                } else {
                    params?.rightMargin = dp5
                }
                holder.rootView?.layoutParams = params
            }

        }

        override fun getItemCount(): Int {
            return list.size
        }

        class Holder(itemView: View, itemWidth: Int) : RecyclerView.ViewHolder(itemView) {
            val rootView: ViewGroup? = itemView.findViewById(R.id.main_cl_item)
            val tvCoinNum: TextView? = itemView.findViewById(R.id.main_tv_coin_num)
            val ivCoin: ImageView? = itemView.findViewById(R.id.main_iv_coin)
            val tvSignNum: TextView? = itemView.findViewById(R.id.main_tv_sign_num)

            init {
                val params = itemView.layoutParams
                params.width = itemWidth
                itemView.layoutParams = params
            }
        }
    }

    interface IRequestCallback<T> {
        fun onResult(t: T)

        fun onEnd()
    }
}