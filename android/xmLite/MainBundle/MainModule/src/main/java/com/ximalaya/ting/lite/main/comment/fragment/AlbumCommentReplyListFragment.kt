package com.ximalaya.ting.lite.main.comment.fragment

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.RelativeLayout
import android.widget.TextView
import com.handmark.pulltorefresh.library.PullToRefreshBase
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.model.dialog.BaseDialogModel
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.view.EmotionUtil2
import com.ximalaya.ting.android.host.view.BaseBottomDialog
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.comment.*
import com.ximalaya.ting.lite.main.comment.adapter.AlbumCommentReplyListAdapter
import com.ximalaya.ting.lite.main.comment.adapter.CommentListAdapter
import com.ximalaya.ting.lite.main.comment.entities.CommentListItemBean
import com.ximalaya.ting.lite.main.comment.view.CommentEditPreviewTextView
import com.ximalaya.ting.lite.main.comment.view.CommentItemView
import com.ximalaya.ting.lite.main.comment.view.UserEditCommentDialog
import java.util.*


/**
 *  @Author: Junxiang Cheng
 *  @Mail: <EMAIL>
 *  @CreateTime: 12/24/21
 *
 *  @Description: 评论回复列表页面
 */
class AlbumCommentReplyListFragment private constructor(
    var presenter: CommentListPresenter
) : BaseFragment2(AppConstants.isPageCanSlide, null),
    View.OnClickListener,
    ReplyListListener,
    IReplyCountListener,
    UserEditCommentDialog.OnTextConfirmListener,
    AlbumCommentReplyListAdapter.OnCommentItemClickListener {

    companion object {
        const val TAG = "CommentReplyListFragment"

        const val ARGS_CURRENT_PARENT_COMMENT_DATA = "ARGS_CURRENT_PARENT_COMMENT_DATA"

        fun getInstance(
            presenter: CommentListPresenter,
            bundle: Bundle?
        ): AlbumCommentReplyListFragment {
            val fragment = AlbumCommentReplyListFragment(presenter)
            fragment.arguments = bundle
            return fragment
        }
    }

    private var mLayoutInflater: LayoutInflater? = null

    private var mList: RefreshLoadMoreListView? = null
    private var mAdapter: AlbumCommentReplyListAdapter? = null

    private var mViewEditPreview: CommentEditPreviewTextView? = null
    private var mHeaderView: View? = null
    private var mEmptyView: RelativeLayout? = null
    private var mEmptyHeaderView: View? = null
    private var mTvTitle: TextView? = null

    private var mCurrentParentData: CommentListItemBean? = null
    private var mCurrentParentId = 0L

    private var mCurrentReplyTargetData: CommentListItemBean? = null
    private var mTraced = false


    override fun getPageLogicName() = "CommentReplyListFragment"
    override fun getContainerLayoutId() = R.layout.main_fra_comment_reply_list

    override fun initUi(savedInstanceState: Bundle?) {
        mLayoutInflater = LayoutInflater.from(context)
        initHeader()

        mEmptyView = findViewById(R.id.main_rl_empty_view)
        mTvTitle = findViewById(R.id.main_tv_ic_back)
        mList = findViewById(R.id.main_rv_reply_list)
        mViewEditPreview = findViewById(R.id.main_view_reply_preview_edit)

        val rootView: RelativeLayout = findViewById(R.id.main_root_view)
        (rootView.parent as ViewGroup).setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        mTvTitle?.setOnClickListener(this)
        mViewEditPreview?.setOnClickListener(this)
        mViewEditPreview?.setHint(getString(R.string.main_comment_preview_default_hint))

        presenter.replyListListener = this
        presenter.setReplyCountListener(this)
        presenter.replyList = ArrayList()

        mList?.apply {
            isSendScrollListener = false

            mode = PullToRefreshBase.Mode.DISABLED
            preLoadMoreItemCount = -1

            refreshableView.divider = null

            setOnRefreshLoadMoreListener(object : IRefreshLoadMoreListener {
                override fun onRefresh() {
                    presenter.requestAlbumReplyList(mCurrentParentId, refresh = true)
                }

                override fun onMore() {
                    presenter.requestAlbumReplyList(mCurrentParentId, refresh = false)
                }
            })

            mAdapter = AlbumCommentReplyListAdapter(
                AlbumCommentReplyListAdapter.TYPE_REPLY,
                context,
                mCurrentParentId,
                presenter,
                presenter.replyList,
                this@AlbumCommentReplyListFragment
            )
            refreshableView.addHeaderView(mHeaderView)
            setAdapter(mAdapter)
        }
    }

    private fun initHeader() {
        mHeaderView = mLayoutInflater?.inflate(R.layout.main_item_comment_replies_header_item, null)

        mEmptyHeaderView = mHeaderView?.findViewById<RelativeLayout>(R.id.main_rl_empty_view)

        val contentView =
            mHeaderView?.findViewById<CommentItemView>(R.id.main_view_parent_comment_item)
        contentView?.apply {
            setType(CommentItemView.TYPE_REPLY_LIST_ITEM)
            mCurrentParentData?.let { data ->
                val spannableStringBuilder = SpannableStringBuilder(
                    EmotionUtil2
                        .getInstance()
                        .convertEmotionText2Span(data.content)
                )
                tvCommentContent.text = spannableStringBuilder
                ImageManager.from(context).displayImage(
                    ivAvatar,
                    data.user?.avatar,
                    R.drawable.host_ic_avatar_default,
                    R.drawable.host_ic_avatar_default
                )

                //昵称
                tvNickname.text = data.user?.nickname
                //vip
                ivUserVip.visibility =
                    if (data.user?.isVip == true)
                        View.VISIBLE
                    else
                        View.GONE

                //更新时间
                val updateTime = data.createTime ?: 0
                tvCommentTime.text =
                    (if (updateTime > 0) TimeHelper.convertTimeNew(updateTime) else "")
                //点赞
                setShowLike(false)
                tvLikeCount.text = data.likeCount.toString()
                if (data.likeStatus) {
                    tvLikeCount.setTextColor(context.resources.getColor(R.color.host_color_ff6110))
                    ivCommentLike.progress = 1f
                } else {
                    tvLikeCount.setTextColor(context.resources.getColor(R.color.host_color_999999))
                    ivCommentLike.progress = 0f
                }
                if (data.score <= 0) {
                    ivRatingbar.visibility = View.GONE
                } else {
                    ivRatingbar.visibility = View.VISIBLE
                    ivRatingbar.progress = data.score.toInt()
                }
                if (TextUtils.isEmpty(data.region)) {
                    tvIpRegion.visibility = View.GONE
                } else {
                    tvIpRegion.text = "来自 ${data.region}"
                    tvIpRegion.visibility = View.VISIBLE
                }
                setOnClickListener {
                    startComment(null)
                }
            }
        }
    }

    fun confirmDeleteParentComment(data: CommentListItemBean) {

        presenter.deleteParentCommentInReplyPage(
            data,
            object : ICommentRequestListener {
                override fun onSuccess() {
                    finish()
                    CustomToast.showSuccessToast("评论删除成功")
                }

                override fun onError(message: String?) {
                    CustomToast.showFailToast("删除失败")
                }
            }
        )
    }

    fun checkEmpty() {
        if (CollectionUtil.isNullOrEmpty(presenter.replyList)) {
            mEmptyHeaderView?.visibility = View.VISIBLE
        } else {
            mEmptyHeaderView?.visibility = View.GONE
        }
    }

    private fun setReplyTotalCount(count: Long) {
        mTvTitle?.text = if (count == 0L) {
            "回复"
        } else {
            "回复（${StringUtil.getFriendlyNumStrEn(count)}）"
        }
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        val arguments = arguments
        arguments?.apply {
            mCurrentParentData = arguments.getParcelable(ARGS_CURRENT_PARENT_COMMENT_DATA)

            mCurrentParentId = mCurrentParentData?.commentId ?: 0
        }

        super.onActivityCreated(savedInstanceState)
        setOnFinishListener {
            val fragment = parentFragment
            if (fragment is CommentDetailDialogFragment) {
                fragment.returnCommentList()
            }
            finish()
            true
        }
    }

    override fun loadData() {
        presenter.requestAlbumReplyList(parentCommentId = mCurrentParentId, refresh = true)
        onPageLoadingCompleted(LoadCompleteType.LOADING)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.main_view_reply_preview_edit -> {
                startComment(null)
            }
            R.id.main_tv_ic_back -> {
                // 声音评论-子评论浮层-返回按钮  弹框控件点击

                // 声音评论-子评论浮层-返回按钮  弹框控件点击
                XMTraceApi.Trace()
                    .setMetaId(41458)
                    .setServiceId("dialogClick")
                    .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
                    .put("trackId", presenter.mCurTrack?.dataId.toString())
                    .createTrace()
                finish()
            }
        }
    }

    // 拉起评论输入
    override fun startComment(targetParentComment: CommentListItemBean?) {
        if (!canUpdateUi()) {
            return
        }
        // 声音评论-子评论浮层-子评论回复入口  弹框控件点击
        XMTraceApi.Trace()
            .setMetaId(if (targetParentComment == null) 41451 else 41452)
            .setServiceId("dialogClick")
            .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
            .put("trackId", presenter.mCurTrack?.dataId.toString())
            .createTrace()

        val hint = if (targetParentComment == null) {
            mCurrentReplyTargetData = mCurrentParentData
            "@${mCurrentParentData?.user?.nickname}："
        } else {
            mCurrentReplyTargetData = targetParentComment
            "@${targetParentComment.user?.nickname}："
        }

        val cachedText = mViewEditPreview?.getCachedText()

        val userEditCommentDialog = UserEditCommentDialog(mActivity, hint, cachedText)
        userEditCommentDialog.setOnTextConfirmListener(this)
        userEditCommentDialog.show()
    }

    override fun startReplyList(targetParentComment: CommentListItemBean) {
        return
    }

    override fun onLongClickDelete(position: Int, data: CommentListItemBean) {
        if (!canUpdateUi()) {
            return
        }
        val models: MutableList<BaseDialogModel> = ArrayList()
        models.add(BaseDialogModel(R.drawable.host_ic_comment_delete, "删除", 0))
        object : BaseBottomDialog(mActivity, models) {
            override fun onItemClick(parent: AdapterView<*>?, view: View, position: Int, id: Long) {
                dismiss()

                // 声音评论-子评论浮层-删除按钮  弹框控件点击
                XMTraceApi.Trace()
                    .setMetaId(41456)
                    .setServiceId("dialogClick")
                    .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
                    .put("trackId", presenter.mCurTrack?.dataId.toString())
                    .createTrace()

                presenter.deleteAlbumComment(
                    pageType = CommentListPresenter.FRAGMENT_PAGE_TYPE_REPLY,
                    data = data,
                    commentRequestListener = object : ICommentRequestListener {
                        override fun onSuccess() {
                            if (!canUpdateUi()) {
                                return
                            }
                            mAdapter?.notifyDataSetChanged()
                            checkEmpty()
                            CustomToast.showSuccessToast("评论删除成功")
                        }

                        override fun onError(message: String?) {
                            if (!canUpdateUi()) {
                                return
                            }
                            CustomToast.showFailToast("删除失败")
                        }
                    }
                )
            }
        }.show()
        // 声音评论-子评论浮层-删除按钮  控件曝光
        XMTraceApi.Trace()
            .setMetaId(41457)
            .setServiceId("slipPage")
            .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
            .put("trackId", presenter.mCurTrack?.dataId.toString())
            .createTrace()
    }

    override fun onConfirm(cs: CharSequence) {
        if (!canUpdateUi()) {
            return
        }
        var targetCommentId = mCurrentParentId
        val requestParams = HashMap<String, String>()
        if (mCurrentReplyTargetData != mCurrentParentData) {
            requestParams["parentReplyId"] = mCurrentReplyTargetData?.replyId.toString()
            requestParams["commentUid"] = mCurrentReplyTargetData?.commentUid.toString()
        } else {
            requestParams["commentUid"] = mCurrentReplyTargetData?.user?.uid.toString()
        }
        requestParams["albumId"] = "" + presenter.sourceId
        requestParams["commentId"] = targetCommentId.toString()
        requestParams["content"] = cs.toString()
        requestParams["syncType"] = "0"
        presenter.addAlbumComment(
            pageType = CommentListPresenter.FRAGMENT_PAGE_TYPE_REPLY,
            targetCommentId = targetCommentId,
            requestParams = requestParams,
            commentRequestListener = object : ICommentRequestListener {
                override fun onSuccess() {
                    mAdapter?.notifyDataSetChanged()
                    checkEmpty()
                    CustomToast.showSuccessToast("回复成功")
                }

                override fun onError(message: String?) {
                    CustomToast.showFailToast(message ?: "回复失败")
                }
            })
        mViewEditPreview?.setCachedText(null)
        mCurrentReplyTargetData = mCurrentParentData
    }

    override fun onCancel(cs: CharSequence?) {
        if (!canUpdateUi()) {
            return
        }
        mViewEditPreview?.setCachedText(cs)
        mCurrentReplyTargetData = mCurrentParentData
    }

    override fun onSuccessUpdateReplyList(
        data: List<CommentListItemBean>?,
        refresh: Boolean,
        hasMore: Boolean
    ) {
        if (!canUpdateUi()) {
            return
        }
        onPageLoadingCompleted(LoadCompleteType.OK)

        mAdapter?.notifyDataSetChanged()
        checkEmpty()

        if (!mTraced) {
            mTraced = true

            //状态：1-有评论；2-无评论；
            val status = if (CollectionUtil.isNullOrEmpty(data)) "2" else "1"
            // 声音评论-子评论浮层  弹框展示
            XMTraceApi.Trace()
                .setMetaId(41450)
                .setServiceId("dialogView")
                .put("status", status)
                .put("albumId", presenter.mCurTrack?.album?.albumId.toString())
                .put("trackId", presenter.mCurTrack?.dataId.toString())
                .createTrace()
        }

        if (refresh) {
            mList?.refreshableView?.smoothScrollToPosition(0)
            mList?.onRefreshComplete(hasMore)
        }
        mList?.hasMore = hasMore

    }

    override fun finish() {
        if (!canUpdateUi()) {
            return
        }
        val fragment = parentFragment
        if (fragment is CommentDetailDialogFragment) {
            fragment.returnCommentList()
        }
        super.finish()
    }

    override fun onRequestFailed() {
        if (!canUpdateUi()) {
            return
        }
        mAdapter?.notifyDataSetChanged()
        checkEmpty()
        mList?.onRefreshComplete(true)
        onPageLoadingCompleted(LoadCompleteType.OK)
    }

    override fun onReplyCountUpdated(replyCount: Long) {
        if (!canUpdateUi()) {
            return
        }
        setReplyTotalCount(replyCount)
    }
}