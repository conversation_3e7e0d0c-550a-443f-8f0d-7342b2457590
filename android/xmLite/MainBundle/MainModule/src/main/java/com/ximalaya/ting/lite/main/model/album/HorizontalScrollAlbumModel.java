package com.ximalaya.ting.lite.main.model.album;

import android.view.View;

/**
 * Created by du<PERSON><PERSON> on 2020/6/16
 * <p>
 * Desc:单行横向滑动的model
 */
public class HorizontalScrollAlbumModel {

    private MainAlbumMList mainAlbumMList;
    private View.OnClickListener moreClickListener;

    private int lastScrollPosition;
    private int lastScrollOffset;

    public HorizontalScrollAlbumModel(MainAlbumMList mainAlbumMList, View.OnClickListener moreClickListener) {
        this.mainAlbumMList = mainAlbumMList;
        this.moreClickListener = moreClickListener;
    }

    public MainAlbumMList getMainAlbumMList() {
        return mainAlbumMList;
    }

    public void setMainAlbumMList(MainAlbumMList mainAlbumMList) {
        this.mainAlbumMList = mainAlbumMList;
    }

    public View.OnClickListener getMoreClickListener() {
        return moreClickListener;
    }

    public void setMoreClickListener(View.OnClickListener moreClickListener) {
        this.moreClickListener = moreClickListener;
    }

    public int getLastScrollPosition() {
        return lastScrollPosition;
    }

    public void setLastScrollPosition(int lastScrollPosition) {
        this.lastScrollPosition = lastScrollPosition;
    }

    public int getLastScrollOffset() {
        return lastScrollOffset;
    }

    public void setLastScrollOffset(int lastScrollOffset) {
        this.lastScrollOffset = lastScrollOffset;
    }
}
