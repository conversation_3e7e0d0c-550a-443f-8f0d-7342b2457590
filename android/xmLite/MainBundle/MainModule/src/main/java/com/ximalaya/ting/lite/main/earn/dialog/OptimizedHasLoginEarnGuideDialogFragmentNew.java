package com.ximalaya.ting.lite.main.earn.dialog;

import android.content.DialogInterface;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.BaseFullScreenDialogFragment;
import com.ximalaya.ting.android.host.listener.IHomeDialogCanNextCallback;
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.earn.NewUserRewardDialogManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.earn.HasLoginEarnGuideDataModel;
import com.ximalaya.ting.android.host.model.user.NewUserReward;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.earn.dialog.adapter.OptimizedHasLoginEarnGuideRvAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 06/14
 * <p> 新人奖励
 */
public class OptimizedHasLoginEarnGuideDialogFragmentNew extends BaseFullScreenDialogFragment implements View.OnClickListener {

    private static final String ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL = "listen_earn_dialog_data_model";

    private static final String TAG = "newUserPage";

    private boolean mMaskIsShow = false; //解决fragment重复添加crash问题

    //是否跳转了登录页面，跳转了，首页弹屏之后的操作将终止
    private boolean mIsGoLoginPage = false;


    private IHomeDialogCanNextCallback mCanNextCallback;

    //奖励数据载体
    private HasLoginEarnGuideDataModel mDataModel;

    private ConstraintLayout rlRootContainer;

    private ImageView viewClose, ivNewUserPercent, ivTopNewUser;

    private TextView tvDrawCash;

    private Runnable mRunnable;
    public boolean isAutoOpenReward;
    private OptimizedHasLoginEarnGuideRvAdapter earnGuideRvAdapter;

    private RecyclerView mRecyclerViewNewUser;
    private boolean isOpenRewardStatus;


    public static Bundle newArgument(HasLoginEarnGuideDataModel dialogDataModel) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL, dialogDataModel);
        return bundle;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mDataModel = arguments.getParcelable(ARGUMENT_KEY_LISTEN_EARN_DIALOG_DATA_MODEL);
        }
        if (mDataModel == null) {
            mDataModel = new HasLoginEarnGuideDataModel();
        }
        View view = inflater.inflate(R.layout.main_fra_dialog_optimized_has_login_earn_guide_new, container, false);
        rlRootContainer = view.findViewById(R.id.main_rl_root_container);
        mRecyclerViewNewUser = view.findViewById(R.id.rcy_new_user);
        tvDrawCash = view.findViewById(R.id.tv_DrawCash);
        ivNewUserPercent = view.findViewById(R.id.iv_top_new_user_percent);
        ivTopNewUser = view.findViewById(R.id.iv_top_new_user);
        viewClose = view.findViewById(R.id.iv_top_new_user_close);
        tvDrawCash.setOnClickListener(this);
        viewClose.setOnClickListener(this);
        List<NewUserReward> newUserRewardList = getNewUserRewardConfigInfo();
        earnGuideRvAdapter = new OptimizedHasLoginEarnGuideRvAdapter(getActivity(), this, newUserRewardList, new NewUserOpenRewardCallBack() {
            @Override
            public void openRewardBack() {
                NewUserRewardDialogManager.INSTANCE.saveHasShowNewUserRewardDialog();
                isOpenRewardStatus = true;
                ivNewUserPercent.setVisibility(View.VISIBLE);
                viewClose.setVisibility(View.VISIBLE);
                tvDrawCash.setText("去领取");
                ivTopNewUser.setImageResource(R.drawable.host_icon_new_user_open_reward_bg);
            }

            @Override
            public void stopRunnableBack() {
                if (!isAutoOpenReward && mRunnable != null) {
                    HandlerManager.removeCallbacks(mRunnable);
                    mRunnable = null;
                }
            }
        });
        mRecyclerViewNewUser.addItemDecoration(new SpaceItemDecoration(
                BaseUtil.dp2px(getContext(), 6f), 0, BaseUtil.dp2px(getContext(), 6f), 0));
        mRecyclerViewNewUser.setNestedScrollingEnabled(false);
        mRecyclerViewNewUser.setLayoutManager(new GridLayoutManager(getActivity(), 3, GridLayoutManager.VERTICAL, false));
        mRecyclerViewNewUser.setItemViewCacheSize(newUserRewardList.size());
        mRecyclerViewNewUser.setAdapter(earnGuideRvAdapter);
        mRunnable = new Runnable() {
            @Override
            public void run() {
                if (!isAutoOpenReward) {
                    isAutoOpenReward = true;
                    tvDrawCash.callOnClick();
                }
            }
        };
        HandlerManager.postOnUIThreadDelay(mRunnable, 5000);
        return view;
    }

    @Override
    public void onPause() {
        super.onPause();
        if (earnGuideRvAdapter != null) {
            earnGuideRvAdapter.isOpenNewUserReward = false;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 新人礼包放送弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(54912)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put("status", isOpenRewardStatus ? "已开启" : "未开启") // 未开启、已开启
                .put("currPage", TAG)
                .createTrace();
    }

    private List<NewUserReward> getNewUserRewardConfigInfo() {
        String data = ConfigureCenter.getInstance().getString(CConstants.Group_Base.GROUP_NAME, CConstants.Group_Base.ITEM_NEWUSER_GIFT_TEXT, result);
        List<NewUserReward> newUserRewards = JsonUtilKt.getInstance().toList(data, new TypeToken<List<NewUserReward>>() {
        }.getType());
        if (newUserRewards == null) {
            newUserRewards = new ArrayList<>();
        }
        return newUserRewards;
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mMaskIsShow = false;
        //屏幕消失
        if (!mIsGoLoginPage) {
            //没有执行跳转登录操作，首页之后的操作可以接着进行
            if (mCanNextCallback != null) {
                mCanNextCallback.canShowNext();
            }
        }
        mIsGoLoginPage = false;
    }

    public boolean isShowing() {
        return mMaskIsShow;
    }

    @Override
    public int show(FragmentTransaction transaction, String tag) {
        if (mMaskIsShow) {
            return 0;
        }
        mMaskIsShow = true;
        return super.show(transaction, tag);
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        if (mMaskIsShow) {
            return;
        }
        mMaskIsShow = true;
        super.show(manager, tag);
    }


    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.iv_top_new_user_close) {
            dismissAllowingStateLoss();
            return;
        }
        if (view.getId() == R.id.tv_DrawCash) {
            String drawCashTxt = tvDrawCash.getText().toString();
            if ("去领取".equals(drawCashTxt)) {
                NewUserRewardDialogManager.INSTANCE.showNewUserRewardGiftDialog(false);
                dismissAllowingStateLoss();
                return;
            }
            if (earnGuideRvAdapter != null && earnGuideRvAdapter.isCanOpenNewUserReward() && !earnGuideRvAdapter.isOpenNewUserReward) {
                if (!isAutoOpenReward && mRunnable != null) {
                    HandlerManager.removeCallbacks(mRunnable);
                    mRunnable = null;
                }
                earnGuideRvAdapter.isOpenNewUserReward = true;
                earnGuideRvAdapter.notifyDataSetChanged();
            }
            // 新人礼包放送弹窗-btn  弹框控件点击
            new XMTraceApi.Trace()
                    .setMetaId(54914)
                    .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                    .put("dialogItem", drawCashTxt)
                    .put("currPage", TAG)
                    .createTrace();
        }
    }

    class SpaceItemDecoration extends RecyclerView.ItemDecoration {
        private final int mLeftSpace;
        private final int mTopSpace;
        private final int mRightSpace;
        private final int mBottomSpace;

        public SpaceItemDecoration(int leftSpace, int topSpace, int rightSpace, int bottomSpace) {
            mLeftSpace = leftSpace;
            mTopSpace = topSpace;
            mRightSpace = rightSpace;
            mBottomSpace = bottomSpace;
        }

        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            if (parent.getLayoutManager() instanceof GridLayoutManager) {
                outRect.left = mLeftSpace;
                outRect.top = mTopSpace;
                outRect.right = mRightSpace;
                outRect.bottom = mBottomSpace;
            }
        }
    }

    private String result = "[{\n" +
            "\t\"rewardType\": 1,\n" +
            "\t\"rewardContent\": \"30天VIP\\n体验卡\"\n" +
            "}, {\n" +
            "\t\"rewardType\": 3,\n" +
            "\t\"rewardContent\": \"30天VIP\\n体验卡\\n+4000\\n金币\"\n" +
            "}, {\n" +
            "\t\"rewardType\": 2,\n" +
            "\t\"rewardContent\": \"4000\\n金币\"\n" +
            "}]";


    public interface NewUserOpenRewardCallBack {
        void openRewardBack();

        void stopRunnableBack();
    }
}
