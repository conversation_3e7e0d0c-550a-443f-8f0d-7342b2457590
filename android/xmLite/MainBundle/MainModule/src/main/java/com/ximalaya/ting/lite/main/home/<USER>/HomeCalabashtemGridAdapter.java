package com.ximalaya.ting.lite.main.home.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeCalabasGridViewModel;
import com.ximalaya.ting.lite.main.model.album.RecommendDiscoveryM;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qinhuifeng on 2019-07-16
 *
 * <AUTHOR>
 */
public class HomeCalabashtemGridAdapter extends BaseAdapter {

    private Context mContext;
    private List<HomeCalabasGridViewModel> mList;
    private OnCalabasItemClickListener mListener;

    public HomeCalabashtemGridAdapter(Context mContext, List<HomeCalabasGridViewModel> list) {
        this.mContext = mContext;
        this.mList = list;
        if (mList == null) {
            mList = new ArrayList<>();
        }
    }

    @Override
    public int getCount() {
        if (mList == null) {
            return 0;
        }
        return mList.size();
    }

    @Override
    public Object getItem(int position) {
        if (mList != null && getCount() > 0 && position < mList.size()) {
            return mList.get(position);
        }
        return null;
    }

    public void setListData(List<HomeCalabasGridViewModel> listData) {
        mList = listData;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        ItemHolder itemHolder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.main_item_home_calabash_line_grid_item, parent, false);
            itemHolder = new ItemHolder(convertView);
            convertView.setTag(itemHolder);
        } else {
            itemHolder = (ItemHolder) convertView.getTag();
        }
        Object object = getItem(position);
        if (!(object instanceof HomeCalabasGridViewModel)) {
            return convertView;
        }
        final HomeCalabasGridViewModel calabasGridViewModel = (HomeCalabasGridViewModel) object;
        RecommendDiscoveryM recommendDiscoveryM = calabasGridViewModel.recommendDiscoveryM;
        if (recommendDiscoveryM == null) {
            return convertView;
        }

        //公用部分
        itemHolder.tvTitle.setText(recommendDiscoveryM.getTitle());
        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onClick(v, calabasGridViewModel, position);
                }
            }
        });

        //类型不同区分处理
        if (calabasGridViewModel.viewType == HomeCalabasGridViewModel.ITEM_ALL) {
            //设置全部分类
            itemHolder.ivIcon.setImageResource(R.drawable.main_icon_home_tanghulu_typeall);
        } else {
            //处理正常分类
            ImageManager.from(mContext).displayImage(itemHolder.ivIcon, recommendDiscoveryM.getCoverPath(), -1, R.drawable.main_icon_home_tanghulu_error_def);
        }
        return convertView;
    }

    public class ItemHolder {
        private TextView tvTitle;
        private ImageView ivIcon;

        public ItemHolder(View convertView) {
            tvTitle = convertView.findViewById(R.id.main_tv_title);
            ivIcon = convertView.findViewById(R.id.main_iv_icon);
        }
    }

    public void setCalabseItemClickListener(OnCalabasItemClickListener listener) {
        mListener = listener;
    }

    public interface OnCalabasItemClickListener {
        void onClick(View view, HomeCalabasGridViewModel viewModel, int position);
    }
}
