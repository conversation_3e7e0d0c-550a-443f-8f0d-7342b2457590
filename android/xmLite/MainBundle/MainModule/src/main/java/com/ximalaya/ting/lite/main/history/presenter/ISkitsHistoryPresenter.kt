package com.ximalaya.ting.lite.main.history.presenter

import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack

interface ISkitsHistoryPresenter {
    /**
     * 加载数据
     */
    fun loadData();

    /**
     * 加载更多
     */
    fun loadMore();

    /**
     * 清除所有历史记录
     */
    fun clearAllHistoryRecord(callback: IDataCallBack<Boolean>?)

    /**
     * 同步记录
     */
    fun syncHistoryModifyRecord()

    /**
     * 查询历史数量
     */
    fun queryHistoryAmount(): Int
}