package com.ximalaya.ting.lite.main.manager

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.listener.IPlayTimeCallBack
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.ContextUtils
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.model.ListenerTimeTaskModel
import com.ximalaya.ting.lite.main.model.ListenerTimeTaskRewardModel
import com.ximalaya.ting.lite.main.play.manager.CalculatePlayTimeManager
import com.ximalaya.ting.lite.main.playnew.dialog.ListenerTimeRewardDialog
import org.json.JSONObject
import java.lang.ref.SoftReference


object ListenerTimeTaskManager {

    private const val TAG = "ListenerTimeTaskManager"

    // 总收听时长
    private const val KEY_TOTAL_LISTENER_TIME = "key_total_listener_time"

    // 总收听时长日期
    private const val KEY_TOTAL_LISTENER_TIME_DATE = "key_total_listener_time_date"

    // 收听时长奖励配置
    private const val KEY_TASK_CONFIG_DATA = "key_task_config_date"

    // 任务完成提示
    private const val KEY_TASK_FINISH_TOAST = "key_task_finish_toast"

    // 请求收听时长奖励时间
    const val KEY_LISTENER_TIME_REWARD = "key_request_listener_time_reward_date"

    const val KEY_IS_OPEN_LISTENER_TASK = "key_is_open_listener_task"

    var mListenerTimeTaskModel: ListenerTimeTaskModel? = null

    private var mIsRequestReward = false

    // 总的已收听时长 单位毫秒
    var mTotalListenerTime = 0L

    // 累计时长 用于间歇性保存"总的已收听时长"
    private var mTotalDiffTime = 0L

    private var mCurView: SoftReference<View>? = null

    private val mIPlayTimeCallBack = object : IPlayTimeCallBack {

        override fun onResult(speedDiffTime: Long, realDiffTime: Long, totalTime: Long, isPlaying: Boolean) {
            mTotalListenerTime += realDiffTime
            mTotalDiffTime += realDiffTime

            if (ConstantsOpenSdk.isDebug) {
                FuliLogger.log(TAG, "onResult mTotalListenerTime:${mTotalListenerTime / 1000} mTotalDiffTime:${mTotalDiffTime} isPlaying:${isPlaying}")
            }

            checkSaveTime(isPlaying)

            checkFinishTaskToast()

            initView(mCurView?.get())
        }
    }

    private fun checkFinishTaskToast() {
        if (!isOpenTask() || !checkConfig()) {
            return
        }

        val context = BaseApplication.mAppInstance
        val curDate = DateTimeUtil.getCurrentDate4yyyyMMdd()
        val saveDate = MmkvCommonUtil.getInstance(context).getString(KEY_TASK_FINISH_TOAST, "")
        if (saveDate == curDate) {
            return
        }

        if (mTotalListenerTime / 60000 >= mListenerTimeTaskModel!!.taskMinute) {
            // 前台才弹出提示
            if (BaseApplication.sInstance.isAppForeground) {
                CustomToast.showToast("今日已完成收听任务，赠送${mListenerTimeTaskModel!!.taskReward}分钟收听时长（明日可领）")
                MmkvCommonUtil.getInstance(context).saveString(KEY_TASK_FINISH_TOAST, curDate)
            }
        }
    }

    private fun checkSaveTime(isPlaying: Boolean) {
        // 暂停或者十秒保存一次
        if (!isPlaying || mTotalDiffTime >= 10000) {
            mTotalDiffTime = 0
            val context = BaseApplication.mAppInstance
            val curDate = DateTimeUtil.getCurrentDate4yyyyMMdd()
            val saveDate = MmkvCommonUtil.getInstance(context).getString(KEY_TOTAL_LISTENER_TIME_DATE, "")
            when {
                saveDate.isNullOrEmpty() -> {
                    // 首次存储需要存储日期
                    MmkvCommonUtil.getInstance(context).saveString(KEY_TOTAL_LISTENER_TIME_DATE, curDate)
                }
                curDate != saveDate -> {
                    // 跨天重置收听数据
                    mTotalListenerTime = 0
                    MmkvCommonUtil.getInstance(context).saveString(KEY_TOTAL_LISTENER_TIME_DATE, curDate)
                }
            }
            MmkvCommonUtil.getInstance(context).saveLong(KEY_TOTAL_LISTENER_TIME, mTotalListenerTime)
        }
    }


    /**
     * 冷启动后初始化配置
     */
    fun cacheConfigData() {
        val json = ConfigureCenter.getInstance().getString(CConstants.Group_Base.GROUP_NAME, CConstants.Group_Base.ITEM_LISTENER_TIME_TASK, "")
        if (!TextUtils.isEmpty(json)) {
            MmkvCommonUtil.getInstance(BaseApplication.mAppInstance).saveString(KEY_TASK_CONFIG_DATA, json)
            mListenerTimeTaskModel = JsonUtilKt.instance.toObject(json, ListenerTimeTaskModel::class.java)
        } else {
            initTaskConfig()
        }

        val isOpenTask = ABTest.getString("listeningtask", "false") == "true"
        MmkvCommonUtil.getInstance(BaseApplication.mAppInstance).saveBoolean(KEY_IS_OPEN_LISTENER_TASK, isOpenTask)
    }

    private fun initTaskConfig() {
        if (mListenerTimeTaskModel == null) {
            val json = MmkvCommonUtil.getInstance(BaseApplication.mAppInstance).getString(KEY_TASK_CONFIG_DATA, "")
            mListenerTimeTaskModel = JsonUtilKt.instance.toObject(json, ListenerTimeTaskModel::class.java)
        }
    }

    fun checkConfig(): Boolean {
        if (mListenerTimeTaskModel == null) {
            return false
        }
        if (mListenerTimeTaskModel!!.switch == 0
            || mListenerTimeTaskModel!!.taskMinute <= 0
            || mListenerTimeTaskModel!!.taskReward <= 0
            || mListenerTimeTaskModel!!.taskFinishedText.isNullOrEmpty()
            || mListenerTimeTaskModel!!.taskUnFinishedText.isNullOrEmpty()) {
            return false
        }
        return true
    }

    /**
     * 是否开启任务
     */
    fun isOpenTask(): Boolean {
        // vip用户 未开启收听时长  新设备  未命中任务ab
        if (UserInfoMannage.isVipUser()
            || !UnlockListenTimeManagerNew.isOpenUnlockTime()
            || UnlockListenTimeManagerNew.isNewDevice()
            || !MmkvCommonUtil.getInstance(BaseApplication.mAppInstance).getBoolean(KEY_IS_OPEN_LISTENER_TASK, false)) {
            return false
        }
        return true
    }

    fun init() {
        val context = BaseApplication.mAppInstance
        val curDate = DateTimeUtil.getCurrentDate4yyyyMMdd()
        // 跨天重置收听数据
        if (curDate != MmkvCommonUtil.getInstance(context).getString(KEY_TOTAL_LISTENER_TIME_DATE, "")) {
            mTotalListenerTime = 0
            MmkvCommonUtil.getInstance(context).saveString(KEY_TOTAL_LISTENER_TIME_DATE, curDate)
            MmkvCommonUtil.getInstance(context).saveLong(KEY_TOTAL_LISTENER_TIME, 0)
        } else {
            // 初始化已收听时长
            mTotalListenerTime = MmkvCommonUtil.getInstance(context).getLong(KEY_TOTAL_LISTENER_TIME)
        }

        initTaskConfig()

        if (isOpenTask()) {
            // app一启动就要监听播放   因为这个是用来计时的  即使没有界面也要计时
            CalculatePlayTimeManager.addAllTrackPlayTimeListener(mIPlayTimeCallBack)
        }
    }

    fun onResume() {

        if (isOpenTask()) {
            checkFinishTaskToast()

            checkReward()
        }
    }

    fun onDismiss() {
        mCurView = null
    }

    fun onDestroy() {
        CalculatePlayTimeManager.removeAllTrackPlayTimeListener(mIPlayTimeCallBack)
    }

    private fun checkReward() {
        val curDate = DateTimeUtil.getCurrentDate4yyyyMMdd()
        val date = MmkvCommonUtil.getInstance(BaseApplication.mAppInstance).getString(KEY_LISTENER_TIME_REWARD)
        if (curDate == date) {
            FuliLogger.log(TAG, "今天已请求过奖励接口")
            return
        }
        if (mIsRequestReward) {
            return
        }
        mIsRequestReward = true
        val map = mutableMapOf<String, String>()
        map["taskId"] = "1001"

        val url = UrlConstants.getInstanse().serverNetAddressHost + "lite-mobile/audi/func/v1/getTaskInfo"

        CommonRequestM.baseGetRequest(url, map, object : IDataCallBack<ListenerTimeTaskRewardModel?> {
            override fun onSuccess(model: ListenerTimeTaskRewardModel?) {
                FuliLogger.log(TAG, "请求成功: model:$model")
                mIsRequestReward = false
                if (model != null) {
                    // 10未完成 20已完成
                    if (model.taskStatus == 20) {
                        val activity = BaseApplication.getTopActivity()
                        if (activity is FragmentActivity && ContextUtils.checkActivity(activity)) {
                            ListenerTimeRewardDialog(model).show(activity.supportFragmentManager, "ListenerTimeRewardDialog")
                        }
                    } else {
                        MmkvCommonUtil.getInstance(BaseApplication.mAppInstance).saveString(KEY_LISTENER_TIME_REWARD, curDate)
                    }
                }
            }

            override fun onError(code: Int, message: String?) {
                mIsRequestReward = false
                FuliLogger.log(TAG, "请求失败: onError code:${code} message:$message")
            }
        }) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            if (ret == 0) {
                JsonUtilKt.instance.toObjectOfType<ListenerTimeTaskRewardModel>(json.optString("data"),
                    object : TypeToken<ListenerTimeTaskRewardModel>() {}.type)
            } else {
                null
            }
        }
    }

    fun checkShowTask(rootView: ViewGroup?) {
        if (rootView == null || !isOpenTask()) {
            rootView?.removeAllViews()
            return
        }

        initTaskConfig()

        if (!checkConfig()) {
            rootView.removeAllViews()
            FuliLogger.log(TAG, "配置参数问题:$mListenerTimeTaskModel")
            return
        }

        val view = LayoutInflater.from(rootView.context).inflate(R.layout.main_listener_time_task_layout, rootView)
        mCurView = SoftReference(view)
        initView(view)

        // 领时长浮层-时长任务曝光  控件曝光
        XMTraceApi.Trace()
            .setMetaId(50737)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "playPageTrackTab")
            .put("exploreType", "领时长浮层") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
            .createTrace()
    }

    private fun initView(view: View?) {
        if (view == null || !ContextUtils.checkActivity(view.context) || !checkConfig()) {
            return
        }
        val mIvStatus = view.findViewById<ImageView?>(R.id.main_iv_status)
        val mTvTitle = view.findViewById<TextView?>(R.id.main_tv_title)

        FuliLogger.log(TAG, "initView 已收听时长:${mTotalListenerTime / 1000}秒 完成任务时长:${mListenerTimeTaskModel!!.taskMinute}分钟")

        if (mTotalListenerTime / 60000 >= mListenerTimeTaskModel!!.taskMinute) {
            if (mIvStatus?.isSelected == false) {
                // 领时长浮层-时长任务完成提醒  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(50738)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "playPageTrackTab")
                    .put("exploreType", "领时长浮层") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                    .createTrace()
            }
            mIvStatus?.isSelected = true
            mTvTitle?.text = mListenerTimeTaskModel?.taskFinishedText
                ?.replace("[N]", mListenerTimeTaskModel!!.taskMinute.toString())
                ?.replace("[M]", mListenerTimeTaskModel!!.taskReward.toString())
        } else {
            mIvStatus?.isSelected = false
            mTvTitle?.text = mListenerTimeTaskModel?.taskUnFinishedText
                ?.replace("[N]", "${(mTotalListenerTime / 60000)}/${mListenerTimeTaskModel!!.taskMinute}")
                ?.replace("[M]", mListenerTimeTaskModel!!.taskReward.toString())
        }
    }
}