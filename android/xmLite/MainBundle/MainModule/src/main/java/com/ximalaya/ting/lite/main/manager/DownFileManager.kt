package com.ximalaya.ting.lite.main.manager

import android.content.Context
import android.content.Intent
import android.os.Environment
import android.util.Base64
import com.ximalaya.ting.android.encryptservice.EncryptUtil
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.FileProviderUtil
import com.ximalaya.ting.android.host.hybrid.utils.MD5Tool
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.lite.main.download.bean.TaskInfo
import com.ximalaya.ting.lite.main.download.bean.TaskState
import com.ximalaya.ting.lite.main.download.inter.DownloadListener
import com.ximalaya.ting.lite.main.download.manager.TaskMgr
import com.ximalaya.ting.lite.main.model.QJAppInfoModel
import java.io.File
import java.nio.charset.Charset
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

object DownFileManager {

    const val TAG = "DownFileManager"

    var mDownloadListener: DownloadListener? = null

    private var mCurLogTime = 0L

    private val dirPath =
        BaseApplication.mAppInstance?.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath?.plus(
            "/qiji"
        )


    fun encrypt(data: String?): String {
        if (data.isNullOrEmpty()) {
            return ""
        }
        val key = EncryptUtil.getInstance(null).getPrivacyStr(null, "down_qiji_apk_check_key")
        if (key.isNullOrEmpty()) {
            return ""
        }
        val utf8 = Charset.forName("utf-8")
        try {
            val cipher: Cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
            cipher.init(Cipher.ENCRYPT_MODE, SecretKeySpec(key.toByteArray(utf8), "AES"))
            val decrypted: ByteArray = cipher.doFinal(data.toByteArray(utf8))
            val decodeBase64: ByteArray = Base64.encode(decrypted, Base64.NO_WRAP)
            return String(decodeBase64, utf8)
        } catch (e: Exception) {
            e.printStackTrace()
            FuliLogger.log(TAG, e.message)
        }
        return ""
    }

    @JvmStatic
    fun checkApk(apkFile: File, sign: String?): Boolean {
        if (sign.isNullOrEmpty()) {
            FuliLogger.log(TAG, "checkApk: sign is null")
            return false
        }
        val localSign = encrypt(MD5Tool.md5(apkFile))

        FuliLogger.log(TAG, "checkApk: localSign:$localSign sign:$sign")

        return localSign == sign
    }

    @JvmStatic
    fun getDownFile(model: QJAppInfoModel): File {
        val downUrl = model.downloadPath!!
        var fileName = downUrl.subSequence(downUrl.lastIndexOf("/") + 1, downUrl.length).toString()
        val index = fileName.indexOf("?")
        if (index != -1) {
            fileName = fileName.substring(0, index)
        }
        return File(dirPath, fileName)
    }

    @JvmStatic
    fun createTaskInfo(model: QJAppInfoModel): TaskInfo {
        val file = getDownFile(model)
        if (file.exists()) {
            if (!checkApk(file, model.digest)) {
                file.delete()
            }
        }

        return TaskInfo.TaskInfoBuilder()
            .setUrl(model.downloadPath).setDirPath(dirPath).setFileName(file.name).build()
    }

    @JvmStatic
    fun isNeedStartDownload(taskInfo: TaskInfo?): Boolean {
        val state = TaskMgr.get().search(taskInfo)?.state
        return state == null || state.state >= TaskState.STATE_DONE
    }

    @JvmStatic
    fun downloadApkFile(taskInfo: TaskInfo?, sign: String?) {
        TaskMgr.get().add(taskInfo, object : DownloadListener {
            override fun onTaskStart(task: TaskInfo?) {
                mDownloadListener?.onTaskStart(task)
            }

            override fun onTaskSuccess(task: TaskInfo?) {
                task?.run {
                    file = File(dirPath, fileName)
                }

                if (task?.file?.exists() == true) {
                    if (!checkApk(task.file, sign)) {
                        task.file.delete()
                        mDownloadListener?.onTaskFailed(task)
                    } else {
                        mDownloadListener?.onTaskSuccess(task)
                    }
                } else {
                    mDownloadListener?.onTaskFailed(task)
                }
            }

            override fun onTaskFailed(task: TaskInfo?) {
                mDownloadListener?.onTaskFailed(task)
            }

            override fun onTaskProgress(task: TaskInfo?, progress: Int) {
                val time = System.currentTimeMillis()
                if (time - mCurLogTime > 300) {
                    FuliLogger.log(TAG, "当前进度${progress}%")
                    mCurLogTime = time
                }
                mDownloadListener?.onTaskProgress(task, progress)
            }
        })
    }

    fun installApp(context: Context?, appFile: File?): Boolean {
        try {
            if (null != appFile && appFile.exists()) {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                FileProviderUtil.setIntentDataAndType(
                    context, intent,
                    "application/vnd.android.package-archive",
                    appFile, true
                )
                context?.startActivity(intent)
                return true
            } else {
                FuliLogger.log(TAG, "触发安装失败,appFile: ${appFile?.exists()}")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            FuliLogger.log(TAG, e.message)
        }
        return false
    }
}