package com.ximalaya.ting.lite.main.home.adapter

import android.graphics.Bitmap
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import com.ximalaya.ting.android.framework.adapter.HolderAdapter.BaseViewHolder
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel
import com.ximalaya.ting.lite.main.manager.ITingHandler
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList
import kotlinx.android.synthetic.main.main_item_net_operate.view.*

/**
 * Created by dumingwei on 2020/6/12
 *
 * Desc: 网页运营模块
 */
class NetOperateProvider @JvmOverloads constructor(
        val mFragment: BaseFragment2,
        private val mExtraModel: HomeRecommendExtraViewModel? = null
) : IMulitViewTypeViewAndData<NetOperateProvider.Holder, MainAlbumMList> {

    private val TAG: String = "NetOperateProvider"

    private var tingHandler: ITingHandler? = null
    private var mModel: MainAlbumMList? = null

    private val dp8: Int
    private val coverWidth: Int

    init {
        tingHandler = ITingHandler()
        dp8 = BaseUtil.dp2px(mFragment.context, 8f)
        coverWidth = BaseUtil.getScreenWidth(mFragment.context) - BaseUtil.dp2px(mFragment.context, 24f)
        Logger.i(TAG, "coverWidth = $coverWidth")
    }

    override fun getView(layoutInflater: LayoutInflater, position: Int, parent: ViewGroup): View {
        return layoutInflater.inflate(R.layout.main_item_net_operate, parent, false)
    }

    override fun buildHolder(convertView: View): Holder {
        return Holder(convertView)
    }

    override fun bindViewDatas(holder: Holder, t: ItemModel<MainAlbumMList>, convertView: View, position: Int) {
        val model = t.getObject()
        mModel = model
        if (model is MainAlbumMList) {
            val list = model.netOperateModelList
            if (CollectionUtil.isNotEmpty(list)) {
                val netOperateModel = list[0]
                netOperateModel?.let {
                    with(holder.itemView) {
                        if (netOperateModel.title.isNullOrEmpty()) {
                            llTitleInfo.visibility = View.GONE
                            //这里修改clCoverInfo的bottomMargin
                            val layoutParams = clCoverInfo.layoutParams as LinearLayout.LayoutParams
                            layoutParams.bottomMargin = dp8
                            clCoverInfo.layoutParams = layoutParams
                        } else {
                            //这里修改clCoverInfo的bottomMargin
                            val layoutParams = clCoverInfo.layoutParams as LinearLayout.LayoutParams
                            layoutParams.bottomMargin = 0
                            clCoverInfo.layoutParams = layoutParams
                            llTitleInfo.visibility = View.VISIBLE
                            mainTvTitle.text = netOperateModel.title
                            if (netOperateModel.subTitle.isNullOrEmpty()) {
                                mainTvSubTitle.visibility = View.GONE
                            } else {
                                mainTvSubTitle.visibility = View.VISIBLE
                                mainTvSubTitle.text = netOperateModel.subTitle
                            }
                        }

                        /*  Picasso.with(context).load(netOperateModel.coverPath)
                                  .error(R.drawable.host_album_default_1_145)
                                  .into(object : Target {
                                      override fun onPrepareLoad(p0: Drawable?) {
                                          Logger.i(TAG, "onPrepareLoad")
                                      }

                                      override fun onBitmapFailed(p0: Drawable?) {
                                          Logger.i(TAG, "onBitmapFailed")
                                      }

                                      override fun onBitmapLoaded(bitmap: Bitmap?, p1: Picasso.LoadedFrom?) {
                                          bitmap?.let {
                                              val width = it.width * 1.0f
                                              val height = it.height * 1.0f
                                              if (width > 0 && height > 0) {
                                                  Logger.i(TAG, "h,${width}:${height}")
                                                  val layoutParams = clCoverInfo.layoutParams as LinearLayout.LayoutParams

                                                  layoutParams.width = coverWidth
                                                  when {
                                                      height / width < (70f / 343f) -> {
                                                          layoutParams.height = (coverWidth * 40f / 343f).toInt()
                                                      }
                                                      height / width > (200f / 343f) -> {
                                                          layoutParams.height = (coverWidth * 200f / 343f).toInt()
                                                      }
                                                      else -> {
                                                          layoutParams.height = (coverWidth * height / width).toInt()
                                                      }
                                                  }
                                                  clCoverInfo.layoutParams = layoutParams

                                                  mainIvCover2.setImageBitmap(it)
                                              }
                                          }
                                      }
                                  })*/
                        ImageManager.from(context).displayOriginalImage(mainIvCover2, netOperateModel.coverPath, R.drawable.host_default_album_145, object : ImageManager.DisplayCallback {
                            override fun onCompleteDisplay(lastUrl: String?, bitmap: Bitmap?) {
                                if (bitmap != null) {
                                    val width = bitmap.width * 1.0f
                                    val height = bitmap.height * 1.0f
                                    if (width > 0 && height > 0) {
                                        Logger.i(TAG, "h,${width}:${height}")
                                        val layoutParams = clCoverInfo.layoutParams as LinearLayout.LayoutParams

                                        layoutParams.width = coverWidth
                                        when {
                                            height / width < (70f / 343f) -> {
                                                layoutParams.height = (coverWidth * 70f / 343f).toInt()
                                            }
                                            height / width > (200f / 343f) -> {
                                                layoutParams.height = (coverWidth * 200f / 343f).toInt()
                                            }
                                            else -> {
                                                layoutParams.height = (coverWidth * height / width).toInt()
                                            }
                                        }

                                        clCoverInfo.layoutParams = layoutParams

                                        mainIvCover2.setImageBitmap(bitmap)
                                    }
                                } else {
                                    //mainIvCover2.background = context.resources.getDrawable(R.drawable.host_default_album_145)
                                }
                            }
                        })
                        /*ImageManager.from(context).displayImage(mainIvCover2, netOperateModel.coverPath,
                                -1, object : ImageManager.DisplayCallback {
                            override fun onCompleteDisplay(lastUrl: String?, bitmap: Bitmap?) {

                            }

                        }, true)*/
                        holder.itemView.setOnClickListener {
                            netOperateModel.url?.let {
                                ToolUtil.clickUrlAction(mFragment, it, null)
                            }
                        }
                    }
                }
            }
        }
    }

    class Holder(var itemView: View) : BaseViewHolder()

}