package com.ximalaya.ting.lite.main.home.adapter;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import com.ximalaya.ting.android.host.fragment.BaseFragment2;

import java.util.List;

/**
 * Created by qinhuifeng on 2021/1/17
 *
 * <AUTHOR>
 */
public class AlbumRankTabViewPageAdapter extends FragmentStatePagerAdapter {

    private final List<? extends BaseFragment2> mFragmentList;
    private final List<String> mTitles;

    public AlbumRankTabViewPageAdapter(@NonNull FragmentManager fm, List<? extends BaseFragment2> list, List<String> titles) {
        super(fm);
        mFragmentList = list;
        mTitles = titles;
    }


    @NonNull
    @Override
    public Fragment getItem(int position) {
        if (mFragmentList != null && mFragmentList.size() > position) {
            Fragment fragment = mFragmentList.get(position);
            if (fragment != null) {
                return fragment;
            }
        }
        return new Fragment();
    }

    @Override
    public int getCount() {
        if (mFragmentList != null) {
            return mFragmentList.size();
        }
        return 0;
    }

    @Nullable
    @Override
    public CharSequence getPageTitle(int position) {
        if (mTitles != null && mTitles.size() > position) {
            String title = mTitles.get(position);
            if (!TextUtils.isEmpty(title)) {
                return title;
            }
        }
        return null;
    }
}
