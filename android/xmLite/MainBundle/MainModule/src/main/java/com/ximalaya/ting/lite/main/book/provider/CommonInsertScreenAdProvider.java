package com.ximalaya.ting.lite.main.book.provider;

import android.app.Activity;
import android.widget.FrameLayout;

import com.ximalaya.ting.android.host.adsdk.callback.IThirdNativeAdLoadCallback;
import com.ximalaya.ting.android.host.adsdk.manager.ThirdInsertScreenAdShowManger;
import com.ximalaya.ting.android.host.adsdk.platform.common.modelproxy.AbstractThirdAd;
import com.ximalaya.ting.android.host.business.unlock.callback.IInsertScreenAdCallBack;
import com.ximalaya.ting.android.host.insertscreen.manager.InsertScreenCallbackManager;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.model.ad.LoadReawardParams;
import com.ximalaya.ting.android.host.util.ContextUtils;
import com.ximalaya.ting.lite.main.home.adapter.HomeCustomAdProvider;

/**
 * <p>
 * 插屏广告模块封装
 *
 * <AUTHOR>
 */
public class CommonInsertScreenAdProvider {

    private final Activity mActivity;

    //是否正在请求广告
    private volatile boolean mIsAdRequesting = false;

    private final InsertScreenCallbackManager mInsertScreenCallbackManager;

    private HomeCustomAdProvider mHomeCustomAdProvider;

    private final IInsertScreenAdCallBack mIInsertScreenAdCallBack;

    private final FrameLayout mAdContainer;

    public CommonInsertScreenAdProvider(Activity activity, FrameLayout adContainer) {
        this(activity, adContainer, null);
    }

    public CommonInsertScreenAdProvider(Activity activity, FrameLayout adContainer, IInsertScreenAdCallBack callBack) {
        mActivity = activity;
        mIInsertScreenAdCallBack = callBack;
        mAdContainer = adContainer;
        mInsertScreenCallbackManager = new InsertScreenCallbackManager(callBack);
    }

    /******************生命周期方法，广点通广告使用*******************************/
    public void loadInsertScreenAd(String positionName, String adCodeId, LoadReawardParams params) {
        loadInsertScreenAd(positionName, adCodeId, params, null);
    }


    public void loadInsertScreenAd(String positionName, String adCodeId, LoadReawardParams params, isShowCallBack showCallBack) {
        if (mIsAdRequesting) {
            return;
        }
        mIsAdRequesting = true;

        if (params == null) {
            params = new LoadReawardParams();
        }

        ThirdInsertScreenAdShowManger.loadInsertScreenAdWithCacheWithDef(mActivity, positionName, adCodeId, params, new IThirdNativeAdLoadCallback() {
            @Override
            public void loadThirdNativeAdSuccess(AbstractThirdAd<?> thirdAd) {
                mIsAdRequesting = false;
                if (!ContextUtils.checkActivity(mActivity)) {
                    return;
                }
                if (thirdAd == null || thirdAd.getAdData() == null) {
                    return;
                }

                if (showCallBack != null && !showCallBack.isShowAD()) {
                    return;
                }

                FuliLogger.log("插屏广告:广告类型:" + thirdAd);

                // 插屏模板渲染啊
                if (mInsertScreenCallbackManager != null && mInsertScreenCallbackManager.isSupportCurThirdAd(thirdAd)) {
                    try {
                        mInsertScreenCallbackManager.showInsertScreenAd(mActivity, thirdAd);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    // 插屏自定义
                    if (mHomeCustomAdProvider == null) {
                        mHomeCustomAdProvider = new HomeCustomAdProvider(mActivity.getApplicationContext(), mAdContainer, mIInsertScreenAdCallBack);
                    }
                    mHomeCustomAdProvider.bindViewData(thirdAd, positionName);
                }
            }

            @Override
            public void serverNoNativeAd() {
                FuliLogger.log("插屏广告:serverNoNativeAd");
                mIsAdRequesting = false;
            }

            @Override
            public void loadThirdNativeAdError() {
                FuliLogger.log("插屏广告:loadThirdNativeAdError");
                mIsAdRequesting = false;
            }
        });
    }

    /**
     * todo 需要确认是首页可见还是推荐频道可见
     */
    public void onResume() {
        if (mHomeCustomAdProvider != null) {
            try {
                mHomeCustomAdProvider.onMyResume();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void closeAd() {
        if (mInsertScreenCallbackManager != null) {
            mInsertScreenCallbackManager.closeAd();
        }
        if (mHomeCustomAdProvider != null) {
            mHomeCustomAdProvider.hideAd();
        }
    }

    public boolean onBackPressed() {
        if (mHomeCustomAdProvider != null) {
            return mHomeCustomAdProvider.onBackPressed();
        }
        return false;
    }

    public void onDestroy() {
        if (mHomeCustomAdProvider != null) {
            mHomeCustomAdProvider.onDestroy();
        }
    }

    public interface isShowCallBack {
        boolean isShowAD();
    }
}
