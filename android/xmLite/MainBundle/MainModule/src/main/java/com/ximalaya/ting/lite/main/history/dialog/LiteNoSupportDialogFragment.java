package com.ximalaya.ting.lite.main.history.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.manager.download.DownloadXmlyFullManager;
import com.ximalaya.ting.android.host.util.common.PackageUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * 极速版不支持，引导下载喜马拉雅完整版或者打开喜马拉雅完整版
 *
 * <AUTHOR>
 */
public class LiteNoSupportDialogFragment extends BaseDialogFragment {

    // 从专辑声音列表进入
    public static final int PAGE_SOURCE_FROM_ALBUM_LIST = 1;

    private LinearLayout mLlClose;
    private boolean mMaskIsShow = false; //解决fragment重复添加crash问题
    private TextView mTvHintContent;
    private TextView mTvDownloadFullXmly;

    //下面的是三个id只能使用一个
    //声音id，小于0，没有声音id
    private long mTrackId = -1;
    //广播id
    private long mRadioId = -1;
    //专辑id
    private long mAlbumId = -1;

    private int mPageSourceFrom;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.main_dialog_lite_no_support, container, false);
        mLlClose = view.findViewById(R.id.main_ll_close);
        mTvHintContent = view.findViewById(R.id.main_tv_hint_content);
        mTvDownloadFullXmly = view.findViewById(R.id.main_tv_download_full_xmly);

        final boolean isXimalayaFullInstalled = PackageUtil.isXimalyaFullInstalled(getActivity());
        if (isXimalayaFullInstalled) {
            //已经安装了喜马拉雅完整版
            mTvHintContent.setText("极速版无法播放该节目，可打开喜马拉雅完整版播放哦");
            mTvDownloadFullXmly.setText("打开喜马拉雅完整版");
        } else {
            mTvHintContent.setText("极速版无法播放该节目，可下载喜马拉雅完整版播放哦");
            mTvDownloadFullXmly.setText("打开应用市场下载喜马拉雅完整版");
        }

        mTvDownloadFullXmly.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                dismissAllowingStateLoss();
                dealWithDownLoadFullXmly(v);

                if (mPageSourceFrom == PAGE_SOURCE_FROM_ALBUM_LIST) {
                    new XMTraceApi.Trace()
                            .setMetaId(12742)
                            .setServiceId("dialogClick")
                            .put("item", isXimalayaFullInstalled ? "打开" : "下载")
                            .createTrace();
                }
            }
        });
        AutoTraceHelper.bindData(mTvDownloadFullXmly, AutoTraceHelper.MODULE_DEFAULT, "");

        //点击任意区域关闭
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissAllowingStateLoss();
            }
        });
        AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, "");
        mLlClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissAllowingStateLoss();
            }
        });
        AutoTraceHelper.bindData(mLlClose, AutoTraceHelper.MODULE_DEFAULT, "");

        if (mPageSourceFrom == PAGE_SOURCE_FROM_ALBUM_LIST) {
            new XMTraceApi.Trace()
                    .setMetaId(12741)
                    .setServiceId("dialogView")
                    .put("albumId", String.valueOf(mAlbumId))
                    .createTrace();
        }
        return view;
    }

    /**
     * 下载完整版喜马拉雅
     */
    private void dealWithDownLoadFullXmly(View v) {
        if (!PackageUtil.isXimalyaFullInstalled(getActivity())) {
            try {
                Uri uri = Uri.parse("market://details?id=com.ximalaya.ting.android");
                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                getActivity().startActivity(intent);
            } catch (Throwable e) {
                e.printStackTrace();
                CustomToast.showFailToast("应用市场打开失败");
            }
            return;
        }
        //已经按照了喜马拉雅完整版，打开本专辑
        String url = "iting://open";
        if (mAlbumId > 0) {
            //打开专辑页面
            url = "iting://open?msg_type=13&album_id=" + mAlbumId;
            Logger.d("LiteNoSupportDialogFragment", "albumId=" + mAlbumId);
        } else if (mTrackId > 0) {
            //打开对应声音播放页面
            url = "iting://open?msg_type=11&track_id=" + mTrackId;
            Logger.d("LiteNoSupportDialogFragment", "trackId=" + mTrackId);
        } else if (mRadioId > 0) {
            //打开对应的广播页面
            url = "iting://open?msg_type=50&radio_id=" + mRadioId;
            Logger.d("LiteNoSupportDialogFragment", "radioId=" + mRadioId);
        } else {
            //打开主app
            url = "iting://open";
            Logger.d("LiteNoSupportDialogFragment", "无id");
        }
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse(url));
            startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
            CustomToast.showFailToast("打开失败");
        }
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        //去掉标题
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setCanceledOnTouchOutside(false);
        Window window = dialog.getWindow();
        if (window != null) {
            //dialog背景设置透明，解决shape不生效问题
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.getDecorView().setPadding(0, 0, 0, 0); //消除边距
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;   //设置宽度充满屏幕
            lp.height = WindowManager.LayoutParams.MATCH_PARENT;
            window.setAttributes(lp);
        }
        return dialog;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mMaskIsShow = false;
    }

    public boolean isShowing() {
        return mMaskIsShow;
    }

    @Override
    public int show(FragmentTransaction transaction, String tag) {
        if (mMaskIsShow) {
            return 0;
        }
        mMaskIsShow = true;
        return super.show(transaction, tag);
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        if (mMaskIsShow) {
            return;
        }
        mMaskIsShow = true;
        super.show(manager, tag);
    }

    public void setRadioId(long mRadioId) {
        this.mRadioId = mRadioId;
    }

    public void setAlbumId(long mAlbumId) {
        this.mAlbumId = mAlbumId;
    }

    public void setTrackId(long mTrackId) {
        this.mTrackId = mTrackId;
    }

    public void setPageSourceFrom(int pageSourceFrom) {
        this.mPageSourceFrom = pageSourceFrom;
    }

    @Override
    public void dismiss() {
        super.dismiss();
    }
}
