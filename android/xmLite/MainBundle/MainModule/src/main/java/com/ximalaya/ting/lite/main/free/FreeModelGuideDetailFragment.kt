package com.ximalaya.ting.lite.main.free

import android.os.Bundle
import android.os.Looper
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.IPlayTimeCallBack
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew.IPlayTimeChangeCallBack
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew.addTimeChangeCallBack
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew.getAvailableListeningTime
import com.ximalaya.ting.android.host.manager.UnlockListenTimeManagerNew.removeTimeChangeCallBack
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.common.SpannableStringUtils
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.free.ad.FreeModelAdHelper
import com.ximalaya.ting.lite.main.free.adapter.FreeModelTaskAdapter
import com.ximalaya.ting.lite.main.manager.ListenerTimeTaskManager
import com.ximalaya.ting.lite.main.model.FreeTaskModel
import com.ximalaya.ting.lite.main.model.ListenerTimeTaskListModel
import com.ximalaya.ting.lite.main.model.TASK_TYPE_COIN_REWARD
import com.ximalaya.ting.lite.main.model.TASK_TYPE_LISTENER_TIME_REWARD
import com.ximalaya.ting.lite.main.model.TASK_TYPE_VIDEO_REWARD
import com.ximalaya.ting.lite.main.play.manager.CalculatePlayTimeManager
import org.json.JSONObject


class FreeModelGuideDetailFragment : BaseFragment2() {

    val mTAG = "FreeModelGuideDetailFragment"

    private var tvTime: TextView? = null
    private var mRvTask: RecyclerView? = null
    private val mList: MutableList<FreeTaskModel> = mutableListOf()

    private var mAdapter: FreeModelTaskAdapter? = null

    // 收听时长任务 下标
    private var mListenTimeTaskIndex = -1

    private var mIsFinishListenTimeTask = false

    // 当前收听时长任务标题
    private var mCurListenTimeTaskTitle = ""

    /**
     * 兑换了可用时长 导致时长改变
     */
    private val mPlayTimeChangeCallBack = object : IPlayTimeChangeCallBack {
        override fun onChange() {
            if (canUpdateUi()) {
                if (Looper.getMainLooper() == Looper.myLooper()) {
                    showTimeUi()
                } else {
                    HandlerManager.postOnUIThread { showTimeUi() }
                }
            }
        }
    }

    /**
     * 收听需要时长的声音导致时长变化回调
     */
    private val mUsedTimeChangeCallBack = object : IPlayTimeCallBack {

        override fun onResult(speedDiffTime: Long, realDiffTime: Long, totalTime: Long, isPlaying: Boolean) {
            if (canUpdateUi()) {
                showTimeUi()

                if (ListenerTimeTaskManager.isOpenTask() && ListenerTimeTaskManager.checkConfig()) {
                    var listenerTime = (ListenerTimeTaskManager.mTotalListenerTime / 60000).toInt()
                    val taskMinute = ListenerTimeTaskManager.mListenerTimeTaskModel!!.taskMinute
                    val isFinish = listenerTime >= taskMinute

                    // 收听时长不超过任务时长
                    listenerTime = listenerTime.coerceAtMost(taskMinute)

                    val title = "今天收听超过${listenerTime}/${taskMinute}分钟"

                    if (isFinish != mIsFinishListenTimeTask || title != mCurListenTimeTaskTitle) {
                        mIsFinishListenTimeTask = isFinish
                        mCurListenTimeTaskTitle = title
                        if (mListenTimeTaskIndex != -1) {
                            val model = mList[mListenTimeTaskIndex]
                            model.subTitle = title
                            mAdapter?.notifyItemChanged(mListenTimeTaskIndex)
                        }
                    }
                }
            }
        }
    }

    override fun getPageLogicName(): String {
        return this.javaClass.simpleName
    }

    override fun initUi(savedInstanceState: Bundle?) {
        // 免费畅听主页面  页面展示
        XMTraceApi.Trace()
            .pageView(51203, "Allfreemainpage") // 页面出现在用户视野时上报一条埋点，包括离开页面后返回、息屏后亮屏等
            .put("currPage", "Allfreemainpage")
            .createTrace()

        findViewById<View?>(R.id.main_iv_back)?.setOnClickListener {
            finishFragment()
        }
        tvTime = findViewById(R.id.main_tv_time)
        mRvTask = findViewById(R.id.main_rv_task_list)

        mRvTask?.run {
            setHasFixedSize(true)
            isNestedScrollingEnabled = false
            layoutManager = object : LinearLayoutManager(context, VERTICAL, false) {
                override fun canScrollVertically(): Boolean {
                    return false
                }
            }
            mAdapter = FreeModelTaskAdapter(mList)
            adapter = mAdapter
        }
    }

    private fun addPlayTimeListener() {
        // 监听可收听时长变化
        CalculatePlayTimeManager.addLockTrackPlayTimeListener(mUsedTimeChangeCallBack)
        addTimeChangeCallBack(mPlayTimeChangeCallBack)

        if (ListenerTimeTaskManager.checkConfig() && ListenerTimeTaskManager.isOpenTask()) {
            if (ListenerTimeTaskManager.mTotalListenerTime < ListenerTimeTaskManager.mListenerTimeTaskModel!!.taskMinute * 60000) {
                // 任务状态发生变化  刷新
                if (mIsFinishListenTimeTask) {
                    if (mListenTimeTaskIndex != -1) {
                        mAdapter?.notifyItemChanged(mListenTimeTaskIndex)
                    }
                }
                mIsFinishListenTimeTask = false
            } else {
                // 任务状态发生变化  刷新
                if (!mIsFinishListenTimeTask) {
                    if (mListenTimeTaskIndex != -1) {
                        mAdapter?.notifyItemChanged(mListenTimeTaskIndex)
                    }
                }
                mIsFinishListenTimeTask = true
            }
        }
    }

    override fun onResume() {
        super.onResume()
        addPlayTimeListener()
        showTimeUi()
    }

    override fun onPause() {
        super.onPause()
        removeTimeChangeCallBack(mPlayTimeChangeCallBack)
        CalculatePlayTimeManager.removeAllTrackPlayTimeListener(mUsedTimeChangeCallBack)
    }

    private fun showTimeUi() {
        //  大于4小时：显示“剩余收听时长超过4小时”； 0<X<4小时：显示“剩余收听时长还剩H小时M分钟”；X=0分钟，显示“剩余收听时长还剩0分钟”
        val text = formatAvailableListeningTime(getAvailableListeningTime())
        val content = SpannableStringUtils.transformForChangeSize(text, 1.8f, true, "\\d+")
        tvTime?.text = content
    }

    /**
     * 格式化可用收听时长
     *
     * @param time 可用收听时长 单位秒
     * @return 格式化后数据  大于4小时：显示“剩余收听时长超过4小时”； 0<X></X><4小时：显示“剩余收听时长还剩H小时M分钟”；X=0分钟，显示“剩余收听时长还剩0分钟”
     */
    private fun formatAvailableListeningTime(time: Long): String {
        if (time <= 0L) {
            return "0分钟"
        }
        // 计算出分钟
        var minutes = time / 60f
        // 计算多余的秒数
        val second = time % 60f
        var hour = (minutes / 60).toInt()
        // 秒数不为空  分钟数+1
        minutes = if (second == 0f) minutes % 60 else minutes % 60 + 1
        var minutesInt = minutes.toInt()

        // 假如是59分10秒  向上+1分钟  会变成60分钟  则需要进位
        if (minutesInt >= 60) {
            minutesInt -= 60
            hour += 1
        }

        if (hour == 0) {
            return "${minutesInt}分钟"
        }

        return "${hour}小时${minutesInt}分钟"
    }

    override fun loadData() {
        val url = UrlConstants.getInstanse().serverNetAddressHost + "lite-mobile/audi/func/v1/taskList"

        CommonRequestM.baseGetRequest(url, null, object : IDataCallBack<ListenerTimeTaskListModel> {
            override fun onSuccess(model: ListenerTimeTaskListModel?) {
                if (model == null) {
                    FuliLogger.log(mTAG, "请求列表失败: model is null")
                } else {
                    performTaskList(model)
                }
            }

            override fun onError(code: Int, message: String?) {
                FuliLogger.log(mTAG, "请求列表失败: onError code:${code} message:$message")
            }
        }, object : CommonRequestM.IRequestCallBack<ListenerTimeTaskListModel?> {
            override fun success(content: String?): ListenerTimeTaskListModel? {
                if (content.isNullOrEmpty()) {
                    return null
                }
                val json = JSONObject(content)
                val ret = json.optInt("ret", -1)
                return if (ret == 0) {
                    JsonUtilKt.instance.toObjectOfType<ListenerTimeTaskListModel>(json.optString("data"),
                        object : TypeToken<ListenerTimeTaskListModel>() {}.type)
                } else {
                    null
                }
            }

        })
    }

    private fun performTaskList(model: ListenerTimeTaskListModel) {
        val videoExchangeTaskInfo = model.videoExchangeTaskInfo
        val coinExchangeTaskInfo = model.coinExchangeTaskInfo
        val listenRewardTaskInfo = model.listenRewardTaskInfo

        if (videoExchangeTaskInfo?.order != null) {
            mList.add(FreeTaskModel("+${videoExchangeTaskInfo.videoExchangeRateListenDuration / 60}分钟", "看1段视频广告", TASK_TYPE_VIDEO_REWARD, videoExchangeTaskInfo.order))
        }

        if (coinExchangeTaskInfo?.order != null) {
            mList.add(FreeTaskModel("+${coinExchangeTaskInfo.coinExchangeRateListenDuration / 60}分钟", "花费${coinExchangeTaskInfo.coinExchangeRateCoinNum}金币兑换", TASK_TYPE_COIN_REWARD, coinExchangeTaskInfo.order))
        }

        if (listenRewardTaskInfo?.order != null && ListenerTimeTaskManager.checkConfig() && ListenerTimeTaskManager.isOpenTask()) {
            mListenTimeTaskIndex = mList.size
            var listenerTime = (ListenerTimeTaskManager.mTotalListenerTime / 60000).toInt()
            val taskMinute = ListenerTimeTaskManager.mListenerTimeTaskModel!!.taskMinute
            // 收听时长不超过任务时长
            listenerTime = listenerTime.coerceAtMost(taskMinute)
            mCurListenTimeTaskTitle = "今天收听超过${listenerTime}/${taskMinute}分钟"
            mList.add(FreeTaskModel("+${ListenerTimeTaskManager.mListenerTimeTaskModel?.taskReward}分钟", mCurListenTimeTaskTitle, TASK_TYPE_LISTENER_TIME_REWARD, listenRewardTaskInfo.order))
        }

        mList.sortWith(Comparator { o1, o2 -> return@Comparator o1.orderNum - o2.orderNum })

        val rewardDuration = videoExchangeTaskInfo?.videoExchangeRateListenDuration ?: 0
        mAdapter?.setRewardDuration(rewardDuration)
        mAdapter?.notifyDataSetChanged()
        //保存免费听的时长,用户激励视频文案展示，避免直接看激励视频
        val key = PreferenceConstantsInHost.REWARD_TIME_KEY + "sub_freetime_inspire_video"
        MmkvCommonUtil.getInstance(BaseApplication.mAppInstance.applicationContext)
            .saveInt(key, rewardDuration )
        //保存展示过免费听
        FreeModelAdHelper.saveReceiveUnlockTime()
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_free_model_detail_layout
    }

    override fun onDestroy() {
        super.onDestroy()
        // 免费畅听主页面  页面离开
        XMTraceApi.Trace()
            .pageExit2(51204) // 页面离开在用户视野时上报一条埋点，包括锁屏、回到主页面等
            .createTrace()
    }

    override fun isShowCoinGuide(): Boolean {
        return false
    }

    override fun isShowPlayButton(): Boolean {
        return false
    }
}