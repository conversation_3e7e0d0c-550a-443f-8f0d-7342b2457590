package com.ximalaya.ting.lite.main.login.qucikmob;

import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;

import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.loginservice.verify.VerifyManager;

/**
 * Created by nali on 2018/5/3.
 *
 * <AUTHOR>
 */
public class QuickLoginForMobHalfPageActivity extends BaseFragmentActivity2 implements ILoginStatusChangeListener {

    private QuickLoginMobForHalfPageProxyFragment loginDialogFragment;

    @Override
    protected void onCreate(Bundle savedState) {
        super.onCreate(savedState);
        VerifyManager.preloadGTCaptchClient(this);
        showLoginDialogFragment();
    }

    private void showLoginDialogFragment() {
        if (isFinishing()) {
            return;
        }
        loginDialogFragment = new QuickLoginMobForHalfPageProxyFragment();
        //将intent中的登录bundle传递到Fragment中
        Bundle bundle = new Bundle();
        if (getIntent() != null) {
            Bundle extras = getIntent().getExtras();
            if (extras != null) {
                bundle.putAll(extras);
            }
        }
        loginDialogFragment.setArguments(bundle);
        loginDialogFragment.show(getSupportFragmentManager(), "SmsLoginProxyFragment");
        loginDialogFragment.setOnDismissListener(new BaseDialogFragment.OnDismissListener() {
            @Override
            public void onDismiss() {
                finish();
            }
        });
        loginDialogFragment.setOnDestroyHandle(new IHandleOk() {
            @Override
            public void onReady() {
                finish();
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (loginDialogFragment != null) {
            loginDialogFragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        VerifyManager.destroyGTCaptchClient();
        //登录使用到了输入法，fix可能造成的内存泄露问题
        ToolUtil.fixInputMethodManagerLeak(this);
        ToolUtil.clearTextLineCache();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (outState != null) {
            String FRAGMENTS_TAG = "android:support:fragments";
            outState.remove(FRAGMENTS_TAG);
        }
    }

    @Override
    public void onResume() {
        super.onResume();

        if (UserInfoMannage.hasLogined()) {
            finish();
        } else {
            UserInfoMannage.getInstance().addLoginStatusChangeListener(this);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(this);
    }


    @Override
    public void onLogout(LoginInfoModelNew olderUser) {

    }

    @Override
    public void onLogin(LoginInfoModelNew model) {
        finish();
    }

    @Override
    public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {
        finish();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        try {
            VerifyManager.onConfigurationChanged(newConfig);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
