package com.ximalaya.ting.lite.main.model.album;

import android.view.View;

import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;

/**
 * Created by <PERSON> on 2017/10/28.
 *
 * <AUTHOR>
 */

public class TitleModule {
    private int categoryId;
    private MainAlbumMList titleBean;
    private View.OnClickListener moreClickListener;
    private boolean showTopDivider = true;
    private boolean showBottomDivider;

    public TitleModule(int categoryId, MainAlbumMList titleBean, View.OnClickListener moreClickListener) {
        this.categoryId = categoryId;
        this.moreClickListener = moreClickListener;
        this.titleBean = titleBean;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public MainAlbumMList getTitleBean() {
        return titleBean;
    }

    public View.OnClickListener getMoreClickListener() {
        return moreClickListener;
    }

    public boolean isShowTopDivider() {
        return showTopDivider;
    }

    public void setShowTopDivider(boolean showTopDivider) {
        this.showTopDivider = showTopDivider;
    }

    public boolean isShowBottomDivider() {
        return showBottomDivider;
    }

    public void setShowBottomDivider(boolean showBottomDivider) {
        this.showBottomDivider = showBottomDivider;
    }
}
