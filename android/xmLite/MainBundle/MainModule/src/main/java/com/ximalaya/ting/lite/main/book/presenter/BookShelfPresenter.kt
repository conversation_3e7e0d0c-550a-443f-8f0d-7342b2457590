package com.ximalaya.ting.lite.main.book.presenter

import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.db.model.BookInfo
import com.ximalaya.ting.android.host.db.repository.BookRepository
import com.ximalaya.ting.android.host.db.repository.BookRecordRepository
import com.ximalaya.ting.android.host.db.utils.BookUtils
import com.ximalaya.ting.android.host.db.utils.BookUtils.TYPE_ADD
import com.ximalaya.ting.android.host.db.utils.BookUtils.TYPE_DEL
import com.ximalaya.ting.android.host.db.utils.BookUtils.TYPE_MODIFY
import com.ximalaya.ting.android.host.db.utils.BookUtils.operatingBooks
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt.Companion.instance
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.host.util.common.DeviceUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.lite.main.book.bean.BookShelfList
import com.ximalaya.ting.lite.main.book.view.IBookShelfView
import com.ximalaya.ting.lite.main.request.LiteUrlConstants
import org.json.JSONObject

class BookShelfPresenter(mView: IBookShelfView?) : IBookShelfPresenter {

    val TAG = "BookShelfPresenter"

    private var mCurPage = 1

    private var mIsLoadMore = false

    private val pageSize = 500

    /**
     *  同步书籍记录
     */
    private var isSyncBookRecord = false

    private var isLoadData = false;

    private val mIBookShelfView: IBookShelfView? = mView

    /**
     * 是否来自登录触发时机
     */
    var mIsFromLogin = false

    override fun loadData() {
        if (isLoadData) {
            FuliLogger.log(TAG, "用户书架数据请求中拦截")
            return;
        }
        isLoadData = true

        if (UserInfoMannage.hasLogined()) {
            FuliLogger.log(TAG, "请求用户书架数据")
            getBookList(mCurPage, object : IDataCallBack<BookShelfList> {
                override fun onSuccess(bookShelfList: BookShelfList?) {
                    if (bookShelfList?.pageList == null || bookShelfList.pageList.isEmpty()) {
                        FuliLogger.log(TAG, "用户书架数据-> 空")
                        resetLoadMore()
                        requestNoLoginBookData()
                    } else {
                        mIBookShelfView?.setTotalCount(bookShelfList.totalCount)
                        val serviceBookList = bookShelfList.pageList
                        // 设置书籍的用户id
                        for (bookInfo in serviceBookList) {
                            if (bookInfo != null) {
                                bookInfo.uid = BookUtils.currentUserId
                            }
                        }

                        // 处理其他设备删除的书籍数据
                        dealLoginUserDelBookData(serviceBookList, BookRepository.queryInUser(BookUtils.currentUserId))

                        // 使用服务端返回数据和本地数据作对比 找出新增数据
                        val addBookList: MutableList<BookInfo> = getAddBookData(serviceBookList, BookRepository.queryInUser(BookUtils.currentUserId))
                        performAddBookList(addBookList)
                        requestNoLoginBookData()
                        mIsLoadMore = false
                    }
                }

                override fun onError(code: Int, message: String) {
                    FuliLogger.log(TAG, "用户书架数据-> code:$code message:$message")
                    resetLoadMore()
                    requestNoLoginBookData()
                }
            })
        } else {
            requestNoLoginBookData()
        }
    }

    /**
     * 处理登录用户删除的书籍数据
     */
    private fun dealLoginUserDelBookData(serviceBookList: MutableList<BookInfo>, curUserBookList: MutableList<BookInfo>) {
        if (CollectionUtil.isNullOrEmpty(curUserBookList)) {
            return
        }

        val iterator = curUserBookList.iterator()
        while (iterator.hasNext()) {
            val curUserBookInfo = iterator.next()
            if (curUserBookInfo == null) {
                iterator.remove()
            } else {
                var index = -1
                if (CollectionUtil.isNotEmpty(serviceBookList)) {
                    for (serviceBookInfo in serviceBookList) {
                        if (serviceBookInfo == null) {
                            continue
                        }
                        // 线上数据和本地用户数据存在同一本书
                        if (curUserBookInfo.bookId == serviceBookInfo.bookId) {
                            index = 0
                            break
                        }
                    }
                }
                // 本地有这本书 线上没有这本书
                if (index == -1) {
                    val modifyBookInfo = BookRecordRepository.query(curUserBookInfo.bookId)
                    // 没有编辑记录 代表这本书在别的设备删除了  删除本地数据
                    if (modifyBookInfo == null) {
                        BookRepository.remove(curUserBookInfo.bookId)
                    } else if (modifyBookInfo.modifyType != TYPE_ADD && modifyBookInfo.modifyType == TYPE_MODIFY) {
                        // 存在编辑记录 但是不是添加和编辑类型  也直接删除这本书
                        BookRepository.remove(curUserBookInfo.bookId)
                    }
                }
            }
        }
    }

    override fun setFromLogin(fromLogin: Boolean) {
        this.mIsFromLogin = fromLogin
    }

    override fun loadMore() {
        mIsLoadMore = true
        mCurPage++
        loadData()
    }

    /**
     * 执行新增数据 需要处理删除记录
     *
     * @param addBookList 书籍列表
     */
    private fun performAddBookList(addBookList: MutableList<BookInfo>) {
        if (CollectionUtil.isNullOrEmpty(addBookList)) {
            return
        }
        val iterator = addBookList.iterator()
        while (iterator.hasNext()) {
            val bookInfo = iterator.next()
            if (bookInfo == null) {
                iterator.remove()
            } else {
                val modifyBookInfo = BookRecordRepository.query(bookInfo.bookId)
                // 该本书本地有删除记录则不添加
                if (modifyBookInfo != null && modifyBookInfo.modifyType == TYPE_DEL) {
                    iterator.remove()
                } else {
                    // 服务端返回数据 存本地标记用户
                    bookInfo.uid = BookUtils.currentUserId
                }
            }
        }

        // 新增书籍插入数据库
        if (CollectionUtil.isNotEmpty(addBookList)) {
            printBookList("新增书籍:", addBookList)
            BookRepository.insertOrReplace(addBookList)
        }
    }

    private fun getAddBookData(serviceBookList: MutableList<BookInfo>, curUserBookList: List<BookInfo>): MutableList<BookInfo> {
        // 无本地数据 全部是新增的
        if (CollectionUtil.isNullOrEmpty(curUserBookList)) {
            return serviceBookList
        }
        val needUpdateList: MutableList<BookInfo> = ArrayList()
        for (localBookInfo in curUserBookList) {
            if (localBookInfo == null) {
                continue
            }
            for (serviceBookInfo in serviceBookList) {
                if (serviceBookInfo != null && serviceBookInfo.bookId == localBookInfo.bookId) {
                    // 本地存在这本书  移除
                    serviceBookList.remove(serviceBookInfo)
                    var isNeedUpdateBook = false
                    // 同一本书取最近更新时间
                    if (serviceBookInfo.lastUpdatedTime > localBookInfo.lastUpdatedTime) {
                        localBookInfo.lastUpdatedTime = serviceBookInfo.lastUpdatedTime
                        isNeedUpdateBook = true
                    }
                    if (serviceBookInfo.bookState != localBookInfo.bookState) {
                        // 更新书籍状态
                        localBookInfo.bookState = serviceBookInfo.bookState
                        isNeedUpdateBook = true
                    }

                    if (isNeedUpdateBook) {
                        needUpdateList.add(localBookInfo)
                    }
                    break
                }
            }
        }

        // 更新书籍时间
        if (needUpdateList.isNotEmpty()) {
            printBookList("更新书籍时间或状态:", needUpdateList)
            BookRepository.update(needUpdateList)
        }

        // 返回这次新增的图书
        return serviceBookList
    }

    /**
     * 请求未登录书籍数据
     */
    private fun requestNoLoginBookData() {
        // 查询本地推荐数据
        val recommendBookList: MutableList<BookInfo> = BookRepository.queryInUser(BookUtils.defaultUId)
        var curUserBookList: MutableList<BookInfo> = arrayListOf()
        // 当前用户数据-已登录才有这个数据 (包含本地和线上的合并数据)
        if (UserInfoMannage.hasLogined()) {
            curUserBookList = BookRepository.queryInUser(BookUtils.currentUserId)
        }

        // 当前用户无数据(包括服务端和本地) 且本地无推荐数据  才请求线上推荐数据
        if (CollectionUtil.isNullOrEmpty(curUserBookList) && CollectionUtil.isNullOrEmpty(recommendBookList)) {
            FuliLogger.log(TAG, "无数据,请求线上推荐数据")
            val finalCurUserBookList = curUserBookList
            getRecommendBookList(object : IDataCallBack<BookShelfList> {
                override fun onSuccess(bookShelfList: BookShelfList?) {
                    if (bookShelfList?.pageList == null || bookShelfList.pageList.isEmpty()) {
                        FuliLogger.log(TAG, "线上无推荐数据")
                        dealBookData(recommendBookList, finalCurUserBookList)
                    } else {
                        val list = bookShelfList.pageList
                        for (bookInfo in list) {
                            if (bookInfo != null) {
                                // 推荐数据默认用户id=-1
                                bookInfo.uid = BookUtils.defaultUId
                            }
                        }
                        printBookList("插入推荐数据", list)
                        //将返回数据插入到数据库(推荐数据)  因为服务端会按照排序返回对应的时间,所以不用关心时间
                        BookRepository.insertOrReplace(list)
                        dealBookData(list, finalCurUserBookList)
                    }
                }

                override fun onError(code: Int, message: String) {
                    FuliLogger.log(TAG, "线上推荐数据 code: $code msg: $message")
                    dealBookData(recommendBookList, finalCurUserBookList)
                }
            })
        } else {
            dealBookData(recommendBookList, curUserBookList)
        }
    }

    /**
     * 处理本地数据以及用户数据
     *
     * @param recommendBookList   本地推荐数据
     * @param curUserBookList 用户数据(只有登录了才有)
     */
    private fun dealBookData(recommendBookList: MutableList<BookInfo>, curUserBookList: MutableList<BookInfo>) {
        // 本地数据为空
        if (CollectionUtil.isNullOrEmpty(recommendBookList)) {
            if (CollectionUtil.isNotEmpty(curUserBookList)) {
                // 将线上用户数据添加到本地  有线上数据说明用户登录了
                mergeBookData(recommendBookList, curUserBookList)

                mIBookShelfView?.setData(curUserBookList)
            } else {
                mIBookShelfView?.setData(null)
            }
        } else {
            // 本地有数据  服务端无数据  显示本地数据
            if (CollectionUtil.isNullOrEmpty(curUserBookList)) {
                // 用户已登录 本地书籍添加到该用户账号上  需要有"添加书籍操作"  未登录显示推荐列表即可
                if (UserInfoMannage.hasLogined()) {
                    mergeBookData(recommendBookList, curUserBookList)
                }
                mIBookShelfView?.setData(recommendBookList)
            } else {
                mergeBookData(recommendBookList, curUserBookList)
                val list: MutableList<BookInfo> = BookRepository.queryInUser(BookUtils.currentUserId)
                if (CollectionUtil.isNotEmpty(list)) {
                    mIBookShelfView?.setData(list)
                } else {
                    mIBookShelfView?.setData(null)
                }
            }
        }

        mIBookShelfView?.loadDataEnd()

        // 同步操作给服务端
        syncBookModifyRecord(mIsFromLogin)

        isLoadData = false
    }

    /**
     * 合并书籍数据
     *
     * @param recommendBookList   本地推荐数据
     * @param curUserBookList 本地用户数据+服务端返回数据
     */
    private fun mergeBookData(recommendBookList: MutableList<BookInfo>, curUserBookList: MutableList<BookInfo>) {
        if (CollectionUtil.isNullOrEmpty(recommendBookList) && CollectionUtil.isNullOrEmpty(curUserBookList)) {
            return
        }
        val needUpdateList: MutableList<BookInfo> = ArrayList()

        if (CollectionUtil.isNotEmpty(recommendBookList) && CollectionUtil.isNotEmpty(curUserBookList)) {
            // (只有远端数据这种情况不用处理  请求到数据后就会存入到数据库)
            // 远端本地都有数据  进行数据合并
            val iterator = curUserBookList.iterator()
            while (iterator.hasNext()) {
                val curUserBookInfo = iterator.next()
                if (curUserBookInfo == null) {
                    iterator.remove()
                } else {
                    for (recommendBookInfo in recommendBookList) {
                        if (recommendBookInfo == null) {
                            continue
                        }
                        // 本地推荐数据和用户数据存在同一本书
                        if (curUserBookInfo.bookId == recommendBookInfo.bookId) {
                            recommendBookList.remove(recommendBookInfo)
                            // 用户的本书操作时间小于本地时间  那才需要更新  (因为用户的数据都在数据库了)
                            if (curUserBookInfo.lastUpdatedTime < recommendBookInfo.lastUpdatedTime) {
                                curUserBookInfo.lastUpdatedTime = recommendBookInfo.lastUpdatedTime
                                needUpdateList.add(curUserBookInfo)
                            }
                            break
                        }
                    }
                }
            }
        }

        // 将剩余推荐数据添加到数据库
        if (recommendBookList.isNotEmpty()) {
            //  数据倒序遍历  因为第一本书才是最后新增的
            for (bookInfo in recommendBookList.reversed()) {
                if (bookInfo != null) {
                    //  清除本地推荐数据的主键id   不然更新后会把推荐数据覆盖  应该是新增一份当前用户的书籍数据
                    bookInfo.id = null
                    bookInfo.uid = BookUtils.currentUserId

                    // 插入新增记录
                    operatingBooks(bookInfo.bookId, bookInfo.bookName, bookInfo.bookCover, bookInfo.lastUpdatedTime, BookUtils.TYPE_ADD)
                }
            }
        }

        if (needUpdateList.isNotEmpty()) {
            // 更新书籍信息 更新归属者
            for (bookInfo in needUpdateList) {
                if (bookInfo != null) {
                    bookInfo.uid = BookUtils.currentUserId
                }
            }
            printBookList("更新推荐数据", needUpdateList)
            BookRepository.insertOrReplace(needUpdateList)
        }
    }

    private fun printBookList(msg: String, list: List<BookInfo>) {
        if (!ConstantsOpenSdk.isDebug) {
            return
        }
        if (CollectionUtil.isNotEmpty(list)) {
            for (bookInfo in list) {
                if (bookInfo != null) {
                    FuliLogger.log(TAG, msg + " bookId:" + bookInfo.bookId + " bookName:" +
                            bookInfo.bookName + " uid:" + bookInfo.uid + " bookState:" + bookInfo.bookState + " time:" + bookInfo.lastUpdatedTime)
                }
            }
        }
    }

    override fun getBookList(curPage: Int, callback: IDataCallBack<BookShelfList>) {
        // 未登录请求单独接口
        if (!UserInfoMannage.hasLogined()) {
            callback.onError(-1, "")
            return;
        }

        val map = mutableMapOf<String, String>()
        map["pageIndex"] = curPage.toString()
        map["pageSize"] = pageSize.toString()

        CommonRequestM.baseGetRequest(LiteUrlConstants.getBookShelfListUrl(), map, callback) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            if (ret == 0) {
                instance.toObjectOfType<BookShelfList>(json.optString("data"),
                        object : TypeToken<BookShelfList>() {}.type)
            } else null
        }
    }

    override fun getRecommendBookList(callback: IDataCallBack<BookShelfList>) {
        val map = mutableMapOf<String, String>()
        map["deviceId"] = DeviceUtil.getDeviceToken(BaseApplication.getMyApplicationContext())

        CommonRequestM.baseGetRequest(LiteUrlConstants.getDefaultBookShelfListUrl(), map, callback) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            if (ret == 0) {
                instance.toObjectOfType<BookShelfList>(json.optString("data"),
                        object : TypeToken<BookShelfList>() {}.type)
            } else null
        }
    }

    private fun syncBookModifyRecord(addBookList: String, modifiedBookList: String, delBookIdList: String,
                                     callback: IDataCallBack<Boolean>) {
        val map = mutableMapOf<String, String>()
        map.put("addBookList", addBookList)
        map.put("delBookIdList", delBookIdList)
        map.put("modifiedBookList", modifiedBookList)

        CommonRequestM.basePostRequest(LiteUrlConstants.syncBookModifyRecordUrl(), map, callback) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            ret == 0
        }
    }

    override fun syncBookModifyRecord(isFromLogin: Boolean) {
        // 专门同步一个类处理同步逻辑
        if (!UserInfoMannage.hasLogined()) {
            return;
        }

        if (isSyncBookRecord) {
            return
        }

        val list = BookRecordRepository.queryAll()
        if (CollectionUtil.isNotEmpty(list)) {
            isSyncBookRecord = true

            FuliLogger.log(TAG, "开始同步书籍编辑记录")

            val addBookListBuild = StringBuilder()
            val delBookListBuild = StringBuilder()
            val modifyBookListBuild = StringBuilder()
            val hashSet = mutableSetOf<Long>()

            list.forEach { modifyBookInfo ->
                modifyBookInfo.run {
                    // 同一本书 可能会有两条记录  一条是登录用户的 一条是未登录的  只保留最新的一条(列表是按照时间倒序排的)
                    if (!hashSet.contains(bookId)) {
                        hashSet.add(bookId)

                        when (modifyType) {
                            TYPE_ADD -> {
                                addBookListBuild.append("${bookId}_${lastUpdatedTime}").append(",")
                                FuliLogger.log(TAG, "上报新增记录 bookId: $bookId")
                            }
                            TYPE_MODIFY -> {
                                modifyBookListBuild.append("${bookId}_${lastUpdatedTime}").append(",")
                                FuliLogger.log(TAG, "上报编辑记录 bookId: $bookId")
                            }
                            TYPE_DEL -> {
                                delBookListBuild.append("$bookId").append(",")
                                FuliLogger.log(TAG, "上报删除记录 bookId: $bookId")
                            }
                            else -> {

                            }
                        }
                    } else {
                        FuliLogger.log(TAG, "存在同一本书记录,不处理: bookId: $bookId")
                    }
                }
            }

            var addBookListString = ""
            var modifyBookListString = ""
            var delBookListString = ""
            if (addBookListBuild.isNotEmpty()) {
                addBookListString = addBookListBuild.substring(0, addBookListBuild.length - 1)
            }
            if (modifyBookListBuild.isNotEmpty()) {
                modifyBookListString = modifyBookListBuild.substring(0, modifyBookListBuild.length - 1)
            }
            if (delBookListBuild.isNotEmpty()) {
                delBookListString = delBookListBuild.substring(0, delBookListBuild.length - 1)
            }

            syncBookModifyRecord(addBookListString, modifyBookListString, delBookListString, object : IDataCallBack<Boolean> {
                override fun onSuccess(result: Boolean?) {
                    isSyncBookRecord = false
                    result?.let {
                        if (it) {
                            BookRecordRepository.remove(list)
                        }

                        FuliLogger.log(TAG, "书籍记录同步结果 $it")
                    }
                }

                override fun onError(code: Int, message: String?) {
                    FuliLogger.log(TAG, "书籍记录同步结果 code:$code message:$message")
                    isSyncBookRecord = false
                }
            })
        }
    }

    private fun resetLoadMore() {
        if (mIsLoadMore) {
            mCurPage--
            mIsLoadMore = false
        }
    }
}