package com.ximalaya.ting.lite.main.home.fragment;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.TextView;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.util.RequestParamsUtil;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter;
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendAdapter;
import com.ximalaya.ting.lite.main.home.manager.HomeRecommendAdapterAddFloorManager;
import com.ximalaya.ting.lite.main.home.presenter.HomeRecommendContact;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel;
import com.ximalaya.ting.lite.main.manager.ApiEvnParamsManager;
import com.ximalaya.ting.lite.main.model.ListViewNoContentModel;
import com.ximalaya.ting.lite.main.model.album.CategoryRecommendMList;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;
import com.ximalaya.ting.lite.main.model.album.TanghuluHotWord;
import com.ximalaya.ting.lite.main.request.HttpParamsConstantsInMain;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.request.LiteUrlConstants;
import com.ximalaya.ting.lite.main.utils.OneKeyRadioUtil;
import com.ximalaya.ting.lite.main.view.LinearItemDecoration;
import com.ximalaya.ting.lite.main.view.RecyclerViewCanDisallowIntercept;
import com.ximalaya.ting.lite.main.vip.listener.AdapterDataSyncListener;
import com.ximalaya.ting.lite.main.vip.listener.ListViewNoContentRefreshListener;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页进入的某个分类的具体分类页面
 *
 * <AUTHOR>
 */
public class HomeCategoryRecommendFragment extends BaseFragment2 implements IXmPlayerStatusListener, HomeRecommendContact.IFragmentView {

    private static final String TAG = "HomeCategoryRecommendFr";

    private static final String KEY_CATEGORY_ID = "key_category_id";
    private static final String KEY_CATEGORY_TITLE = "key_category_title";


    private RecyclerViewCanDisallowIntercept rvFloatTanghuluHotWord;

    private RefreshLoadMoreListView mRefreshLoadMoreListView;
    private ListView mListView;

    private HomeRecommendAdapterAddFloorManager mAddFloorManager;
    private HomeRecommendAdapter mAdapter;
    private CategoryRecommendMList mData;
    private boolean mIsLoading;

    private int mCategoryId = -1;
    private String mCategoryTitle = "喜马拉雅";

    //是否是首次请求楼层列表，增加曝光埋点使用
    private boolean mIsFristRequest = true;
    private boolean mLoadMetaDataSuccess;
    private boolean lastItemIsFloatTanghuluHotWord;//最后一个是悬浮类型的糖葫芦

    /**
     * 注意，这个是热词糖葫芦在整个列表中的位置，当listView向上滚动超过整个位置的时候，要显示顶部悬浮的热词信息
     */
    private int hotWordTanghuluPosition = 0;

    private TanghuluHotWord mTanghuluHotWord;
    //标记选中的热词位置
    private int mVipHotWordSelectedPosition = -1;
    private int mPageId = 1;
    private List<TanghuluHotWord> mTanghuluHotWordList = new ArrayList<>();
    private MultiRecyclerAdapter<TanghuluHotWord, SuperRecyclerHolder> tanghuluHotWordAdapter;


    public static HomeCategoryRecommendFragment newInstance(int categoryId, String categoryTitle) {
        Bundle args = new Bundle();
        HomeCategoryRecommendFragment fragment = new HomeCategoryRecommendFragment();
        args.putInt(KEY_CATEGORY_ID, categoryId);
        args.putString(KEY_CATEGORY_TITLE, categoryTitle);
        fragment.setArguments(args);
        return fragment;
    }

    public HomeCategoryRecommendFragment() {
        super(AppConstants.isPageCanSlide, SlideView.TYPE_LINEARLAYOUT, null);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mCategoryId = arguments.getInt(KEY_CATEGORY_ID, -1);
            mCategoryTitle = arguments.getString(KEY_CATEGORY_TITLE, "");
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_category_recommend;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle(mCategoryTitle);
        rvFloatTanghuluHotWord = findViewById(R.id.main_rv_float_tanghulu_hot_word);
        rvFloatTanghuluHotWord.setDisallowInterceptTouchEventView((ViewGroup) (rvFloatTanghuluHotWord.getParent()));

        mRefreshLoadMoreListView = findViewById(R.id.main_listview);
        mRefreshLoadMoreListView.setSendScrollListener(false);
        mRefreshLoadMoreListView.setIsShowLoadingLabel(true);
        mRefreshLoadMoreListView.setMode(PullToRefreshBase.Mode.PULL_FROM_START);
        mRefreshLoadMoreListView.setOnRefreshLoadMoreListener(new IRefreshLoadMoreListener() {
            @Override
            public void onRefresh() {
                mLoadMetaDataSuccess = false;
                lastItemIsFloatTanghuluHotWord = false;
                mPageId = 1;
                mTanghuluHotWord = null;
                mTanghuluHotWordList.clear();
                if (tanghuluHotWordAdapter != null) {
                    tanghuluHotWordAdapter.notifyDataSetChanged();
                }
                loadDataFromNet();
                Logger.d("HomeCategoryRecommendFragment===", "下拉刷新了");
            }

            @Override
            public void onMore() {
                Logger.d("HomeCategoryRecommendFragment===", "加载更多了");
                if (mLoadMetaDataSuccess && lastItemIsFloatTanghuluHotWord) {
                    mPageId++;
                    loadBottomDataByType();
                } else {
                    loadRecommendFeedData();
                }
            }
        });
        mListView = mRefreshLoadMoreListView.getRefreshableView();
        mListView.setFocusable(false);
        mListView.setFocusableInTouchMode(false);
        mListView.setDividerHeight(0);
        mRefreshLoadMoreListView.addOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {

            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (getiGotoTop() != null) {
                    getiGotoTop().setState(firstVisibleItem > 5);
                }
                if (firstVisibleItem > hotWordTanghuluPosition && CollectionUtil.isNotEmpty(mTanghuluHotWordList)) {
                    rvFloatTanghuluHotWord.setVisibility(View.VISIBLE);
                } else {
                    rvFloatTanghuluHotWord.setVisibility(View.INVISIBLE);
                }
            }
        });

        if (getActivity() != null && !getActivity().isFinishing()) {
            mListView.setPadding(0, 0, 0, BaseUtil.dp2px(getActivity(), 70));
            mListView.setClipToPadding(false);
        }

        //记录选中的位置
        AdapterDataSyncListener<TanghuluHotWord> adapterDataSyncListener = new AdapterDataSyncListener<TanghuluHotWord>() {
            @Override
            public void syncData(@NotNull List<TanghuluHotWord> list) {
            }

            @Override
            public void syncDataOnClick(@NotNull List<TanghuluHotWord> list, int position, @NotNull TanghuluHotWord selectedWord) {
                Logger.d(TAG, "syncDataOnClick");
                setFloatTanghuluAdapter();
                if (position == mTanghuluHotWordList.size() - 1) {//全部跳转
                    startAllTypeFragment();
                } else {
                    mPageId = 1;
                    mTanghuluHotWord = selectedWord;
                    mVipHotWordSelectedPosition = position;
                    performClickByType();
                }
            }

            @Override
            public boolean isLoading() {
                return mIsLoading;
            }
        };

        ListViewNoContentRefreshListener listViewNoContentRefreshListener = new ListViewNoContentRefreshListener() {
            @Override
            public void onRefresh() {
                if (!canUpdateUi()) {
                    return;
                }
                loadBottomDataByType();
            }
        };

        //给adapter设置额外的参数
        HomeRecommendExtraViewModel extraViewModel = new HomeRecommendExtraViewModel();
        extraViewModel.categoryId = mCategoryId;
        extraViewModel.adapterDataSyncListener = adapterDataSyncListener;
        extraViewModel.refreshListener = listViewNoContentRefreshListener;

        mAdapter = new HomeRecommendAdapter(this, extraViewModel);
        mAdapter.setRecommendFeedItemActionListener(null);
        mAddFloorManager = new HomeRecommendAdapterAddFloorManager(mAdapter, this);
        mListView.setAdapter(mAdapter);
        hasLoadData = true;

        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
                if (!OneClickHelper.getInstance().onClick(view)) {
                    return;
                }
                int index = position - mListView.getHeaderViewsCount();
                if (index < 0 || index >= mAdapter.getCount()) {
                    return;
                }
                ItemModel itemModel = mAdapter.getItem(index);
                Object item = itemModel.getObject();
                if (!(item instanceof AlbumM)) {
                    return;
                }
                AlbumM album = (AlbumM) item;
                AlbumEventManage.startMatchAlbumFragment(album, AlbumEventManage.FROM_DISCOVERY_CATEGORY, 0, album.getRecSrc(), album.getRecTrack(), -1, getActivity());
            }
        });
        loadData();
    }

    @Override
    protected void loadData() {
        if (!canUpdateUi()) {
            return;
        }
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        loadDataFromNet();
    }

    private void loadDataFromNet() {
        if (mIsLoading) {
            return;
        }
        mIsLoading = true;
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_CATEGORY_ID, mCategoryId + "");
        params.put(HttpParamsConstants.PARAM_CONTENT_TYPE, "album");
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        params.put(HttpParamsConstants.PARAM_VERSION, DeviceUtil.getVersion(getActivity()));
        params.put("network", CommonRequestM.getInstanse().getNetWorkType());
        params.put("operator", NetworkType.getOperator(mContext) + "");
        params.put("deviceId", DeviceUtil.getDeviceToken(mContext));
        params.put("scale", "1");
        params.put("appid", "0");
        params.put(HttpParamsConstants.PARAM_CATEGORY_GENDER, "9");
        params.put("isHomepage", String.valueOf(true));
        // 添加上次播放的电台id传给服务端
        String lastRadioId = OneKeyRadioUtil.getLastRadiosStr(mContext);
        if (!TextUtils.isEmpty(lastRadioId)) {
            params.put("lastRadioId", lastRadioId);
        }
        params = RequestParamsUtil.addVipShowParam(params);
        if (UserInfoMannage.hasLogined()) {
            params.put(HttpParamsConstants.PARAM_UID, UserInfoMannage.getUid() + "");
        }
        IDataCallBack<CategoryRecommendMList> callback = new IDataCallBack<CategoryRecommendMList>() {
            @Override
            public void onSuccess(final CategoryRecommendMList object) {
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        mIsLoading = false;
                        if (!canUpdateUi()) {
                            return;
                        }
                        mRefreshLoadMoreListView.onRefreshComplete();
                        if (object == null) {
                            loadErrorPage();
                            return;
                        }
                        if (object.getList() == null || object.getList().size() == 0) {
                            loadErrorPage();
                            return;
                        }
                        mData = object;

                        List<MainAlbumMList> list = object.getList();
                        int size = list.size();
                        //第一个悬浮类型的糖葫芦的下标
                        int indexOfFirstFloatTanghuluHotWord = -1;
                        for (int i = 0; i < size; i++) {
                            MainAlbumMList mainAlbumMList = list.get(i);
                            if (MainAlbumMList.MODULE_TANGHULU_HOT_WORD == (mainAlbumMList.getModuleType())
                                    && MainAlbumMList.TANGHUYLU_HOT_WORD_FLOAT.equals(mainAlbumMList.getCardClass())) {
                                indexOfFirstFloatTanghuluHotWord = i;
                            }
                        }
                        if (indexOfFirstFloatTanghuluHotWord != -1 && indexOfFirstFloatTanghuluHotWord != size - 1) {//热词糖葫芦float类型的楼层，不是最后一个，直接显示错误界面
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            mRefreshLoadMoreListView.onRefreshComplete();
                            loadErrorPage();
                            return;
                        }

                        //最后一个热词糖葫芦float类型的楼层，不能为空列表
                        MainAlbumMList mainAlbumMList = list.get(size - 1);
                        if (MainAlbumMList.MODULE_TANGHULU_HOT_WORD == (mainAlbumMList.getModuleType())
                                && MainAlbumMList.TANGHUYLU_HOT_WORD_FLOAT.equals(mainAlbumMList.getCardClass())) {

                            lastItemIsFloatTanghuluHotWord = true;

                            if (CollectionUtil.isNullOrEmpty(mainAlbumMList.tanghuluHotWordList)) {
                                mLoadMetaDataSuccess = false;
                                mData = object;
                                mAddFloorManager.setDataForView(mData);
                                mRefreshLoadMoreListView.onRefreshComplete(false);
                                onPageLoadingCompleted(LoadCompleteType.OK);
                                return;
                            } else {
                                mTanghuluHotWordList = mainAlbumMList.tanghuluHotWordList;
                                setFloatTanghuluAdapter();
                                mTanghuluHotWord = mTanghuluHotWordList.get(0);
                                mVipHotWordSelectedPosition = 0;
                            }
                        }

                        mLoadMetaDataSuccess = true;
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        mAddFloorManager.setDataForView(mData);

                        //增加首屏曝光埋点
                        if (mIsFristRequest) {
                            AutoTraceHelper.scrollViewItemExposure(getFragment(), mListView);
                            mIsFristRequest = false;
                        }

                        if (lastItemIsFloatTanghuluHotWord) {
                            loadBottomDataByType();
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                mIsLoading = false;
                if (!canUpdateUi()) {
                    return;
                }
                onPageLoadingCompleted(LoadCompleteType.OK);
                mRefreshLoadMoreListView.onRefreshComplete();
                loadErrorPage();
            }
        };
        LiteCommonRequest.getCategoryRecommend(params, callback);
    }

    private void loadBottomDataByType() {
        if (mTanghuluHotWord == null) {
            return;
        }
        if (mIsLoading) {
            return;
        }
        String type = mTanghuluHotWord.itemType;
        if (TanghuluHotWord.ITEM_FEED.equals(type)) {
            loadTanghuluFeedStream();
        } else if (TanghuluHotWord.ITEM_KEY_WORD.equals(type)) {
            loadHotWordAlbum();

        } else if (TanghuluHotWord.ITEM_POOL.equals(type)) {
            loadContentPool();
        } else {
            mRefreshLoadMoreListView.onRefreshComplete(false);
            onPageLoadingCompleted(LoadCompleteType.OK);
        }
    }

    @Override
    public void onRefresh() {
        super.onRefresh();
        mRefreshLoadMoreListView.setRefreshing();
    }

    private void loadErrorPage() {
        //没有数据的时候，加载错误页面
        if (mAdapter == null) {
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
            return;
        }
        if (mAdapter.isEmpty()) {
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        }
    }

    //加载推荐信息流数据
    private void loadRecommendFeedData() {
        if (mAddFloorManager.getRecommendFeedChannelId() >= 0) {
            Map<String, String> params = new HashMap<>();
            params.put(HttpParamsConstants.PARAM_CATEGORY_ID, mCategoryId + "");
            params.put("channelId", mAddFloorManager.getRecommendFeedChannelId() + "");
            params.put("deviceId", DeviceUtil.getDeviceToken(mContext));
            params.put(HttpParamsConstants.PARAM_DEVICE, "android");
            params = RequestParamsUtil.addVipShowParam(params);
            LiteCommonRequest.getCategoryRecommendFeed(params, new IDataCallBack<List<RecommendItemNew>>() {
                @Override
                public void onSuccess(@Nullable List<RecommendItemNew> data) {
                    if (!canUpdateUi()) {
                        return;
                    }
                    if (data == null) {
                        return;
                    }
                    if (data.size() == 0) {
                        mRefreshLoadMoreListView.onRefreshComplete(false);
                        return;
                    }
                    mRefreshLoadMoreListView.onRefreshComplete(true);
                    mAddFloorManager.setRecommendFeedListForView(data);
                    mAdapter.notifyDataSetChanged();
                }

                @Override
                public void onError(int code, String message) {
                    if (!canUpdateUi()) {
                        return;
                    }
                    mRefreshLoadMoreListView.onRefreshComplete(true);
                }
            });
        }
    }

    /**
     * 加载信息流
     */
    private void loadTanghuluFeedStream() {
        TanghuluHotWord.ItemBean item = mTanghuluHotWord.item;
        if (item == null) {
            return;
        }
        mIsLoading = true;
        Map<String, String> params = new HashMap<>();
        params.put("channelId", "0");
        params.put("recIds", item.streamId);
        params.put("categoryId", String.valueOf(item.categoryId));
        params.put("size", DTransferConstants.DEFAULT_PAGE_SIZE + "");
        params.put("vipShow", "1");

        IDataCallBack<List<RecommendItemNew>> callBack = new IDataCallBack<List<RecommendItemNew>>() {
            @Override
            public void onSuccess(@Nullable final List<RecommendItemNew> list) {
                mIsLoading = false;
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        calculateHotWordTanghuluPosition();
                        if (CollectionUtil.isNullOrEmpty(list)) {
                            if (mPageId == 1) {
                                removePreviousHotWordAlum();
                                removeNoContentModel();
                                if (mAdapter != null) {
                                    mAddFloorManager.setNoContentModel(new ListViewNoContentModel());
                                    mAdapter.notifyDataSetChanged();
                                }
                            }
                            mRefreshLoadMoreListView.onRefreshComplete(false);
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            return;
                        }

                        if (mPageId == 1) {
                            removePreviousHotWordAlum();
                            removeNoContentModel();
                        }

                        if (CollectionUtil.isNullOrEmpty(list)) {
                            mRefreshLoadMoreListView.onRefreshComplete(false);
                        } else {
                            mRefreshLoadMoreListView.onRefreshComplete(true);
                        }
                        mAddFloorManager.setRecommendFeedListForView(list);
                        mAdapter.notifyDataSetChanged();
                        onPageLoadingCompleted(LoadCompleteType.OK);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                mIsLoading = false;
                mRefreshLoadMoreListView.onRefreshComplete(false);
                onPageLoadingCompleted(LoadCompleteType.OK);
                if (mPageId == 1) {
                    removePreviousHotWordAlum();
                    removeNoContentModel();
                    if (mAdapter != null) {
                        mAddFloorManager.setNoContentModel(new ListViewNoContentModel(true));
                        mAdapter.notifyDataSetChanged();
                    }
                }
                Logger.e(TAG, "code:" + code + "  message:" + message);
            }
        };
        LiteCommonRequest.getTanghuluRecommendFeed(params, callBack);

    }

    /**
     * 加载热词
     */
    private void loadHotWordAlbum() {
        TanghuluHotWord.ItemBean item = mTanghuluHotWord.item;
        if (item == null) {
            return;
        }
        mIsLoading = true;
        Map<String, String> params = new HashMap<>();
        int categoryId = item.categoryId;
        params.put(HttpParamsConstantsInMain.PARAM_CATEGORY_ID, String.valueOf(categoryId));
        params.put(HttpParamsConstantsInMain.PARAM_KEYWORD_ID, String.valueOf(item.keywordId));
        params.put(HttpParamsConstantsInMain.PARAM_DEVICE, "android");
        params.put(HttpParamsConstantsInMain.PARAM_VERSION, DeviceUtil.getVersion(getActivity()));
        params.put(HttpParamsConstants.PARAM_PAGE_ID, String.valueOf(mPageId));
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        params.put("scale", "1");
        params.put("network", CommonRequestM.getInstanse().getNetWorkType());
        params.put("operator", NetworkType.getOperator(mContext) + "");
        params.put("deviceId", DeviceUtil.getDeviceToken(mContext));
        params.put("appid", "0");
        params.put("vipShow", "1");
        params.put("vipPage", "0");
        if (UserInfoMannage.hasLogined()) {
            params.put("uid", UserInfoMannage.getUid() + "");
        }
        IDataCallBack<ListModeBase<AlbumM>> callBack = new IDataCallBack<ListModeBase<AlbumM>>() {
            @Override
            public void onSuccess(@Nullable final ListModeBase<AlbumM> object) {
                mIsLoading = false;
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        calculateHotWordTanghuluPosition();
                        if (object == null || CollectionUtil.isNullOrEmpty(object.getList())) {
                            if (mPageId == 1) {
                                removePreviousHotWordAlum();
                                removeNoContentModel();
                                if (mAdapter != null) {
                                    mAddFloorManager.setNoContentModel(new ListViewNoContentModel());
                                    mAdapter.notifyDataSetChanged();
                                }
                            }
                            mRefreshLoadMoreListView.onRefreshComplete(false);
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            return;
                        }

                        if (mPageId == 1) {
                            removePreviousHotWordAlum();
                            removeNoContentModel();
                        }

                        if (CollectionUtil.isNullOrEmpty(object.getList())) {
                            mRefreshLoadMoreListView.onRefreshComplete(false);
                        } else {
                            mRefreshLoadMoreListView.onRefreshComplete(true);
                        }
                        mAddFloorManager.setAlbumLists(object.getList());
                        mAdapter.notifyDataSetChanged();
                        onPageLoadingCompleted(LoadCompleteType.OK);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                mIsLoading = false;
                mRefreshLoadMoreListView.onRefreshComplete();
                if (mPageId == 1) {
                    removePreviousHotWordAlum();
                    removeNoContentModel();
                    if (mAdapter != null) {
                        mAddFloorManager.setNoContentModel(new ListViewNoContentModel(true));
                        mAdapter.notifyDataSetChanged();
                    }
                }
                Logger.e(TAG, "code:" + code + "  message:" + message);
            }
        };
        LiteCommonRequest.getAlbumsByMetadata(params, callBack);
    }

    /**
     * 加载内容池
     */
    private void loadContentPool() {
        TanghuluHotWord.ItemBean item = mTanghuluHotWord.item;
        if (item == null) {
            return;
        }
        mIsLoading = true;
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstantsInMain.PARAM_UID, String.valueOf(UserInfoMannage.getUid()));
        params.put("poolId", String.valueOf(item.poolId));
        params.put(HttpParamsConstants.PARAM_PAGE_ID, String.valueOf(mPageId));
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        IDataCallBack<List<AlbumM>> callBack = new IDataCallBack<List<AlbumM>>() {
            @Override
            public void onSuccess(@Nullable final List<AlbumM> list) {
                mIsLoading = false;
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        calculateHotWordTanghuluPosition();
                        if (CollectionUtil.isNullOrEmpty(list)) {
                            if (mPageId == 1) {
                                removePreviousHotWordAlum();
                                removeNoContentModel();
                                if (mAdapter != null) {
                                    mAddFloorManager.setNoContentModel(new ListViewNoContentModel());
                                    mAdapter.notifyDataSetChanged();
                                }
                            }
                            mRefreshLoadMoreListView.onRefreshComplete(false);
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            return;
                        }

                        if (mPageId == 1) {
                            removePreviousHotWordAlum();
                            removeNoContentModel();
                        }

                        if (CollectionUtil.isNullOrEmpty(list)) {
                            mRefreshLoadMoreListView.onRefreshComplete(false);
                        } else {
                            mRefreshLoadMoreListView.onRefreshComplete(true);
                        }
                        mAddFloorManager.setAlbumLists(list);
                        mAdapter.notifyDataSetChanged();
                        onPageLoadingCompleted(LoadCompleteType.OK);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                mIsLoading = false;
                mRefreshLoadMoreListView.onRefreshComplete();
                if (mPageId == 1) {
                    removePreviousHotWordAlum();
                    removeNoContentModel();
                    if (mAdapter != null) {
                        mAddFloorManager.setNoContentModel(new ListViewNoContentModel(true));
                        mAdapter.notifyDataSetChanged();
                    }
                }
                Logger.e(TAG, "code:" + code + "  message:" + message);
            }
        };
        String url = LiteUrlConstants.getContentPoolUrl() + "/" + System.currentTimeMillis();
        LiteCommonRequest.getContentPoolData(url,params, callBack);

    }

    private void initVipHotWordAdapter() {
        if (rvFloatTanghuluHotWord.getAdapter() == null) {
            rvFloatTanghuluHotWord.setLayoutManager(new LinearLayoutManager(mActivity, LinearLayoutManager.HORIZONTAL, false));
            rvFloatTanghuluHotWord.addItemDecoration(new LinearItemDecoration(
                    BaseUtil.dp2px(mContext, 10),
                    BaseUtil.dp2px(mContext, 16)
            ));
            tanghuluHotWordAdapter = new MultiRecyclerAdapter<TanghuluHotWord, SuperRecyclerHolder>(mActivity, mTanghuluHotWordList) {
                @Override
                public SuperRecyclerHolder createMultiViewHolder(Context mCtx, @NonNull View itemView, int viewType) {
                    return SuperRecyclerHolder.createViewHolder(mCtx, itemView);
                }

                @Override
                public void onBindMultiViewHolder(SuperRecyclerHolder holder, TanghuluHotWord tanghuluHotWord, int viewType, int position) {
                    if (tanghuluHotWord.selected &&
                            (tanghuluHotWord.itemType.equals(TanghuluHotWord.ITEM_FEED)
                                    || tanghuluHotWord.itemType.equals(TanghuluHotWord.ITEM_POOL)
                                    || tanghuluHotWord.itemType.equals(TanghuluHotWord.ITEM_KEY_WORD)
                            )
                    ) {
                        holder.setBackgroundResource(R.id.rlHotWord, R.drawable.main_bg_f39699_dp18);
                        holder.setTextColorResource(R.id.tvHotWordTitle, R.color.host_color_e83f46);
                    } else {
                        holder.setBackgroundResource(R.id.rlHotWord, R.drawable.main_bg_dddddd_stroke_dp18);
                        holder.setTextColorResource(R.id.tvHotWordTitle, R.color.main_color_333333);
                    }

                    TanghuluHotWord.ItemBean itemBean = tanghuluHotWord.item;

                    String keywordName = "";
                    if (itemBean != null) {
                        keywordName = itemBean.name;
                    }
                    holder.setText(R.id.tvHotWordTitle, keywordName);

                    if (TanghuluHotWord.NAME_ALL.equals(keywordName)) {
                        holder.setVisibility(R.id.ivAllHotWord, View.VISIBLE);
                    } else {
                        holder.setVisibility(R.id.ivAllHotWord, View.GONE);
                    }

                    holder.setOnItemClickListenner(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (mIsLoading) {
                                return;
                            }
                            Logger.d(TAG, "position = " + position);
                            if (position == mVipHotWordSelectedPosition) {
                                //点击了全部还是要跳转的
                                if (mVipHotWordSelectedPosition == mTanghuluHotWordList.size() - 1) {
                                    startAllTypeFragment();
                                }
                                return;
                            }
                            if (CollectionUtil.isNotEmpty(mTanghuluHotWordList) && position < mTanghuluHotWordList.size()) {
                                mVipHotWordSelectedPosition = position;
                                Logger.d(TAG, "vipHotWordClickedPosition = " + mVipHotWordSelectedPosition);
                                TanghuluHotWord clickedWord = mTanghuluHotWordList.get(position);
                                mTanghuluHotWord = clickedWord;

                                if (position != mTanghuluHotWordList.size() - 1) {
                                    for (TanghuluHotWord hotWord : mTanghuluHotWordList) {
                                        hotWord.selected = false;
                                    }
                                    clickedWord.selected = true;
                                }
                                tanghuluHotWordAdapter.notifyDataSetChanged();

                                mListView.smoothScrollToPositionFromTop(hotWordTanghuluPosition + 1, 0);
                                //点击了全部
                                if (mVipHotWordSelectedPosition == mTanghuluHotWordList.size() - 1) {
                                    startAllTypeFragment();
                                    return;
                                }
                                mPageId = 1;
                                performClickByType();
                            }
                        }
                    });
                }

                @Override
                public int getMultiItemViewType(TanghuluHotWord model, int position) {
                    return 0;
                }

                @Override
                public int getMultiItemLayoutId(int viewType) {
                    return R.layout.main_item_hot_word_single_line_item;
                }
            };
            rvFloatTanghuluHotWord.setAdapter(tanghuluHotWordAdapter);
        }
    }

    private void performClickByType() {
        if (mTanghuluHotWord == null || mTanghuluHotWord.item == null) {
            return;
        }
        if (mIsLoading) {
            return;
        }
        String itemType = mTanghuluHotWord.itemType;

        if (TanghuluHotWord.ITEM_H5.equals(itemType) || TanghuluHotWord.ITEM_UTING.equals(itemType)) {
            String link = mTanghuluHotWord.item.link;
            if (!TextUtils.isEmpty(link)) {
                ToolUtil.clickUrlAction(this, link, null);
            }
        } else {
            loadBottomDataByType();
        }
    }

    private void setFloatTanghuluAdapter() {
        if (tanghuluHotWordAdapter == null) {
            initVipHotWordAdapter();
        } else {
            tanghuluHotWordAdapter.setValueList(mTanghuluHotWordList);
            tanghuluHotWordAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 删除前一个热词分类下面的数据
     */
    private void removePreviousHotWordAlum() {
        List<ItemModel> bodyData = getBodyData();
        if (CollectionUtil.isNotEmpty(bodyData)) {
            for (int i = bodyData.size() - 1; i >= 0; i--) {
                ItemModel itemModel = bodyData.get(i);
                if (itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_VIP_HOT_WORD_ALBUM
                        || itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_TRACK
                        || itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_TRACK_STYLE_V2
                        || itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM
                        || itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V2
                        || itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V3
                        || itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V4
                ) {
                    bodyData.remove(i);
                }
            }
        }
    }

    /**
     * 删除没有内容的布局文件
     */
    private void removeNoContentModel() {
        List<ItemModel> bodyData = getBodyData();
        if (CollectionUtil.isNotEmpty(bodyData)) {
            for (int i = bodyData.size() - 1; i >= 0; i--) {
                ItemModel itemModel = bodyData.get(i);
                if (itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_LIST_VIEW_NO_CONTENT) {
                    bodyData.remove(i);
                }
            }
        }
    }

    /**
     * 计算热词糖葫芦的位置，
     */
    private void calculateHotWordTanghuluPosition() {
        List<ItemModel> modelList = getBodyData();
        if (CollectionUtil.isNullOrEmpty(modelList)) {
            hotWordTanghuluPosition = 0;
        } else {
            for (int i = 0; i < modelList.size(); i++) {
                ItemModel model = modelList.get(i);
                if (model.getViewType() == HomeRecommendAdapter.VIEW_TYPE_TANGHULU_FLOAT_HOT_WORD) {
                    //热词糖葫芦滑出以后才可见，所以要加1
                    hotWordTanghuluPosition = i + 1;
                    break;
                }
            }
        }
        Logger.d(TAG, "hotWordTanghuluPosition =" + hotWordTanghuluPosition);
    }

    /**
     * 跳转到筛选页面
     */
    public void startAllTypeFragment() {
        HomeCategoryContentTabFragment fragment = new HomeCategoryContentTabFragment();
        Bundle argumentDef = HomeCategoryContentTabFragment.createArguments(mCategoryId, 0, true);
        fragment.setArguments(argumentDef);
        startFragment(fragment);
    }

    private List<ItemModel> getBodyData() {
        if (mAdapter != null) {
            return mAdapter.getListData();
        }
        return null;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(mTopBtnListener);
        }
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
        XmPlayerManager.getInstance(mContext).addPlayerStatusListener(this);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (!canUpdateUi()) {
            return;
        }
        if (isVisibleToUser && isResumed()) {
            if (getiGotoTop() != null) {
                getiGotoTop().addOnClickListener(mTopBtnListener);
            }
            XmPlayerManager.getInstance(mContext).addPlayerStatusListener(this);
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        } else {
            XmPlayerManager.getInstance(mContext).removePlayerStatusListener(this);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
        XmPlayerManager.getInstance(mContext).removePlayerStatusListener(this);
    }

    @Override
    protected String getPageLogicName() {
        return "HomeCategoryRecommendFragment";
    }

    @Override
    protected void onNoContentButtonClick(View view) {
    }

    @Override
    public void onDestroyView() {
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
        XmPlayerManager.getInstance(mContext).removePlayerStatusListener(this);

        if (mAddFloorManager != null) {
            mAddFloorManager.onDestroy();
        }
        super.onDestroyView();
    }

    private final IGotoTop.IGotoTopBtnClickListener mTopBtnListener = new IGotoTop.IGotoTopBtnClickListener() {
        @Override
        public void onClick(View v) {
            Logger.d("HomeRecommendFragment", "回到顶部");
            if (!isRealVisable()) {
                return;
            }
            if (mListView == null) {
                return;
            }
            mListView.setSelection(0);
        }
    };

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        super.setTitleBar(titleBar);
        TitleBar.ActionType action = new TitleBar.ActionType("action", TitleBar.RIGHT, R.string.main_string_empty_str, R.drawable.main_icon_title_bar_right_search_black, R.color.main_color_999999, TextView.class);
        action.setFontSize(14);
        titleBar.addAction(action, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                BaseFragment fragment = SearchActionRouter.getInstance().getFragmentAction() != null ? SearchActionRouter.getInstance().getFragmentAction().newSearchFragmentByHotWord(SearchActionRouter.TYPE_CATEGORY, ApiEvnParamsManager.getHomeCategoryId(), null) : null;
                if (fragment != null) {
                    startFragment(fragment);
                }
            }
        });
        titleBar.update();
        View view = titleBar.getActionView("action");
        if (view != null) {
            view.setVisibility(View.VISIBLE);
            view.setPadding(BaseUtil.dp2px(getActivity(), 7), 0, BaseUtil.dp2px(getActivity(), 5), 0);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mAdapter != null) {
            mAdapter.release();
        }
    }

    @Override
    public void onPlayStart() {
        playStatueChange();
    }

    @Override
    public void onPlayPause() {
        playStatueChange();
    }

    @Override
    public void onPlayStop() {
        playStatueChange();
    }

    @Override
    public void onSoundPlayComplete() {
        playStatueChange();
    }

    @Override
    public void onSoundPrepared() {

    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        playStatueChange();
    }

    @Override
    public void onBufferingStart() {

    }

    @Override
    public void onBufferingStop() {

    }

    @Override
    public void onBufferProgress(int percent) {

    }

    @Override
    public void onPlayProgress(int currPos, int duration) {

    }

    @Override
    public boolean onError(XmPlayerException exception) {
        playStatueChange();
        return false;
    }

    private void playStatueChange() {
        if (mAdapter != null && canUpdateUi()) {
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void setHasMore(boolean hasMore) {
        if (!lastItemIsFloatTanghuluHotWord) {
            mRefreshLoadMoreListView.setHasMore(hasMore);
        }
    }

    @Override
    public RefreshLoadMoreListView getRefreshLoadMoreListView() {
        return mRefreshLoadMoreListView;
    }

    @Override
    public HomeRecommendAdapterAddFloorManager getHomeRecommendAdapterAddFloorManager() {
        return mAddFloorManager;
    }

    public Fragment getFragment() {
        return this;
    }
}

