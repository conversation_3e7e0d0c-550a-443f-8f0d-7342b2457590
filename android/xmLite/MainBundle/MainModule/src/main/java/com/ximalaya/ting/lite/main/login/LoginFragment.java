/**
 * LoginFragment.java
 * com.ximalaya.ting.android.host.mSmsLoginFragment.other.login
 * <p/>
 * Function： TODO
 * <p/>
 * ver     date      		author
 * ──────────────────────────────────
 * 2015-11-25 		jack.qin
 * <p/>
 * Copyright (c) 2015, TNT All Rights Reserved.
 */
package com.ximalaya.ting.lite.main.login;

import android.os.Bundle;
import android.view.View;

import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.LoginBundleParamsManager;
import com.ximalaya.ting.android.host.manager.login.LoginPageTraceManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * <AUTHOR> on 2017/6/29.
 */
public class LoginFragment extends BaseLoginFragment {

    private LoginView mLoginView;
    private LoginArgeementView mLoginAgreementView;

    private final ILoginViewHandle mLoginViewHandle = new ILoginViewHandle() {
        @Override
        public boolean checkAgreementSelectedAndShowHint() {
            if (mLoginAgreementView == null) {
                return true;
            }
            return mLoginAgreementView.checkAgreementSelectedAndShowHint();
        }
    };

    @SuppressWarnings("NullAway")
    public LoginFragment() {
        super(true, null);
    }

    public static LoginFragment newInstance(Bundle bundle) {
        LoginFragment loginFragment = new LoginFragment();
        loginFragment.setArguments(bundle);
        return loginFragment;
    }

    @Override
    protected String getPageLogicName() {
        return "login";
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_login;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_head_layout;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle(LoginBundleParamsManager.getLoginTitle(getArguments()));

        initView();

        LoginPageTraceManager.traceLoginPageShow(false, true);
    }


    private void initView() {
        mLoginAgreementView = findViewById(R.id.main_view_login_agreement_view);
        mLoginAgreementView.initUi(getActivity(), getArguments());

        //初始化登录控件
        mLoginView = findViewById(R.id.main_layout_login_view);
        mLoginView.initUi(this, getArguments(), mLoginViewHandle);

        //切换监听
        mLoginView.setShowLayoutListener(new LoginViewShowLayoutCallback() {
            @Override
            public void showLayoutPage(int showLayoutType) {
                if (showLayoutType == LoginView.TYPE_SHOW_LAYOUT_INPUT_PHONE) {
                    mLoginAgreementView.setVisibility(View.VISIBLE);
                } else {
                    mLoginAgreementView.setVisibility(View.INVISIBLE);
                }
            }
        });

        new XMTraceApi.Trace()
                .pageView(11509, "logIn")
                .put("currPage", "logIn")
                .put("oneKeyLogin", "false")
                .put("screenType", "fullScreen")
                .createTrace();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        new XMTraceApi.Trace()
                .pageExit2(11510)
                .put("oneKeyLogin", "false")
                .put("screenType", "fullScreen")
                .createTrace();
    }

    @Override
    protected void loadData() {
    }

    @Override
    public void finish() {
        super.finish();
        if (mActivity != null) {
            mActivity.finish();
            mActivity.overridePendingTransition(0, 0);
        }
    }

    @Override
    public void onMyResume() {
        tabIdInBugly = 38370;
        super.onMyResume();
        if (mLoginView != null) {
            mLoginView.onMyResume();
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mLoginView != null) {
            mLoginView.onPause();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        UserInfoMannage.fromLoginUrl = null;
        if (mLoginView != null) {
            mLoginView.onDestroy();
        }
    }

    @Override
    public boolean onBackPressed() {
        //登录view拦截返回操作
        if (mLoginView != null && mLoginView.onBackPressed()) {
            return true;
        }
        return super.onBackPressed();
    }
}
