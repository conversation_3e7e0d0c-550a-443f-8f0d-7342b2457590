package com.ximalaya.ting.lite.main.download.utils;

/**
 * <AUTHOR> feiwen
 * date   : 2019/5/17
 * desc   :
 */
public class SpeedHelper {
    private double lastSpeed = 0;
    /**
     *  开始计算的那一刻
     */
    private long startTick = 0;
    /**
     * 每次计算的时间间隔
     */
    private long intervalTime = 0;

    public void start() {
        intervalTime = startTick = System.currentTimeMillis();
    }

    public double calculate(long done) {
        // 每一秒计算一次，所以在首次一秒内，都会0.
        long interval = System.currentTimeMillis() - intervalTime;
        if (interval < 1000) {
            return lastSpeed;
        }
        intervalTime = System.currentTimeMillis();
        long spendTime = intervalTime - startTick;
        double curr = spendTime > 0 ? done * 1000.0 / spendTime : 0;
        lastSpeed = (curr + lastSpeed) / 2;
        return lastSpeed;
    }
}
