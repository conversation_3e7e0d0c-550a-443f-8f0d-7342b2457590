package com.ximalaya.ting.lite.main.album.fragment;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.astuetz.PagerSlidingTabStrip;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.request.ApiErrorToastManager;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.album.adapter.RankAlbumListFragment;
import com.ximalaya.ting.lite.main.model.rank.GroupRankInfo;
import com.ximalaya.ting.lite.main.model.rank.GroupRankTabInfo;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 单一分类排行榜
 *
 * <AUTHOR> on 2016/12/2.
 */
public class GroupRankSingleFragment extends BaseFragment2 implements View.OnClickListener {
    private PagerSlidingTabStrip mTabs;
    private ViewPager mPager;
    private TabCommonAdapter mAdapter;
    private TextView mTVTitle;
    private ImageView mIVBack;
    private long mRankClusterId;

    private List<TabCommonAdapter.FragmentHolder> mFragmentList = new CopyOnWriteArrayList<>();

    public GroupRankSingleFragment() {
        super(true, null);
    }

    /**
     * @param rankClusterId 聚合榜id或分类id，聚合榜id
     */
    public static GroupRankSingleFragment getInstance(long rankClusterId) {
        GroupRankSingleFragment fragment = new GroupRankSingleFragment();
        Bundle bundle = new Bundle();
        bundle.putLong(BundleKeyConstants.KEY_RANK_CLUSTER_ID, rankClusterId);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mRankClusterId = arguments.getLong(BundleKeyConstants.KEY_RANK_CLUSTER_ID, -1);
        }
    }

    @Override
    protected String getPageLogicName() {
        return mTVTitle.getText() == null ? "" : mTVTitle.getText().toString();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mTabs = (PagerSlidingTabStrip) findViewById(R.id.main_tabs);
        mPager = (ViewPager) findViewById(R.id.main_content);
        mTabs.setTabPaddingLeftRight(BaseUtil.dp2px(getActivity(), 17));
        mTabs.setDisallowInterceptTouchEventView((ViewGroup) mTabs.getParent());

        mTVTitle = (TextView) findViewById(R.id.main_tv_title);
        mIVBack = (ImageView) findViewById(R.id.main_iv_back);

        mIVBack.setOnClickListener(this);
        AutoTraceHelper.bindData(mIVBack, "");
        mPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageSelected(int page) {
                Logger.log("GroupRank" + "onPageSelected" + page);
                if (getSlideView() != null) {
                    if (page == 0) {
                        getSlideView().setSlide(true);
                    } else {
                        getSlideView().setSlide(false);
                    }
                }
            }

            @Override
            public void onPageScrolled(int arg0, float arg1, int arg2) {
                Logger.log("GroupRank" + "onPageScrolled");
            }

            @Override
            public void onPageScrollStateChanged(int arg0) {
                Logger.log("GroupRank" + "onPageScrollStateChanged");
            }
        });
    }

    @Override
    protected void loadData() {
        requestRankTabData();
    }

    /**
     * 请求排行榜tab数据
     */
    private void requestRankTabData() {
        HashMap<String, String> params = new HashMap<>();
        params.put("device", "android");
        params.put("version", DeviceUtil.getVersion(mContext));
        params.put("rankClusterId", mRankClusterId + "");
        params.put("deviceId", DeviceUtil.getDeviceToken(getActivity()));
        //该接口增加极速版标识
        params.put("speed", "1");
        LiteCommonRequest.getRankGroupInfo(params, new IDataCallBack<GroupRankInfo>() {
            @Override
            public void onSuccess(@Nullable final GroupRankInfo object) {
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        if (object == null || object.rankingList == null) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }
                        List<GroupRankTabInfo> tabList = object.rankingList;
                        int rankTabSize = tabList.size();
                        //没有请求到tab数据，失败
                        if (rankTabSize == 0) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }
                        //为了快速开发，和服务端协商的是，不管返回什么只去第一个tab，第一个tab在主app这边是免费榜
                        //只使用免费榜的数据，如果主app接口发生变动或者出现付费等异常情况，服务端根据params.put("speed", "1");对该接口做版本控制

                        mTVTitle.setText(object.title);
                        GroupRankTabInfo groupRankTabInfo = tabList.get(0);
                        if (groupRankTabInfo == null) {
                            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            return;
                        }
                        long rankingListId = groupRankTabInfo.rankingListId;
                        String tabName = groupRankTabInfo.displayName;
                        String rankTitle = object.title;
                        Bundle argmentBundle = RankAlbumListFragment.getArgumentBundle(rankingListId, tabName, rankTitle);
                        TabCommonAdapter.FragmentHolder recommendHolder = new TabCommonAdapter.FragmentHolder(RankAlbumListFragment.class, tabName, argmentBundle);
                        mFragmentList.add(recommendHolder);


                        //下面注释的是支持多个榜tab，如果要使用，需要注意服务端进行接口版本控制
//                        for (int i = 0; i < tabList.size(); i++) {
//                            GroupRankTabInfo groupRankTabInfo = tabList.get(i);
//                            if (groupRankTabInfo == null) {
//                                continue;
//                            }
//                            long rankingListId = groupRankTabInfo.rankingListId;
//                            String tabName = groupRankTabInfo.displayName;
//                            String rankTitle = object.title;
//                            Bundle argmentBundle = RankAlbumListFragment.getArgumentBundle(rankingListId, tabName, rankTitle);
//                            TabCommonAdapter.FragmentHolder recommendHolder = new TabCommonAdapter.FragmentHolder(RankAlbumListFragment.class, tabName, argmentBundle);
//                            mFragmentList.add(recommendHolder);
//                        }
                        mAdapter = new TabCommonAdapter(getChildFragmentManager(), mFragmentList);

                        //只有一个的情况下，不展示tab
                        mTabs.setVisibility(mFragmentList.size() < 2 ? View.GONE : View.VISIBLE);
                        //4个内，平分布局，超过4个往后排列
                        mTabs.setShouldExpand(rankTabSize < 5);
                        mPager.setAdapter(mAdapter);
                        mTabs.setViewPager(mPager);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                ApiErrorToastManager.showToast(code, TextUtils.isEmpty(message) ? "网络异常，请重试" : message);
            }
        });
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_group_rank_single;
    }

    @Override
    public void onClick(View v) {
        int i1 = v.getId();
        if (i1 == R.id.main_iv_back) {
            finishFragment();
        }
    }
}
