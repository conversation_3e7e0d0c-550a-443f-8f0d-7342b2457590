package com.ximalaya.ting.lite.main.comment.entities

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 *  @Author: <PERSON><PERSON><PERSON>
 *  @Mail: <EMAIL>
 *  @CreateTime: 12/28/21
 *
 *  @Description: 评论列表数据实体类
 */
@Parcelize
data class CommentUserBean(
    var avatar: String? = "",
    var nickname: String? = "",
    var uid: Long = 0,
    var isVip: Boolean? = null    //是否VIP，暂仅主评论有标识
) : Parcelable

// region Comment
@Parcelize
data class CommentListBean(
    var pageId: Long = 0,
    var pageSize: Long = 0,
    var maxPageId: Long = 0,
    var totalCount: Long = 0,
    var allCommentTotalCount: Long = 0,
    var dataList: List<CommentListItemBean>? = null
) : Parcelable

@Parcelize
data class CommentListItemBean(
    var commentId: Long = 0,        //评论ID
    var content: String? = "",      //评论内容
    var createTime: Long = 0,       //创建时间
    var likeCount: Long = 0,        //点赞数
    var likeStatus: Boolean = false,//可以自己点赞
    var isCanDelete: Boolean = false,//是否可删除
    var status: Long = 0,           //状态：0-待审核；1-正常；其他-无效
    var user: CommentUserBean? = null,      //评论用户信息

    // 评论字段
    var replyCount: Long = 0,        //回复数
    var replys: ArrayList<CommentListItemBean>? = null,
    // 回复字段
    var parentCommentId: Long? = -1,  //上级评论Id
    var parentUser: CommentUserBean? = null,      //上级评论用户信息

    var isExpanded: Boolean = false,
    var score: Double = 0.0,
    var pNickName: String? = null,
    var parentUid: Long = 0,
    var replyId: Long = 0,
    var commentUid: Long = 0,
    var parentReplyId: Long = 0,
    var parentReplyNickName: String? = null,
    var parentReplyUid: Long = 0,
    var region: String? = null

) : Parcelable


@Parcelize
data class LikeItemBean(
    var likeCount: Long = 0,
    var likeStatus: Boolean = false
) : Parcelable

//endregion
