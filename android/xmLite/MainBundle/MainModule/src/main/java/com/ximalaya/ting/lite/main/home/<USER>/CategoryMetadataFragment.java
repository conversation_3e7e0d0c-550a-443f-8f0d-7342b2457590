package com.ximalaya.ting.lite.main.home.fragment;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AbsListView;
import android.widget.AbsListView.OnScrollListener;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.handmark.pulltorefresh.library.PullToRefreshBase.Mode;
import com.ximalaya.ting.android.framework.adapter.AbstractAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.SlideView;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.category.CategoryMetadata;
import com.ximalaya.ting.android.host.util.RequestParamsUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.base.GotoTopFragment;
import com.ximalaya.ting.lite.main.base.album.AlbumAdapter;
import com.ximalaya.ting.lite.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.home.view.ChooseMetadataView;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分类页，点击“全部”tab显示该页面
 *
 * <AUTHOR>
 */
@SuppressLint("ValidFragment")
public class CategoryMetadataFragment extends GotoTopFragment implements OnClickListener,
        IRefreshLoadMoreListener, ChooseMetadataView.OnMetadataChangeListener {
    public static final int METADATA_FOLD = 0;
    public static final int METADATA_UNFOLD = 1;
    public static final String BUNDLE_KEY_SELECTED_METADATA = "metadataStr";

    public static final String bestHotCondition = "最火";
    public static final String bestNewCondition = "最新";
    public static final String classicCondition = "经典";

    private int mCategoryId = -1;
    private int mSelectMetadataId = -1;
    private int mSelectMetadataValueId = -1;
    private int from = -1;
    private String mCalDimension = bestHotCondition;
    private int mPageId = 1;
    private final List<String> metadataList = new ArrayList<>();
    private RefreshLoadMoreListView mListView;
    private AbstractAdapter mAdapter;
    private FrameLayout mHeadPanelContainer;
    private FrameLayout mPullDownMenuContainer;
    private TextView tvMetadata;
    private ChooseMetadataView mChooseMetadataView;
    private String metadataParam;
    private LinearLayout layoutNoContent;
    private ImageView mIvHint;
    private boolean mLastItemVisible;
    private boolean isLoading = false;
    private boolean hasMore = true;
    private boolean mIsPullDownMenuShowing = false;
    private View mRlTitleBar; //状态栏

    //listview只能在metadata加载成功后才能加载数据
    private boolean mLoadMetaDataSuccess = false;

    private final List<Album> albumMList = new ArrayList<>();
    private boolean mNeedTitleBar = false; //是否需要标题栏，默认是不需要，默认在首页tab使用

    public CategoryMetadataFragment() {
    }

    public CategoryMetadataFragment(boolean canSlide) {
        super(canSlide, null);  //该页面不能支持侧滑，否则无法正常滑动该页面内的分类信息
    }

    /**
     * 在tab下使用，所需要的argument
     */
    public static Bundle createArgumentFromTab(int categoryId) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putBoolean(BundleKeyConstantsInMain.KEY_NEED_TITLE_BAR, false);
        return bundle;
    }

    /**
     * 在tab下使用，所需要的argument
     */
    public static Bundle createArgumentFromTab(int categoryId, int metadataId, int metadataValueId) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_METADATA_ID, metadataId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_METADATA_VALUE_ID, metadataValueId);
        bundle.putBoolean(BundleKeyConstantsInMain.KEY_NEED_TITLE_BAR, false);
        return bundle;
    }

    /**
     * 在tab下使用，所需要的argument
     */
    public static Bundle createArgumentFromTab(int categoryId, int metadataId, int metadataValueId,
                                               int from) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_METADATA_ID, metadataId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_METADATA_VALUE_ID, metadataValueId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_FROM, from);
        bundle.putBoolean(BundleKeyConstantsInMain.KEY_NEED_TITLE_BAR, false);
        return bundle;
    }

    /**
     * 作为单一页面使用，所需要的的argument
     */
    public static Bundle createArgumentFromSinglePage(int categoryId, int from) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleKeyConstantsInMain.KEY_CATEGORY_ID, categoryId);
        bundle.putInt(BundleKeyConstantsInMain.KEY_FROM, from);
        bundle.putBoolean(BundleKeyConstantsInMain.KEY_NEED_TITLE_BAR, true);
        return bundle;
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        if (arguments != null) {
            mCategoryId = arguments.getInt(BundleKeyConstants.KEY_CATEGORY_ID, -1);
            mNeedTitleBar = arguments.getBoolean(BundleKeyConstantsInMain.KEY_NEED_TITLE_BAR, false);
            mSelectMetadataId = arguments.getInt(BundleKeyConstantsInMain.KEY_METADATA_ID, -1);
            mSelectMetadataValueId = arguments.getInt(BundleKeyConstantsInMain.KEY_METADATA_VALUE_ID, -1);
            from = arguments.getInt(BundleKeyConstants.KEY_FROM, -1);
        }
        //需要标题栏，设置为可滑动返回
        setCanSlided(mNeedTitleBar);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRlTitleBar = findViewById(R.id.main_title_bar);
        mPullDownMenuContainer = (FrameLayout) findViewById(R.id.main_fl_pull_down_menu_container);
        metadataList.add(bestHotCondition);// 默认条件是“最火"，必加
        tvMetadata = (TextView) findViewById(R.id.main_tv_metadatas);
        mListView = (RefreshLoadMoreListView) findViewById(R.id.main_listview);
        mListView.setOnRefreshLoadMoreListener(this);
        mAdapter = new AlbumAdapter(mActivity, albumMList);

        mChooseMetadataView = new ChooseMetadataView(getActivity());
        mChooseMetadataView.setFrom(ChooseMetadataView.FROM_CATEGORY);
        mChooseMetadataView.setCategoryId(mCategoryId + "");
        mChooseMetadataView.addMetadataChangeListener(this);
        Fragment parentFragment = getParentFragment();
        if (parentFragment instanceof HomeCategoryContentTabFragment) {
            HomeCategoryContentTabFragment fragment = (HomeCategoryContentTabFragment) parentFragment;
            mChooseMetadataView.setSlideView(fragment.getSlideView());
        } else {
            mChooseMetadataView.setSlideView(getSlideView());
        }
        mHeadPanelContainer = new FrameLayout(mContext);
        mHeadPanelContainer.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        mHeadPanelContainer.addView(mChooseMetadataView);

        mListView.getRefreshableView().addHeaderView(mHeadPanelContainer);

        layoutNoContent = new LinearLayout(mContext);
        layoutNoContent.setLayoutParams(new AbsListView.LayoutParams(AbsListView.LayoutParams.MATCH_PARENT, AbsListView.LayoutParams.WRAP_CONTENT));
        layoutNoContent.setGravity(Gravity.CENTER);
        mIvHint = new ImageView(mContext);
        mIvHint.setPadding(0, BaseUtil.dp2px(mContext, 30), 0, 0);
        mIvHint.setImageResource(R.drawable.main_bg_meta_nocontent);
        layoutNoContent.addView(mIvHint);
        layoutNoContent.setVisibility(View.GONE);
        mIvHint.setVisibility(View.GONE);
        mListView.getRefreshableView().addFooterView(layoutNoContent);
        mListView.setAdapter(mAdapter);
        mListView.getRefreshableView().setOnScrollListener(new OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                if (scrollState == OnScrollListener.SCROLL_STATE_IDLE && mLastItemVisible && !isLoading && hasMore) {
                    showPullDowWindow(false);
                    loadData();
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (firstVisibleItem <= 1) {
                    mPullDownMenuContainer.setVisibility(View.INVISIBLE);
                } else {
                    mPullDownMenuContainer.setVisibility(View.VISIBLE);
                    showPullDowWindow(false);
                }
                mLastItemVisible = (totalItemCount > 0) && (firstVisibleItem + visibleItemCount >= totalItemCount - 1);
                if (getiGotoTop() != null) {
                    getiGotoTop().setState(firstVisibleItem > 12);
                }
            }
        });

        findViewById(R.id.main_fl_pull_down_menu_container).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                showPullDowWindow(true);
            }
        });
        AutoTraceHelper.bindData(findViewById(R.id.main_fl_pull_down_menu_container), "");
        mListView.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
                if (!OneClickHelper.getInstance().onClick(view)) {
                    return;
                }
                if (mAdapter.getListData() == null) {
                    return;
                }
                int index = (int) id;
                if (index < 0 || index >= mAdapter.getListData().size()) {
                    return;
                }
                AlbumM albumM = (AlbumM) mAdapter.getListData().get(index);
                if (albumM == null) {
                    return;
                }
                AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_DISCOVERY_CATEGORY, ConstantsOpenSdk.PLAY_FROM_FIND_CLASSIFICATION, albumM.getRecSrc(), albumM.getRecTrack(), -1, getActivity());
            }
        });
        onPageLoadingCompleted(BaseFragment.LoadCompleteType.LOADING);

        //设置状态栏展示
        if (mNeedTitleBar) {
            mRlTitleBar.setVisibility(View.VISIBLE);
            setTitle("全部");

            //单独页面，滑动冲突处理
            SlideView slideView = getSlideView();
            if (slideView != null) {
                mChooseMetadataView.setSlideView(slideView);
            }
        }
    }

    private void showPullDowWindow(boolean showPullDowWindow) {
        boolean isNoShow = showPullDowWindow == mIsPullDownMenuShowing || (showPullDowWindow && isLoading);
        if (isNoShow) {
            //正在刷新页面时不显示下拉弹窗
            return;
        }
        if (mIsPullDownMenuShowing) {
            mIsPullDownMenuShowing = false;
            mPullDownMenuContainer.removeView(mChooseMetadataView);
            mChooseMetadataView.showFoldButton(true);
            mChooseMetadataView.showBottomDivider(true);
            mHeadPanelContainer.addView(mChooseMetadataView);
            mChooseMetadataView.setBackgroundColor(Color.TRANSPARENT);
        } else {
            mIsPullDownMenuShowing = true;
            mHeadPanelContainer.removeView(mChooseMetadataView);
            mChooseMetadataView.setFold(false);
            mChooseMetadataView.showBottomDivider(false);
            mChooseMetadataView.showFoldButton(false);
            mPullDownMenuContainer.addView(mChooseMetadataView, mPullDownMenuContainer.getChildCount() - 1);
            mChooseMetadataView.setBackgroundColor(Color.WHITE);
        }
    }

    /**
     * 加载页面数据，需要保证加载分类数据以后才可以请求
     */
    void loadPageListData() {
        if (isLoading) {
            return;
        }

        final Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_CATEGORY_ID, mCategoryId + "");
        if (!TextUtils.isEmpty(metadataParam)) {
            params.put(HttpParamsConstants.PARAM_METADATAS, metadataParam);
        }
        params.put(HttpParamsConstants.PARAM_CALC_DIMENSION, mCalDimension);
        params.put(HttpParamsConstants.PARAM_PAGE_ID, mPageId + "");
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        params.put(HttpParamsConstants.PARAM_VERSION, DeviceUtil.getVersion(mContext));
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        params.put("deviceId", DeviceUtil.getDeviceToken(getActivity()));
        //该接口增加极速版标识
        RequestParamsUtil.addVipShowParam(params);
        if (from == BundleValueConstantsInMain.FROM_VIP_PAGE) {
            params.put("vipPage", "1");
        }
        isLoading = true;
        final IDataCallBack<ListModeBase<AlbumM>> callback = new IDataCallBack<ListModeBase<AlbumM>>() {
            @Override
            public void onSuccess(final ListModeBase<AlbumM> object) {
                if (!canUpdateUi()) {
                    return;
                }
                isLoading = false;
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        if (object == null) {
                            if (albumMList.size() == 0) {
                                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                            }
                            return;
                        }
                        if (mPageId == 1) {
                            albumMList.clear();
                            mAdapter.notifyDataSetChanged();
                        }
                        List<AlbumM> reqList = object.getList();

                        int dataSize = (reqList != null ? reqList.size() : 0) + albumMList.size();

                        //空页面
                        if (dataSize == 0) {
                            mAdapter.notifyDataSetChanged();
                            mListView.setHasMoreNoFooterView(false);
                            mListView.setMode(Mode.DISABLED);
                            layoutNoContent.setVisibility(View.VISIBLE);
                            mIvHint.setImageResource(R.drawable.main_bg_meta_nocontent);
                            mIvHint.setVisibility(View.VISIBLE);
                            return;
                        }

                        boolean isFirstPage = mPageId == 1;

                        if (object.getMaxPageId() > mPageId) {
                            mPageId++;
                            mListView.onRefreshComplete(true);
                            hasMore = true;
                        } else {
                            mListView.onRefreshComplete(false);
                            mListView.setHasMoreNoFooterView(false);
                            hasMore = false;
                        }
                        if (reqList != null) {
                            albumMList.addAll(reqList);
                        }
                        mAdapter.notifyDataSetChanged();
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                isLoading = false;
                if (!canUpdateUi()) {
                    return;
                }
                onPageLoadingCompleted(LoadCompleteType.OK);
                if (albumMList.size() == 0) {
                    onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                } else {
                    CustomToast.showFailToast(R.string.main_network_error);
                }
            }
        };
        LiteCommonRequest.getAlbumsByMetadata(params, callback);
    }

    /**
     * 加载分类数据
     */
    private void loadMeteData() {
        if (isLoading) {
            return;
        }
        final Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_CATEGORY_ID, mCategoryId + "");
        params.put(HttpParamsConstants.PARAM_CHANNEL, DeviceUtil.getChannelInApk(getActivity()));
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        params.put(HttpParamsConstants.PARAM_VERSION, DeviceUtil.getVersion(getActivity()));
        params.put("deviceId", DeviceUtil.getDeviceToken(getActivity()));
        //该接口增加极速版标识
        if (from == BundleValueConstantsInMain.FROM_VIP_PAGE) {
            params.put("speed", "1");
        } else {
            params.put("speed", "2");
        }
        isLoading = true;
        onPageLoadingCompleted(BaseFragment.LoadCompleteType.LOADING);
        LiteCommonRequest.getCategoryMetadatas(0, params,  //getCategoryMetadatas会修改服务端返回的数据，以适配搜索直达需求
                new IDataCallBack<List<CategoryMetadata>>() {
                    @Override
                    public void onSuccess(final List<CategoryMetadata> object) {
                        isLoading = false;
                        if (object == null) {
                            mLoadMetaDataSuccess = false;
                            if (canUpdateUi()) {
                                onPageLoadingCompleted(BaseFragment.LoadCompleteType.NOCONTENT);
                            }
                            return;
                        }
                        mLoadMetaDataSuccess = true;
                        if (!canUpdateUi()) {
                            return;
                        }
                        doAfterAnimation(new IHandleOk() {
                            @Override
                            public void onReady() {
                                mChooseMetadataView.setMetadata(object, mSelectMetadataId, mSelectMetadataValueId);
                            }
                        });
                    }

                    @Override
                    public void onError(int code, String message) {
                        isLoading = false;
                        if (!canUpdateUi()) {
                            return;
                        }
                        onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                    }
                });
    }


    private boolean isLoadingDataed = false;

    @Override
    protected void loadData() {
        if (!canUpdateUi()) return;

        //如果没有请求到分类数据，先请求分类数据
        //已经存在分类数据以后再进行请求页面数据
        Logger.d("CategoryMetadateFragment", "loadData");
        mListView.setMode(Mode.PULL_FROM_START);
        if (getUserVisibleHint()) {
            if (mLoadMetaDataSuccess) {
                isLoadingDataed = true;
                loadPageListData();
            } else {
                loadMeteData();
            }
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (!isLoadingDataed && isVisibleToUser && isResumed()) {
            loadData();
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_category_metadata;
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
    }

    @Override
    public void onRefresh() {
        mPageId = 1;
        loadData();
    }

    @Override
    public void onMore() {

    }

    @Override
    public void onMetadataChange(String calDimension, String metadataRequestParam, String hintStr) {
        showPullDowWindow(false);

        mListView.getRefreshableView().setSelection(0);

        mCalDimension = calDimension;
        metadataParam = metadataRequestParam;
        tvMetadata.setText(hintStr);
        Logger.d("CategoryMetadateFragment", "onMetadataChange=" + calDimension + "   " + metadataRequestParam + "   " + hintStr);

        mPageId = 1;
        loadData();
    }

    @Override
    public void gotoTop() {
        if (mListView == null) {
            return;
        }
        mListView.getRefreshableView().setSelection(0);
    }

    @Override
    public void onPageLoadingCompleted(BaseFragment.LoadCompleteType loadCompleteType) {
        switch (loadCompleteType) {
            case OK:
            case LOADING:
                super.onPageLoadingCompleted(loadCompleteType);
                mIvHint.setVisibility(View.GONE);
                break;
            case NOCONTENT:
                super.onPageLoadingCompleted(BaseFragment.LoadCompleteType.OK);
                mIvHint.setImageResource(R.drawable.main_bg_meta_nocontent);
                mIvHint.setVisibility(View.VISIBLE);
                break;
            case NETWOEKERROR:
                super.onPageLoadingCompleted(BaseFragment.LoadCompleteType.OK);
                CustomToast.showFailToast(R.string.main_network_error);
                mIvHint.setImageResource(R.drawable.host_no_net);
                mIvHint.setVisibility(View.VISIBLE);
                break;
            default:
                break;
        }
    }
}
