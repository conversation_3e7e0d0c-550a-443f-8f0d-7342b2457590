package com.ximalaya.ting.lite.main.home.adapter;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.host.adapter.recyclerview.MultiRecyclerAdapter;
import com.ximalaya.ting.android.host.adapter.recyclerview.SuperRecyclerHolder;
import com.ximalaya.ting.lite.main.model.album.RecommendAlbumItem;

import java.util.List;

/**
 * Created by qinhuifeng on 2020/11/25
 *
 * <AUTHOR>
 */
public class HomeFreeAlbumTrackRvAdapter extends MultiRecyclerAdapter<TrackM, SuperRecyclerHolder> {
    public Activity mActivity;
    public RecommendAlbumItem mRecommendAlbumItem;

    public HomeFreeAlbumTrackRvAdapter(Activity mCtx, List<TrackM> mValueList) {
        super(mCtx, mValueList);
        mActivity = mCtx;
    }

    @Override
    public SuperRecyclerHolder createMultiViewHolder(Context mCtx, @NonNull View itemView, int viewType) {
        return SuperRecyclerHolder.createViewHolder(mCtx, itemView);
    }

    @Override
    public void onBindMultiViewHolder(SuperRecyclerHolder holder, TrackM trackM, int viewType, int position) {
        if (trackM == null) {
            return;
        }
        ImageView playAnimationFlag = (ImageView) holder.getViewById(R.id.main_playing_flag);
        TextView trackTitle = (TextView) holder.getViewById(R.id.main_sound_name);
        trackTitle.setText(trackM.getTrackTitle());
        boolean isCurrentTrack = (PlayTools.isPlayCurrTrackById(getContext(), trackM.getDataId()));
        XmPlayerManager xManager = XmPlayerManager.getInstance(mActivity);
        if (isCurrentTrack) {
            trackTitle.setSingleLine(false);
            trackTitle.setMaxLines(2);
            trackTitle.setEllipsize(TextUtils.TruncateAt.END);
            trackTitle.setTextColor(0xFFE83F46);
            if (xManager.isPlaying()) {
                playAnimationFlag.setImageResource(R.drawable.host_anim_play_flag_v2);
                if (playAnimationFlag.getDrawable() instanceof AnimationDrawable) {
                    final AnimationDrawable animationDrawable = (AnimationDrawable) playAnimationFlag.getDrawable();
                    playAnimationFlag.post(new Runnable() {
                        @Override
                        public void run() {
                            if (animationDrawable == null) {
                                return;
                            }
                            if (animationDrawable.isRunning()) {
                                return;
                            }
                            animationDrawable.start();
                        }
                    });
                }
            } else {
                playAnimationFlag.setImageResource(R.drawable.host_play_flag_wave_v2_11);
            }
        } else {
            trackTitle.setMaxLines(1);
            trackTitle.setSingleLine(true);
            trackTitle.setEllipsize(TextUtils.TruncateAt.END);
            trackTitle.setTextColor(0xFF666666);
            playAnimationFlag.setImageResource(R.drawable.main_icon_album_track_item_play);
        }
        holder.setOnItemClickListenner(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HomeRecommendFeedAlbumStyleV3AdapterProvider.playAlbumTrack(mActivity, mRecommendAlbumItem, position, true);
            }
        });
    }

    @Override
    public int getMultiItemViewType(TrackM model, int position) {
        return 0;
    }

    @Override
    public int getMultiItemLayoutId(int viewType) {
        return R.layout.main_item_home_album_track_item;
    }

    public void updateRecommendAlbumItem(RecommendAlbumItem item) {
        mRecommendAlbumItem = item;
    }
}
