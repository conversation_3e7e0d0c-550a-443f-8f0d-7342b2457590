package com.ximalaya.ting.lite.main.home.adapter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel;
import com.ximalaya.ting.lite.main.model.album.CategoryRecommendRefresh;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;

import java.util.List;

/**
 * 换一换
 *
 * <AUTHOR> on 2018/3/16.
 */
public class HomeRecommendRefreshProvider implements IMulitViewTypeViewAndData<HomeRecommendRefreshProvider.RefreshHolder, CategoryRecommendRefresh> {

    @NonNull
    private BaseFragment2 mFragment;

    @NonNull
    private IRefreshContext mRefreshContext;

    private HomeRecommendExtraViewModel extraViewModel;

    public HomeRecommendRefreshProvider(@NonNull BaseFragment2 fragment, @NonNull IRefreshContext context,
                                        HomeRecommendExtraViewModel extraViewModel) {
        mFragment = fragment;
        mRefreshContext = context;
        this.extraViewModel = extraViewModel;
    }

    @Override
    public void bindViewDatas(final RefreshHolder holder, final ItemModel<CategoryRecommendRefresh> t, View convertView, final int position) {
        if (convertView == null || t == null || t.getObject() == null || !(t.getObject() instanceof CategoryRecommendRefresh)) {
            return;
        }
        final CategoryRecommendRefresh refreshAndMore = t.getObject();
        if (!refreshAndMore.isRefreshing) {
            AnimationUtil.stopAnimation(holder.refreshImg);
        } else {
            AnimationUtil.rotateView(mFragment.getActivity(), holder.refreshImg);
        }
        holder.refreshLay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CategoryRecommendRefresh recommendRefresh = t.getObject();
                if (recommendRefresh == null) {
                    return;
                }
                if (recommendRefresh.dataPool == null) {
                    return;
                }
                if (recommendRefresh.albumMList == null) {
                    return;
                }
                AnimationUtil.rotateView(mFragment.getActivity(), holder.refreshImg);
                refreshAndMore.isRefreshing = true;
                IDataCallBack<List<AlbumM>> callBack = new IDataCallBack<List<AlbumM>>() {
                    @Override
                    public void onSuccess(List<AlbumM> object) {
                        AnimationUtil.stopAnimation(holder.refreshImg);
                        refreshAndMore.isRefreshing = false;
                        mRefreshContext.onRefreshSuccess(object, t.getObject(), position);
                    }

                    @Override
                    public void onError(int code, String message) {
                        AnimationUtil.stopAnimation(holder.refreshImg);
                        refreshAndMore.isRefreshing = false;
                    }
                };
                int moduleType = recommendRefresh.albumMList.getModuleType();
                //猜你喜欢使用新的接口,其他推荐使用老的接口
                if (moduleType == MainAlbumMList.MODULE_PERSONAL_RECOMMEND) {
                    int from = 0;
                    if (extraViewModel != null) {
                        from = extraViewModel.from;
                    }
                    recommendRefresh.dataPool.refreshGuessYouLikeLiteNew(from, callBack);
                } else {
                    recommendRefresh.dataPool.refresh(callBack);
                }
            }
        });
        if (refreshAndMore.albumMList != null) {
            AutoTraceHelper.bindData(holder.refreshLay, refreshAndMore.albumMList.getModuleType() + "", refreshAndMore.albumMList);
        }
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_refresh_guessyoulike, parent, false);
    }

    @Override
    public RefreshHolder buildHolder(View convertView) {
        return new RefreshHolder(convertView);
    }

    public static class RefreshHolder extends HolderAdapter.BaseViewHolder {
        View rootView;
        final View refreshLay;
        final ImageView refreshImg;

        RefreshHolder(View convertView) {
            rootView = convertView.findViewById(R.id.main_rl_root_container);
            refreshLay = convertView.findViewById(R.id.main_refresh_lay);
            refreshImg = convertView.findViewById(R.id.main_iv);
        }
    }

    public interface IRefreshContext {

        /**
         * 无需任何内容的刷新成功回调
         */
        void onRefreshSuccess();

        /**
         * 刷新成功的回调
         */
        void onRefreshSuccess(@Nullable List<AlbumM> newData, CategoryRecommendRefresh refreshAndMore, int finalI);
    }
}