package com.ximalaya.ting.lite.main.book

import com.ximalaya.ting.android.host.db.model.BookHistoryInfo
import com.ximalaya.ting.android.host.db.model.BookInfo
import com.ximalaya.ting.android.host.db.utils.BookHistoryUtils
import com.ximalaya.ting.android.host.db.utils.BookUtils
import com.ximalaya.ting.android.host.db.utils.BookUtils.TYPE_ADD
import com.ximalaya.ting.android.host.db.utils.BookUtils.TYPE_DEL
import com.ximalaya.ting.android.host.db.utils.BookUtils.TYPE_MODIFY
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IBookAction
import com.ximalaya.ting.lite.main.book.presenter.BookShelfPresenter
import com.ximalaya.ting.lite.main.book.presenter.IBookShelfPresenter
import com.ximalaya.ting.lite.main.history.presenter.BookHistoryPresenter
import com.ximalaya.ting.lite.main.history.presenter.IBookHistoryPresenter

class BookActionImpl : IBookAction {

    private var mIBookShelfPresenter: IBookShelfPresenter = BookShelfPresenter(null)

    private var mIBookHistoryPresenter: IBookHistoryPresenter = BookHistoryPresenter(null)

    override fun addBook(bookId: Long, bookName: String, bookCover: String) {
        BookUtils.operatingBooks(bookId, bookName, bookCover, TYPE_ADD)
        // 编辑数据上报
        mIBookShelfPresenter.syncBookModifyRecord()
    }

    override fun delBook(bookId: Long) {
        BookUtils.operatingBooks(bookId, "", "", TYPE_DEL)
        // 编辑数据上报
        mIBookShelfPresenter.syncBookModifyRecord()
    }

    override fun readBook(bookId: Long) {
        BookUtils.operatingBooks(bookId, "", "", TYPE_MODIFY)
        // 编辑数据上报
        mIBookShelfPresenter.syncBookModifyRecord()
    }

    override fun queryBookOnTheShelf(bookId: Long): BookInfo? {
        return BookUtils.queryBooks(bookId)
    }

    override fun addBookHistory(bookId: Long, bookName: String, bookCover: String, readChapterId: Long, isLastPage : Boolean) {
        BookHistoryUtils.operatingBooks(bookId, bookName, bookCover, readChapterId, isLastPage, TYPE_ADD)

        mIBookHistoryPresenter.syncBookHistoryModifyRecord()
    }

    override fun queryBookHistory(bookId: Long): BookHistoryInfo? {
        return BookHistoryUtils.queryBookHistory(bookId)
    }

    override fun delBookHistory(bookId: Long) {
        BookHistoryUtils.operatingBooks(bookId, "", "", 0L, false , TYPE_DEL)

        mIBookHistoryPresenter.syncBookHistoryModifyRecord()
    }

    override fun onLogin() {
        try {
            // 登录触发  需要提示用户
            mIBookShelfPresenter.setFromLogin(true)
            // 加载用户书架数据 (以及同步操作记录给服务端)   用于阅读器判断是否加入书架
            mIBookShelfPresenter.loadData()

            // 同步书籍历史操作记录  (以及同步历史操作记录给服务端)   用于阅读器获取阅读章节
            mIBookHistoryPresenter.loadData()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun queryBookHistoryAmount(): Int {
        return mIBookHistoryPresenter.queryBookHistoryAmount()
    }

    override fun getRecentHistory(): BookHistoryInfo? {
        return try {
            mIBookHistoryPresenter.getRecentHistory()
        }catch (e:Exception){
            null
        }
    }
}