package com.ximalaya.ting.lite.main.history.presenter

import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.host.db.model.BookHistoryInfo
import com.ximalaya.ting.android.host.db.model.ModifyBookHistoryInfo
import com.ximalaya.ting.android.host.db.repository.BookHistoryRecordRepository
import com.ximalaya.ting.android.host.db.repository.BookHistoryRepository
import com.ximalaya.ting.android.host.db.repository.SkitsHistoryRepository
import com.ximalaya.ting.android.host.db.utils.BookHistoryUtils
import com.ximalaya.ting.android.host.db.utils.BookUtils
import com.ximalaya.ting.android.host.listenertask.FuliLogger
import com.ximalaya.ting.android.host.listenertask.JsonUtilKt
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.common.CollectionUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.lite.main.history.bean.BookHistoryList
import com.ximalaya.ting.lite.main.history.view.IBookHistoryView
import com.ximalaya.ting.lite.main.request.LiteUrlConstants
import org.json.JSONObject

class BookHistoryPresenter(val mBookHistoryView: IBookHistoryView?) : IBookHistoryPresenter {

    private val TAG = "BookHistoryPresenter"

    private var lastTimeMillis = 0L

    private var mIsLoadMore = false

    private val pageSize = 200

    private var isLoadData = false

    /**
     *  同步书籍记录
     */
    private var isSyncBookRecord = false

    private var allRequest = 0

    override fun loadData() {
        if (isLoadData) {
            FuliLogger.log(TAG, "用户阅读历史数据请求中拦截")
            return;
        }
        isLoadData = true

        if (UserInfoMannage.hasLogined()) {
            FuliLogger.log(TAG, "请求用户阅读历史数据")
            getBookHistoryList(lastTimeMillis, pageSize, object : IDataCallBack<BookHistoryList> {
                override fun onSuccess(bookHistoryList: BookHistoryList?) {
                    if (bookHistoryList?.dataList == null || bookHistoryList.dataList.isEmpty()) {
                        FuliLogger.log(TAG, "用户历史数据-> 空")
                        requestNoLoginBookData()
                    } else {
                        mBookHistoryView?.setTotalCount(bookHistoryList.totalCount)
                        val serviceBookList = bookHistoryList.dataList
                        // 设置书籍的用户id
                        for (bookInfo in serviceBookList) {
                            if (bookInfo != null) {
                                bookInfo.uid = BookUtils.currentUserId
                            }
                        }

                        // 处理其他设备删除的书籍历史数据
                        dealLoginUserDelBookHistoryData(serviceBookList, BookHistoryRepository.queryInUser(BookUtils.currentUserId))

                        // 使用服务端返回数据和本地数据作对比 找出新增数据
                        val addBookList: MutableList<BookHistoryInfo> = getAddBookData(serviceBookList, BookHistoryRepository.queryInUser(BookUtils.currentUserId))
                        performAddBookList(addBookList)
                        requestNoLoginBookData()
                    }
                    resetLoadMore()
                }

                override fun onError(code: Int, message: String) {
                    FuliLogger.log(TAG, "用户历史数据-> code:$code message:$message")
                    resetLoadMore()
                    requestNoLoginBookData()
                }
            })
        } else {
            FuliLogger.log(TAG, "未登录,不请求用户阅读历史数据")
            requestNoLoginBookData()
        }
    }

    /**
     * 执行新增数据 需要处理删除记录
     *
     * @param addBookList 书籍列表
     */
    private fun performAddBookList(addBookList: MutableList<BookHistoryInfo>) {
        if (CollectionUtil.isNullOrEmpty(addBookList)) {
            return
        }
        val iterator = addBookList.iterator()
        while (iterator.hasNext()) {
            val bookInfo = iterator.next()
            if (bookInfo == null) {
                iterator.remove()
            } else {
                val modifyBookInfo = BookHistoryRecordRepository.query(bookInfo.bookId)
                // 该本书本地有删除记录则不添加
                if (modifyBookInfo != null && modifyBookInfo.modifyType == BookUtils.TYPE_DEL) {
                    FuliLogger.log(TAG, "存在删除记录,不添加 ${bookInfo.bookName}")
                    iterator.remove()
                } else {
                    // 服务端返回数据 存本地标记用户
                    bookInfo.uid = BookUtils.currentUserId
                }
            }
        }

        // 新增书籍插入数据库
        if (CollectionUtil.isNotEmpty(addBookList)) {
            printBookHistoryList("历史籍:", addBookList)
            BookHistoryRepository.insertOrReplace(addBookList)
        }
    }

    private fun getAddBookData(serviceBookList: MutableList<BookHistoryInfo>, curUserBookList: List<BookHistoryInfo>): MutableList<BookHistoryInfo> {
        // 无本地数据 全部是新增的
        if (CollectionUtil.isNullOrEmpty(curUserBookList)) {
            return serviceBookList
        }
        val needUpdateList: MutableList<BookHistoryInfo> = ArrayList()
        for (localBookInfo in curUserBookList) {
            if (localBookInfo == null) {
                continue
            }
            for (serviceBookInfo in serviceBookList) {
                if (serviceBookInfo != null && serviceBookInfo.bookId == localBookInfo.bookId) {
                    // 本地存在这本书  移除
                    serviceBookList.remove(serviceBookInfo)
                    var isNeedUpdateBook = false
                    // 同一本书取最近更新时间
                    if (serviceBookInfo.lastUpdatedTime > localBookInfo.lastUpdatedTime) {
                        localBookInfo.lastUpdatedTime = serviceBookInfo.lastUpdatedTime
                        isNeedUpdateBook = true
                    }
                    if (serviceBookInfo.bookState != localBookInfo.bookState) {
                        // 更新书籍状态
                        localBookInfo.bookState = serviceBookInfo.bookState
                        isNeedUpdateBook = true
                    }

                    if (isNeedUpdateBook) {
                        needUpdateList.add(localBookInfo)
                    }
                    break
                }
            }
        }

        // 更新书籍时间
        if (needUpdateList.isNotEmpty()) {
            printBookHistoryList("更新历史书籍时间或状态:", needUpdateList)
            BookHistoryRepository.update(needUpdateList)
        }

        // 返回这次新增的图书
        return serviceBookList
    }

    private fun requestNoLoginBookData() {
        // 查询本地历史数据
        val localBookList: MutableList<BookHistoryInfo> = BookHistoryRepository.queryInUser(BookUtils.defaultUId)
        var curUserBookList: MutableList<BookHistoryInfo> = arrayListOf()
        // 当前用户数据-已登录才有这个数据
        if (UserInfoMannage.hasLogined()) {
            curUserBookList = BookHistoryRepository.queryInUser(BookUtils.currentUserId)
        }

        dealBookData(localBookList, curUserBookList)
    }

    /**
     * 处理本地数据以及用户数据
     *
     * @param localBookList   本地推荐数据
     * @param curUserBookList 用户数据(只有登录了才有)
     */
    private fun dealBookData(localBookList: MutableList<BookHistoryInfo>, curUserBookList: MutableList<BookHistoryInfo>) {
        // 本地数据为空
        if (CollectionUtil.isNullOrEmpty(localBookList)) {
            if (CollectionUtil.isNullOrEmpty(curUserBookList)) {
                mBookHistoryView?.setData(null)
            } else {
                // 将线上用户数据添加到本地  有线上数据说明用户登录了
                mergeBookData(localBookList, curUserBookList)

                mBookHistoryView?.setData(curUserBookList)
            }
        } else {
            // 本地有数据  服务端无数据  显示本地数据
            if (CollectionUtil.isNullOrEmpty(curUserBookList)) {
                // 用户已登录 本地书籍历史添加到该用户账号上  需要有"添加书籍历史操作"  未登录显示未登录历史列表即可
                if (UserInfoMannage.hasLogined()) {
                    mergeBookData(localBookList, curUserBookList)
                }
                mBookHistoryView?.setData(localBookList)
            } else {
                mergeBookData(localBookList, curUserBookList)
                val list: MutableList<BookHistoryInfo> = BookHistoryRepository.queryInUser(BookUtils.currentUserId)
                if (CollectionUtil.isNullOrEmpty(list)) {
                    mBookHistoryView?.setData(null)
                } else {
                    mBookHistoryView?.setData(list)
                }
            }
        }

        mBookHistoryView?.loadDataEnd()

        // 同步操作给服务端
        syncBookHistoryModifyRecord()

        isLoadData = false
    }

    /**
     * 合并书籍数据
     *
     * @param localBookList   本地未登录用户历史数据
     * @param curUserBookList 本地用户数据+服务端返回数据
     */
    public fun mergeBookData(localBookList: MutableList<BookHistoryInfo>, curUserBookList: MutableList<BookHistoryInfo>) {
        if (CollectionUtil.isNullOrEmpty(localBookList) && CollectionUtil.isNullOrEmpty(curUserBookList)) {
            return
        }
        val needUpdateList: MutableList<BookHistoryInfo> = ArrayList()

        if (CollectionUtil.isNotEmpty(localBookList) && CollectionUtil.isNotEmpty(curUserBookList)) {
            // (只有远端数据这种情况不用处理  请求到数据后就会存入到数据库)
            // 远端本地都有数据  进行数据合并
            val iterator = curUserBookList.iterator()
            while (iterator.hasNext()) {
                val curUserBookInfo = iterator.next()
                if (curUserBookInfo == null) {
                    iterator.remove()
                } else {
                    for (recommendBookInfo in localBookList) {
                        if (recommendBookInfo == null) {
                            continue
                        }
                        // 本地推荐数据和用户数据存在同一本书
                        if (curUserBookInfo.bookId == recommendBookInfo.bookId) {
                            localBookList.remove(recommendBookInfo)
                            // 用户的本书操作时间小于本地时间  那才需要更新  (因为用户的数据都在数据库了)
                            if (curUserBookInfo.lastUpdatedTime < recommendBookInfo.lastUpdatedTime) {
                                curUserBookInfo.lastUpdatedTime = recommendBookInfo.lastUpdatedTime
                                needUpdateList.add(curUserBookInfo)
                            }
                            break
                        }
                    }
                }
            }
        }

        // 将剩余推荐数据添加到数据库
        if (localBookList.isNotEmpty()) {
            //  数据倒序遍历  因为第一本书才是最后新增的
            for (bookInfo in localBookList.reversed()) {
                if (bookInfo != null) {
                    //  清除本地推荐数据的主键id   不然更新后会把推荐数据覆盖  应该是新增一份当前用户的书籍数据
                    bookInfo.id = null
                    bookInfo.uid = BookUtils.currentUserId

                    // 插入新增记录
                    BookHistoryUtils.operatingBooks(bookInfo.bookId, bookInfo.bookName,
                            bookInfo.bookCover, bookInfo.readChapterId,
                            bookInfo.extensionField1 == true.toString(),
                            bookInfo.lastUpdatedTime, BookUtils.TYPE_ADD)
                }
            }
        }

        if (needUpdateList.isNotEmpty()) {
            // 更新书籍信息 更新归属者
            for (bookInfo in needUpdateList) {
                if (bookInfo != null) {
                    bookInfo.uid = BookUtils.currentUserId
                }
            }
            printBookHistoryList("更新阅读历史数据", needUpdateList)
            BookHistoryRepository.insertOrReplace(needUpdateList)
        }
    }

    private fun printBookHistoryList(msg: String, list: List<BookHistoryInfo>) {
        if (!ConstantsOpenSdk.isDebug) {
            return
        }
        if (CollectionUtil.isNotEmpty(list)) {
            for (bookInfo in list) {
                if (bookInfo != null) {
                    FuliLogger.log(TAG, msg + " bookId:" + bookInfo.bookId + " bookName:" +
                            bookInfo.bookName + " uid:" + bookInfo.uid + " bookState:" + bookInfo.bookState + " time:" + bookInfo.lastUpdatedTime)
                }
            }
        }
    }

    private fun getBookHistoryList(lastTimeMillis: Long, pageSize: Int, callback: IDataCallBack<BookHistoryList>) {
        // 未登录请求单独接口
        if (!UserInfoMannage.hasLogined()) {
            callback.onError(-1, "")
            return;
        }

        val map = mutableMapOf<String, String>()
        map["lastTimeMillis"] = lastTimeMillis.toString()
        map["count"] = pageSize.toString()

        CommonRequestM.baseGetRequest(LiteUrlConstants.getBookHistoryListUrl() + System.currentTimeMillis(), map, callback) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            if (ret == 0) {
                JsonUtilKt.instance.toObjectOfType<BookHistoryList>(json.optString("data"),
                        object : TypeToken<BookHistoryList>() {}.type)
            } else null
        }
    }

    /**
     * 单条记录上报
     */
    private fun reportBookHistoryModifyRecord(bookInfo: ModifyBookHistoryInfo, callback: IDataCallBack<Boolean>) {
        val map = mutableMapOf<String, String>()
        bookInfo.run {
            map["bookId"] = bookId.toString()
            map["chapterId"] = readChapterId.toString()
            map["lastUpdatedTime"] = lastUpdatedTime.toString()

            CommonRequestM.basePostRequest(LiteUrlConstants.getBookHistoryReportUrl() + System.currentTimeMillis(), map, callback) { content ->
                val json = JSONObject(content)
                val ret = json.optInt("ret", -1)
                ret == 0
            }
        }
    }

    /**
     * 批量新增数据
     */
    private fun batchAddBookHistoryModifyRecord(list: MutableList<ModifyBookHistoryInfo>, callback: IDataCallBack<Boolean>) {
        val data = JsonObject()
        val array = JsonArray()
        list.forEach {
            it.run {
                val map = JsonObject()
                map.addProperty("bookId", bookId)
                map.addProperty("chapterId", readChapterId)
                map.addProperty("lastUpdatedTime", lastUpdatedTime)
                array.add(map)
            }
        }
        data.add("records", array)

        CommonRequestM.basePostRequestParmasToJson(LiteUrlConstants.getBookHistoryBatchReportUrl() + System.currentTimeMillis(),
                data, callback) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            ret == 0
        }

    }

    /**
     * 批量处理数据  包括删除  新增
     */
    private fun batchDelBookHistoryModifyRecord(list: MutableList<ModifyBookHistoryInfo>, callback: IDataCallBack<Boolean>) {
        val data = JsonObject()
        val array = JsonArray()
        list.forEach {
            it.run {
                val map = JsonObject()
                map.addProperty("bookId", bookId)
                array.add(map)
            }
        }
        data.add("records", array)

        CommonRequestM.basePostRequestParmasToJson(LiteUrlConstants.getBookHistoryBatchDeleteUrl() + System.currentTimeMillis(),
                data, callback) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            ret == 0
        }
    }

    /**
     * 清除所有历史记录
     */
    override fun clearAllBookHistoryRecord(callback: IDataCallBack<Boolean>?) {
        val map = mutableMapOf<String, String>()
        // 框架必须要默认参数 所以加一个
        map["a"] = "0"
        CommonRequestM.basePostRequest(LiteUrlConstants.getClearAllBookHistoryUrl() + System.currentTimeMillis(), map, object : IDataCallBack<Boolean> {
            override fun onSuccess(result: Boolean?) {
                callback?.onSuccess(result)
                // 清除本地记录和本地操作记录
                BookHistoryRepository.removeAll()
                BookHistoryRecordRepository.removeAll()
            }

            override fun onError(code: Int, message: String?) {
                callback?.onError(code, message)
            }

        }) { content ->
            val json = JSONObject(content)
            val ret = json.optInt("ret", -1)
            ret == 0
        }
    }

    override fun syncBookHistoryModifyRecord() {
        // 专门同步一个类处理同步逻辑
        if (!UserInfoMannage.hasLogined()) {
            return;
        }

        if (isSyncBookRecord) {
            return
        }

        val list = BookHistoryRecordRepository.queryAll()
        if (CollectionUtil.isNotEmpty(list)) {
            isSyncBookRecord = true

            FuliLogger.log(TAG, "开始同步书籍历史编辑记录")

            val addBookList = mutableListOf<ModifyBookHistoryInfo>()
            val delBookList = mutableListOf<ModifyBookHistoryInfo>()
            val hashSet = mutableSetOf<Long>()
            var addCount = 0
            var delCount = 0

            list.forEach { modifyBookInfo ->
                modifyBookInfo.run {
                    // 同一本书 可能会有两条记录  一条是登录用户的 一条是未登录的  只保留最新的一条(列表是按照时间倒序排的)
                    if (!hashSet.contains(bookId)) {
                        hashSet.add(bookId)

                        when (modifyType) {
                            BookUtils.TYPE_ADD, BookUtils.TYPE_MODIFY -> {
                                addCount++

                                addBookList.add(this)
                                FuliLogger.log(TAG, "上报新增历史记录 bookId: $bookId")
                            }
                            BookUtils.TYPE_DEL -> {
                                delCount++
                                delBookList.add(this)
                                FuliLogger.log(TAG, "上报删除历史记录 bookId: $bookId")
                            }
                        }
                    } else {
                        FuliLogger.log(TAG, "存在同一本书记录,不处理: bookId: $bookId")
                    }
                }
            }

            performUpload(addCount, delCount, addBookList, delBookList)
        }
    }

    override fun queryBookHistoryAmount() :Int {
        return BookHistoryRepository.queryCountByUser(BookUtils.currentUserId).toInt()
    }

    override fun getRecentHistory(): BookHistoryInfo? {
        BookHistoryRepository.queryInUser(BookUtils.currentUserId)?.let {
            if (it.isNotEmpty()){
                return it.first()
            }
        }
        return null
    }

    /**
     * 处理登录用户删除的书籍历史数据
     */
    private fun dealLoginUserDelBookHistoryData(serviceBookList: MutableList<BookHistoryInfo>, curUserBookList: MutableList<BookHistoryInfo>) {
        if (CollectionUtil.isNullOrEmpty(curUserBookList)) {
            return
        }

        val iterator = curUserBookList.iterator()
        while (iterator.hasNext()) {
            val curUserBookInfo = iterator.next()
            if (curUserBookInfo == null) {
                iterator.remove()
            } else {
                var index = -1
                if (CollectionUtil.isNotEmpty(serviceBookList)) {
                    for (serviceBookInfo in serviceBookList) {
                        if (serviceBookInfo == null) {
                            continue
                        }
                        // 线上数据和本地用户数据存在同一本书
                        if (curUserBookInfo.bookId == serviceBookInfo.bookId) {
                            index = 0
                            break
                        }
                    }
                }
                // 本地有这本书 线上没有这本书
                if (index == -1) {
                    val modifyBookInfo = BookHistoryRecordRepository.query(curUserBookInfo.bookId)
                    // 没有编辑记录 代表这本书在别的设备删除了  删除本地数据
                    if (modifyBookInfo == null) {
                        BookHistoryRepository.remove(curUserBookInfo.bookId)
                    } else if (modifyBookInfo.modifyType != BookUtils.TYPE_ADD && modifyBookInfo.modifyType == BookUtils.TYPE_MODIFY) {
                        // 存在编辑记录 但是不是添加和编辑类型  也直接删除这本书
                        BookHistoryRepository.remove(curUserBookInfo.bookId)
                    }
                }
            }
        }
    }

    private fun performUpload(addCount: Int, delCount: Int, addBookList: MutableList<ModifyBookHistoryInfo>, delBookList: MutableList<ModifyBookHistoryInfo>) {
        // 单条上报
        allRequest = 0;
        if (addCount == 1) {
            allRequest++
            val info = addBookList.first()
            info.run {
                reportBookHistoryModifyRecord(info, object : IDataCallBack<Boolean> {
                    override fun onSuccess(result: Boolean?) {
                        checkLoadEnd()
                        result?.let {
                            if (it) {
                                BookHistoryRecordRepository.remove(addBookList)
                            }
                            FuliLogger.log(TAG, "添加->书籍操作记录上报结果 $it")
                        }
                    }

                    override fun onError(code: Int, message: String?) {
                        FuliLogger.log(TAG, "添加->书籍操作记录上报结果 code:$code message:$message")
                        checkLoadEnd()
                    }
                })
            }
        } else if (addCount > 1) {
            allRequest++
            // 多条上报
            batchAddBookHistoryModifyRecord(addBookList, object : IDataCallBack<Boolean> {
                override fun onSuccess(result: Boolean?) {
                    checkLoadEnd()
                    result?.let {
                        if (it) {
                            BookHistoryRecordRepository.remove(addBookList)
                        }

                        FuliLogger.log(TAG, "添加->书籍操作记录上报结果 $it")
                    }
                }

                override fun onError(code: Int, message: String?) {
                    FuliLogger.log(TAG, "添加->书籍操作记录上报结果 code:$code message:$message")
                    checkLoadEnd()
                }
            })
        }

        // 上报删除
        if (delCount != 0) {
            allRequest++
            batchDelBookHistoryModifyRecord(delBookList, object : IDataCallBack<Boolean> {
                override fun onSuccess(result: Boolean?) {
                    checkLoadEnd()
                    result?.let {
                        if (it) {
                            BookHistoryRecordRepository.remove(delBookList)
                        }

                        FuliLogger.log(TAG, "删除->书籍操作记录上报结果 $it")
                    }
                }

                override fun onError(code: Int, message: String?) {
                    FuliLogger.log(TAG, "删除->书籍操作记录上报结果 code:$code message:$message")
                    checkLoadEnd()
                }
            })
        }
    }

    private fun checkLoadEnd() {
        allRequest--
        if (allRequest == 0) {
            isSyncBookRecord = false
        }
    }

    private fun resetLoadMore() {
        if (mIsLoadMore) {
            mIsLoadMore = false
        }
    }

    override fun loadMore() {

    }
}