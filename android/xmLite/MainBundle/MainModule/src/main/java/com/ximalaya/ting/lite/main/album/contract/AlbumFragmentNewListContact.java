package com.ximalaya.ting.lite.main.album.contract;

import com.ximalaya.ting.android.host.business.unlock.model.AlbumPaidUnLockHintInfo;
import com.ximalaya.ting.android.opensdk.model.album.Album;

/**
 * Created by qinhuifeng on 2020/9/11
 *
 * <AUTHOR>
 */
public interface AlbumFragmentNewListContact {
    interface PageView {

        boolean canUpdateUi();

        //获取解锁相关信息
        AlbumPaidUnLockHintInfo getAlbumPaidUnLockHintInfo();

        //获取当前专辑信息
        Album getAlbum();

        //是否是顺序排序
        boolean isTrackAsc();
    }
}
