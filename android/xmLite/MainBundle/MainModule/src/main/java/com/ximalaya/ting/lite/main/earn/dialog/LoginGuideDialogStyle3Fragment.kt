package com.ximalaya.ting.lite.main.earn.dialog

import android.view.View
import android.widget.ImageView
import com.ximalaya.ting.android.host.constants.LoginByConstants
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.lite.main.earn.LoginGuideDialogManager

class LoginGuideDialogStyle3Fragment : BaseLoginGuideDialogFragment(), View.OnClickListener {

    companion object {
        @JvmStatic
        fun newInstance(anim1Folder: Pair<String, String>, anim2Folder: Pair<String, String>, type : Int)
                : LoginGuideDialogStyle3Fragment {
            return LoginGuideDialogStyle3Fragment().apply {
                this.anim1Folder = anim1Folder
                this.anim2Folder = anim2Folder
                this.type = type
            }
        }
    }

    private var step1Anim: XmLottieAnimationView? = null
    private var step2Anim: XmLottieAnimationView? = null
    private var closeIv: ImageView? = null
    private var anim1Folder: Pair<String, String>? = null
    private var anim2Folder: Pair<String, String>? = null
    private var type: Int? = null

    private val delayAnim = Runnable {
        if (canUpdateUi()) {
            startAnim2()
            cancelAnim1()
        }
    }

    override fun getLayoutRes(): Int {
        return R.layout.main_fra_dialog_login_guide_style3
    }

    override fun initUI(view: View) {
        step1Anim = view.findViewById(R.id.main_login_guide_anim1)
        step2Anim = view.findViewById(R.id.main_login_guide_anim2)
        closeIv = view.findViewById(R.id.main_login_guide_close)
        step1Anim?.setOnClickListener(this)
        step2Anim?.setOnClickListener(this)
        closeIv?.setOnClickListener(this)
        startAnim1()
        step1Anim?.postDelayed(delayAnim, 3_000)

        type?.let { LoginGuideDialogManager.traceDialogShow(it) }
    }

    override fun onClick(v: View?) {
        when (v) {
            step1Anim -> {
                step1Anim?.handler?.removeCallbacks(delayAnim)
                startAnim2()
                cancelAnim1()
            }
            step2Anim -> {
                UserInfoMannage.gotoLogin(activity, LoginByConstants.LOGIN_BY_DEFUALT)
                dismissAllowingStateLoss()
                type?.let { LoginGuideDialogManager.traceDialogClick(it) }
            }
            closeIv -> {
                step1Anim?.handler?.removeCallbacks(delayAnim)
                dismissAllowingStateLoss()
                onDialogCloseListener?.onClose()
            }
        }
    }

    private fun startAnim1() {
        step1Anim?.run {
            anim1Folder?.let {
                imageAssetsFolder = it.first
                setAnimation(it.second)
                playAnimation()
            }
        }
    }

    private fun startAnim2() {
        step2Anim?.run {
            anim2Folder?.let {
                imageAssetsFolder = it.first
                setAnimation(it.second)
                playAnimation()
                visibility = View.VISIBLE
                closeIv?.visibility = View.VISIBLE
            }
        }
    }

    private fun cancelAnim1() {
        step1Anim?.cancelAnimation()
        step1Anim?.visibility = View.INVISIBLE
    }

    override fun onDestroy() {
        super.onDestroy()
        step1Anim?.handler?.removeCallbacks(delayAnim)
    }

}