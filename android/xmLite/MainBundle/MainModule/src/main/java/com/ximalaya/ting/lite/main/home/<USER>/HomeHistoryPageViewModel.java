package com.ximalaya.ting.lite.main.home.viewmodel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qinhuifeng on 2021/1/18
 * <p>
 * 播放历史页面数据model
 *
 * <AUTHOR>
 */
public class HomeHistoryPageViewModel {
    public List<HomeItemPlayHistoryViewModel> historyViewModelList;

    public HomeItemPlayHistoryViewModel moreItemModel;

    public void fillViewModel(HomeHistoryPageViewModel viewModel) {
        if (viewModel == null) {
            return;
        }
        if (viewModel.historyViewModelList != null) {
            if (historyViewModelList == null) {
                historyViewModelList = new ArrayList<>();
            }
            historyViewModelList.clear();
            historyViewModelList.addAll(viewModel.historyViewModelList);
        }
    }


    public List<HomeItemPlayHistoryViewModel> build() {
        if (moreItemModel == null) {
            moreItemModel = new HomeItemPlayHistoryViewModel();
            moreItemModel.viewType = HomeItemPlayHistoryViewModel.ITEM_MORE;
        }
        List<HomeItemPlayHistoryViewModel> allData = new ArrayList<>();
        if (historyViewModelList != null) {
            allData.addAll(historyViewModelList);
        }
        //大于等于20个的时候展示查看更多按钮
        if (allData.size() >= 20) {
            allData.add(moreItemModel);
        }
        return allData;
    }
}
