package com.ximalaya.ting.lite.main.book.fragment;

import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.view.View;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshRecyclerView;
import com.ximalaya.ting.android.host.db.model.BookInfo;
import com.ximalaya.ting.android.host.db.utils.BookUtils;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listenertask.FuliLogger;
import com.ximalaya.ting.android.host.trace.PageTraceManager;
import com.ximalaya.ting.android.host.util.ReadUtils;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.lite.main.book.adapter.BookShelfListAdapter;
import com.ximalaya.ting.lite.main.book.bean.BookShelfAddBean;
import com.ximalaya.ting.lite.main.book.bean.BookWrapperBean;
import com.ximalaya.ting.lite.main.book.presenter.BookShelfPresenter;
import com.ximalaya.ting.lite.main.book.presenter.IBookShelfPresenter;
import com.ximalaya.ting.lite.main.book.view.IBookShelfView;
import com.ximalaya.ting.lite.main.mylisten.view.SubScribeFragment;
import com.ximalaya.ting.lite.main.read.fragment.NovelRankFragment;
import com.ximalaya.ting.lite.main.read.model.LoveNovelRankArgsModel;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;

/**
 * 书架
 */
public class BookShelfListFragment extends BaseFragment2 {

    private static final String TAG = "BookShelfListFragment";

    private RefreshRecyclerView mRecyclerView;

    private final List<BookWrapperBean<?>> mEBookList = new ArrayList<>();

    private BookShelfListAdapter mBookShelfListAdapter;

    private LinearLayout mLLMenu;
    private CheckBox mCBSelectAll;
    private TextView mTvSelectAllTitle;
    private TextView mTvRemove;

    private DialogBuilder<?> mRemoveOneBookDialog;

    private IBookShelfPresenter mBookShelfPresenter;

    private int mTotalCount;

    private BookWrapperBean<BookShelfAddBean> mAddBean;

    private long mCurTime;

    private final Handler mHandler = new Handler();

    @Override
    protected String getPageLogicName() {
        return getClass().getSimpleName();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mRecyclerView = findViewById(R.id.main_rv_book_list);
        setScrollViewListener(mRecyclerView);
        mRecyclerView.getRefreshableView().setLayoutManager(new GridLayoutManager(getContext(), 3));
        mRecyclerView.setMode(PullToRefreshBase.Mode.PULL_FROM_START);

        mLLMenu = findViewById(R.id.main_ll_edit_menu);
        mCBSelectAll = findViewById(R.id.main_menu_cb_all_select);
        mTvSelectAllTitle = findViewById(R.id.main_menu_tv_all_select);
        mTvRemove = findViewById(R.id.main_menu_tv_remove);

        mAddBean = new BookWrapperBean<>(new BookShelfAddBean());
        mEBookList.add(mAddBean);

        mBookShelfListAdapter = new BookShelfListAdapter(mContext, mEBookList);
        mRecyclerView.setAdapter(mBookShelfListAdapter);

        mBookShelfPresenter = new BookShelfPresenter(new IBookShelfView() {
            @Override
            public void loadDataEnd() {
                mRecyclerView.onRefreshComplete(false);
            }

            @Override
            public void setTotalCount(int totalCount) {
                mTotalCount = totalCount;
            }

            @Override
            public void setData(@Nullable List<BookInfo> list) {
                if (canUpdateUi()) {
                    setRecyclerViewData(list);
                }
            }
        });

        initListener();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_book_shelf_list_layout;
    }

    private void initListener() {
        AutoTraceHelper.bindData(mCBSelectAll, AutoTraceHelper.MODULE_DEFAULT, "");
        mCBSelectAll.setOnClickListener(v -> performChangeCheckBox());

        AutoTraceHelper.bindData(mTvSelectAllTitle, AutoTraceHelper.MODULE_DEFAULT, "");
        mTvSelectAllTitle.setOnClickListener(v -> {
            // 变更选中状态
            mCBSelectAll.setChecked(!mCBSelectAll.isChecked());

            performChangeCheckBox();
        });

        AutoTraceHelper.bindData(mTvRemove, AutoTraceHelper.MODULE_DEFAULT, "");
        mTvRemove.setOnClickListener(v -> {
            StringBuilder stringBuilder = new StringBuilder();
            for (BookWrapperBean<?> wrapperBean : mEBookList) {
                if (wrapperBean != null && wrapperBean.getData() instanceof BookInfo) {
                    BookInfo bookInfo = (BookInfo) wrapperBean.getData();
                    if (bookInfo.isSelect()) {
                        stringBuilder.append(bookInfo.getBookId()).append(",");
                    }
                }
            }

            if (stringBuilder.length() > 0) {
                String bookIds = stringBuilder.substring(0, stringBuilder.length() - 1);
                // 移出书籍弹窗  弹框展示
                new XMTraceApi.Trace()
                        .setMetaId(39433)
                        .setServiceId("dialogView")
                        .put("bookId", bookIds)
                        .put("currPage", "navSubscribeDownload")
                        .createTrace();

                showDelBookDialog("确认将书籍移出订阅", () -> {
                    // 移出书籍弹窗-确认点击  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(39434)
                            .setServiceId("dialogClick")
                            .put("bookId", bookIds)
                            .put("currPage", "navSubscribeDownload")
                            .createTrace();

                    Iterator<BookWrapperBean<?>> iterator = mEBookList.iterator();
                    boolean isRemove = false;
                    while (iterator.hasNext()) {
                        BookWrapperBean<?> wrapperBean = iterator.next();
                        if (wrapperBean == null) {
                            iterator.remove();
                            continue;
                        }

                        if (wrapperBean.getData() instanceof BookInfo) {
                            BookInfo bookInfo = (BookInfo) wrapperBean.getData();
                            if (bookInfo.isSelect()) {
                                isRemove = true;
                                iterator.remove();
                                BookUtils.INSTANCE.operatingBooks(bookInfo.getBookId(), "", "",
                                        BookUtils.TYPE_DEL);
                            }
                        }
                    }

                    if (isRemove) {
                        mBookShelfListAdapter.notifyDataSetChanged();
                        mBookShelfPresenter.syncBookModifyRecord(false);

                        // 删除后退出编辑模式
                        Fragment fragment = getParentFragment();
                        if (fragment instanceof SubScribeFragment) {
                            SubScribeFragment subScribeFragment = (SubScribeFragment) fragment;
                            subScribeFragment.performEditModel();
                        }

                        checkCleanAll();
                    }
                }, () -> {
                    // 移出书籍弹窗-取消点击  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(39435)
                            .setServiceId("dialogClick")
                            .put("bookId", bookIds)
                            .put("currPage", "navSubscribeDownload")
                            .createTrace();
                });
            }
        });

        mRecyclerView.setOnRefreshLoadMoreListener(new IRefreshLoadMoreListener() {
            @Override
            public void onRefresh() {
                requestData();
            }

            @Override
            public void onMore() {
                // 因为有一个默认+站位
                if (mEBookList.size() - 1 >= mTotalCount) {
                    CustomToast.showToast("暂无更多数据");
                    mRecyclerView.finishLoadingMore();
                    return;
                }
                mBookShelfPresenter.loadMore();
            }
        });

        mBookShelfListAdapter.setBookEditListener(new BookShelfListAdapter.onBookEditListener() {
            @Override
            public void onClickBook(int position) {
                BookWrapperBean<?> wrapperBean = mEBookList.get(position);
                BookInfo bookInfo = null;

                if (wrapperBean != null && wrapperBean.getData() instanceof BookInfo) {
                    bookInfo = (BookInfo) wrapperBean.getData();
                }

                // 书籍下架
                if (bookInfo == null || bookInfo.isOffShelf()) {
                    return;
                }

                // 移除书籍
                mEBookList.remove(position);
                bookInfo.setLastUpdatedTime(BookUtils.INSTANCE.getLastUpdatedTime());
                // 移动到第一本
                mEBookList.add(0, wrapperBean);

                //跳转到阅读器
                ReadUtils.startToReader(bookInfo.getBookId());

                // 延时刷新
                mHandler.postDelayed(() -> mBookShelfListAdapter.notifyDataSetChanged(), 500);
            }

            @Override
            public void onLongClickBook(int position) {
                BookWrapperBean<?> bookWrapperBean = mEBookList.get(position);
                String bookId = "";
                if (bookWrapperBean != null && bookWrapperBean.getData() instanceof BookInfo) {
                    BookInfo bookInfo = (BookInfo) bookWrapperBean.getData();
                    bookId = String.valueOf(bookInfo.getBookId());
                }
                // 移出本书弹窗  弹框展示
                new XMTraceApi.Trace()
                        .setMetaId(39436)
                        .setServiceId("dialogView")
                        .put("bookId", bookId)
                        .put("currPage", "navSubscribeDownload")
                        .createTrace();

                // 删除
                String finalBookId = bookId;

                showDelBookDialog("确认将本书移出订阅", () -> {

                    // 移出本书弹窗-确认点击  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(39437)
                            .setServiceId("dialogClick")
                            .put("bookId", finalBookId)
                            .put("currPage", "navSubscribeDownload")
                            .createTrace();

                    BookWrapperBean<?> wrapperBean = mEBookList.remove(position);
                    mBookShelfListAdapter.notifyDataSetChanged();

                    if (wrapperBean != null && wrapperBean.getData() instanceof BookInfo) {
                        BookInfo bookInfo = (BookInfo) wrapperBean.getData();
                        BookUtils.INSTANCE.operatingBooks(bookInfo.getBookId(), "", "",
                                BookUtils.TYPE_DEL);
                        mBookShelfPresenter.syncBookModifyRecord(false);
                    }

                    checkCleanAll();
                }, () -> {
                    // 移出本书弹窗-取消点击  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(39438)
                            .setServiceId("dialogClick")
                            .put("bookId", finalBookId)
                            .put("currPage", "navSubscribeDownload")
                            .createTrace();
                });
            }

            @Override
            public void onClickAddBook(int position) {
                //跳转到排行榜
                startFragment(NovelRankFragment.newInstance(new LoveNovelRankArgsModel()));
            }

            @Override
            public void onSelectBook(int position) {
                // 编辑模式选中图书
                performCheckSelectAllStatus();
            }
        });
    }

    private void showDelBookDialog(String msg, DialogBuilder.DialogCallback callback,
                                   DialogBuilder.DialogCallback cancelCallback) {
        if (mRemoveOneBookDialog == null) {
            mRemoveOneBookDialog = new DialogBuilder<>(mActivity);
        }
        mRemoveOneBookDialog.setTitleVisibility(false)
                .setMessage(msg)
                .setOkBtn(callback)
                .setCancelBtn(cancelCallback);

        if (!mRemoveOneBookDialog.isShowing()) {
            mRemoveOneBookDialog.showConfirm();
        }
    }

    /**
     * 检查选中状态
     */
    private void performCheckSelectAllStatus() {
        if (mEBookList.isEmpty()) {
            return;
        }
        int selectCount = 0, allCount = 0;
        for (BookWrapperBean<?> wrapperBean : mEBookList) {
            if (wrapperBean != null) {
                if (wrapperBean.getData() instanceof BookInfo) {
                    BookInfo eBook = (BookInfo) wrapperBean.getData();
                    if (eBook != null && eBook.isSelect()) {
                        selectCount++;
                    }
                    allCount++;
                }
            }
        }

        boolean isAllSelect = allCount == selectCount;
        mCBSelectAll.setChecked(isAllSelect);
        mTvSelectAllTitle.setText(isAllSelect ? "取消全选" : "全选");
    }

    private void performChangeCheckBox() {
        mTvSelectAllTitle.setText(mCBSelectAll.isChecked() ? "取消全选" : "全选");
        // 处理
        mBookShelfListAdapter.performSelectAll(mCBSelectAll.isChecked());
    }

    public void changeEditModel(boolean isEditModel) {
        if (mBookShelfListAdapter != null) {
            // 编辑模式下不显示+
            if (isEditModel) {
                mEBookList.remove(mAddBean);
            } else {
                // 先移除 怕其他场景出现这个
                mEBookList.remove(mAddBean);
                mEBookList.add(mAddBean);
            }
            mBookShelfListAdapter.changeEditMode(isEditModel);
            // 退出编辑模式  清除选中记录
            if (!isEditModel) {
                mBookShelfListAdapter.performSelectAll(false);
                mCBSelectAll.setChecked(false);
                performChangeCheckBox();
            }
        }
        // 编辑模式下禁止下拉刷新
        if (mRecyclerView != null) {
            mRecyclerView.setMode(isEditModel ? PullToRefreshBase.Mode.DISABLED :
                    PullToRefreshBase.Mode.PULL_FROM_START);
        }

        mLLMenu.setVisibility(isEditModel ? View.VISIBLE : View.GONE);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isRealVisable()) {
            FuliLogger.log(TAG, "setUserVisibleHint:" + isVisibleToUser);
            requestData();

            //页面曝光
            if (isVisibleToUser) {
                PageTraceManager.INSTANCE.intoBookShelfPageView();
            }
        }
    }

    private void requestData() {
        if (mBookShelfPresenter != null) {
            mBookShelfPresenter.loadData();
        }
    }

    @Override
    protected void loadData() {

    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        // 界面可见时加载数据  防止频繁刷新
        long sysTime = SystemClock.elapsedRealtime();
        if (sysTime - mCurTime > 5000L) {
            FuliLogger.log(TAG, "onMyResume loadData");
            mCurTime = sysTime;
            requestData();
        }

        if (isRealVisable()) {
            PageTraceManager.INSTANCE.intoBookShelfPageView();
        }
    }

    private void setRecyclerViewData(List<BookInfo> list) {
        // 清除所有数据  因为每次都是从数据库获取全量数据
        mEBookList.clear();

        if (CollectionUtil.isNotEmpty(list)) {
            HashSet<Long> hashSet = new HashSet<>();
            // 数据去重
            for (BookInfo bookInfo : list) {
                if (bookInfo == null) {
                    continue;
                }
                // 判断是否存在同一本书
                if (hashSet.contains(bookInfo.getBookId())) {
                    FuliLogger.log(TAG,
                            "存在同一本书:" + bookInfo.getBookName() + " uid:" + bookInfo.getUid());
                } else {
                    hashSet.add(bookInfo.getBookId());
                    mEBookList.add(new BookWrapperBean<>(bookInfo));
                }
            }
        }

        mEBookList.add(mAddBean);
        mBookShelfListAdapter.notifyDataSetChanged();

        checkCleanAll();
    }

    private void checkCleanAll() {
        // 有一个添加按钮
        boolean isNoData = mEBookList.size() == 1;
        checkDelViewVisible(isNoData ? View.GONE : View.VISIBLE);
    }

    public void checkDelViewVisible(int visible) {
        Fragment fragment = getParentFragment();
        if (fragment instanceof SubScribeFragment) {
            SubScribeFragment subScribeFragment = (SubScribeFragment) fragment;
            subScribeFragment.checkDelBtnVisible(visible);
        }
    }
}
