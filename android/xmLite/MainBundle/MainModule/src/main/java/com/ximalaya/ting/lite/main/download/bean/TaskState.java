package com.ximalaya.ting.lite.main.download.bean;


import com.ximalaya.ting.lite.main.download.inter.ITaskStateObserver;
import com.ximalaya.ting.lite.main.download.utils.TaskCode;

/**
 * <AUTHOR> feiwen
 * date   : 2019/4/9
 * desc   :
 */
public class TaskState {

    /**
     * 准备中，什么都没有做。
     */
    public static final int STATE_PENDING = 10;
    /**
     * 等待下载
     */
    public static final int STATE_WAITING = 11;
    /**
     * 连接服务端
     */
    public static final int STATE_CONNECTING = 12;
    /**
     * 下载中
     */
    public static final int STATE_DOWNLOADING = 13;
    /**
     * 暂停(停止)
     */
    public static final int STATE_PAUSE = 14;
    /**
     * 已完成
     */
    public static final int STATE_DONE = 15;
    /**
     * 下载错误
     */
    public static final int STATE_ERROR = 16;
    /**
     * 下载任务被删除
     */
    public static final int STATE_DELETE = 17;
    /**
     * 任务信息改变
     */
    public static final int STATE_UPDATED_INFO = 19;
    /**
     * 下载的状态
     */
    private int state = STATE_PENDING;
    /**
     * 错误码
     */
    private int errorCode = TaskCode.SUCCESS;
    private double speed = 0;
    /**
     * 下载状态观察者
     */
    private ITaskStateObserver observer;

    private long currSize;

    private long totalSize;

    public long getCurrSize() {
        return currSize;
    }

    public long getTotalSize() {
        return totalSize;
    }

    public void setCurrSize(long currSize) {
        this.currSize = currSize;
    }

    public void setTotalSize(long totalSize) {
        this.totalSize = totalSize;
    }

    public void setState(int value) {
        this.state = value;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int code) {
        this.errorCode = code;
    }

    public int getState() {
        return state;
    }

    public void setObserver(ITaskStateObserver obs) {
        this.observer = obs;
    }

    /**
     * 更新状态
     */
    public void updateState(int value, Task<?, ?> task) {

        boolean changed = true;
        if (value == STATE_DOWNLOADING) {
            TaskInfo info = task.getInfo();
            totalSize = info.getSize();
            currSize = info.getCurrSize();
        } else {
            // 状态相同，不通知。
            if (state == value) {
                changed = false;
            }
        }
        if (changed) {
            setState(value);
            notifyStateChange(task);
        }
    }

    private void notifyStateChange(Task<?, ?> task) {
        if (this.observer != null) {
            this.observer.update(task.getInfo(), task.getState());
        }
    }

    /**
     * 获取当前下载的进度，0.00~100.00.
     */
    public double getProgressValue() {
        if (totalSize <= 0) {
            return 0;
        }
        final double ts = (double) totalSize;
        final double cs = (double) currSize;

        return cs * 100 / ts;
    }

    public double getSpeedValue() {
        return speed;
    }

    public void setSpeed(double speed) {
        this.speed = speed;
    }

}
