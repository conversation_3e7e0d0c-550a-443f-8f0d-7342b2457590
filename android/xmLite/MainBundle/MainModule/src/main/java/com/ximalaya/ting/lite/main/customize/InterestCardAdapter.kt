package com.ximalaya.ting.lite.main.customize

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.model.user.NewInterestCardModel
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter

/**
 * Created by duming<PERSON> on 2020/12/9.
 *
 * Desc:
 */
class InterestCardAdapter(
    private val context: Context,
    private val SPAN_COUNT: Int,
    private val interestList: ArrayList<NewInterestCardModel>
) : AbRecyclerViewAdapter<InterestCardAdapter.VH>() {

    var onSelectedInterface: OnSelectedInterface? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.main_item_custom_card, parent, false)
        return VH(view)
    }

    override fun getItem(position: Int): Any {
        return interestList[position]
    }

    override fun getItemCount(): Int {
        return interestList.size
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        val screenWidth = BaseUtil.getScreenWidth(context)
        // 左右间距是17，去掉左右是17*2，网格中间的缝隙是12，去掉缝隙(SPAN_COUNT - 1) * 12;
        val gap = 17 * 2 + (SPAN_COUNT - 1) * 12
        // 计算网格中图片和图片父View的宽度和高度
        val mInterestImageViewWith =
            (screenWidth - BaseUtil.dp2px(context, gap.toFloat())) / SPAN_COUNT
        // 动态改变宽高比
        val layoutParams = holder.rlCustomImage.layoutParams
        layoutParams.width = mInterestImageViewWith
        layoutParams.height = mInterestImageViewWith * 70 / 106
        holder.rlCustomImage.layoutParams = layoutParams

        val model = interestList[position]
        if (model.bgColor == null) {
            holder.tvName.setTextColor(ContextCompat.getColor(context, R.color.host_color_333333))

            holder.ivCustomCardCover.background =
                (ContextCompat.getDrawable(context, R.drawable.main_radius_8_stroke_dddddd))
        } else {
            holder.ivCustomCardCover.setBackgroundColor(Color.parseColor(model.bgColor))
        }
        holder.tvName.text = model.categoryName

        holder.ivCustomCardTag.isSelected = model.chosen

        holder.itemView.setOnClickListener {
            onSelectedInterface?.onSelected(position, model)
        }
    }

    class VH(mItemView: View) : RecyclerView.ViewHolder(mItemView) {
        val rlCustomImage: RelativeLayout = itemView.findViewById(R.id.main_rl_custom_image)
        val ivCustomCardCover: ImageView = itemView.findViewById(R.id.main_custom_card_cover)
        val tvName: TextView = itemView.findViewById(R.id.mainTvName)
        val ivCustomCardTag: ImageView = itemView.findViewById(R.id.main_custom_card_tag)
    }

    interface OnSelectedInterface {

        fun onSelected(position: Int, model: NewInterestCardModel)
    }

}