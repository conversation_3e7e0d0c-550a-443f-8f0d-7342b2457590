package com.ximalaya.ting.lite.main.earn

import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.lite.main.earn.dialog.BaseLoginGuideDialogFragment
import com.ximalaya.ting.lite.main.earn.dialog.LoginGuideDialogStyle3Fragment

object LoginGuideDialogManager {

    @JvmStatic
    fun getGuideDialog(): BaseLoginGuideDialogFragment {
        //全量方案4
        return LoginGuideDialogStyle3Fragment.newInstance(
            Pair("lottie/login_guide3_step1/images", "lottie/login_guide3_step1/data_step1.json"),
            Pair("lottie/login_guide3_step1/images", "lottie/login_guide3_step1/data_step2.json"), 4
        )
    }

    fun traceDialogShow(style: Int) {
        XMTraceApi.Trace()
            .setMetaId(6190)
            .setServiceId("dialogView")
            .put("currPage", "homePage")
            .put("dialogTitle", style.toString())
            .createTrace()
    }

    fun traceDialogClick(style: Int) {
        XMTraceApi.Trace()
            .setMetaId(6191)
            .setServiceId("dialogClick")
            .put("currPage", "homePage")
            .put("dialogTitle", style.toString())
            .createTrace()
    }

}