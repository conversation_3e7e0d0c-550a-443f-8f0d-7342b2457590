package com.ximalaya.ting.lite.main.earn.view;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.widget.RelativeLayout;

/**
 * Created by qinhuifeng on 2019-10-19
 *
 * <AUTHOR>
 */
public class CoinEnterDragView extends RelativeLayout {

    private int mTouchSlop = 0;

    public CoinEnterDragView(Context context) {
        super(context);
        mTouchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
    }

    public CoinEnterDragView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mTouchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
    }

    public CoinEnterDragView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mTouchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                //禁止父view拦截
                //进行反拦截，保证子view获取down事件, 否则后续事件无法获取
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_MOVE:
                //禁止父View拦截
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_UP:
                break;
            default:
                break;
        }
        return super.dispatchTouchEvent(event);
    }

    private int mLastY = 0;
    private int mLastX = 0;
    private int mDownY = 0;
    private int mDownX = 0;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN://按下
                mLastX = (int) event.getX();
                mLastY = (int) event.getY();
                mDownX = (int) event.getX();
                mDownY = (int) event.getY();
                break;
            case MotionEvent.ACTION_MOVE://抬起
                int diffX = (int) (event.getX() - mLastX);
                int diffY = (int) (event.getY() - mLastY);
                if (Math.abs(diffX) >= mTouchSlop || Math.abs(diffY) >= mTouchSlop) {
                    //下拉生效,或者上拉
                    if (onDragListener != null) {
                        onDragListener.dragLoading(diffX, diffY);
                    }
                    mLastX = (int) event.getX();
                    mLastY = (int) event.getY();
                }
                break;
            case MotionEvent.ACTION_UP:
                if (onDragListener != null) {
                    int diffXDown = (int) (event.getX() - mDownX);
                    int diffYDown = (int) (event.getY() - mDownY);
                    onDragListener.dragFinish(diffXDown, diffYDown);
                }
                break;
        }
        return true;
    }

    OnDragListener onDragListener;

    public void setOnDragListener(OnDragListener onRopDownListener) {
        this.onDragListener = onRopDownListener;
    }

    /**
     * 绳子下拉监听
     */
    public interface OnDragListener {

        //触发下拉操作
        void dragLoading(int dragX, int dragY);

        //下拉结束
        void dragFinish(int totalDragX, int totalDragY);
    }

}
