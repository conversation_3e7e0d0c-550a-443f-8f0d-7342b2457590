package com.ximalaya.ting.lite.main.history.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.db.model.BookHistoryInfo;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmriskdatacollector.util.ScreenUtils;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;
import com.ximalaya.ting.lite.main.book.bean.BookWrapperBean;

import org.jetbrains.annotations.NotNull;

import java.util.List;

public class BookHistoryListAdapter extends AbRecyclerViewAdapter<RecyclerView.ViewHolder> {

    private final Context mContext;
    private final List<BookWrapperBean<?>> mList;

    private static float mItemWidth;
    private static float mImgWidth;
    private static float mImgHeight;

    private onBookClickListener mBookEditListener;

    public BookHistoryListAdapter(Context context, List<BookWrapperBean<?>> list) {
        mList = list;
        mContext = context;
    }

    public void setOnBookClickListener(onBookClickListener mBookEditListener) {
        this.mBookEditListener = mBookEditListener;
    }


    @NotNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view;

        if (mItemWidth == 0) {
            mItemWidth = (ScreenUtils.getScreenWidth() - BaseUtil.dp2px(mContext, 16)) / 3f;
            // 图片距离item左右两边留白12dp
            mImgWidth = mItemWidth - BaseUtil.dp2px(mContext, 24);
            mImgHeight = mImgWidth * 1.42f;
        }

        view = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_item_book_history_book_layout, parent, false);

        return new BookHistoryListAdapter.BookViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (mList == null || position < 0 || position >= mList.size()) {
            return;
        }
        final BookWrapperBean<?> wrapperBean = mList.get(position);
        if (wrapperBean == null) {
            return;
        }

        if (wrapperBean.getData() instanceof BookHistoryInfo) {
            final BookHistoryInfo bookHistoryInfo = (BookHistoryInfo) wrapperBean.getData();
            if (bookHistoryInfo == null) {
                return;
            }

            if (holder instanceof BookViewHolder) {
                BookViewHolder viewHolder = (BookViewHolder) holder;

                viewHolder.itemView.setOnClickListener(v -> {
                    if (mBookEditListener != null) {
                        mBookEditListener.onClickBook(position);
                    }
                });

                viewHolder.itemView.setOnLongClickListener(v -> {
                    if (mBookEditListener != null) {
                        mBookEditListener.onLongClickBook(position);
                    }
                    return true;
                });

                viewHolder.tvName.setText(bookHistoryInfo.getBookName());
                ImageManager.from(mContext).displayImage(((BookViewHolder) holder).ivCover, bookHistoryInfo.getBookCover(), R.drawable.host_bg_book_default);

                if (bookHistoryInfo.isOffShelf()) {
                    viewHolder.tvOffShelfCover.setVisibility(View.VISIBLE);
                    viewHolder.tvName.setTextColor(mContext.getResources().getColor(R.color.main_color_4c333333));
                } else {
                    viewHolder.tvOffShelfCover.setVisibility(View.GONE);
                    viewHolder.tvName.setTextColor(mContext.getResources().getColor(R.color.main_color_333333));
                }
            }
        }
    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }

    private static void setItemViewWidth(View itemView) {
        RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) itemView.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new RecyclerView.LayoutParams((int) mItemWidth, ConstraintLayout.LayoutParams.WRAP_CONTENT);
        } else {
            layoutParams.width = (int) mItemWidth;
        }
        itemView.setLayoutParams(layoutParams);
    }

    private static void setCoverViewWidth(View coverView) {
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) coverView.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new ConstraintLayout.LayoutParams((int) mImgWidth, (int) mImgHeight);
        } else {
            layoutParams.width = (int) mImgWidth;
            layoutParams.height = (int) mImgHeight;
        }
        coverView.setLayoutParams(layoutParams);
    }

    @Override
    public Object getItem(int position) {
        if (mList != null && position >= 0 && mList.size() > position) {
            return mList.get(position).getData();
        }
        return null;
    }


    public static class BookViewHolder extends RecyclerView.ViewHolder {
        View itemView;
        RoundImageView ivCover;
        TextView tvName;
        TextView tvOffShelfCover;

        public BookViewHolder(@NonNull View view) {
            super(view);
            itemView = view.findViewById(R.id.main_book_shelf_root);
            ivCover = view.findViewById(R.id.main_book_shelf_iv_cover);
            tvName = view.findViewById(R.id.main_book_shelf_tv_name);
            tvOffShelfCover = view.findViewById(R.id.main_book_shelf_tv_img_cover);

            setItemViewWidth(itemView);
            setCoverViewWidth(ivCover);
        }
    }

    public interface onBookClickListener {
        void onClickBook(int position);

        void onLongClickBook(int position);
    }
}
