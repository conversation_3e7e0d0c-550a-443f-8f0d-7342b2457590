package com.ximalaya.ting.lite.main.home.fragment;

import android.app.Activity;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.handmark.pulltorefresh.library.ILoadingLayout;
import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IGotoTop;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.manager.TempDataManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.customize.BottomStyleCustomizePageManager;
import com.ximalaya.ting.android.host.manager.customize.CustomizeManager;
import com.ximalaya.ting.android.host.manager.earn.AppStoreManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.base.Point;
import com.ximalaya.ting.android.host.model.homepage.HomeAwardSetting;
import com.ximalaya.ting.android.host.model.user.InterestCardSetting;
import com.ximalaya.ting.android.host.model.user.UserTrait;
import com.ximalaya.ting.android.host.util.RequestParamsUtil;
import com.ximalaya.ting.android.host.util.common.CollectionUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.opensdk.util.mmkv.MmkvCommonUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.lite.main.album.listener.IRecommendFeedItemActionListener;
import com.ximalaya.ting.lite.main.base.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.lite.main.constant.BundleValueConstantsInMain;
import com.ximalaya.ting.lite.main.constant.PreferenceConstantsInMain;
import com.ximalaya.ting.lite.main.customize.CustomizeFragment;
import com.ximalaya.ting.lite.main.home.adapter.HomeRecommendAdapter;
import com.ximalaya.ting.lite.main.home.manager.HomeExcitationVideoManager;
import com.ximalaya.ting.lite.main.home.manager.HomeRecommendAdapterAddFloorManager;
import com.ximalaya.ting.lite.main.home.manager.HomeTingUpdateMaskManager;
import com.ximalaya.ting.lite.main.home.presenter.HomeRecommendContact;
import com.ximalaya.ting.lite.main.home.view.NoticeView;
import com.ximalaya.ting.lite.main.home.viewmodel.HomeRecommendExtraViewModel;
import com.ximalaya.ting.lite.main.model.NoticeModel;
import com.ximalaya.ting.lite.main.model.album.CategoryRecommendMList;
import com.ximalaya.ting.lite.main.model.album.FeedStreamOtherData;
import com.ximalaya.ting.lite.main.model.album.MainAlbumMList;
import com.ximalaya.ting.lite.main.model.album.RecommendAlbumItem;
import com.ximalaya.ting.lite.main.model.album.RecommendItemNew;
import com.ximalaya.ting.lite.main.model.album.RecommendRealTimeFeedModel;
import com.ximalaya.ting.lite.main.model.album.RecommendTrackItem;
import com.ximalaya.ting.lite.main.model.album.TitleModule;
import com.ximalaya.ting.lite.main.request.LiteCommonRequest;
import com.ximalaya.ting.lite.main.request.LiteUrlConstants;
import com.ximalaya.ting.lite.main.tab.HomeFragment;
import com.ximalaya.ting.lite.main.utils.MainSearchUtils;
import com.ximalaya.ting.lite.main.utils.OneKeyRadioUtil;
import com.ximalaya.ting.lite.main.utils.SkeletonUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

import static com.ximalaya.ting.android.host.util.constant.MMKVKeyConstantsKt.HAVE_SHOW_NOTICE_IDS;
import static com.ximalaya.ting.lite.main.onekey.OneKeyRadioSettingFragment.KEY_ONEKEY_CATEGORY_SETTING_CHANGE;

/**
 * 分类-具体分类-推荐页面
 *
 * <AUTHOR>
 */
public class HomeRecommendFragment extends BaseFragment2 implements
        IXmPlayerStatusListener, HomeRecommendContact.IFragmentView {

    private static final String TAG = "HomeRecommendFragment";

    private RefreshLoadMoreListView mRefreshLoadMoreListView;
    private ListView mListView;
    private RelativeLayout mainRlSetInterest;
    private ImageView mainIvCloseInterestTip;

    private HomeRecommendAdapterAddFloorManager mAddFloorManager;
    private HomeRecommendAdapter mAdapter;

    private CategoryRecommendMList mData;
    private int mPlaySource = 0;
    private boolean mIsLoadingNetData;

    //是否是首次请求楼层列表，增加曝光埋点使用
    private boolean mIsFirstRequest = true;
    private boolean mIsFirstResume = true;
    private RecommendItemNew mRealTimeRecommendStrongModule; // 实时推荐强露出模块，需要保证最多只存在一个
    private boolean mListDataUpdated; // 列表中有新插入数据，需要更新一下界面
    private boolean mInsertedData; // 是否执行了插入列表的操作

    private HomeAwardSetting mHomeAwardSetting;
    private HomeAwardSetting.GameTimesBean mCurrGameTimesBean;

    private HomeRecommendExtraViewModel mExtraViewModel;//给adapter设置额外的参数

    //第一页，本地用来标识是不是第一页，下拉刷新的时候应该重置为1
    private int mPage = 1;

    private long mModuleId = 0;
    private boolean mCircle;

    private long mLastShowSetInterestTimestamp;//上次显示设置兴趣入口的时间戳

    private long mThreeDayMills = 259200000;//3天的毫秒数，3 * 24 * 60 * 60 * 1000
    private boolean mIsNewUser = true;//默认是新用户
    private int mDefaultScrollY = 0;//默认超过此距离显示设置兴趣入口

    private Set<String> interestIdSet;//存储用户兴趣爱好的id

    /*******滑动埋点用开始********/

    //埋点用 滑动维度 0:horizontal 水平滑动 1:vertical 竖直滑动;
    private int scrollDimen = 1;
    //埋点用 滑动方向 0:在水平滑动时表示向左滑，在竖直滑动时表示向上滑；1:在水平滑动时表示向右滑,在竖直滑动时表示向下滑
    private int scrollDirection;

    private int mScrollHeight;
    private int mScrollStatus = -1;

    private int mListViewWeight;//ListView的宽度
    private int mListViewHeight;//ListView的高度
    private Point topLeftPosition = new Point();
    private Point lowerRightPosition = new Point();

    /*******滑动埋点用结束********/

    private NoticeView noticeView;
    //已经展示的公告的id
    private String haveShowNoticeIds;
    private View.OnClickListener bottomRefreshListener;
    private View.OnClickListener showInterestCardListener;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        interestIdSet = new HashSet<>();
        mRefreshLoadMoreListView = findViewById(R.id.main_listview);
        mainRlSetInterest = findViewById(R.id.mainRlSetInterest);
        mainIvCloseInterestTip = findViewById(R.id.mainIvCloseInterestTip);

        mRefreshLoadMoreListView.setSendScrollListener(false);
        ILoadingLayout loadingLayout = mRefreshLoadMoreListView.getLoadingLayoutProxy();
        loadingLayout.setPullLabel(getStringSafe(R.string.main_pull_to_refresh));
        loadingLayout.setReleaseLabel(getStringSafe(R.string.main_release_to_recommend));
        loadingLayout.setRefreshingLabel(getStringSafe(R.string.main_generating_new_commend_content));
        loadingLayout.setTextColor(Color.BLACK);
        mRefreshLoadMoreListView.setMode(PullToRefreshBase.Mode.PULL_FROM_START);
        mRefreshLoadMoreListView.setIsShowLoadingLabel(true);
        mRefreshLoadMoreListView.setAllHeaderViewColor(Color.BLACK);
        mRefreshLoadMoreListView.setOnRefreshLoadMoreListener(new IRefreshLoadMoreListener() {
            @Override
            public void onRefresh() {
                Logger.d(TAG, "下拉刷新了");
                if (mAdapter != null) {
                    mAdapter.resetValByOnRefresh();
                }
                //loadSearchHint();

                mPage = 1;
                mModuleId = -1;
                mCircle = false;

                interestIdSet.clear();

                loadDataFromNet();

                if (mHomeAwardSetting == null) {
                    requestHomeActivitySetting();
                } else {
                    //当前是没有被使用的，更新一次标题
                    if (mCurrGameTimesBean != null && !mCurrGameTimesBean.isUsed()) {
                        HomeExcitationVideoManager.getInstance().updateTitleBean();
                    }
                }
                if (mCurrGameTimesBean != null) {
                    mCurrGameTimesBean.setReceivedCount(mCurrGameTimesBean.getReceivedCount() + new Random().nextInt(1000) + 100);
                }
            }

            @Override
            public void onMore() {
                Logger.d(TAG, "加载更多了");
                loadDataFromNet();
            }
        });
        mListView = mRefreshLoadMoreListView.getRefreshableView();
        mListView.setFocusable(false);
        mListView.setFocusableInTouchMode(false);
        mListView.setDividerHeight(0);

        initNoticeView();

        mListView.addHeaderView(noticeView);

        mRefreshLoadMoreListView.addOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                Logger.d(TAG, "onScrollStateChanged" + scrollState);
                mScrollStatus = scrollState;
                if (scrollState == AbsListView.OnScrollListener.SCROLL_STATE_IDLE) {
                    traceScrollDepth();
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (getiGotoTop() != null && isRealVisable()) {
                    getiGotoTop().setState(firstVisibleItem > 5);
                }
            }
        });

        mRefreshLoadMoreListView.addScrollHeightListener(scrollHeight -> {
            Logger.d(TAG, "scrollHeight = " + scrollHeight);
            if (mScrollHeight > scrollHeight) {
                //向上滑动
                scrollDirection = 1;
            } else {
                //向下滑动
                scrollDirection = 0;
            }
            //重新resume的时候，由于适配器notify，会再次回调这个方法，所以判断一下mScrollHeight != scrollHeight才调用
            mScrollHeight = scrollHeight;

            if (scrollHeight > mDefaultScrollY) {
                if (System.currentTimeMillis() - mLastShowSetInterestTimestamp >= mThreeDayMills && mIsNewUser
                        && isFromTingBookTab()) {
                    mainRlSetInterest.setVisibility(View.VISIBLE);
                } else {
                    mainRlSetInterest.setVisibility(View.GONE);
                }
            } else {
                mainRlSetInterest.setVisibility(View.GONE);
            }


        });

        if (getActivity() != null && !getActivity().isFinishing()) {
            mListView.setPadding(0, 0, 0, BaseUtil.dp2px(getActivity(), 70));
            mListView.setClipToPadding(false);
        }

        mExtraViewModel = new HomeRecommendExtraViewModel();
        //设置页面来源
        mExtraViewModel.from = isFromTingBookTab() ? BundleValueConstantsInMain.FROM_TING_BOOK : BundleValueConstantsInMain.FROM_HOME;


        mAdapter = new HomeRecommendAdapter(this, mExtraViewModel);
        //TODO 本期去掉实时推荐
//      mAdapter.setRecommendFeedItemActionListener(mRecommendFeedItemActionListener);

        mListView.setAdapter(mAdapter);
        mAddFloorManager = new HomeRecommendAdapterAddFloorManager(mAdapter, this);

        mLastShowSetInterestTimestamp = SharedPreferencesUtil.getInstance(mContext).getLong(
                PreferenceConstantsInMain.KEY_LAST_SHOW_SET_INTEREST_TIP_TIMESTAMP, 0);

        //1334dp,大概滑动2屏
        int TWO_SCREEN_HEIGHT = 1334;
        mDefaultScrollY = ConfigureCenter.getInstance().getInt("ximalaya_lite", "sliderDistance",
                BaseUtil.dp2px(mContext, TWO_SCREEN_HEIGHT));

        initListener();

        UserInfoMannage.getInstance().addLoginStatusChangeListener(mLoginStatusChangeListener);
        hasLoadData = true;

        checkIsNewUser();
        loadData();
        mRefreshLoadMoreListView.post(new Runnable() {
            @Override
            public void run() {
                mListViewWeight = mRefreshLoadMoreListView.getWidth();
                lowerRightPosition.setX(mListViewWeight);
                mListViewHeight = mRefreshLoadMoreListView.getHeight();
            }
        });
        getNotice();


        if (!isFromTingBookTab()) {
            bottomRefreshListener = new View.OnClickListener() {

                @Override
                public void onClick(View v) {
                    if (canUpdateUi()) {
                        mRefreshLoadMoreListView.setRefreshing(true);
                    }
                }
            };

            showInterestCardListener = new View.OnClickListener() {

                @Override
                public void onClick(View v) {
                    if (!OneClickHelper.getInstance().onClick(v)) {
                        return;
                    }
                    dealBottomStyleCustomShow();
                }
            };
            mRefreshLoadMoreListView.setFooterRefreshClickListener(bottomRefreshListener);

            mRefreshLoadMoreListView.setFooterSetInterestClickListener(showInterestCardListener);
        }
    }

    private void initNoticeView() {
        if (noticeView == null) {
            noticeView = new NoticeView(mContext);
            //关闭的时候移除HeadView
            noticeView.setOnCloseListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (canUpdateUi()) {
                        mListView.removeHeaderView(v);
                    }
                }
            });
            noticeView.setOnDetailClickListener(new Function1<String, Unit>() {
                @Override
                public Unit invoke(String url) {
                    if (canUpdateUi()) {
                        ToolUtil.clickUrlAction(HomeRecommendFragment.this, url, null);
                    }
                    return null;
                }
            });
        }

        //获取已经展示的公告的id
        haveShowNoticeIds = MmkvCommonUtil.getInstance(mContext).getString(HAVE_SHOW_NOTICE_IDS,
                "");


    }

    /**
     * 检查是不是新用户
     */
    private void checkIsNewUser() {
        CommonRequestM.queryTrait(new IDataCallBack<UserTrait>() {
            @Override
            public void onSuccess(@Nullable UserTrait userTrait) {
                if (userTrait != null) {
                    mIsNewUser = userTrait.isIsNewUser();
                    mExtraViewModel.isNewUser = mIsNewUser;
                }
            }

            @Override
            public void onError(int code, String message) {
                //do nothing
            }
        });
    }

    @Override
    protected void loadData() {
        if (!canUpdateUi()) {
            return;
        }
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        loadDataFromNet();
    }

    private void loadDataFromNet() {
        if (mIsLoadingNetData) {
            return;
        }
        mIsLoadingNetData = true;
        Map<String, String> params = new HashMap<>();

        // 添加上次播放的电台id传给服务端
        String lastRadioId = OneKeyRadioUtil.getLastRadiosStr(mContext);
        if (!TextUtils.isEmpty(lastRadioId)) {
            params.put("lastRadioId", lastRadioId);
        }

        if (mPage > 1) {
            //不是第一页，要传递moduleId和circle
            params.put("moduleId", String.valueOf(mModuleId));
            params.put("circle", String.valueOf(mCircle));
        }

        //添加有限信息流相关参数,大于0需要添加字段，小于0不能传字段
        FeedStreamOtherData feedStreamOtherData = mAddFloorManager.getFeedStreamOtherData();
        if (feedStreamOtherData != null) {
            if (feedStreamOtherData.oldestTimeline > 0) {
                params.put("oldestTimeline", String.valueOf(feedStreamOtherData.oldestTimeline));
            }
            if (feedStreamOtherData.newestTimeline > 0) {
                params.put("newestTimeline", String.valueOf(feedStreamOtherData.newestTimeline));
            }
        }

        if (CollectionUtil.isNotEmpty(interestIdSet)) {
            StringBuilder builder = new StringBuilder();
            for (String interestId : interestIdSet) {
                builder.append(interestId);
                builder.append(",");
            }

            String interests = builder.toString();
            interests = interests.substring(0, interests.length() - 1);
            Logger.d(TAG, "interests = " + interests);

            params.put("lastInterest", interests);
        }

        IDataCallBack<CategoryRecommendMList> callback = new IDataCallBack<CategoryRecommendMList>() {
            @Override
            public void onSuccess(final CategoryRecommendMList object) {
                if (!canUpdateUi()) {
                    return;
                }
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        mIsLoadingNetData = false;
                        if (!canUpdateUi()) {
                            return;
                        }

                        //每次请求成功，使用过后，重置下mModuleId和mCircle
                        mModuleId = 0;
                        mCircle = false;

                        //请求成功后，需要重置信息流清除参数
                        mAddFloorManager.resetFeedStreamOtherData();

                        mRefreshLoadMoreListView.onRefreshComplete();
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        if (object == null) {
                            loadErrorPage();
                            return;
                        }
                        List<MainAlbumMList> tempList = object.getList();

                        //第一页就没加载到数据，展示错误界面
                        if (CollectionUtil.isNullOrEmpty(tempList) && mPage == 1) {
                            loadErrorPage();
                            return;
                        }

                        //第二页，第三页数据也可能为空，如果为空的话，不展示错误界面
                        if (CollectionUtil.isNullOrEmpty(tempList)) {
                            setHasMore(true);
                            mRefreshLoadMoreListView.onRefreshComplete(true);
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            return;
                        }

                        /*
                         * 查找最后一个moduleType不是35的 moduleId 和 circle 属性
                         * 从后向前遍历，效率更好
                         */
                        for (int i = tempList.size() - 1; i >= 0; i--) {
                            MainAlbumMList albumMList = tempList.get(i);
                            if (albumMList == null) {
                                continue;
                            }
                            if (albumMList.getModuleType() != MainAlbumMList.MODULE_FEED_STREAM && albumMList.getModuleType() != MainAlbumMList.MODULE_FEED_STREAM_V2) {
                                mModuleId = albumMList.getModuleId();
                                mCircle = albumMList.isCircle();
                                break;
                            }
                        }

                        //查找interestId
                        for (int i = tempList.size() - 1; i >= 0; i--) {
                            MainAlbumMList albumMList = tempList.get(i);
                            if (albumMList == null) {
                                continue;
                            }
                            if (albumMList.getModuleType() != MainAlbumMList.MODULE_INTEREST_RECOMMEND) {
                                continue;
                            }
                            interestIdSet.add(albumMList.interestId);
                        }

                        if (mPage == 1) {
                            //下拉刷新完成的时候要上报一次埋点
                            traceScrollDepth();
                        }
                        mData = object;
                        mAddFloorManager.setDataForView(tempList, mPage == 1);
                        mPage++;

                        //没有下一页了
                        if (!object.isHasMore()) {
                            setHasMore(false);
                            mRefreshLoadMoreListView.setFootViewText(getStringSafe(R.string.main_in_the_end_refresh_see_more));
                            if (!isFromTingBookTab()) {
                                mRefreshLoadMoreListView.setFooterSetInterestText(getStringSafe(R.string.main_no_interest_adjust_your_favorite));
                                mRefreshLoadMoreListView.setFooterRefreshText(getStringSafe(R.string.main_refresh));
                            }

                        } else {
                            setHasMore(true);
                        }

                        //增加首屏曝光埋点
                        if (mIsFirstRequest) {
                            AutoTraceHelper.scrollViewItemExposure(HomeRecommendFragment.this, mListView);
                            mIsFirstRequest = false;

                            traceScrollDepth();
                        }
                        updateVideoAd(true);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                mIsLoadingNetData = false;
                if (!canUpdateUi()) {
                    return;
                }
                onPageLoadingCompleted(LoadCompleteType.OK);
                mRefreshLoadMoreListView.onRefreshComplete();
                loadErrorPage();
            }
        };
        String url = LiteUrlConstants.getHomeRecommendsNew() + "/ts-" + System.currentTimeMillis();
        //如果是听书页面，修改为听书的url
        if (isFromTingBookTab()) {
            url = LiteUrlConstants.getTingBookRecommendsNew() + "/ts-" + System.currentTimeMillis();
        }
        LiteCommonRequest.getHomeRecommends(url, params, callback);
    }

    private boolean isFromTingBookTab() {
        return false;
    }

    private void getNotice() {
        LiteCommonRequest.getNotice(new IDataCallBack<List<NoticeModel>>() {
            @Override
            public void onSuccess(@Nullable List<NoticeModel> noticeModelList) {
                if (!canUpdateUi() || CollectionUtil.isNullOrEmpty(noticeModelList)) {
                    return;
                }
                String[] haveShowNoticeIdsArray = haveShowNoticeIds.split(",");
                NoticeModel noticeModel = null;

                for (int i = 0; i < noticeModelList.size(); i++) {
                    noticeModel = noticeModelList.get(i);
                    for (int j = 0; j < haveShowNoticeIdsArray.length; j++) {
                        if (haveShowNoticeIdsArray[j].equals(String.valueOf(noticeModel.getId()))) {
                            noticeModel = null;
                            break;
                        }
                    }
                    //说明找到了
                    if (noticeModel != null) {
                        break;
                    }
                }

                if (noticeModel != null) {
                    String newHaveShowNoticeIds = haveShowNoticeIds + noticeModel.getId() + ",";
                    MmkvCommonUtil.getInstance(mContext).saveString(HAVE_SHOW_NOTICE_IDS, newHaveShowNoticeIds);
                    noticeView.setData(noticeModel);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }
                Logger.i(TAG, "getNotice code = " + code + " msg = " + message);
            }
        });

       /* //延迟两秒获取公告内容
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                String mockText = "{" + "\"content\":\"hello world\",\"url\":\"https://www.baidu.com\"" + "}";
                String text = ConfigureCenter.getInstance().getString("ximalaya_lite", "notice_info",
                        mockText);
                if (!TextUtils.isEmpty(text)) {
                    NoticeModel noticeModel = JsonUtilKt.getInstance().toObject(text, NoticeModel.class);
                    if (canUpdateUi() && noticeModel != null) {
                        noticeView.setData(noticeModel);
                    }
                }
            }
        }, 2000);*/
    }

    @Override
    public void onRefresh() {
        super.onRefresh();
        refreshDelayWhenInterestChanged();
    }

    private void loadErrorPage() {
        //没有数据的时候，加载错误页面
        if (mAdapter == null) {
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
            return;
        }
        if (mAdapter.isEmpty()) {
            onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
        }
    }

    /**
     * 在首屏曝光，下拉刷新完成，滑动状态为停止滑动的时候调用此方法
     */
    private void traceScrollDepth() {
        Logger.d(TAG, "traceScrollDepth");
        topLeftPosition.setY(mScrollHeight);
        lowerRightPosition.setY(mScrollHeight + mListViewHeight);

        int topLeftPositionXdp = BaseUtil.px2dip(mActivity, topLeftPosition.getX());
        int topLeftPositionYdp = BaseUtil.px2dip(mActivity, topLeftPosition.getY());

        int lowerRightPositionXdp = BaseUtil.px2dip(mActivity, lowerRightPosition.getX());
        int lowerRightPositionYdp = BaseUtil.px2dip(mActivity, lowerRightPosition.getY());

        String topLeftValue = topLeftPositionXdp + "," + topLeftPositionYdp;
        String lowerRightValue = lowerRightPositionXdp + "," + lowerRightPositionYdp;

        new XMTraceApi.Trace()
                .setMetaId(18063)
                .setServiceId("scrollDepth")
                .put("currPage", getPageLogicName()) //当前页面
                .put("topLeftPosition", topLeftValue)  //左上边界坐标，格式 "0,0"
                .put("lowerRightPosition", lowerRightValue) //右下边界坐标，格式 "0,0"
                .put("dimension", String.valueOf(scrollDimen)) //滑动维度 0:horizontal 水平滑动 1:vertical 竖直滑动
                .put("direction", String.valueOf(scrollDirection)) //滑动方向  0:在水平滑动时表示向左滑，在竖直滑动时表示向上滑1:在水平滑动时表示向右滑,在竖直滑动时表示向下滑
                .createTrace();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_home_recommend;
    }

    @Override
    protected View getLoadingView() {
        int screenHeight = BaseUtil.getScreenHeight(getActivity()); // 用屏幕高度近似当做Fragment的高度
        int screenWidth = BaseUtil.getScreenWidth(getActivity());
        return SkeletonUtil.generateGeneralSkeleton(getActivity(), screenWidth, screenHeight);
    }

    @Override
    public void onMyResume() {
        //不做状态栏改变操作,tab内
        setFilterStatusBarSet(true);

        super.onMyResume();
        if (getiGotoTop() != null) {
            getiGotoTop().addOnClickListener(mTopBtnListener);
        }

        if (mListDataUpdated) {
            setListDataUpdated(false);
            doAfterAnimation(new IHandleOk() {
                @Override
                public void onReady() {
                    if (mData != null) {
                        mAddFloorManager.setDataForView(mData.getList(), mPage == 1);
                    }
                    updateVideoAd(true);
                }
            });
        }

        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
        XmPlayerManager.getInstance(mContext).addPlayerStatusListener(this);

        dealCustomizeChange();

        dealOneKeyCategorySettingChange();

        if (mHomeAwardSetting == null
                || mHomeAwardSetting.getCurrentStep() == -1
                || (mRequestActivitySettingUserId != -1 && mRequestActivitySettingUserId != UserInfoMannage.getUid())) {
            mCurrGameTimesBean = null;
            mHomeAwardSetting = null;
            requestHomeActivitySetting();
        } else {
            updateVideoAd(mInsertedData);
        }
        mInsertedData = false;

        if (!mIsFirstResume) {
            HomeTingUpdateMaskManager.checkTingUpdataAndShow(mActivity, mAdapter, this, 100);
        }
        mIsFirstResume = false;
    }

    private void dealOneKeyCategorySettingChange() {
        if (!isRealVisable()) {
            return;
        }
        boolean changed = TempDataManager.getInstance().getBoolean(KEY_ONEKEY_CATEGORY_SETTING_CHANGE);
        if (!changed) {
            return;
        }
        TempDataManager.getInstance().saveBoolean(KEY_ONEKEY_CATEGORY_SETTING_CHANGE, false);
        mRefreshLoadMoreListView.setRefreshing(true);
    }

    /**
     * 处理设置页面是否重新选择了兴趣卡片
     * <p>
     * 如果选择了，当前页面需要重新刷新
     */
    private void dealCustomizeChange() {
        if (!isRealVisable()) {
            return;
        }
        CustomizeManager instance = CustomizeManager.getInstance();
        if (!instance.isCustomizeChange()) {
            return;
        }
        instance.setCustomizeChange(false);
        refreshDelayWhenInterestChanged();
        loadSearchHint();
    }

    private void refreshDelayWhenInterestChanged() {
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                if (canUpdateUi()) {
                    mRefreshLoadMoreListView.setRefreshing(true);
                }
            }
        }, 1000);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (!canUpdateUi()) {
            return;
        }
        if (isVisibleToUser && isResumed()) {
            if (getiGotoTop() != null) {
                getiGotoTop().addOnClickListener(mTopBtnListener);
            }
            XmPlayerManager.getInstance(mContext).addPlayerStatusListener(this);
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        } else {
            XmPlayerManager.getInstance(mContext).removePlayerStatusListener(this);
        }
    }


    @Override
    public void onPause() {
        super.onPause();
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
        XmPlayerManager.getInstance(mContext).removePlayerStatusListener(this);
    }

    @Override
    protected String getPageLogicName() {
        return "HomeRecommendFragment";
    }

    private void initListener() {
        mListView.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, final View view, int position, long id) {
                if (!OneClickHelper.getInstance().onClick(view)) {
                    return;
                }
                int index = position - mListView.getHeaderViewsCount();
                if (index < 0 || index >= mAdapter.getCount()) {
                    return;
                }
                ItemModel itemModel = mAdapter.getItem(index);
                Object item = itemModel.getObject();
                if (!(item instanceof AlbumM)) {
                    return;
                }
                AlbumM album = (AlbumM) item;
                AlbumEventManage.startMatchAlbumFragment(album, AlbumEventManage.FROM_DISCOVERY_CATEGORY, mPlaySource, album.getRecSrc(), album.getRecTrack(), -1, getActivity());
            }
        });

        mainRlSetInterest.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mainRlSetInterest.setVisibility(View.GONE);
                mLastShowSetInterestTimestamp = System.currentTimeMillis();
                SharedPreferencesUtil.getInstance(mContext).saveLong(
                        PreferenceConstantsInMain.KEY_LAST_SHOW_SET_INTEREST_TIP_TIMESTAMP, mLastShowSetInterestTimestamp);
                dealCustomShow();
            }
        });

        mainIvCloseInterestTip.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mainRlSetInterest.setVisibility(View.GONE);
                mLastShowSetInterestTimestamp = System.currentTimeMillis();
                SharedPreferencesUtil.getInstance(mContext).saveLong(
                        PreferenceConstantsInMain.KEY_LAST_SHOW_SET_INTEREST_TIP_TIMESTAMP, mLastShowSetInterestTimestamp);
            }
        });
    }

    /**
     * 兴趣卡片展示，先获取兴趣卡片配置，是否要跳过等等
     */
    private void dealCustomShow() {
        CommonRequestM.getInterestCardSetting(new IDataCallBack<InterestCardSetting>() {
            @Override
            public void onSuccess(@NonNull InterestCardSetting setting) {
                Logger.i(TAG, "dealCustomShow success");
                if (canUpdateUi()) {
                    setting.setInterestCardCanSkip(false);
                    setting.setFrom(2);
                    startFragment(CustomizeFragment.newInstance(setting));
                }
            }

            @Override
            public void onError(int code, String message) {
                Logger.i(TAG, "dealCustomShow error code = " + code + " msg = " + message);
                if (canUpdateUi()) {
                    InterestCardSetting setting = new InterestCardSetting();
                    setting.setInterestCardCanSkip(false);
                    setting.setFrom(2);
                    startFragment(CustomizeFragment.newInstance(setting));
                }
            }
        });
    }


    /**
     * 兴趣卡片展示，先获取兴趣卡片配置，是否要跳过等等
     */
    private void dealBottomStyleCustomShow() {
        CommonRequestM.getInterestCardSetting(new IDataCallBack<InterestCardSetting>() {
            @Override
            public void onSuccess(@NonNull InterestCardSetting setting) {
                Logger.i(TAG, "getInterestCardSetting success");

                boolean canShow = setting.getInterestCardCanShow();
                if (!canShow) {
                    return;
                }

                Activity activity = BaseApplication.getMainActivity();
                if (canUpdateUi() && activity instanceof MainActivity) {
                    setting.setFrom(3);
                    BottomStyleCustomizePageManager.requestShowDialog(setting, (MainActivity) activity);
                }
            }

            @Override
            public void onError(int code, String message) {
                Logger.i(TAG, "getInterestCardSetting error code = " + code + " msg = " + message);
            }
        });
    }

    @Override
    protected void onNoContentButtonClick(View view) {
    }

    @Override
    public void onDestroyView() {
        if (getiGotoTop() != null) {
            getiGotoTop().removeOnClickListener(mTopBtnListener);
        }
        XmPlayerManager.getInstance(mContext).removePlayerStatusListener(this);

        if (mAddFloorManager != null) {
            mAddFloorManager.onDestroy();
        }

        super.onDestroyView();
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(mLoginStatusChangeListener);
    }

    private final IGotoTop.IGotoTopBtnClickListener mTopBtnListener = new IGotoTop.IGotoTopBtnClickListener() {
        @Override
        public void onClick(View v) {
            Logger.d("HomeRecommendFragment", "回到顶部");
            if (!isRealVisable()) {
                return;
            }
            if (mListView == null) {
                return;
            }
            mListView.setSelection(0);
        }
    };

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mAdapter != null)
            mAdapter.release();
    }

    private void onLoginStatusChanged() {
        if (!canUpdateUi() || getActivity() == null) {
            return;
        }
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mRefreshLoadMoreListView.setRefreshing(true);
            }
        });
    }

    private void loadSearchHint() {
        if (getParentFragment() != null && isParentCategoryContent()) {
            MainSearchUtils.updateSearchHint(HomeRecommendFragment.this);
        } else {
            MainSearchUtils.updateSearchHint(HomeRecommendFragment.this, 68);
        }
    }

    private boolean isParentCategoryContent() {
        Fragment parent = getParentFragment();
        return parent == null || parent instanceof HomeFragment;
    }

    private ILoginStatusChangeListener mLoginStatusChangeListener = new ILoginStatusChangeListener() {
        @Override
        public void onLogout(LoginInfoModelNew olderUser) {
            //退出登录也需要刷新
            onLoginStatusChanged();
        }

        @Override
        public void onLogin(LoginInfoModelNew model) {
            onLoginStatusChanged();
        }

        @Override
        public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {
            onLoginStatusChanged();
        }
    };

    @Override
    public void onPlayStart() {
        playStatueChange();
    }

    @Override
    public void onPlayPause() {
        playStatueChange();
    }

    @Override
    public void onPlayStop() {
        playStatueChange();
    }

    @Override
    public void onSoundPlayComplete() {
        playStatueChange();
    }

    @Override
    public void onSoundPrepared() {

    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        playStatueChange();
    }

    @Override
    public void onBufferingStart() {

    }

    @Override
    public void onBufferingStop() {

    }

    @Override
    public void onBufferProgress(int percent) {

    }

    @Override
    public void onPlayProgress(int currPos, int duration) {

    }

    @Override
    public boolean onError(XmPlayerException exception) {
        playStatueChange();
        return false;
    }

    private void playStatueChange() {
        if (mAdapter != null && canUpdateUi()) {
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void setHasMore(boolean hasMore) {
        mRefreshLoadMoreListView.setHasMore(hasMore);
    }

    @Override
    public RefreshLoadMoreListView getRefreshLoadMoreListView() {
        return mRefreshLoadMoreListView;
    }

    @Override
    public HomeRecommendAdapterAddFloorManager getHomeRecommendAdapterAddFloorManager() {
        return mAddFloorManager;
    }

    private IRecommendFeedItemActionListener mRecommendFeedItemActionListener = new IRecommendFeedItemActionListener() {
        @Override
        public void onItemAction(FeedItemType itemType, long itemContentId, ActionType actionType, long categoryId, final RecommendItemNew itemData, ItemModel itemModel) {
            requestRealTimeData(itemType, itemContentId, actionType, categoryId, itemData, itemModel);
        }

        private void requestRealTimeData(FeedItemType itemType, long itemContentId, ActionType actionType,
                                         long categoryId, final RecommendItemNew itemData, final ItemModel itemModel) {
            if (itemContentId > 0) {
                Map<String, String> params = new HashMap<>();
                params.put("feedItemType", itemType.name());
                params.put("contentId", String.valueOf(itemContentId));
                params.put("actionType", actionType.name());
                params.put("categoryId", String.valueOf(categoryId));
                if (itemData != null) {
                    params.put("itemType", itemData.getItemType());
                }
                params = RequestParamsUtil.addVipShowParam(params);

                LiteCommonRequest.getRealTimeFeed(params, new IDataCallBack<RecommendRealTimeFeedModel>() {
                    @Override
                    public void onSuccess(@Nullable RecommendRealTimeFeedModel object) {
                        if (object != null && !ToolUtil.isEmptyCollects(object.getData())) {
                            List<RecommendItemNew> objectData = object.getData();

                            //根据点击的UiType设置展示的类型，首页默认使用V2展示，如果需要适配其他分类页面，需要修改此处
                            int trackUiType = RecommendTrackItem.TRACK_ITEM_UI_TYPE_STYLE_V2;
                            int albumUiType = RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V2;
                            if (itemData != null && RecommendItemNew.RECOMMEND_ITEM_TRACK.equals(itemData.getItemType())) {
                                if (itemData.getItem() instanceof RecommendTrackItem) {
                                    RecommendTrackItem trackItem = (RecommendTrackItem) itemData.getItem();
                                    trackUiType = trackItem.getUiType();
                                    //当前点击的是声音类型，根据声音类型设置专辑显示类型
                                    if (trackUiType == RecommendTrackItem.TRACK_ITEM_UI_TYPE_DEF) {
                                        albumUiType = RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_DEF;
                                    } else if (trackUiType == RecommendTrackItem.TRACK_ITEM_UI_TYPE_STYLE_V2) {
                                        albumUiType = RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V2;
                                    }
                                }
                            } else if (itemData != null && RecommendItemNew.RECOMMEND_ITEM_ALBUM.equals(itemData.getItemType())) {
                                if (itemData.getItem() instanceof RecommendAlbumItem) {
                                    RecommendAlbumItem albumItem = (RecommendAlbumItem) itemData.getItem();
                                    albumUiType = albumItem.getUiType();
                                    //当前点击的是专辑类型，根据点击的专辑类型设置声音类型显示的样式
                                    if (albumUiType == RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_DEF) {
                                        trackUiType = RecommendTrackItem.TRACK_ITEM_UI_TYPE_DEF;
                                    } else if (albumUiType == RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V2 || albumUiType == RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V3) {
                                        trackUiType = RecommendTrackItem.TRACK_ITEM_UI_TYPE_STYLE_V2;

                                        //修正实时推荐如果是RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V3的时候也修改为RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V2
                                        albumUiType = RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V2;
                                    }
                                    //TODO 如果需要适配实时推荐，需要注意接口是否返回了评分数据
                                    //来源如果是听书页面，强制修改为带评分的
                                    if (isFromTingBookTab()) {
                                        albumUiType = RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V3;
                                    }
                                }
                            }

                            for (int i = 0; i < objectData.size(); i++) {
                                RecommendItemNew itemNew = objectData.get(i);
                                if (itemNew == null) {
                                    continue;
                                }
                                //动态设置实时推荐的展示类型
                                if (RecommendItemNew.RECOMMEND_ITEM_TRACK.equals(itemNew.getItemType())) {
                                    if (itemNew.getItem() instanceof RecommendTrackItem) {
                                        RecommendTrackItem trackItem = (RecommendTrackItem) itemNew.getItem();
                                        trackItem.setUiType(trackUiType);
                                    }
                                } else if (RecommendItemNew.RECOMMEND_ITEM_ALBUM.equals(itemNew.getItemType())) {
                                    if (itemNew.getItem() instanceof RecommendAlbumItem) {
                                        RecommendAlbumItem albumItem = (RecommendAlbumItem) itemNew.getItem();
                                        albumItem.setUiType(albumUiType);
                                    }
                                }
                            }

                            List<RecommendItemNew> insertData;
                            List<ItemModel> bodyData = new ArrayList<>();
                            if (getBodyData() != null) {
                                bodyData.addAll(getBodyData());
                            }
                            if (object.getDisplayType() == RecommendRealTimeFeedModel.DISPLAY_TYPE_HORIZONTAL) {
                                // 需要保证只显示一个，把之前的移除。
                                if (mRealTimeRecommendStrongModule != null) {
                                    if (!ToolUtil.isEmptyCollects(bodyData)) {
                                        int index = -1;
                                        for (int i = bodyData.size() - 1; i >= 0; i--) {
                                            ItemModel itemModel = bodyData.get(i);
                                            if (itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_STRONG_MODULE) {
                                                index = i;
                                                break;
                                            }
                                        }
                                        if (index != -1) {
                                            bodyData.remove(index);
                                        }
                                    }
                                }
                                insertData = new ArrayList<>();
                                mRealTimeRecommendStrongModule = object.covertToRecommendItemNew();
                                insertData.add(mRealTimeRecommendStrongModule);
                            } else {
                                insertData = object.getData();
                            }
                            ItemModel insertAfterItem = itemModel;
                            if (RecommendRealTimeFeedModel.INSERT_POSITION_TYPE_NEXT_BATCH_FIRST
                                    .equals(object.getDataPositionType())) {
                                if (mRefreshLoadMoreListView != null && mRefreshLoadMoreListView.getRefreshableView() != null && mAdapter != null) {
                                    int lastVisibleItemIndex = mRefreshLoadMoreListView.getRefreshableView().getLastVisiblePosition()
                                            - mRefreshLoadMoreListView.getRefreshableView().getHeaderViewsCount();
                                    ItemModel itemModel = mAdapter.getItem(lastVisibleItemIndex);
                                    int index = lastVisibleItemIndex;
                                    while (true) {
                                        if (itemModel == null || itemModel.getObject() instanceof RecommendItemNew) {
                                            break;
                                        }
                                        index++;
                                        itemModel = mAdapter.getItem(index);
                                    }
                                    if (itemModel != null && itemModel.getObject() instanceof RecommendItemNew) {
                                        insertAfterItem = itemModel;
                                    }
                                }
                            }
                            // 如果在body里找不到，说明是在header里的，那就插在body的头部
                            int insertPosition = bodyData.indexOf(insertAfterItem) + 1; // -1加1等于0，也没问题
                            insertPosition = Math.min(insertPosition, bodyData.size());
                            // 加在糖葫芦，一键听后面，信息流的头部
                            if (insertPosition == 0) {
                                for (int i = 0; i < bodyData.size() - 1; i++) {
                                    ItemModel model = bodyData.get(i);
                                    if (model != null && model.getViewType() != HomeRecommendAdapter.VIEW_TYPE_TITLE_NORMAL &&
                                            model.getViewType() != HomeRecommendAdapter.VIEW_TYPE_CALABASH_LINE &&
                                            model.getViewType() != HomeRecommendAdapter.VIEW_TYPE_ONEKEY_LISTENER
                                            && model.getViewType() != HomeRecommendAdapter.VIEW_TYPE_ONE_KEY_RADIO
                                            && model.getViewType() != HomeRecommendAdapter.VIEW_TYPE_ONE_KEY_RADIO_SETTING) {
                                        insertPosition = i;
                                        break;
                                    }
                                }
                            }
                            for (int i = insertData.size() - 1; i >= 0; i--) {
                                RecommendItemNew recommendItem = insertData.get(i);
                                if (recommendItem == null) {
                                    continue;
                                }
                                if (RecommendItemNew.RECOMMEND_ITEM_TRACK.equals(recommendItem.getItemType())) {
                                    if (recommendItem.getItem() instanceof RecommendTrackItem) {
                                        RecommendTrackItem trackItem = (RecommendTrackItem) recommendItem.getItem();
                                        if (trackItem.getUiType() == RecommendTrackItem.TRACK_ITEM_UI_TYPE_STYLE_V2) {
                                            bodyData.add(insertPosition, new ItemModel(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_TRACK_STYLE_V2));
                                        } else {
                                            bodyData.add(insertPosition, new ItemModel(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_TRACK));
                                        }
                                    }
                                } else if (RecommendItemNew.RECOMMEND_ITEM_ALBUM.equals(recommendItem.getItemType())) {
                                    if (recommendItem.getItem() instanceof RecommendAlbumItem) {
                                        RecommendAlbumItem albumItem = (RecommendAlbumItem) recommendItem.getItem();
                                        if (albumItem.getUiType() == RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V2) {
                                            bodyData.add(insertPosition, new ItemModel(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V2));
                                        } else if (albumItem.getUiType() == RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V3) {
                                            bodyData.add(insertPosition, new ItemModel(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V3));
                                        } else if (albumItem.getUiType() == RecommendAlbumItem.ALBUM_ITEM_UI_TYPE_STYLE_V4) {
                                            bodyData.add(insertPosition, new ItemModel(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V4));
                                        } else {
                                            bodyData.add(insertPosition, new ItemModel(recommendItem, HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM));
                                        }
                                    }
                                } else if (RecommendItemNew.RECOMMEND_TYPE_LOCAL_REAL_TIME_RECOMMEND.equals(recommendItem.getItemType())) {
                                    bodyData.add(insertPosition, new ItemModel(recommendItem, HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_STRONG_MODULE));
                                } else if (RecommendItemNew.RECOMMEND_TYPE_LOCAL_AD_CUSTOM.equals(recommendItem.getItemType())) {
                                    bodyData.add(insertPosition, new ItemModel(recommendItem, HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION));
                                } else if (RecommendItemNew.RECOMMEND_TYPE_LOCAL_AD_CUSTOM_STYLE3.equals(recommendItem.getItemType())) {
                                    bodyData.add(insertPosition, new ItemModel(recommendItem, HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3));
                                } else if (RecommendItemNew.RECOMMEND_TYPE_LOCAL_AD_CUSTOM_STYLE4.equals(recommendItem.getItemType())) {
                                    bodyData.add(insertPosition, new ItemModel(recommendItem, HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4));
                                }
                            }

                            if (isRealVisable() && mAdapter != null && canUpdateUi()) {
                                mInsertedData = true;
                                mAddFloorManager.setDataForViewWithRealTimeRecommendFeedItem(bodyData);
                                updateVideoAd(mInsertedData);
                            } else {
                                setListDataUpdated(true);
                            }
                        }
                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                });
            }
        }
    };

    private List<ItemModel> getBodyData() {
        if (mAdapter != null) {
            return mAdapter.getListData();
        }
        return null;
    }

    private void setListDataUpdated(boolean listDataUpdated) {
        mListDataUpdated = listDataUpdated;
    }

    private long mRequestActivitySettingUserId = -1;

    private void requestHomeActivitySetting() {
        //信息流红包接入审核开关
        if (AppStoreManager.isAppStoreEnable()) {
            return;
        }
        //只有在听页添加
        if (isFromTingBookTab()) {
            CommonRequestM.queryActivitySetting(new IDataCallBack<HomeAwardSetting>() {
                @Override
                public void onSuccess(@Nullable HomeAwardSetting object) {
                    if (!canUpdateUi()) {
                        return;
                    }
                    mRequestActivitySettingUserId = UserInfoMannage.getUid();
                    mHomeAwardSetting = object;
                    HomeExcitationVideoManager.getInstance().setHomeAwardStyle();
                    if (mHomeAwardSetting != null) {
                        HomeExcitationVideoManager.getInstance().setTitlesBeans(mHomeAwardSetting);
                        List<HomeAwardSetting.GameTimesBean> gameTimes = mHomeAwardSetting.getGameTimes();
                        if (!ToolUtil.isEmptyCollects(gameTimes)) {
                            boolean isSetShowTime = false;
                            for (HomeAwardSetting.GameTimesBean gameTimesBean : gameTimes) {
                                gameTimesBean.setViewStyle(HomeExcitationVideoManager.getInstance().getHomeAwardStyle());
                                if (gameTimesBean.getStep() < mHomeAwardSetting.getCurrentStep()
                                        // 等于-1表示没有任何剩余的时间了
                                        || mHomeAwardSetting.getCurrentStep() == -1) {
                                    gameTimesBean.setUsed(true);
                                } else if (!isSetShowTime) {
                                    isSetShowTime = true;
                                    gameTimesBean.setShowTime(System.currentTimeMillis());
                                }
                            }
                        }
                    }
                    updateVideoAd(true);
                }

                @Override
                public void onError(int code, String message) {
                    mHomeAwardSetting = null;
                }
            });
        }
    }

    private void updateVideoAd(boolean resertData) {
        if (mHomeAwardSetting == null) {
            return;
        }
        if (mCurrGameTimesBean != null && !mCurrGameTimesBean.isUsed() && !resertData) {
            return;
        }
        List<ItemModel> bodyData = getBodyData();
        if (ToolUtil.isEmptyCollects(bodyData)) {
            return;
        }
        //for禁止remove,此处移除后，立即进行了break，所以不会出问题
        for (ItemModel itemModel : bodyData) {
            if (itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3 ||
                    itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4 ||
                    itemModel.getViewType() == HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION) {
                bodyData.remove(itemModel);
                mAdapter.notifyDataSetChanged();
                break;
            }
        }

        if (mHomeAwardSetting != null && mHomeAwardSetting.getRet() == 0 && !ToolUtil.isEmptyCollects(mHomeAwardSetting.getGameTimes())) {

            List<HomeAwardSetting.GameTimesBean> gameTimes = mHomeAwardSetting.getGameTimes();

            boolean hasAdInserted = false;
            for (int i = 0; i < gameTimes.size(); i++) {
                HomeAwardSetting.GameTimesBean gameTimesBean = gameTimes.get(i);
                if (gameTimesBean.isUsed()) {
                    continue;
                }
                //查找第一个信息流的位置
                int streamIndex = -1;
                for (int j = 0; j < bodyData.size(); j++) {
                    ItemModel model = bodyData.get(j);
                    if (model == null) {
                        continue;
                    }
                    if (model.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM
                            || model.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V2
                            || model.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V3
                            || model.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_ALBUM_STYLE_V4
                            || model.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_TRACK
                            || model.getViewType() == HomeRecommendAdapter.VIEW_TYPE_FEED_TRACK_STYLE_V2) {
                        streamIndex = j;
                        break;
                    }
                }
                int needAddPosition = gameTimesBean.getPosition() + streamIndex;
                if (bodyData.size() > needAddPosition - 1 && needAddPosition - 1 >= 0) {
                    ItemModel checkTitleModel = bodyData.get(needAddPosition - 1);
                    //当前要插入
                    if (checkTitleModel != null && checkTitleModel.getObject() instanceof TitleModule) {
                        needAddPosition++;
                    }
                }
                if (streamIndex >= 0 && bodyData.size() > needAddPosition) {
                    if (i > 0) {
                        if (gameTimesBean.getShowTime() <= 0) {
                            gameTimesBean.setShowTime(System.currentTimeMillis() +
                                    gameTimes.get(i - 1).getTime() * 60 * 1000);
                        }
                    } else {
                        if (gameTimesBean.getShowTime() <= 0) {
                            gameTimesBean.setShowTime(System.currentTimeMillis());
                        }
                    }

                    if (gameTimesBean.getReceivedCount() <= 0) {
                        gameTimesBean.setReceivedCount(50 * 10000 + new Random().nextInt(50 * 10000));
                    }
                    int remaining = 0;
                    //设置还剩几个红包
                    for (HomeAwardSetting.GameTimesBean bean : gameTimes) {
                        if (bean == null) {
                            continue;
                        }
                        if (!bean.isUsed()) {
                            remaining++;
                        }
                    }
                    //样式是，后面还剩，除去当前这个
                    remaining = remaining - 1;
                    if (remaining <= 0) {
                        remaining = 0;
                    }
                    gameTimesBean.setRemaining(remaining);

                    int viewType = HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION;
                    int homeAwardStyle = HomeExcitationVideoManager.getInstance().getHomeAwardStyle();
                    if (homeAwardStyle == 3) {
                        viewType = HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE3;
                    } else if (homeAwardStyle == 4) {
                        viewType = HomeRecommendAdapter.VIEW_TYPE_REAL_TIME_RECOMMEND_EXCITATION_STYLE4;
                    }
                    ItemModel itemModel = new ItemModel<HomeAwardSetting.GameTimesBean>(gameTimesBean, viewType);
                    bodyData.add(needAddPosition, itemModel);
                    mCurrGameTimesBean = gameTimesBean;
                    hasAdInserted = true;
                }

                break;
            }

            if (hasAdInserted) {
                mAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    protected boolean isShowSevenDay() {
        return true;
    }
}

