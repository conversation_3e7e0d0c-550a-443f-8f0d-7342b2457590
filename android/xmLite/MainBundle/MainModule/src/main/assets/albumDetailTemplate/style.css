html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header,
menu, nav, output, ruby, section, summary,
time, mark, audio, video, input {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font-weight: normal;
  vertical-align: baseline; }

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, menu, nav, section {
  display: block; }

html, body {
  font-family: "lucida grande", "lucida sans unicode", lucida, helvetica, "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif; }

body {
  line-height: 1; }

blockquote, q {
  quotes: none; }

blockquote:before, blockquote:after,
q:before, q:after {
  content: none; }

table {
  border-collapse: collapse;
  border-spacing: 0; }

/* custom */
a {
  color: #7e8c8d;
  text-decoration: none;
  -webkit-backface-visibility: hidden; }

li {
  list-style: none; }

::-webkit-scrollbar {
  width: 5px;
  height: 5px; }

::-webkit-scrollbar-track-piece {
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 6px; }

::-webkit-scrollbar-thumb:vertical {
  height: 5px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px; }

::-webkit-scrollbar-thumb:horizontal {
  width: 5px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px; }

html, body {
  width: 100%; }

body {
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis; }

.page {
  display: block;
  max-width: 640px;
  min-width: 320px;
  margin: auto; }

.rel {
  position: relative; }

.abs {
  position: absolute; }

.fr {
  float: right; }

.fl {
  float: left; }

.clearfix {
  zoom: 1; }
  .clearfix::after {
    content: '';
    display: block;
    width: 100%;
    clear: both; }

.tc {
  text-align: center; }

.txt {
  font-size: 14px; }

.txt1 {
  font-size: 12px; }

.txt2 {
  font-size: 16px; }

.mgt-20 {
  margin-top: 20px; }

.mgtb-20 {
  margin-top: 20px;
  margin-bottom: 20px; }

.pb-10 {
  padding-bottom: 10px; }

.pb-30 {
  padding-bottom: 30px; }
