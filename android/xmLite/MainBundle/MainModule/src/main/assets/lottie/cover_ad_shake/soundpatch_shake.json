{"v": "5.8.1", "fr": 30, "ip": 0, "op": 30, "w": 279, "h": 279, "nm": "手摇手机", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "情况2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0], "e": [22]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [22], "e": [-22]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [-22], "e": [22]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23, "s": [22], "e": [0]}, {"t": 30}], "ix": 10, "x": "var $bm_rt;\nvar range, samples, on_off;\nrange = $bm_div(effect('旋转 - Smoothness')('ADBE Slider Control-0001'), 20);\nsamples = $bm_div(effect('旋转 - Blend Precision')('ADBE Slider Control-0001'), 2);\n$bm_rt = on_off = effect('旋转 - Blend On/Off')('ADBE Checkbox Control-0001');\nif (on_off == 1) {\n    try {\n        $bm_rt = smooth(range, samples);\n    } catch (e$$4) {\n        $bm_rt = smooth(1, 1);\n    }\n} else {\n    $bm_rt = value = value;\n}"}, "p": {"a": 0, "k": [154.5, 229.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [15, 90, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "旋转 - Smoothness", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "旋转 - Blend Precision", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 25, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "旋转 - Blend On/Off", "np": 3, "mn": "ADBE Checkbox Control", "ix": 3, "en": 1, "ef": [{"ty": 7, "nm": "复选框", "mn": "ADBE Checkbox Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.591, 0.362], [0, 0], [0.365, -1.582], [-1.59, -0.362], [0, 0], [-0.365, 1.582]], "o": [[0, 0], [-1.583, -0.36], [-0.367, 1.589], [0, 0], [1.583, 0.36], [0.367, -1.589]], "v": [[-8.338, 60.62], [-34.771, 54.608], [-38.295, 56.818], [-36.078, 60.354], [-9.645, 66.366], [-6.121, 64.156]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-4.192, -2.848], [0, 0], [2.525, -4.064], [4.192, 2.848], [0, 0], [-2.525, 4.064]], "o": [[0, 0], [3.958, 2.689], [-2.675, 4.304], [0, 0], [-3.958, -2.689], [2.675, -4.305]], "v": [[-54.662, 2.804], [-36.3, 15.278], [-33.736, 27.363], [-46.323, 30.034], [-64.686, 17.559], [-67.25, 5.474]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[-4.891, -3.322], [0, 0], [2.946, -4.741], [4.891, 3.322], [0, 0], [-2.946, 4.741]], "o": [[0, 0], [4.617, 3.136], [-3.121, 5.022], [0, 0], [-4.617, -3.136], [3.121, -5.022]], "v": [[-46.788, -18.646], [-26.154, -4.628], [-23.163, 9.471], [-37.848, 12.586], [-58.482, -1.431], [-61.473, -15.53]], "c": true}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [-8.153, -8.059], [0, 0], [0, 0], [-5.175, 6.391], [-0.204, 0.225], [0, 0], [0, 0]], "o": [[-7.265, 8.971], [0, 0], [0, 0], [-5.519, -5.922], [0.192, -0.238], [0, 0], [0, 0], [0, 0]], "v": [[-66.678, -53.643], [-65.034, -23.616], [-60.816, -27.884], [-61.288, -28.369], [-62.015, -49.867], [-61.422, -50.561], [-65.872, -54.585], [-66.281, -54.12]], "c": true}, "ix": 2}, "nm": "路径 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [-11.457, -12.769], [0, 0], [0, 0], [-8.497, 11.689], [0, 0]], "o": [[-9.758, 14.143], [0, 0], [0, 0], [-9.254, -10.891], [0, 0], [0, 0]], "v": [[-77.336, -61.458], [-74.412, -15.478], [-69.946, -19.485], [-70.5, -20.12], [-71.949, -58.683], [-76.803, -62.211]], "c": true}, "ix": 2}, "nm": "路径 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [-6.458, -1.491], [0, 0], [1.491, -6.458], [0, 0], [-0.396, -0.447], [4.111, -14.293], [1.31, -4.266], [-2.078, -3.677], [-0.41, -2.303], [1.221, -1.324], [0.031, -0.036], [4.588, -1.947], [9.199, 2.692], [8.921, 15.491], [0, 0], [-1.491, 6.457], [0, 0], [0, 0], [-2.589, 4.014], [-4.141, -2.924], [0, 0], [2.589, -4.014], [3.161, 0.255], [0, 0], [0, 0], [-1.499, -0.346], [0, 0], [0, 0], [-6.506, 8.881], [-3.427, 5.111], [0, 0], [1.365, 2.299], [0.247, 0.475], [-6.628, 4.009], [-5.143, -5.225], [0, 0], [0, 0], [1.499, 0.346], [0, 0], [0, 0], [0.346, -1.499], [0, 0], [0, 0], [2.525, -4.064], [4.192, 2.848], [0, 0], [-2.525, 4.064], [-2.145, 0.55]], "o": [[1.491, -6.458], [0, 0], [6.458, 1.491], [0, 0], [0.388, 0.439], [10.94, 12.349], [-0.855, 2.973], [-4.989, 16.245], [2.864, 5.069], [0.554, 2.38], [-0.032, 0.035], [-4.587, 5.301], [-12.101, 5.136], [-12.051, -8.461], [0, 0], [-6.458, -1.491], [0, 0], [0, 0], [-3.901, -2.755], [2.747, -4.26], [0, 0], [3.902, 2.755], [-1.849, 2.867], [0, 0], [0, 0], [-0.167, 1.493], [0, 0], [0, 0], [0.967, -5.797], [5.078, -6.932], [0, 0], [-1.254, -4.018], [-0.429, -0.723], [-5.264, -10.133], [3.987, -2.411], [0, 0], [0, 0], [0.167, -1.493], [0, 0], [0, 0], [-1.493, -0.167], [0, 0], [0, 0], [3.958, 2.689], [-2.675, 4.305], [0, 0], [-3.958, -2.689], [1.266, -2.037], [0, 0]], "v": [[-38.274, -63.3], [-23.882, -72.293], [37.503, -58.121], [46.497, -43.729], [39.297, -12.545], [40.472, -11.215], [52.447, 26.369], [49.101, 37.452], [39.322, 79.737], [43.697, 90.806], [42.696, 96.362], [42.603, 96.465], [28.841, 107.337], [-5.966, 111.376], [-37.424, 75.448], [-56.95, 70.94], [-65.943, 56.548], [-62.641, 42.247], [-67.628, 38.726], [-69.977, 26.616], [-57.346, 24.166], [-48.533, 30.389], [-46.184, 42.5], [-54.399, 46.554], [-57.174, 58.572], [-57.233, 58.918], [-54.926, 62.17], [-14.609, 71.478], [-2.734, 72.524], [10.039, 42.656], [20.358, 29.477], [25.295, 8.093], [20.622, -1.685], [19.586, -3.491], [18.406, -26.39], [31.93, -20.644], [37.727, -45.753], [37.786, -46.099], [35.479, -49.352], [-25.907, -63.524], [-26.252, -63.582], [-29.505, -61.275], [-35.722, -34.344], [-28.308, -29.308], [-25.745, -17.223], [-38.332, -14.553], [-47.229, -20.597], [-49.792, -32.681], [-44.435, -36.613]], "c": true}, "ix": 2}, "nm": "路径 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [9.075, 7.005], [0, 0], [0, 0], [4.358, -6.974], [0.175, -0.248], [0, 0], [0, 0]], "o": [[6.117, -9.79], [0, 0], [0, 0], [6.199, 5.205], [-0.162, 0.259], [0, 0], [0, 0], [0, 0]], "v": [[64.811, -34.211], [59.52, -63.814], [55.853, -59.064], [56.381, -58.639], [59.723, -37.391], [59.218, -36.63], [64.126, -33.177], [64.475, -33.689]], "c": true}, "ix": 2}, "nm": "路径 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [12.928, 11.278], [0, 0], [0, 0], [7.009, -12.638], [0, 0]], "o": [[7.962, -15.227], [0, 0], [0, 0], [10.512, 9.682], [0, 0], [0, 0]], "v": [[76.174, -27.825], [67.668, -73.105], [63.724, -68.584], [64.351, -68.022], [70.489, -29.922], [75.736, -27.012]], "c": true}, "ix": 2}, "nm": "路径 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 30, "st": 0, "bm": 0}], "markers": []}