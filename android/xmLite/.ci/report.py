# -*- coding:utf-8 -*-
import json
from subprocess import <PERSON><PERSON>, PIPE

import sys

ding_host = "https://oapi.dingtalk.com/robot/send?access_token="
git_commit_max_len = 5
success_is_at_all = False
fail_is_at_all = True


def do_shell(shell_str):
    with Popen(shell_str, shell=True, stdout=PIPE, stderr=PIPE):
        pass


def shell(shell_str):
    with Popen(shell_str, shell=True, stdout=PIPE, stderr=PIPE) as p:
        return p.stdout.read()


def post_ding(ding_msg, ding_token):
    url = ding_host + ding_token
    do_shell("curl {} -H 'Content-Type: application/json' -d '{}' ".format(url, ding_msg))
    pass


def make_markdown_json(ding_title, ding_text, is_at_all=False, phones=""):
    if ding_title and ding_text:
        json_map = {
            "msgtype": "markdown",
            "markdown": {
                "title": ding_title,
                "text": ding_text
            },
            "at": {
                "atMobiles": [phones],
                "isAtAll": is_at_all
            }
        }
        return json.dumps(json_map)
    else:
        return None


def report_build_success(ding_token, project_path, job_id):
    ref_log_hash_command = 'git --no-pager reflog show --pretty=format:"%H"'
    with Popen(ref_log_hash_command, shell=True, stdout=PIPE, stderr=PIPE) as p:
        arr_hash = p.stdout.readlines()
    if arr_hash:
        current_commit_hash = str(arr_hash[0], encoding='utf-8').replace("\n", "").strip()
        last_commit_hash = str(arr_hash[1], encoding='utf-8').replace("\n", "").strip()
        diff_commit_cmd = 'git --no-pager log {}..{} --pretty=format:"%h %s"'.format(last_commit_hash,
                                                                                     current_commit_hash)
        title = None
        content = "GitlabCi Pipeline Job Success ${}\n\n".format(job_id)
        commit_prefix = "http://gitlab.ximalaya.com/{}/commit/".format(project_path)
        with Popen(diff_commit_cmd, shell=True, stdout=PIPE, stderr=PIPE) as p:
            result = p.stdout.readlines()
            max_len = len(result) if len(result) < git_commit_max_len else git_commit_max_len
            for index in range(max_len):
                line = result[index]
                commit_msg = str(line, encoding='utf-8')
                commit_msg_items = commit_msg.split(" ")
                commit_hash = commit_msg_items[0]
                inner_msg = commit_msg[commit_msg.find(" ") + 1:]
                if not title:
                    title = "成功 " + inner_msg
                content += "> [{}]({}) {} \n\n".format(commit_hash, commit_prefix + commit_hash, inner_msg)
                print(content)
        content += "[下载产物](http://gitlab.ximalaya.com/{0}/-/jobs/{1}/artifacts/download) | [任务详情](http://gitlab.ximalaya.com/{0}/-/jobs/{1}) \n".format(
            project_path, job_id)
        msg = make_markdown_json(title, content, is_at_all=success_is_at_all)
        post_ding(msg, ding_token)
    pass


def report_build_fail(ding_token, project_path, pipeline_id):
    ref_log_hash_command = 'git --no-pager reflog show --pretty=format:"%H"'
    with Popen(ref_log_hash_command, shell=True, stdout=PIPE, stderr=PIPE) as p:
        arr_hash = p.stdout.readlines()
    if arr_hash:
        current_commit_hash = str(arr_hash[0], encoding='utf-8').replace("\n", "").strip()
        last_commit_hash = str(arr_hash[1], encoding='utf-8').replace("\n", "").strip()
        diff_commit_cmd = 'git --no-pager log {}..{} --pretty=format:"%h %s"'.format(last_commit_hash,
                                                                                     current_commit_hash)
        title = None
        content = "GitlabCi Pipeline Failure ${}\n\n".format(pipeline_id)
        commit_prefix = "http://gitlab.ximalaya.com/{}/commit/".format(project_path)
        with Popen(diff_commit_cmd, shell=True, stdout=PIPE, stderr=PIPE) as p:
            result = p.stdout.readlines()
            max_len = len(result) if len(result) < git_commit_max_len else git_commit_max_len
            for index in range(max_len):
                line = result[index]
                commit_msg = str(line, encoding='utf-8')
                commit_msg_items = commit_msg.split(" ")
                commit_hash = commit_msg_items[0]
                inner_msg = commit_msg[commit_msg.find(" ") + 1:]
                if not title:
                    title = "失败 " + inner_msg
                content += "> [{}]({}) {} \n\n".format(commit_hash, commit_prefix + commit_hash, inner_msg)
                print(content)
        content += "[流水线详情](http://gitlab.ximalaya.com/{0}/pipelines/{1})\n".format(
            project_path, pipeline_id)
        msg = make_markdown_json(title, content, is_at_all=fail_is_at_all)
        post_ding(msg, ding_token)
    pass


if __name__ == "__main__":
    if bool(sys.argv[1]):
        report_build_success(sys.argv[2], sys.argv[3], sys.argv[4])
    else:
        report_build_fail(sys.argv[2], sys.argv[3], sys.argv[4])
