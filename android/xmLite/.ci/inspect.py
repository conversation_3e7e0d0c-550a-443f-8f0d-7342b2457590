# -*- coding:utf-8 -*-
import os, sys
import xml.dom.minidom

result_report_name = "inspect.md"


class Issue:
    def __init__(self, file, line, description, module, package, entry_point):
        self.file = file
        self.line = line
        self.description = description
        self.module = module
        self.package = package
        self.entry_point = entry_point

    def md_format_str(self):
        return "### {} line: {}\t\n- {}\t\n\n".format(self.file, self.line, self.description)


def create_issue(problem):
    return Issue(parse_xml_node_value(problem, "file"), parse_xml_node_value(problem, "line"),
                 parse_xml_node_value(problem, "description"), parse_xml_node_value(problem, "module"),
                 parse_xml_node_value(problem, "package"), parse_xml_node_value(problem, "entry_point"))


def read(path):
    if not os.path.exists(path):
        return None
    try:
        with open(path, "r") as file:
            return file.read().strip()
    except Exception as e:
        print(e)
        return None


def get_xml_path_dict(dir_path):
    xml_dict = {}
    # 列出文件夹下所有的目录与文件
    file_list = os.listdir(dir_path)
    for i in range(0, len(file_list)):
        file_name = file_list[i]
        path = os.path.join(dir_path, file_name)
        if os.path.isfile(path) and file_name.endswith(".xml"):
            xml_dict[file_name] = path
    return xml_dict


def parse_xml_node_value(node, sub_name):
    if node:
        sub_node = node.getElementsByTagName(sub_name)
        if sub_node and sub_node[0].childNodes:
            return sub_node[0].childNodes[0].nodeValue
    return None


# 获取问题列表
def parse_xml_issues(xml_path):
    if not xml_path:
        return
    # 打开xml文档
    dom = xml.dom.minidom.parse(xml_path)
    # 得到文档元素对象
    root = dom.documentElement
    problems = root.getElementsByTagName("problem")
    issues = []
    for problem in problems:
        issue = create_issue(problem)
        issues.append(issue)
    return issues


def write_lines(path, lines):
    name = os.path.basename(path)
    dir_path = path[:path.rfind(name)]
    if not os.path.exists(dir_path):
        os.mkdir(dir_path)
    try:
        with open(path, "w", encoding='utf-8') as file:
            file.writelines(lines)
    except Exception as e:
        print(e)
    pass


def write_result(issues, report_path):
    ready_to_write_lines = []
    for issue in issues:
        ready_to_write_lines.append(issue.md_format_str())
    write_lines(report_path, ready_to_write_lines)
    pass


def deal_result(result_path, result_report_path=None):
    print("处理分析结果")
    xml_dict = get_xml_path_dict(result_path)
    print("xml_dict:{}".format(xml_dict))
    constants_condition_path = xml_dict.get('ConstantConditions.xml')
    if not constants_condition_path:
        print("未找到constants_condition文件")
        return
    if not result_report_path:
        result_report_path = os.path.join("./", result_report_name)
    issues = parse_xml_issues(constants_condition_path)
    if issues:
        print("将分析结果写入报告文件:{}".format(result_report_path))
        write_result(issues, result_report_path)
        print("报告生成完成")
    pass


def inspect_check(inspect_bin, project, config, output_path, report_path=None, inspect_target_dir=None):
    # https://www.jetbrains.com/help/idea/command-line-code-inspector.html
    # inspect project-path inspection-path output-path [-vX] [-d subdirectory-path]
    if not output_path:
        raise Exception("output_path is empty")
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    command_line = '{}  {} {} {} -v2'.format(inspect_bin, project, config, output_path)
    if inspect_target_dir:
        command_line += " -d {}".format(inspect_target_dir)
    os.system(command_line)
    # 检查执行
    deal_result(output_path, report_path)
    pass


if __name__ == '__main__':
    if len(sys.argv) >= 6:
        inspect_check(sys.argv[1], sys.argv[2], sys.argv[3], sys.argv[4], sys.argv[5],
                      sys.argv[6] if len(sys.argv) == 7 else None)
    else:
        raise Exception("参数不正确,args:{}".format(str(sys.argv)))
    pass
