#!/usr/bin/env bash
set -e

project_dir=`echo $(dirname $(pwd))`
#inspect_target_path=${project_dir}/app

project_parent_dir=`echo $(dirname ${project_dir})`
inspect_bin_path=/android-studio/bin/inspect.sh
inspect_result_path=${project_parent_dir}/inspect/
inspect_config_path=${project_dir}/.idea/inspectionProfiles/Project_Default.xml
inspect_report_path=${project_parent_dir}/inspect/report.md
inspect_log_path=${project_parent_dir}/inspect/log.txt

#${inspect_bin_path} ${project_dir} ${inspect_config_path} ${inspect_result_path} -v2 -d ${inspect_target_path}
if [ ! -f "${inspect_config_path}" ];then
  echo "未找到inspect配置文件"
  exit 1
fi

if [ -d ${inspect_result_path} ];then
    echo "删除已有分析数据"
    rm -rf ${inspect_result_path}/*
else
    mkdir ${inspect_result_path}
fi

echo "检查执行开始"
python3 ./inspect.py "${inspect_bin_path}" "${project_dir}" "${inspect_config_path}" "${inspect_result_path}" "${inspect_report_path}"  > ${inspect_log_path}
#python3 ./inspect.py "${inspect_bin_path}" "${project_dir}" "${inspect_config_path}" "${inspect_result_path}" "${inspect_report_path}" "${inspect_target_path}" > ${inspect_log_path}

ls "${inspect_result_path}"

echo -e "检查执行完成\n\n"

if [ -f "${inspect_log_path}" ];then
   echo "检查执行日志开始"
   while read line
   do
        echo $line
   done < "${inspect_log_path}"
   echo -e "检查执行日志结束\n\n"
fi

if [ -f "${inspect_report_path}" ];then
   while read line
   do
        echo $line
   done < "${inspect_report_path}"
   echo "请解决上面的问题"
   exit 1
fi

exit 0