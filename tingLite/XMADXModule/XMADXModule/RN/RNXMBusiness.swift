//
//  RNXMBusiness.swift
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/3.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation

@objc(Business)
public class Business: NSObject {

    @objc public func listenEarnRewardCoinNew(_ params: [String: Any],
                                             resolve: @escaping (Any?) -> Void,
                                             reject: @escaping (String, String, Error?) -> Void) {

        print("🎯 Swift listenEarnRewardCoinNew 被调用了！")
        print("📦 接收到的参数: \(params)")

        // 1. 参数校验
        guard let positionName = params["positionName"] as? String, !positionName.isEmpty else {
            print("❌ positionName 参数错误")
            reject("PARAM_ERROR", "positionName is required", nil)
            return
        }

        guard let slotId = params["slot_id"] as? String, !slotId.isEmpty else {
            print("❌ slot_id 参数错误")
            reject("PARAM_ERROR", "slot_id is required", nil)
            return
        }

        // 2. 网络检查 (iOS中通常通过Reachability检查)
        // 这里简化处理，实际项目中应该使用网络状态检查

        // 3. 解析其他参数
        let renderTemplate = params["renderTemplate"] as? String ?? "1"
        let isUseADX = params["isUseADX"] as? Bool ?? true
        let rewardVideoStyle = params["rewardVideoStyle"] as? Int ?? 0
        let coins = params["coins"] as? Int ?? 0

        print("🔧 解析后的参数:")
        print("   positionName: \(positionName)")
        print("   slotId: \(slotId)")
        print("   renderTemplate: \(renderTemplate)")
        print("   isUseADX: \(isUseADX)")
        print("   rewardVideoStyle: \(rewardVideoStyle)")
        print("   coins: \(coins)")

        // 4. 调用激励视频接口
        DispatchQueue.main.async {
            print("🎬 开始调用激励视频接口")
            print("📦 视频参数: positionName=\(positionName), slotId=\(slotId), renderTemplate=\(renderTemplate)")

            let videoParams: [String: Any] = [
                "slotId": slotId,
                "positionName": positionName,
                "renderTemplate": renderTemplate,
                "isIgnoreADX": !isUseADX,
                "isPropUp": true,
                "needLoading": true
            ]

            XMLADXManager.shared().loadVideoAd(videoParams) { [weak self] (result: [String: Any]) in
                print("🎬 激励视频回调结果: \(result)")

                // 5. 处理激励视频结果
                let isSuccessLoad = result["isSuccessLoadVideo"] as? Bool ?? false
                let isSuccessRend = result["isSuccessRendVideo"] as? Bool ?? false
                let isVerifyResult = result["isVerifyVideoResult"] as? Bool ?? true

                if isSuccessLoad && isSuccessRend && isVerifyResult {
                    // 6. 根据rewardVideoStyle决定奖励发放方式
                    if rewardVideoStyle == 1 {
                        // 新流程：发放金币奖励
                        self?.handleGoldCoinReward(params: params,
                                                 adResult: result,
                                                 resolve: resolve,
                                                 reject: reject)
                    } else {
                        // 老流程：普通奖励回调
                        self?.handleNormalReward(params: params,
                                               adResult: result,
                                               resolve: resolve,
                                               reject: reject)
                    }
                } else {
                    print("❌ 激励视频播放失败 - isSuccessLoad: \(isSuccessLoad), isSuccessRend: \(isSuccessRend), isVerifyResult: \(isVerifyResult)")
                    reject("VIDEO_ERROR", "激励视频播放失败", nil)
                }
            }
        }
    }

    // MARK: - 私有方法

    /// 处理金币奖励（新流程）
    private func handleGoldCoinReward(params: [String: Any],
                                    adResult: [String: Any],
                                    resolve: @escaping (Any?) -> Void,
                                    reject: @escaping (String, String, Error?) -> Void) {

        print("💰 处理金币奖励 - 新流程")

        // 这里应该调用金币奖励接口，类似Android中的doRewardCoinRequest
        // 由于iOS中的具体实现可能不同，这里提供一个基础的成功回调结构

        let coins = params["coins"] as? Int ?? 0
        let adId = adResult["adId"] as? String ?? "0"
        let responseId = adResult["adResponseId"] as? String ?? "0"

        // 模拟成功响应，实际项目中应该调用真实的金币奖励接口
        let successData: [String: Any] = [
            "clientCode": 10002, // CLIENT_CODE_REQUEST_REWARD
            "clientMsg": "金币奖励发放成功",
            "adId": adId,
            "adResponseId": responseId,
            "coins": coins,
            "encryptType": 0,
            "fallbackReq": "0"
        ]

        print("✅ 金币奖励发放成功: \(successData)")
        resolve(successData)
    }

    /// 处理普通奖励（老流程）
    private func handleNormalReward(params: [String: Any],
                                  adResult: [String: Any],
                                  resolve: @escaping (Any?) -> Void,
                                  reject: @escaping (String, String, Error?) -> Void) {

        print("🎁 处理普通奖励 - 老流程")

        let adId = adResult["adId"] as? String ?? "0"
        let responseId = adResult["adResponseId"] as? String ?? "0"

        let successData: [String: Any] = [
            "clientCode": 10002, // CLIENT_CODE_REQUEST_REWARD
            "clientMsg": "激励视频观看完成",
            "adId": adId,
            "adResponseId": responseId,
            "encryptType": 0,
            "fallbackReq": "0"
        ]

        print("✅ 普通奖励处理成功: \(successData)")
        resolve(successData)
    }
}
