//
//  RNAdModule.swift
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/4.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation

@objc(AdModule)
class AdModule: NSObject {
    
    @objc func exposureWithAdItem(_ params: [String: Any]) {
        
    }
    
    @objc func clickWithAdItem(_ params: [String: Any],
                               resolver resolve: @escaping (Any?) -> Void,
                               rejecter reject: @escaping (String, String, Error?) -> Void) {
        
    }
    
    @objc func clickWithAdDeepLink(_ params: [String: Any],
                                  resolver resolve: @escaping (Any?) -> Void,
                                  rejecter reject: @escaping (String, String, Error?) -> Void) {

    }
}
