//
//  RNAdModuleBridge.m
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/4.
//  Copyright © 2025 ximalaya. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(AdModule, NSObject)

RCT_EXTERN_METHOD(exposureWithAdItem:(NSDictionary *)params)

RCT_EXTERN_METHOD(clickWithAdItem:(NSDictionary *)params
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(clickWithAdDeepLink:(NSDictionary *)params
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

@end
